import os
import sys
import mysql.connector
import json

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)

from common.pyUtils.athena_reader import AthenaReader
import common.pyUtils.aktana_ml_utils as mlutils

def read_journey_priority(params):
        print(f"Reading content from journey_factor_priority_v")
        reader = AthenaReader()
        reader.connect2(aws_access_key=params.get("athena-username"), aws_secret_key=params.get("athena-password"), session_token="", aws_region=params.get("athena-region"), athena_staging_bucket=params.get("athena-s3bucket"), athena_staging_dir=params.get("athena-stagedir"), schema=params.get("athena-schema"))
        df = reader.query2(query=f"select * from journey_factor_priority_v")
        return df

def connect_stage_database(env_params, tunnel_port):
    if (tunnel_port == ""):
        rds_host = env_params["rds-server"]
        tunnel_port = "3306"
    else:
        rds_host = "localhost"

    print("RDS host=", rds_host, ",port=", tunnel_port)

    connection = mysql.connector.connect(host=rds_host, user=env_params["rds-user"], password=env_params["rds-password"],
                                            database=env_params["rds-stagedbname"], port=tunnel_port)
    return connection

def write_dco_influence(params, df, tunnel_port):

    try:
        purge_old_journey_priority = ("delete from AKT_DCO_ExternalInfluences where influenceUID = 'Escalate-Campaign-Step-Priority'")
        add_influence = ("INSERT INTO AKT_DCO_ExternalInfluences "
                    "(influenceUID, influenceValue, accountUID, factorUID) "
                    "VALUES ('Escalate-Campaign-Step-Priority', %s, %s, %s)")

        connection = connect_stage_database(params, tunnel_port)
        cursor = connection.cursor(buffered=True)

        cursor.execute(purge_old_journey_priority)
        connection.commit()
        
        for index, rows in df.iterrows():
            data_influence = (rows.influence_value, rows.accountuid, rows.factoruid)
            cursor.execute(add_influence, data_influence)

        connection.commit()
        cursor.close()
        connection.close()
        print("Inserted {} dco_influence(s) into RDS".format(df.shape[0]))
    except Exception as e:
        print("Inserting dco_influence failed")
        print(e)
        exit(1)

def main():
    ml_utils = mlutils.aktana_ml_utils()

    cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:t:", ["customer=","env=","app=","region=", "ecosystem=", "tunnelport="])

    params = ml_utils.get_params_json(False)
    params = json.loads(params)
    #print(params)

    # Get journey_priority from athena
    df = read_journey_priority(params)

    # Write journey_priority to RDS
    tunnel_port = ""
    if (cmdline_params.get("t","") != ""):
        tunnel_port = cmdline_params.get("t","")
    if (cmdline_params.get("tunnelport","") != ""):
        tunnel_port = cmdline_params.get("tunnelport","")

    if (df.shape[0] > 0):
        write_dco_influence(params, df, tunnel_port)
    else:
        print('No entry found. Skipping...')
        exit(0)

if __name__ == "__main__":
    main()
