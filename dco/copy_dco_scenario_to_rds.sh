#!/bin/bash

# Wrapper/driver script to synchronize the list of DCO scenarios in RDS from Snowflake

# Usage: copy_dco_scenario_to_rds.sh --customer <customer-name> --env <env-name>"
#   Sample usage: ./copy_dco_scenario_to_rds.sh --customer pfizerbdpus --env dev"

#
# All jobs take the following command line parameters:
#   --customer custName (name of the customer configured in aktana metadata)
#   --env envName (name of the environment configured in aktana metadata)
#   --region regionName (name of the region for the Job.  Note: This is not used now since metadata is used to find the region for databricks and Snowflake
#

echo "Python version : $(python3 --version)"

LEARNING_DCO_INSTALL_DIR=`dirname $0`

APP_PARAM="--app DCO"

#echo "Install python dependencies from $LEARNING_DCO_INSTALL_DIR/requirements.txt"
#pip3 install -r $LEARNING_DCO_INSTALL_DIR/requirements.txt

echo "Running DCO Scenario-sync utility to copy Snowflake scenarios to RDS... python3 $LEARNING_DCO_INSTALL_DIR/copy_dco_scenario_to_rds.py $@ $APP_PARAM"
python3 $LEARNING_DCO_INSTALL_DIR/copy_dco_scenario_to_rds.py $@ $APP_PARAM
rc=$?
echo "Script returned $rc"
exit $rc
