import os
import sys

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)

#
# Use this utility to read/write DCO_CONFIG parameters from Snowflake
# python3 manage_dco_config.py --customer GENENTECHCA --env UAT --app DCO --ecosystem PROD --task set --paramvalue ENABLE_RECENCY_URGENCY_FACTOR=True,ENABLE_FREQUENCY_URGENCY_FACTOR=True
# python3 manage_dco_config.py --customer GENENTECHCA --env UAT --app DCO --ecosystem PROD --task get --paramname ENABLE_PLANNED_REP_ACTIONS,ENABLE_SNOOZE_FILTER,ENABLE_ADL_PREPROC,ENABLE_AUTO_SNOOZE_PREPROC,ENABLE_RECENCY_URGENCY_FACTOR,ENABLE_FREQUENCY_URGENCY_FACTOR,ENABLE_HORIZON_PLANNING
#

import common.pyUtils.aktana_ml_utils as mlutils

import common.pyUtils.snowflake_python_base as sfpb
import common.pyUtils.run_sql_on_snowflake as sfsql
import pandas as pd

def get_and_print_df(sfcur, param_name):
    param_name = param_name.replace(",", "','")
    if (param_name == "ALL"):
        query = f"SELECT DCO_CONFIG_NAME, INPUT_VERSION_ID, DCO_CONFIG_VALUE FROM DCO_CONFIG ORDER BY INPUT_VERSION_ID desc, DCO_CONFIG_NAME "
    else:
        query = f"SELECT DCO_CONFIG_NAME, INPUT_VERSION_ID, DCO_CONFIG_VALUE FROM DCO_CONFIG WHERE DCO_CONFIG_NAME IN ('{param_name}') ORDER BY DCO_CONFIG_NAME, INPUT_VERSION_ID desc "
    #print (f"Executing: {query}")
    sfcur.execute(query)
    data = sfcur.fetchall()
    col_names = [i[0] for i in sfcur.description]
    df = pd.DataFrame(data, columns=col_names)
    print(df.to_markdown())
    return df

def get_dco_config(snowflake_params, param_name):
    """
    PURPOSE:
        retrieve value for a DCO_CONFIG parameter
    INPUTS:
        snowflake parameters to connect
        Name of config parameter
    RETURNS:
        Returns data of list of values from each scenario
    """

    try:
        spb = sfpb.snowflake_python_base()
        conn = spb.create_connection(snowflake_params)
        sfcur = conn.cursor()
        df = get_and_print_df(sfcur, param_name)

    except Exception as e:
        print("Could not read config-value from snowflake.")
        print(e)
        exit(1)

    return (df)

def update_dco_config(snowflake_params, param_value):
    """
    PURPOSE:
        update value for a DCO_CONFIG parameter
    INPUTS:
        snowflake parameters to connect
        Name of config parameter
        Value of the parameter
    RETURNS:
        None
    """

    try:
        spb = sfpb.snowflake_python_base()
        conn = spb.create_connection(snowflake_params)
        sfcur = conn.cursor()

        param_list = param_value.split(",")

        for param in param_list:
            name, value = param.split("=")
            
            print (f"Current value for config name={name}:")
            get_and_print_df(sfcur, name)

            query = f"UPDATE DCO_CONFIG SET DCO_CONFIG_VALUE = '{value}' WHERE DCO_CONFIG_NAME = '{name}'"
            print (f"Executing: {query}")
            sfcur.execute(query)

    except Exception as e:
        print(f"Could not set {value} for {name}")
        print(e)
        exit(1)

    sfcur.close()
    conn.close()


def main():
    ml_utils = mlutils.aktana_ml_utils()

    cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:t:", ["customer=","env=","app=","region=", "ecosystem=", "task=", "paramname=", "paramvalue="])
    #print(metadata_params)
    #print(cmdline_params)

    region = cmdline_params['region']
    env = cmdline_params['env']
    customer = cmdline_params['customer']

    snowflake_params = ml_utils.get_snowflake_metadata()
    #print(snowflake_params)

    env_params = ml_utils.get_env_metadata()
    #print(env_params)

    task = cmdline_params.get("task","")
    paramname = cmdline_params.get("paramname","")
    paramvalue = cmdline_params.get("paramvalue","")
    if (task == "set"):
        if (paramvalue == ""):
            print(f"--paramvalue required to 'set' config values. Exiting...")
        else:
            print(f"Updating {paramvalue} for customer={customer} and env={env}")
            update_dco_config(snowflake_params, paramvalue)
    elif (task == "get"):
        if (paramname == ""):
            print(f"--paramname required to 'get' config values. Exiting...")
        else:
            print(f"Reading {paramname} for customer={customer} and env={env}")
            get_dco_config(snowflake_params, paramname)
    else:
        print(f"--task parameter has to be 'get' or 'set'. Exiting...")

if __name__ == "__main__":
    main()
