import os
import sys

import mysql.connector
from mysql.connector import <PERSON>rror
import traceback

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
print ("Learning dir:",learning_dir)
sys.path.append(learning_dir + "/common/pyUtils")

import aktana_ml_utils as mlutils

#import common.pyUtils.snowflake_python_base as sfpb
#import common.pyUtils.run_sql_on_snowflake as sfsql
import pandas as pd
import numpy as np
import time
import requests
import json

influenceCols = ['influenceUID', 'influenceValue', 'isVeto', 'accountUid', 'segment', 'repUid',
                            'suggestionCandidateUid',
                            'type', 'products', 'channel', 'startDate', 'endDate', 'configId', 'factorUid', 'useCaseTagId', 'actionTypeId', 'actorTypeId']

#############
#
# function to read the view from the stage-db of the customer/env.
# the view must have only a subset of the columns in the influenceCols variable above
#
##############
def get_influences_from_view(env_params, tunnel_port, db, view):
    """
    PURPOSE:
        retrieve list of influences as a dataframe with all influence columns
    INPUTS:
        env parameters to connect
        tunnel port
    RETURNS:
        Returns dataframe of list of influences
    """

    dbname = env_params["rds-stagedbname"]
    if (db == "engine"):
        dbname = env_params["rds-enginedbname"]
    if (db == "learning"):
        dbname = env_params["rds-learningdbname"]
    if (db == "cs"):
        dbname = env_params["rds-copystormdbName"]

    try:
        conn = connect_dse_database(env_params, tunnel_port)
        df = pd.read_sql(f"select * from {dbname}.{view};", con=conn)
        conn.close()

        #df.rename(columns={"accountUID": "accountUid", "productUID": "products", "scaledPriorityScore_akt": "influenceValue"}, inplace=True)
        #df['influenceUID'] = 'AccountProductPriority'

        print(df.info())
        print (df.head(10))

        print('number of influences = {}'.format(df.shape[0]))

    except Exception as e:
        print(f"Could not read influences using view:{dbname}.{view}.")
        print(e)

    return (df)

def connect_dse_database(env_params, tunnel_port):
    if (tunnel_port == ""):
        rds_host = env_params["rds-server"]
        tunnel_port = "3306"
    else:
        rds_host = "127.0.0.1"

    connection = mysql.connector.connect(host=rds_host, user=env_params["rds-user"], password=env_params["rds-password"],
                                            database=env_params["rds-enginedbname"], port=tunnel_port)
    return connection

def intersection(lst1, lst2):
    return list(set(lst1) & set(lst2))

def write_influences_json(env_params, df, s3path, s3file):

    try:

        nested_cols = ['influence', 'account', 'rep', 'suggestion', 'channelDetails', 'factor']
        unnested_cols = ['products', 'channel', 'startDate', 'endDate', 'configId']
        nexted = {
            'influence': ['influenceUID', 'influenceValue', 'isVeto'],
            'account': ['accountUid', 'segment'],
            'rep': ['repUid'],
            'factor': ['factorUid'],
            'suggestion': ['suggestionCandidateUid', 'type', 'factorUid', 'useCaseTagId'],
            'channelDetails': ['actionTypeId', 'actorTypeId']
        }

        db_cols = set(df.columns)
        extra_cols = db_cols - set(influenceCols)
        if extra_cols:
            print ("Unrecognized/extra columns in input dataframe:" + str(extra_cols))
            print ("Accepted columns are:" + influenceCols)

        final_cols = list(set(unnested_cols) & db_cols)
        for groupName in nested_cols:
            nested_cols = set(nexted[groupName])
            result_cols = list(nested_cols & db_cols)
            if result_cols:
                dftemp = df
                if 'repUid' in result_cols:
                    dftemp = df.rename(columns={"repUid": "uid"})
                    result_cols.remove('repUid')
                    result_cols.append('uid')
                if 'accountUid' in result_cols:
                    dftemp = df.rename(columns={"accountUid": "uid"})
                    result_cols.remove('accountUid')
                    result_cols.append('uid')
                if 'factorUid' in result_cols:
                    dftemp = df.rename(columns={"factorUid": "uid"})
                    result_cols.remove('factorUid')
                    result_cols.append('uid')
                df[groupName]  = dftemp[result_cols].to_dict('records')
                final_cols.append(groupName)
        df = df[final_cols]
        print(df.info())
        print (df.head(10))

        #Save output to parquet
        s3path = s3path.replace("s3a://", "s3://", 1) # pandas to_parquet expect s3 url and doesn't support s3a protocol
        print("Writing influence file to " + s3path + s3file + ".parquet")
        df.to_parquet(s3path + s3file + '.parquet', compression='gzip')

        #Save output to json
        #result = df.to_json(orient="records")
        #parsed = json.loads(result)
        #json_object = json.dumps(parsed, indent=4)    
        #with open(s3file, "w") as outfile:
        #    outfile.write(json_object)

        #Code to verify the file was written correctly, the file below will be the same as the file created above
        #final_df = pd.DataFrame.from_dict(parsed, orient="columns")
        #final_df.to_parquet(s3file + '2.parquet', compression='gzip') 

    except Exception as e:
        print("Creating influences file failed")
        traceback.print_exc()
        print(e)
        exit(1)

def main():
    ml_utils = mlutils.aktana_ml_utils()

    cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:", ["customer=","env=","app=","region=", "ecosystem=", "tunnelport=", "view=", "db=", "filename=", "localfile="])
    print(metadata_params)
    print(cmdline_params)

    if (cmdline_params.get("help","") == "true"):
        print("Usage: rdsview_to_influencejson_converter reads a view in RDS to create the CIE Optimizer influence file in json format")
        print("Usage: Parameter specified with --view should exist in the Customer's RDS stage-db with a subset of the following columns:" + influenceCols)
        print("Usage: Parameter specified with --db should be one of 'engine', 'stage', 'learning' or 'cs'.  Default is stage if not specified ")
        print("Usage: Parameter specified with --filename should be in folder/file format unless --localfile is set as true")
        print ("Usage: The output file will follow the format explained in https://aktana.atlassian.net/wiki/spaces/MAIN/pages/2319941637/DCO+Influences")
        print ("Usage: --customer, --env, --app, --view and --filename are required in commandline")
        sys.exit(2)

    region = cmdline_params['region']
    env = cmdline_params['env']
    customer = cmdline_params['customer']

    snowflake_params = ml_utils.get_snowflake_metadata()
    print(snowflake_params)

    env_params = ml_utils.get_env_metadata()
    print(env_params)

    adl_params = ml_utils.get_adl_metadata()
    print(adl_params)

    tunnel_port = ""
    if (cmdline_params.get("t","") != ""):
        tunnel_port = cmdline_params.get("t","")
    if (cmdline_params.get("tunnelport","") != ""):
        tunnel_port = cmdline_params.get("tunnelport","")

    if (cmdline_params.get("localfile","") == "true"):
        filepath = cmdline_params.get("filename","")
    else:
        filepath = adl_params.get("adl-dcoS3Location", "s3://aktana-bdp-" + customer + "/" + env + "/dco/") + "data/dco_read_write/bronze/EXTERNAL_INFLUENCES/"

    df = get_influences_from_view(env_params, tunnel_port, cmdline_params.get("db","stage"), cmdline_params.get("view",""))
    if (df.shape[0] > 0):
        write_influences_json(env_params, df, filepath, cmdline_params.get("filename",""))
    else:
        print('View not found or is empty.  No influences to write. Skipping...')

if __name__ == "__main__":
    main()
