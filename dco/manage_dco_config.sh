#!/bin/bash

# Wrapper/driver script for manage dco config jobs

# Usage: manage_dco_config.sh <job_name> --customer <customer-name> --env <env-name> --app <appname> --ecosystem <ecosystem>"
#   Supported job names are: IS_ADL_ENABLED, DIS<PERSON>LE_ADL, ENABLE_ADL, GET_CONFIG_VALUE and SET_CONFIG_VALUE"
#
# ./manage_dco_config.sh IS_ADL_ENABLED --customer GENENTECHCA --env UAT --app DCO --ecosystem PROD
# ./manage_dco_config.sh DISABLE_ADL --customer GENENTECHCA --env UAT --app DCO --ecosystem PROD
# ./manage_dco_config.sh ENABLE_ADL --customer GENENTECHCA --env UAT --app DCO --ecosystem PROD
# ./manage_dco_config.sh GET_CONFIG_VALUE --customer GENENTECHCA --env UAT --app DCO --ecosystem PROD --paramname ENABLE_RECENCY_URGENCY_FACTOR,ENABLE_FREQUENCY_URGENCY_FACTOR,ENABLE_HORIZON_PLANNING
# ./manage_dco_config.sh SET_CONFIG_VALUE --customer GENENTECHCA --env UAT --app DCO --ecosystem PROD --paramvalue ENABLE_RECENCY_URGENCY_FACTOR=True,ENABLE_FREQUENCY_URGENCY_FACTOR=True,ENABLE_HORIZON_PLANNING=False
# ./manage_dco_config.sh GET_CONFIG_VALUE --customer GENENTECHCA --env UAT --app DCO --ecosystem PROD --paramname ALL

#
# All jobs take the following command line parameters:
#   --customer custName (name of the customer configured in aktana metadata)
#   --env envName (name of the environment configured in aktana metadata)
#   --region regionName (name of the region for the Job.  Note: This is not used now since metadata is used to find the region for databricks and Snowflake
#

echo "Python version : $(python3 --version)"

pip3 install tabulate

INSTALL_DIR=`dirname $0`

JOB=$1
shift 1

case $JOB in

    IS_ADL_ENABLED)
        SCRIPT_ADDITIONAL_PARAMS=" --task get --paramname ENABLE_PLANNED_REP_ACTIONS,ENABLE_SNOOZE_FILTER,ENABLE_ADL_PREPROC,ENABLE_AUTO_SNOOZE_PREPROC,ENABLE_RECENCY_URGENCY_FACTOR,ENABLE_FREQUENCY_URGENCY_FACTOR,ENABLE_HORIZON_PLANNING "
        ;;

    ENABLE_ADL)
        SCRIPT_ADDITIONAL_PARAMS=" --task set --paramvalue ENABLE_PLANNED_REP_ACTIONS=True,ENABLE_SNOOZE_FILTER=True,ENABLE_ADL_PREPROC=True,ENABLE_AUTO_SNOOZE_PREPROC=True "
        ;;

    DISABLE_ADL)
        SCRIPT_ADDITIONAL_PARAMS=" --task set --paramvalue ENABLE_PLANNED_REP_ACTIONS=False,ENABLE_SNOOZE_FILTER=False,ENABLE_ADL_PREPROC=False,ENABLE_AUTO_SNOOZE_PREPROC=False,ENABLE_RECENCY_URGENCY_FACTOR=False,ENABLE_FREQUENCY_URGENCY_FACTOR=False,ENABLE_HORIZON_PLANNING=False "
        ;;

    GET_CONFIG_VALUE)
        SCRIPT_ADDITIONAL_PARAMS=" --task get "
        ;;

    SET_CONFIG_VALUE)
        SCRIPT_ADDITIONAL_PARAMS=" --task set "
        ;;

    *)
        echo "Usage: manage_dco_config.sh <job_name> --customer <customer-name> --env <env-name> --app <app-name> --ecosystem <ecosystem> --task <get|set>"
        echo "  Supported job names are: IS_ADL_ENABLED, DISABLE_ADL, ENABLE_ADL, GET_CONFIG_VALUE and SET_CONFIG_VALUE"
        echo "  Sample usage: ./manage_dco_config.sh IS_ADL_ENABLED --customer GENENTECHCA --env UAT --app DCO --ecosystem PROD "
        exit 1
        ;;

esac

echo "Invoking manage-dco-config utility with ... $@ $SCRIPT_ADDITIONAL_PARAMS"
python3 $INSTALL_DIR/manage_dco_config.py $@ $SCRIPT_ADDITIONAL_PARAMS
rc=$?

if [ "$JOB" == "ENABLE_ADL" ]; then
    echo "***** WARNING: Parameters ENABLE_RECENCY_URGENCY_FACTOR,ENABLE_FREQUENCY_URGENCY_FACTOR,ENABLE_HORIZON_PLANNING were not enabled by this job.  Enable them using SET_CONFIG_VALUE if they are used for this customer " 
fi

exit $rc