import os
import sys

import mysql.connector
from mysql.connector import Error

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)


#
#
# Use SCD meta-data to create DCO metadata, if it doesn't already exist
# insert into aktanameta.CustomerSnowflakeConfigProperties
# (customerId, envName, account, endPoint, region, warehouse, db, dbschema, appName, `role`, `user`, password, isActive)
# select customerId, envName, account, endPoint, region, warehouse, db, 'DCO', 'DCO', `role`, `user`, password, isActive
# from CustomerSnowflakeConfigProperties a
# where appName = 'SCD'
# and not exists (select 1 from CustomerSnowflakeConfigProperties b
# where a.customerId = b.customerId and b.appName = 'DCO');
#
#

import common.pyUtils.aktana_ml_utils as mlutils

import common.pyUtils.snowflake_python_base as sfpb
import common.pyUtils.run_sql_on_snowflake as sfsql
import pandas as pd
import numpy as np
import time
import requests
import json

def get_dco_scenarios(snowflake_params):
    """
    PURPOSE:
        retrieve list of scenarios
    INPUTS:
        snowflake parameters to connect
    RETURNS:
        Returns data of list of scenarios
    """

    try:
        spb = sfpb.snowflake_python_base()
        conn = spb.create_connection(snowflake_params)
        sfcur = conn.cursor()

        query = """ SELECT SCENARIO_ID, SCENARIO_UID, SCENARIO_NAME, SCENARIO_DESCRIPTION, CREATED_BY, CREATED_TS, UPDATED_TS, IS_ACTIVE, ROW_NUMBER() OVER (ORDER BY CREATED_TS) SCENARIO_SEQ FROM SCENARIO WHERE IS_ACTIVE = True and IS_DELETED = False
                    """
        sfcur.execute(query)
        data = sfcur.fetchall()
        col_names = [i[0] for i in sfcur.description]

        df = pd.DataFrame(data, columns=col_names)
    except Exception as e:
        print("Could not read scenarios from snowflake.  Skipping...")
        print(e)
        exit(0)

    return (df)

def connect_dse_database(env_params, tunnel_port):
    if (tunnel_port == ""):
        rds_host = env_params["rds-server"]
        tunnel_port = "3306"
    else:
        rds_host = "localhost"

    #print("RDS host=", rds_host, ",port=", tunnel_port)

    connection = mysql.connector.connect(host=rds_host, user=env_params["rds-user"], password=env_params["rds-password"],
                                            database=env_params["rds-enginedbname"], port=tunnel_port)
    return connection

def write_scenarios_to_mysql(env_params, df, tunnel_port):

    try:
        add_scenario = ("INSERT INTO DCOScenario "
                    "(scenarioId, scenarioUID, scenarioName, scenarioDescription, createdBy, createdAt, updatedAt, scenarioSeq ) "
                    "VALUES (%s, %s, %s, %s, %s, %s, %s, %s)")

        connection = connect_dse_database(env_params, tunnel_port)
        cursor = connection.cursor(buffered=True)
        for index, rows in df.iterrows():
            data_scenario = (rows.SCENARIO_ID, rows.SCENARIO_UID, rows.SCENARIO_NAME, rows.SCENARIO_DESCRIPTION, rows.CREATED_BY, rows.CREATED_TS, rows.UPDATED_TS, rows.SCENARIO_SEQ)

            # Insert new employee
            cursor.execute(add_scenario, data_scenario)

        connection.commit()
        cursor.close()
        connection.close()
        print("Inserted {} scenario(s) into RDS".format(df.shape[0]))
    except Exception as e:
        print("Inserting scenarios failed")
        print(e)
        exit(1)

def delete_scenarios_in_mysql(env_params, tunnel_port):
    """
    PURPOSE:
        process snowflake dataframe into rows for DSE DCOScenario table
    INPUTS:
        dataframe from snowflake
    RETURNS:
        dataframe to be inserted
    """

    try:
        connection = connect_dse_database(env_params, tunnel_port)
        cursor = connection.cursor(buffered=True)
        sql_query = "DELETE FROM {DB_NAME}.{DCO_SCENARIO_TABLE}".format(DB_NAME=env_params["rds-enginedbname"], DCO_SCENARIO_TABLE="DCOScenario")
        cursor.execute(sql_query)
        connection.commit()
        cursor.close()
        connection.close()
        print("Deleted existing scenarios")
    except Exception as e:
        print("Deleting existing scenarios failed")
        print(e)
        exit(1)

def main():
    ml_utils = mlutils.aktana_ml_utils()

    cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:t:", ["customer=","env=","app=","region=", "ecosystem=", "tunnelport="])
    print(metadata_params)
    print(cmdline_params)

    region = cmdline_params['region']
    env = cmdline_params['env']
    customer = cmdline_params['customer']

    snowflake_params = ml_utils.get_snowflake_metadata()
    print(snowflake_params)

    env_params = ml_utils.get_env_metadata()
    print(env_params)

    # Get scenario list from snowflake only if snowflake metadata exists for DCO
    if (snowflake_params.get("snowflake-schema", "") != ""):
        df = get_dco_scenarios(snowflake_params)
        print('number of scenarios in snowflake = {}'.format(df.shape[0]))
    else:
        print('No snowflake metadata defined for DCO. Skipping...')
        exit(0)

    # Write scenarios to RDS
    tunnel_port = ""
    if (cmdline_params.get("t","") != ""):
        tunnel_port = cmdline_params.get("t","")
    if (cmdline_params.get("tunnelport","") != ""):
        tunnel_port = cmdline_params.get("tunnelport","")

    delete_scenarios_in_mysql(env_params, tunnel_port)
    if (df.shape[0] > 0):
        write_scenarios_to_mysql(env_params, df, tunnel_port)
    else:
        print('No scenarios found in snowflake. Skipping...')
        exit(0)


if __name__ == "__main__":
    main()
