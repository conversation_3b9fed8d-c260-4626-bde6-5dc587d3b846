import os
import sys

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)

import common.pyUtils.aktana_ml_utils as mlutils

import common.pyUtils.snowflake_python_base as sfpb
import boto3
import pandas as pd
import numpy as np
import time
import requests
import json
import pyarrow
import pyarrow.parquet as pq
import s3fs
from datetime import date

suggestion_candidates_view = "VW_DCO_EXPORT_SUGGESTIONS"
influences_view = "VW_DCO_EXPORT_INFLUENCES"
product_messages_view = "VW_DCO_EXPORT_PRODUCT_MESSAGE"

today = date.today()

def initialize_df(view_name):
    if view_name == suggestion_candidates_view:
        return pd.DataFrame({'SUGGESTION_CANDIDATE_UID': pd.Series(dtype='str'),
                   'ACCOUNT_NAME': pd.Series(dtype='str'),
                   'ACCOUNT_UID': pd.Series(dtype='str'),
                   'RUN_DATE': pd.Series(dtype='str'),
                   'SE_CONFIG_ID': pd.Series(dtype='int'),
                   'SE_CONFIG_NAME': pd.Series(dtype='str'),
                   'USER_UID': pd.Series(dtype='str'),
                   'USER_NAME': pd.Series(dtype='str'),
                   'SUGGESTION_REFERENCE_ID': pd.Series(dtype='str'),
                   'SUGGESTED_DATE': pd.Series(dtype='str'),
                   'IS_SUGGESTION_CRITICAL': pd.Series(dtype='str'),
                   'PRIMARY_FACTOR_UID': pd.Series(dtype='str'),
                   'PRIMARY_FACTOR_NAME': pd.Series(dtype='str'),
                   'PRIMARY_PRODUCT_UID': pd.Series(dtype='str'),
                   'PRIMARY_PRODUCT_NAME': pd.Series(dtype='str'),
                   'CHANNEL_UID': pd.Series(dtype='str'),
                   'CHANNEL_NAME': pd.Series(dtype='str'),
                   'SUGGESTION_DRIVER': pd.Series(dtype='str'),
                   'COUNTRY_CODE': pd.Series(dtype='str'),
                   'PRIMARY_REP_ACTION_TYPE_NAME': pd.Series(dtype='str'),
                   'MESSAGE_UID': pd.Series(dtype='str'),
                   'MESSAGE_SET_UID': pd.Series(dtype='str'),
                   'REASONS': pd.Series(dtype='str'),
                   'EXPECTED_VALUE': pd.Series(dtype='float'),
                   'RECOMMENDED': pd.Series(dtype='str'),
                   'DCO_REASON_SUMMARY': pd.Series(dtype='str')
                   })

    elif view_name == influences_view:

        return pd.DataFrame({'SUGGESTION_CANDIDATE_UID': pd.Series(dtype='str'),
                   'RUN_DATE': pd.Series(dtype='str'),
                   'INFLUENCE_UID': pd.Series(dtype='str'),
                   'INFLUENCE_VALUE_SCALED': pd.Series(dtype='int'),
                   'INFLUENCE_DISPLAY_NAME': pd.Series(dtype='str'),
                   'DECISIVE_COMPONENT_RANK': pd.Series(dtype='int'),
                   'DECISIVE_COMPONENT_DEGREE': pd.Series(dtype='str')
                   })

    elif view_name == product_messages_view:
        return pd.DataFrame({'SUGGESTION_CANDIDATE_UID': pd.Series(dtype='str'),
                             'RUN_DATE': pd.Series(dtype='str'),
                             'ACTION_ORDER': pd.Series(dtype='int'),
                             'PRODUCT_UID': pd.Series(dtype='str'),
                             'MESSAGE_UID': pd.Series(dtype='str'),
                             'CMS_UID': pd.Series(dtype='str'),
                             'FRAGMENT_UID_LIST': pd.Series(dtype='str'),
                             'FRAGMENT_CMS_UID_LIST': pd.Series(dtype='str'),
                             # 'MESSAGE_ORDER': pd.Series(dtype='int'),
                             # 'MESSAGE_SET_UID': pd.Series(dtype='str'),
                             'PRODUCT_NAME': pd.Series(dtype='str'),
                             'MESSAGE_NAME': pd.Series(dtype='str'),
                             # 'MESSAGE_SET_NAME': pd.Series(dtype='str')
        })

    else:
        pd.DataFrame()

def get_data(view_name, runDate, snowflake_params):
    """
    PURPOSE:
        retrieve data from given view name
    INPUTS:
        view name, rundate and snowflake parameters to connect
    RETURNS:
        Returns list of rows from given view
    """

    try:
        spb = sfpb.snowflake_python_base()
        conn = spb.create_connection(snowflake_params)
        sfcur = conn.cursor()

        query = f'SELECT * FROM DCO.{view_name} where RUN_DATE = \'{runDate}\''
        sfcur.execute(query)

        #Initialize pd df based on view name with specific set of columns
        all_data = initialize_df(view_name)

        # Fetch 50000 rows at a time
        while True:

            data = sfcur.fetchmany(50000)
            new_df = pd.DataFrame(data, columns=all_data.columns)
            if not data:
                break
            all_data = pd.concat([all_data, new_df], axis=0)

        col_names = [i[0] for i in sfcur.description]

        df = pd.DataFrame(all_data, columns=col_names)
        df.fillna("",inplace=True)
        df = df.reset_index(drop=True)

    except Exception as e:
        print(f"Could not read view {view_name} from snowflake...")
        print(e)
        exit(1)

    return (df)

def is_config_enabled(snowflake_params, config_name):
    """
    PURPOSE:
        Retrieve whether a specific DCO_CONFIG_NAME is enabled for DEFAULT scenario
    INPUTS:
        snowflake_params: Parameters to connect to Snowflake
        config_name: Name of the configuration to check
    RETURNS:
        Returns true/false
    """

    retValue = False
    try:
        spb = sfpb.snowflake_python_base()
        conn = spb.create_connection(snowflake_params)
        sfcur = conn.cursor()

        query = f"SELECT DCO_CONFIG_VALUE FROM DCO.DCO_CONFIG WHERE INPUT_VERSION_ID = '__DEFAULT__' AND DCO_CONFIG_NAME = '{config_name}'"
        sfcur.execute(query)

        data = sfcur.fetchall()
        col_names = [i[0] for i in sfcur.description]

        df = pd.DataFrame(data, columns=col_names)
        if df.empty:
            print(f"Could not find {config_name} flag in DCO_CONFIG...")
        else:
            flag = df['DCO_CONFIG_VALUE'].iloc[0]
            print(f"{config_name}={flag}")
            if flag == 'True':
                retValue = True

    except Exception as e:
        print(f"Could not read {config_name} flag from snowflake...")
        print(e)

    return retValue

def modify_product_messages_df(df, isExportFragments):
    # columns to be removed if the isExportFragments flag is False
    columns_to_remove = ['CMS_UID', 'FRAGMENT_UID_LIST', 'FRAGMENT_CMS_UID_LIST']

    # Move the columns to the end
    cols = df.columns.tolist()
    for col in columns_to_remove:
        if col in cols:
            cols.append(cols.pop(cols.index(col)))

    # Reorder DataFrame columns
    df = df[cols]

    # Remove columns if isExportFragments is False
    if not isExportFragments:
        df = df.drop(columns=columns_to_remove)

    return df

def clean_dataframe(df):
    """
    Cleans the DataFrame by filling NaN values and converting data types appropriately.

    Args:
    - df (pd.DataFrame): The DataFrame to be cleaned.

    Returns:
    - pd.DataFrame: The cleaned DataFrame.
    """
    for col in df.columns:
        if df[col].dtype == 'object':
            df[col] = df[col].fillna('').astype('str')
        elif 'int' in str(df[col].dtype):
            df[col] = df[col].fillna(0).astype('int')
        elif 'float' in str(df[col].dtype):
            df[col] = df[col].fillna(0.0).astype('float')

    return df


def main():
    ml_utils = mlutils.aktana_ml_utils()

    # Job requires params - customer, env, app, region
    # AND Optional params - runDate and ecosystem
    cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:a:t:", ["customer=","env=","app=","region=","ecosystem=","runDate="])
    print(metadata_params)
    print(cmdline_params)

    region = cmdline_params['region']
    env = cmdline_params['env']
    customer = cmdline_params['customer']
    # If runDate is empty or not provided, current date is considered
    if "runDate" not in cmdline_params or cmdline_params['runDate'].strip() == "":
        runDate = today.strftime("%Y-%m-%d")
    else:
        runDate = cmdline_params['runDate']


    snowflake_params = ml_utils.get_snowflake_metadata()
    print(snowflake_params)
    print(f"run date: '{runDate}'")


    if (snowflake_params.get("snowflake-schema", "") != ""):

        isExport = is_config_enabled(snowflake_params, 'ENABLE_EXPORT_DCO_OUTPUT')
        if not isExport:
            print('ENABLE_EXPORT_DCO_OUTPUT is not set for __DEFAULT__ scenario.  Skipping export...')
            exit(0)

        # Fetch suggestion candidates from snowflake
        suggestions_df = get_data(suggestion_candidates_view, runDate, snowflake_params)
        suggestions_df['RUN_DATE'] = pd.to_datetime(suggestions_df['RUN_DATE'], format="%Y/%m/%d")
        suggestions_df.rename(columns={'USER_UID':'ACTOR_UID', 'USER_NAME':'ACTOR_NAME'}, inplace=True)

        # Fetch influences from snowflake
        influences_df = get_data(influences_view, runDate, snowflake_params)

        # Fetch product_messages from snowflake
        product_messages_df = get_data(product_messages_view, runDate, snowflake_params)
        isExportFragments = is_config_enabled(snowflake_params, 'EXPORT_DCO_OUTPUT_LATEST')
        product_messages_df = modify_product_messages_df(product_messages_df, isExportFragments)

    else:
        print('No snowflake metadata defined for DCO. Skipping...')
        exit(0)

    try:
        # If df has data, write to s3
        if (suggestions_df.shape[0] > 0):
            # Check for and fill NaN values, convert data types explicitly
            suggestions_df = clean_dataframe(suggestions_df)
            output_file = f"s3://aktana-externalfiles-{customer}/{env}/outgoing/cie/runDate={runDate}/suggestion_candidates.parquet"
            print(f"writing suggestions(count: {suggestions_df.shape[0]}) to s3 path {output_file}")
            suggestions_df.to_parquet(output_file) #partition_cols=['RUN_DATE']
            print("Done writing suggestions.")
        else:
            print('No Suggestion candidates found in snowflake.')
            exit(0)

        if (influences_df.shape[0] > 0):
            influences_df = clean_dataframe(influences_df)
            output_file = f"s3://aktana-externalfiles-{customer}/{env}/outgoing/cie/runDate={runDate}/influences.parquet"
            print(f"writing influences(count: {influences_df.shape[0]}) to s3 path {output_file}")
            influences_df.to_parquet(output_file)
            print("Done writing influences.")
        else:
            print('No influences found in snowflake. Skipping...')

        if (product_messages_df.shape[0] > 0):
            product_messages_df = clean_dataframe(product_messages_df)
            output_file = f"s3://aktana-externalfiles-{customer}/{env}/outgoing/cie/runDate={runDate}/product_messages.parquet"
            print(f"writing product messages(count: {product_messages_df.shape[0]}) to s3 path {output_file}")
            product_messages_df.to_parquet(output_file)
            print("Done writing product messages.")
        else:
            print('No product_messages found in snowflake. Skipping...')
    except Exception as e:
        print('Error writing data into s3.....')
        print(e)
        exit(1)

if __name__ == "__main__":
    main()
