import pyspark
from pyspark.sql import SparkSession
import sys
import os
import findspark
import boto3
from pyspark.sql.types import IntegerType, StringType, DoubleType, FloatType, LongType, BooleanType, TimestampType, DecimalType
import pandas as pd
import pyspark.sql.functions as F
import getopt
import json
from os import path, listdir
import requests
from pyspark.sql import SQLContext

class DeltaTableVacuum():

    def __init__(self):
        """

        """

        # 0. Read and set arguments to the program
        """
        Arguments to the engine from Rundeck
        [scenario_uid, run_date, credentials]
        """
        self.cmdline_params = {}
        self.params = {}
        self.__read_cmd_line_parameters()
        self.spark = None

    def get_aws_region(self):
        r = requests.get("http://169.254.169.254/latest/meta-data/placement/region")
        return r.text

    def get_cmdline_args(self, argv):

        """
        PURPOSE:
            Read the command-line arguments and store them in a dictionary.
            Command-line arguments should come in pairs, e.g.:
                "--customer abc"
        INPUTS:
            The command line arguments (sys.argv).
        RETURNS:
            Returns the dictionary.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """

        if self.cmdline_params:
            return self.cmdline_params

        self.cmdline_params = {}

        shortopts = "hc:e:s:r:f:"
        longopts = ["customer=","env=","conn-param-file=","param1=","param2=","param3=", "rundate=", "scenario=", "table-path=", "csv-path=", "use-debug=", "local=", "optimize=", "vacuum=", "base-path=", "table-list=", "ecosystem="]

        try:
            opts, args = getopt.getopt(argv[1:],shortopts,longopts)
        except getopt.GetoptError:
            print("Supported args are:" + str(longopts), file=sys.stderr)
            sys.exit(2)

        for opt, arg in opts:
            if opt == '-h':
                print("Usage: --customer, --env, --conn-param-file, --table-path, --csv-path are required.  --param1, --param2 or --param3 are optional.")
                sys.exit()
            elif opt in ("-c", "--customer"):
                self.cmdline_params["customer"] = arg
            elif opt in ("--use-debug"):
                self.cmdline_params["use-debug"] = arg
            elif opt in ("-e", "--env"):
                self.cmdline_params["env"] = arg
            elif opt in ("-f", "--conn-param-file"):
                self.cmdline_params["conn-param-file"] = arg
            else:
                self.cmdline_params[opt[2:]] = arg

        print ("******Cmd line params:" + str(self.cmdline_params))
        return self.cmdline_params

    def __read_cmd_line_parameters(self):
        """

        @return:
        """
#        print("Command lined arugment Count- ", len(sys.argv))
#        param_file_path = None
#        if len(sys.argv)  == constants.CMD_ARGUMENT_COUNT + 1:
#            param_file_path = sys.argv[1]
#        else:
#            param_file_path = constants.DEFAULT_PARAM_FILE_PATH

        self.get_cmdline_args(sys.argv)

        # param_file_path = self.cmdline_params.get("conn-param-file", "")
        #
        # print("Reading parameter File- ", param_file_path)
        # if path.isfile(param_file_path):
        #     with open(param_file_path) as param_file:
        #         self.params = json.load(param_file)
        #
        # else:
        #     raise IOError("Unable to locate param file at:" + param_file_path)

    def get_caller_identity(self):

        client = boto3.client("sts")
        response = client.get_caller_identity()
        print("Current caller identity:")
        print(response)

    def get_role_credentials(self):

        client = boto3.client("sts")
        response = client.get_caller_identity()
        print("Current caller identity:")
        print(response)

        #response = client.assume_role(RoleArn=response['Arn'], RoleSessionName="Archive-overwrite")
        #response = client.assume_role(RoleArn="arn:aws:iam::332437952577:role/emr-ec2-useks-us-east-1", RoleSessionName="Archive-overwrite")
        response = client.get_session_token()

        print("Credentials for role:")
        print(response)
        credentials = response["Credentials"]
        return credentials

    def initalize_spark(self):
        findspark.init()
        os.environ['PYSPARK_SUBMIT_ARGS'] = "--driver-memory 12g --packages=com.amazonaws:aws-java-sdk-bundle:1.12.170," \
                                            "org.apache.hadoop:hadoop-aws:3.2.2,io.delta:delta-core_2.12:2.0.2 " \
                                            "pyspark-shell "

        self.get_caller_identity()

        sc = pyspark.SparkContext()
        sc.setSystemProperty("com.amazonaws.services.s3.enableV4", "true")
        sc.setLogLevel("WARN")
        hadoop_conf = sc._jsc.hadoopConfiguration()
        hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
        hadoop_conf.set("com.amazonaws.services.s3.enableV4", "true")

        if self.cmdline_params.get("local", "false") == "true":
            print(f"Running in local-mode. Using aws-access and aws-secret keys")
            hadoop_conf.set("fs.s3a.access.key", self.params['adl-awsAccessKey'])
            hadoop_conf.set("fs.s3a.secret.key", self.params['adl-awsSecretKey'])
        if self.cmdline_params.get("local", "false") == "role":
            credentials = self.get_role_credentials()
            print(f"Running in server-mode. Using aws-access and aws-secret keys from assumed role")
            hadoop_conf.set("fs.s3a.aws.credentials.provider", "org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider")
            hadoop_conf.set("fs.s3a.access.key", credentials["AccessKeyId"])
            hadoop_conf.set("fs.s3a.secret.key", credentials["SecretAccessKey"])
            hadoop_conf.set("fs.s3a.session.token", credentials["SessionToken"])
        if self.cmdline_params.get("local", "false") == "sse-key":
            print(f"Running in server-mode. Enabling server side encryption with key")
            #https://docs.cloudera.com/HDPDocuments/HDP2/HDP-2.6.3/bk_cloud-data-access/content/SSE-KMS.html
            hadoop_conf.set("fs.s3a.enableServerSideEncryption", "true")
            hadoop_conf.set("fs.s3a.server-side-encryption-algorithm", "SSE-KMS")
            hadoop_conf.set("fs.s3a.server-side-encryption.key", "arn:aws:kms:us-east-1:518905070720:key/8d49fe5f-8763-413e-b405-c96cc4d5d9e5")
        if self.cmdline_params.get("local", "false") == "sse-kms":
            print(f"Running in server-mode. Enabling server side encryption without key")
            #https://docs.cloudera.com/HDPDocuments/HDP2/HDP-2.6.3/bk_cloud-data-access/content/SSE-KMS.html
            hadoop_conf.set("fs.s3a.enableServerSideEncryption", "true")
            hadoop_conf.set("fs.s3a.server-side-encryption-algorithm", "SSE-KMS")
        if self.cmdline_params.get("local", "false") == "direct":
            hadoop_conf.set("fs.s3a.aws.credentials.provider", "org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider")

        hadoop_conf.set("fs.s3a.enableServerSideEncryption", "true")
        hadoop_conf.set("fs.s3a.connection.maximum", "100000")
        # hadoop_conf.set("fs.s3a.endpoint", "s3." + constants.aws_region + ".amazonaws.com")
        hadoop_conf.set("fs.s3a.endpoint", "s3.amazonaws.com")
        # hadoop_conf.set("delta.logRetentionDuration", "36500")
        # hadoop_conf.set("delta.deletedFileRetentionDuration", "365")

        self.spark = SparkSession(sc) \
            .builder \
            .appName("adl_metrics") \
            .config("spark.sql.autoBroadcastJoinThreshold", -1) \
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
            .config("spark.driver.memory", "12g") \
            .config("spark.driver.maxResultSize", "4g") \
            .config('spark.executor.cores', '4') \
            .config('spark.executor.memory', '12g') \
            .config("spark.python.worker.memory", '12g') \
            .config("spark.yarn.executor.memoryOverhead", '1200') \
            .config("spark.sql.debug.maxToStringFields", 1000) \
            .config("spark.databricks.delta.retentionDurationCheck.enabled", "false") \
            .getOrCreate()

        sqlContext = SQLContext(sparkContext=sc, sparkSession=self.spark)
        sqlContext.setConf("spark.sql.parquet.compression.codec","gzip")

    def vacuum_tables(self, base_path, table_list, is_optimize=False, is_vacuum=False):
        from delta.tables import DeltaTable
        table_list = [base_path + t for t in table_list.split(",")]

        region = self.cmdline_params.get("param3", "")
        #if (region == ""):
        #    region = self.get_aws_region()
        print("Using aws-region:" + region)

        for table_path in table_list:
            print("Process table " + table_path)
            deltaTable = DeltaTable.forPath(self.spark, table_path)
            if is_optimize:
                res = deltaTable.optimize().executeCompaction()
                res.show(truncate=False)
                print("Optimize finished")
            if is_vacuum:
                deltaTable.vacuum(0)
                print("Vacuum finished")

    def start(self):

        # initialize spark
        self.initalize_spark()
        # from delta.tables import DeltaTable
        print("Spark initialization finished")

        # read csv file
        base_path = self.cmdline_params.get('base-path', None)
        table_list = self.cmdline_params.get('table-list', None)
        is_optimize = self.cmdline_params.get('optimize', "false") == 'true'
        is_vacuum = self.cmdline_params.get('vacuum', "false") == 'true'
        if not is_vacuum and not is_vacuum:
            print("Both optimize and vacuum flags are set to false, exit...")
            exit(0)
        self.vacuum_tables(base_path, table_list, is_optimize, is_vacuum)
        print("Finish...")


if __name__ == "__main__":
    vacuum_util = DeltaTableVacuum()
    vacuum_util.start()
