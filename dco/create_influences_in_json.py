import os
import sys

import mysql.connector
from mysql.connector import <PERSON><PERSON>r
import traceback

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
print ("Learning dir:",learning_dir)
sys.path.append(learning_dir + "/common/pyUtils")

print (sys.path)
#

import aktana_ml_utils as mlutils

#import common.pyUtils.snowflake_python_base as sfpb
#import common.pyUtils.run_sql_on_snowflake as sfsql
import pandas as pd
import numpy as np
import time
import requests
import json

def get_influences_from_view(env_params, tunnel_port, view):
    """
    PURPOSE:
        retrieve list of influences as a dataframe with all influence columns
    INPUTS:
        env parameters to connect
        tunnel port
    RETURNS:
        Returns dataframe of list of influences
    """

    stagedb = env_params["rds-stagedbname"]

    try:
        conn = connect_dse_database(env_params, tunnel_port)
        df = pd.read_sql(f"select * from {stagedb}.{view};", con=conn)
        conn.close()

        #df.rename(columns={"accountUID": "accountUid", "productUID": "products", "scaledPriorityScore_akt": "influenceValue"}, inplace=True)
        #df['influenceUID'] = 'AccountProductPriority'

        print(df.info())
        print (df.head(10))

        print('number of influences = {}'.format(df.shape[0]))

    except Exception as e:
        print("Could not read influences.")
        print(e)
        exit(-1)

    return (df)

def get_accountpriority_from_view(env_params, tunnel_port, view):
    """
    PURPOSE:
        retrieve list of influences as a dataframe with all influence columns
    INPUTS:
        env parameters to connect
        tunnel port
    RETURNS:
        Returns dataframe of list of influences
    """

    stagedb = env_params["rds-stagedbname"]

    try:
        conn = connect_dse_database(env_params, tunnel_port)
        df = pd.read_sql(f"select accountUID as accountUid, productUID as products, scaledPriorityScore_akt as influenceValue from {stagedb}.AKT_AccountProduct_PriorityScore_v;", con=conn)
        conn.close()

        df['influenceUID'] = 'AccountProductPriority'

        print(df.info())
        print (df.head(10))

        print('number of AccountPriority influences = {}'.format(df.shape[0]))

    except Exception as e:
        print("Could not read influences.")
        print(e)
        exit(-1)

    return (df)

def get_channelaffinity_from_view(env_params, tunnel_port, view):
    """
    PURPOSE:
        retrieve list of influences as a dataframe with all influence columns
    INPUTS:
        env parameters to connect
        tunnel port
    RETURNS:
        Returns dataframe of list of influences
    """

    stagedb = env_params["rds-stagedbname"]

    try:
        conn = connect_dse_database(env_params, tunnel_port)
        # df = pd.read_sql(f"select accountUID as accountUid, 'VISIT' as channel, F2F_Affinity as influenceValue from genentechusdev_stage.AKT_AffinityMonitor_Processed_v where F2F_Affinity is not null " \
        #     " UNION select accountUID as accountUid, 'SEND' as channel, RTE_Affinity as influenceValue from genentechusdev_stage.AKT_AffinityMonitor_Processed_v where RTE_Affinity is not null " \
        #     " UNION select accountUID as accountUid, 'WEB_EDETAIL' as channel, Remote_Affinity as influenceValue from genentechusdev_stage.AKT_AffinityMonitor_Processed_v where Remote_Affinity is not null " \
        #     " UNION select accountUID as accountUid, 'LYTICS' as channel, LyticsMediaBanner_Affinity as influenceValue from genentechusdev_stage.AKT_AffinityMonitor_Processed_v where LyticsMediaBanner_Affinity is not null " \
        #     ";", con=conn)
        df = pd.read_sql(f"select accountUID as accountUid, 'VISIT' as channel, VISIT_CHANNEL as influenceValue from {stagedb}.AKT_AffinityMonitor_Processed where VISIT_CHANNEL is not null " \
            f" UNION select accountUID as accountUid, 'SEND' as channel, SEND_CHANNEL as influenceValue from {stagedb}.AKT_AffinityMonitor_Processed where SEND_CHANNEL is not null " \
            f" UNION select accountUID as accountUid, 'WEB_EDETAIL' as channel, WEB_INTERACTIVE_CHANNEL as influenceValue from {stagedb}.AKT_AffinityMonitor_Processed where WEB_INTERACTIVE_CHANNEL is not null  " \
            f" UNION select accountUID as accountUid, 'Lytics_MediaBanner' as channel, Lytics_MediaBanner as influenceValue from {stagedb}.AKT_AffinityMonitor_Processed where Lytics_MediaBanner is not null " \
            ";", con=conn)
        conn.close()

        # select accountUID as accountUid, 'VISIT' as channel, VISIT_CHANNEL as influenceValue from genentechusdev_stage.AKT_AffinityMonitor_Processed where VISIT_CHANNEL is not null
        # UNION select accountUID as accountUid, 'SEND' as channel, SEND_CHANNEL as influenceValue from genentechusdev_stage.AKT_AffinityMonitor_Processed where SEND_CHANNEL is not null
        # UNION select accountUID as accountUid, 'WEB_EDETAIL' as channel, WEB_INTERACTIVE_CHANNEL as influenceValue from genentechusdev_stage.AKT_AffinityMonitor_Processed where WEB_INTERACTIVE_CHANNEL is not null 
        # UNION select accountUID as accountUid, 'Lytics_MediaBanner' as channel, Lytics_MediaBanner as influenceValue from genentechusdev_stage.AKT_AffinityMonitor_Processed where Lytics_MediaBanner is not null ;

        df['influenceUID'] = 'ChannelPropensity'

        print(df.info())
        print (df.head(10))

    except Exception as e:
        print("Could not read channel affinity.")
        print(e)
        exit(-1)

    return (df)

def connect_dse_database(env_params, tunnel_port):
    if (tunnel_port == ""):
        rds_host = env_params["rds-server"]
        tunnel_port = "3306"
    else:
        rds_host = "127.0.0.1"

    connection = mysql.connector.connect(host=rds_host, user=env_params["rds-user"], password=env_params["rds-password"],
                                            database=env_params["rds-enginedbname"], port=tunnel_port)
    return connection

def intersection(lst1, lst2):
    return list(set(lst1) & set(lst2))

def write_influences_json(env_params, df, s3file):

    try:

        influenceCols = ['influenceUID', 'influenceValue', 'isVeto', 'accountUid', 'segment', 'repUid',
                                    'suggestionCandidateUid',
                                    'type', 'products', 'channel', 'startDate', 'endDate', 'configId', 'factorUid', 'useCaseTagId', 'actionTypeId', 'actorTypeId']


        nested_cols = ['influence', 'account', 'rep', 'suggestion', 'channelDetails']
        unnested_cols = ['products', 'channel', 'startDate', 'endDate', 'configId']
        nexted = {
            'influence': ['influenceUID', 'influenceValue', 'isVeto'],
            'account': ['accountUid', 'segment'],
            'rep': ['repUid'],
            'suggestion': ['suggestionCandidateUid', 'type', 'factorUid', 'useCaseTagId'],
            'channelDetails': ['actionTypeId', 'actorTypeId']
        }

        db_cols = set(df.columns)
        final_cols = list(set(unnested_cols) & db_cols)
        for groupName in nested_cols:
            nested_cols = set(nexted[groupName])
            result_cols = list(nested_cols & db_cols)
            if result_cols:
                dftemp = df
                if 'repUid' in result_cols:
                    dftemp = df.rename(columns={"repUid": "uid"})
                    result_cols.remove('repUid')
                    result_cols.append('uid')    
                if 'accountUid' in result_cols:
                    dftemp = df.rename(columns={"accountUid": "uid"})    
                    result_cols.remove('accountUid')
                    result_cols.append('uid')    
                df[groupName]  = dftemp[result_cols].to_dict('records')
                final_cols.append(groupName)
        df = df[final_cols]
        print(df.info())
        print (df.head(10))
     
        #Save output to parquet
        df.to_parquet(s3file + '.parquet', compression='gzip') 

        #Save output to json
        result = df.to_json(orient="records")
        parsed = json.loads(result)
        json_object = json.dumps(parsed, indent=4)    
        with open(s3file, "w") as outfile:
            outfile.write(json_object)

        #Code to verify the file was written correctly, the file below will be the same as the file created above
        #final_df = pd.DataFrame.from_dict(parsed, orient="columns")
        #final_df.to_parquet(s3file + '2.parquet', compression='gzip') 

    except Exception as e:
        print("Creating influences file failed")
        traceback.print_exc()
        print(e)
        exit(1)

def main():
    ml_utils = mlutils.aktana_ml_utils()

    cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:t:v:s:", ["customer=","env=","app=","region=", "ecosystem=", "tunnelport=", "view=", "s3file="])
    print(metadata_params)
    print(cmdline_params)

    region = cmdline_params['region']
    env = cmdline_params['env']
    customer = cmdline_params['customer']

    snowflake_params = ml_utils.get_snowflake_metadata()
    print(snowflake_params)

    env_params = ml_utils.get_env_metadata()
    print(env_params)

    tunnel_port = ""
    if (cmdline_params.get("t","") != ""):
        tunnel_port = cmdline_params.get("t","")
    if (cmdline_params.get("tunnelport","") != ""):
        tunnel_port = cmdline_params.get("tunnelport","")

    df = get_influences_from_view(env_params, tunnel_port, cmdline_params.get("view",""))
    if (df.shape[0] > 0):
        write_influences_json(env_params, df, "dco_influences." + cmdline_params.get("s3file",""))
    else:
        print('No AccountPriority influences to write. Skipping...')

    # df = get_accountpriority_from_view(env_params, tunnel_port, cmdline_params.get("view",""))
    # if (df.shape[0] > 0):
    #     write_influences_json(env_params, df, "accountpriority." + cmdline_params.get("s3file",""))
    # else:
    #     print('No AccountPriority influences to write. Skipping...')

    # df = get_channelaffinity_from_view(env_params, tunnel_port, cmdline_params.get("view",""))
    # if (df.shape[0] > 0):
    #     write_influences_json(env_params, df, "channelaffinity." +cmdline_params.get("s3file",""))
    # else:
    #     print('No ChannelAffinity influences to write. Skipping...')

if __name__ == "__main__":
    main()
