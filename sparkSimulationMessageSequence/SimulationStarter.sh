#!/bin/sh

# List of arguments to the python Script
#DB_HOST="127.0.0.1"
#DB_USER="pfizerusadmin"
#DB_PASSWORD="njTB95MacVDLaMUAnKHdXubOkVPyCU"
#DSE_DB_NAME="pfizerusdev"
#DB_PORT="33066"
DB_HOST="127.0.0.1"
DB_USER="appadmin"
DB_PASSWORD="rk9sKQSx1ziqUb8GfS2XrmYS4Tsw6T"
DSE_DB_NAME="pfizerusprod"
MSO_BUILD_UID="72bf6fd6-1533-4072-ad6a-f7aa242ba903"
LEARNING_HOME="/Users/<USER>/code/INT-955"

# Generate the simulation script path
SIMULATION_SCIRPT="SimulationMessageSequenceDriver.py"
CURRENT_DIR_PATH=$(dirname "$0")
SIMULATION_SCRIPT_PATH="$CURRENT_DIR_PATH/$SIMULATION_SCIRPT"

# OPTION-1: Passing the MSO build directory which will allow us to read the learning.properties file
#python $SIMULATION_SCRIPT_PATH $DB_HOST $DB_USER $DB_PASSWORD $DSE_DB_NAME $DB_PORT $MSO_BUILD_DIR

# OPTION-2: Passing Learning Home directory and MSO Build UID
python $SIMULATION_SCRIPT_PATH $DB_HOST $DB_USER $DB_PASSWORD $DSE_DB_NAME $LEARNING_HOME $MSO_BUILD_UID