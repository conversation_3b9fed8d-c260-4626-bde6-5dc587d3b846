#!/bin/sh

# List of arguments to the python Script
DB_HOST="pfizerusrds-uat2.aktana.com"
DB_USER="pfizerusadmin"
DB_PASSWORD="njTB95MacVDLaMUAnKHdXubOkVPyCU"
DSE_DB_NAME="pfizerusprod"
DB_PORT="3306"
LEARNING_HOME="/mnt/aktana/apps/learning"
SIM_RUN_UID=""

# Generate the simulation script path
SIMULATION_SCIRPT="SimulationStarter.py"
CURRENT_DIR_PATH=$(dirname "$0")
SIMULATION_SCRIPT_PATH="$CURRENT_DIR_PATH/$SIMULATION_SCIRPT"


# OPTION-3: Running in parallel reading from database
python $SIMULATION_SCRIPT_PATH $DB_HOST $DB_USER $DB_PASSWORD $DSE_DB_NAME $LEARNING_HOME