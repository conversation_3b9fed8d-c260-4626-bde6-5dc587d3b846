# image: python:3.6 # Choose an image matching your project needs

clone:
      depth: full              # SonarCloud scanner needs the full history to assign issues properly

definitions:
      caches:
            sonar: ~/.sonar/cache  # Caching SonarCloud artifacts will speed up your build
      steps:
      - step: &install-npm
              name: Install npm
              image: node
              script:
                - npm install
              services:
                - mysql
      - step: &run-unit-tests
              name: Run unit tests
              image: jupyter/all-spark-notebook
              script:
                - pip install -r common/unitTest/requirements.txt
                - bin/coverage/sonarQubeRunUnitTests.sh
              artifacts:
                - bin/coverage/**
              services:
                - mysql
      - step: &build-test-sonarcloud
              name: Build, test and analyze on SonarCloud
              image: python:3.6
              caches:
              #- pip          # See https://confluence.atlassian.com/bitbucket/caching-dependencies-895552876.html
              - sonar
              script:
              #- **************************           # Build your project and run
              - pipe: sonarsource/sonarcloud-scan:1.0.1
                variables:
                  #EXTRA_ARGS: -Dsonar.exclusions=**common/pyUtils/use_case_example.py,**/vendor/**
                  EXTRA_ARGS: -Dsonar.sources=anchor,anchorAccuracy,dataAccessLayer,engagement,messageSequence,messageTiming,paraRun,simulationMessageSequence,sparkEngagement,sparkLearningPackage,sparkMSOPredictorOptimizer,sparkMessageSequence,sparkMessageTiming,sparkRepEngagementCalculator,sparkSimulationMessageSequence,sparkTTEPredictorOptimizer -Dsonar.exclusions=**common/pyUtils/use_case_example.py,**learning/learningPackage/src/** -Dsonar.test.inclusions=**/tests/**/* -Dsonar.python.coverage.reportPaths=bin/coverage/*.xml
      - step: &check-quality-gate-sonarcloud
              name: Check the Quality Gate on SonarCloud
              image: python:3.6
              script:
              - pipe: sonarsource/sonarcloud-quality-gate:0.1.3

      services:
              mysql:
                image: mysql:5.7
                variables:
                  MYSQL_DATABASE: 'test'
                  MYSQL_ROOT_PASSWORD: 'root_pswd'

pipelines:                 # More info here: https://confluence.atlassian.com/bitbucket/configure-bitbucket-pipelines-yml-792298910.html
  branches:
    master:
      - step: *build-test-sonarcloud
      - step: *check-quality-gate-sonarcloud
    develop:
      - step: *install-npm
      - step: *run-unit-tests
      - step: *build-test-sonarcloud
      - step: *check-quality-gate-sonarcloud
    INT-*:
      - step: *install-npm
      - step: *run-unit-tests
      - step: *build-test-sonarcloud
      - step: *check-quality-gate-sonarcloud
    RB-*:
      - step: *build-test-sonarcloud
      - step: *check-quality-gate-sonarcloud
  pull-requests:
    '**':
      - step: *install-npm
      - step: *run-unit-tests
      - step: *build-test-sonarcloud
      - step: *check-quality-gate-sonarcloud
