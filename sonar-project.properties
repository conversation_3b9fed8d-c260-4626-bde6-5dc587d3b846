sonar-project.properties
# must be unique in a given SonarQube instance
sonar.projectKey=aktana_learning
sonar.organization=aktana
# --- optional properties ---

# defaults to project key
sonar.projectName=Learning
# defaults to 'not provided'
sonar.projectVersion=${env.BUILD_NUMBER}

# Path is relative to the sonar-project.properties file. Defaults to .
sonar.sources=anchor,anchorAccuracy,dataAccessLayer,engagement,messageSequence,messageTiming,paraRun,simulationMessageSequence,sparkEngagement,sparkLearningPackage,sparkMSOPredictorOptimizer,sparkMessageSequence,sparkMessageTiming,sparkRepEngagementCalculator,sparkSimulationMessageSequence,sparkTTEPredictorOptimizer

# Encoding of the source code. Default is default system encoding
sonar.sourceEncoding=UTF-8
sonar.c.file.suffixes=-
sonar.cpp.file.suffixes=-
sonar.objc.file.suffixes=-
sonar.exclusions=**common/pyUtils/use_case_example.py,**learning/learningPackage/src/**
sonar.test.inclusions=**/tests/**/*
sonar.python.coverage.reportPaths=bin/coverage/*.xml
# add test comment