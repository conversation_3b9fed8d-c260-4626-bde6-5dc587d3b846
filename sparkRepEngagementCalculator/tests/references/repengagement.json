{"year": 2018, "month": 1, "repUID": "g", "repName": "Rep7", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 1, "totalSuggestionsDeliveredTimes": 2, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "g", "repName": "Rep7", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 1, "totalSuggestionsDeliveredTimes": 1, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "h", "repName": "Rep8", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 1, "totalSuggestionsDeliveredTimes": 1, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "h", "repName": "Rep8", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 2, "totalSuggestionsDeliveredTimes": 2, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "e", "repName": "Rep5", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 2, "totalSuggestionsDeliveredTimes": 2, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "e", "repName": "Rep5", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 0, "totalSuggestionsDeliveredTimes": 1, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "f", "repName": "Rep6", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 2, "totalSuggestionsDeliveredTimes": 2, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "f", "repName": "Rep6", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 1, "totalSuggestionsDeliveredTimes": 1, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "b", "repName": "Rep2", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 2, "totalSuggestionsDeliveredTimes": 2, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "b", "repName": "Rep2", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 0, "totalSuggestionsDeliveredTimes": 1, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "c", "repName": "Rep3", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 0, "totalSuggestionsDeliveredTimes": 2, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "a", "repName": "Rep1", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 0, "totalSuggestionsDeliveredTimes": 2, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "c", "repName": "Rep3", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 1, "totalSuggestionsDeliveredTimes": 1, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "d", "repName": "Rep4", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 2, "totalSuggestionsDeliveredTimes": 2, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "d", "repName": "Rep4", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 0, "totalSuggestionsDeliveredTimes": 1, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "a", "repName": "Rep1", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 1, "totalSuggestionsDeliveredTimes": 1, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "i", "repName": "Rep9", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 2, "totalSuggestionsDeliveredTimes": 2, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "j", "repName": "Rep10", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 1, "totalSuggestionsDeliveredTimes": 1, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "i", "repName": "Rep9", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 1, "totalSuggestionsDeliveredTimes": 1, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
{"year": 2018, "month": 1, "repUID": "j", "repName": "Rep10", "repTeamUID": "A", "repTeamName": "A", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "engagedUniqueSuggestionsCount": 2, "totalSuggestionsDeliveredTimes": 2, "createdAt": "2020-02-07 13:51:00", "updatedAt": "2020-02-07 13:51:00"}
