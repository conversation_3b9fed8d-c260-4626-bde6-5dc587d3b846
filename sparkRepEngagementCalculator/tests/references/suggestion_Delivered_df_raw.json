{"externalId": 1001, "suggestionLifeCycleId": 3, "runId": "ag", "runUID": "", "repTeamId": 3, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-03", "repId": "2018-01-15", "repUID": "a", "repName": "Rep1", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ag1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "No Action Taken", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1002, "suggestionLifeCycleId": 2, "runId": "ai", "runUID": "", "repTeamId": 2, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-02", "repId": "2018-01-15", "repUID": "b", "repName": "Rep2", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ai1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "No Action Taken", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1006, "suggestionLifeCycleId": 1, "runId": "q", "runUID": "", "repTeamId": 1, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-02", "repId": "2018-01-15", "repUID": "f", "repName": "Rep6", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "q1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1010, "suggestionLifeCycleId": 3, "runId": "ad", "runUID": "", "repTeamId": 3, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-03", "repId": "2018-01-15", "repUID": "j", "repName": "Rep10", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ad1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1002, "suggestionLifeCycleId": 3, "runId": "aj", "runUID": "", "repTeamId": 3, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-03", "repId": "2018-01-15", "repUID": "b", "repName": "Rep2", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "aj1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Dismissed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1006, "suggestionLifeCycleId": 3, "runId": "ar", "runUID": "", "repTeamId": 3, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-01", "repId": "2018-01-15", "repUID": "f", "repName": "Rep6", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ar1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Dismissed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1003, "suggestionLifeCycleId": 2, "runId": "al", "runUID": "", "repTeamId": 2, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-02", "repId": "2018-01-15", "repUID": "c", "repName": "Rep3", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "al1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Dismissed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1005, "suggestionLifeCycleId": 2, "runId": "ap", "runUID": "", "repTeamId": 2, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-01", "repId": "2018-01-15", "repUID": "e", "repName": "Rep5", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ap1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Dismissed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1001, "suggestionLifeCycleId": 1, "runId": "ae", "runUID": "", "repTeamId": 1, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-01", "repId": "2018-01-15", "repUID": "a", "repName": "Rep1", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ae", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "No Action Taken", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1008, "suggestionLifeCycleId": 1, "runId": "v", "runUID": "", "repTeamId": 1, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-01", "repId": "2018-01-15", "repUID": "h", "repName": "Rep8", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "v1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1009, "suggestionLifeCycleId": 1, "runId": "y", "runUID": "", "repTeamId": 1, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-02", "repId": "2018-01-15", "repUID": "i", "repName": "Rep9", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "y1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1008, "suggestionLifeCycleId": 2, "runId": "w", "runUID": "", "repTeamId": 2, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-02", "repId": "2018-01-15", "repUID": "h", "repName": "Rep8", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "w1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1007, "suggestionLifeCycleId": 2, "runId": "u", "runUID": "", "repTeamId": 2, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-03", "repId": "2018-01-15", "repUID": "g", "repName": "Rep7", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "u1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1001, "suggestionLifeCycleId": 2, "runId": "af", "runUID": "", "repTeamId": 2, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-02", "repId": "2018-01-15", "repUID": "a", "repName": "Rep1", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "af1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Dismissed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1004, "suggestionLifeCycleId": 1, "runId": "l", "runUID": "", "repTeamId": 1, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-03", "repId": "2018-01-15", "repUID": "d", "repName": "Rep4", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "l1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1007, "suggestionLifeCycleId": 3, "runId": "as", "runUID": "", "repTeamId": 3, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-01", "repId": "2018-01-15", "repUID": "g", "repName": "Rep7", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "as1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "No Action Taken", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1006, "suggestionLifeCycleId": 2, "runId": "r", "runUID": "", "repTeamId": 2, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-03", "repId": "2018-01-15", "repUID": "f", "repName": "Rep6", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "r1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1004, "suggestionLifeCycleId": 2, "runId": "an", "runUID": "", "repTeamId": 2, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-01", "repId": "2018-01-15", "repUID": "d", "repName": "Rep4", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "an1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Dismissed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1009, "suggestionLifeCycleId": 3, "runId": "aa", "runUID": "", "repTeamId": 3, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-01", "repId": "2018-01-15", "repUID": "i", "repName": "Rep9", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "aa1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1010, "suggestionLifeCycleId": 1, "runId": "ab", "runUID": "", "repTeamId": 1, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-01", "repId": "2018-01-15", "repUID": "j", "repName": "Rep10", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ab1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1005, "suggestionLifeCycleId": 1, "runId": "o", "runUID": "", "repTeamId": 1, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-03", "repId": "2018-01-15", "repUID": "e", "repName": "Rep5", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "o1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1007, "suggestionLifeCycleId": 1, "runId": "t", "runUID": "", "repTeamId": 1, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-02", "repId": "2018-01-15", "repUID": "g", "repName": "Rep7", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "t1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1005, "suggestionLifeCycleId": 3, "runId": "aq", "runUID": "", "repTeamId": 3, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-02", "repId": "2018-01-15", "repUID": "e", "repName": "Rep5", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "aq1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "No Action Taken", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1003, "suggestionLifeCycleId": 1, "runId": "ak", "runUID": "", "repTeamId": 1, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-01", "repId": "2018-01-15", "repUID": "c", "repName": "Rep3", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ak1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "No Action Taken", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1009, "suggestionLifeCycleId": 2, "runId": "z", "runUID": "", "repTeamId": 2, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-03", "repId": "2018-01-15", "repUID": "i", "repName": "Rep9", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "z1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1008, "suggestionLifeCycleId": 3, "runId": "x", "runUID": "", "repTeamId": 3, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-03", "repId": "2018-01-15", "repUID": "h", "repName": "Rep8", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "x1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1004, "suggestionLifeCycleId": 3, "runId": "ao", "runUID": "", "repTeamId": 3, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-02", "repId": "2018-01-15", "repUID": "d", "repName": "Rep4", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ao1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "No Action Taken", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1010, "suggestionLifeCycleId": 2, "runId": "ac", "runUID": "", "repTeamId": 2, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-02", "repId": "2018-01-15", "repUID": "j", "repName": "Rep10", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ac1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Completed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 1, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1003, "suggestionLifeCycleId": 3, "runId": "am", "runUID": "", "repTeamId": 3, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-03", "repId": "2018-01-15", "repUID": "c", "repName": "Rep3", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "am1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "No Action Taken", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Target", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
{"externalId": 1002, "suggestionLifeCycleId": 1, "runId": "ah", "runUID": "", "repTeamId": 1, "repTeamUID": "", "repTeamName": "", "seConfigId": "", "seConfigName": 1, "runGroupId": "a", "suggestedDate": "", "startDateLocal": "2018-02-01", "repId": "2018-01-15", "repUID": "b", "repName": "Rep2", "repCreatedAt": "", "accountId": "", "accountUID": "", "runRepDateSuggestionId": "", "accountName": "", "detailRepActionTypeId": 3, "detailRepActionTypeUID": "", "detailRepActionName": "", "runRepDateSuggestionDetailId": "", "productId": "", "productUID": "", "productName": "", "messageId": "", "messageUID": "", "messageName": "", "suggestionUID": "", "suggestionReferenceId": "ah1", "lastViewedAt": "", "viewedAt": "", "viewedDuration": "", "actionTaken": "Suggestions Dismissed", "actionTaken_dt": "", "isSuggestionCompleted": "", "isSuggestionCompletedDirect": "", "isSuggestionCompletedInfer": "", "isSuggestionDismissed": "", "dismissReasonType": "", "dismissReason": "", "dismissReason_dt": "", "isSuggestionActive": "", "facilityLatitude": "", "facilityLongitude": "", "facilityGeoLocationString": "", "repLatitude": "", "repLongitude": "", "territoryCityName": "", "districtName": "", "territoryId": "", "territoryName": "", "regionName": "", "regionGroup": "", "dismissedAt": "", "dismissCount": "", "lastPublishedAt": "", "createdAt": "2018-02-15T12:00:00.000-08:00", "updatedAt": "2018-02-15T12:00:00.000-08:00", "reportedInteractionUID": "", "inferredInteractionUID": "", "interactionUID": "", "completedAt": "", "inferredAt": "", "isFirstSuggestedDate": "", "isLastSuggestedDate": "", "suggestionDriver": "Trigger", "timeoffday": "", "isHighestRunidForDay": "", "holiday_weekend_Flag": "", "crmFieldName": "", "reasonText": "", "reasonRank": "", "runRepDateSuggestionReasonId": "", "repRole": "", "repBag": "", "dmUID": "", "dmName": "", "interactionId": "", "isCompleted": 0, "startDateTime": "2018-01-15T12:00:00.000-08:00", "isREMix": "", "isDSESpark": "", "arc_createdAt": "2019-01-01T12:00:01.000-08:00", "arc_updatedAt": "2019-01-01T12:00:01.000-08:00"}
