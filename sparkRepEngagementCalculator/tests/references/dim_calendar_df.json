{"datefield": "2018-01-01", "day_of_week": 2, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 1, "week_label": "2018-01-05", "week_value": "2018-01-05", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 1, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-02", "day_of_week": 3, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 2, "week_label": "2018-01-05", "week_value": "2018-01-05", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-03", "day_of_week": 4, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 3, "week_label": "2018-01-05", "week_value": "2018-01-05", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-04", "day_of_week": 5, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 4, "week_label": "2018-01-05", "week_value": "2018-01-05", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-05", "day_of_week": 6, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 5, "week_label": "2018-01-05", "week_value": "2018-01-05", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-01-06", "day_of_week": 7, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 6, "week_label": "2018-01-05", "week_value": "2018-01-05", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-01-07", "day_of_week": 1, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 7, "week_label": "2018-01-12", "week_value": "2018-01-12", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-08", "day_of_week": 2, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 8, "week_label": "2018-01-12", "week_value": "2018-01-12", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-09", "day_of_week": 3, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 9, "week_label": "2018-01-12", "week_value": "2018-01-12", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-10", "day_of_week": 4, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 10, "week_label": "2018-01-12", "week_value": "2018-01-12", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-11", "day_of_week": 5, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 11, "week_label": "2018-01-12", "week_value": "2018-01-12", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-12", "day_of_week": 6, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 12, "week_label": "2018-01-12", "week_value": "2018-01-12", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-01-13", "day_of_week": 7, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 13, "week_label": "2018-01-12", "week_value": "2018-01-12", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-01-14", "day_of_week": 1, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 14, "week_label": "2018-01-19", "week_value": "2018-01-19", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-15", "day_of_week": 2, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 15, "week_label": "2018-01-19", "week_value": "2018-01-19", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 1, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-16", "day_of_week": 3, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 16, "week_label": "2018-01-19", "week_value": "2018-01-19", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-17", "day_of_week": 4, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 17, "week_label": "2018-01-19", "week_value": "2018-01-19", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-18", "day_of_week": 5, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 18, "week_label": "2018-01-19", "week_value": "2018-01-19", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-19", "day_of_week": 6, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 19, "week_label": "2018-01-19", "week_value": "2018-01-19", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-01-20", "day_of_week": 7, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 20, "week_label": "2018-01-19", "week_value": "2018-01-19", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-01-21", "day_of_week": 1, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 21, "week_label": "2018-01-26", "week_value": "2018-01-26", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-22", "day_of_week": 2, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 22, "week_label": "2018-01-26", "week_value": "2018-01-26", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-23", "day_of_week": 3, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 23, "week_label": "2018-01-26", "week_value": "2018-01-26", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-24", "day_of_week": 4, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 24, "week_label": "2018-01-26", "week_value": "2018-01-26", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-25", "day_of_week": 5, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 25, "week_label": "2018-01-26", "week_value": "2018-01-26", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-26", "day_of_week": 6, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 26, "week_label": "2018-01-26", "week_value": "2018-01-26", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-01-27", "day_of_week": 7, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 27, "week_label": "2018-01-26", "week_value": "2018-01-26", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-01-28", "day_of_week": 1, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 28, "week_label": "2018-02-02", "week_value": "2018-02-02", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-29", "day_of_week": 2, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 29, "week_label": "2018-02-02", "week_value": "2018-02-02", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-30", "day_of_week": 3, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 30, "week_label": "2018-02-02", "week_value": "2018-02-02", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-01-31", "day_of_week": 4, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 1, "day_of_month": 31, "week_label": "2018-02-02", "week_value": "2018-02-02", "month_value": "2018-01", "month_value_full": "2018-01-01", "month_label": "January 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 1}
{"datefield": "2018-02-01", "day_of_week": 5, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 1, "week_label": "2018-02-02", "week_value": "2018-02-02", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-02-02", "day_of_week": 6, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 2, "week_label": "2018-02-02", "week_value": "2018-02-02", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-02-03", "day_of_week": 7, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 3, "week_label": "2018-02-02", "week_value": "2018-02-02", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-02-04", "day_of_week": 1, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 4, "week_label": "2018-02-09", "week_value": "2018-02-09", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-02-05", "day_of_week": 2, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 5, "week_label": "2018-02-09", "week_value": "2018-02-09", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-02-06", "day_of_week": 3, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 6, "week_label": "2018-02-09", "week_value": "2018-02-09", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-02-07", "day_of_week": 4, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 7, "week_label": "2018-02-09", "week_value": "2018-02-09", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-02-08", "day_of_week": 5, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 8, "week_label": "2018-02-09", "week_value": "2018-02-09", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-02-09", "day_of_week": 6, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 9, "week_label": "2018-02-09", "week_value": "2018-02-09", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-02-10", "day_of_week": 7, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 10, "week_label": "2018-02-09", "week_value": "2018-02-09", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 1, "is_last_day_of_month": 0}
{"datefield": "2018-02-11", "day_of_week": 1, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 11, "week_label": "2018-02-16", "week_value": "2018-02-16", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-02-12", "day_of_week": 2, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 12, "week_label": "2018-02-16", "week_value": "2018-02-16", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-02-13", "day_of_week": 3, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 13, "week_label": "2018-02-16", "week_value": "2018-02-16", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-02-14", "day_of_week": 4, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 14, "week_label": "2018-02-16", "week_value": "2018-02-16", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
{"datefield": "2018-02-15", "day_of_week": 5, "calendar_year": 2018, "calendar_quarter": 1, "quarter_value": "2018Q1", "month_of_year": 2, "day_of_month": 15, "week_label": "2018-02-16", "week_value": "2018-02-16", "month_value": "2018-02", "month_value_full": "2018-02-01", "month_label": "February 1, 2018", "is_holiday": 0, "is_weekend": 0, "is_last_day_of_month": 0}
