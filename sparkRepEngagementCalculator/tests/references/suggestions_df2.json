{"year": 2018, "month": 1, "repUID": "a", "repName": "Rep1", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "af1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Dismissed"}
{"year": 2018, "month": 1, "repUID": "a", "repName": "Rep1", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "ae", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "No Action Taken"}
{"year": 2018, "month": 1, "repUID": "a", "repName": "Rep1", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "ag1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "No Action Taken"}
{"year": 2018, "month": 1, "repUID": "b", "repName": "Rep2", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "ah1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Dismissed"}
{"year": 2018, "month": 1, "repUID": "b", "repName": "Rep2", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "aj1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Dismissed"}
{"year": 2018, "month": 1, "repUID": "b", "repName": "Rep2", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "ai1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "No Action Taken"}
{"year": 2018, "month": 1, "repUID": "c", "repName": "Rep3", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "am1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "No Action Taken"}
{"year": 2018, "month": 1, "repUID": "c", "repName": "Rep3", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "ak1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "No Action Taken"}
{"year": 2018, "month": 1, "repUID": "c", "repName": "Rep3", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "al1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Dismissed"}
{"year": 2018, "month": 1, "repUID": "d", "repName": "Rep4", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "ao1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "No Action Taken"}
{"year": 2018, "month": 1, "repUID": "d", "repName": "Rep4", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "an1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Dismissed"}
{"year": 2018, "month": 1, "repUID": "d", "repName": "Rep4", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "l1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "e", "repName": "Rep5", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "aq1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "No Action Taken"}
{"year": 2018, "month": 1, "repUID": "e", "repName": "Rep5", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "o1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "e", "repName": "Rep5", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "ap1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Dismissed"}
{"year": 2018, "month": 1, "repUID": "f", "repName": "Rep6", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "r1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "f", "repName": "Rep6", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "ar1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Dismissed"}
{"year": 2018, "month": 1, "repUID": "f", "repName": "Rep6", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "q1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "g", "repName": "Rep7", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "t1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "g", "repName": "Rep7", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "as1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "No Action Taken"}
{"year": 2018, "month": 1, "repUID": "g", "repName": "Rep7", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "u1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "h", "repName": "Rep8", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "x1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "h", "repName": "Rep8", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "w1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "h", "repName": "Rep8", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "v1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "i", "repName": "Rep9", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "z1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "i", "repName": "Rep9", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "aa1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "i", "repName": "Rep9", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "y1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "j", "repName": "Rep10", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Target", "suggestionReferenceId": "ac1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "j", "repName": "Rep10", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "ab1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
{"year": 2018, "month": 1, "repUID": "j", "repName": "Rep10", "seConfigId": 1, "seConfigName": "a", "repTeamUID": "A", "repTeamName": "A", "suggestionType": "Trigger", "suggestionReferenceId": "ad1", "territoryId": 1, "territoryName": "territoryA", "suggestionsDelivered": 1, "actionTaken": "Suggestions Completed"}
