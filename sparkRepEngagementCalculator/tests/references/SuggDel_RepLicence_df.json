{"startDateLocal": "2018-01-15", "repUID": "a", "repName": "Rep1", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "af1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-02", "actionTaken": "Suggestions Dismissed"}
{"startDateLocal": "2018-01-15", "repUID": "a", "repName": "Rep1", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "ae", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-01", "actionTaken": "No Action Taken"}
{"startDateLocal": "2018-01-15", "repUID": "a", "repName": "Rep1", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "ag1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-03", "actionTaken": "No Action Taken"}
{"startDateLocal": "2018-01-15", "repUID": "b", "repName": "Rep2", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "ah1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-01", "actionTaken": "Suggestions Dismissed"}
{"startDateLocal": "2018-01-15", "repUID": "b", "repName": "Rep2", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "aj1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-03", "actionTaken": "Suggestions Dismissed"}
{"startDateLocal": "2018-01-15", "repUID": "b", "repName": "Rep2", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "ai1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-02", "actionTaken": "No Action Taken"}
{"startDateLocal": "2018-01-15", "repUID": "c", "repName": "Rep3", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "am1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-03", "actionTaken": "No Action Taken"}
{"startDateLocal": "2018-01-15", "repUID": "c", "repName": "Rep3", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "ak1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-01", "actionTaken": "No Action Taken"}
{"startDateLocal": "2018-01-15", "repUID": "c", "repName": "Rep3", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "al1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-02", "actionTaken": "Suggestions Dismissed"}
{"startDateLocal": "2018-01-15", "repUID": "d", "repName": "Rep4", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "ao1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-02", "actionTaken": "No Action Taken"}
{"startDateLocal": "2018-01-15", "repUID": "d", "repName": "Rep4", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "an1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-01", "actionTaken": "Suggestions Dismissed"}
{"startDateLocal": "2018-01-15", "repUID": "d", "repName": "Rep4", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "l1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-03", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "e", "repName": "Rep5", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "aq1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-02", "actionTaken": "No Action Taken"}
{"startDateLocal": "2018-01-15", "repUID": "e", "repName": "Rep5", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "o1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-03", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "e", "repName": "Rep5", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "ap1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-01", "actionTaken": "Suggestions Dismissed"}
{"startDateLocal": "2018-01-15", "repUID": "f", "repName": "Rep6", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "r1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-03", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "f", "repName": "Rep6", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "ar1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-01", "actionTaken": "Suggestions Dismissed"}
{"startDateLocal": "2018-01-15", "repUID": "f", "repName": "Rep6", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "q1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-02", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "g", "repName": "Rep7", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "t1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-02", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "g", "repName": "Rep7", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "as1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-01", "actionTaken": "No Action Taken"}
{"startDateLocal": "2018-01-15", "repUID": "g", "repName": "Rep7", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "u1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-03", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "h", "repName": "Rep8", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "x1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-03", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "h", "repName": "Rep8", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "w1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-02", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "h", "repName": "Rep8", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "v1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-01", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "i", "repName": "Rep9", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "z1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-03", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "i", "repName": "Rep9", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "aa1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-01", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "i", "repName": "Rep9", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "y1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-02", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "j", "repName": "Rep10", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Target", "suggestionReferenceId": "ac1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-02", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "j", "repName": "Rep10", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "ab1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-01", "actionTaken": "Suggestions Completed"}
{"startDateLocal": "2018-01-15", "repUID": "j", "repName": "Rep10", "cluster": "a", "seConfigId": 1, "seConfigName": "a", "suggestionDriver": "Trigger", "suggestionReferenceId": "ad1", "territoryId": 1, "territoryName": "territoryA", "suggestedDate": "2018-02-03", "actionTaken": "Suggestions Completed"}
