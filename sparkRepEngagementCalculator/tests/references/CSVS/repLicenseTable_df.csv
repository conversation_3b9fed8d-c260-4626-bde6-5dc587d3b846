cluster, districtName, repName, territoryName, territoryId, externalId, customerId, regionName, activatedDate, isActivated, createdAt, updatedAt, repRole, repBag, seConfigId, testRunGroupId, RunGroupId, dmName, dmUID, startDate, endDate
a,,Rep1,territoryA,1,a,,,,,,,,,,,,,,2018-01-01T12:00:01.000-08:00,2030-01-01T12:00:01.000-08:00
a,,Rep2,territoryA,1,b,,,,,,,,,,,,,,2018-01-01T12:00:01.000-08:00,2030-01-01T12:00:01.000-08:00
a,,Rep3,territoryA,1,c,,,,,,,,,,,,,,2018-01-01T12:00:01.000-08:00,2030-01-01T12:00:01.000-08:00
a,,Rep4,territoryA,1,d,,,,,,,,,,,,,,2018-01-01T12:00:01.000-08:00,2030-01-01T12:00:01.000-08:00
a,,Rep5,territoryA,1,e,,,,,,,,,,,,,,2018-01-01T12:00:01.000-08:00,2030-01-01T12:00:01.000-08:00
a,,Rep6,territoryA,1,f,,,,,,,,,,,,,,2018-01-01T12:00:01.000-08:00,2030-01-01T12:00:01.000-08:00
a,,Rep7,territoryA,1,g,,,,,,,,,,,,,,2018-01-01T12:00:01.000-08:00,2030-01-01T12:00:01.000-08:00
a,,Rep8,territoryA,1,h,,,,,,,,,,,,,,2018-01-01T12:00:01.000-08:00,2030-01-01T12:00:01.000-08:00
a,,Rep9,territoryA,1,i,,,,,,,,,,,,,,2018-01-01T12:00:01.000-08:00,2030-01-01T12:00:01.000-08:00
a,,Rep10,territoryA,1,j,,,,,,,,,,,,,,2018-01-01T12:00:01.000-08:00,2030-01-01T12:00:01.000-08:00
