{"year": 2018, "month": 1, "repUID": "d", "repName": "Rep4", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 1}
{"year": 2018, "month": 1, "repUID": "e", "repName": "Rep5", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 1}
{"year": 2018, "month": 1, "repUID": "e", "repName": "Rep5", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 2}
{"year": 2018, "month": 1, "repUID": "b", "repName": "Rep2", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 2}
{"year": 2018, "month": 1, "repUID": "h", "repName": "Rep8", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 1}
{"year": 2018, "month": 1, "repUID": "a", "repName": "Rep1", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 2}
{"year": 2018, "month": 1, "repUID": "j", "repName": "Rep10", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 1}
{"year": 2018, "month": 1, "repUID": "i", "repName": "Rep9", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 1}
{"year": 2018, "month": 1, "repUID": "g", "repName": "Rep7", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 1}
{"year": 2018, "month": 1, "repUID": "h", "repName": "Rep8", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 2}
{"year": 2018, "month": 1, "repUID": "i", "repName": "Rep9", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 2}
{"year": 2018, "month": 1, "repUID": "d", "repName": "Rep4", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 2}
{"year": 2018, "month": 1, "repUID": "c", "repName": "Rep3", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 1}
{"year": 2018, "month": 1, "repUID": "j", "repName": "Rep10", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 2}
{"year": 2018, "month": 1, "repUID": "c", "repName": "Rep3", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 2}
{"year": 2018, "month": 1, "repUID": "a", "repName": "Rep1", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 1}
{"year": 2018, "month": 1, "repUID": "b", "repName": "Rep2", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 1}
{"year": 2018, "month": 1, "repUID": "g", "repName": "Rep7", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 2}
{"year": 2018, "month": 1, "repUID": "f", "repName": "Rep6", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Target", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 1}
{"year": 2018, "month": 1, "repUID": "f", "repName": "Rep6", "seConfigId": 1, "seConfigName": "a", "suggestionType": "Trigger", "territoryId": 1, "territoryName": "territoryA", "totalSuggestionsDeliveredTimes": 2}
