####################################################################################################################
#
#
# aktana- engagement estimates estimates Aktana Learning Engines.
#
# description: estimate the
#
# created by : <EMAIL>
# updated by : <EMAIL>
#
# updated on : 2019-03-12
#
# Copyright AKTANA (c) 2016.
#
#
####################################################################################################################

library(data.table)
library(sparklyr)
library(dplyr)

calculateTriggerEngagement <- function(sc, chl, useForProbability, interactions, suggestions)
{
    flog.info("calculateTriggerEngagement for channel %s ...", chl)

    # subset the interactions for the right channel and today date
    events <- interactions %>% filter(repActionTypeId==chl) %>% select(accountId,repId,date,reaction)

    # no need to have multiple records with the same account-rep-date combo
    events <- events %>%
      distinct(accountId, repId, date) %>%
      mutate(repActionTypeId=chl, reaction="Touchpoint")  # add back the two columns dropped due to distinct
    flog.info("finish initial prepare events")

    suggestions <- suggestions %>% filter(repActionTypeId==chl & date<=today & date>=sugStartDate) %>%
                                   select(repId,accountId,Suggestion_External_Id_vod__c,reaction,date)

    # rename fields
    suggestions <- dplyr::rename(suggestions, suggestion = Suggestion_External_Id_vod__c)

    # remove the AKTSUG~ prefix from the suggestion
    suggestions <- suggestions %>% mutate(suggestion = regexp_replace(suggestion, "AKTSUG~", ""))

    flog.info("finish prepare suggestions")

    # identify the trigger driven suggestions
    sugs <- suggestions %>% filter(like(suggestion, "%TRIG%"))

    # if reaction to suggestion is not in parameter includeReactions then set the reaction to IGNORE
    sugs <- sugs %>% mutate(reaction = ifelse(!(reaction %in% includeReactions), "Ignored", reaction))

    # for subsequent processing number the records in the new suggestion table
    sugs <- sdf_with_unique_id(sugs, id="recordId")               # just need an id not need to be sequencetial, so use sdf_with_unique_id instead of sdf_with_sequential_id
    sugs <- sugs %>% mutate(dur = 0)

    # subset the ignored suggestions
    ign <- sugs %>% filter(reaction == "Ignored")

    if(sdf_dim(ign)[1] > 0)                                                             # if there are any ignored suggestions than find the time from suggestion to next action
    {
        ign <- sdf_bind_rows(ign, events)                                               # append the ignored suggestions to the intereactions table (now called events)

        ign <- arrange(ign, repId, accountId, date)                                     # sort ign table as setup to find time between suggestion and next action

        # add the dur field to the table that contains that difference in time
        ign <- ign %>% group_by(repId, accountId) %>%
                       mutate(date_lag = lag(date), dur = as.integer(datediff(date, date_lag))) %>%
                       ungroup() %>% select (-date_lag)

        # move the dur field values to the rows that contain the suggestion associated with the duration
        ign <- ign %>% mutate(dur = lead(dur)) %>%
                       mutate(dur = ifelse(is.na(dur), 0, dur))

        # only include the appropriate types of differences
        ign <- ign %>% filter(reaction=="Ignored" & lead(reaction) == "Touchpoint")

        # merge the processed ignored table to the full suggestions table
        sugs <- sugs %>% left_join(select(ign, recordId, dur), by=c("recordId"), suffix = c("_x", "_y"))
        sugs <- sugs %>% select(-dur_x) %>% dplyr::rename(dur = dur_y)

        # if there is no subsequent intereaction after a suggestionthan set the duration to one more than the engageWindow
        sugs <- mutate(sugs, dur = ifelse(is.na(dur), engageWindow+1, dur))

        sugs <- sugs %>% mutate(dur = ifelse(reaction!="Ignored", 0, dur))    # if suggestions are not ignored then set the duration to 0
    }

    # setup to count the number of times that suggestions are engaged or no
    sugs <- sugs %>% mutate(ctr = 1, include = 0)

    # this is where the suggestions <= engageWindow are included
    sugs <- mutate(sugs, include = ifelse(dur<=engageWindow, 1, 0))

    # count the number of suggestions offered and accepted
    sugs <- sugs %>% group_by(repId, accountId) %>%
                     mutate(yes = sum(include,na.rm=TRUE), total = sum(ctr,na.rm=TRUE)) %>% ungroup()


    # that ratio is the probability estimate
    sugs <- sugs %>% group_by(repId, accountId) %>%
                     mutate(probability = yes/total) %>% ungroup()
    flog.info("finish prepare sugs")

    # prepare to save the simple estimates of accepted trigger suggestions
    temp.A <- sugs %>% select(repId, accountId, probability)

    temp.A <- temp.A %>% mutate(repActionTypeId = chl, runDate = date_add(today, as.integer(1)), date = date_add(today, as.integer(1)), suggestionType = "Trigger")

    # setup for estimating the likelihood of action after a suggestion and within the engageWindow
    flog.info("Prepare for estimating the likelihood of action after a suggestion and within the engageWindow")
    total <- sdf_bind_rows(sugs %>% select(accountId,repId,date,reaction), events)

    total <- arrange(total, repId, accountId, date, reaction)

    # identify the next record in the merged event-suggestion table
    total <- total %>% group_by(repId, accountId) %>%
                       mutate(nextValue = lead(reaction), nextDate = lead(date)) %>% ungroup()

    # pick out the accpentd and ignored
    temp.T <- total %>% filter(reaction %in% c("Accepted", "Ignored") & nextValue == "Touchpoint")

    # calculate the time difference between suggestion and action
    temp.T <- temp.T %>% mutate(difference = datediff(nextDate, date), ctr = 0)

    # count those records where the time difference is within the engageWindow
    temp.T <- temp.T %>% mutate(ctr = ifelse(difference<=engageWindow, 1, ctr))

    # now count them
    temp.T <- temp.T %>% group_by(accountId, repId) %>% mutate(numerator = sum(ctr,na.rm=TRUE)) %>% ungroup()

    temp.T <- temp.T %>% mutate(ctr = 1)

    # could all the suggestions
    temp.T <- temp.T %>% group_by(accountId, repId) %>% mutate(denominator = sum(ctr,na.rm=TRUE)) %>% ungroup()

    # the ratio is the probability estimate
    temp.T <- temp.T %>% mutate(probability = numerator/denominator)

    # continue adding info for the save to the DB
    temp.T <- temp.T %>%
      sdf_with_sequential_id(id="tempId", from=1) %>%
      group_by(repId, accountId) %>%
      filter(tempId==min(tempId, na.rm=TRUE)) %>%
      ungroup() %>%
      select(-tempId)


    temp.T <- temp.T %>% mutate(repActionTypeId = chl, runDate = date_add(today, as.integer(1)), date = date_add(today, as.integer(1)), suggestionType = "Trigger")

    temp.T <- temp.T %>% select(accountId, repId, date, suggestionType, runDate, repActionTypeId, probability)

    dayEstimate <- interactions %>% filter(repActionTypeId==chl & date<=today)

    flog.info("estimates the likelihood of an interaction by day of week and week of month")
    # estimates the likelihood of an interaction by day of week and week of month
    dayEstimate <- calculateDaysEstimate(dayEstimate, today, lookForward)

    flog.info("prepare to spread the estimates over the lookForward interval")
    # prepare to spread the estimates over the lookForward interval
    temp.T <- addWeekdayWeekmonth(temp.T, today, lookForward)

    flog.info("get final likelihood")

    temp.T <- temp.T %>% left_join(dayEstimate, by=c("repId","accountId","mw_wd"))

    temp.T <- temp.T %>% mutate(ratio = ifelse(is.na(ratio), 0, ratio))

    # finialize the separate estimates of prob touch, prob accept, and the combination
    temp.T <- temp.T %>% mutate(probability = ratio*probability)

    temp.T <- temp.T %>% select(accountId,repId,date,suggestionType,runDate,repActionTypeId,probability)

    if (useForProbability == "A") {
      triggerEngage <- temp.A
    }
    else if (useForProbability == "T") {
      triggerEngage <- temp.T
    }
    else {
      triggerEngage <- sdf_bind_rows(temp.A, temp.T)
    }

    triggerEngage <- triggerEngage %>% mutate(learningRunUID = RUN_UID, learningBuildUID = BUILD_UID, repUID = repId, accountUID = accountId)

    triggerEngage <- triggerEngage %>% select(learningRunUID,learningBuildUID,repActionTypeId,repUID,accountUID,suggestionType,date,probability)

    flog.info("finish calculateTriggerEngagement")

    return(triggerEngage)
}
