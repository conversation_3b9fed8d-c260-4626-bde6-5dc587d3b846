import argparse
import jinja2
import yaml
import sys


def _get_cmdline_param():
    parser = argparse.ArgumentParser()
    parser.add_argument("--target", required=True)
    parser.add_argument("--schema", required=True)
    parser.add_argument("--staging_dir", required=True)
    parser.add_argument("--region", required=True)

    args, unknown = parser.parse_known_args()

    arg_dict = {}
    for arg in vars(args):
        if getattr(args, arg):
            arg_dict[arg] = getattr(args, arg)

    if unknown:
        for i in range(0, len(unknown), 2):
            key, value = unknown[i][2:], unknown[i + 1]
            arg_dict[key] = value

    print("Command line params are:")
    print(arg_dict)
    return arg_dict


def main():
    # Read cmdline param
    arg_dict = _get_cmdline_param()

    # Load Jinja template
    with open('./profiles_template.j2', 'r') as f:
        template = jinja2.Template(f.read())

    # Render template with argument values
    rendered = template.render(**arg_dict)
    print(rendered)

    # Convert YAML string to dictionary
    data = yaml.safe_load(rendered)

    # Write YAML dictionary to file
    with open('./profiles_test.yml', 'w') as f:
        yaml.dump(data, f)


if __name__ == '__main__':
    main()
