// Generated by using Rcpp::compileAttributes() -> do not edit by hand
// Generator token: 10BE3573-1514-4C36-9D1C-5A225CD40393

#include <Rcpp.h>

using namespace Rcpp;

// EncodeMessages
DataFrame EncodeMessages(DataFrame events, std::string targetName, std::string sendName);
RcppExport SEXP Learning_EncodeMessages(SEXP eventsSEXP, SEXP targetNameSEXP, SEXP sendNameSEXP) {
BEGIN_RCPP
    Rcpp::RObject rcpp_result_gen;
    Rcpp::RNGScope rcpp_rngScope_gen;
    Rcpp::traits::input_parameter< DataFrame >::type events(eventsSEXP);
    Rcpp::traits::input_parameter< std::string >::type targetName(targetNameSEXP);
    Rcpp::traits::input_parameter< std::string >::type sendName(sendNameSEXP);
    rcpp_result_gen = Rcpp::wrap(EncodeMessages(events, targetName, sendName));
    return rcpp_result_gen;
END_RCPP
}

