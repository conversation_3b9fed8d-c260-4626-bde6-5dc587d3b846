# Generated by using Rcpp::compileAttributes() -> do not edit by hand
# Generator token: 10BE3573-1514-4C36-9D1C-5A225CD40393

EncodeMessages <- function(events, targetName, sendName) {
    .Call('Learning_EncodeMessages', PACKAGE = 'Learning', events, targetName, sendName)
}

EncodeTiming <- function(events, targetName) {
    .Call('Learning_EncodeTiming', PACKAGE = 'Learning', events, targetName)
}

EncodeTimingMessages <- function(events, targets) {
    .Call('Learning_EncodeTimingMessages', PACKAGE = 'Learning', events, targets)
}

rcpp_hello_world <- function() {
    .Call('Learning_rcpp_hello_world', PACKAGE = 'Learning')
}

