\name{initializeConfiguration}
\alias{initializeConfiguration}
%- Also NEED an '\alias' for EACH other topic documented here.
\title{initializeConfiguration}
\description{
%%  ~~ A concise (1-5 lines) description of what the function does. ~~
}
\usage{
initializeConfiguration(name)
}
%- maybe also 'usage' for other objects documented here.
\arguments{
  \item{name}{
%%     ~~Describe \code{name} here~~
}
}
\details{
%%  ~~ If necessary, more details than the description above ~~
}
\value{
%%  ~Describe the value returned
%%  If it is a LIST, use
%%  \item{comp1 }{Description of 'comp1'}
%%  \item{comp2 }{Description of 'comp2'}
%% ...
}
\references{
%% ~put references to the literature/web site here ~
}
\author{
%%  ~~who you are~~
}
\note{
%%  ~~further notes~~
}

%% ~Make other sections like Warning with \section{Warning }{....} ~

\seealso{
%% ~~objects to See Also as \code{\link{help}}, ~~~
}
\examples{
##---- Should be DIRECTLY executable !! ----
##-- ==>  Define data, use random,
##--	or do  help(data=index)  for the standard data sets.

## The function is currently defined as
function (name) 
{
    cN <- sprintf("./\%s/Configurations/\%s", CLIENT, name)
    flog.info("Init Config file = \%s", cN)
    CONFIGURATION <<- data.table(read.xlsx(cN))
    addWorksheet(WORKBOOK, "Configuration")
    writeData(WORKBOOK, "Configuration", CONFIGURATION)
    DRIVERMODULE <<- sys.frame(1)$ofile
    CATALOGENTRY <<- sprintf("\%s --- \%s --- \%s", runStamp, DRIVERMODULE, 
        configName)
    write.xlsx(CONFIGURATION, file = sprintf("\%s/\%s_\%s.xlsx", 
        runDir, configName, runStamp))
  }
}
% Add one or more standard keywords, see file 'KEYWORDS' in the
% R documentation directory.
\keyword{ ~kwd1 }% use one of  RShowDoc("KEYWORDS")
\keyword{ ~kwd2 }% __ONLY ONE__ keyword per line
