#!/usr/bin/env python3
"""
Test script for the simplified learner to verify SHAP analysis works correctly
"""

import pandas as pd
import numpy as np
from sklearn.datasets import make_classification
from xgboost import XGBClassifier

# Import the simplified learner
import sys
import os
sys.path.append('modelFactory')
from content_selection.learner import <PERSON><PERSON>

def test_shap_analysis():
    """Test the SHAP analysis method with a simple dataset"""
    print("Testing SHAP analysis with simplified learner...")
    
    # Create a simple test dataset
    X, y = make_classification(
        n_samples=1000,
        n_features=10,
        n_informative=5,
        n_redundant=2,
        n_clusters_per_class=1,
        random_state=111
    )
    
    # Convert to DataFrame
    feature_names = [f'feature_{i}' for i in range(X.shape[1])]
    X_df = pd.DataFrame(X, columns=feature_names)
    
    # Train a simple XGBoost model
    model = XGBClassifier(
        n_estimators=50,
        max_depth=3,
        random_state=111,
        enable_categorical=True
    )
    model.fit(X_df, y)
    
    # Test SHAP analysis
    learner = Learner()
    shap_results = learner.analyze_feature_quality_with_shap(
        model, X_df, feature_names, sample_size=100
    )
    
    print("\nSHAP Analysis Results:")
    print(f"Total features analyzed: {shap_results.get('total_features_analyzed', 'N/A')}")
    print(f"Extreme features count: {shap_results.get('extreme_features_count', 'N/A')}")
    
    if 'error' in shap_results:
        print(f"Error: {shap_results['error']}")
        if 'recommendations' in shap_results:
            print("Recommendations:")
            for rec in shap_results['recommendations']:
                print(f"  - {rec}")
    else:
        print("✅ SHAP analysis completed successfully!")
        if 'top_shap_features' in shap_results:
            print("Top SHAP features:")
            for name, importance in shap_results['top_shap_features'][:5]:
                print(f"  - {name}: {importance}")
    
    return shap_results

def test_model_evaluation():
    """Test the model evaluation method"""
    print("\nTesting model evaluation...")
    
    # Create test data
    X, y = make_classification(n_samples=500, n_features=5, random_state=111)
    X_train, X_test = X[:400], X[400:]
    y_train, y_test = y[:400], y[400:]
    
    # Train model
    model = XGBClassifier(n_estimators=50, max_depth=3, random_state=111)
    model.fit(X_train, y_train)
    
    # Test evaluation
    learner = Learner()
    results = learner.evaluate_model_performance(model, X_test, y_test, X_train, y_train)
    
    print("Evaluation Results:")
    for key, value in results.items():
        print(f"  {key}: {value}")
    
    return results

def test_recommendations():
    """Test the recommendations method"""
    print("\nTesting recommendations...")
    
    # Mock evaluation results
    evaluation_results = {
        'test_auc': 0.6,
        'train_auc': 0.63,
        'auc_gap': 0.03,
        'test_f1': 0.25,
        'overfitting_warning': False
    }
    
    # Mock SHAP analysis
    shap_analysis = {
        'total_features_analyzed': 10,
        'extreme_features_count': 2,
        'extreme_features': [
            {'feature': 'feature_1', 'max_shap': 2.5},
            {'feature': 'feature_3', 'max_shap': 3.1}
        ]
    }
    
    learner = Learner()
    recommendations = learner.get_training_recommendations(evaluation_results, shap_analysis)
    
    print("Recommendations:")
    for i, rec in enumerate(recommendations, 1):
        print(f"  {i}. {rec}")
    
    return recommendations

if __name__ == "__main__":
    print("=" * 60)
    print("TESTING SIMPLIFIED LEARNER")
    print("=" * 60)
    
    try:
        # Test SHAP analysis
        shap_results = test_shap_analysis()
        
        # Test model evaluation
        eval_results = test_model_evaluation()
        
        # Test recommendations
        recommendations = test_recommendations()
        
        print("\n" + "=" * 60)
        print("ALL TESTS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        # Summary
        print(f"\nSummary:")
        print(f"- SHAP analysis: {'✅ PASSED' if 'error' not in shap_results else '❌ FAILED'}")
        print(f"- Model evaluation: {'✅ PASSED' if eval_results else '❌ FAILED'}")
        print(f"- Recommendations: {'✅ PASSED' if recommendations else '❌ FAILED'}")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
