artifactory_user=deployer
artifactory_password=AP6TBvrz1QFeasN3UDZngZxX8yZ
artifactory_contextUrl=https://aktanarepo.jfrog.io/aktanarepo
artifactory_publish_repository=aktana-snapshot-local
artifactory_resolve_repository=aktana-snapshot

# set this to the customer for which the gradle tasks are performed. Must match a customer in metadata database
# customer=v3demo_en

# set this to the metadata rds server name and dbname
aktmeta.user=appadmin
aktmeta.password=Uwh27J0Pj31qH2HGn7h48oTm3gC9FS
aktmeta.url=**************************************************************************************
aktmeta.driver=com.mysql.jdbc.Driver
aktmeta.datalakedb=datalakemeta
aktmeta.coredb=aktanameta
aktanameta.serverAddress=metadatards.aktana.com
