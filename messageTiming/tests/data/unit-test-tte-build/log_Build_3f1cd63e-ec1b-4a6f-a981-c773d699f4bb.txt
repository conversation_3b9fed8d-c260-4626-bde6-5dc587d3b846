INFO [2019-10-25 10:47:34] Run directory = /Users/<USER>/Documents/newDev/learning/bin/../builds/unit-test-tte-build
INFO [2019-10-25 10:47:34] Run initialized at: **********.61905
INFO [2019-10-25 10:47:34] Return from initConfiguration
INFO [2019-10-25 10:47:34] predictRunDate: 2017-11-03
INFO [2019-10-25 10:47:34] Entered loadMessageTimingData
INFO [2019-10-25 10:47:34] read V3db data: all (accounts,interactions,messages,messageSetMessage,messageSet,accountProduct,events,products)
INFO [2019-10-25 10:47:34] read messages
INFO [2019-10-25 10:47:34] read messageSetMessage
INFO [2019-10-25 10:47:34] read messageSet
INFO [2019-10-25 10:47:34] get filtered accts list using Reps and RepAccountAssignment table
INFO [2019-10-25 10:47:34] read accounts
INFO [2019-10-25 10:47:34] read products
INFO [2019-10-25 10:47:34] read accountProduct: All
INFO [2019-10-25 10:47:34] accountProductSQL=SELECT accountId, productId, sampleAO_akt FROM AccountProduct;
INFO [2019-10-25 10:47:34] read interactions: All
INFO [2019-10-25 10:47:35] read events: All
INFO [2019-10-25 10:47:35] return from V3readFilter
INFO [2019-10-25 10:47:35] Merge accounts and accountProduct tables
INFO [2019-10-25 10:47:35] Remove bad characters
INFO [2019-10-25 10:47:35] Return from loadMessageTimingData
INFO [2019-10-25 10:47:37] Reading configuration
INFO [2019-10-25 10:47:37] Analyzing account product data
INFO [2019-10-25 10:47:37] Starting to analyze AP variable classes
INFO [2019-10-25 10:47:37] Build Design Matrix
INFO [2019-10-25 10:47:37] Size of ints 42517 5
INFO [2019-10-25 10:47:37] Using target: OPEN
INFO [2019-10-25 10:47:37] Start building dynamic model ......
INFO [2019-10-25 10:47:41] Finished building dynamic model.
INFO [2019-10-25 10:47:41] Finished building allModel.A
INFO [2019-10-25 10:47:41] Finished building allModel.B
INFO [2019-10-25 10:47:41] looping through method-A...
INFO [2019-10-25 10:47:41] Number of design matrix records: 79384
INFO [2019-10-25 10:47:41] Number of design matrix records: 79384 > 20?
INFO [2019-10-25 10:47:41] Target TARGET > 5 in input data
INFO [2019-10-25 10:47:41] Counts 1975.*********** need to be > 5 to continue
INFO [2019-10-25 10:47:43] Check tmp.hex rows : 79384
INFO [2019-10-25 10:47:43] Start training model-A ......
INFO [2019-10-25 10:47:56] Finished training model-A.
INFO [2019-10-25 10:47:57] finished training model A.
INFO [2019-10-25 10:47:57] looping through method-B...
INFO [2019-10-25 10:47:57] Number of design matrix records: 79384
INFO [2019-10-25 10:47:57] Number of design matrix records: 79384 > 20?
INFO [2019-10-25 10:47:57] Target TARGET > 5 in input data
INFO [2019-10-25 10:47:57] Counts 1975.*********** need to be > 5 to continue
INFO [2019-10-25 10:47:58] Check tmp.hex rows : 79384
INFO [2019-10-25 10:47:58] Start training model-B ......
INFO [2019-10-25 10:48:11] Finished training model-B.
INFO [2019-10-25 10:48:11] finished training model B.
INFO [2019-10-25 10:48:11] looping through method-C...
INFO [2019-10-25 10:48:11] Number of design matrix records: 79384
INFO [2019-10-25 10:48:11] Number of design matrix records: 79384 > 20?
INFO [2019-10-25 10:48:11] Target TARGET > 5 in input data
INFO [2019-10-25 10:48:11] Counts 1975.*********** need to be > 5 to continue
INFO [2019-10-25 10:48:12] Check tmp.hex rows : 79384
INFO [2019-10-25 10:48:12] Start training model-C ......
INFO [2019-10-25 10:48:19] Finished training model-C.
INFO [2019-10-25 10:48:19] finished training model C.
INFO [2019-10-25 10:48:20] Model Build Time = 44.5654029846191
INFO [2019-10-25 10:48:20] Save catalog and workbook
INFO [2019-10-25 10:48:20] deployOnSuccess not passed from API, set to FALSE
