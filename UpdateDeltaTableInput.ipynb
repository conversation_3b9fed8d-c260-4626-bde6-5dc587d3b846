{"cells": [{"cell_type": "markdown", "source": ["# Utility to update input DeltaTable contents on S3"], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 1, "outputs": [{"name": "stdout", "output_type": "stream", "text": [":: loading settings :: url = jar:file:/Users/<USER>/libs/spark-3.1.2-bin-hadoop3.2/jars/ivy-2.4.0.jar!/org/apache/ivy/core/settings/ivysettings.xml\n"]}, {"name": "stderr", "output_type": "stream", "text": ["<PERSON> Default <PERSON>ache set to: /Users/<USER>/.ivy2/cache\n", "The jars for the packages stored in: /Users/<USER>/.ivy2/jars\n", "com.amazonaws#aws-java-sdk-bundle added as a dependency\n", "org.apache.hadoop#hadoop-aws added as a dependency\n", "io.delta#delta-core_2.12 added as a dependency\n", "net.snowflake#snowflake-jdbc added as a dependency\n", "net.snowflake#spark-snowflake_2.12 added as a dependency\n", ":: resolving dependencies :: org.apache.spark#spark-submit-parent-8aa441bf-990d-49de-91bb-8e68acf42913;1.0\n", "\tconfs: [default]\n", "\tfound com.amazonaws#aws-java-sdk-bundle;1.11.271 in central\n", "\tfound io.netty#netty-codec-http;4.1.17.Final in central\n", "\tfound io.netty#netty-codec;4.1.17.Final in central\n", "\tfound io.netty#netty-transport;4.1.17.Final in central\n", "\tfound io.netty#netty-buffer;4.1.17.Final in central\n", "\tfound io.netty#netty-common;4.1.17.Final in central\n", "\tfound io.netty#netty-resolver;4.1.17.Final in central\n", "\tfound io.netty#netty-handler;4.1.17.Final in central\n", "\tfound org.apache.hadoop#hadoop-aws;3.1.2 in central\n", "\tfound io.delta#delta-core_2.12;0.8.0 in central\n", "\tfound org.antlr#antlr4;4.7 in central\n", "\tfound org.antlr#antlr4-runtime;4.7 in central\n", "\tfound org.antlr#antlr-runtime;3.5.2 in central\n", "\tfound org.antlr#ST4;4.0.8 in central\n", "\tfound org.abego.treelayout#org.abego.treelayout.core;1.0.3 in central\n", "\tfound org.glassfish#javax.json;1.0.4 in central\n", "\tfound com.ibm.icu#icu4j;58.2 in central\n", "\tfound net.snowflake#snowflake-jdbc;3.8.0 in central\n", "\tfound net.snowflake#spark-snowflake_2.12;2.8.4-spark_3.0 in central\n", "\tfound net.snowflake#snowflake-ingest-sdk;0.10.1 in central\n", "\tfound net.snowflake#snowflake-jdbc;3.12.17 in central\n", ":: resolution report :: resolve 434ms :: artifacts dl 15ms\n", "\t:: modules in use:\n", "\tcom.amazonaws#aws-java-sdk-bundle;1.11.271 from central in [default]\n", "\tcom.ibm.icu#icu4j;58.2 from central in [default]\n", "\tio.delta#delta-core_2.12;0.8.0 from central in [default]\n", "\tio.netty#netty-buffer;4.1.17.Final from central in [default]\n", "\tio.netty#netty-codec;4.1.17.Final from central in [default]\n", "\tio.netty#netty-codec-http;4.1.17.Final from central in [default]\n", "\tio.netty#netty-common;4.1.17.Final from central in [default]\n", "\tio.netty#netty-handler;4.1.17.Final from central in [default]\n", "\tio.netty#netty-resolver;4.1.17.Final from central in [default]\n", "\tio.netty#netty-transport;4.1.17.Final from central in [default]\n", "\tnet.snowflake#snowflake-ingest-sdk;0.10.1 from central in [default]\n", "\tnet.snowflake#snowflake-jdbc;3.12.17 from central in [default]\n", "\tnet.snowflake#spark-snowflake_2.12;2.8.4-spark_3.0 from central in [default]\n", "\torg.abego.treelayout#org.abego.treelayout.core;1.0.3 from central in [default]\n", "\torg.antlr#ST4;4.0.8 from central in [default]\n", "\torg.antlr#antlr-runtime;3.5.2 from central in [default]\n", "\torg.antlr#antlr4;4.7 from central in [default]\n", "\torg.antlr#antlr4-runtime;4.7 from central in [default]\n", "\torg.apache.hadoop#hadoop-aws;3.1.2 from central in [default]\n", "\torg.glassfish#javax.json;1.0.4 from central in [default]\n", "\t:: evicted modules:\n", "\tnet.snowflake#snowflake-jdbc;3.8.0 by [net.snowflake#snowflake-jdbc;3.12.17] in [default]\n", "\t---------------------------------------------------------------------\n", "\t|                  |            modules            ||   artifacts   |\n", "\t|       conf       | number| search|dwnlded|evicted|| number|dwnlded|\n", "\t---------------------------------------------------------------------\n", "\t|      default     |   21  |   0   |   0   |   1   ||   20  |   0   |\n", "\t---------------------------------------------------------------------\n", ":: retrieving :: org.apache.spark#spark-submit-parent-8aa441bf-990d-49de-91bb-8e68acf42913\n", "\tconfs: [default]\n", "\t0 artifacts copied, 20 already retrieved (0kB/13ms)\n", "23/01/11 11:40:53 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable\n", "Using Spark's default log4j profile: org/apache/spark/log4j-defaults.properties\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "23/01/11 11:40:54 WARN Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.\n", "23/01/11 11:40:54 WARN Utils: Service 'SparkUI' could not bind on port 4041. Attempting port 4042.\n"]}], "source": ["import findspark\n", "\n", "import os\n", "os.environ['PYSPARK_SUBMIT_ARGS'] = \"--driver-memory 4g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.271,org.apache.hadoop:hadoop-aws:3.1.2,io.delta:delta-core_2.12:0.8.0,net.snowflake:snowflake-jdbc:3.8.0,net.snowflake:spark-snowflake_2.12:2.8.4-spark_3.0 pyspark-shell\"\n", "#os.environ[\"JAVA_HOME\"] = \"/Library/Java/JavaVirtualMachines/jdk1.8.0_321.jdk/Contents/Home\"\n", "os.environ[\"SPARK_LOCAL_IP\"] = \"127.0.0.1\"\n", "findspark.init(\"/Users/<USER>/libs/spark-3.1.2-bin-hadoop3.2\")\n", "import pyspark\n", "#from delta import *\n", "from pyspark.sql import SparkSession\n", "import pandas as pd\n", "ACCESS_ID = \"\" # << ADD HERE\n", "SECRET_KEY = \"\" # << ADD HERE\n", "sc = pyspark.SparkContext()\n", "sc.setSystemProperty(\"com.amazonaws.services.s3.enableV4\", \"true\")\n", "hadoop_conf = sc._jsc.hadoopConfiguration()\n", "hadoop_conf.set(\"fs.s3a.impl\", \"org.apache.hadoop.fs.s3a.S3AFileSystem\")\n", "hadoop_conf.set(\"com.amazonaws.services.s3.enableV4\", \"true\")\n", "hadoop_conf.set(\"fs.s3a.access.key\", ACCESS_ID)\n", "hadoop_conf.set(\"fs.s3a.secret.key\", SECRET_KEY)\n", "hadoop_conf.set(\"fs.s3a.connection.maximum\", \"100000\")\n", "# hadoop_conf.set(\"fs.s3a.endpoint\", \"s3.\" + constants.aws_region + \".amazonaws.com\")\n", "hadoop_conf.set(\"fs.s3a.endpoint\", \"s3.amazonaws.com\")\n", "hadoop_conf.set(\"delta.logRetentionDuration\", \"36500\")\n", "hadoop_conf.set(\"delta.deletedFileRetentionDuration\", \"365\")\n", "\n", "spark = SparkSession(sc) \\\n", "    .builder \\\n", "    .appName(\"dco_jupyter\") \\\n", "    .config(\"spark.sql.autoBroadcastJoinThreshold\", -1) \\\n", "    .config(\"spark.sql.extensions\", \"io.delta.sql.DeltaSparkSessionExtension\") \\\n", "    .config(\"spark.sql.catalog.spark_catalog\", \"org.apache.spark.sql.delta.catalog.DeltaCatalog\") \\\n", "    .config('spark.port.maxRetries', 100)\\\n", "      .getOrCreate()\n", "spark = pyspark.sql.SparkSession.builder.getOrCreate()"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["## Step-1 : Specify the input path and download the deltatable as SparkDataframe"], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 24, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading File Path- s3a://aktana-bdp-dconovartisau/preuat/dco/data/dco_read_write/bronze/AUTO_SNOOZE_LIST/input_version_id=2021-08-02-V65/\n"]}], "source": ["input_s3_path = \"s3a://aktana-bdp-dconovartisau/preuat/dco/data/dco_read_write/bronze/AUTO_SNOOZE_LIST/input_version_id=2021-08-02-V65/\"\n", "\n", "print(f\"Reading Delta table at S3 Path- {input_s3_path}\")\n", "input_df = spark.read.format(\"delta\").load(input_s3_path)#.where(\"INPUT_VERSION_ID='2023-01-03-V5'\")"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 25, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 43:====================>                                    (4 + 7) / 11]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["+---------+-----+--------------------+-------------------+-----------+----------------+\n", "|accountId|repId|             channel|             expiry|dayToSnooze|input_version_id|\n", "+---------+-----+--------------------+-------------------+-----------+----------------+\n", "|   418345| 1129|       VISIT_CHANNEL|2021-08-09 17:00:00|          5|  2021-08-02-V65|\n", "|   419050| 1117|       VISIT_CHANNEL|2021-08-09 17:00:00|          5|  2021-08-02-V65|\n", "|   419050| 1123|       VISIT_CHANNEL|2021-08-09 17:00:00|          5|  2021-08-02-V65|\n", "|   419050| 1129|       VISIT_CHANNEL|2021-08-09 17:00:00|          5|  2021-08-02-V65|\n", "|   417882| 1100|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418518| 1118|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418618| 1111|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   945173| 1075|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418345| 1117|       VISIT_CHANNEL|2021-08-09 17:00:00|          5|  2021-08-02-V65|\n", "|   418345| 1123|       VISIT_CHANNEL|2021-08-09 17:00:00|          5|  2021-08-02-V65|\n", "|   418214| 1123|       VISIT_CHANNEL|2021-08-09 17:00:00|          5|  2021-08-02-V65|\n", "|   418214| 1129|       VISIT_CHANNEL|2021-08-09 17:00:00|          5|  2021-08-02-V65|\n", "|   418175| 1118|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   419372| 1118|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418118| 1111|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418573| 1100|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   419085| 1111|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418536| 1075|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418859| 1127|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418932| 1101|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|    30332| 1008|WEB_INTERACTIVE_C...|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   419252| 1118|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|    27687| 1128|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   986542| 1101|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   975652| 1101|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   419132| 1111|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   419223| 1100|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   968787| 1127|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418453| 1127|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   417877| 1100|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418118| 1111|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418813| 1100|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   986542| 1101|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418618| 1075|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418891| 1100|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418214| 1117|       VISIT_CHANNEL|2021-08-09 17:00:00|          5|  2021-08-02-V65|\n", "+---------+-----+--------------------+-------------------+-----------+----------------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["input_df.show(40)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["## Step-2: Convert the SparkDataframe to Pandas and write it out as CSV\n", "NOTE: You can update the dataframe via code here or output CSV in Excel"], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["input_pdf = input_df.toPandas()"], "metadata": {"collapsed": false, "pycharm": {"is_executing": true, "name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 27, "outputs": [{"data": {"text/plain": "    accountId  repId                  channel               expiry  \\\n0      418345   1129            VISIT_CHANNEL  2021-08-09 17:00:00   \n1      419050   1117            VISIT_CHANNEL  2021-08-09 17:00:00   \n2      419050   1123            VISIT_CHANNEL  2021-08-09 17:00:00   \n3      419050   1129            VISIT_CHANNEL  2021-08-09 17:00:00   \n4      417882   1100            VISIT_CHANNEL  2021-08-08 17:00:00   \n5      418518   1118            VISIT_CHANNEL  2021-08-08 17:00:00   \n6      418618   1111            VISIT_CHANNEL  2021-08-08 17:00:00   \n7      945173   1075            VISIT_CHANNEL  2021-08-08 17:00:00   \n8      418345   1117            VISIT_CHANNEL  2021-08-09 17:00:00   \n9      418345   1123            VISIT_CHANNEL  2021-08-09 17:00:00   \n10     418214   1123            VISIT_CHANNEL  2021-08-09 17:00:00   \n11     418214   1129            VISIT_CHANNEL  2021-08-09 17:00:00   \n12     418175   1118             SEND_CHANNEL  2021-08-08 17:00:00   \n13     419372   1118             SEND_CHANNEL  2021-08-08 17:00:00   \n14     418118   1111            VISIT_CHANNEL  2021-08-08 17:00:00   \n15     418573   1100            VISIT_CHANNEL  2021-08-08 17:00:00   \n16     419085   1111            VISIT_CHANNEL  2021-08-08 17:00:00   \n17     418536   1075            VISIT_CHANNEL  2021-08-08 17:00:00   \n18     418859   1127            VISIT_CHANNEL  2021-08-08 17:00:00   \n19     418932   1101            VISIT_CHANNEL  2021-08-08 17:00:00   \n20      30332   1008  WEB_INTERACTIVE_CHANNEL  2021-08-08 17:00:00   \n21     419252   1118             SEND_CHANNEL  2021-08-08 17:00:00   \n22      27687   1128             SEND_CHANNEL  2021-08-08 17:00:00   \n23     986542   1101            VISIT_CHANNEL  2021-08-08 17:00:00   \n24     975652   1101            VISIT_CHANNEL  2021-08-08 17:00:00   \n25     419132   1111             SEND_CHANNEL  2021-08-08 17:00:00   \n26     419223   1100             SEND_CHANNEL  2021-08-08 17:00:00   \n27     968787   1127            VISIT_CHANNEL  2021-08-08 17:00:00   \n28     418453   1127            VISIT_CHANNEL  2021-08-08 17:00:00   \n29     417877   1100             SEND_CHANNEL  2021-08-08 17:00:00   \n30     418118   1111             SEND_CHANNEL  2021-08-08 17:00:00   \n31     418813   1100            VISIT_CHANNEL  2021-08-08 17:00:00   \n32     986542   1101             SEND_CHANNEL  2021-08-08 17:00:00   \n33     418618   1075            VISIT_CHANNEL  2021-08-08 17:00:00   \n34     418891   1100            VISIT_CHANNEL  2021-08-08 17:00:00   \n35     418214   1117            VISIT_CHANNEL  2021-08-09 17:00:00   \n\n    dayToSnooze input_version_id  \n0             5   2021-08-02-V65  \n1             5   2021-08-02-V65  \n2             5   2021-08-02-V65  \n3             5   2021-08-02-V65  \n4             5   2021-08-02-V65  \n5             5   2021-08-02-V65  \n6             5   2021-08-02-V65  \n7             5   2021-08-02-V65  \n8             5   2021-08-02-V65  \n9             5   2021-08-02-V65  \n10            5   2021-08-02-V65  \n11            5   2021-08-02-V65  \n12            5   2021-08-02-V65  \n13            5   2021-08-02-V65  \n14            5   2021-08-02-V65  \n15            5   2021-08-02-V65  \n16            5   2021-08-02-V65  \n17            5   2021-08-02-V65  \n18            5   2021-08-02-V65  \n19            5   2021-08-02-V65  \n20            5   2021-08-02-V65  \n21            5   2021-08-02-V65  \n22            5   2021-08-02-V65  \n23            5   2021-08-02-V65  \n24            5   2021-08-02-V65  \n25            5   2021-08-02-V65  \n26            5   2021-08-02-V65  \n27            5   2021-08-02-V65  \n28            5   2021-08-02-V65  \n29            5   2021-08-02-V65  \n30            5   2021-08-02-V65  \n31            5   2021-08-02-V65  \n32            5   2021-08-02-V65  \n33            5   2021-08-02-V65  \n34            5   2021-08-02-V65  \n35            5   2021-08-02-V65  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>accountId</th>\n      <th>repId</th>\n      <th>channel</th>\n      <th>expiry</th>\n      <th>dayToSnooze</th>\n      <th>input_version_id</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>418345</td>\n      <td>1129</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-09 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>419050</td>\n      <td>1117</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-09 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>419050</td>\n      <td>1123</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-09 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>419050</td>\n      <td>1129</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-09 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>417882</td>\n      <td>1100</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>5</th>\n      <td>418518</td>\n      <td>1118</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>6</th>\n      <td>418618</td>\n      <td>1111</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>7</th>\n      <td>945173</td>\n      <td>1075</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>8</th>\n      <td>418345</td>\n      <td>1117</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-09 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>9</th>\n      <td>418345</td>\n      <td>1123</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-09 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>10</th>\n      <td>418214</td>\n      <td>1123</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-09 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>11</th>\n      <td>418214</td>\n      <td>1129</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-09 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>12</th>\n      <td>418175</td>\n      <td>1118</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>13</th>\n      <td>419372</td>\n      <td>1118</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>14</th>\n      <td>418118</td>\n      <td>1111</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>15</th>\n      <td>418573</td>\n      <td>1100</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>16</th>\n      <td>419085</td>\n      <td>1111</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>17</th>\n      <td>418536</td>\n      <td>1075</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>18</th>\n      <td>418859</td>\n      <td>1127</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>19</th>\n      <td>418932</td>\n      <td>1101</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>20</th>\n      <td>30332</td>\n      <td>1008</td>\n      <td>WEB_INTERACTIVE_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>21</th>\n      <td>419252</td>\n      <td>1118</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>22</th>\n      <td>27687</td>\n      <td>1128</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>986542</td>\n      <td>1101</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>24</th>\n      <td>975652</td>\n      <td>1101</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>25</th>\n      <td>419132</td>\n      <td>1111</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>26</th>\n      <td>419223</td>\n      <td>1100</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>27</th>\n      <td>968787</td>\n      <td>1127</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>28</th>\n      <td>418453</td>\n      <td>1127</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>29</th>\n      <td>417877</td>\n      <td>1100</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>30</th>\n      <td>418118</td>\n      <td>1111</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>31</th>\n      <td>418813</td>\n      <td>1100</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>32</th>\n      <td>986542</td>\n      <td>1101</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>33</th>\n      <td>418618</td>\n      <td>1075</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>34</th>\n      <td>418891</td>\n      <td>1100</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>35</th>\n      <td>418214</td>\n      <td>1117</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-09 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["input_pdf"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 5, "outputs": [], "source": ["input_pdf.to_csv(\"~/Downloads/snoozelist_v2021-08-02-V65.csv\", index=False)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["## Step-3: Read the updated CSV file and Convert it to Spark Dataframe to be written as delta table."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 18, "outputs": [], "source": ["input_updated_pdf = pd.read_csv(\"/Users/<USER>/code/jupyter_workspace/cie/snooze_list_update/snoozelist_v2021-08-02-V65_uu.csv\")"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 19, "outputs": [{"data": {"text/plain": "   accountId  repId                  channel               expiry  \\\n0     418859   1127            VISIT_CHANNEL  2021-08-08 17:00:00   \n1     418932   1101            VISIT_CHANNEL  2021-08-08 17:00:00   \n2      30332   1008  WEB_INTERACTIVE_CHANNEL  2021-08-08 17:00:00   \n3     419252   1118             SEND_CHANNEL  2021-08-08 17:00:00   \n4     418175   1118             SEND_CHANNEL  2021-08-08 17:00:00   \n\n   dayToSnooze input_version_id  \n0            5   2021-08-02-V65  \n1            5   2021-08-02-V65  \n2            5   2021-08-02-V65  \n3            5   2021-08-02-V65  \n4            5   2021-08-02-V65  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>accountId</th>\n      <th>repId</th>\n      <th>channel</th>\n      <th>expiry</th>\n      <th>dayToSnooze</th>\n      <th>input_version_id</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>418859</td>\n      <td>1127</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>418932</td>\n      <td>1101</td>\n      <td>VISIT_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>30332</td>\n      <td>1008</td>\n      <td>WEB_INTERACTIVE_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>419252</td>\n      <td>1118</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>418175</td>\n      <td>1118</td>\n      <td>SEND_CHANNEL</td>\n      <td>2021-08-08 17:00:00</td>\n      <td>5</td>\n      <td>2021-08-02-V65</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["input_updated_pdf.head()"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 20, "outputs": [], "source": ["input_updated_df = spark.createDataFrame(input_updated_pdf)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 21, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+---------+-----+--------------------+-------------------+-----------+----------------+\n", "|accountId|repId|             channel|             expiry|dayToSnooze|input_version_id|\n", "+---------+-----+--------------------+-------------------+-----------+----------------+\n", "|   418859| 1127|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418932| 1101|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|    30332| 1008|WEB_INTERACTIVE_C...|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   419252| 1118|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418175| 1118|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   419372| 1118|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   419085| 1111|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   418536| 1075|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|    27687| 1128|        SEND_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "|   986542| 1101|       VISIT_CHANNEL|2021-08-08 17:00:00|          5|  2021-08-02-V65|\n", "+---------+-----+--------------------+-------------------+-----------+----------------+\n", "only showing top 10 rows\n", "\n"]}], "source": ["input_updated_df.show(10)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["## Step-4 : Write the dataframe as Delta table on S3"], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 22, "outputs": [], "source": ["DCO_INPUT_VERSION_ID_COL = 'INPUT_VERSION_ID'\n", "partition = [DCO_INPUT_VERSION_ID_COL.lower()]"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 23, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["input_updated_df.write.format(\"delta\").partitionBy(*partition).mode(\"overwrite\").option(\"mergeSchema\", \"false\").save(input_s3_path)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.11"}}, "nbformat": 4, "nbformat_minor": 4}