#!/bin/bash

# Wrapper/driver script for SCD jobs
# Can be used to invoke any of the SCD related jobs

# Usage: scd_driver.sh <job_name> --customer <customer-name> --env <env-name>"
#   Supported job names are: CREATE_SCD, RUN_SCD, RUN_SCD_PREPROC and RUN_SCD_POSTPROC"
#   Sample usage: ./scd_driver.sh CREATE_SCD --customer pfizerbdpus --env dev"

# ./scd_driver.sh CREATE_SCD --customer pfizerbdpus --env dev
# ./scd_driver.sh RUN_SCD --customer pfizerbdpus --env dev
# ./scd_driver.sh RUN_SCD_PREPROC --customer pfizerbdpus --env dev
# ./scd_driver.sh RUN_SCD_POSTPROC --customer pfizerbdpus --env dev

# CREATE_SCD - to setup a Databricks Job for SCD
# RUN_SCD - to execute the Databricks Job for SCD
# RUN_SCD_PREPROC - to execute the pre-processor Job for SCD
# RUN_SCD_POSTPROC - to execute the post-processor Job for SCD

#
# All jobs take the following command line parameters:
#   --customer custName (name of the customer configured in aktana metadata)
#   --env envName (name of the environment configured in aktana metadata)
#   --region regionName (name of the region for the Job.  Note: This is not used now since metadata is used to find the region for databricks and Snowflake
#

# Underlying command being executed:
# python /mnt/apps/learning/repo/learning/scd/run_sql_on_snowflake.py --customer pfizerbdpus --env dev --app scd --scriptfile=/mnt/apps/learning/repo/learning/scd/code/scd_pre_processor.sql
# python /mnt/apps/learning/repo/learning/scd/driver_create_scd_model.py --customer pfizerbdpus --env dev --app scd
# python /mnt/apps/learning/repo/learning/scd/driver_run_scd_model.py --customer pfizerbdpus --env dev --app scd

echo "Python version : $(python3 --version)"

SCD_INSTALL_DIR=`dirname $0`

JOB=$1
shift 1

APP_PARAM="--app scd"
PREPROC_SCRIPT="--scriptfile $SCD_INSTALL_DIR/code/scd_pre_processor.sql"

# echo "Install python dependencies from $SCD_INSTALL_DIR/requirements.txt"
# python3 -m pip install -r $SCD_INSTALL_DIR/requirements.txt
# pip3 install -r $SCD_INSTALL_DIR/requirements.txt

case $JOB in

    CREATE_SCD_DATABRICKS_JOB)
        echo "Creating SCD Databricks job... $@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/driver_create_scd_model.py $@ $APP_PARAM
        ;;

    RUN_SCD)
        echo "Running SCD Databricks job...$@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/driver_run_scd_model.py $@ $APP_PARAM
        ;;

    RUN_SCD_PREPROC)
        echo "Running SCD Pre-processor job...$@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/run_sql_on_snowflake.py $@ $PREPROC_SCRIPT $APP_PARAM
        ;;

    RUN_SCD_POSTPROC)
        echo "Running SCD Post-processor job...$@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/code/scd_post_process_dse.py $@ $APP_PARAM
        ;;

    RUN_SCD_PRE_MODEL_AND_POST)
        echo "Running SCD Pre-processor job...$@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/run_sql_on_snowflake.py $@ $PREPROC_SCRIPT $APP_PARAM
        echo "Running SCD Databricks job...$@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/driver_run_scd_model.py $@ $APP_PARAM
        echo "Running SCD Post-processor job...$@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/code/scd_post_process_dse.py $@ $APP_PARAM
        ;;

    RUN_SCD_PRE_AND_MODEL)
        echo "Running SCD Pre-processor job...$@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/run_sql_on_snowflake.py $@ $PREPROC_SCRIPT $APP_PARAM
        echo "Running SCD Databricks job...$@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/driver_run_scd_model.py $@ $APP_PARAM
        ;;

    RUN_SCD_AND_POSTPROC)
        echo "Running SCD Databricks job...$@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/driver_run_scd_model.py $@ $APP_PARAM
        echo "Running SCD Post-processor job...$@ $APP_PARAM"
        python3 $SCD_INSTALL_DIR/code/scd_post_process_dse.py $@ $APP_PARAM
        ;;

    GIT_REPO_ONLY)
        echo "Skipping SCD run since GIT_REPO_ONLY is selected"
        ;;

    *)
        echo "Usage: scd_driver.sh <job_name> --customer <customer-name> --env <env-name>"
        echo "  Supported job names are: CREATE_SCD_DATABRICKS_JOB, RUN_SCD, RUN_SCD_PREPROC, RUN_SCD_POSTPROC, RUN_SCD_PRE_MODEL_AND_POST, RUN_SCD_PRE_AND_MODEL, RUN_SCD_AND_POSTPROC, GIT_REPO_ONLY"
        echo "  Sample usage: ./scd_driver.sh CREATE_SCD --customer pfizerbdpus --env dev"
        ;;

esac
