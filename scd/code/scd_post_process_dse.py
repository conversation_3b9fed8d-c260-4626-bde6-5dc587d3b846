import os, sys, inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)

import aktana_ml_utils as mlutils
import snowflake_python_base as sfpb
import run_sql_on_snowflake as sfsql
import pandas as pd
import numpy as np
import time
import requests
import json

CHUNK_SIZE=70000  # number of rows to write


def fetch_api_token(TOKEN_URL, secret_info):
    """
    PURPOSE:
        Fetch a api token
    INPUTS:
        TOKEN_URL - the endpoit to get token
        secret_info - API secret_info
    RETURNS:
        Returns API token.
    """
    response = requests.post(TOKEN_URL, json=secret_info)
    return response.text

def get_standard_header(api_token):
    """
    PURPOSE:
        Makes a standard header with baearer auth and app.json type
    INPUTS:
        api_token - the token for API
    RETURNS:
        Returns header using api token.
    """
    return {"Content-Type": "application/json", 'Authorization': 'Bearer {0}'.format(api_token)}

def make_get_call(url, api_token, headers=None):
    """
    PURPOSE:
        Makes a get call with a url and optional custom headers
    INPUTS:
        url - the endpoint
        api_token - the token for API
        headers - standard header
    RETURNS:
        Returns results
    """
    if headers is None:  headers = get_standard_header(api_token)
    response = requests.get(url, headers=headers)
    return response.text

def make_post_call(url, api_token, json_body, headers=None):
    """
    PURPOSE:
        Makes a post call with a url, json body and optional custom headers
    INPUTS:
        url - the endpoint
        api_token - the token for API
        headers - standard header
    RETURNS:
        Returns results
    """
    if headers is None:  headers = get_standard_header(api_token)
    response = requests.post(url, headers=headers, data=json_body)
    return response.text

def make_delete_call(url, api_token, headers=None):
    '''
    Purpose:
        Makes a delete call with a url suffix, json body and optional custom headers
    Inputs:
        url - the endpoint for delete
        api_token - the token for API
        headers - standard header
    Returns:
        API call results
    '''
    if headers is None:  headers = get_standard_header(api_token)
    response = requests.delete(url, headers=headers)
    return response.text

def get_scd_pp_output(snowflake_params):
    """
    PURPOSE:
        retrieve post process results for APLV
    INPUTS:
        snowflake parameters to connect
    RETURNS:
        Returns data of label type and value
    """
    spb = sfpb.snowflake_python_base()
    conn = spb.create_connection(snowflake_params)
    sfcur = conn.cursor()

    query = """ with scd_label_data as
                    (select concat(d.REPORTING_LEVEL_NAME, '_', d.USE_CASE_NAME, '_', po.CHANGE_DIRECTION) as labelTypeUID,
                       po.ACCOUNT_UID as accountUID,
                       po.PRODUCT_UID as productUID,
                       d.DATAPOINT_NAME as label,
                       concat(upper(d.REPORTING_LEVEL_NAME), '_', upper(d.USE_CASE_NAME), '_', upper(po.CHANGE_DIRECTION), '~', accountUID, '~',  productUID, '~', label) as accountProductLabelUID,
                       po.SCD_NAME as description,
                       po.SCD_SCORE as value,
                       po.SCD_PREDICTED_VALUE as scdDataPointPredictedValue_std_akt,
                       d.PERIOD_NUMBER as scdDataPointTimePeriod_std_akt
                    from SCD.vw_SCD_POST_PROCESS_OUTPUT_ACCOUNT po
                         inner join SCD.SCD_OUT_DATAPOINT d
                         on po.SCD_OUT_DATAPOINT_KEY = d.SCD_OUT_DATAPOINT_KEY)
                select upper(replace(labelTypeUID, ' ', '_')) as labelTypeUID, accountUID, productUID, label,
                       replace(accountProductLabelUID, ' ', '_') as accountProductLabelUID, description, value,
                       scdDataPointPredictedValue_std_akt, scdDataPointTimePeriod_std_akt, initcap(replace(labelTypeUID, '_', ' ')) as labelTypeName
                from scd_label_data
                where value > 0;
            """
    print ("Executing query:" + query)
    sfcur.execute(query)
    data = sfcur.fetchall()
    col_names = [i[0] for i in sfcur.description]

    df = pd.DataFrame(data, columns = col_names)
    return(df)

def get_param_value(snowflake_params, param_name, default_value):
    """
    PURPOSE:
        retrieves the chunk-size for DSE batch api call
    INPUTS:
        snowflake parameters to connect
    RETURNS:
        Returns chunk-size
    """
    spb = sfpb.snowflake_python_base()
    conn = spb.create_connection(snowflake_params)

    param_value = default_value

    # Create a cursor for this connection.
    cursor1 = conn.cursor()
    command = f"select PARAM_VALUE from SCD.SCD_IN_PARAM where PARAM_NAME = '{param_name}'"
    cursor1.execute(command)
    # Get the results (should be only one):
    for row in cursor1:
        param_value = row[0]
    # Close this cursor.
    cursor1.close()
    conn.close()

    return(param_value)

def write_to_APLV(url, api_token, chunk_size, df):
    """
    PURPOSE:
        write data into DSE table AccountProductLabelValue
    INPUTS:
        url - the endpoint
        api_token - the token for API
        headers - standard header
    RETURNS:

    """
    batch = df.shape[0] // chunk_size + 1
    print('there are {} batches to write ...'.format(batch))

    # break data into chunks to call DSE API
    # since there is batch size limitation on DSE API
    for chunk in np.array_split(df, batch):
        data = chunk.to_csv(sep='|', index=False)
        resp = make_post_call(url, api_token, data)
        print(resp)

    return(0)

def main():

    ml_utils = mlutils.aktana_ml_utils()

    cmdline_params, metadata_params = ml_utils.initialize(sys.argv)
    print(metadata_params)
    print(cmdline_params)

    region   = cmdline_params['region']
    env      = cmdline_params['env']
    customer = cmdline_params['customer']

    snowflake_params = ml_utils.get_snowflake_metadata()
    print(snowflake_params)

    chunk_size = get_param_value(snowflake_params, "SCD_POST_PROC_CHUNK_SIZE", "10000")
    print('chunk size = {}'.format(chunk_size))

    write_datapoint_period = get_param_value(snowflake_params, "SCD_POST_PROC_WRITE_PERIOD_DATAPOINT", "0")
    print('chunk size = {}'.format(chunk_size))

    # Construct base dse api url
    base_url = 'https://{}dse{}.aktana.com/{}/api/v3.0/'.format(region, env, customer)
    print('base_url is:{}'.format(base_url))

    TOKEN_URL = base_url + 'Token/internal'
    APLV_URL = base_url + 'AccountProductLabelValue/batch/insert'

    # Get API secret by quering metadata
    secret = ml_utils.get_api_secret()

    secret_info = {"secret":secret}
    print('secret_info is:{}'.format(secret_info))

    # Fetch API token
    api_token = fetch_api_token(TOKEN_URL, secret_info)
    print('api_token={}'.format(api_token))

    # Get SCD output data for APLV
    df = get_scd_pp_output(snowflake_params)
    print('number of rows = {}'.format(df.shape[0]))

    # delete same scd label data first
    print('build url for delete')

    for l in df['LABELTYPENAME'].unique():
        url_del = base_url + 'AccountProductLabelValue/byLabelType/' + l
        print(url_del)
        res = make_delete_call(url_del, api_token)
        print(res)

    print('finished delete.')

    # drop labelTypeName
    df = df.drop(columns=['LABELTYPENAME'])

    # change column names to be case sensitive as DSE API requires it
    df.columns = ['labelTypeUID', 'accountUID', 'productUID', 'label', 'accountProductLabelUID', 'description', 'value', 'scdDataPointPredictedValue_std_akt', 'scdDataPointTimePeriod_std_akt']

    # Remove predictedValue and timePeriod columns if param SCD_POST_PROC_WRITE_PERIOD_DATAPOINT != 1
    if write_datapoint_period != "1":
        df = df.drop(columns=['scdDataPointPredictedValue_std_akt'])
        df = df.drop(columns=['scdDataPointTimePeriod_std_akt'])

    # batch insert to DSE table APLV
    print('start batch insert ...')
    write_to_APLV(APLV_URL, api_token, int(chunk_size), df)
    print('return from write_to_APLV.')


if __name__ == "__main__":
    main()
