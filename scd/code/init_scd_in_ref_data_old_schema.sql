
----------------------------
-- INSERT FOR REPORTING_LEVEL
----------------------------

--delete from SCD.SCD_IN_REPORTING_LEVEL;

INSERT INTO SCD.SCD_IN_REPORTING_LEVEL (REPORTING_LEVEL_KEY, REPORTING_LEVEL_NAME, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (1, 'BRICK', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_IN_REPORTING_LEVEL (REPORTING_LEVEL_KEY, REPORTING_LEVEL_NAME, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (2, 'ACCOUNT', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

commit;

----------------------------
-- INSERT FOR SCD USE CASE
----------------------------

--delete from SCD.SCD_IN_USE_CASE;

INSERT INTO SCD.SCD_IN_USE_CASE (USE_CASE_NAME_KEY, USE_CASE_NAME, USE_CASE_NAME_DESC, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (1, 'SALES VOLUME', 'SALES VOLUME', TRUE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP() );

INSERT INTO SCD.SCD_IN_USE_CASE (USE_CASE_NAME_KEY, USE_CASE_NAME, USE_CASE_NAME_DESC, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (2, 'ORDER GAP', 'ORDER GAP', TRUE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP() );

INSERT INTO SCD.SCD_IN_USE_CASE (USE_CASE_NAME_KEY, USE_CASE_NAME, USE_CASE_NAME_DESC, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (3, 'MARKETBASKET VOLUME', 'MARKETBASKET VOLUME', TRUE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP() );

INSERT INTO SCD.SCD_IN_USE_CASE (USE_CASE_NAME_KEY, USE_CASE_NAME, USE_CASE_NAME_DESC, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (4, 'MARKET SHARE', 'MARKET SHARE', TRUE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP() );

commit;

----------------------------
-- INSERT FOR SCD TARGET_VIEWS
-- 16 rows for all combinations of 2 reporting-levels, 2 frequencies, 4 SCD USE CASES
-- REPORTING LEVEL (1, 2 for BRICK and ACCOUNT)
-- FREQUENCY (2, 3 for WEEKLY and MONTHLY)
-- SCD USE CASES (1,2,3,4 for SALES_VOLUME, ORDER_GAP, MARKETBASKET_VOLUME, MARKET_SHARE)
----------------------------

--delete from SCD.SCD_IN_TARGET_VIEW_MAPPING;

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (1, 1, 2, 3, 'vw_ACCOUNT_WEEKLY_SALES_VOLUME', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (2, 1, 2, 4,'vw_ACCOUNT_MONTHLY_SALES_VOLUME', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (3, 2, 2, 3, 'vw_ACCOUNT_WEEKLY_ORDER_GAP_DETECTION', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (4, 2, 2, 4,'vw_ACCOUNT_MONTHLY_ORDER_GAP_DETECTION', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (5, 3, 2, 3, 'vw_ACCOUNT_WEEKLY_MARKETBASKET_VOLUME', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (6, 3, 2, 4,'vw_ACCOUNT_MONTHLY_MARKETBASKET_VOLUME', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (7, 4, 2, 3, 'vw_ACCOUNT_WEEKLY_MARKET_SHARE', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (8, 4, 2, 4,'vw_ACCOUNT_MONTHLY_MARKET_SHARE', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (9, 1, 1, 3, 'vw_BRICK_WEEKLY_SALES_VOLUME', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (10, 1, 1, 4, 'vw_BRICK_MONTHLY_SALES_VOLUME', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (11, 2, 1, 3, 'vw_BRICK_WEEKLY_ORDER_GAP_DETECTION', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (12, 2, 1, 4, 'vw_BRICK_MONTHLY_ORDER_GAP_DETECTION', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (13, 3, 1, 3, 'vw_BRICK_WEEKLY_MARKETBASKET_VOLUME', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (14, 3, 1, 4, 'vw_BRICK_MONTHLY_MARKETBASKET_VOLUME', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (15, 4, 1, 3, 'vw_BRICK_WEEKLY_MARKET_SHARE', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (16, 4, 1, 4, 'vw_BRICK_MONTHLY_MARKET_SHARE', 0, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (17, 3, 2, 3, 'vw_ACCOUNT_WEEKLY_MARKETBASKET_VOLUME_CUSTOM', 1, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (18, 3, 2, 4,'vw_ACCOUNT_MONTHLY_MARKETBASKET_VOLUME_CUSTOM', 1, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (19, 4, 2, 3, 'vw_ACCOUNT_WEEKLY_MARKET_SHARE_CUSTOM', 1, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (20, 4, 2, 4,'vw_ACCOUNT_MONTHLY_MARKET_SHARE_CUSTOM', 1, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (21, 3, 1, 3, 'vw_BRICK_WEEKLY_MARKETBASKET_VOLUME_CUSTOM', 1, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (22, 3, 1, 4, 'vw_BRICK_MONTHLY_MARKETBASKET_VOLUME_CUSTOM', 1, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (23, 4, 1, 3, 'vw_BRICK_WEEKLY_MARKET_SHARE_CUSTOM', 1, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (TARGET_VIEW_MAPPING_KEY, USE_CASE_NAME_KEY, REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (24, 4, 1, 4, 'vw_BRICK_MONTHLY_MARKET_SHARE_CUSTOM', 1, 1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

commit;

----------------------------
-- INSERT FOR SCD_IN_PARAM
----------------------------

--delete from SCD.SCD_IN_PARAM;

INSERT INTO SCD.SCD_IN_PARAM (PARAM_KEY, PARAM_NAME, PARAM_VALUE, PARAM_TYPE, PARAM_DESC, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (1, 'SCD_HISTORY_DAYS', '730', 'INTEGER', 'Min days of history required', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_IN_PARAM (PARAM_KEY, PARAM_NAME, PARAM_VALUE, PARAM_TYPE, PARAM_DESC, IS_ACTIVE, CREATED_TS, UPDATED_TS)
VALUES (2, 'SCD_LATE_TOLERANCE_DAYS', '60', 'INTEGER', 'Max days of tolerance allowed for sales file arrival', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- we seem to have 1 week less than 2 years data? is this a problem during purge or intentional?
update SCD.SCD_IN_PARAM
set PARAM_VALUE = '725'
where PARAM_NAME = 'SCD_HISTORY_DAYS';

-- Temporary override to be more lenient until we have more regular data loads
update SCD.SCD_IN_PARAM
set PARAM_VALUE = '200'
where PARAM_NAME = 'SCD_LATE_TOLERANCE_DAYS';

commit;

