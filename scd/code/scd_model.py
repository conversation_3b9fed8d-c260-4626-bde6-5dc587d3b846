# Databricks notebook source
from pyspark.sql import SparkSession
from pyspark.sql import SQLContext

from pyspark.sql.types import *
from decimal import Decimal
from pyspark.sql import Window
from pyspark.sql.functions import expr, col, column
from pyspark.sql.functions import pandas_udf, PandasUDFType
from pyspark.sql.types import StructType

import pyspark.sql.functions as F
from pyspark.sql.functions import current_timestamp
from pyspark.sql.functions import desc, row_number, monotonically_increasing_id
from pyspark.sql.types import *
from pyspark.sql import Row
from pyspark.sql.functions import date_format

import pandas as pd
import snowflake.connector
import snowflake as sf

import sys

from statsmodels.distributions.empirical_distribution import ECDF
from statsmodels.tsa.statespace.exponential_smoothing import ExponentialSmoothing

import numpy
import pyarrow

"""
  #user, password, datbase passed by metadata
  user = "ml_admin"
  password = "trsky&j0912ml"
  account = "aktanapartner"
  warehouse = "AKTANADEV_WH_US_WEST_2"
  database = "RPT_DWPREPROD_QA"
  schema_scd = "SCD"

"""
user = sys.argv[1]
password = sys.argv[2]
account = sys.argv[3]
warehouse = sys.argv[4]
database = sys.argv[5]
schema_scd = sys.argv[6]
db_role = ""
if len(sys.argv) >= 8:
  db_role = sys.argv[7]

scd_params = {}

print("Pyarrow version info:")
print(pyarrow.__version__)

print("Numpy version info:")
print(numpy.__version__)

print("Pandas version info:")
pd.show_versions()

# Initialize an options dictionary to read from snowflake
def get_db_options():
  options = {
    "sfUrl": "{}.snowflakecomputing.com/".format(account), 
    "sfUser": user,
    "sfPassword": password,
    "sfDatabase": database,
    "sfSchema": schema_scd,
    "sfWarehouse": warehouse
  }

  if len(db_role) > 0:
    options["sfRole"] = db_role
  
  return options



# Read the SCD use-cases to process from Snowflake
def ingest_scd_tables():

  scd_use_cases_sdf = spark.read.format("snowflake").options(**options).option("query", 
              """SELECT 
                   SCD_OUT_DATAPOINT_KEY,
                   dp.USE_CASE_NAME AS DATAPOINT_NAME,
                   dp.SCD_IN_USE_CASE_KEY,
                   PERIOD_NUMBER,
                   METRIC_NAME,
                   dp.PRODUCT_NAME,
                   MARKET_BASKET_NAME,
                   TARGET_VIEW_NAME,
                   REPORTING_LEVEL_NAME,
                   p.SCD_IN_PRODUCT_CONFIG_KEY,
                   p.PRODUCT_ALL_PRODUCT_IDENTIFIER AS PRODUCT_IDENTIFIER,
                   u.ALPHA_UP,
                   u.ALPHA_DOWN
                FROM
                   SCD.SCD_OUT_DATAPOINT dp,
                   SCD.SCD_IN_TARGET_VIEW_MAPPING tvm,
                   SCD.SCD_IN_PRODUCT_CONFIG p,
                   SCD.SCD_IN_USE_CASE u
                WHERE
                  dp.IS_DELETED = FALSE 
                  AND p.IS_SCD_ENABLED = TRUE 
                  AND dp.SCD_IN_USE_CASE_KEY = tvm.SCD_IN_USE_CASE_KEY
                  AND dp.SCD_IN_REPORTING_LEVEL_KEY = tvm.SCD_IN_REPORTING_LEVEL_KEY
                  AND dp.DIM_FREQUENCY_KEY = tvm.DIM_FREQUENCY_KEY
                  AND p.SCD_IN_PRODUCT_CONFIG_KEY = dp.SCD_IN_PRODUCT_CONFIG_KEY
                  AND dp.SCD_IN_USE_CASE_KEY = u.SCD_IN_USE_CASE_KEY
                  AND dp.IS_USEABLE = True
                  AND COALESCE(p.IS_MARKET_CALC_CUSTOM, 0) = COALESCE(tvm.IS_MARKET_CALC_CUSTOM, 0)""").load()
  
  return scd_use_cases_sdf

def read_scd_params():

  scd_params_sdf = spark.read.format("snowflake").options(**options).option("query", 
              """SELECT 
                   PARAM_NAME, PARAM_VALUE
                FROM
                   SCD.SCD_IN_PARAM p""").load()

  for row in scd_params_sdf.rdd.collect():
    scd_params[row.PARAM_NAME] = row.PARAM_VALUE


# Read use-case specific view from Snowflake Sales schema
def ingest_scd_view (target_view_name, period_number, metric_name, product_identifier, marketbasket_name=None):
  
  if "MARKETBASKET" in target_view_name or "MARKET_SHARE" in target_view_name: 
      query = f"SELECT * from {target_view_name} where PERIOD_NUMBER = {period_number} and metric_name = '{metric_name}' and product_identifier = '{product_identifier}' and marketbasket_name = '{marketbasket_name}'" 
  else:
    if "GAP_DETECTION" in target_view_name:
       query = f"SELECT * from {target_view_name} where metric_name = '{metric_name}' and product_identifier = '{product_identifier}'" 
    else:
       query = f"SELECT * from {target_view_name} where PERIOD_NUMBER = {period_number} and metric_name = '{metric_name}' and product_identifier = '{product_identifier}'"
    
  sales_sdf = spark.read.format("snowflake").options(**options).option("query", query).load()
  return sales_sdf



def remove_inadequate_data(df, target, reporting_level_name):
    # Find if there are adequate records only using records with non-zero targets
    if (use_case_key == 2):
        
        nonzero_filter = F.col(target) > 0
        target_count = int(scd_params.get("SCD_MIN_NON_ZERO_VALUES_FOR_ORDERGAP", "10"))
    else:
        nonzero_filter = F.col(target) > 0
        target_count = int(scd_params.get("SCD_MIN_NON_ZERO_VALUES_FOR_ANOMALY", "40"))
    
    filtered = df.where(nonzero_filter)
    
    # for col(target) > 0, find the min date and max date
    min_max = filtered.groupby(reporting_level_name).agg(
        F.min("SALE_DATE").alias("MIN_DATE"),
        F.max("SALE_DATE").alias("MAX_DATE")
    )
    
    filtered_min_max = df.join(min_max, on=reporting_level_name,
                                    how='left')
    
    if (use_case_key !=2):
      
      filtered_min_max_no_nan = filtered_min_max.where(F.col("SALE_DATE") >= F.col("MIN_DATE"))
    
    else:
    
      filtered_min_max_no_nan = filtered_min_max.where(F.col(target).isNotNull())
  
    # Find the most recent sale date
    # Spark throws warning with this line since it is trying to bring all data to single partition since there is no window partition --> .withColumn("max", max("value") over ())
    most_recent_date = filtered_min_max_no_nan.groupby().agg(
        F.max("MAX_DATE").alias("MOST_RECENT_SALE_DATE")).collect()[0][0]
    
    min_max2 = filtered_min_max_no_nan.withColumn("MOST_RECENT_SALE_DATE",
                                 F.lit(most_recent_date))
    # Reporting-levels must meet 3 rules:
    # 1. At least 6 months of data (i.e. MAX - MIN is more than 6 months)
    # 2. At least 1 sale within 1 year of the most recent sale date (i.e. most-recent-sale-date - most-recent-date for the reporting level)
    # 3. At least 40 non-zero transactions to use for modeling
    min_max2 = min_max2.withColumn("DIFF_DAYS",
                                 F.datediff("MAX_DATE", "MIN_DATE")) \
        .withColumn("DAYS_TILL_MOST_RECENTSALE",
                    F.datediff("MOST_RECENT_SALE_DATE", "MAX_DATE")) \
        .where("DIFF_DAYS >= " + scd_params.get("SCD_MAX_TO_MIN_DAYS", "180")) \
        .where("DAYS_TILL_MOST_RECENTSALE <= " + scd_params.get("SCD_DAYS_TILL_MOST_RECENTSALE", "365"))

    if target_view_name.find('GAP') == -1:
        nonzero_counts = min_max2.where(F.col(target) != 0)
    else:
        nonzero_counts = min_max2.where(F.col("SALES_METRIC_VALUE") != 0)
        
    temp = nonzero_counts.groupby(reporting_level_name).agg(
        F.count("SALE_DATE").alias("TARGET_COUNT")
    )
    nonzero_counts_merge = min_max2.join(temp, on=reporting_level_name,
                                        how='left')
    counts = nonzero_counts_merge.where("TARGET_COUNT >= {}".format(
        target_count))
    return (counts)


ret_columns = ['reporting_level_value','reporting_level_name','sale_date','observed', 'predicted','scd_name','Score', 
               'product_name', 'scd_out_datapoint_key','change_direction']
schema = StructType([
    StructField("reporting_level_value", StringType(), False), #reporting value
    StructField("reporting_level_name", StringType(), True), #reporting name
    StructField("sale_date", DateType(), True),  
    StructField("observed", FloatType(), True),
    StructField("predicted", FloatType(), True),
    StructField("scd_name", StringType(), True),
    StructField("Score", FloatType(), True),
    StructField("product_name", StringType(), True),
    StructField("scd_out_datapoint_key", DecimalType(), True),
    StructField("change_direction", StringType(), True)
  
])


def anomaly_fn(df, target, reporting_level_name, alpha_up, alpha_down):
  @pandas_udf(schema, PandasUDFType.GROUPED_MAP)
  def anomaly_detect(df):
  
    group_key = df[reporting_level_name].iloc[0]
 
    df.sort_values(by='SALE_DATE', inplace=True, ascending=True)
    x = df[target].astype(float).tolist()
    ret_sale_date= df['SALE_DATE'].max()

    min_date = df['SALE_DATE'].min()
    series_date = pd.date_range(min_date, periods=len(x), freq='W-SAT')
    data = pd.Series(x, series_date)

    up = "SCD_",product_name.title(),"_",target_view_name.split("_", 3)[-1].title(),"_",metric_name,"_Increase_",str(period_number),"w"
    down="SCD_",product_name.title(),"_",target_view_name.split("_", 3)[-1].title(),"_",metric_name,"_Decrease_",str(period_number),"w"
     
    model = ExponentialSmoothing(data, trend=True, seasonal=4)
    results = model.fit(low_memory=True, method='powell', disp=0)

    predicted = results.fittedvalues[-1]
    resid = results.resid[-1]
    lower = results.resid.quantile(q=.25)
    upper = results.resid.quantile(q=.75)
    iqr = upper - lower
    
    if (predicted < 0):
      predicted = 0
    if (resid > upper):
      SCD_up = 1 - ((alpha_up * iqr) / (resid - upper))
    else:
      SCD_up = 0.0

    if (resid < lower): 
      SCD_down = 1 + ((alpha_down * iqr ) / (resid - lower))
    else:
      SCD_down = 0.0
     
   
    if (SCD_up >=100):
        SCD_up = 99.99
    if (SCD_up <0):
       SCD_up = 0
    if (SCD_down < 0):
        SCD_down =0
    if (SCD_down >=100):
       SCD_down = 99.99 
  
    SCD_up = round(SCD_up*100, 2)
    SCD_down = round(SCD_down*100, 2)
    
    ret_product_name = product_name 
    ret_scd_out_datapoint_key = scd_out_datapoint_key 
    ret_reporting_level_name = reporting_level_name_string.split("_", 1)[0].upper()
    
    ret_change_direction_increasing = 'Increase' 
    ret_change_direction_decreasing = 'Decrease' 
    
    
    return pd.DataFrame([[group_key] + [ret_reporting_level_name, ret_sale_date, x[-1], predicted, "".join(up), SCD_up, 
                                        product_name, scd_out_datapoint_key,  ret_change_direction_increasing]], 
                                        columns= ret_columns).append(
           pd.DataFrame([[group_key] + [ret_reporting_level_name, ret_sale_date, x[-1], predicted, "".join(down), SCD_down,
                                         product_name, scd_out_datapoint_key, ret_change_direction_decreasing]], 
                                         columns=ret_columns))
    
  return df.groupby(reporting_level_name).apply(anomaly_detect)
  
  
gap_ret_columns = ['reporting_level_value','reporting_level_name','sale_date','observed', 'predicted','scd_name','Score', 
                   'product_name', 'scd_out_datapoint_key', 'change_direction']

schema = StructType([
    StructField("reporting_level_value", StringType(), False), #reporting value
    StructField("reporting_level_name", StringType(), True), #reporting name
    StructField("sale_date", DateType(), True),  
    StructField("observed", FloatType(), True),
    StructField("predicted", FloatType(), True),
    StructField("scd_name", StringType(), True),
    StructField("Score", FloatType(), True),
    StructField("product_name", StringType(), True),
    StructField("scd_out_datapoint_key", DecimalType(), True),
    StructField("change_direction", StringType(), True)])
    
def gap_detect_fn(df, target, reporting_level_name):
  @pandas_udf(schema, PandasUDFType.GROUPED_MAP)
  def gap_detect(df):
    group_key = df[reporting_level_name].iloc[0]
    # for Order Gap Down lastDiff is the number of days since the last order
    lastDiff = float(df[target].iloc[-1])
 
    # estimate the ecdf function fn() using order_gap_week by creating vector of purchase times
    # sorted 
    df.sort_values(by='SALE_DATE', inplace=True, ascending=True)
    
    x = df[df.SALES_METRIC_VALUE!=0][target][1:len(df[df.SALES_METRIC_VALUE!=0])].astype(float)
    fn = ECDF(x)
  
    observed = float(lastDiff)
    predicted = x.quantile().astype(float)
    sub_string = "SCD_",product_name.title(),"_",target_view_name.split("_", 3)[-1].title(),"_",metric_name,"_Increase_",str(period_number),"w"
    scd_name = "".join(sub_string)
    Score = fn(lastDiff).round(4)*100.0
    
   
    ret_sale_date= df['SALE_DATE'].max()
    #ret_sale_date= df[df[reporting_level_name]]['SALE_DATE'].iloc[-1]
    print(ret_sale_date)
  
    ret_product_name = product_name 
    ret_scd_out_datapoint_key = scd_out_datapoint_key 
    ret_reporting_level_name = reporting_level_name_string.split("_", 1)[0].upper()
    
    ret_change_direction= 'Increase' 
  
    # adjust for rounding issues
    if (observed == predicted):
      Score = 0.0
        
    # adjust for too much confidence
    if (Score == 100.0):
      Score = 99.0
  
    prob_up = pd.DataFrame([[group_key] + [ret_reporting_level_name, ret_sale_date, lastDiff, predicted, scd_name, Score,
                                           ret_product_name, ret_scd_out_datapoint_key, ret_change_direction]], 
                                           columns=gap_ret_columns)
    # for prob_down
    last_order_gap = df[target][df.SALES_METRIC_VALUE != 0].iloc[-1]
    observed = float(last_order_gap)
    sub_string = "SCD_", product_name.title(), "_", target_view_name.split("_", 3)[
          -1].title(), "_", metric_name, "_Decrease_", str(period_number), "w"
    scd_name = "".join(sub_string)
    Score = (1 - fn(last_order_gap)).round(4) * 100
    
    # adjust for rounding issues
    if (observed == predicted):
      Score = 0.0
        
    # adjust for too much confidence
    if (Score == 100.0):
      Score = 99.0
      
    ret_change_direction = 'Decrease'
  
    prob_down = pd.DataFrame(
          [[group_key] + [ret_reporting_level_name, ret_sale_date, observed, predicted, scd_name, Score,
                          ret_product_name, ret_scd_out_datapoint_key, ret_change_direction]], 
                          columns=gap_ret_columns)
    
    prob = pd.concat([prob_up, prob_down])
  
    return prob
  return df.groupby(reporting_level_name).apply(gap_detect)
  
  
def create_metrics(df_spark, product_name, reporting_level_value, reporting_level_name, scd_out_datapoint_key):
    # data for anomalies up
    df_pd = df_spark.toPandas()
    scd_names = df_pd["scd_name"].unique()
    
    sale_date =df_pd["sale_date"].unique()
    
    # table for anomalies up
   
    rep_count = len(df_pd[reporting_level_value].unique())
    
    change_direction = 'Increase'

    x = df_pd[df_pd['scd_name']==df_pd['scd_name'].unique()[0]]['Score']
   
    df_up= ()
   
    df_up = pd.DataFrame({
            'CURRENT_WEEK_DATE': sale_date.repeat(5),
            'SCD_NAME': scd_names[0],
            'ELIGIBILITY_PERCENT':[(x[x>=75].count() / x.count()*100).round(0),
                                   (x[x>=80].count() / x.count()*100).round(0),
                                   (x[x>=85].count() / x.count()*100).round(0),
                                   (x[x>=90].count() / x.count()*100).round(0),
                                   (x[x>=95].count() / x.count()*100).round(0)],
            'ANOMALY_THRESHOLD': [.75, .80, .85, .90, .95],
            'REPORTING_LEVEL_COUNT':rep_count,
            'PRODUCT_NAME': product_name,
            'REPORTING_LEVEL_NAME': reporting_level_name,
            'SCD_OUT_DATAPOINT_KEY': scd_out_datapoint_key,
            'CHANGE_DIRECTION': change_direction 
    })
   
 
    change_direction = 'Decrease'
    
    y = df_pd[df_pd['scd_name']==df_pd['scd_name'].unique()[1]]['Score']
   
    df_down = ()
    df_down = pd.DataFrame({
            'CURRENT_WEEK_DATE': sale_date.repeat(5),
            'SCD_NAME': scd_names[1],
            'ELIGIBILITY_PERCENT':[(y[y>=75].count() / y.count()*100).round(0),
                                     (y[y>=80].count() / y.count()*100).round(0),
                                     (y[y>=85].count() / y.count()*100).round(0),
                                     (y[y>=90].count() / y.count()*100).round(0),
                                     (y[y>=95].count() / y.count()*100).round(0)],
            'ANOMALY_THRESHOLD': [.75, .80, .85, .90, .95],
            'REPORTING_LEVEL_COUNT': rep_count,
            'PRODUCT_NAME': product_name,
            'REPORTING_LEVEL_NAME': reporting_level_name,
            'SCD_OUT_DATAPOINT_KEY': scd_out_datapoint_key,
            'CHANGE_DIRECTION': change_direction 
    })
            
    create_df = pd.concat([df_up, df_down])
    ret_df_spark = spark.createDataFrame(create_df)
    return (ret_df_spark)

def processRow(use_case_key, target_view_name, scd_out_datapoint_key, period_number, metric_name, product_name, product_identifier,market_basket_name, alpha_up, alpha_down):
  
    if use_case_key ==3 or use_case_key ==4:
      vol = ingest_scd_view (target_view_name, period_number, metric_name, product_identifier,market_basket_name)
    else:
      vol = ingest_scd_view (target_view_name, period_number, metric_name, product_identifier)
  
    if target_view_name.find('BRICK') != -1:  # use brick_hco_code for BRICK
      reporting_level_name = 'BRICK_HCO_CODE'
    else:
      reporting_level_name = 'SALES_REFERENCE_ACCOUNT_ID'  # use sales_reference_account_id for ACCOUNT

    target = vol.columns[-1]
  
    vol_clean = remove_inadequate_data(vol, target, reporting_level_name)

    print("Filtered sales data count:" + str(vol_clean.count()) + " for datapoint-key:" + str(scd_out_datapoint_key))  

    if (vol_clean.count() == 0):
      vol_model = sqlContext.createDataFrame(sc.emptyRDD(), schema_output)
    else:
      if (use_case_key == 2):
        vol_model = gap_detect_fn(vol_clean, target, reporting_level_name)
      else:
        vol_model = anomaly_fn(vol_clean, target, reporting_level_name, alpha_up, alpha_down)
    
    reporting_level_value = vol_model.columns[0]
    
    if (vol_model.count() > 0):
        vol_metric = create_metrics(vol_model,product_name,reporting_level_value,reporting_level_name, scd_out_datapoint_key)
    else:
        vol_metric = sqlContext.createDataFrame(sc.emptyRDD(), schema_bi_metric)
        
    return vol_model, vol_metric, vol_clean, vol

def persistOutput(conn, model_df, metric_df, scd_out_datapoint_key):

  # Step1: Prepare the extra columns needed

  #model_df = model_df.withColumn("SCD_POST_PROCESS_OUTPUT_KEY", monotonically_increasing_id())
  #metric_df = metric_df.withColumn("SCD_POST_PROCESS_BI_METRIC_OUTPUT_KEY", monotonically_increasing_id())
  model_df = model_df.withColumn("SCD_POST_PROCESS_OUTPUT_KEY", F.lit(1))
  metric_df = metric_df.withColumn("SCD_POST_PROCESS_BI_METRIC_OUTPUT_KEY", F.lit(1))

  model_df = model_df.withColumn('CREATED_TS', F.current_timestamp())
  model_df = model_df.withColumn('UPDATED_TS', F.current_timestamp())

  metric_df = metric_df.withColumn('CREATED_TS', F.current_timestamp())
  metric_df = metric_df.withColumn('UPDATED_TS', F.current_timestamp())

  # Step2: Select the columns in the order required by the table
  model_df = model_df.select("SCD_POST_PROCESS_OUTPUT_KEY", "scd_name", "reporting_level_name", "reporting_level_value", "product_name", "sale_date", "observed", "predicted", "Score", "CREATED_TS", "UPDATED_TS", "scd_out_datapoint_key", "change_direction")
  metric_df = metric_df.select("SCD_POST_PROCESS_BI_METRIC_OUTPUT_KEY", "scd_name", "reporting_level_name", "product_name", "CURRENT_WEEK_DATE","ELIGIBILITY_PERCENT","ANOMALY_THRESHOLD", "REPORTING_LEVEL_COUNT", "CREATED_TS", "UPDATED_TS", "scd_out_datapoint_key", "change_direction")

  # Step3: Find the datapoint-key and date and purge from archive tables
  current_week_date=model_df.select("SALE_DATE")
  date = current_week_date.first()[0]

  sqlstmt_output_archive =f"DELETE FROM SCD_POST_PROCESS_OUTPUT_ARCHIVE WHERE CURRENT_WEEK_DATE ='{date}' AND SCD_OUT_DATAPOINT_KEY = {scd_out_datapoint_key}"
  sqlstmt_metric_archive =f"DELETE FROM SCD_POST_PROCESS_BI_METRIC_OUTPUT_ARCHIVE WHERE CURRENT_WEEK_DATE ='{date}' AND SCD_OUT_DATAPOINT_KEY = {scd_out_datapoint_key}"

  cur = con.cursor()
  try:
    cur.execute(sqlstmt_output_archive)
    cur.execute(sqlstmt_metric_archive)
  finally:
    cur.close()

  # Step4: Write to model output
  model_df.write.format("snowflake").options(**options).mode("append").option("dbtable", "SCD_POST_PROCESS_OUTPUT").option("continue_on_error","on").save()
  model_df.write.format("snowflake").options(**options).mode("append").option("dbtable", "SCD_POST_PROCESS_OUTPUT_ARCHIVE").option("continue_on_error","on").save()

  # Step5: Write to metrics output
  metric_df.write.format("snowflake").options(**options).mode("append").option("dbtable", "SCD_POST_PROCESS_BI_METRIC_OUTPUT").option("continue_on_error","on").save()
  metric_df.write.format("snowflake").options(**options).mode("append").option("dbtable", "SCD_POST_PROCESS_BI_METRIC_OUTPUT_ARCHIVE").option("continue_on_error","on").save()

  #df_output = df_output.select("SCD_POST_PROCESS_OUTPUT_KEY","SCD_NAME","REPORTING_LEVEL_NAME","REPORTING_LEVEL_VALUE","PRODUCT_NAME","CURRENT_WEEK_DATE","SCD_ACTUAL_VALUE","SCD_PREDICTED_VALUE","SCD_SCORE","CREATED_TS","UPDATED_TS","SCD_OUT_DATAPOINT_KEY","CHANGE_DIRECTION")
  #df_metrics = df_metrics.select("SCD_POST_PROCESS_BI_METRIC_OUTPUT_KEY","SCD_NAME","REPORTING_LEVEL_NAME","PRODUCT_NAME","CURRENT_WEEK_DATE","ELIGIBILITY_PERCENT","ANOMALY_THRESHOLD", "REPORTING_LEVEL_COUNT","CREATED_TS","UPDATED_TS","SCD_OUT_DATAPOINT_KEY","CHANGE_DIRECTION")

# Initialize spark and sql context
spark = SparkSession.builder.appName("SCD2 Model").getOrCreate()
sqlContext = SQLContext(sparkContext=spark.sparkContext, sparkSession=spark)
sc = spark.sparkContext
options = get_db_options()

scd_out_param = ingest_scd_tables()
rows = scd_out_param.rdd.collect()
read_scd_params()

schema_output = StructType([
    StructField("REPORTING_LEVEL_VALUE", StringType(), True),
    StructField("REPORTING_LEVEL_NAME", StringType(), True),
    StructField("SALE_DATE", DateType(), True),
    StructField("OBSERVED", FloatType(), True),
    StructField("PREDICTED", FloatType(), True), 
    StructField("SCD_NAME", StringType(), True),
    StructField("SCORE", FloatType(), True), 
    StructField("PRODUCT_NAME", StringType(), True),
    StructField("SCD_OUT_DATAPOINT_KEY", IntegerType(), True),
    StructField("CHANGE_DIRECTION", StringType(), True),
])

schema_bi_metric = StructType([
    StructField("CURRENT_WEEK_DATE", DateType(), True),
    StructField("SCD_NAME", StringType(), True),
    StructField("ELIGIBILITY_PERCENT", FloatType(), True),
    StructField("ANOMALY_THRESHOLD", FloatType(), True),  
    StructField("REPORTING_LEVEL_COUNT", IntegerType(), True),
    StructField("PRODUCT_NAME", StringType(), True),
    StructField("REPORTING_LEVEL_NAME", StringType(), True),
    StructField("SCD_OUT_DATAPOINT_KEY", IntegerType(), True),
    StructField("CHANGE_DIRECTION", StringType(), True),
])

con = sf.connector.connect(
  user=user,
  password=password,
  account=account,
  database=database, 
  schema=schema_scd,
  role=db_role,
  warehouse=warehouse
)
cur = con.cursor()

try:
    cur.execute("truncate table SCD_POST_PROCESS_OUTPUT")
    cur.execute("truncate table SCD_POST_PROCESS_BI_METRIC_OUTPUT")
finally:
    cur.close()

for row in rows:
  use_case_key = row.SCD_IN_USE_CASE_KEY
  period_number = row.PERIOD_NUMBER
  metric_name = row.METRIC_NAME
  product_name = row.PRODUCT_NAME
  target_view_name = row.TARGET_VIEW_NAME
  scd_out_datapoint_key = row.SCD_OUT_DATAPOINT_KEY
  reporting_level_name_string = row.REPORTING_LEVEL_NAME
  market_basket_name  = row.MARKET_BASKET_NAME
  product_identifer = row.PRODUCT_IDENTIFIER
  alpha_up = float(row.ALPHA_UP or 0)
  alpha_down = float(row.ALPHA_DOWN or 0)
    
  print("Processing datapoint-key:" + str(scd_out_datapoint_key))  
  vol_model, vol_metric, vol_clean, vol = processRow(use_case_key, target_view_name, scd_out_datapoint_key, period_number, metric_name, product_name, product_identifer, market_basket_name, alpha_up, alpha_down)
  vol_model.cache()
  vol_metric.cache()
  output_count = vol_model.count()
  print("Output count:" + str(vol_model.count()) + " for datapoint-key:" + str(scd_out_datapoint_key))  
  if (output_count > 0):
    persistOutput(con, vol_model, vol_metric, scd_out_datapoint_key)
  #vol_model.to_csv('scd_out' + str(scd_out_datapoint_key) + '.csv', index=False)
  #svol_model = spark.createDataFrame(vol_model)
  #svol_model = svol_model.select("SCD_NAME","REPORTING_LEVEL_NAME","REPORTING_LEVEL_VALUE","PRODUCT_NAME","CURRENT_WEEK_DATE","SCD_ACTUAL_VALUE","SCD_PREDICTED_VALUE","SCD_SCORE","CREATED_TS","UPDATED_TS","SCD_OUT_DATAPOINT_KEY","CHANGE_DIRECTION")
  #svol_model.write.format("snowflake").options(**options).mode("append").option("dbtable", "SCD_POST_PROCESS_OUTPUT_INT").option("continue_on_error","on").save()
  
con.close()


