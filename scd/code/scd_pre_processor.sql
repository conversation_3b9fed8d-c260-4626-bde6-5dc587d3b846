--------------------------------------------------
-- <PERSON><PERSON><PERSON> to initialize SCD_IN tables whenever new sales data is loaded.
-- This script will preserve the list of products (SCD_IN_PRODUCT_CONFIG table with user's updates)
-- It will delete and recreate the other 2 tables (SALES_FACT_SUMMARY and PRODUCT_METRIC_SUMMARY)
--------------------------------------------------

--------------------------------------------------
-- High level steps

-- 1. Summarize the raw facts to SCD_IN_SALES_FACT_SUMMARY
-- a. First, from BRICK_WEEKLY
-- b. Join with both (internal and external) product tables to find the latest key and natural-key for both
-- c. Then, from ACCOUNT_WEEKLY (need skip logic, if fact table does not exist and if DB design is not going to pre-create empty fact tables for it)
-- Note: Don't do this for BRICK_MONTHLY, ACOUNT_MONTHLY now.  To be added later if/when SCD model supports it.
-- d. Before using the sales-fact-summary data, update the latest DIM_PRODUCT_KEY, DIM_PRODUCT_ALL_KEY (and natural keys from ALL_PRODUCTS list) in case they have been versioned/updated to newer records

-- 2. Use the SALES_FACT_SUMMARY, to insert/update the unique list of products into SCD_IN_PRODUCT_CONFIG.
-- a. Update latest DIM_PRODUCT_KEY, PRODUCT_UID and DIM_PRODUCT_ALL_KEY for all products. Note: This update is being done with the natural key of the sales product to capture any product that was originally in sales-data and later moved to being part of DSE
-- b. Insert any new products. Again, the not-exists query below is based on the natural key of the sales product (this will capture all products in sales-data regardless of when they were converted to DSE products or when the mapping data was provided)

-- 3. Update the additional attributes for the products
-- a. Update the latest PRODUCT_NAME, IS_COMPETITOR for all products
-- b. Then, update PRODUCT_ID (for DSE) products
-- c. Finally, Auto-enable SCD for all internal products?

-- 4. Update SCD_IN_SALES_FACT_SUMMARY with PRODUCT_CONFIG_KEY for ease of joining (without having to rely on PRODUCT_UID or PRODUCT_ALL_PRODUCT_IDENTIFIER)
-- 5. Compute SCD_IN_PRODUCT_METRIC_SUMMARY for useable metrics for each product.  The 2 rules are: x days of history (max_date - min_date) and most-recent sale within y days of tolerance
-- 6. For each product and mkt-basket that it is part of, find how many of the other-produts have this metric useable. If any of them are, then the mkt-basket is useable.  Else, not useable.
-- 7. Finally, mark any products for which no market-basket is defined or when no products exist for the basket as not useable with DIM_MARKETBASKET_KEY = -1

--------------------------------------------------

--------------------------------------------------
-- 0. Re-build sales rollup from central sales schema
---------------------------------------------------

/*
truncate table SCD.F_ACCOUNT_WEEKLY_ROLLUP;

INSERT INTO SCD.F_ACCOUNT_WEEKLY_ROLLUP
      (		DIM_EXTERNAL_ACCOUNT_KEY,DIM_BRAND_KEY,DIM_PRODUCT_ALL_KEY,DIM_PRODUCT_EXTENSION_KEY,DIM_CURRENCY_KEY,
            DIM_METRIC_KEY,DIM_FREQUENCY_KEY,ACCOUNT_NAME,ACCOUNT_ID,ACCOUNT_UID,SALES_REFERENCE_ACCOUNT_ID,
            BRAND_NAME,PRODUCT_NAME,PRODUCT_NAME_ENGLISH,PRODUCT_IDENTIFIER,PRODUCT_UID,PRODUCT_STRENGTH,PRODUCT_PACKAGE,
            CURRENCY_CODE,METRIC_NAME,FREQUENCY_NAME,SALE_DATE,SALE_YEAR,WEEK_NUMBER,QUANTITY,NET_SALE_AMOUNT
       )
SELECT      DIM_EXTERNAL_ACCOUNT_KEY,DIM_BRAND_KEY,DIM_PRODUCT_ALL_KEY,DIM_PRODUCT_EXTENSION_KEY,DIM_CURRENCY_KEY,
            DIM_METRIC_KEY,DIM_FREQUENCY_KEY,ACCOUNT_NAME,ACCOUNT_ID,ACCOUNT_UID,SALES_REFERENCE_ACCOUNT_ID,
            BRAND_NAME,PRODUCT_NAME,PRODUCT_NAME_ENGLISH,PRODUCT_IDENTIFIER,PRODUCT_UID,PRODUCT_STRENGTH,PRODUCT_PACKAGE,
            CURRENCY_CODE,METRIC_NAME,FREQUENCY_NAME,SALE_DATE,SALE_YEAR,WEEK_NUMBER,QUANTITY,NET_SALE_AMOUNT
FROM        SCD.VW_F_ACCOUNT_WEEKLY_ROLLUP
;
*/

-- TODO: Insert statements for BRICK_WEEKLY_ROLLUP and MKTSHARE-CUSTOM-ROLLUP here

--------------------------------------------------
-- 1. Summarize the raw facts
---------------------------------------------------
DELETE FROM
    SCD.SCD_IN_SALES_FACT_SUMMARY;

-- 1a. First from BRICK_WEEKLY
-- Join with both (internal and external) product tables to find the latest key and natural-key for both
-- Note: DIM_PRODUCT_KEY is no longer populated in this query (directly from the fact table).  Instead it is built later using PRODUCT_UID
INSERT INTO
    SCD.SCD_IN_SALES_FACT_SUMMARY (
        DIM_METRIC_KEY,
        DIM_FREQUENCY_KEY,
        SCD_IN_REPORTING_LEVEL_KEY,
        DIM_PRODUCT_ALL_KEY,
        PRODUCT_UID,
        PRODUCT_ALL_PRODUCT_IDENTIFIER,
        PRODUCT_ALL_SOURCE_SYSTEM_NAME,
        MIN_SALE_DATE,
        MAX_SALE_DATE,
        IS_DELETED,
        CREATED_TS,
        UPDATED_TS
    )
SELECT
    DIM_METRIC_KEY,
    DIM_FREQUENCY_KEY,
    1, -- 1 for BRICK and 2 for ACCOUNT tables
    MAX(pa.DIM_PRODUCT_ALL_KEY),
    latest_pa.PRODUCT_UID,
    COALESCE(latest_pa.PRODUCT_UID, latest_pa.PRODUCT_IDENTIFIER),
    latest_pa.SOURCE_SYSTEM_NAME,
    MIN(SALE_DATE) AS MIN_SALE_DATE,
    MAX(SALE_DATE) AS MAX_SALE_DATE,
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM
    $(REGION_DB).DW_CENTRAL_VIEW.VW_F_BRICK_WEEKLY f
        LEFT JOIN $(REGION_DB).DW_CENTRAL_VIEW.VW_DIM_PRODUCT_ALL pa ON f.DIM_PRODUCT_ALL_KEY = pa.DIM_PRODUCT_ALL_KEY
        LEFT JOIN $(REGION_DB).DW_CENTRAL_VIEW.VW_DIM_PRODUCT_ALL latest_pa on pa.PRODUCT_IDENTIFIER = latest_pa.PRODUCT_IDENTIFIER AND pa.SOURCE_SYSTEM_NAME = latest_pa.SOURCE_SYSTEM_NAME AND latest_pa.RECORD_END_DATE > TO_DATE('2099-01-01', 'YYYY-MM-DD')
WHERE 
    QUANTITY > 0
GROUP BY
    DIM_METRIC_KEY,
    DIM_FREQUENCY_KEY,
    latest_pa.PRODUCT_UID,
    COALESCE(latest_pa.PRODUCT_UID, latest_pa.PRODUCT_IDENTIFIER),
    latest_pa.SOURCE_SYSTEM_NAME;
    
    
-- TODO: 1b. Then, from ACCOUNT_WEEKLY (need skip logic, if fact table does not exist and if DB design is not going to pre-create empty fact tables for it)
-- Don't do this for BRICK_MONTHLY, ACOUNT_MONTHLY until SCD model supports it
-- Note: DIM_PRODUCT_KEY is no longer populated in this query (directly from the fact table).  Instead it is built later using PRODUCT_UID
INSERT INTO
    SCD.SCD_IN_SALES_FACT_SUMMARY (
        DIM_METRIC_KEY,
        DIM_FREQUENCY_KEY,
        SCD_IN_REPORTING_LEVEL_KEY,
        DIM_PRODUCT_ALL_KEY,
        PRODUCT_UID,
        PRODUCT_ALL_PRODUCT_IDENTIFIER,
        PRODUCT_ALL_SOURCE_SYSTEM_NAME,
        MIN_SALE_DATE,
        MAX_SALE_DATE,
        IS_DELETED,
        CREATED_TS,
        UPDATED_TS
    )
SELECT
    DIM_METRIC_KEY,
    DIM_FREQUENCY_KEY,
    2, -- 1 for BRICK and 2 for ACCOUNT tables
    MAX(pa.DIM_PRODUCT_ALL_KEY),
    latest_pa.PRODUCT_UID,
    COALESCE(latest_pa.PRODUCT_UID, latest_pa.PRODUCT_IDENTIFIER),
    latest_pa.SOURCE_SYSTEM_NAME,
    MIN(SALE_DATE) AS MIN_SALE_DATE,
    MAX(SALE_DATE) AS MAX_SALE_DATE,
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM
    $(REGION_DB).DW_CENTRAL_VIEW.VW_F_ACCOUNT_WEEKLY f
        LEFT JOIN $(REGION_DB).DW_CENTRAL_VIEW.VW_DIM_PRODUCT_ALL pa ON f.DIM_PRODUCT_ALL_KEY = pa.DIM_PRODUCT_ALL_KEY
        LEFT JOIN $(REGION_DB).DW_CENTRAL_VIEW.VW_DIM_PRODUCT_ALL latest_pa on pa.PRODUCT_IDENTIFIER = latest_pa.PRODUCT_IDENTIFIER AND pa.SOURCE_SYSTEM_NAME = latest_pa.SOURCE_SYSTEM_NAME AND latest_pa.RECORD_END_DATE > TO_DATE('2099-01-01', 'YYYY-MM-DD')
WHERE 
    QUANTITY > 0
GROUP BY
    DIM_METRIC_KEY,
    DIM_FREQUENCY_KEY,
    latest_pa.PRODUCT_UID,
    COALESCE(latest_pa.PRODUCT_UID, latest_pa.PRODUCT_IDENTIFIER),
    latest_pa.SOURCE_SYSTEM_NAME;

-- 1c. Before using the sales-fact-summary data, update the latest DIM_PRODUCT_KEY, DIM_PRODUCT_ALL_KEY (and natural keys from ALL_PRODUCTS list) in case they have been versioned/updated to newer records
-- TODO: Replace DIM_PRODUCT_EXTERNAL with new tablename, DIM_EXTERNAL_PRODUCT_KEY with DIM_PRODUCT_ALL_KEY and use PRODUCT_IDENTIFIER instead of PRODUCT_NAME once new tables are avaiable
-- TODO: Confirm if 'dse' or 'DSE' is used - sing UPPER function for now.  Confirm if 2099 or 9999 is used - using greater-than 2099-Jan for now
UPDATE 
    SCD.SCD_IN_SALES_FACT_SUMMARY fs
SET
    DIM_PRODUCT_KEY = p.DIM_PRODUCT_KEY,
    DIM_PRODUCT_ALL_KEY = latest_pa.DIM_PRODUCT_ALL_KEY
    --PRODUCT_ALL_PRODUCT_IDENTIFIER = COALESCE(latest_pa.PRODUCT_UID, latest_pa.PRODUCT_IDENTIFIER),
    --PRODUCT_ALL_SOURCE_SYSTEM_NAME = COALESCE(latest_pa.SOURCE_SYSTEM_NAME, UPPER(p.SOURCE_SYSTEM_NAME))
FROM
    SCD.SCD_IN_SALES_FACT_SUMMARY f
    LEFT JOIN $(REGION_DB).DW_CENTRAL_VIEW.VW_DIM_PRODUCT p ON f.PRODUCT_UID = p.PRODUCT_UID and p.RECORD_END_DATE > to_date('2099-01-01', 'YYYY-MM-DD') and UPPER(p.SOURCE_SYSTEM_NAME) = 'DSE'
    LEFT JOIN $(REGION_DB).DW_CENTRAL_VIEW.VW_DIM_PRODUCT_ALL pa ON f.DIM_PRODUCT_ALL_KEY = pa.DIM_PRODUCT_ALL_KEY
    LEFT JOIN $(REGION_DB).DW_CENTRAL_VIEW.VW_DIM_PRODUCT_ALL latest_pa on pa.PRODUCT_IDENTIFIER = latest_pa.PRODUCT_IDENTIFIER AND pa.SOURCE_SYSTEM_NAME = latest_pa.SOURCE_SYSTEM_NAME AND latest_pa.RECORD_END_DATE > TO_DATE('2099-01-01', 'YYYY-MM-DD')
WHERE 
 f.SCD_IN_SALES_FACT_SUMMARY_KEY = fs.SCD_IN_SALES_FACT_SUMMARY_KEY;

COMMIT;

--------------------------------------------------
-- 2. Use the SALES_FACT_SUMMARY, to build the unique list of products into SCD_IN_PRODUCT_CONFIG
---------------------------------------------------
-- 2a. Cleanup any old PRODUCT_IDENTIFIER values to be reset with PRODUCT_UID if it exists
-- Needed only till all existing customers switch to V27 code and run SCD pre-proc once
UPDATE 
    SCD.SCD_IN_PRODUCT_CONFIG p
SET 
    PRODUCT_ALL_PRODUCT_IDENTIFIER = PRODUCT_UID
WHERE  
    PRODUCT_UID IS NOT NULL;

-- 2b. Do insert/update of products into SCD_IN_PRODUCT_CONFIG
-- Update latest DIM_PRODUCT_KEY, PRODUCT_UID and DIM_PRODUCT_ALL_KEY for all products
-- Note: This update is being done with the natural key of the sales product to capture any product that was originally in sales-data and later moved to being part of DSE
UPDATE 
    SCD.SCD_IN_PRODUCT_CONFIG p
SET
    p.DIM_PRODUCT_KEY = fs_latest_key.DIM_PRODUCT_KEY,
    p.DIM_PRODUCT_ALL_KEY = fs_latest_key.DIM_PRODUCT_ALL_KEY
FROM
    (select PRODUCT_UID, 
        CASE WHEN PRODUCT_UID is NULL THEN PRODUCT_ALL_PRODUCT_IDENTIFIER ELSE PRODUCT_UID END PRODUCT_ALL_PRODUCT_IDENTIFIER, 
        PRODUCT_ALL_SOURCE_SYSTEM_NAME, 
        MAX(f.DIM_PRODUCT_KEY) DIM_PRODUCT_KEY,
        MAX(f.DIM_PRODUCT_ALL_KEY) DIM_PRODUCT_ALL_KEY
    from SCD.SCD_IN_SALES_FACT_SUMMARY f 
    group by PRODUCT_UID, 
        CASE WHEN PRODUCT_UID is NULL THEN PRODUCT_ALL_PRODUCT_IDENTIFIER ELSE PRODUCT_UID END, 
        PRODUCT_ALL_SOURCE_SYSTEM_NAME) fs_latest_key
WHERE
    fs_latest_key.PRODUCT_ALL_PRODUCT_IDENTIFIER = p.PRODUCT_ALL_PRODUCT_IDENTIFIER AND fs_latest_key.PRODUCT_ALL_SOURCE_SYSTEM_NAME = p.PRODUCT_ALL_SOURCE_SYSTEM_NAME
;
  
-- 2c. Insert any new products
-- Again, the not-exists query below is based on the natural key of the sales product 
-- (this will capture all products in sales-data regardless of when they were converted to DSE products or when the mapping data was provided)
INSERT INTO
    SCD.SCD_IN_PRODUCT_CONFIG (
        PRODUCT_UID,
        PRODUCT_ALL_PRODUCT_IDENTIFIER,
        PRODUCT_ALL_SOURCE_SYSTEM_NAME,
        DIM_PRODUCT_KEY,
        DIM_PRODUCT_ALL_KEY,
        IS_DELETED,
        CREATED_TS,
        UPDATED_TS
    )
SELECT
    PRODUCT_UID,
    PRODUCT_ALL_PRODUCT_IDENTIFIER,
    PRODUCT_ALL_SOURCE_SYSTEM_NAME,
    DIM_PRODUCT_KEY,
    DIM_PRODUCT_ALL_KEY,
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM
    (
        SELECT
            PRODUCT_UID,
            CASE WHEN PRODUCT_UID is NULL THEN PRODUCT_ALL_PRODUCT_IDENTIFIER ELSE PRODUCT_UID END PRODUCT_ALL_PRODUCT_IDENTIFIER,
            PRODUCT_ALL_SOURCE_SYSTEM_NAME,
            DIM_PRODUCT_KEY,
            DIM_PRODUCT_ALL_KEY
        FROM
            SCD.SCD_IN_SALES_FACT_SUMMARY
        GROUP BY
            PRODUCT_UID,
            CASE WHEN PRODUCT_UID is NULL THEN PRODUCT_ALL_PRODUCT_IDENTIFIER ELSE PRODUCT_UID END,
            PRODUCT_ALL_SOURCE_SYSTEM_NAME,
            DIM_PRODUCT_KEY,
            DIM_PRODUCT_ALL_KEY
    ) fs
WHERE
    NOT EXISTS (
        SELECT
            1
        FROM
            SCD.SCD_IN_PRODUCT_CONFIG p
        WHERE
            fs.PRODUCT_ALL_PRODUCT_IDENTIFIER = p.PRODUCT_ALL_PRODUCT_IDENTIFIER AND fs.PRODUCT_ALL_SOURCE_SYSTEM_NAME = p.PRODUCT_ALL_SOURCE_SYSTEM_NAME
    );

-- 3a. Update the latest PRODUCT_NAME, IS_COMPETITOR for all products
-- Note DIM_PRODUCT_ALL.IS_COMPETITOR = TRUE for any sales product for which mapping is not provided. So, for all external products, flag will get set to true by this query.
update
    SCD.SCD_IN_PRODUCT_CONFIG sp
set
    sp.PRODUCT_NAME = p.PRODUCT_IDENTIFIER || '-' || p.PRODUCT_NAME, -- use product-identifier since product-name is not unique
    sp.IS_COMPETITOR = COALESCE(p.IS_COMPETITOR, FALSE)
from
    $(REGION_DB).DW_CENTRAL_VIEW.VW_DIM_PRODUCT_ALL p
where
    sp.DIM_PRODUCT_ALL_KEY = p.DIM_PRODUCT_ALL_KEY;

-- 3b. Then, update PRODUCT_ID (for DSE) products
-- Note: For external-products, IS_COMPETITOR will be eset to true by query 3a above.
-- Now, for a mapped-product, check if DIM_PRODUCT.IS_COMPETITOR is set and update that.
update
    SCD.SCD_IN_PRODUCT_CONFIG sp
set
    sp.PRODUCT_NAME = p.PRODUCT_NAME,
    sp.PRODUCT_ID = p.PRODUCT_ID,
    sp.IS_COMPETITOR =  COALESCE(p.IS_COMPETITOR, FALSE) 
from
    $(REGION_DB).DW_CENTRAL_VIEW.VW_DIM_PRODUCT p
where
    sp.DIM_PRODUCT_KEY = p.DIM_PRODUCT_KEY;

-- 3c. Finally, Auto-enable SCD for all internal products?
-- TODO: Need to see more data to decide if this is correct and worth doing
-- Disabled the following based on SI-4455 - since we don't want to auto-enable un-useable products and even the useable products list is long

-- update
--     SCD.SCD_IN_PRODUCT_CONFIG
-- set
--     IS_SCD_ENABLED = 
--         CASE
--         WHEN IS_COMPETITOR = FALSE THEN TRUE
--         ELSE FALSE
--         END
-- where
--     IS_SCD_ENABLED is null;

COMMIT;

---------------------------------------------------
-- 4. Update SCD_IN_SALES_FACT_SUMMARY with PRODUCT_CONFIG_KEY for ease of joining (without having to rely on PRODUCT_UID or PRODUCT_ALL_PRODUCT_IDENTIFIER)
---------------------------------------------------
UPDATE
    SCD.SCD_IN_SALES_FACT_SUMMARY fs
SET
    SCD_IN_PRODUCT_CONFIG_KEY = p.SCD_IN_PRODUCT_CONFIG_KEY
FROM
    SCD.SCD_IN_PRODUCT_CONFIG p
WHERE
    fs.PRODUCT_ALL_PRODUCT_IDENTIFIER = p.PRODUCT_ALL_PRODUCT_IDENTIFIER AND fs.PRODUCT_ALL_SOURCE_SYSTEM_NAME = p.PRODUCT_ALL_SOURCE_SYSTEM_NAME
;

COMMIT;

---------------------------------------------------
-- 5. Compute PRODUCT_METRIC_SUMMARY for useable metrics for each product (2 rules: x days of history (max_date - min_date) and most-recent sale within y days of tolerance)
---------------------------------------------------
DELETE FROM
    SCD.SCD_IN_PRODUCT_METRIC_SUMMARY;

INSERT INTO
    SCD.SCD_IN_PRODUCT_METRIC_SUMMARY (
        SCD_IN_PRODUCT_CONFIG_KEY,
        DIM_METRIC_KEY,
        DIM_FREQUENCY_KEY,
        SCD_IN_REPORTING_LEVEL_KEY,
        IS_USEABLE,
        REASON_TEXT,
        IS_DELETED,
        CREATED_TS,
        UPDATED_TS
    )
SELECT
    fs.SCD_IN_PRODUCT_CONFIG_KEY,
    DIM_METRIC_KEY,
    DIM_FREQUENCY_KEY,
    SCD_IN_REPORTING_LEVEL_KEY,
    CASE
        WHEN MAX_SALE_DATE - MIN_SALE_DATE >= TO_NUMBER(p1.PARAM_VALUE) THEN CASE
            WHEN CURRENT_DATE - MAX_SALE_DATE <= TO_NUMBER(p2.PARAM_VALUE) THEN TRUE
            ELSE FALSE
        END
        ELSE FALSE
    END IS_USEABLE,
    CASE
        WHEN MAX_SALE_DATE - MIN_SALE_DATE >= TO_NUMBER(p1.PARAM_VALUE) THEN CASE
            WHEN CURRENT_DATE - MAX_SALE_DATE <= TO_NUMBER(p2.PARAM_VALUE) THEN NULL
            ELSE 'Most-recent-sale-date is ' || TO_CHAR(MAX_SALE_DATE) || '. Most recent sale should be within the last ' || p2.PARAM_VALUE || ' days'
        END
        ELSE 'MAX=' || TO_CHAR(MAX_SALE_DATE) || ' AND MIN=' || TO_CHAR(MIN_SALE_DATE) || ' found. ' || p1.PARAM_VALUE || ' days of history expected'
    END REASON_TEXT,
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM
    SCD.SCD_IN_SALES_FACT_SUMMARY fs,
    SCD.SCD_IN_PRODUCT_CONFIG p,
    SCD.SCD_IN_PARAM p1,
    SCD.SCD_IN_PARAM p2
WHERE
    fs.SCD_IN_PRODUCT_CONFIG_KEY = p.SCD_IN_PRODUCT_CONFIG_KEY
    and p1.PARAM_NAME = 'SCD_HISTORY_DAYS'
    and p2.PARAM_NAME = 'SCD_LATE_TOLERANCE_DAYS';

---------------------------------------------------
-- 6. For each product and mkt-basket that it is part of, find how many of the other-produts have this metric useable.  
-- If any of them are, then the mkt-basket is useable.  Else, not useable.
---------------------------------------------------
INSERT INTO
    SCD.SCD_IN_PRODUCT_METRIC_SUMMARY (
        SCD_IN_PRODUCT_CONFIG_KEY,
        DIM_METRIC_KEY,
        DIM_FREQUENCY_KEY,
        SCD_IN_REPORTING_LEVEL_KEY,
        DIM_MARKETBASKET_KEY,
        IS_USEABLE,
        REASON_TEXT,
        IS_DELETED,
        CREATED_TS,
        UPDATED_TS
    )
SELECT
    SCD_IN_PRODUCT_CONFIG_KEY,
    DIM_METRIC_KEY,
    DIM_FREQUENCY_KEY,
    SCD_IN_REPORTING_LEVEL_KEY,
    DIM_MARKETBASKET_KEY,
    CASE WHEN USEABLE_PROD_COUNT > 1 THEN TRUE ELSE FALSE END,
    CASE WHEN USEABLE_PROD_COUNT > 0 THEN '' || USEABLE_PROD_COUNT || ' / ' || (TOTAL_PROD_COUNT) || ' products available' ELSE 'No competitor product data available for this metric' END,
    --CASE WHEN USEABLE_PROD_COUNT = TOTAL_PROD_COUNT-1 THEN TRUE ELSE FALSE END,
    --CASE WHEN USEABLE_PROD_COUNT = TOTAL_PROD_COUNT-1 THEN NULL ELSE 'Only ' || USEABLE_PROD_COUNT || ' out of ' || TOTAL_PROD_COUNT || ' products are useable' END,
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM
    (SELECT
        others.SCD_IN_PRODUCT_CONFIG_KEY,
        pm.DIM_METRIC_KEY,
        pm.DIM_FREQUENCY_KEY,
        pm.SCD_IN_REPORTING_LEVEL_KEY,
        bskt_count.DIM_MARKETBASKET_KEY,
        SUM(
            CASE
                WHEN pm.IS_USEABLE THEN 1
                ELSE 0
            END
        ) AS USEABLE_PROD_COUNT,
        bskt_count.TOTAL_PROD_COUNT
    FROM
        SCD.vw_OTHER_BASKET_PRODUCTS others, SCD.SCD_IN_PRODUCT_METRIC_SUMMARY pm,
        (
            select
                DIM_MARKETBASKET_KEY,
                MARKETBASKET_NAME,
                COUNT(DIM_MARKETBASKET_KEY) TOTAL_PROD_COUNT
            from
                SCD.vw_MARKET_BASKET_PRODUCT
            group by
                DIM_MARKETBASKET_KEY,
                MARKETBASKET_NAME
        ) bskt_count -- total products in a basket
    WHERE
        others.OTHER_SCD_IN_PRODUCT_CONFIG_KEY = pm.SCD_IN_PRODUCT_CONFIG_KEY 
        and pm.DIM_MARKETBASKET_KEY is null 
        and others.DIM_MARKETBASKET_KEY = bskt_count.DIM_MARKETBASKET_KEY
        -- and others.SCD_IN_PRODUCT_CONFIG_KEY != others.OTHER_SCD_IN_PRODUCT_CONFIG_KEY -- keep the product being compared as well.  Otherwise, if this is the only product, it is not being selected
    GROUP BY
        others.SCD_IN_PRODUCT_CONFIG_KEY,
        bskt_count.DIM_MARKETBASKET_KEY,
        bskt_count.MARKETBASKET_NAME,
        pm.DIM_METRIC_KEY,
        pm.DIM_FREQUENCY_KEY,
        pm.SCD_IN_REPORTING_LEVEL_KEY,
        bskt_count.TOTAL_PROD_COUNT
    ) other_prod_count;

---------------------------------------------------
-- 6b. Finally, mark any products for which no market-basket is defined or when no products exist for the basket as not useable with DIM_MARKETBASKET_KEY = -1
---------------------------------------------------
INSERT INTO
    SCD.SCD_IN_PRODUCT_METRIC_SUMMARY (
        SCD_IN_PRODUCT_CONFIG_KEY,
        DIM_METRIC_KEY,
        DIM_FREQUENCY_KEY,
        SCD_IN_REPORTING_LEVEL_KEY,
        DIM_MARKETBASKET_KEY,
        IS_USEABLE,
        REASON_TEXT,
        IS_DELETED,
        CREATED_TS,
        UPDATED_TS
    )
SELECT
    SCD_IN_PRODUCT_CONFIG_KEY,
    m.DIM_METRIC_KEY,
    -1,
    -1,
    -1,
    FALSE,
    'No basket defined or no sales data available for any other product in the basket',
    FALSE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM
    SCD.SCD_IN_PRODUCT_CONFIG p,
    $(REGION_DB).DW_CENTRAL_VIEW.VW_DIM_METRIC m -- cross-join to get a row for all metrics
WHERE
    not exists (
        select
            1
        from
            SCD.vw_SCD_IN_USEABLE_SALES_DIM u
        where
            u.scd_in_product_config_key = p.scd_in_product_config_key
            and u.dim_marketbasket_key is not null
    ) -- no usability records created for marketbasket cases
    and not exists (
        select
            1
        from
            SCD.vw_MARKET_BASKET_PRODUCT bskt
        where
            bskt.SCD_IN_PRODUCT_CONFIG_KEY = p.SCD_IN_PRODUCT_CONFIG_KEY
    ) -- and no basket defined for this product
    ;

COMMIT;

-- Update useability flag for any existing datapoints based on current useability stats
update
    SCD.SCD_OUT_DATAPOINT d
set
    d.IS_USEABLE = pms.IS_USEABLE,
    d.REASON_TEXT = pms.REASON_TEXT
FROM
    SCD.SCD_IN_PRODUCT_METRIC_SUMMARY pms
WHERE
    d.SCD_IN_PRODUCT_CONFIG_KEY = pms.SCD_IN_PRODUCT_CONFIG_KEY
    AND d.DIM_METRIC_KEY = pms.DIM_METRIC_KEY
    AND d.DIM_FREQUENCY_KEY = pms.DIM_FREQUENCY_KEY
    AND d.SCD_IN_REPORTING_LEVEL_KEY = pms.SCD_IN_REPORTING_LEVEL_KEY
    AND COALESCE(d.DIM_MARKETBASKET_KEY,-2) = COALESCE(pms.DIM_MARKETBASKET_KEY,-2);

-- Update useability flag for any existing datapoints for which no useability data exists    
update
    SCD.SCD_OUT_DATAPOINT d
set
    d.IS_USEABLE = FALSE,
    d.REASON_TEXT = 'No summary stats exist for product/metric'
WHERE
    NOT EXISTS (
        select
            1
        FROM
            SCD.SCD_IN_PRODUCT_METRIC_SUMMARY pms
        WHERE
            d.SCD_IN_PRODUCT_CONFIG_KEY = pms.SCD_IN_PRODUCT_CONFIG_KEY
            AND d.DIM_METRIC_KEY = pms.DIM_METRIC_KEY
            AND d.DIM_FREQUENCY_KEY = pms.DIM_FREQUENCY_KEY
            AND d.SCD_IN_REPORTING_LEVEL_KEY = pms.SCD_IN_REPORTING_LEVEL_KEY
            AND COALESCE(d.DIM_MARKETBASKET_KEY,-2) = COALESCE(pms.DIM_MARKETBASKET_KEY,-2)
    );

COMMIT;

/*
delete from SCD.F_BRICK_WEEKLY_MARKETDATA_CUSTOM_ROLLUP;

INSERT INTO SCD.F_BRICK_WEEKLY_MARKETDATA_CUSTOM_ROLLUP
      (		DIM_BRICK_KEY,DIM_BRAND_KEY,DIM_PRODUCT_ALL_KEY,DIM_PRODUCT_EXTENSION_KEY,DIM_CURRENCY_KEY,
            DIM_METRIC_KEY,DIM_FREQUENCY_KEY,DIM_MARKETBASKET_KEY,BRICK_HCO_CODE,BRAND_NAME,PRODUCT_NAME,
            PRODUCT_NAME_ENGLISH,PRODUCT_IDENTIFIER,PRODUCT_UID,PRODUCT_STRENGTH,PRODUCT_PACKAGE,
            CURRENCY_CODE,METRIC_NAME,FREQUENCY_NAME,MARKETBASKET_NAME,MARKET_METRIC_AS_OF_DATE,
            MARKET_METRIC_AS_OF_YEAR,WEEK_NUMBER,
            MARKET_SHARE_VALUE,MARKET_VOLUME
       )
SELECT      DIM_BRICK_KEY,DIM_BRAND_KEY,DIM_PRODUCT_ALL_KEY,DIM_PRODUCT_EXTENSION_KEY,DIM_CURRENCY_KEY,
            DIM_METRIC_KEY,DIM_FREQUENCY_KEY,DIM_MARKETBASKET_KEY,BRICK_HCO_CODE,BRAND_NAME,PRODUCT_NAME,
            PRODUCT_NAME_ENGLISH,PRODUCT_IDENTIFIER,PRODUCT_UID,PRODUCT_STRENGTH,PRODUCT_PACKAGE,
            CURRENCY_CODE,METRIC_NAME,FREQUENCY_NAME,MARKETBASKET_NAME,SALE_DATE,
            SALE_YEAR,WEEK_NUMBER,
            MARKET_SHARE_VALUE,MARKET_VOLUME
FROM        SCD.VW_F_BRICK_WEEKLY_MARKETDATA_CUSTOM_ROLLUP
;

COMMIT;

delete from SCD.F_ACCOUNT_WEEKLY_MARKETDATA_CUSTOM_ROLLUP;

INSERT INTO SCD.F_ACCOUNT_WEEKLY_MARKETDATA_CUSTOM_ROLLUP
      (		DIM_EXTERNAL_ACCOUNT_KEY,DIM_BRAND_KEY,DIM_PRODUCT_ALL_KEY,DIM_PRODUCT_EXTENSION_KEY,DIM_CURRENCY_KEY,
            DIM_METRIC_KEY,DIM_FREQUENCY_KEY,DIM_MARKETBASKET_KEY,ACCOUNT_NAME,ACCOUNT_ID,ACCOUNT_UID,
            SALES_REFERENCE_ACCOUNT_ID,BRAND_NAME,PRODUCT_NAME,PRODUCT_NAME_ENGLISH,
            PRODUCT_IDENTIFIER,PRODUCT_UID,PRODUCT_STRENGTH,PRODUCT_PACKAGE,CURRENCY_CODE,METRIC_NAME,
            FREQUENCY_NAME,MARKETBASKET_NAME,MARKET_METRIC_AS_OF_DATE,MARKET_METRIC_AS_OF_YEAR,WEEK_NUMBER,
            MARKET_SHARE_VALUE,MARKET_VOLUME
       )
SELECT      DIM_EXTERNAL_ACCOUNT_KEY,DIM_BRAND_KEY,DIM_PRODUCT_ALL_KEY,DIM_PRODUCT_EXTENSION_KEY,DIM_CURRENCY_KEY,
            DIM_METRIC_KEY,DIM_FREQUENCY_KEY,DIM_MARKETBASKET_KEY,ACCOUNT_NAME,ACCOUNT_ID,ACCOUNT_UID,
            SALES_REFERENCE_ACCOUNT_ID,BRAND_NAME,PRODUCT_NAME,PRODUCT_NAME_ENGLISH,
            PRODUCT_IDENTIFIER,PRODUCT_UID,PRODUCT_STRENGTH,PRODUCT_PACKAGE,CURRENCY_CODE,METRIC_NAME,
            FREQUENCY_NAME,MARKETBASKET_NAME,SALE_DATE,SALE_YEAR,WEEK_NUMBER,
            MARKET_SHARE_VALUE,MARKET_VOLUME
FROM        SCD.VW_F_ACCOUNT_WEEKLY_MARKETDATA_CUSTOM_ROLLUP
;

COMMIT;
*/

delete from SCD.F_BRICK_WEEKLY_ROLLUP;

INSERT INTO SCD.F_BRICK_WEEKLY_ROLLUP
      (		DIM_BRICK_KEY,DIM_BRAND_KEY,DIM_PRODUCT_ALL_KEY,DIM_PRODUCT_EXTENSION_KEY,DIM_CURRENCY_KEY,
            DIM_METRIC_KEY,DIM_FREQUENCY_KEY,BRICK_HCO_CODE,BRAND_NAME,PRODUCT_NAME,PRODUCT_NAME_ENGLISH,
            PRODUCT_IDENTIFIER,PRODUCT_UID,PRODUCT_STRENGTH,PRODUCT_PACKAGE,CURRENCY_CODE,METRIC_NAME,
            FREQUENCY_NAME,SALE_DATE,SALE_YEAR,WEEK_NUMBER,QUANTITY,NET_SALE_AMOUNT
       )
SELECT      DIM_BRICK_KEY,DIM_BRAND_KEY,DIM_PRODUCT_ALL_KEY,DIM_PRODUCT_EXTENSION_KEY,DIM_CURRENCY_KEY,
            DIM_METRIC_KEY,DIM_FREQUENCY_KEY,BRICK_HCO_CODE,BRAND_NAME,PRODUCT_NAME,PRODUCT_NAME_ENGLISH,
            PRODUCT_IDENTIFIER,PRODUCT_UID,PRODUCT_STRENGTH,PRODUCT_PACKAGE,CURRENCY_CODE,METRIC_NAME,
            FREQUENCY_NAME,SALE_DATE,SALE_YEAR,WEEK_NUMBER,QUANTITY,NET_SALE_AMOUNT
FROM        SCD.VW_F_BRICK_WEEKLY_ROLLUP
;

COMMIT;

delete from SCD.F_ACCOUNT_WEEKLY_ROLLUP;

INSERT INTO SCD.F_ACCOUNT_WEEKLY_ROLLUP
      (		DIM_EXTERNAL_ACCOUNT_KEY,DIM_BRAND_KEY,DIM_PRODUCT_ALL_KEY,DIM_PRODUCT_EXTENSION_KEY,DIM_CURRENCY_KEY,
            DIM_METRIC_KEY,DIM_FREQUENCY_KEY,ACCOUNT_NAME,ACCOUNT_ID,ACCOUNT_UID,SALES_REFERENCE_ACCOUNT_ID,
            BRAND_NAME,PRODUCT_NAME,PRODUCT_NAME_ENGLISH,PRODUCT_IDENTIFIER,PRODUCT_UID,PRODUCT_STRENGTH,PRODUCT_PACKAGE,
            CURRENCY_CODE,METRIC_NAME,FREQUENCY_NAME,SALE_DATE,SALE_YEAR,WEEK_NUMBER,QUANTITY,NET_SALE_AMOUNT
       )
SELECT      DIM_EXTERNAL_ACCOUNT_KEY,DIM_BRAND_KEY,DIM_PRODUCT_ALL_KEY,DIM_PRODUCT_EXTENSION_KEY,DIM_CURRENCY_KEY,
            DIM_METRIC_KEY,DIM_FREQUENCY_KEY,ACCOUNT_NAME,ACCOUNT_ID,ACCOUNT_UID,SALES_REFERENCE_ACCOUNT_ID,
            BRAND_NAME,PRODUCT_NAME,PRODUCT_NAME_ENGLISH,PRODUCT_IDENTIFIER,PRODUCT_UID,PRODUCT_STRENGTH,PRODUCT_PACKAGE,
            CURRENCY_CODE,METRIC_NAME,FREQUENCY_NAME,SALE_DATE,SALE_YEAR,WEEK_NUMBER,QUANTITY,NET_SALE_AMOUNT
FROM        SCD.VW_F_ACCOUNT_WEEKLY_ROLLUP
;

COMMIT;
