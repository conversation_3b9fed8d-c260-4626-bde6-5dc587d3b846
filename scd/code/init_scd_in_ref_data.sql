
----------------------------
-- INSERT FOR REPORTING_LEVEL
----------------------------

delete from SCD.SCD_IN_REPORTING_LEVEL;

INSERT INTO SCD.SCD_IN_REPORTING_LEVEL (SCD_IN_REPORTING_LEVEL_KEY, REPORTING_LEVEL_NAME, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (1, 'BRICK', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_IN_REPORTING_LEVEL (SCD_IN_REPORTING_LEVEL_KEY, REPORTING_LEVEL_NAME, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (2, 'ACCOUNT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

commit;

----------------------------
-- INSERT FOR SCD USE CASE
----------------------------

delete from SCD.SCD_IN_USE_CASE;

INSERT INTO SCD.SCD_IN_USE_CASE (SCD_IN_USE_CASE_KEY, USE_CASE_NAME, USE_CASE_NAME_DESC, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (1, 'SALES VOLUME', 'Sales Volume Change', FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP() );

INSERT INTO SCD.SCD_IN_USE_CASE (SCD_IN_USE_CASE_KEY, USE_CASE_NAME, USE_CASE_NAME_DESC, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (2, 'ORDER GAP', 'Order Gap Detection', FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP() );

INSERT INTO SCD.SCD_IN_USE_CASE (SCD_IN_USE_CASE_KEY, USE_CASE_NAME, USE_CASE_NAME_DESC, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (3, 'MARKETBASKET VOLUME', 'Market Basket Volume Change', FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP() );

INSERT INTO SCD.SCD_IN_USE_CASE (SCD_IN_USE_CASE_KEY, USE_CASE_NAME, USE_CASE_NAME_DESC, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (4, 'MARKET SHARE', 'Market Share Change', FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP() );

commit;

----------------------------
-- INSERT FOR SCD TARGET_VIEWS
-- 16 rows for all combinations of 2 reporting-levels, 2 frequencies, 4 SCD USE CASES
-- REPORTING LEVEL (1, 2 for BRICK and ACCOUNT)
-- FREQUENCY (2, 3 for WEEKLY and MONTHLY)
-- SCD USE CASES (1,2,3,4 for SALES_VOLUME, ORDER_GAP, MARKETBASKET_VOLUME, MARKET_SHARE)
----------------------------

delete from SCD.SCD_IN_TARGET_VIEW_MAPPING;

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (1, 1, 2, 3, 'vw_ACCOUNT_WEEKLY_SALES_VOLUME', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (2, 2, 2, 3, 'vw_ACCOUNT_WEEKLY_ORDER_GAP_DETECTION', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (3, 3, 2, 3, 'vw_ACCOUNT_WEEKLY_MARKETBASKET_VOLUME', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (4, 4, 2, 3, 'vw_ACCOUNT_WEEKLY_MARKET_SHARE', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (5, 1, 1, 3, 'vw_BRICK_WEEKLY_SALES_VOLUME', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (6, 2, 1, 3, 'vw_BRICK_WEEKLY_ORDER_GAP_DETECTION', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (7, 3, 1, 3, 'vw_BRICK_WEEKLY_MARKETBASKET_VOLUME', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (8, 4, 1, 3, 'vw_BRICK_WEEKLY_MARKET_SHARE', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (9, 3, 2, 3, 'vw_ACCOUNT_WEEKLY_MARKETBASKET_VOLUME_CUSTOM', 1, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (10, 4, 2, 3, 'vw_ACCOUNT_WEEKLY_MARKET_SHARE_CUSTOM', 1, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (11, 3, 1, 3, 'vw_BRICK_WEEKLY_MARKETBASKET_VOLUME_CUSTOM', 1, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (12, 4, 1, 3, 'vw_BRICK_WEEKLY_MARKET_SHARE_CUSTOM', 1, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

---------------

/*
-- Skip the monthly views since SCD2 does not support detection with monthly data

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (13, 1, 2, 4,'vw_ACCOUNT_MONTHLY_SALES_VOLUME', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (14, 2, 2, 4,'vw_ACCOUNT_MONTHLY_ORDER_GAP_DETECTION', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (15, 3, 2, 4,'vw_ACCOUNT_MONTHLY_MARKETBASKET_VOLUME', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (16, 4, 2, 4,'vw_ACCOUNT_MONTHLY_MARKET_SHARE', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (17, 1, 1, 4, 'vw_BRICK_MONTHLY_SALES_VOLUME', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (18, 2, 1, 4, 'vw_BRICK_MONTHLY_ORDER_GAP_DETECTION', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (19, 3, 1, 4, 'vw_BRICK_MONTHLY_MARKETBASKET_VOLUME', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (20, 4, 1, 4, 'vw_BRICK_MONTHLY_MARKET_SHARE', 0, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (21, 3, 2, 4,'vw_ACCOUNT_MONTHLY_MARKETBASKET_VOLUME_CUSTOM', 1, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (22, 4, 2, 4,'vw_ACCOUNT_MONTHLY_MARKET_SHARE_CUSTOM', 1, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (23, 3, 1, 4, 'vw_BRICK_MONTHLY_MARKETBASKET_VOLUME_CUSTOM', 1, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

INSERT INTO SCD.SCD_IN_TARGET_VIEW_MAPPING (SCD_IN_TARGET_VIEW_MAPPING_KEY, SCD_IN_USE_CASE_KEY, SCD_IN_REPORTING_LEVEL_KEY, DIM_FREQUENCY_KEY, TARGET_VIEW_NAME, IS_MARKET_CALC_CUSTOM, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (24, 4, 1, 4, 'vw_BRICK_MONTHLY_MARKET_SHARE_CUSTOM', 1, FALSE, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

*/

commit;

----------------------------
-- INSERT FOR SCD_IN_PARAM
----------------------------

delete from SCD.SCD_IN_PARAM;

INSERT INTO SCD.SCD_IN_PARAM (SCD_IN_PARAM_KEY, PARAM_NAME, PARAM_VALUE, PARAM_TYPE, PARAM_DESC, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (1, 'SCD_HISTORY_DAYS', '730', 'INTEGER', 'Min days of history required', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_IN_PARAM (SCD_IN_PARAM_KEY, PARAM_NAME, PARAM_VALUE, PARAM_TYPE, PARAM_DESC, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (2, 'SCD_LATE_TOLERANCE_DAYS', '60', 'INTEGER', 'Max days of tolerance allowed for sales file arrival', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- we seem to have 1 week less than 2 years data? is this a problem during purge or intentional?
update SCD.SCD_IN_PARAM
set PARAM_VALUE = '725'
where PARAM_NAME = 'SCD_HISTORY_DAYS';

-- Temporary override to be more lenient until we have more regular data loads
update SCD.SCD_IN_PARAM
set PARAM_VALUE = '300'
where PARAM_NAME = 'SCD_LATE_TOLERANCE_DAYS';

delete from SCD.SCD_BI_CONTROL_RANGE;

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (1, 'SCD_SUGG_VS_ALL_SUGG', NULL, 5, 15, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (2, 'SCD_SUGG_PUBLISHED_VS_TRIGGERED', NULL, 20, 50, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (3, 'SCD_SUGG_ACTION_COMPLETE_VS_ALL', NULL, 20, 70, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (4, 'SCD_SUGG_ACTION_INPROGRESS_VS_ALL', NULL, 20, 70, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (5, 'SCD_SUGG_ACTION_DISMISSED_VS_ALL', NULL, 10, 40, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (6, 'SCD_SUGG_ACTION_NOACTION_VS_ALL', NULL, 10, 40, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

commit;

