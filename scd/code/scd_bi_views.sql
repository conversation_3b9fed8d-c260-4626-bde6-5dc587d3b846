create
or replace view SCD.VW_DSECONFIGVERSION_FACTORS as (
    select
        seConfigId,
        versionId,
        to_varchar(f.value:id) as factorUID,
        to_varchar(f.value:rules[0]:labelValueMetricTypeId) as labelTypeId,
        to_varchar(f.value:rules[0]:attributeTypeId) as attributeTypeId,
        configSpecJson
    from
        DW_RAW.RAW_DSEConfigVersion,
        lateral flatten(
            input => parse_json(configSpecJson),
            recursive => true,
            path => 'factorList'
        ) f
    where
        isDeleted = 0
);

create
or replace view SCD.VW_DSECONFIGVERSION_SCD_FACTORS as (
    select
        seConfigId,
        versionId,
        factorUID,
        case
            when labelTypeId like 'ACCOUNT_SALES%'
            or labelTypeId like 'BRICK_SALES%'
            or labelTypeId like 'ACCOUNT_MARKET%'
            or labelTypeId like 'BRICK_MARKET%'
            or labelTypeId like 'ACCOUNT_ORDER_GAP%'
            or labelTypeId like 'BRICK_ORDER_GAP%' 
            or attributeTypeId like 'ACCOUNT_SALES%'
            or attributeTypeId like 'BRICK_SALES%'
            or attributeTypeId like 'ACCOUNT_MARKET%'
            or attributeTypeId like 'BRICK_MARKET%'
            or attributeTypeId like 'ACCOUNT_ORDER_GAP%'
            or attributeTypeId like 'BRICK_ORDER_GAP%' 
            then 1
            else 0
        end as is_scd_sugg,
        COALESCE (labelTypeId, attributeTypeId) as labelTypeId
    from
        vw_DSEConfigVersion_factors
    where
        is_scd_sugg > 0
);

create
or replace view SCD.VW_DSE_SUGGESTION as (
    select
        sr.runuid,
        r.seConfigId,
        r.versionId,
        sr.suggestedDate,
        srs.suggestionReferenceId,
        srs.accountUID,
        srsd.detailRepActionTypeUID,
        srsd.factorUID,
        srsd.productUID as product_uid,
        sl.reportedInteractionUID,
        sl.inferredInteractionUID,
        sl.dismissCount,
        sl.isSuggestionActive,
        sl.isSuggestionCompleted,
        sl.isSuggestionActioned,
        sl.isSuggestionDismissed,
        sl.isSuggestionCompletedInfer --,srs.suggestedChannelId
    from
        DW_RAW.RAW_SPARKDSERUNREPDATESUGGESTION srs
        inner join DW_RAW.RAW_SPARKDSERUNREPDATESUGGESTIONDETAIL srsd on srs.runRepDateSuggestionId = srsd.runRepDateSuggestionId
        inner join DW_RAW.RAW_DSESuggestionLifecycle sl on srs.suggestionReferenceId = sl.suggestionReferenceId
        inner join DW_RAW.RAW_SPARKDSERUNREPDATE sr on sr.runRepDateId = srs.runRepDateId
        inner join DW_RAW.RAW_SPARKDSERUN r on r.runuid = sr.runuid
);

create
or replace view SCD.VW_DSE_SUGGESTION_HCP_COUNT as (
    select
        DATEADD('DAY',5,DATE_TRUNC('WEEK',suggestedDate)) suggestion_date,
        p.PRODUCT_ID, p.PRODUCT_NAME,
        case
            when isSuggestionDismissed = 1 then 'Dismissed'
            when isSuggestionDismissed = 0
            and isSuggestionCompleted = 0
            and isSuggestionActioned = 0 then 'No Action Taken'
            else case
                when i.isCompleted = 1
                or j.isCompleted = 1 then 'Accepted/Complete'
                else 'Accepted/Incomplete'
            end
            /* 'Accepted' when isSuggestionCompleted = 1 or isSuggestionActioned = 1 */
        end action_type,
        count(distinct accountUID) HCP_count
    from
        vw_DSE_Suggestion s
        INNER JOIN DW_CENTRAL.DIM_PRODUCT p on s.product_UID = p.PRODUCT_UID and p.RECORD_END_DATE = '9999-12-31'
        LEFT JOIN DW_RAW.RAW_Interaction i ON i.externalId = s.reportedInteractionUID
        LEFT JOIN DW_RAW.RAW_Interaction j ON j.externalId = s.inferredInteractionUID
    group by
    grouping sets (
        (suggestion_date, p.PRODUCT_ID, p.PRODUCT_NAME, action_type),
        (suggestion_date, action_type),
        (suggestion_date, p.PRODUCT_ID, p.PRODUCT_NAME),
        (suggestion_date)
    )
);

create
or replace view SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT as (
    select
        DATEADD('DAY',5,DATE_TRUNC('WEEK',suggestedDate)) suggestion_date,
        p.PRODUCT_ID, p.PRODUCT_NAME,
        case
            when isSuggestionDismissed = 1 then 'Dismissed'
            when isSuggestionDismissed = 0
            and isSuggestionCompleted = 0
            and isSuggestionActioned = 0 then 'No Action Taken'
            else case
                when i.isCompleted = 1
                or j.isCompleted = 1 then 'Accepted/Complete'
                else 'Accepted/Incomplete'
            end
            /* 'Accepted' when isSuggestionCompleted = 1 or isSuggestionActioned = 1 */
        end action_type,
        INITCAP(REPLACE(labelTypeId, '_', ' '),' /_') as LABEL_TYPE_EXT_ID,
        INITCAP(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(labelTypeId, '_INCREASE', ''),'_DECREASE',''),'ACCOUNT_',''),'BRICK_',''), '_', ' '),' /_') as USE_CASE_NAME,
        count(distinct accountUID) HCP_count
    from
        vw_DSE_Suggestion s
        INNER JOIN DW_CENTRAL.DIM_PRODUCT p on s.product_UID = p.PRODUCT_UID and p.RECORD_END_DATE = '9999-12-31'
        INNER JOIN vw_DSEConfigVersion_SCD_factors cf on s.seConfigId = cf.seConfigId
        and COALESCE(s.versionId,1) = cf.versionId
        and s.factorUID = cf.factorUID
        LEFT JOIN DW_RAW.RAW_Interaction i ON i.externalId = s.reportedInteractionUID
        LEFT JOIN DW_RAW.RAW_Interaction j ON j.externalId = s.inferredInteractionUID
    group by
    grouping sets (
        (suggestion_date, p.PRODUCT_ID, p.PRODUCT_NAME, action_type),
        (suggestion_date, action_type, USE_CASE_NAME),
        (suggestion_date, action_type, LABEL_TYPE_EXT_ID),
        (suggestion_date, p.PRODUCT_ID, p.PRODUCT_NAME, USE_CASE_NAME),
        (suggestion_date, p.PRODUCT_ID, p.PRODUCT_NAME, LABEL_TYPE_EXT_ID),
        (suggestion_date, p.PRODUCT_ID, p.PRODUCT_NAME),
        (suggestion_date, action_type),
        (suggestion_date, USE_CASE_NAME),
        (suggestion_date, LABEL_TYPE_EXT_ID),
        (suggestion_date)
    )
);

create
    or replace view SCD.VW_SCD_POST_PROCESS_OUTPUT_ACCOUNT_ARCHIVE AS (
        (select SCD_POST_PROCESS_OUTPUT_ARCH_KEY, 
                SCD_NAME, 
                uc.USE_CASE_NAME,
                po.REPORTING_LEVEL_NAME, 
                po.REPORTING_LEVEL_VALUE, 
                cast(m.DIM_BRICK_KEY as integer)  REPORTING_KEY, 
                cast(m.DIM_ACCOUNT_KEY as integer) ACCOUNT_KEY,
                m.ACCOUNT_UID ACCOUNT_UID, 
                pc.PRODUCT_NAME,
                pc.PRODUCT_UID,
                pc.PRODUCT_ID, 
                CURRENT_WEEK_DATE, 
                SCD_ACTUAL_VALUE, 
                SCD_PREDICTED_VALUE, 
                SCD_SCORE,
                CHANGE_DIRECTION,
                po.SCD_OUT_DATAPOINT_KEY,
                po.CREATED_TS, 
                po.UPDATED_TS
        from
            SCD_POST_PROCESS_OUTPUT_ARCHIVE po
            INNER JOIN DW_CENTRAL.F_BRICK_ACCOUNT_MAPPING m on po.reporting_level_value = m.brick_hco_code
            INNER JOIN SCD_OUT_DATAPOINT d on d.SCD_OUT_DATAPOINT_KEY = po.SCD_OUT_DATAPOINT_KEY
            INNER JOIN SCD_IN_PRODUCT_CONFIG pc on d.SCD_IN_PRODUCT_CONFIG_KEY = pc.SCD_IN_PRODUCT_CONFIG_KEY
            INNER JOIN SCD_IN_USE_CASE uc on uc.SCD_IN_USE_CASE_KEY = d.SCD_IN_USE_CASE_KEY
            INNER JOIN DW_CENTRAL.DIM_PRODUCT p on pc.DIM_PRODUCT_KEY = p.DIM_PRODUCT_KEY AND p.PRODUCT_ID > 0
            INNER JOIN DW_CENTRAL.DIM_ACCOUNT acc on m.ACCOUNT_UID = acc.ACCOUNT_UID and acc.ACCOUNT_ID > 0        
        where po.REPORTING_LEVEL_NAME = 'BRICK' and m.DW_DELETED_FLAG = 'FALSE' and SCD_SCORE > 0)
        UNION
        (select SCD_POST_PROCESS_OUTPUT_ARCH_KEY, 
                SCD_NAME, 
                uc.USE_CASE_NAME,
                po.REPORTING_LEVEL_NAME, 
                po.REPORTING_LEVEL_VALUE, 
                cast(m.HCO_ACCOUNT_KEY as integer) REPORTING_KEY, 
                cast(m.HCP_ACCOUNT_KEY as integer) ACCOUNT_KEY,
                m.HCP_ACCOUNT_UID  ACCOUNT_UID, 
                pc.PRODUCT_NAME, 
                pc.PRODUCT_UID,
                pc.PRODUCT_ID, 
                CURRENT_WEEK_DATE, 
                SCD_ACTUAL_VALUE, 
                SCD_PREDICTED_VALUE, 
                SCD_SCORE, 
                CHANGE_DIRECTION,
                po.SCD_OUT_DATAPOINT_KEY,
                po.CREATED_TS, 
                po.UPDATED_TS
        from
            SCD_POST_PROCESS_OUTPUT_ARCHIVE po
            INNER JOIN DW_CENTRAL.F_HCO_HCP_MAPPING m on po.reporting_level_value = m.hco_account_key
            INNER JOIN SCD_OUT_DATAPOINT d on d.SCD_OUT_DATAPOINT_KEY = po.SCD_OUT_DATAPOINT_KEY
            INNER JOIN SCD_IN_PRODUCT_CONFIG pc on d.SCD_IN_PRODUCT_CONFIG_KEY = pc.SCD_IN_PRODUCT_CONFIG_KEY
            INNER JOIN SCD_IN_USE_CASE uc on uc.SCD_IN_USE_CASE_KEY = d.SCD_IN_USE_CASE_KEY
            INNER JOIN DW_CENTRAL.DIM_PRODUCT p on pc.DIM_PRODUCT_KEY = p.DIM_PRODUCT_KEY AND p.PRODUCT_ID > 0
            INNER JOIN DW_CENTRAL.DIM_ACCOUNT acc on m.HCP_ACCOUNT_UID = acc.ACCOUNT_UID and acc.ACCOUNT_ID > 0        
        where po.REPORTING_LEVEL_NAME = 'HCO' and m.DW_DELETED_FLAG = 'FALSE' and SCD_SCORE > 0)  
        UNION
        (select SCD_POST_PROCESS_OUTPUT_ARCH_KEY, 
                SCD_NAME, 
                uc.USE_CASE_NAME,
                po.REPORTING_LEVEL_NAME, 
                po.REPORTING_LEVEL_VALUE, 
                cast(po.REPORTING_LEVEL_VALUE as integer) REPORTING_KEY, 
                cast(po.REPORTING_LEVEL_VALUE as integer) ACCOUNT_KEY,
                a.ACCOUNT_UID ACCOUNT_UID, 
                pc.PRODUCT_NAME, 
                pc.PRODUCT_UID,
                pc.PRODUCT_ID, 
                CURRENT_WEEK_DATE, 
                SCD_ACTUAL_VALUE, 
                SCD_PREDICTED_VALUE, 
                SCD_SCORE, 
                CHANGE_DIRECTION,
                po.SCD_OUT_DATAPOINT_KEY,
                po.CREATED_TS, 
                po.UPDATED_TS
        from
            SCD_POST_PROCESS_OUTPUT_ARCHIVE po
            INNER JOIN DW_CENTRAL.DIM_EXTERNAL_ACCOUNT a on reporting_level_value = a.SALES_REFERENCE_ACCOUNT_ID
            INNER JOIN SCD_OUT_DATAPOINT d on d.SCD_OUT_DATAPOINT_KEY = po.SCD_OUT_DATAPOINT_KEY
            INNER JOIN SCD_IN_PRODUCT_CONFIG pc on d.SCD_IN_PRODUCT_CONFIG_KEY = pc.SCD_IN_PRODUCT_CONFIG_KEY
            INNER JOIN SCD_IN_USE_CASE uc on uc.SCD_IN_USE_CASE_KEY = d.SCD_IN_USE_CASE_KEY
            INNER JOIN DW_CENTRAL.DIM_PRODUCT p on pc.DIM_PRODUCT_KEY = p.DIM_PRODUCT_KEY AND p.PRODUCT_ID > 0
            INNER JOIN DW_CENTRAL.DIM_ACCOUNT acc on a.ACCOUNT_UID = acc.ACCOUNT_UID and acc.ACCOUNT_ID > 0        
        where po.REPORTING_LEVEL_NAME = 'ACCOUNT' and SCD_SCORE > 0)
    );

create
or replace view VW_SCD_TRIGGERED_HCP_COUNT AS (
    select count(distinct ACCOUNT_UID) as HCP_count, 
           PRODUCT_ID,
           PRODUCT_NAME, 
           INITCAP(REPLACE(USE_CASE_NAME, '_', ' '), ' /_') AS USE_CASE_NAME, 
           SCD_NAME,
           DATEADD('DAY',5,DATE_TRUNC('WEEK',TO_DATE(updated_ts))) scd_trigger_date
    from
        SCD.vw_SCD_POST_PROCESS_OUTPUT_ACCOUNT_ARCHIVE
    group by grouping sets((scd_trigger_date, PRODUCT_ID, PRODUCT_NAME), 
                           (scd_trigger_date, PRODUCT_ID, PRODUCT_NAME, USE_CASE_NAME),
                           (scd_trigger_date, SCD_NAME), 
                           (scd_trigger_date, USE_CASE_NAME), 
                           (scd_trigger_date))
);

create
or replace view SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT as (
select suggestion_date,
           IFNULL(PRODUCT_ID, -1) PRODUCT_ID,
           IFNULL(PRODUCT_NAME,'All') PRODUCT_NAME, 
           IFNULL(ACTION_TYPE,'All') ACTION_TYPE, 
           IFNULL(USE_CASE_NAME,'All') USE_CASE_NAME, 
           IFNULL(LABEL_TYPE_EXT_ID,'All') LABEL_TYPE_EXT_ID, 
           HCP_count
           from SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT
);

create
or replace view SCD.VW_DSE_SUGGESTION_HCP_COUNT_RPT as (
select suggestion_date,
           IFNULL(PRODUCT_ID, -1) PRODUCT_ID,
           IFNULL(PRODUCT_NAME,'All') PRODUCT_NAME, 
           IFNULL(ACTION_TYPE,'All') ACTION_TYPE, 
           HCP_count
    from SCD.VW_DSE_SUGGESTION_HCP_COUNT
);

create
or replace view SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT as (
select HCP_count, 
           IFNULL(PRODUCT_ID, -1) PRODUCT_ID,
           IFNULL(PRODUCT_NAME,'All') PRODUCT_NAME, 
           IFNULL(USE_CASE_NAME, 'All') USE_CASE_NAME, 
           IFNULL(SCD_NAME, 'All') SCD_NAME,
           scd_trigger_date 
from VW_SCD_TRIGGERED_HCP_COUNT
);

-- cleaning up the range-names to bu useful in the visualization.  This needs to be added to migrations

delete from SCD.SCD_BI_CONTROL_RANGE;

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (1, 'SCD_SUGG_VS_ALL_SUGG', NULL, 5, 15, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (2, 'SCD_SUGG_PUBLISHED_VS_TRIGGERED', NULL, 20, 50, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (3, 'SCD_SUGG_ACTION_ACCEPTED/COMPLETE_VS_ALL', NULL, 20, 70, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (4, 'SCD_SUGG_ACTION_ACCEPTED/INCOMPLETE_VS_ALL', NULL, 20, 70, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (5, 'SCD_SUGG_ACTION_DISMISSED_VS_ALL', NULL, 10, 40, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO SCD.SCD_BI_CONTROL_RANGE (SCD_BI_CONTROL_RANGE_KEY, SCD_BI_CONTROL_RANGE_NAME, SCD_IN_PRODUCT_CONFIG_KEY, RANGE_MIN, RANGE_MAX, RANGE_UNITS, IS_DELETED, CREATED_TS, UPDATED_TS)
VALUES (6, 'SCD_SUGG_ACTION_NO ACTION TAKEN_VS_ALL', NULL, 10, 40, 'PERCENT', FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

commit;

create or replace view SCD.VW_SCD_BI_ACTION_TYPE_CONTROL_RANGE as (
select INITCAP(REPLACE(REPLACE(SCD_BI_CONTROL_RANGE_NAME,'_VS_ALL', ''),'SCD_SUGG_ACTION_', ''),' /_') SCD_BI_CONTROL_RANGE_NAME, RANGE_MIN, RANGE_MAX from SCD_BI_CONTROL_RANGE where SCD_BI_CONTROL_RANGE_NAME like '%ACTION%'
);

-- drop old SCD BI views created in V23 since they have been updated with new names above

drop view SCD.VW_SCD_PUB_SUGG;
drop view SCD.VW_SCD_TRIGGERED;
drop view SCD.VW_SCD_LABEL_SUGG;
drop view SCD.VW_FLATTEN_CONFIGJSON;
