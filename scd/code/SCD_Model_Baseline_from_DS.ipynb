{"cells": [{"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["## SCD 2.0 Build using Snowflake \n", "## Customer: <PERSON><PERSON>is <PERSON>\n", "## written by: <PERSON> \n", "## task: Translate R Prototype to Python\n", "## current date: 20 July 2020\n", "## updated date: 24 August 2020\n", "\n", "import snowflake.connector\n", "import snowflake as sf\n", "import pandas as pd\n", "import numpy as np\n", "import logging\n", "from statsmodels.distributions.empirical_distribution import ECDF\n", "from statsmodels.tsa.statespace.exponential_smoothing import ExponentialSmoothing\n", "\n", "# define logger\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')\n", "\n", "# snowflake parameters\n", "#username = 'amyb'\n", "#pwd = 'jdytg&0946'\n", "username = 'ml_admin'\n", "pwd = 'trsky&j0912ml'\n", "acct = 'aktanapartner'\n", "db = 'RPT_DWPREPROD_QA'\n", "schema = 'SCD'\n", "\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:snowflake.connector.connection:Snowflake Connector for Python Version: 2.2.9, Python Version: 3.7.7, Platform: Darwin-19.6.0-x86_64-i386-64bit\n", "INFO:snowflake.connector.connection:This connection is in OCSP Fail Open Mode. TLS Certificates would be checked for validity and revocation status. Any other Certificate Revocation related exceptions or OCSP Responder failures would be disregarded in favor of connectivity.\n", "INFO:snowflake.connector.cursor:query: [SELECT current_version()]\n", "INFO:snowflake.connector.cursor:query execution done\n", "INFO:snowflake.connector.connection:closed\n", "INFO:snowflake.connector.connection:Snowflake Connector for Python Version: 2.2.9, Python Version: 3.7.7, Platform: Darwin-19.6.0-x86_64-i386-64bit\n", "INFO:snowflake.connector.connection:This connection is in OCSP Fail Open Mode. TLS Certificates would be checked for validity and revocation status. Any other Certificate Revocation related exceptions or OCSP Responder failures would be disregarded in favor of connectivity.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["4.37.0\n"]}], "source": ["\n", "# validate snowflake connection and version\n", "con = sf.connector.connect(\n", "  user=username,\n", "  password=pwd,\n", "  account=acct,\n", "  database=db, \n", "  schema=schema\n", ")\n", "cur = con.cursor()\n", "try:\n", "    cur.execute(\"SELECT current_version()\")\n", "    one_row = cur.fetchone()\n", "    print(one_row[0])\n", "finally:\n", "    cur.close()\n", "con.close()\n", "\n", "# open connection to Snowflake\n", "con = sf.connector.connect(\n", "  user=username,\n", "  password=pwd,\n", "  account=acct,\n", "  database=db, \n", "  schema=schema\n", ")\n", "sfq = con.cursor()\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["\n", "### FUNCTIONS to ingest data and supporting UI parameters ###\n", "# bring in SCD_OUT tables from snowflake with parameters selected from the UI \n", "def ingest_scd_tables():\n", "    query = \"\"\"SELECT a.*, 1 change_direction,\n", "            a.scd_in_use_case_key use_case_name_key,\n", "            a.market_basket_name marketbasket_name,\n", "            e.dim_frequency_key frequency_key,\n", "            a.scd_in_reporting_level_key reporting_level_key,\n", "            e.target_view_name \n", "            from SCD_OUT_DATAPOINT a \n", "            left join SCD_IN_USE_CASE b \n", "            on a.scd_in_use_case_key = b.scd_in_use_case_key \n", "            left join SCD_IN_PRODUCT_CONFIG c \n", "            on a.SCD_IN_PRODUCT_CONFIG_KEY=c.SCD_IN_PRODUCT_CONFIG_KEY \n", "            left join SCD_IN_TARGET_VIEW_MAPPING e \n", "            on b.scd_in_use_case_key=e.scd_in_use_case_key\n", "            and a.dim_frequency_key = e.dim_frequency_key\n", "            and a.scd_in_reporting_level_key = e.scd_in_reporting_level_key\n", "            and e.is_market_calc_custom = false\n", "            where scd_out_datapoint_key = 11\n", "            \"\"\"\n", "    sfq.execute(query)\n", "    data = sfq.fetchall()\n", "    column_names = [i[0] for i in sfq.description]\n", "    df = pd.DataFrame(data, columns = column_names)\n", "    df.columns = map(str.lower, df.columns)\n", "    logger.info(f'Dimensions for data ingested from %s = ({df.shape[0]}, {df.shape[1]})' % df)\n", "    return(df)\n", "\n", "# bring in use case specific parameters \n", "def get_parameters(df, freq_key, use_case_name_key, level):\n", "    period_number = df[df['use_case_name_key']==use_case_name_key].period_number.to_list()[-1]\n", "    metric_name = df[df['use_case_name_key']==use_case_name_key].metric_name.iloc[-1]\n", "    product_name = df[df['use_case_name_key']==use_case_name_key].product_name.iloc[-1]\n", "    marketbasket_name = df[df['use_case_name_key']==use_case_name_key].marketbasket_name.iloc[-1]\n", "    change_direction = df[df['use_case_name_key']==use_case_name_key].change_direction.iloc[-1]\n", "    target_view_name = df[(df.frequency_key==freq_key) & (df.use_case_name_key==use_case_name_key) & (df.reporting_level_key==level)].target_view_name.iloc[-1]  \n", "    return(period_number, metric_name, product_name, marketbasket_name, change_direction, target_view_name)\n", "\n", "# bring in views from snowflake \n", "def ingest_scd_views(target_view_name, period_number, metric_name, product_name):\n", "    if target_view_name.find('GAP') != -1:\n", "        query = f\"SELECT * from {target_view_name} where metric_name = '{metric_name}' and product_name = '{product_name}'\" \n", "        #logger.info(f'Query********= {query}')\n", "        sfq.execute(query)\n", "        data = sfq.fetchall()\n", "        column_names = [i[0] for i in sfq.description]\n", "        df = pd.DataFrame(data, columns = column_names)\n", "        df.columns = map(str.lower, df.columns)\n", "        logger.info(f'Dimensions for data ingested from {target_view_name} = ({df.shape[0]}, {df.shape[1]})')\n", "        return(df)      \n", "    else:\n", "        query = f\"SELECT * from {target_view_name} where PERIOD_NUMBER = {period_number} and metric_name = '{metric_name}' and product_name = '{product_name}'\" \n", "        #logger.info(f'Query********= {query}')\n", "        sfq.execute(query)\n", "        data = sfq.fetchall()\n", "        column_names = [i[0] for i in sfq.description]\n", "        df = pd.DataFrame(data, columns = column_names)\n", "        df.columns = map(str.lower, df.columns)\n", "        logger.info(f'Dimensions for data ingested from {target_view_name} = ({df.shape[0]}, {df.shape[1]})')\n", "        return(df)\n", "\n", "### FUNCTIONS to prepare data for modeling ###\n", "# find first and final purchase dates by reporting_level_name \n", "def first_final_purchase_date(df):\n", "\n", "    # initialize first and final purchase as df subsets\n", "    first_purchase_date = []\n", "    final_purchase_date = []\n", "\n", "    # create subsets of the data and add columns back to original data\n", "    temp_filter = df[df[target]>0]\n", "\n", "    first_purchase_date = pd.DataFrame(temp_filter.groupby([reporting_level_name])['sale_date'].min())\n", "    first_purchase_date = first_purchase_date.rename(columns={'sale_date':'first_delivery'})\n", "\n", "    final_purchase_date = pd.DataFrame(temp_filter.groupby([reporting_level_name])['sale_date'].max())\n", "    final_purchase_date = final_purchase_date.rename(columns={'sale_date':'final_delivery'})\n", "\n", "    output = pd.merge(df, first_purchase_date, on=reporting_level_name, how='left')\n", "    output = pd.merge(output, final_purchase_date, on=reporting_level_name, how='left')\n", "\n", "    return(output)\n", "\n", "# make sure each reporting level has at least one purchase in the last year and at least six months of history\n", "def purchase_timing_requirement(df):\n", "    \n", "    year_prior_date = df['sale_date'].max() - pd.to_timedelta(365, unit='d')\n", "    \n", "    # require at least one purchase within the last year\n", "    df = df[df.final_delivery >= year_prior_date]\n", "\n", "    # require at least six months total duration of purchase history per reporting_level_name\n", "    df.insert(len(df.columns)-1, \"time_flag\", df.final_delivery - df.first_delivery, True) \n", "    df = df[df.loc[:,'time_flag'] >= '180 days']\n", "\n", "    return(df)\n", "\n", "# make sure each reporting level has enough purchase history for the model\n", "def purchase_count_requirement(df, count_threshold):\n", "\n", "    # initialize empty dataframe to hold datasubset\n", "    nonzero_counts = []    \n", "\n", "    # find non zero counts per reporting_level_name\n", "    if target_view_name.find('GAP') == -1:\n", "        nonzero_counts = df[df[target]!=0]\n", "    else:\n", "        nonzero_counts = df[df.sales_metric_value!=0]\n", "\n", "    temp = pd.DataFrame(nonzero_counts.groupby([reporting_level_name], as_index=False)[target].count())\n", "    temp = temp.rename(columns={target:'purchase_count'})\n", "\n", "    nonzero_counts_merge = pd.merge(df, temp, on=reporting_level_name, how='left')\n", "\n", "    # criteria 3 subset to reporting_level_name with at least the required number of counts\n", "    counts = nonzero_counts_merge[nonzero_counts_merge['purchase_count'] >= count_threshold]\n", "\n", "    return(counts)\n", "\n", "### FUNCTIONS to model, score, and create data points and metrics ###\n", "# fit a time series model to volume and share data and score for outliers\n", "def anomaly_detect(df, alpha_up, alpha_down):\n", "  \n", "  # set up time series model where x is the historic vector of sales values for one reference type\n", "  x = df[target].astype(float).tolist()\n", "      \n", "  min_date = df['sale_date'].min()\n", "  series_date = pd.date_range(min_date, periods=len(x), freq='W-SAT')\n", "  data = pd.Series(x, series_date)\n", "\n", "  #  build model\n", "  model = ExponentialSmoothing(data, trend=True, seasonal=4)\n", "  results = model.fit(low_memory=True, method='powell', disp=0)\n", "\n", "  # build prediciton data frame\n", "  pred = pd.DataFrame(df.loc[:,[reporting_level_name,'sale_date']])\n", "  pred['Predicted'] = np.array(results.fittedvalues)\n", "  pred['Observed'] = x\n", "  pred['resid'] = np.array(results.resid)\n", "  pred['lower'] = results.resid.quantile(q=.25)\n", "  pred['upper'] = results.resid.quantile(q=.75)\n", "  pred['iqr'] = pred['upper'] - pred['lower']\n", "  \n", "  # find most recent date for output\n", "  prob = pred.tail(1)\n", "  \n", "  # control minimum predictions \n", "  check = prob['Predicted'] < 0\n", "  if check.bool():\n", "      prob['Predicted'] = 0\n", "      \n", "  # score for Up use cases \n", "  check  = (prob['resid'] > prob['upper'])\n", "  if check.bool():\n", "      prob.loc[:,'SCD_up'] = 1 - ((alpha_up * prob['iqr'] ) / (prob['resid'] - prob['upper']))\n", "  else:\n", "      prob.loc[:,'SCD_up'] = 0  \n", "  \n", "  # score for Down use cases \n", "  check  = (prob['resid'] < prob['lower'])\n", "  if check.bool():\n", "      prob.loc[:,'SCD_down'] = 1 + ((alpha_down * prob['iqr'] ) / (prob['resid'] - prob['lower']))\n", "  else:\n", "      prob.loc[:,'SCD_down'] = 0 \n", "                             \n", "  prob = prob.loc[:,[reporting_level_name,'sale_date','Observed','Predicted','SCD_up','SCD_down']]\n", "    \n", "  return(prob)\n", "\n", "# control minimum and maximum scores and predictions where appropriate\n", "def control_scores(df):\n", "   \n", "    # control minimum scores\n", "    check = df['SCD_up'] < 0\n", "    if check.bool():\n", "        df['SCD_up'] = 0 \n", "  \n", "    check = df['SCD_down'] < 0\n", "    if check.bool():\n", "        df['SCD_down'] = 0  \n", "      \n", "    # control maximum scores\n", "    check = df['SCD_up'] >= 100\n", "    if check.bool():\n", "        df['SCD_up'] = 99.99\n", "  \n", "    check = df['SCD_down'] >= 100\n", "    if check.bool():\n", "        df['SCD_down'] = 99.99  \n", "    return(df)\n", "\n", "# create data points from scored model\n", "def create_data_points(df):\n", " \n", "    # create column for scd_name   \n", "    up = [\"SCD_\",product_name.title(),\"_\",target_view_name.split(\"_\", 3)[-1].title(),\"_\",metric_name,\"_Increasing_\",str(period_number),\"w\"]\n", "    up = \"\".join(up)\n", "\n", "    down = [\"SCD_\",product_name.title().lower(),\"_\",target_view_name.split(\"_\", 3)[-1].title(),\"_\",metric_name,\"_Decreasing_\",str(period_number),\"w\"]\n", "    down = \"\".join(down)\n", "\n", "    mapping = {df.columns[4]:up, df.columns[5]:down}\n", "    df.rename(columns=mapping, inplace=True)    \n", "\n", "    # pull data long before appending\n", "    score = pd.melt(df, id_vars=[reporting_level_name,'sale_date','Observed','Predicted'], \n", "                    value_vars=[up,down],\n", "                    var_name='scd_name', value_name='Score')\n", "\n", "    score.Score = round(score.Score*100,2)\n", "    score['product_name'] = product_name\n", "    return(score)\n", "\n", "# model for order gap use case\n", "def gap_detect(df):\n", "\n", "  # initialize empty data frame to incorporate output for probability of increased gap\n", "  prob_up = pd.DataFrame(columns=[reporting_level_name,'sale_date','Observed','Predicted','scd_name','Score','product_name'])\n", "  \n", "  # for Order Gap increase lastDiff is the number of weeks since the last order\n", "  lastDiff = df[target].iloc[-1]\n", "  \n", "  # estimate the ecdf function fn() using order_gap_week by creating vector of purchase times\n", "  x = df[df.sales_metric_value!=0][target][1:len(df[df.sales_metric_value!=0])]\n", "  fn = ECDF(x)\n", "  \n", "  # probability of an observation in an interval in the fn() ecdf\n", "  prob_up = pd.DataFrame(df.loc[:,[reporting_level_name,'sale_date']]).iloc[[-1]]\n", "  prob_up['Observed'] = lastDiff\n", "  prob_up['Predicted']= np.array(x.quantile())\n", "  sub_string = \"SCD_\",product_name.title(),\"_\",target_view_name.split(\"_\", 3)[-1].title(),\"_\",metric_name,\"_Increasing_\",str(period_number),\"w\"\n", "  prob_up['scd_name'] = \"\".join(sub_string)\n", "  prob_up['Score'] = fn(lastDiff).round(4)*100\n", "  prob_up['product_name'] = product_name\n", "  \n", "  # adjust for rounding issues\n", "  check  = (prob_up['Observed'] == prob_up['Predicted'])\n", "  if check.bool():\n", "      prob_up['Score'] = 0\n", "  elif check.bool(): \n", "      prob_up['Score'] = prob_up['Score']\n", "      \n", " # adjust for too much confidence\n", "  check = (prob_up['Score'] == 100)\n", "  if check.bool():\n", "      prob_up['Score'] = 99\n", "  elif check.bool(): \n", "      prob_up['Score'] = prob_up['Score']    \n", "  \n", "  # initialize empty data frame to incorporate output for probability of decreased gap\n", "  prob_down = pd.DataFrame(columns=[reporting_level_name,'sale_date','Observed','Predicted','scd_name','Score','product_name'])\n", "  \n", "  # for Order Gap decrease lastDiff is the number of weeks between the last two orders\n", "  last_order_gap = df[target][df.sales_metric_value!=0].iloc[-1]\n", "   \n", "  # Fit the data to survival model\n", "  x = df[df.sales_metric_value!=0][target][1:len(df[df.sales_metric_value!=0])]\n", "  \n", "  # probability of an observation in an interval for the complementary ECDF\n", "  prob_down = pd.DataFrame(df.loc[:,[reporting_level_name,'sale_date']]).iloc[[-1]]\n", "  prob_down['Observed'] = last_order_gap\n", "  prob_down['Predicted']= np.array(x.quantile())\n", "  sub_string = \"SCD_\",product_name.title(),\"_\",target_view_name.split(\"_\", 3)[-1].title(),\"_\",metric_name,\"_Decreasing_\",str(period_number),\"w\"\n", "  prob_down['scd_name'] = \"\".join(sub_string)\n", "  prob_down['Score'] = (1-fn(last_order_gap)).round(4)*100\n", "  prob_down['product_name'] = product_name\n", "  \n", "  # adjust for rounding issues\n", "  check  = (prob_down['Observed'] == prob_down['Predicted'])\n", "  if check.bool():\n", "      prob_down['Score'] = 0\n", "  elif check.bool(): \n", "      prob_down['Score'] = prob_down['Score']\n", "      \n", " # adjust for too much confidence\n", "  check  = (prob_down['Score'] == 100)\n", "  if check.bool():\n", "      prob_down['Score'] = 99\n", "  elif check.bool(): \n", "      prob_down['Score'] = prob_down['Score']  \n", "  \n", "  # combine up and down use cases\n", "  prob = pd.concat([prob_up, prob_down])\n", "  \n", "  return(prob)\n", "\n", "# create table of HCP proportions (output metrics) \n", "def create_metrics(df):\n", " \n", "  # data for anomalies up\n", "  x = df[df['scd_name']==df['scd_name'].unique()[0]]['Score']\n", "  \n", "  # table for anomalies up\n", "  df_up = ()\n", "  df_up = pd.DataFrame({'sale_date':df['sale_date'].unique().repeat(5),\n", "                        'use_case':df['scd_name'].unique()[0],\n", "                        'percent':[(x[x>=75].count() / x.count()*100).round(0),\n", "                                   (x[x>=80].count() / x.count()*100).round(0),\n", "                                   (x[x>=85].count() / x.count()*100).round(0),\n", "                                   (x[x>=90].count() / x.count()*100).round(0),\n", "                                   (x[x>=95].count() / x.count()*100).round(0)],\n", "                        'threshold':[.75,.80,.85,.90,.95],\n", "                        'count':len(df[reporting_level_name].unique())\n", "                       })\n", "\n", "  # data for anomalies down\n", "  #if target_view_name.find('GAP') == -1:\n", "  y = df[df['scd_name']==df['scd_name'].unique()[1]]['Score']\n", "  \n", "  # table for anomalies down\n", "  df_down = ()\n", "  df_down = pd.DataFrame({'sale_date':df['sale_date'].unique().repeat(5),\n", "                          'use_case':df['scd_name'].unique()[1],\n", "                          'percent':[(y[y>=75].count() / y.count()*100).round(0),\n", "                                     (y[y>=80].count() / y.count()*100).round(0),\n", "                                     (y[y>=85].count() / y.count()*100).round(0),\n", "                                     (y[y>=90].count() / y.count()*100).round(0),\n", "                                     (y[y>=95].count() / y.count()*100).round(0)],\n", "                            'threshold':[.75,.80,.85,.90,.95],\n", "                            'count':len(df[reporting_level_name].unique())\n", "                          })\n", "      # combine up and down for full use case metrics\n", "  df =  pd.concat([df_up, df_down])\n", "  #else :\n", "  #    df =  df_up\n", " \n", "  return(df)\n", "   "]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:snowflake.connector.cursor:query: [SELECT a.*, 1 change_direction, a.scd_in_use_case_key use_case_name_key, a.marke...]\n", "INFO:snowflake.connector.cursor:query execution done\n", "INFO:snowflake.connector.arrow_result:fetching data done\n", "INFO:__main__:Dimensions for data ingested from    scd_out_datapoint_key  scd_in_product_config_key  dim_metric_key  \\\n", "0                     11                          4               4   \n", "\n", "   dim_frequency_key dim_marketbasket_key  scd_in_use_case_key  \\\n", "0                  3                 None                    2   \n", "\n", "   scd_in_reporting_level_key        product_name metric_name frequency_name  \\\n", "0                           1  **POTE************       Units         WEEKLY   \n", "\n", "   ... reason_text  is_deleted              created_ts updated_ts  \\\n", "0  ...        None       False 2020-11-05 17:14:42.417       None   \n", "\n", "  change_direction  use_case_name_key marketbasket_name  frequency_key  \\\n", "0                1                  2              None              3   \n", "\n", "  reporting_level_key                     target_view_name  \n", "0                   1  vw_BRICK_WEEKLY_ORDER_GAP_DETECTION  \n", "\n", "[1 rows x 26 columns] = (1, 26)\n"]}], "source": ["## Bring in SCD_OUT table with required parameters ---------------------------------------------\n", "scd_out_param = ingest_scd_tables()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "## Part 1: Sales Volume Change Use Case ---------------------------------------------\n", "# get parameters for volume use case\n", "# freq_key 3 = weekly; use_case_name_key 1 = sales volume view; reporting_level_key 1 = brick level\n", "### These tables are populated ONLY FOR NOVARTIS AUSTRALIA as of 07-15-2020 other clients will need these parameters manually assigned ### \n", "period_number, metric_name, product_name, marketbasket_name, change_direction, target_view_name = get_parameters(scd_out_param, freq_key=3, use_case_name_key=1, level=1)  \n", "\n", "# ingest data from views after assigning parameters\n", "vol = ingest_scd_views(target_view_name, period_number, metric_name, product_name)\n", "\n", "# assign final parameters for use case\n", "if target_view_name.find('BRICK') != -1:  # use brick_hco_code for BRICK\n", "    reporting_level_name = vol.columns[0]\n", "else:\n", "    reporting_level_name = vol.columns[3]  # use sales_reference_account_id for ACCOUNT\n", "target = vol.columns[-1]\n", "\n", "# convert sale_date to date\n", "vol['sale_date'] = pd.to_datetime(vol['sale_date'], format='%Y-%m-%d')\n", "\n", "# call function to create columns for first and final deliveries \n", "vol_first_final = first_final_purchase_date(vol)\n", "\n", "# cutoff values by reporting_level_name to ensure no values are counted in the model prior to the first purchase\n", "vol_first_final = vol_first_final[vol_first_final['sale_date'] >= vol_first_final['first_delivery']]\n", "\n", "# call function to subset data according to purchase timing requirements\n", "vol_time_flag = purchase_timing_requirement(vol_first_final)\n", "\n", "# call function to subset data according to minimum count requirement\n", "vol_clean = purchase_count_requirement(vol_time_flag, 40)\n", "\n", "# clean columns before model\n", "vol_clean = vol_clean.loc[:,[reporting_level_name,'product_name','sale_date',target]]\n", "\n", "# max_level = number of bricks_hco_codes feeding into the model\n", "max_level = len(vol_clean[reporting_level_name].unique())  \n", "\n", "# call the model\n", "vol_model = vol_clean.groupby(reporting_level_name).apply(anomaly_detect, .25, .15)  # higher alpha = lower metric percentages \n", "logger.info(f'Dimensions for data ingested from volume model = ({vol_model.shape[0]}, {vol_model.shape[1]})')\n", "\n", "# reset index\n", "vol_model.reset_index(drop=True, inplace=True)\n", "\n", "# adjust for negative scores\n", "vol_model_control = vol_model.groupby(reporting_level_name).apply(control_scores)\n", "\n", "# create data points for the model\n", "vol_score  = create_data_points(vol_model_control)\n", "\n", "# create metrics for BI output\n", "vol_metric = create_metrics(vol_score)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:snowflake.connector.cursor:query: [SELECT * from vw_BRICK_WEEKLY_ORDER_GAP_DETECTION where metric_name = 'Units' an...]\n", "INFO:snowflake.connector.cursor:query execution done\n", "INFO:snowflake.connector.arrow_result:fetching data done\n", "INFO:__main__:Dimensions for data ingested from vw_BRICK_WEEKLY_ORDER_GAP_DETECTION = (53612, 9)\n"]}], "source": ["## Part 2: Order Gap Detectiom Use Case ------------------------------------------------------\n", "# pass parameters from the UI into use case\n", "### These tables are populated ONLY FOR NOVARTIS AUSTRALIA as of 07-15-2020 other clients will need these parameters manually assigned ### \n", "#freq_key 3 = weekly; use_case_number 2 = gap view; reporting_level_key 1 = brick level\n", "period_number, metric_name, product_name, marketbasket_name, change_direction, target_view_name = get_parameters(scd_out_param, freq_key=3, use_case_name_key=2, level=1)  \n", "\n", "#period_number = 1\n", "#metric_name = 'Units' \n", "#product_name = '**NTRE****' \n", "#target_view_name = 'VW_BRICK_WEEKLY_ORDER_GAP_DETECTION'\n", "\n", "# ingest data from views\n", "gap = ingest_scd_views(target_view_name, period_number, metric_name, product_name)\n", "\n", "# set parameters based on use case\n", "if target_view_name.find('BRICK') != -1:  # use brick_hco_code for BRICK\n", "    reporting_level_name = gap.columns[0]\n", "else:\n", "    reporting_level_name = gap.columns[3]  # use sales_reference_account_id for ACCOUNT\n", "target = gap.columns[-1]\n", "\n", "# convert sale_date to date\n", "gap['sale_date'] = pd.to_datetime(gap['sale_date'], format='%Y-%m-%d')\n", "\n", "# keep only necessary rows and columns\n", "gap = gap.loc[ :,[reporting_level_name,'product_name','sale_date','sales_metric_value',target]]"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>brick_hco_code</th>\n", "      <th>product_name</th>\n", "      <th>sale_date</th>\n", "      <th>sales_metric_value</th>\n", "      <th>order_gap_week</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00000</td>\n", "      <td>**POTE************</td>\n", "      <td>2019-01-05</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00000</td>\n", "      <td>**POTE************</td>\n", "      <td>2019-01-12</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>00000</td>\n", "      <td>**POTE************</td>\n", "      <td>2019-01-19</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>00000</td>\n", "      <td>**POTE************</td>\n", "      <td>2019-01-26</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00000</td>\n", "      <td>**POTE************</td>\n", "      <td>2019-02-02</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53607</th>\n", "      <td>99994</td>\n", "      <td>**POTE************</td>\n", "      <td>2020-02-08</td>\n", "      <td>120.000000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53608</th>\n", "      <td>99994</td>\n", "      <td>**POTE************</td>\n", "      <td>2020-02-15</td>\n", "      <td>5.000000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53609</th>\n", "      <td>99994</td>\n", "      <td>**POTE************</td>\n", "      <td>2020-02-22</td>\n", "      <td>7.000000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53610</th>\n", "      <td>99994</td>\n", "      <td>**POTE************</td>\n", "      <td>2020-02-29</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53611</th>\n", "      <td>99994</td>\n", "      <td>**POTE************</td>\n", "      <td>2020-03-07</td>\n", "      <td>63.000000</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>53612 rows × 5 columns</p>\n", "</div>"], "text/plain": ["      brick_hco_code        product_name  sale_date sales_metric_value  \\\n", "0              00000  **POTE************ 2019-01-05           0.000000   \n", "1              00000  **POTE************ 2019-01-12           0.000000   \n", "2              00000  **POTE************ 2019-01-19           0.000000   \n", "3              00000  **POTE************ 2019-01-26           0.000000   \n", "4              00000  **POTE************ 2019-02-02           0.000000   \n", "...              ...                 ...        ...                ...   \n", "53607          99994  **POTE************ 2020-02-08         120.000000   \n", "53608          99994  **POTE************ 2020-02-15           5.000000   \n", "53609          99994  **POTE************ 2020-02-22           7.000000   \n", "53610          99994  **POTE************ 2020-02-29           0.000000   \n", "53611          99994  **POTE************ 2020-03-07          63.000000   \n", "\n", "       order_gap_week  \n", "0                 NaN  \n", "1                 NaN  \n", "2                 NaN  \n", "3                 NaN  \n", "4                 NaN  \n", "...               ...  \n", "53607             2.0  \n", "53608             1.0  \n", "53609             1.0  \n", "53610             1.0  \n", "53611             2.0  \n", "\n", "[53612 rows x 5 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["gap"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["# call function to create columns for first and final deliveries \n", "gap_first_final = first_final_purchase_date(gap)"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>brick_hco_code</th>\n", "      <th>product_name</th>\n", "      <th>sale_date</th>\n", "      <th>sales_metric_value</th>\n", "      <th>order_gap_week</th>\n", "      <th>first_delivery</th>\n", "      <th>final_delivery</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00000</td>\n", "      <td>**POTE************</td>\n", "      <td>2019-01-05</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>2019-12-07</td>\n", "      <td>2020-03-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00000</td>\n", "      <td>**POTE************</td>\n", "      <td>2019-01-12</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>2019-12-07</td>\n", "      <td>2020-03-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>00000</td>\n", "      <td>**POTE************</td>\n", "      <td>2019-01-19</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>2019-12-07</td>\n", "      <td>2020-03-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>00000</td>\n", "      <td>**POTE************</td>\n", "      <td>2019-01-26</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>2019-12-07</td>\n", "      <td>2020-03-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00000</td>\n", "      <td>**POTE************</td>\n", "      <td>2019-02-02</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>2019-12-07</td>\n", "      <td>2020-03-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53607</th>\n", "      <td>99994</td>\n", "      <td>**POTE************</td>\n", "      <td>2020-02-08</td>\n", "      <td>120.000000</td>\n", "      <td>2.0</td>\n", "      <td>2019-12-14</td>\n", "      <td>2020-03-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53608</th>\n", "      <td>99994</td>\n", "      <td>**POTE************</td>\n", "      <td>2020-02-15</td>\n", "      <td>5.000000</td>\n", "      <td>1.0</td>\n", "      <td>2019-12-14</td>\n", "      <td>2020-03-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53609</th>\n", "      <td>99994</td>\n", "      <td>**POTE************</td>\n", "      <td>2020-02-22</td>\n", "      <td>7.000000</td>\n", "      <td>1.0</td>\n", "      <td>2019-12-14</td>\n", "      <td>2020-03-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53610</th>\n", "      <td>99994</td>\n", "      <td>**POTE************</td>\n", "      <td>2020-02-29</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>2019-12-14</td>\n", "      <td>2020-03-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53611</th>\n", "      <td>99994</td>\n", "      <td>**POTE************</td>\n", "      <td>2020-03-07</td>\n", "      <td>63.000000</td>\n", "      <td>2.0</td>\n", "      <td>2019-12-14</td>\n", "      <td>2020-03-07</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>53612 rows × 7 columns</p>\n", "</div>"], "text/plain": ["      brick_hco_code        product_name  sale_date sales_metric_value  \\\n", "0              00000  **POTE************ 2019-01-05           0.000000   \n", "1              00000  **POTE************ 2019-01-12           0.000000   \n", "2              00000  **POTE************ 2019-01-19           0.000000   \n", "3              00000  **POTE************ 2019-01-26           0.000000   \n", "4              00000  **POTE************ 2019-02-02           0.000000   \n", "...              ...                 ...        ...                ...   \n", "53607          99994  **POTE************ 2020-02-08         120.000000   \n", "53608          99994  **POTE************ 2020-02-15           5.000000   \n", "53609          99994  **POTE************ 2020-02-22           7.000000   \n", "53610          99994  **POTE************ 2020-02-29           0.000000   \n", "53611          99994  **POTE************ 2020-03-07          63.000000   \n", "\n", "       order_gap_week first_delivery final_delivery  \n", "0                 NaN     2019-12-07     2020-03-07  \n", "1                 NaN     2019-12-07     2020-03-07  \n", "2                 NaN     2019-12-07     2020-03-07  \n", "3                 NaN     2019-12-07     2020-03-07  \n", "4                 NaN     2019-12-07     2020-03-07  \n", "...               ...            ...            ...  \n", "53607             2.0     2019-12-14     2020-03-07  \n", "53608             1.0     2019-12-14     2020-03-07  \n", "53609             1.0     2019-12-14     2020-03-07  \n", "53610             1.0     2019-12-14     2020-03-07  \n", "53611             2.0     2019-12-14     2020-03-07  \n", "\n", "[53612 rows x 7 columns]"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["gap_first_final"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["# cutoff values by level to ensure no values are counted in the model prior to the first purchase\n", "gap_first_final2 = gap_first_final[gap_first_final.order_gap_week.notnull()]"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["(      brick_hco_code        product_name  sale_date sales_metric_value  \\\n", " 47             00000  **POTE************ 2019-11-30          35.000000   \n", " 48             00000  **POTE************ 2019-12-07          58.000000   \n", " 49             00000  **POTE************ 2019-12-14          52.000000   \n", " 50             00000  **POTE************ 2019-12-21          51.000000   \n", " 51             00000  **POTE************ 2019-12-28          39.000000   \n", " ...              ...                 ...        ...                ...   \n", " 53607          99994  **POTE************ 2020-02-08         120.000000   \n", " 53608          99994  **POTE************ 2020-02-15           5.000000   \n", " 53609          99994  **POTE************ 2020-02-22           7.000000   \n", " 53610          99994  **POTE************ 2020-02-29           0.000000   \n", " 53611          99994  **POTE************ 2020-03-07          63.000000   \n", " \n", "        order_gap_week first_delivery final_delivery  \n", " 47                0.0     2019-12-07     2020-03-07  \n", " 48                1.0     2019-12-07     2020-03-07  \n", " 49                1.0     2019-12-07     2020-03-07  \n", " 50                1.0     2019-12-07     2020-03-07  \n", " 51                1.0     2019-12-07     2020-03-07  \n", " ...               ...            ...            ...  \n", " 53607             2.0     2019-12-14     2020-03-07  \n", " 53608             1.0     2019-12-14     2020-03-07  \n", " 53609             1.0     2019-12-14     2020-03-07  \n", " 53610             1.0     2019-12-14     2020-03-07  \n", " 53611             2.0     2019-12-14     2020-03-07  \n", " \n", " [12695 rows x 7 columns],\n", " 47      91 days\n", " 48      91 days\n", " 49      91 days\n", " 50      91 days\n", " 51      91 days\n", "           ...  \n", " 53607   84 days\n", " 53608   84 days\n", " 53609   84 days\n", " 53610   84 days\n", " 53611   84 days\n", " Length: 12695, dtype: timedelta64[ns])"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["gap_first_final2, gap_first_final2.final_delivery - gap_first_final2.first_delivery"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["# call function to subset data according to purchase requirements\n", "gap_time_flag = purchase_timing_requirement(gap_first_final2)\n", "         "]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>brick_hco_code</th>\n", "      <th>product_name</th>\n", "      <th>sale_date</th>\n", "      <th>sales_metric_value</th>\n", "      <th>order_gap_week</th>\n", "      <th>first_delivery</th>\n", "      <th>time_flag</th>\n", "      <th>final_delivery</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [brick_hco_code, product_name, sale_date, sales_metric_value, order_gap_week, first_delivery, time_flag, final_delivery]\n", "Index: []"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["gap_time_flag"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": [" \n", "# call function to subset data accirding to use case requirements\n", "gap_clean = purchase_count_requirement(gap_time_flag, 10)\n", "      "]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["         \n", "# capture results by reproting level \n", "max_level = len(gap_clean[reporting_level_name].unique()) # total number of levels for use case\n", "\n", "# call model for order gap use cases\n", "gap_model = gap_clean.groupby(reporting_level_name).apply(gap_detect)\n", "logger.info(f'Dimensions for data ingested from gap model = ({gap_model.shape[0]}, {gap_model.shape[1]})')\n", "\n", "# create metrics for BI output\n", "gap_metric = create_metrics(gap_model)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "## Part 3: Market Basket Volume Use Case ---------------------------------------------\n", "# pass parameters from the UI into use case\n", "### These tables are populated ONLY FOR NOVARTIS AUSTRALIA as of 07-15-2020 other clients will need these parameters manually assigned ### \n", "# freq_key 3 = weekly; use_case_number 3 = marketbasket view; reporting_level_key 1 = brick level\n", "period_number, metric_name, product_name, marketbasket_name, change_direction, target_view_name = get_parameters(scd_out_param, freq_key=3, use_case_name_key=3, level=1)  \n", "\n", "# ingest data from views\n", "mktbskt = ingest_scd_views(target_view_name, period_number, metric_name, product_name)  \n", "\n", "# set parameters for use case\n", "if target_view_name.find('BRICK') != -1:  # use brick_hco_code for BRICK\n", "    reporting_level_name = mktbskt.columns[0]\n", "else:\n", "    reporting_level_name = mktbskt.columns[3]  # use sales_reference_account_id for ACCOUNT\n", "target = mktbskt.columns[-1]\n", "\n", "# convert sale_date to date\n", "mktbskt['sale_date'] = pd.to_datetime(mktbskt['sale_date'], format='%Y-%m-%d')\n", "\n", "# Important for MKTBSKT and MKT_SHARE subset data to market basket of interest  \n", "mktbskt = mktbskt[mktbskt.marketbasket_name==marketbasket_name]\n", "\n", "# call function to create columns for first and final deliveries  \n", "mktbskt_first_final = first_final_purchase_date(mktbskt)\n", "\n", "# cutoff values by reporting_level_name to ensure no values are counted in the model prior to the first purchase\n", "mktbskt_first_final = mktbskt_first_final[mktbskt_first_final['sale_date'] >= mktbskt_first_final['first_delivery']]\n", "\n", "# call function to subset data according to purchase requirements\n", "mktbskt_time_flag = purchase_timing_requirement(mktbskt_first_final)\n", "\n", "# call function to subset data according to minimum count requirements\n", "mktbskt_clean = purchase_count_requirement(mktbskt_time_flag, 40)\n", " \n", "# clean columns before model\n", "mktbskt_clean = mktbskt_clean.loc[:,[reporting_level_name,'product_name','sale_date',target]]\n", "\n", "# max_level = number of bricks_hco_codes feeding into the model\n", "max_level = len(mktbskt_clean[reporting_level_name].unique())  \n", "\n", "# call the model\n", "mktbskt_model = mktbskt_clean.groupby(reporting_level_name).apply(anomaly_detect, .25, .05)  # higher alpha = lower metric percentages \n", "logger.info(f'Dimensions for data ingested from market basket model = ({mktbskt_model.shape[0]}, {mktbskt_model.shape[1]})')\n", "\n", "# reset index\n", "mktbskt_model.reset_index(drop=True, inplace=True)\n", "\n", "# adjust for negative scores\n", "mktbskt_model_control = mktbskt_model.groupby(reporting_level_name).apply(control_scores)\n", "\n", "# create data points for the model\n", "mktbskt_score = create_data_points(mktbskt_model_control)\n", "\n", "# create metrics for BI output\n", "mktbskt_metric = create_metrics(mktbskt_score)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:snowflake.connector.cursor:query: [SELECT a.*, 1 change_direction, a.scd_in_use_case_key use_case_name_key, a.marke...]\n", "INFO:snowflake.connector.cursor:query execution done\n", "INFO:snowflake.connector.arrow_result:fetching data done\n", "INFO:__main__:Dimensions for data ingested from    scd_out_datapoint_key  scd_in_product_config_key  dim_metric_key  \\\n", "0                     11                          4               4   \n", "\n", "   dim_frequency_key dim_marketbasket_key  scd_in_use_case_key  \\\n", "0                  3                 None                    2   \n", "\n", "   scd_in_reporting_level_key        product_name metric_name frequency_name  \\\n", "0                           1  **POTE************       Units         WEEKLY   \n", "\n", "   ... reason_text  is_deleted              created_ts updated_ts  \\\n", "0  ...        None       False 2020-11-05 17:14:42.417       None   \n", "\n", "  change_direction  use_case_name_key marketbasket_name  frequency_key  \\\n", "0                1                  2              None              3   \n", "\n", "  reporting_level_key                     target_view_name  \n", "0                   1  vw_BRICK_WEEKLY_ORDER_GAP_DETECTION  \n", "\n", "[1 rows x 26 columns] = (1, 26)\n", "INFO:snowflake.connector.cursor:query: [SELECT * from vw_BRICK_WEEKLY_ORDER_GAP_DETECTION where metric_name = 'Units' an...]\n", "INFO:snowflake.connector.cursor:query execution done\n", "INFO:snowflake.connector.arrow_result:fetching data done\n", "INFO:__main__:Dimensions for data ingested from vw_BRICK_WEEKLY_ORDER_GAP_DETECTION = (53612, 9)\n", "INFO:__main__:Dimensions for data ingested from gap model = (0, 0)\n"]}, {"ename": "KeyError", "evalue": "'scd_name'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[0;32m~/.pyenv/versions/3.7.7/lib/python3.7/site-packages/pandas/core/indexes/base.py\u001b[0m in \u001b[0;36mget_loc\u001b[0;34m(self, key, method, tolerance)\u001b[0m\n\u001b[1;32m   2645\u001b[0m             \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2646\u001b[0;31m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2647\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mKeyError\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32mpandas/_libs/index.pyx\u001b[0m in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/index.pyx\u001b[0m in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/hashtable_class_helper.pxi\u001b[0m in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/hashtable_class_helper.pxi\u001b[0m in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: 'scd_name'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-25-de9465972cb6>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m    104\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    105\u001b[0m \u001b[0;31m# create metrics for BI output\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 106\u001b[0;31m \u001b[0mgap_metric\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcreate_metrics\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mgap_model\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    107\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    108\u001b[0m \"\"\"\n", "\u001b[0;32m<ipython-input-22-fdd75f6b3539>\u001b[0m in \u001b[0;36mcreate_metrics\u001b[0;34m(df)\u001b[0m\n\u001b[1;32m    290\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    291\u001b[0m   \u001b[0;31m# data for anomalies up\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 292\u001b[0;31m   \u001b[0mx\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdf\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mdf\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'scd_name'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m==\u001b[0m\u001b[0mdf\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'scd_name'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0munique\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'Score'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    293\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    294\u001b[0m   \u001b[0;31m# table for anomalies up\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.pyenv/versions/3.7.7/lib/python3.7/site-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36m__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   2798\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcolumns\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnlevels\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2799\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_getitem_multilevel\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2800\u001b[0;31m             \u001b[0mindexer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcolumns\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2801\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mis_integer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mindexer\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2802\u001b[0m                 \u001b[0mindexer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0mindexer\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.pyenv/versions/3.7.7/lib/python3.7/site-packages/pandas/core/indexes/base.py\u001b[0m in \u001b[0;36mget_loc\u001b[0;34m(self, key, method, tolerance)\u001b[0m\n\u001b[1;32m   2646\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2647\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mKeyError\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2648\u001b[0;31m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_maybe_cast_indexer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2649\u001b[0m         \u001b[0mindexer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_indexer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mmethod\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtolerance\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mtolerance\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2650\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mindexer\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mndim\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m1\u001b[0m \u001b[0;32mor\u001b[0m \u001b[0mindexer\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msize\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32mpandas/_libs/index.pyx\u001b[0m in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/index.pyx\u001b[0m in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/hashtable_class_helper.pxi\u001b[0m in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/hashtable_class_helper.pxi\u001b[0m in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: 'scd_name'"]}], "source": ["\n", "## Part 4: Market Share Use Case ----------------------------------------------------\n", "# pass parameters from the UI into use case\n", "### These tables are populated ONLY FOR NOVARTIS AUSTRALIA as of 07-15-2020 other clients will need these parameters manually assigned ### \n", "# freq_key 3 = weekly; use_case_number 4 = market share view; reporting_level_key 1 = brick level\n", "period_number, metric_name, product_name, marketbasket_name, change_direction, target_view_name = get_parameters(scd_out_param, freq_key=3, use_case_name_key=4, level=1)  \n", "\n", "# ingest data from views\n", "mkt_share = ingest_scd_views(target_view_name, period_number, metric_name, product_name)\n", "\n", "# assign parameters for use case\n", "if target_view_name.find('BRICK') != -1:  # use brick_hco_code for BRICK\n", "    reporting_level_name = mkt_share.columns[0]\n", "else:\n", "    reporting_level_name = mkt_share.columns[3]  # use sales_reference_account_id for ACCOUNT\n", "target = mktbskt.columns[-1]\n", "target = mkt_share.columns[-1]\n", "\n", "# convert sale_date to date\n", "mkt_share['sale_date'] = pd.to_datetime(mkt_share['sale_date'], format='%Y-%m-%d')\n", "\n", "# Important for MKTBSKT and MKT_SHARE subset data to market basket of interest  \n", "mkt_share = mkt_share[mkt_share.marketbasket_name==marketbasket_name]\n", "\n", "# call function to create columns for first and final deliveries \n", "mkt_share_first_final = first_final_purchase_date(mkt_share)\n", "\n", "# cutoff values by reporting_level_name to ensure no values are counted in the model prior to the first purchase\n", "mkt_share_first_final = mkt_share_first_final[mkt_share_first_final['sale_date'] >= mkt_share_first_final['first_delivery']]\n", "\n", "# call function to subset data according to purchase requirements\n", "mkt_share_time_flag = purchase_timing_requirement(mkt_share_first_final)\n", "\n", "# call function to subset data according to  minimum purchase count requirement\n", "mkt_share_clean = purchase_count_requirement(mkt_share_time_flag, 40)\n", "\n", "# clean columns before model\n", "mkt_share_clean = mkt_share_clean.loc[:,[reporting_level_name,'product_name','sale_date',target]]\n", "\n", "# max_level = number of bricks_hco_codes feeding into the model\n", "max_level = len(mkt_share_clean[reporting_level_name].unique())  \n", "\n", "# call the model\n", "mkt_share_model = mkt_share_clean.groupby(reporting_level_name).apply(anomaly_detect, .05, .25)  # higher alpha = lower metric percentages \n", "logger.info(f'Dimensions for data ingested from market share model = ({mkt_share_model.shape[0]}, {mkt_share_model.shape[1]})')\n", "\n", "# reset index\n", "mkt_share_model.reset_index(drop=True, inplace=True)\n", "\n", "# adjust for negative scores\n", "mkt_share_model_control = mkt_share_model.groupby(reporting_level_name).apply(control_scores)\n", "\n", "# market share only--adjust for predicted scores over 100  \n", "for i in range(len(mkt_share_model_control)):\n", "    if mkt_share_model_control.iloc[i].Predicted > 100:\n", "        mkt_share_model_control.iloc[i]['Predicted'] = 100 \n", "\n", "# create data points for the model\n", "mkt_share_score = create_data_points(mkt_share_model_control)\n", "\n", "# create metrics for BI output\n", "mkt_share_metric = create_metrics(mkt_share_score)\n", "\n", "## create process output tables -----------------------------------------------------------\n", "# create final output dataset and adjust for Marketbasket and Marketshare availibility \n", "if str(scd_out_param.target_view_name).find('MARKETBASKET') != -1:\n", "    scd_post_process_output = pd.concat([vol_score, gap_model, mktbskt_score, mkt_share_score]) \n", "else:\n", "    scd_post_process_output = pd.concat([vol_score, gap_model]) \n", "\n", "# rename and add columns to match post process output tables\n", "scd_post_process_output.columns = ['reporting_level_value','current_week_date','scd_actual_value','scd_predicted_value','scd_name','scd_score','product_name']\n", "scd_post_process_output['reporting_level_name'] = reporting_level_name.split(\"_\", 1)[0].upper()\n", "scd_post_process_output['scd_predicted_value'] = round(scd_post_process_output['scd_predicted_value'],2)\n", "scd_post_process_output['product_name'] = product_name\n", "scd_post_process_output = scd_post_process_output.loc[:,['scd_name','reporting_level_name','reporting_level_value','product_name','current_week_date','scd_actual_value','scd_predicted_value','scd_score']]\n", "\n", "# create final metrics dataset and adjust for Marketbasket and Marketshare availibility \n", "if str(scd_out_param.target_view_name).find('MARKETBASKET') != -1:\n", "    scd_post_process_bi_metric_output = pd.concat([vol_metric, gap_metric, mktbskt_metric, mkt_share_metric])\n", "else:\n", "    scd_post_process_bi_metric_output = pd.concat([vol_metric, gap_metric])\n", "\n", "# rename and add columns to match bi metric output tables\n", "scd_post_process_bi_metric_output.columns = ['current_week_date','scd_name','eligibility_percent','anomaly_threshold','reporting_level_count']\n", "scd_post_process_bi_metric_output['reporting_level_name'] = reporting_level_name.split(\"_\", 1)[0].upper()\n", "scd_post_process_bi_metric_output['product_name'] = product_name\n", "scd_post_process_bi_metric_output = scd_post_process_bi_metric_output.loc[:,['scd_name','reporting_level_name','product_name','current_week_date','eligibility_percent','anomaly_threshold','reporting_level_count']]\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>brick_hco_code</th>\n", "      <th>product_name</th>\n", "      <th>sale_date</th>\n", "      <th>sales_metric_value</th>\n", "      <th>order_gap_week</th>\n", "      <th>first_delivery</th>\n", "      <th>time_flag</th>\n", "      <th>final_delivery</th>\n", "      <th>purchase_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00002</td>\n", "      <td>**NTRE****</td>\n", "      <td>2018-03-24</td>\n", "      <td>2.000000</td>\n", "      <td>0.0</td>\n", "      <td>2018-03-31</td>\n", "      <td>707 days</td>\n", "      <td>2020-03-07</td>\n", "      <td>34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00002</td>\n", "      <td>**NTRE****</td>\n", "      <td>2018-03-31</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>2018-03-31</td>\n", "      <td>707 days</td>\n", "      <td>2020-03-07</td>\n", "      <td>34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>00002</td>\n", "      <td>**NTRE****</td>\n", "      <td>2018-04-07</td>\n", "      <td>0.000000</td>\n", "      <td>2.0</td>\n", "      <td>2018-03-31</td>\n", "      <td>707 days</td>\n", "      <td>2020-03-07</td>\n", "      <td>34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>00002</td>\n", "      <td>**NTRE****</td>\n", "      <td>2018-04-14</td>\n", "      <td>0.000000</td>\n", "      <td>3.0</td>\n", "      <td>2018-03-31</td>\n", "      <td>707 days</td>\n", "      <td>2020-03-07</td>\n", "      <td>34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00002</td>\n", "      <td>**NTRE****</td>\n", "      <td>2018-04-21</td>\n", "      <td>0.000000</td>\n", "      <td>4.0</td>\n", "      <td>2018-03-31</td>\n", "      <td>707 days</td>\n", "      <td>2020-03-07</td>\n", "      <td>34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107644</th>\n", "      <td>99994</td>\n", "      <td>**NTRE****</td>\n", "      <td>2020-02-08</td>\n", "      <td>74.000000</td>\n", "      <td>1.0</td>\n", "      <td>2018-03-17</td>\n", "      <td>721 days</td>\n", "      <td>2020-03-07</td>\n", "      <td>105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107645</th>\n", "      <td>99994</td>\n", "      <td>**NTRE****</td>\n", "      <td>2020-02-15</td>\n", "      <td>1078.000000</td>\n", "      <td>1.0</td>\n", "      <td>2018-03-17</td>\n", "      <td>721 days</td>\n", "      <td>2020-03-07</td>\n", "      <td>105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107646</th>\n", "      <td>99994</td>\n", "      <td>**NTRE****</td>\n", "      <td>2020-02-22</td>\n", "      <td>20.000000</td>\n", "      <td>1.0</td>\n", "      <td>2018-03-17</td>\n", "      <td>721 days</td>\n", "      <td>2020-03-07</td>\n", "      <td>105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107647</th>\n", "      <td>99994</td>\n", "      <td>**NTRE****</td>\n", "      <td>2020-02-29</td>\n", "      <td>64.000000</td>\n", "      <td>1.0</td>\n", "      <td>2018-03-17</td>\n", "      <td>721 days</td>\n", "      <td>2020-03-07</td>\n", "      <td>105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107648</th>\n", "      <td>99994</td>\n", "      <td>**NTRE****</td>\n", "      <td>2020-03-07</td>\n", "      <td>102.000000</td>\n", "      <td>1.0</td>\n", "      <td>2018-03-17</td>\n", "      <td>721 days</td>\n", "      <td>2020-03-07</td>\n", "      <td>105</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>107444 rows × 9 columns</p>\n", "</div>"], "text/plain": ["       brick_hco_code product_name  sale_date sales_metric_value  \\\n", "0               00002   **NTRE**** 2018-03-24           2.000000   \n", "1               00002   **NTRE**** 2018-03-31           0.000000   \n", "2               00002   **NTRE**** 2018-04-07           0.000000   \n", "3               00002   **NTRE**** 2018-04-14           0.000000   \n", "4               00002   **NTRE**** 2018-04-21           0.000000   \n", "...               ...          ...        ...                ...   \n", "107644          99994   **NTRE**** 2020-02-08          74.000000   \n", "107645          99994   **NTRE**** 2020-02-15        1078.000000   \n", "107646          99994   **NTRE**** 2020-02-22          20.000000   \n", "107647          99994   **NTRE**** 2020-02-29          64.000000   \n", "107648          99994   **NTRE**** 2020-03-07         102.000000   \n", "\n", "        order_gap_week first_delivery time_flag final_delivery  purchase_count  \n", "0                  0.0     2018-03-31  707 days     2020-03-07              34  \n", "1                  1.0     2018-03-31  707 days     2020-03-07              34  \n", "2                  2.0     2018-03-31  707 days     2020-03-07              34  \n", "3                  3.0     2018-03-31  707 days     2020-03-07              34  \n", "4                  4.0     2018-03-31  707 days     2020-03-07              34  \n", "...                ...            ...       ...            ...             ...  \n", "107644             1.0     2018-03-17  721 days     2020-03-07             105  \n", "107645             1.0     2018-03-17  721 days     2020-03-07             105  \n", "107646             1.0     2018-03-17  721 days     2020-03-07             105  \n", "107647             1.0     2018-03-17  721 days     2020-03-07             105  \n", "107648             1.0     2018-03-17  721 days     2020-03-07             105  \n", "\n", "[107444 rows x 9 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display (gap_clean)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>brick_hco_code</th>\n", "      <th>product_name</th>\n", "      <th>sale_date</th>\n", "      <th>rolling_week_sum</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8274</th>\n", "      <td>00412</td>\n", "      <td>**NTRE****</td>\n", "      <td>2018-03-24</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8275</th>\n", "      <td>00412</td>\n", "      <td>**NTRE****</td>\n", "      <td>2018-03-31</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8276</th>\n", "      <td>00412</td>\n", "      <td>**NTRE****</td>\n", "      <td>2018-04-07</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8277</th>\n", "      <td>00412</td>\n", "      <td>**NTRE****</td>\n", "      <td>2018-04-14</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8278</th>\n", "      <td>00412</td>\n", "      <td>**NTRE****</td>\n", "      <td>2018-04-21</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8372</th>\n", "      <td>00412</td>\n", "      <td>**NTRE****</td>\n", "      <td>2020-02-08</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8373</th>\n", "      <td>00412</td>\n", "      <td>**NTRE****</td>\n", "      <td>2020-02-15</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8374</th>\n", "      <td>00412</td>\n", "      <td>**NTRE****</td>\n", "      <td>2020-02-22</td>\n", "      <td>8.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8375</th>\n", "      <td>00412</td>\n", "      <td>**NTRE****</td>\n", "      <td>2020-02-29</td>\n", "      <td>8.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8376</th>\n", "      <td>00412</td>\n", "      <td>**NTRE****</td>\n", "      <td>2020-03-07</td>\n", "      <td>8.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>103 rows × 4 columns</p>\n", "</div>"], "text/plain": ["     brick_hco_code product_name  sale_date rolling_week_sum\n", "8274          00412   **NTRE**** 2018-03-24         2.000000\n", "8275          00412   **NTRE**** 2018-03-31         2.000000\n", "8276          00412   **NTRE**** 2018-04-07         4.000000\n", "8277          00412   **NTRE**** 2018-04-14         4.000000\n", "8278          00412   **NTRE**** 2018-04-21         4.000000\n", "...             ...          ...        ...              ...\n", "8372          00412   **NTRE**** 2020-02-08         4.000000\n", "8373          00412   **NTRE**** 2020-02-15        10.000000\n", "8374          00412   **NTRE**** 2020-02-22         8.000000\n", "8375          00412   **NTRE**** 2020-02-29         8.000000\n", "8376          00412   **NTRE**** 2020-03-07         8.000000\n", "\n", "[103 rows x 4 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["vol_clean[vol_clean[\"brick_hco_code\"]=='00412']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7"}}, "nbformat": 4, "nbformat_minor": 4}