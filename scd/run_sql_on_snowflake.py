
import sys
import snowflake.connector
from snowflake_python_base import snowflake_python_base
from codecs import open

class run_sql_on_snowflake (snowflake_python_base):

    """
    PURPOSE:
        This program executes the scd-pre-processor script.
    """

    # def replace_parameters(self, f, snowflake_params):
    #     print ("In replace")
    #     regionDB = snowflake_params.get("snowflake-dbregion", "")
    #     print ("Region DB to use for DW_CENTRAL_VIEW:", regionDB)
    #     for value in f:
    #         value.replace("$(REGION_DB)", regionDB)
    #         yield value
    
    #     #iterable = self.replace_parameters(f, snowflake_params)
    #     #for i in iterable:
    #     #    print (i)

    def replace_file_params( self, scriptFile, snowflake_params ):

        regionDB = snowflake_params.get("snowflake-dbregion", "")
        replacePattern = regionDB
        searchPattern = "$(REGION_DB)"
        print ("Region DB to use for DW_CENTRAL_VIEW:", regionDB)
        if (regionDB is None or regionDB == ""):
            print ("No region DB specified in metadata.  Continuing as single-tenant...")
            searchPattern = "$(REGION_DB).DW_CENTRAL_VIEW.VW_"
            replacePattern = "DW_CENTRAL."
            regionDB = "single-tenant"

        outputScriptFile = scriptFile + "." + regionDB

        #open input file
        fin = open(scriptFile, "rt")
        #output file to write the result to
        fout = open(outputScriptFile, "wt")
        #for each line in the input file
        for line in fin:
            #read replace the string and write to output file
            fout.write(line.replace(searchPattern, replacePattern))
        #close input and output files
        fin.close()
        fout.close()

        return outputScriptFile

    def process_db(self, conn, cmdline_params, snowflake_params):

        """
        INPUTS:
            conn is a Connection object returned from snowflake.connector.connect().
        """
        scriptFile = cmdline_params["scriptfile"]
        if scriptFile is None or scriptFile == '':
            print("ERROR: Use --scriptfile to specify sql script to execute")
            sys.exit(-3)

        print("Original SQL script file {}...".format(scriptFile))
        updatedScriptFile = self.replace_file_params(scriptFile, snowflake_params)
        print("Updated SQL script file {}...".format(updatedScriptFile))
        
        with open(updatedScriptFile, 'r', encoding='utf-8') as f:
            for cur in conn.execute_stream(f):
                for ret in cur:
                    print(ret)

if __name__ == '__main__':

    test_case = run_sql_on_snowflake()
    test_case.main(sys.argv)
