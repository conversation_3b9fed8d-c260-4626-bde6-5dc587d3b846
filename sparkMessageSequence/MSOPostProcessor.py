import os
import sys
import mysql.connector
import pandas as pd

# Update sys path to find the modules from Common and DataAccessLayer

script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
print("Learning dir:", learning_dir)
sys.path.append(learning_dir)

import common.pyUtils.aktana_ml_utils as mlutils

# import packages
from common.DataAccessLayer.DataAccessLayer import MessageAccessor
from common.DataAccessLayer.DataAccessLayer import SegmentAccessor
from common.DataAccessLayer.DataAccessLayer import DatabaseIntializer
from common.DataAccessLayer.DatabaseConfig import DatabaseConfig
from common.DataAccessLayer.DataAccessLayer import LearningAccessor
from common.DataAccessLayer.DataAccessLayer import DSEAccessor
import itertools
from datetime import datetime
from common.logger.Logger import create_logger
import common.DataAccessLayer.DataAccessLayer as data_access_layer

DEFAULT_LOOK_BACK_DAYS = 7


def get_look_back_date(conn, env_params, run_date):
    sql = f"""SELECT paramValue FROM {env_params['rds-learningdbname']}.DefaultLearningParam
    WHERE modelType = 'MSO' AND paramName = 'LE_MS_postProcLookBackDays' """

    look_back_df = pd.read_sql(sql, con=conn)
    look_back_days = DEFAULT_LOOK_BACK_DAYS
    if look_back_df.shape[0] > 0:
        look_back_days = int(look_back_df['paramValue'].tolist()[0])
        print(f"Use value: {look_back_days} from DefaultLearningParam...")
    else:
        print(f"Use default value {DEFAULT_LOOK_BACK_DAYS}...")

    look_back_date = datetime.strptime(run_date, "%Y-%m-%d") - pd.tseries.offsets.DateOffset(days=look_back_days)
    return datetime.strftime(look_back_date, "%Y-%m-%d")


# def find_sent_messages(start_date, product_id_list):
#     dse_accessor = DSEAccessor()
#
#     dse_accessor.find_sent_messages(start_date, product_id_list)


def connect_dse_database(env_params, tunnel_port):
    if tunnel_port == "":
        rds_host = env_params["rds-server"]
        tunnel_port = "3306"
    else:
        rds_host = "127.0.0.1"

    connection = mysql.connector.connect(host=rds_host, user=env_params["rds-user"],
                                         password=env_params["rds-password"],
                                         database=env_params["rds-enginedbname"], port=tunnel_port)
    return connection


def get_product_uids(conn, env_params):
    sql = f"""SELECT DISTINCT productUID FROM 
    {env_params['rds-learningdbname']}.LearningConfig lc, 
    {env_params['rds-learningdbname']}.LearningRun lr, 
    {env_params['rds-enginedbname']}.AccountMessageSequence ams 
    WHERE ams.learningRunUID = lr.learningRunUID 
    AND lr.learningConfigUID = lc.learningConfigUID"""

    df = pd.read_sql(sql, con=conn)
    uid_list = []

    for _, row in df.iterrows():
        uid_list.append(row['productUID'])

    product_uids = "'" + "','".join(uid_list) + "'"

    print("Products: " + product_uids)
    return product_uids

def update_account_message_sequence_scores(conn, env_params, run_date, look_back_date, product_uids):
    sql = f"""UPDATE {env_params['rds-enginedbname']}.AccountMessageSequence ams, {env_params['rds-learningdbname']}.SentMessages_v s 
    SET ams.probability = 0
    WHERE ams.accountId = s.accountId AND ams.messageId = s.messageId
    AND s.sentTime > '{look_back_date}' AND s.sentTime < '{run_date}' 
    AND s.productUID IN ({product_uids})"""

    print("Executing query:")
    print(sql)

    cursor = conn.cursor()
    cursor.execute(sql)
    conn.commit()
    cursor.close()


def main():
    """
    This is main function
    """
    ml_utils = mlutils.aktana_ml_utils()
    cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:",
                                                          ["customer=", "env=", "app=", "region=", "tunnelport=", "rundate="])
    print(metadata_params)
    print(cmdline_params)

    tunnel_port = cmdline_params.get("tunnelport", "")
    run_date = cmdline_params.get("rundate", datetime.strftime(datetime.today(), "%Y-%m-%d"))

    env_params = ml_utils.get_env_metadata()
    print(env_params)

    # set up connection
    conn = connect_dse_database(env_params, tunnel_port)

    # get look back date
    look_back_date = get_look_back_date(conn, env_params, run_date)

    # get product uids
    product_uids = get_product_uids(conn, env_params)

    # update AccountMessageSequence table
    update_account_message_sequence_scores(conn, env_params, run_date, look_back_date, product_uids)

    # close connection
    conn.close()

    print("Successfully updated AccountMessageSequence table")


if __name__ == '__main__':
    main()
