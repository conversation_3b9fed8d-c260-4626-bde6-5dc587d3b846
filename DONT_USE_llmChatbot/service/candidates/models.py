from typing import Optional, Union

from langchain_core.messages import HumanMessage, AIMessage
from langserve import CustomUserType


class SuggestionSummary(CustomUserType):
    summary: str
    raw_response: str
    query_filter: str
    context: list[dict]


class UserQuery(CustomUserType):
    query: str
    chat_history: Optional[list[Union[HumanMessage, AIMessage]]]
    context: dict


class UserQueryWithStructuredQuery(UserQuery):
    structured_query: str