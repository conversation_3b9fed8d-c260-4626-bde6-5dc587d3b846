import os
import shutil
from pathlib import Path

import boto3
from botocore.exceptions import ClientError

from candidates.query_constructor import get_data_dir


def get_s3_path(customer: str, region: str, suffix: str) -> tuple[str, str]:
    """
    TODO: Confirm this location
    Returns the S3 path for llm chatbot related files. This is where the column metadata is saved and where
    vector store will be uploaded
    :param customer:
    :param region:
    :param suffix:
    :return:
    """
    if suffix == 'prod':
        bucket = f"aktana-bdp-{customer}"
        path = "prod/llm_chatbot"
    else:
        bucket = f"aktana-bdp{region}-glue"
        path = f"llm_chatbot/{customer}/{suffix}"
    return bucket, path


def download_column_metadata(persist_directory: str, target_filename: str, s3_bucket: str, s3_path: str):
    """
    Downloads the column metadata from the S3 bucket and path specified
    :param target_filename: The filename of the column metadata file
    :param persist_directory: The location where the file will be downloaded
    :param s3_bucket: the s3 bucket where the file exists
    :param s3_path: the path in the s3 back where the file exists
    :return:
    """
    loc = Path(f"{persist_directory}/{target_filename}")
    os.makedirs(persist_directory, exist_ok=True)
    try:
        s3_client.download_file(s3_bucket, f"{s3_path}/{target_filename}", loc)
        print(f"Downloaded column metadata file from S3")
    except ClientError as e:
        print("Error getting column metadata from S3")
        raise e


def download_vector_store(persist_directory: str, target_filename: str, s3_bucket: str, s3_path: str):
    """
    Downloads the vector store from S3. The compressed vector store is extracted into the persist_directory
    :param persist_directory: The location where the vector store will be downloaded and extracted
    :param target_filename: the name of the vector store file
    :param s3_bucket: the S3 bucket where the vector store exists
    :param s3_path: the path in the S3 bucket where the vector store exists
    :return:
    """
    try:
        zip_file = f"{persist_directory}/{target_filename}"
        s3_client.download_file(s3_bucket, f"{s3_path}/{target_filename}", zip_file)
        shutil.unpack_archive(zip_file, extract_dir=f"{persist_directory}/vector_store")
        print(f"Downloaded existing vector store")
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            print("Vector store doesn't exist in S3")
        raise e


def main():
    """
    The main method in this utility script. It downloads the column metadata and vector store and saves it in the
    customer specific directory
    """
    customer = os.environ.get('CUSTOMER')
    awsregion = os.environ.get('AWS_REGION')
    environment = os.environ.get('ENVIRONMENT')
    devsuffix = os.environ.get('SCHEMA_SUFFIX', environment)
    region = os.environ.get('REGION')

    print('CUSTOMER    :', customer)
    print('AWSREGION   :', awsregion)
    print('ENVIRONMENT :', environment)
    print('DEVSUFFIX   :', devsuffix)
    print('REGION      :', region)

    bucket, path = get_s3_path(customer, region, devsuffix or environment)

    boto3.setup_default_session(region_name=awsregion)

    global s3_client
    s3_client = boto3.client('s3')

    local_dir = get_data_dir(customer, environment)
    download_column_metadata(local_dir, "column_metadata.csv", bucket, path)
    download_vector_store(local_dir, "vector_store.zip", bucket, path)


if __name__ == "__main__":
    main()
