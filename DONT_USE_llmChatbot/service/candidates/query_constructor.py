import csv
import datetime
import os
import re
import time
from datetime import datetime

from langchain.chains.query_constructor.base import AttributeInfo
from langchain.chains.query_constructor.base import get_query_constructor_prompt, StructuredQueryOutputParser
from langchain_community.query_constructors.chroma import ChromaTranslator
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnableLambda
from langchain_core.structured_query import Operation, Comparison, Comparator, Operator


def get_data_dir(customer, environment):
    return f"data/{customer}/{environment}"


# TODO: Get content description from some config
document_content_description = "Suggestions offering guidance tailored to Genentech's sales representatives. It " + \
                             "covers the suggestion's topic, the factors driving it, the reasoning behind its " + \
                             "conception, its importance, and potential talking points with healthcare " + \
                             "professionals (HCPs). Furthermore, it provides insights about the HCP, " + \
                             "including their content preferences, speciality, and other pertinent observations " + \
                             "such as prescribing behavior. Note that while HCP channel preferences are available " + \
                             "(email, face-to-face, virtual), information about specific RTE (Rep Triggered Email) " + \
                             "interactions is not currently tracked in the system."

# TODO: Make examples configurable
examples = [
    (
        "Summarize suggestions that were not recommended and are related to increase in the share of Dupixent and about HCPs who prefer video calls",
        {
            "query": "increase in the share of Dupixent",
            "filter": 'and(eq("RepId", "0015f00000Ub0kgAAB"), eq("Is Suggestion Suggested", false), eq("preferred_channel", "VIRTUAL_VISIT_CHANNEL"))',
        },
    ),
    (
        "Give a summary of high value suggestions for Xolair related to HCPs who placed order for samples and whose priority trend is increasing.",
        {
            "query": "HCPs who placed order for samples",
            "filter": 'and(eq("RepId", "0015f00000UZ76OAAT"), eq("Adjusted Expected Value", "High"), eq("Product", "Xolair"), eq("HCP Priority Trend", "Increase"))',
        },
    ),
    (
        "Show suggestions for Dr. Michael Kligerman that must be done",
        {
            "query": "Suggestions for Dr. Michael Kligerman",
            "filter": 'and(eq("RepId", "${rep_id}"), eq("Account Name", "Michael Kligerman"), eq("Is Suggestion Suggested", true), gte("Suggestion Date", "${today}"))',
        },
    ),
    (
        "Are there any suggestions for HCPs who have clicked or opened an RTE recently?",
        {
            "query": "suggestions about RTE interactions and email engagement",
            "filter": 'and(eq("RepId", "${rep_id}"), eq("preferred_channel", "SEND_CHANNEL"), eq("preferred_channel_segment", "High"))'
        }
    ),
    (
        "List out all suggestions for HCPs that have had a recent decline in prescribing behavior",
        {
            "query": "suggestions for HCPs with declining prescribing behavior",
            "filter": 'and(eq("RepId", "${rep_id}"), eq("HCP Priority Trend", "Decline"))',
        },
    ),
    (
        "Share the List of HCP's with urgent suggestions",
        {
            "query": "urgent suggestions for HCPs",
            "filter": 'and(eq("RepId", "${rep_id}"), eq("Is Suggestion Critical ", true))',
        },
    ),
    (
        "Which HCPs are decreasing in new prescriptions for drug Ocrevus?",
        {
            "query": "HCPs with declining trend for Ocrevus",
            "filter": 'and(eq("RepId", "${rep_id}"), eq("Product", "Ocrevus"), eq("HCP Priority Trend", "Decline"))',
        },
    ),
    (
        "Which HCPs are prescribing less of drug Xolair and more of competitor drug Ocrevus?",
        {
            "query": "HCPs switching from Xolair",
            "filter": 'and(eq("RepId", "${rep_id}"), eq("Product", "Xolair"), eq("HCP Priority Trend", "Decline"))',
        },
    ),
    (
        "What should I prioritize?",
{
    "query": "High priority suggestions requiring immediate attention",
    "filter": 'and(eq("RepId", "${rep_id}"), eq("Is Suggestion Suggested", true))',
},
    ),
    (
        "What suggestions do I need to do for HCPs who prefer video calls?",
{
    "query": "HCPs who prefer video calls",
    "filter": 'and(eq("RepId", "0015f00000Ub0kgAAB"), eq("Is Suggestion Suggested", true), eq("preferred_channel", "VIRTUAL_VISIT_CHANNEL"))',
},
    ),
    (
        "Can you give me suggestions for any HCPs that are endocrinologists?",
        {
            "query": "suggestions for endocrinologist HCPs",
            "filter": 'and(eq("RepId", "${rep_id}"), eq("HCP Speciality", "ENDOCRINOLOGY"))',
        },
    ),
    (
        "Are there any suggestions available that relate to patients switching data?",
        {
            "query": "suggestions where patients are switching medications",
            "filter": 'eq("RepId", "${rep_id}"),eq("Suggestion Use Case Tag", "Market Data Response"))',
        },
    ),
    (
        "what should I prioritize?",
        {
            "query": "High priority suggestions requiring immediate attention",
            "filter": 'and(eq("RepId", "${rep_id}"), eq("Is Suggestion Suggested", true), eq("Adjusted Expected Value", "High"))',
        },
    ),
    (
        "List out all suggestions for Dr John Bond",
        {
            "query": "suggestions for Dr John Bond",
            "filter": 'and(eq("RepId", "${rep_id}"), eq("Account Name", "John Bond"))',
        },
    ),
    (
        "Share the list of HCP's with active suggestions",
        {
            "query": "active suggestions for HCPs",
            "filter": 'and(eq("RepId", "${rep_id}"), eq("Is Suggestion Suggested", true))',
        },
    ),
(
    "I want to send a few RTE's. Can you recommend the best candidates I can send RTE today?",
    {
        "query": "suggestions for sending RTEs",
        "filter": 'and(eq("RepId", "${rep_id}"), eq("Is Suggestion Suggested", true))',
    }
)
]

QUERY_CONSTRUCTOR_PROMPT = """\
<< Structured Request Schema >>
When responding use a markdown code snippet with a JSON object formatted in the following schema:

```json
{{{{
    "query": string \\ text string to compare to document contents
    "filter": string \\ logical condition statement for filtering documents
}}}}
```

The query string should contain only text that is expected to match the contents of documents.

A logical condition statement is composed of one or more comparison and logical operation statements.

A comparison statement takes the form: `comp(attr, val)`:
- `comp` ({allowed_comparators}): comparator
- `attr` (string):  name of attribute to apply the comparison to
- `val` (string): is the comparison value

A logical operation statement takes the form `op(statement1, statement2, ...)`:
- `op` ({allowed_operators}): logical operator
- `statement1`, `statement2`, ... (comparison statements or logical operation statements): one or more statements to apply the operation to

User query is asked by {rep_name} who is a Sales Representative with RepId {rep_id}. Use this RepId in the filter, unless the user query asks about other sales representatives, in which case filter on the "Rep Name" field.
Make sure that you only use the comparators and logical operators listed above and no others.
If user query contains conjunction "and" or "as well as" related to the different values of a single attribute, then the filter should use "or" instead of "and".
For example, instead of `and(eq("Account Name", "Christen Kutz"), eq("Account Name", "Allen Bowling"))` use `or(eq("Account Name", "Christen Kutz"), eq("Account Name", "Allen Bowling"))`.
Make sure that filters only use the attributed names with its function names if there are functions applied on them.
Make sure that filters only use format `YYYY-MM-DD` when handling date data typed values. Today's date is {today}. When adding date conditions, be sure to add start and end dates in the filters.
Make sure that filters take into account the descriptions of attributes and only make comparisons that are feasible given the type of data being stored.
If there are no filters that should be applied return "NO_FILTER" for the filter value.
Make sure that filters only use valid values for comparison when applicable.
Always generate the filters first and then generate query. If no suitable query can be used, use the user query as-is in the query.
"""


class CustomChromaTranslator(ChromaTranslator):
    allowed_operators = [Operator.AND, Operator.OR]
    allowed_comparators = [
        Comparator.EQ,
        Comparator.NE,
        Comparator.GT,
        Comparator.GTE,
        Comparator.LT,
        Comparator.LTE,
        Comparator.IN,
        Comparator.NIN
    ]


def _read_metadata_field_info_from_csv(file_name):
    meta_json = []
    input_file = csv.DictReader(open(file_name))
    for row in input_file:
        meta_json.append(row)
    fields = []
    for m in meta_json:
        if m['include_in_metadata'] == '1':
            att = AttributeInfo.parse_obj({'type': m['type'], 'name': m['label'], 'description': m['description']})
            fields.append(att)
    return fields


# metadata_field_info = _read_metadata_field_info_from_csv("data/column_metadata.csv")


def _get_prompt(data):
    return PromptTemplate.from_template(QUERY_CONSTRUCTOR_PROMPT, partial_variables=data)


def _is_valid(arg, allowed_attributes):
    """Checks if the given argument is an instance of a Comparison and if so, checks if a valid field is used in the
    comparison. If the argument is not a Comparison (i.e., it is an Operator), then check if there is at least one
    argument in the Operator
    """
    if isinstance(arg, Comparison):
        return arg.attribute in allowed_attributes
    return bool(arg.arguments)


def _cleanup(op: Comparison, metadata_field_info):
    """Does a cleanup of attribute values. Mainly does the following:
    1. If Account name starts with the Dr. prefix, remove it.
    2. Convert date fields from String to timestamp.
    """
    if op.attribute == "Account Name" and op.value.startswith("Dr"):
        op.value = re.sub(r"Dr\.?", "", op.value).strip()
    if op.attribute in [f.name for f in metadata_field_info if f.type == 'date']:
        op.value = time.mktime(datetime.strptime(op.value["date"], "%Y-%m-%d").timetuple())


def _transform_date_into_range(op: Comparison, start_att_name: str, end_att_name: str):
    """Transforms the date query into a rango of date query. In particular, it does the following transformations:
    att == value -> end_att >= value and start_att <= value
    att >= value -> end_att >= value
    att <= value -> start_att <= value

    :param op: the comparison object to be transformed
    :param start_att_name: the name of the attribute representing start of the date range
    :param end_att_name: the name of the attribute representing end of the date range
    :return: the transformed comparison object
    """

    if op.comparator == Comparator.EQ:
        op = Operation(Operator.AND, [Comparison(Comparator.GTE, end_att_name, op.value),
                                      Comparison(Comparator.LTE, start_att_name, op.value)])
    elif op.comparator == Comparator.LT or op.comparator == Comparator.LTE:
        op.attribute = start_att_name
    elif op.comparator == Comparator.GT or op.comparator == Comparator.GTE:
        op.attribute = end_att_name
    return op


def _fix_operator(op, metadata_field_info):
    """Recursively traverses the operators in the provided operator and checks if the operators are valid. If not valid,
     removes it. Also does any cleanup on the Comparators. It also does transformations on the "Suggestion Date" and
     "Product" related conditions.
    :param op: Either an Operation or Comparison object
    :return: the fixed instance of the Operation or Comparison object
    """
    if isinstance(op, Operation):
        new_args = []
        for arg in op.arguments:
            arg = _fix_operator(arg, metadata_field_info)
            if _is_valid(arg, [f.name for f in metadata_field_info]):
                new_args.append(arg)
        op.arguments = new_args
    else:
        if op.attribute == "Suggestion Date":
            op = _transform_date_into_range(op, "First Suggestion Date", "Last Suggestion Date")
            _fix_operator(op, metadata_field_info)
        elif op.attribute == "Product":
            ops = []
            for i in range(1, 6):
                ops.append(Comparison(op.comparator, f"{op.attribute} {i}", op.value))
            op = Operation(Operator.OR, ops)
            _fix_operator(op, metadata_field_info)
        else:
            _cleanup(op, metadata_field_info)
    return op


def _fix_query(q, metadata_field_info):
    """Fixes any issues in the structured query including date filtering. Handles date transformations before
    operator fixes."""
    if q.filter is not None:
        # Add today's date filter
        today = datetime.today()
        date_comp = Comparison(Comparator.GTE, "Suggestion Date",
                             {"date": today.strftime("%Y-%m-%d")})

        # First transform dates properly
        if isinstance(q.filter, Operation) and q.filter.operator == Operator.AND:
            has_date = any(isinstance(arg, Comparison) and
                         arg.attribute == "Suggestion Date"
                         for arg in q.filter.arguments)
            if not has_date:
                q.filter.arguments.append(date_comp)
        else:
            q.filter = Operation(Operator.AND, [q.filter, date_comp])

        # Then fix other operators
        q.filter = _fix_operator(q.filter, metadata_field_info)

    return q


def _glossary_lookup(query):
    glossary = {}
    for k in glossary:
        if k in query.query:
            query.query = re.sub(r"\b" + k + r"\b", glossary[k], query.query)
    return query


def _get_computed_fields():
    """
    Returns additional computed fields that are not part of the metadata CSV.
    """
    fields = [AttributeInfo.parse_obj({'type': 'date', 'name': "First Suggestion Date", 'description': ''}),
              AttributeInfo.parse_obj({'type': 'date', 'name': "Last Suggestion Date", 'description': ''}),]

    for i in range(1, 6):
        fields.append(AttributeInfo.parse_obj({'type': 'string', 'name': f"Product {i}", 'description': ''}))
    return fields


def get_query_post_processing_runnable(metadata_field_info):
    """
    Returns the runnable that does post-processing on the generated query
    """
    metadata_field_info = list(metadata_field_info)
    metadata_field_info.extend(_get_computed_fields())
    return RunnableLambda(
        lambda x: _fix_query(x, metadata_field_info)) | RunnableLambda(_glossary_lookup)


def get_metadata_field_info(customer):
    """
    Reads and returns the metadata field info for the given customer
    """
    loc = get_data_dir(customer, os.environ.get('ENVIRONMENT'))
    metadata_file = f"{loc}/column_metadata.csv"
    return _read_metadata_field_info_from_csv(metadata_file)


def get_query_constructor_chain(llm, context, intermediate_results):
    """
    Returns the query constructor chain
    """
    data = {"rep_id": context["rep_id"], "rep_name": context["rep_name"], "today": context["today"]}

    metadata_field_info = get_metadata_field_info(context["customer"])

    prompt = get_query_constructor_prompt(
        document_content_description,
        metadata_field_info,
        examples=examples,
        allowed_comparators=CustomChromaTranslator.allowed_comparators,
        allowed_operators=CustomChromaTranslator.allowed_operators,
        schema_prompt=_get_prompt(data),
    )
    allowed_attributes = []
    for ainfo in metadata_field_info:
        allowed_attributes.append(
            ainfo.name if isinstance(ainfo, AttributeInfo) else ainfo["name"]
        )
    output_parser = StructuredQueryOutputParser.from_components(
        allowed_comparators=CustomChromaTranslator.allowed_comparators,
        allowed_operators=CustomChromaTranslator.allowed_operators,
        allowed_attributes=allowed_attributes,
    )

    return prompt | llm | RunnableLambda(
        lambda x: intermediate_results.update(
            {"structured_query": JsonOutputParser().parse(
                x.content)}) or x) | output_parser | get_query_post_processing_runnable(metadata_field_info)
