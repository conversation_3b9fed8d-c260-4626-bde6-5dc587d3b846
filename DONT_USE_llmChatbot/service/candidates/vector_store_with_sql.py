from sqlite3 import Connection
from typing import Any, Dict, List, Tuple
from typing import Union

from langchain.docstore.document import Document
from langchain.retrievers.self_query.base import SelfQueryRetriever
from langchain_core.structured_query import Comparison, Comparator, Operator, Operation, StructuredQuery, Visitor

from candidates.query_constructor import CustomChromaTranslator

COMPARATOR_TO_SQL = {
    Comparator.EQ: "=",
    Comparator.GT: ">",
    Comparator.GTE: ">=",
    Comparator.LT: "<",
    Comparator.LTE: "<=",
}

OPERATOR_TO_SQL = {
    Operator.AND: "AND",
    Operator.OR: "OR",
    Operator.NOT: "NOT",
}


def can_cast_to_float(string: str) -> bool:
    """Check if a string can be cast to a float."""
    try:
        float(string)
        return True
    except ValueError:
        return False


class SQLTranslator(Visitor):
    allowed_operators = [Operator.AND, Operator.OR, Operator.NOT]
    """Subset of allowed logical operators."""
    allowed_comparators = [
        Comparator.EQ,
        Comparator.GT,
        Comparator.GTE,
        Comparator.LT,
        Comparator.LTE,
    ]
    """Subset of allowed logical comparators."""

    def _format_func(self, func: Union[Operator, Comparator]) -> str:
        self._validate_func(func)
        if isinstance(func, Operator):
            value = OPERATOR_TO_SQL[func.value]  # type: ignore
        elif isinstance(func, Comparator):
            value = COMPARATOR_TO_SQL[func.value]  # type: ignore
        else:
            raise TypeError(f"func must be of type Operator or Comparator. Passed {type(func)}")
        return f"{value}"

    def visit_operation(self, operation: Operation) -> str:
        args = [arg.accept(self) for arg in operation.arguments]
        operator = self._format_func(operation.operator)
        return "(" + (" " + operator + " ").join(args) + ")"

    def visit_comparison(self, comparison: Comparison) -> str:
        comparator = self._format_func(comparison.comparator)
        values = comparison.value
        if isinstance(values, list):
            sql = []
            for value in values:
                comparison.value = value
                sql.append(self.visit_comparison(comparison))

            return "(" + " OR ".join(sql) + ")"

        if not can_cast_to_float(comparison.value):
            values = f"'{values}'"
        return f"\"{comparison.attribute}\" {comparator} {values}"

    def visit_structured_query(
            self, structured_query: StructuredQuery
    ) -> Tuple[str, dict]:
        if structured_query.filter is None:
            kwargs = {}
        else:
            sql = f"SELECT * FROM suggestion_metadata WHERE {structured_query.filter.accept(self)}"
            kwargs = {"sql": sql}
        return structured_query.query, kwargs


class CustomSelfQueryRetriever(SelfQueryRetriever):
    db_conn: Connection

    def __init__(self, db_conn, **kwargs):
        super().__init__(db_conn=db_conn, **kwargs)

    def _get_docs_with_query(self, query: str, search_kwargs: Dict[str, Any]) -> List[Document]:
        sql = search_kwargs["sql"]
        search_kwargs.pop('sql', None)
        res = self.db_conn.execute(sql)
        data = res.fetchall()
        if data is not None and len(data) > 0:
            comp = Comparison(Comparator.IN, "SuggestionReferenceId", [x["Suggestion ID"] for x in data])
            new_kwargs = {"filter": CustomChromaTranslator().visit_comparison(comp)}
            search_kwargs = {**self.search_kwargs, **new_kwargs}
        docs = self.vectorstore.search(query, self.search_type, **search_kwargs)
        return docs
