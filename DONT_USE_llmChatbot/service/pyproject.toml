[tool.poetry]
name = "llm_chatbot"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "app" },
]

[tool.poetry.dependencies]
python = "^3.11"
uvicorn = "^0.23.2"
langserve = {extras = ["server"], version = ">=0.0.30"}
pydantic = "<2"


[tool.poetry.group.dev.dependencies]
langchain-cli = ">=0.0.15"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
