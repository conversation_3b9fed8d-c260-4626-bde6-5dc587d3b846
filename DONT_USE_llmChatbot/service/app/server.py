import datetime
import os
from pathlib import Path

from fastapi import FastAPI
from fastapi.responses import RedirectResponse
from langchain_core.runnables import <PERSON><PERSON><PERSON>L<PERSON>bda
from langserve import add_routes

from candidates import summarizer, download_artifacts
from candidates.models import UserQuery
from candidates.models import UserQueryWithStructuredQuery
from candidates.query_constructor import get_data_dir


def get_file_modified_date(filename):
    t = os.path.getmtime(filename)
    return datetime.datetime.fromtimestamp(t)


def get_artifact_file_paths():
    dir = get_data_dir(os.environ.get('CUSTOMER'), os.environ.get('ENVIRONMENT'))
    vs_dir = Path(f"{dir}/vector_store")
    column_metadata = Path(f"{dir}/column_metadata.csv")
    return vs_dir, column_metadata


vs_dir, meta_file = get_artifact_file_paths()
if not vs_dir.exists() or not meta_file.exists():
    download_artifacts.main()

app = FastAPI()


@app.get("/ping")
async def health_check():
    return {"status": "OK"}


@app.get("/info")
async def get_status():
    status = {"status": "OK"}
    vs_dir, column_metadata = get_artifact_file_paths()
    if vs_dir.exists():
        status["vectorstore_created_at"] = get_file_modified_date(vs_dir)
    else:
        status["vectorstore_created_at"] = None
    if column_metadata.exists():
        status["column_metadata_created_at"] = get_file_modified_date(column_metadata)
    else:
        status["column_metadata_created_at"] = None
    return status


@app.post("/refresh_data")
async def refresh_data():
    download_artifacts.main()
    return {"status": "OK"}


@app.get("/")
async def redirect_root_to_docs():
    return RedirectResponse("/docs")


add_routes(app, RunnableLambda(summarizer.get_full_summarization_runnable).with_types(input_type=UserQuery).bind(
    intermediate_results={}), path="/suggestion_summarizer")

add_routes(app, RunnableLambda(summarizer.get_query_generator_runnable).with_types(input_type=UserQuery).bind(
    intermediate_results={}), path="/suggestion_summarizer/query_generator")

add_routes(app, summarizer.get_standalone_question_generator_runnable(),
           path="/suggestion_summarizer/standalone_question_generator")

add_routes(app, RunnableLambda(summarizer.get_summarization_only_runnable).with_types(
    input_type=UserQueryWithStructuredQuery).bind(intermediate_results={}),
           path="/suggestion_summarizer/summarization_only")

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8100)
