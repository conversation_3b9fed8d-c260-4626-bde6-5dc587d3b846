import codecs
import csv
import json
import os
import re
import shutil
import sqlite3
import sys
from datetime import datetime, timedelta

import tiktoken
from dotenv import load_dotenv
from jinja2 import Template

# The below block is added because Chroma requires sqlite==3.35
if sqlite3.sqlite_version_info < (3, 35, 0):
    import pysqlite3

    ignore = pysqlite3  # Added to prevent IDE from auto-removing import
    sys.modules["sqlite3"] = sys.modules.pop("pysqlite3")

import time

import boto3
import pandas as pd
from botocore.exceptions import ClientError
import chromadb
from chromadb.config import Settings
from langchain.docstore.document import Document
from langchain_community.vectorstores import Chroma
from langchain_openai import AzureOpenAIEmbeddings
from pyathena import connect

load_dotenv()

AWS_ACCESS_KEY = os.environ.get('AWS_ACCESS_KEY_ID')
AWS_SECRET_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
AWS_SESSION_TOKEN = os.environ.get('AWS_SESSION_TOKEN')

""" These are system fields that are to be stored as metadata for each record, but is not meant to be used to in the
application """
SYSTEM_FIELDS = [
    {"name": "rundate", "label": "rundate", "type": "date", "include_in_metadata": "1", "include_in_content": "0"},
    {"name": "First Suggestion Date", "label": "First Suggestion Date", "type": "date", "include_in_metadata": "1",
     "include_in_content": "0"},
    {"name": "Last Suggestion Date", "label": "Last Suggestion Date", "type": "date", "include_in_metadata": "1",
     "include_in_content": "0"},
]

""" The number of days from the current date to be used to fetch candidates """
DAYS_INTERVAL = 30

today = datetime.today()

""" The number of records to be embedded at a time. """
BATCH_SIZE = 500

COLLECTION_NAME = "langchain"

# Set the environment variables AZURE_OPENAI_API_KEY and AZURE_OPENAI_ENDPOINT
embeddings = AzureOpenAIEmbeddings(
    model="text-embedding-3-small",
    retry_min_seconds=30,
    retry_max_seconds=60,
    request_timeout=60,
    show_progress_bar=False
)


def get_schema(customer: str, suffix: str) -> str:
    """
    Get the athena DB containing views and tables
    """
    schema = "impact_" + customer + "_" + suffix
    return schema


def get_table(sql: str) -> pd.DataFrame:
    """
    Run the query on athena and returns pandas dataframe
    """
    print(f"Getting data from Athena")
    df = pd.read_sql_query(sql, conn)
    """
    df = pd.read_csv("local_data.csv")
    import json
    df['json_reasons'] = df['json_reasons'].apply(json.loads)
    """
    return df


def get_s3staging(customer: str, region: str, devsuffix: str) -> str:
    """
    The S3 location where Athena results are saved
    """
    s3_staging_dir = f"s3://aktana-bdp{region}-glue/athena/{customer}/{devsuffix}"
    return s3_staging_dir


def get_s3target(customer: str, region: str, suffix: str) -> tuple[str, str]:
    """
    Returns the S3 path for llm chatbot related files

    TODO: Confirm this location
    Returns the S3 path for llm chatbot related files. This is where the column metadata is saved and where
    vector store will be uploaded
    :param customer:
    :param region:
    :param suffix:
    :return:
    """

    if suffix == 'prod':
        bucket = f"aktana-bdp-{customer}"
        path = "prod/llm_chatbot"
    else:
        bucket = f"aktana-bdp{region}-glue"
        path = f"llm_chatbot/{customer}/{suffix}"
    return bucket, path


def get_metadata_template(s3_bucket: str, s3_path: str) -> str:
    """
    Reads the metadata template file from S3. If not found, returns the default template.
    Customer name is injected from environment variable.
    """
    print(f"Fetching metadata template from {s3_bucket}/{s3_path}")
    try:
        obj = s3_client.get_object(Bucket=s3_bucket, Key=f"{s3_path}/metadata_template.csv")
        template_content = obj['Body'].read().decode('utf-8')
        return template_content
    except ClientError as e:
        if e.response["Error"]["Code"] == "404" or e.response["Error"]["Code"] == "NoSuchKey":
            print("Metadata template doesn't exist in S3. Using default template.")
            return '''type,name,label,description,include_in_metadata,include_in_content
string,suggestionreferenceid,id,A non-unique ID for the suggestion,1,0
date,suggesteddate,Suggestion Date,"The date of the suggestion. The date can either be in the past or in the future. Suggestions are usually about scheduling visits. So, date of the suggestion is the same as the date of the visit.",1,0
string,repname,Rep Name,The name of the Sales Representative promoting {{ customer }}'s products.,1,0
string,repuid,RepId,A unique ID for the Rep,1,0
string,accountname,Account Name,The name of the Account or HCP to whom {{ customer }}'s products are promoted by the Sales Representative,1,0
string,accountuid,AccountId,A unique ID for the account or HCP,1,0
string,channel,Suggested Channel,"The channel recommended by the suggestion. Valid values are {{ channel }}",1,0
string,json_reasons,Suggestion Reason,A detailed explanation of why the suggestion is created.,0,1
string,factorname,Suggestion Factors,The factors or the rules that triggered the creation of the suggestion. This can often tell what this suggestion is about.,0,1
string,productname,Product,"{{ customer }}'s product associated with the first suggestion reason.This is the product the Sales Representative is promoting.Valid values are {{ productname }}.",1,0
string,use_case_tag,Suggestion Use Case Tag,"This is the tactic or the strategy that the first suggestion reason is trying to address. Valid values are {{ use_case_tag }}.",1,0
boolean,recommended,Is Suggestion Suggested,"Indicates if the suggestion was presented to the Sales Representative. A true value indicates that the suggestion was presented to the Sales Representative, whereas a false value indicates that the suggestion was not presented to the Sales Representative.",1,0
string,reasontext,Suggestion,The action that was suggested to the Sales Representative,0,1
string,,Account Insight List,Contains any insight pertaining to an Account or HCP.,0,1
string,gnespecialty1_akt,HCP Speciality,"The speciality of the HCP. Valid values are {{ gnespecialty1_akt }}",1,0
string,facilityname,Facility Name,The name of the facility that the HCP works at.,1,0
string,expectedvalue_group,Adjusted Expected Value,"Adjusted Expected Value gives an indication of the expected economic value of the suggestion and is used to rank them. The expected value takes into account any costs associated with acting on the suggestion. Valid values are {{ expectedvalue_group }}",1,0
string,priority_segment,HCP Priority Segment,"Gives an indication of the priority of the HCP in terms of the market potential. Valid values are {{ priority_segment }}. Filter on this field only when specifically asked for it.",1,0
string,priority_trend,HCP Priority Trend,"Gives the trend of the priority of the HCP. Valid values are {{ priority_trend }}. Filter on this field only when specifically asked for it.",1,0
string,recommended_content_1,Primary HCP Content Affinity,"The type of content that the HCP has affinity to. Valid values are {{ recommended_content_1 }}. Note: when filtering on this field, also filter on 'Secondary HCP Content Affinity' field with an 'or' condition.",1,0
string,recommended_content_2,Secondary HCP Content Affinity,"An additional type of content that the HCP has affinity to. Valid values are {{ recommended_content_2 }}. Note: when filtering on this field, also filter on 'Primary HCP Content Affinity' field with an 'or' condition.",1,0
string,dco_reason_category,Reason For Not Suggesting,"Gives a reason why the suggestion was not given to the Sales Representative. Valid values are {{ dco_reason_category }}",1,0
boolean,issuggestioncritical,Is Suggestion Critical,Indicates if the suggestion is marked as critical or urgent,1,0
string,send_prob_cali_segment,Email Channel Propensity,"The propensity segment for email channel. Valid values are {{ send_prob_cali_segment }}",1,0
string,visit_prob_cali_segment,Face-to-Face Channel Propensity,"The propensity segment for face-to-face channel. Valid values are {{ visit_prob_cali_segment }}",1,0
string,virtual_visit_prob_cali_segment,Virtual Visit Channel Propensity,"The propensity segment for virtual visits. Valid values are {{ virtual_visit_prob_cali_segment }}",1,0
string,preferred_channel,Preferred Channel,"The channel that the HCP prefers most based on probability calculations. Valid values are {{ preferred_channel }}",1,0
string,preferred_channel_segment,Preferred Channel Segment,"The segment indicating channel preference strength. Valid values are {{ preferred_channel_segment }}",1,0
'''
        raise e


def get_valid_values_from_existing_docs(vectorstore) -> dict:
    """
    Gets valid values from existing documents in the vector store
    """
    results = vectorstore.get(include=['metadatas'])
    metadatas = results['metadatas']

    valid_values = {
        'channel': set(),
        'use_case_tag': set(),
        'gnespecialty1_akt': set(),
        'expectedvalue_group': set(),
        'priority_segment': {'High', 'Medium', 'Low'},
        'priority_trend': {'Decline', 'Hold', 'Increase'},
        'recommended_content_1': set(),
        'recommended_content_2': set(),
        'dco_reason_category': set(),
        'productname': set(),
        'send_prob_cali_segment': set(),
        'visit_prob_cali_segment': set(),
        'virtual_visit_prob_cali_segment': set(),
        'preferred_channel':set(),
        'preferred_channel_segment': set()
    }

    for metadata in metadatas:
        for field in valid_values.keys():
            if field in metadata and metadata[field]:
                valid_values[field].add(metadata[field])

    return {k: sorted(list(v)) for k, v in valid_values.items()}


def merge_valid_values(existing_values: dict, new_values: dict) -> dict:
    """
    Merges valid values from existing documents and new data.
    Values are no longer hardcoded but derived from the data.
    """
    merged = {}
    all_keys = set(existing_values.keys()) | set(new_values.keys())

    for key in all_keys:
        # Merge and deduplicate values
        existing = set(existing_values.get(key, []))
        new = set(new_values.get(key, []))
        combined = existing | new
        # Remove None and empty strings
        cleaned = {v for v in combined if v is not None and str(v).strip()}
        merged[key] = sorted(list(cleaned))

    return merged


def preprocess_data_values(data: pd.DataFrame) -> pd.DataFrame:
    """
    Preprocesses all data values in the dataframe. This includes all transformations, cleanups,
    and standardizations before column mapping.
    """
    # Priority trend processing
    data.loc[data["rankchange"] < 0, "priority_trend"] = "Decline"
    data.loc[data["rankchange"] == 0, "priority_trend"] = "Hold"
    data.loc[data["rankchange"] > 0, "priority_trend"] = "Increase"

    # Segment to priority mapping
    data.loc[(data["segment"] == "T1") | (data["segment"] == "T2"), "priority_segment"] = "High"
    data.loc[data["segment"] == "T3", "priority_segment"] = "Medium"
    data.loc[(data["segment"] == "T4") | (data["segment"] == "T5"), "priority_segment"] = "Low"

    # Channel propensity processing
    # Instead of creating boolean flags, we'll ensure the segment values are properly formatted
    for col in ["send_prob_cali_segment", "visit_prob_cali_segment", "virtual_visit_prob_cali_segment"]:
        if col in data.columns:
            data[col] = data[col].apply(lambda x: x.capitalize() if x else x)

    # Clean and standardize text fields
    data["dco_reason_category"] = (data["dco_reason_category"]
                                   .apply(lambda x: re.sub(r"\d+\. ", "", x) if x else x)
                                   .apply(lambda x: x if x != "Recommended" else None))

    data["expectedvalue_group"] = data["expectedvalue_group"].apply(lambda x: x.capitalize() if x else x)
    data["channel"] = data["channel"].apply(lambda x: x.replace("_", " ") if x else x)
    data["use_case_tag"] = data["use_case_tag"].fillna("").apply(lambda x: x.replace("_", " "))

    # Process suggestion reasons
    data["json_reasons"] = data["json_reasons"].apply(
        lambda x: [a["text"].replace("\xa0", " ") for a in x if a["reasonrank"] != 1])

    data["suggesteddate"] = data["suggesteddate"].apply(
        lambda x: time.mktime(datetime.strptime(x, "%Y-%m-%d").timetuple()))
    data["rundate"] = data["rundate"].apply(lambda x: time.mktime(datetime.strptime(x, "%Y-%m-%d").timetuple()))
    return data


def get_valid_values_from_df(df: pd.DataFrame) -> dict:
    """
    Computes valid values for relevant columns from the dataframe.
    All values, including what were previously fixed values, are now extracted from the data.
    """
    valid_values = {
        'channel': sorted(df['channel'].dropna().unique().tolist()),
        'use_case_tag': sorted(df['use_case_tag'].dropna().unique().tolist()),
        'gnespecialty1_akt': sorted(df['gnespecialty1_akt'].dropna().unique().tolist()),
        'expectedvalue_group': sorted(df['expectedvalue_group'].dropna().unique().tolist()),
        'priority_segment': sorted(df['priority_segment'].dropna().unique().tolist()),
        'priority_trend': sorted(df['priority_trend'].dropna().unique().tolist()),
        'recommended_content_1': sorted(df['recommended_content_1'].dropna().unique().tolist()),
        'recommended_content_2': sorted(df['recommended_content_2'].dropna().unique().tolist()),
        'dco_reason_category': sorted(df['dco_reason_category'].dropna().unique().tolist()),
        'productname': sorted(df['productname'].dropna().unique().tolist()),
    'send_prob_cali_segment': sorted(df['send_prob_cali_segment'].dropna().unique().tolist()),
    'visit_prob_cali_segment': sorted(df['visit_prob_cali_segment'].dropna().unique().tolist()),
    'virtual_visit_prob_cali_segment': sorted(df['virtual_visit_prob_cali_segment'].dropna().unique().tolist())
        'preferred_channel': sorted(df['preferred_channel'].dropna().unique().tolist()),
        'preferred_channel_segment': sorted(df['preferred_channel_segment'].dropna().unique().tolist()),

    }
    return valid_values


def remove_duplicates(data: list[dict]) -> list[dict]:
    """
    Removed duplicate records so that there is just 1 record per suggestion reference Id. This method assumes that the
    records are already sorted by Suggestion Date. Following strategy is used:
    1. Create "First Suggestion Date" and "Last Suggestion Date" fields that tells when the suggestion was first and
    last generated. This is done to deal with duplication created due to the same suggestion generated on multiple days.
    2. Maintain a "Set" of products. This is to handle duplication due to multiple products for a single candidate.

    Only the latest Suggestion candidate will be retained.
    """
    id_map = {}
    for d in data:
        sid = d['id']
        if sid in id_map:
            existing = id_map[sid]
            d["First Suggestion Date"] = existing["First Suggestion Date"]
            d["Last Suggestion Date"] = d["Suggestion Date"]
            if d["Product"]:
                existing["Product"].add(d["Product"])
            if existing["Product"]:
                d["Product"] = existing["Product"]
            id_map[sid] = d
        else:
            d["First Suggestion Date"] = d["Suggestion Date"]
            d["Last Suggestion Date"] = d["Suggestion Date"]
            if d["Product"]:
                p = set()
                p.add(d["Product"])
                d["Product"] = p
            id_map[sid] = d
    return [v for k, v in id_map.items()]


def preprocess_data(data: pd.DataFrame, column_meta: list[dict]) -> list[dict]:
    """
    Handles only column mapping and filtering. All value transformations should be done
    in preprocess_data_values.
    """
    # No need for copy here as we're doing non-destructive operations
    data = data.sort_values(by=["suggestionreferenceid", "suggesteddate", "productname"])
    column_mapping = {c["name"]: c["label"] for c in column_meta if c["name"] is not None and c["name"] != ""}
    data = data.rename(columns=column_mapping)
    data = data.drop(columns=data.columns.difference([c["label"] for c in column_meta]), errors='ignore')
    data = data.to_dict('records')
    data = remove_duplicates(data)
    return data


def generate_metadata_csv(data: pd.DataFrame, existing_vectorstore, s3_bucket: str, s3_path: str):
    """
    Generates the metadata CSV file with computed valid values and uploads it to S3.
    """
    print(f"Generating metadata CSV with computed valid values")

    # Get valid values
    existing_values = get_valid_values_from_existing_docs(existing_vectorstore)
    new_values = get_valid_values_from_df(data)
    valid_values = merge_valid_values(existing_values, new_values)

    # Add customer name to template variables
    valid_values['customer'] = os.environ.get('CUSTOMER', 'Unknown')

    # Render template
    template_content = get_metadata_template(s3_bucket, s3_path)
    template = Template(template_content)
    rendered_csv = template.render(**valid_values)

    temp_file = "column_metadata.csv"
    with open(temp_file, "w", encoding="utf-8") as f:
        f.write(rendered_csv)

    try:
        s3_client.upload_file(temp_file, s3_bucket, f"{s3_path}/column_metadata.csv")
        print(f"Successfully uploaded metadata CSV to {s3_bucket}/{s3_path}")
    except Exception as e:
        print(f"Error uploading metadata CSV: {e}")
        raise
    finally:
        os.remove(temp_file)


def read_column_metadata(s3_bucket, s3_path):
    """
    Reads the column metadata file from S3
    """
    print(f"Fetching column metadata from {s3_bucket}/{s3_path}")
    try:
        obj = s3_client.get_object(Bucket=s3_bucket, Key=f"{s3_path}/column_metadata.csv")
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            print("Column metadata doesn't exist in S3. Please create/upload one.")
        raise e
    meta_json = []
    for row in csv.DictReader(codecs.getreader('utf-8')(obj[u'Body'])):
        meta_json.append(row)
    meta_json.extend(SYSTEM_FIELDS)
    return meta_json


def prepare_metadata(record: dict, metadata_fields: list[dict]):
    metadata = {}
    for c_meta in metadata_fields:
        name = c_meta["label"]
        if name in record:
            if record[name] is not None:
                if isinstance(record[name], set):
                    for indx, item in enumerate(record[name]):
                        metadata[f"{name} {indx+1}"] = item
                elif c_meta["type"] == "number":
                    # Ensure numeric values are stored as floats
                    metadata[name] = float(record[name])
                else:
                    metadata[name] = record[name]
    return metadata


def prepare_documents_for_vector_store(data: list[dict], column_meta: list[dict]):
    """
    Converts the data into Documents for indexing in the vector store
    """
    print(f"Creating documents for the vector store")
    docs = []
    metadata_cols = [m for m in column_meta if m["include_in_metadata"] == '1']
    content_col_names = [m["label"] for m in column_meta if m["include_in_content"] == '1']

    for values_to_embed in data:
        to_metadata = prepare_metadata(values_to_embed, metadata_cols)
        to_embed = {k: v for k, v in values_to_embed.items() if k in content_col_names}
        new_doc = Document(page_content=json.dumps(to_embed), metadata=to_metadata)
        docs.append(new_doc)
    return docs


def count_tokens(docs):
    """
    Counts tokens across all documents and distinct documents and prints some statistics
    """
    if len(docs) > 0:
        encoding = tiktoken.encoding_for_model('text-embedding-3-small')
        token_counts = []
        content_token_dict = {}
        for doc in docs:
            num_tokens = len(encoding.encode(doc.page_content))
            token_counts.append(num_tokens)
            content_token_dict[doc.page_content] = num_tokens
        total = sum(token_counts)
        print(f"Total number of documents: {len(docs)}")
        print(f"Total number of distinct documents: {len(content_token_dict)}")
        print(f"Average token count: {total / len(docs)}")
        print(f"Total number of tokens: {total}")
        total_tokens_in_distinct_docs = sum([v for k, v in content_token_dict.items()])
        print(f"Total number of tokens in distinct documents: {total_tokens_in_distinct_docs}")
        print(f"Approx cost of indexing: ${total * 0.00002 / 1000}")
        print(f"Approx cost of indexing distinct documents: ${total_tokens_in_distinct_docs * 0.00002 / 1000}")


def get_chroma_settings(persist_directory):
    """
    Returns the chroma db config for the vectorstore
    """
    return Settings(persist_directory=persist_directory, anonymized_telemetry=False, is_persistent=True)


def get_vector_store(persist_directory):
    """
    Instantiates the vectorstore stored in the persist_directory
    """
    chroma_settings = get_chroma_settings(persist_directory)
    client = chromadb.Client(chroma_settings)
    vectorstore = Chroma(embedding_function=embeddings, client=client, client_settings=chroma_settings,
                         collection_metadata={"hnsw:space": "ip"})
    return vectorstore, client


def load_data(docs, ids, client_settings):
    """
    Loads the documents into a vector store. Adds 30 seconds timeout between requests to avoid 429 errors.
    """
    vectorstore = None
    for i in range(0, len(docs), BATCH_SIZE):
        print(f"Indexing docs from i={i} to i={min(i + BATCH_SIZE, len(docs))}")
        if i + BATCH_SIZE > len(docs):
            end = len(docs)
        else:
            end = i + BATCH_SIZE
        start_t = time.time()
        vectorstore = Chroma.from_documents(docs[i:end], embeddings, collection_name=COLLECTION_NAME, ids=ids[i:end],
                                            client_settings=client_settings)
        end_t = time.time()
        print(f"Time taken to index: {(end_t - start_t)}s")
        # Add sleep if you get HTTP 429: Too many requests Errors
        # time.sleep(30)
    return vectorstore


def update_existing_docs(vs_client, docs, metadatas):
    """
    If document already exists, only the metadata part of the document is updated. The metadata is replaced by the
    metadata of the latest document, while maintaining the "First Suggestion Date" from the existing data.
    """
    ids = []
    new_metas = []
    for d in docs:
        sid = d.metadata["id"]
        existing_meta = metadatas[sid]
        d.metadata["First Suggestion Date"] = existing_meta["First Suggestion Date"]
        ids.append(sid)
        new_metas.append(d.metadata)
    if ids:
        vs_client.get_collection(COLLECTION_NAME).update(ids, metadatas=new_metas)


def create_vector_store(docs: list[Document], persist_directory: str):
    """
    Adds the documents in the vector store
    """
    print(f"Creating vector store using the documents")
    vectorstore, vs_client = get_vector_store(persist_directory)
    existing = vectorstore.get(ids=[x.metadata["id"] for x in docs], include=["metadatas"])
    new_docs = [d for d in docs if d.metadata["id"] not in existing["ids"]]
    existing_docs = [d for d in docs if d.metadata["id"] in existing["ids"]]
    update_existing_docs(vs_client, existing_docs, {existing["ids"][i]: m for i, m in enumerate(existing["metadatas"])})
    count_tokens(new_docs)
    vectorstore = load_data(new_docs, [x.metadata["id"] for x in new_docs], get_chroma_settings(persist_directory))
    return vectorstore


def download_vector_store(persist_directory, target_filename, s3_bucket, s3_path):
    """
    Downloads the vector store zip file from S3 and unpacks it.
    """
    try:
        s3_client.download_file(s3_bucket, f"{s3_path}/{target_filename}.zip", f"{target_filename}.zip")
        shutil.unpack_archive(f"{target_filename}.zip", extract_dir=persist_directory)
        print(f"Downloaded existing vector store")
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            print("Vector store doesn't exist in S3. Will create a new one.")
            pass
        else:
            print(e)
            raise e


def upload_vector_store(persist_directory, target_filename, s3_bucket, s3_path):
    """
    Creates a ZIP of the vector DB and uploads it to S3
    """
    print(f"Uploading vector store to S3 at {s3_bucket}/{s3_path}")
    filename = shutil.make_archive(target_filename, 'zip', persist_directory)
    try:
        s3_client.upload_file(filename, s3_bucket, f"{s3_path}/{target_filename}.zip")
    except ClientError as e:
        print(e)
        raise e


def cleanup_files(persist_directory, filename):
    """
    Deletes temporary files
    """
    shutil.rmtree(persist_directory, ignore_errors=True)
    os.remove(filename)


def get_start_date_for_query(vectorstore, run_date: datetime, days_interval: int) -> str:
    """
    Gets the start date to be used in the query to Athena. Returns the maximum of the
    date days_interval in the past and the highest rundate among all the documents
    stored in the vector store.
    """
    # Calculate the date days_interval days before run_date (same as t_minus_thirty_days logic)
    t_minus_days = time.mktime((run_date - timedelta(days=days_interval)).timetuple())

    # Get records with same logic as before
    recs = vectorstore.get(where={
        "rundate": {
            "$gt": t_minus_days
        }
    }, include=["metadatas"])

    # Get max date with same logic
    max_start_date = max([r['rundate'] for r in recs['metadatas']], default=-1)
    max_start_date = max(max_start_date, t_minus_days)
    # TODO: Timezone??
    return datetime.fromtimestamp(max_start_date).strftime('%Y-%m-%d')


def delete_old_candidates(vectorstore, run_date: datetime, days_interval: int):
    """
    Deletes candidate records that are older than the defined days_interval from run_date

    Args:
        vectorstore: The vector store instance
        run_date: Reference date to calculate deletion cutoff
        days_interval: Number of days to look back
    """
    # Calculate cutoff date same way as get_start_date_for_query
    t_minus_days = time.mktime((run_date - timedelta(days=days_interval)).timetuple())

    recs = vectorstore.get(where={
        "rundate": {
            "$lt": t_minus_days
        }
    }, include=["metadatas"])

    if len(recs['ids']) > 0:
        print(
            f"Deleting {len(recs['ids'])} records older than {datetime.fromtimestamp(t_minus_days).strftime('%Y-%m-%d')}")
        vectorstore.delete([i for i in recs['ids']])
    else:
        print("No old records to delete")


def get_runtime_parameters(vectorstore):
    """
    Get runtime parameters with simplified priority:
    1. RUNDATE from environment if provided
    2. Today's date as fallback
    3. DAYS_INTERVAL from environment or default 30

    Returns:
        tuple: (run_date: datetime.date, days_interval: int)
    """
    try:
        # Get days interval from environment variable, default to 30 if not provided
        days_interval = int(os.environ.get('DAYS_INTERVAL', 30))

        # Get rundate - only check env var or use today
        rundate_str = os.environ.get('RUNDATE')
        if rundate_str:
            run_date = datetime.strptime(rundate_str, '%Y-%m-%d').date()
            print(f"Using provided RUNDATE: {run_date}")
        else:
            run_date = datetime.today().date()
            print(f"Using current date: {run_date}")

        print(f"Final parameters - Run Date: {run_date}, Days Interval: {days_interval}")
        return run_date, days_interval

    except ValueError as e:
        print(f"Error parsing parameters: {str(e)}")
        raise


def view_vector_store_content(persist_directory: str):
    """
    Displays the content of the vector store including documents and their metadata

    Args:
        persist_directory (str): Directory where the vector store is located
    """
    # Initialize the vector store
    vectorstore, _ = get_vector_store(persist_directory)

    # Get all documents with their content and metadata
    results = vectorstore.get(include=['documents', 'metadatas'])

    print(f"Total documents in vector store: {len(results['ids'])}\n")

    for idx, (doc, metadata) in enumerate(zip(results['documents'], results['metadatas'])):
        print(f"Document {idx + 1}:")
        print("=" * 50)
        print("Content:")
        print(doc)
        print("\nMetadata:")
        for key, value in metadata.items():
            # Convert timestamp to datetime for readability if it's the rundate field
            if key == 'rundate':
                value = datetime.fromtimestamp(value).strftime('%Y-%m-%d')
            print(f"{key}: {value}")
        print("\n")


def main():
    """
    The main method in this utility script. The entire script does the following:
    1. Gets column metadata from S3. This column metadata contains information like the type of fields, whether to
        include the field in the metadata or content of the Document to be store in the vector store etc.
    2. Queries Athena for suggestions/candidates data
    3. Pre-process the data: Concatenate the reason texts, clean up data
    4. Convert the data into Documents to be indexed in the vector store.
    5. Saves all the documents in to persisted Chroma DB. This creates a directory with SQLite DB
       and other files used by Chroma.
    6. Creates a ZIP of the generated directory and uploads it to S3.
    """
    # Initialize environment variables
    customer = os.environ.get('CUSTOMER')
    awsregion = os.environ.get('AWS_REGION')
    environment = os.environ.get('ENVIRONMENT')
    devsuffix = os.environ.get('SCHEMA_SUFFIX', environment)
    region = os.environ.get('REGION')

    # Log configuration
    print('CUSTOMER    :', customer)
    print('AWSREGION   :', awsregion)
    print('ENVIRONMENT :', environment)
    print('DEVSUFFIX   :', devsuffix)
    print('REGION      :', region)

    # Get S3 and schema information
    s3staging = get_s3staging(customer, region, devsuffix or environment)
    bucket, path = get_s3target(customer, region, devsuffix or environment)
    srcschema = get_schema(customer, devsuffix)

    print('S3STAGING   :', s3staging)
    print('S3TARGET    :', bucket)
    print('SRCSCHEMA   :', srcschema)

    # Initialize AWS services
    boto3.setup_default_session(region_name=awsregion)
    global conn, s3_client
    conn = connect(
        aws_access_key_id=AWS_ACCESS_KEY,
        aws_secret_access_key=AWS_SECRET_KEY,
        aws_session_token=AWS_SESSION_TOKEN,
        s3_staging_dir=s3staging,
        region_name=awsregion,
        schema_name=srcschema
    )
    s3_client = boto3.client('s3')

    # Set up vector store
    vectorstore_location = "./vector_store"
    download_vector_store(
        persist_directory=vectorstore_location,
        target_filename="vector_store",
        s3_bucket=bucket,
        s3_path=path
    )
    # view_vector_store_content(vectorstore_location)

    vector_store, _ = get_vector_store(vectorstore_location)

    # Get runtime parameters
    run_date, days_interval = get_runtime_parameters(vector_store)
    print(f"Using run_date: {run_date}, days_interval: {days_interval}")

    # Use runtime parameters for deletion and query
    delete_old_candidates(vector_store, run_date, days_interval)
    max_run_date = get_start_date_for_query(vector_store, run_date, days_interval)
    # TODO: Change the query
    query = f"""
        SELECT *, cast (reasons as json) as json_reasons 
        FROM candidate_chatbot_v 
        WHERE date_parse(rundate,'%Y-%m-%d') > DATE('{max_run_date}')
        AND date_parse(rundate,'%Y-%m-%d') <= DATE('{run_date}')  -- Added condition to avoid future dates
        ORDER BY rundate ASC, suggestioncandidateuid DESC 
        """
    data = get_table(query)

    if len(data) != 0:
        # Process data and generate metadata
        preprocessed_values = preprocess_data_values(data)
        generate_metadata_csv(preprocessed_values, vector_store, bucket, path)

        # Process documents
        column_meta = read_column_metadata(bucket, path)
        final_data = preprocess_data(preprocessed_values, column_meta)
        docs = prepare_documents_for_vector_store(final_data, column_meta)

        # Update vector store
        create_vector_store(docs, persist_directory=vectorstore_location)

        # Upload results
        upload_vector_store(
            persist_directory=vectorstore_location,
            target_filename="vector_store",
            s3_bucket=bucket,
            s3_path=path
        )
    else:
        print("No candidates found")

    # Cleanup
    cleanup_files(vectorstore_location, "vector_store.zip")


if __name__ == "__main__":
    main()
