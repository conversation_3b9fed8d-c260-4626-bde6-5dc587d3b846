#!/bin/bash

# Set the env variables AWS_ACCESS_KEY_ID, AWS_REGION, AWS_SECRET_ACCESS_KEY, AWS_SESSION_TOKEN, AZURE_OPENAI_API_KEY,
# AZURE_OPENAI_ENDPOINT, CUSTOMER, ENVIRONMENT, REGION

# Check if virtual environment folder exists, if not create it
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# Activate the virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install Python dependencies
pip install --upgrade boto3 PyAthena pandas jinja2 beautifulsoup4 chromadb==0.5.5 langchain==0.2.11 langchain-community==0.2.10 \
    langchain-openai==0.1.17 python-dotenv

sqlite_version=$(python -c "import sqlite3; print(sqlite3.sqlite_version)")
major_version=$(echo $sqlite_version | cut -d "." -f 1)
minor_version=$(echo $sqlite_version | cut -d "." -f 2)

# Install pysqlite only if sqlite version is less than 3.35.0
if [ "$major_version" -lt "3" ] || ( [ "$major_version" -eq "3" ] && [ "$minor_version" -lt "35" ] ); then
    pip install --upgrade pysqlite3-binary
fi

SCRIPT_DIR=`dirname $0`

python3 $SCRIPT_DIR/create_vector_store.py

# Deactivate the virtual environment
deactivate
