FROM databricksruntime/standard:7.x

# GETTING BUILD METADATA
ARG GIT_COMMIT
ARG GIT_URL
ARG GIT_BRANCH
ARG BUILD_NUMBER
ARG BUILD_URL

# ADDING BUILD METADATA AS ENV VARIABLES
ENV GIT_COMMIT=${GIT_COMMIT}
ENV GIT_URL=${GIT_URL}
ENV GIT_BRANCH=${GIT_BRANCH}
ENV BUILD_NUMBER=${BUILD_NUMBER}
ENV BUILD_URL=${BUILD_URL}

LABEL MAINTAINER="<EMAIL> , <EMAIL>" \
      GIT_COMMIT="${GIT_COMMIT}" \
      GIT_URL="${GIT_URL}" \
      GIT_BRANCH="${GIT_BRANCH}" \
      BUILD_NUMBER="${BUILD_NUMBER}" \
      BUILD_URL="${BUILD_URL}"

ENV MLFLOW_TRACKING_URI=databricks
ENV DATABRICKS_HOST=https://aktana-poc.cloud.databricks.com
ENV DATABRICKS_TOKEN=************************************

RUN /databricks/conda/envs/dcs-minimal/bin/pip install snowflake-connector-python==2.4.3
RUN /databricks/conda/envs/dcs-minimal/bin/pip install statsmodels==0.12
RUN /databricks/conda/envs/dcs-minimal/bin/pip install pyarrow==3.0.0
RUN /databricks/conda/envs/dcs-minimal/bin/pip install pandas==1.2.4

RUN mkdir /learning
COPY . /learning
