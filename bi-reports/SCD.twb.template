<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20211.21.0326.1554                               -->
<workbook source-build='2020.4.1 (20204.21.0114.0916)' source-platform='mac' version='18.1' xml:base='https://10ay.online.tableau.com' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <CustomReferenceLineTooltips />
    <ISO8601DefaultCalendarPref />
    <_.fcp.MarkAnimation.true...MarkAnimation />
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <_.fcp.SetMembershipControl.true...SetMembershipControl />
    <SheetIdentifierTracking />
    <SortTagCleanup />
    <WindowsPersistSimpleIdentifiers />
    <WorksheetBackgroundTransparency />
    <ZoneBackgroundTransparency />
  </document-format-change-manifest>
  <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/workbooks/SCD?rev=2.0' id='${name}' path='/t/aktana/workbooks' revision='2.0' site='${site}' />
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <style>
    <_.fcp.MarkAnimation.true...style-rule element='animation'>
      <format attr='animation-on' value='ao-off' />
    </_.fcp.MarkAnimation.true...style-rule>
  </style>
  <datasources>
    <datasource hasconnection='false' inline='true' name='Parameters' version='18.1'>
      <aliases enabled='yes' />
      <column caption='Top N Label Types' datatype='integer' name='[Parameter 1]' param-domain-type='range' role='measure' type='quantitative' value='3'>
        <calculation class='tableau' formula='3' />
        <range max='16' min='1' />
      </column>
    </datasource>
    <datasource caption='scd_published_vs_dse_published' inline='true' name='sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li' version='18.1'>
      <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/datasources/scd_published_vs_dse_published?rev=1.4' id='${name}' path='/t/${site}/datasources' revision='2.0' site='${site}' />
      <connection channel='https' class='sqlproxy' dbname='scd_published_vs_dse_published' directory='/dataserver' port='443' server='10ay.online.tableau.com' server-oauth='' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <calculations>
          <calculation column='[Calculation_106890129488527402]' formula='IF [HCP_COUNT] = NULL or [HCP_COUNT] = 0 THEN 0 &#10;ELSEIF [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] = NULL or [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] = 0 THEN 0&#10;ELSE ROUND((([HCP_COUNT]/[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]) * 100), 0)&#10;END' />
          <calculation column='[Calculation_3496341431263809540]' formula='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
          <calculation column='[Calculation_3496341431263932421]' formula='[HCP_COUNT]' />
        </calculations>
        <metadata-records>
          <metadata-record class='column'>
            <remote-name>ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>10</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ACTION_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACTION_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTION_TYPE</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_106890129488527402</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_106890129488527402]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_106890129488527402</remote-alias>
            <ordinal>16</ordinal>
            <layered>true</layered>
            <caption>scd_by_dse_hcp_count</caption>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [HCP_COUNT] = NULL or [HCP_COUNT] = 0 THEN 0 
ELSEIF [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] = NULL or [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] = 0 THEN 0
ELSE ROUND((([HCP_COUNT]/[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]) * 100), 0)
END&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_3496341431263809540</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_3496341431263809540]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_3496341431263809540</remote-alias>
            <ordinal>17</ordinal>
            <layered>true</layered>
            <caption>ALL</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_3496341431263932421</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_3496341431263932421]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_3496341431263932421</remote-alias>
            <ordinal>18</ordinal>
            <layered>true</layered>
            <caption>SCD</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;[HCP_COUNT]&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>20</remote-type>
            <local-name>[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>11</ordinal>
            <layered>true</layered>
            <caption>Hcp Count (Vw Dse Suggestion Hcp Count Rpt)</caption>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>HCP_COUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[HCP_COUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_COUNT</remote-alias>
            <ordinal>6</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LABEL_TYPE_EXT_ID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[LABEL_TYPE_EXT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LABEL_TYPE_EXT_ID</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>8</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_ID</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PRODUCT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>9</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>7</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_DATE</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SUGGESTION_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_DATE</remote-alias>
            <ordinal>0</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USE_CASE_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USE_CASE_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USE_CASE_NAME</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_CASE_INSENSITIVE_CONTAINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORT_ANALYTIC_FUNCTIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='scd_published_vs_dse_published' inline='true' version='18.1' xml:base='https://10ay.online.tableau.com' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/datasources/scd_published_vs_dse_published?rev=1.4' id='${name}' path='/t/${site}/datasources' revision='2.0' site='${site}' />
  <connection channel='https' class='sqlproxy' dbname='scd_published_vs_dse_published' directory='/dataserver' port='443' server='10ay.online.tableau.com'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <cols>
      <map key='[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[ACTION_TYPE]' value='[sqlproxy].[ACTION_TYPE]' />
      <map key='[Calculation_106890129488527402]' value='[sqlproxy].[Calculation_106890129488527402]' />
      <map key='[Calculation_3496341431263809540]' value='[sqlproxy].[Calculation_3496341431263809540]' />
      <map key='[Calculation_3496341431263932421]' value='[sqlproxy].[Calculation_3496341431263932421]' />
      <map key='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[HCP_COUNT]' value='[sqlproxy].[HCP_COUNT]' />
      <map key='[LABEL_TYPE_EXT_ID]' value='[sqlproxy].[LABEL_TYPE_EXT_ID]' />
      <map key='[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[PRODUCT_ID]' value='[sqlproxy].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[PRODUCT_NAME]' value='[sqlproxy].[PRODUCT_NAME]' />
      <map key='[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[SUGGESTION_DATE]' value='[sqlproxy].[SUGGESTION_DATE]' />
      <map key='[USE_CASE_NAME]' value='[sqlproxy].[USE_CASE_NAME]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='Count' datatype='string' default-type='nominal' name='[:Measure Names]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <aliases>
      <alias key='&quot;[scd_published_vs_dse_published].[sum:HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]&quot;' value='All' />
      <alias key='&quot;[scd_published_vs_dse_published].[sum:HCP_COUNT:qk]&quot;' value='SCD' />
    </aliases>
  </column>
  <column aggregation='Sum' caption='scd_by_dse_hcp_count' datatype='real' default-type='quantitative' name='[Calculation_106890129488527402]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='IF [HCP_COUNT] = NULL or [HCP_COUNT] = 0 THEN 0 &\#10;ELSEIF [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] = NULL or [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] = 0 THEN 0&\#10;ELSE ROUND((([HCP_COUNT]/[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]) * 100), 0)&\#10;END' />
  </column>
  <column aggregation='Sum' caption='ALL' datatype='integer' default-type='quantitative' name='[Calculation_3496341431263809540]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
  </column>
  <column aggregation='Sum' caption='SCD' datatype='integer' default-type='quantitative' name='[Calculation_3496341431263932421]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='[HCP_COUNT]' />
  </column>
  <column aggregation='Sum' caption='Hcp Count (Vw Dse Suggestion Hcp Count Rpt)' datatype='integer' default-type='quantitative' name='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' name='[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <column-instance column='[Calculation_3496341431263809540]' derivation='Sum' name='[sum:Calculation_3496341431263809540:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_3496341431263932421]' derivation='Sum' name='[sum:Calculation_3496341431263932421:qk]' pivot='key' type='quantitative' />
  <column-instance column='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' derivation='Sum' name='[sum:HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]' pivot='key' type='quantitative' />
  <column-instance column='[HCP_COUNT]' derivation='Sum' name='[sum:HCP_COUNT:qk]' pivot='key' type='quantitative' />
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[:Measure Names]' palette='superfishel_stone_10_0' type='palette'>
        <map to='\#4e79a7'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:DSE_HCP_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:PRODUCT_ID (Custom SQL Query):qk]&quot;</bucket>
        </map>
        <map to='\#6388b4'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:Calculation_3496341431196352513:qk]&quot;</bucket>
        </map>
        <map to='\#64cdcc'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:Calculation_3496341431263809540:qk]&quot;</bucket>
        </map>
        <map to='\#64cdcc'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:PRODUCT_ID:qk]&quot;</bucket>
        </map>
        <map to='\#90728f'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:HCP_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#9c755f'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:SCD_IN_PRODUCT_CONFIG_KEY:qk]&quot;</bucket>
        </map>
        <map to='\#a0cbe8'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:SCD_HCP_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#b07aa1'>
          <bucket>&quot;[scd_published_vs_dse_published].[none:RANGE_MIN:qk]&quot;</bucket>
        </map>
        <map to='\#b07aa1'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:RANGE_MIN:qk]&quot;</bucket>
        </map>
        <map to='\#b173a0'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:Calculation_3496341431263932421:qk]&quot;</bucket>
        </map>
        <map to='\#bab0ac'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:Calculation_106890129292181510:qk]&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;[scd_published_vs_dse_published].[__tableau_internal_object_id__].[cnt:_0143C679185C465C82B39D85EA5BD633:qk]&quot;</bucket>
        </map>
        <map to='\#edc948'>
          <bucket>&quot;[scd_published_vs_dse_published].[none:RANGE_MAX:qk]&quot;</bucket>
        </map>
        <map to='\#edc948'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:RANGE_MAX:qk]&quot;</bucket>
        </map>
        <map to='\#ff9da7'>
          <bucket>&quot;[scd_published_vs_dse_published].[sum:SCD_BI_CONTROL_RANGE_KEY:qk]&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT' id='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='hyper' version='0.0.0'>
  </primary-dialect>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>true</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
              <attribute datatype='string' name='update-time'>&quot;3/30/2021 7:09:56 AM&quot;</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='sunday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[:Measure Names]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <aliases>
          <alias key='&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]&quot;' value='All' />
          <alias key='&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:HCP_COUNT:qk]&quot;' value='SCD' />
        </aliases>
      </column>
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' caption='scd_by_dse_hcp_count' datatype='real' default-type='quantitative' layered='true' name='[Calculation_106890129488527402]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='IF [HCP_COUNT] = NULL or [HCP_COUNT] = 0 THEN 0 &#10;ELSEIF [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] = NULL or [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] = 0 THEN 0&#10;ELSE ROUND((([HCP_COUNT]/[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]) * 100), 0)&#10;END' />
      </column>
      <column aggregation='Sum' caption='ALL' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_3496341431263809540]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      </column>
      <column aggregation='Sum' caption='SCD' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_3496341431263932421]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='[HCP_COUNT]' />
      </column>
      <column aggregation='Sum' caption='Hcp Count (Vw Dse Suggestion Hcp Count Rpt)' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[LABEL_TYPE_EXT_ID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='integer' default-type='ordinal' layered='true' name='[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='integer' default-type='ordinal' layered='true' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <column-instance column='[Calculation_3496341431263809540]' derivation='Sum' name='[sum:Calculation_3496341431263809540:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_3496341431263932421]' derivation='Sum' name='[sum:Calculation_3496341431263932421:qk]' pivot='key' type='quantitative' />
      <column-instance column='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' derivation='Sum' name='[sum:HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]' pivot='key' type='quantitative' />
      <column-instance column='[HCP_COUNT]' derivation='Sum' name='[sum:HCP_COUNT:qk]' pivot='key' type='quantitative' />
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[:Measure Names]' palette='superfishel_stone_10_0' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:DSE_HCP_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:PRODUCT_ID (Custom SQL Query):qk]&quot;</bucket>
            </map>
            <map to='#6388b4'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431196352513:qk]&quot;</bucket>
            </map>
            <map to='#64cdcc'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263809540:qk]&quot;</bucket>
            </map>
            <map to='#64cdcc'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:PRODUCT_ID:qk]&quot;</bucket>
            </map>
            <map to='#90728f'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:HCP_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#9c755f'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:SCD_IN_PRODUCT_CONFIG_KEY:qk]&quot;</bucket>
            </map>
            <map to='#a0cbe8'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:SCD_HCP_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:RANGE_MIN:qk]&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:RANGE_MIN:qk]&quot;</bucket>
            </map>
            <map to='#b173a0'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263932421:qk]&quot;</bucket>
            </map>
            <map to='#bab0ac'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_106890129292181510:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[__tableau_internal_object_id__].[cnt:_0143C679185C465C82B39D85EA5BD633:qk]&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:RANGE_MAX:qk]&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:RANGE_MAX:qk]&quot;</bucket>
            </map>
            <map to='#ff9da7'>
              <bucket>&quot;[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:SCD_BI_CONTROL_RANGE_KEY:qk]&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT' id='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_BF24F5D9AE9F49C18351B8A1E6B601BD'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
    <datasource caption='scd_hcp_count_per_threshold' inline='true' name='sqlproxy.16438650nafte01d7oe5u1vstoux' version='18.1'>
      <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/datasources/scd_hcp_count_per_threshold?rev=1.0' id='${name}' path='/t/${site}/datasources' revision='2.0' site='${site}' />
      <connection channel='https' class='sqlproxy' dbname='scd_hcp_count_per_threshold' directory='/dataserver' port='443' server='10ay.online.tableau.com' server-oauth='' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <calculations>
          <calculation column='[Calculation_5079849300315119616]' formula='[SCD_TRIGGER_DATE]' />
        </calculations>
        <metadata-records>
          <metadata-record class='column'>
            <remote-name>Calculation_5079849300315119616</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_5079849300315119616]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_5079849300315119616</remote-alias>
            <ordinal>10</ordinal>
            <layered>true</layered>
            <caption>SUGGESTION_DATE</caption>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;[SCD_TRIGGER_DATE]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>HCP_COUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[HCP_COUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_COUNT</remote-alias>
            <ordinal>0</ordinal>
            <layered>true</layered>
            <family>VW_HCP_COUNT_PER_THRESHOLD_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>PERCENTAGE</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PERCENTAGE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PERCENTAGE</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <family>VW_HCP_COUNT_PER_THRESHOLD_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_ID</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PRODUCT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <family>VW_HCP_COUNT_PER_THRESHOLD_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <family>VW_HCP_COUNT_PER_THRESHOLD_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCD_TRIGGER_DATE</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SCD_TRIGGER_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCD_TRIGGER_DATE</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <family>VW_HCP_COUNT_PER_THRESHOLD_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USE_CASE_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USE_CASE_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USE_CASE_NAME</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <family>VW_HCP_COUNT_PER_THRESHOLD_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_CASE_INSENSITIVE_CONTAINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORT_ANALYTIC_FUNCTIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='scd_hcp_count_per_threshold' inline='true' version='18.1' xml:base='https://10ay.online.tableau.com' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/datasources/scd_hcp_count_per_threshold?rev=1.0' id='${name}' path='/t/${site}/datasources' revision='2.0' site='${site}' />
  <connection channel='https' class='sqlproxy' dbname='scd_hcp_count_per_threshold' directory='/dataserver' port='443' server='10ay.online.tableau.com'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <cols>
      <map key='[Calculation_5079849300315119616]' value='[sqlproxy].[Calculation_5079849300315119616]' />
      <map key='[HCP_COUNT]' value='[sqlproxy].[HCP_COUNT]' />
      <map key='[PERCENTAGE]' value='[sqlproxy].[PERCENTAGE]' />
      <map key='[PRODUCT_ID]' value='[sqlproxy].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME]' value='[sqlproxy].[PRODUCT_NAME]' />
      <map key='[SCD_TRIGGER_DATE]' value='[sqlproxy].[SCD_TRIGGER_DATE]' />
      <map key='[USE_CASE_NAME]' value='[sqlproxy].[USE_CASE_NAME]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='Year' caption='SUGGESTION_DATE' datatype='date' default-type='ordinal' name='[Calculation_5079849300315119616]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
    <calculation class='tableau' formula='[SCD_TRIGGER_DATE]' />
  </column>
  <column aggregation='Count' datatype='integer' default-type='ordinal' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_HCP_COUNT_PER_THRESHOLD_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='VW_HCP_COUNT_PER_THRESHOLD_RPT' id='VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='hyper' version='0.0.0'>
  </primary-dialect>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>true</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
              <attribute datatype='string' name='update-time'>&quot;3/31/2021 11:08:10 PM&quot;</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='sunday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='Year' caption='SUGGESTION_DATE' datatype='date' default-type='ordinal' layered='true' name='[Calculation_5079849300315119616]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
        <calculation class='tableau' formula='[SCD_TRIGGER_DATE]' />
      </column>
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[PERCENTAGE]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='integer' default-type='ordinal' layered='true' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SCD_TRIGGER_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_HCP_COUNT_PER_THRESHOLD_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='VW_HCP_COUNT_PER_THRESHOLD_RPT' id='VW_HCP_COUNT_PER_THRESHOLD_RPT (SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT)_94820F1D38EC4BEFA23537B29F5EB0CD'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
    <datasource caption='scd_published_vs_dse_published_with_actiontype' inline='true' name='sqlproxy.176n3kc0h2wiuu14rjs660rnxot4' version='18.1'>
      <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/datasources/scd_published_vs_dse_published_with_actiontype?rev=1.4' id='${name}' path='/t/${site}/datasources' revision='2.0' site='${site}' />
      <connection channel='https' class='sqlproxy' dbname='scd_published_vs_dse_published_with_actiontype' directory='/dataserver' port='443' server='10ay.online.tableau.com' server-oauth='' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <calculations>
          <calculation column='[Calculation_106890129499590702]' formula='ROUND((([HCP_COUNT] / [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)' />
          <calculation column='[SCD (copy)_106890129500364847]' formula='ROUND((([HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] / [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)' />
        </calculations>
        <metadata-records>
          <metadata-record class='column'>
            <remote-name>ACTION_TYPE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACTION_TYPE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTION_TYPE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>15</ordinal>
            <layered>true</layered>
            <caption>Action Type (Vw Dse Scd Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>10</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>22</ordinal>
            <layered>true</layered>
            <caption>Action Type (Vw Dse Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ACTION_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACTION_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTION_TYPE</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_106890129499590702</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_106890129499590702]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_106890129499590702</remote-alias>
            <ordinal>28</ordinal>
            <layered>true</layered>
            <caption>SCD</caption>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;ROUND((([HCP_COUNT] / [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>20</remote-type>
            <local-name>[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>18</ordinal>
            <layered>true</layered>
            <caption>Hcp Count (Vw Dse Scd Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>20</remote-type>
            <local-name>[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>11</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>20</remote-type>
            <local-name>[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>23</ordinal>
            <layered>true</layered>
            <caption>Hcp Count (Vw Dse Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>HCP_COUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[HCP_COUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_COUNT</remote-alias>
            <ordinal>6</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LABEL_TYPE_EXT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[LABEL_TYPE_EXT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LABEL_TYPE_EXT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>17</ordinal>
            <layered>true</layered>
            <caption>Label Type Ext Id (Vw Dse Scd Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LABEL_TYPE_EXT_ID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[LABEL_TYPE_EXT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LABEL_TYPE_EXT_ID</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>13</ordinal>
            <layered>true</layered>
            <caption>Product Id (Vw Dse Scd Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>8</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>20</ordinal>
            <layered>true</layered>
            <caption>Product Id (Vw Dse Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>PRODUCT_ID</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PRODUCT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>14</ordinal>
            <layered>true</layered>
            <caption>Product Name (Vw Dse Scd Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>9</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>21</ordinal>
            <layered>true</layered>
            <caption>Product Name (Vw Dse Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>SCD (copy)_106890129500364847</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[SCD (copy)_106890129500364847]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCD (copy)_106890129500364847</remote-alias>
            <ordinal>29</ordinal>
            <layered>true</layered>
            <caption>All</caption>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;ROUND((([HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] / [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_DATE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SUGGESTION_DATE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_DATE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>12</ordinal>
            <layered>true</layered>
            <caption>Suggestion Date (Vw Dse Scd Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>7</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>19</ordinal>
            <layered>true</layered>
            <caption>Suggestion Date (Vw Dse Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_DATE</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SUGGESTION_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_DATE</remote-alias>
            <ordinal>0</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)</remote-alias>
            <ordinal>16</ordinal>
            <layered>true</layered>
            <caption>Use Case Name (Vw Dse Scd Suggestion Hcp Count Rpt1)</caption>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USE_CASE_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USE_CASE_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USE_CASE_NAME</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_CASE_INSENSITIVE_CONTAINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORT_ANALYTIC_FUNCTIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='scd_published_vs_dse_published_with_actiontype' inline='true' version='18.1' xml:base='https://10ay.online.tableau.com' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/datasources/scd_published_vs_dse_published_with_actiontype?rev=1.4' id='${name}' path='/t/${site}/datasources' revision='2.0' site='${site}' />
  <connection channel='https' class='sqlproxy' dbname='scd_published_vs_dse_published_with_actiontype' directory='/dataserver' port='443' server='10ay.online.tableau.com'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <cols>
      <map key='[ACTION_TYPE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[ACTION_TYPE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[ACTION_TYPE]' value='[sqlproxy].[ACTION_TYPE]' />
      <map key='[Calculation_106890129499590702]' value='[sqlproxy].[Calculation_106890129499590702]' />
      <map key='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[HCP_COUNT]' value='[sqlproxy].[HCP_COUNT]' />
      <map key='[LABEL_TYPE_EXT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[LABEL_TYPE_EXT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[LABEL_TYPE_EXT_ID]' value='[sqlproxy].[LABEL_TYPE_EXT_ID]' />
      <map key='[PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[PRODUCT_ID]' value='[sqlproxy].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[PRODUCT_NAME]' value='[sqlproxy].[PRODUCT_NAME]' />
      <map key='[SCD (copy)_106890129500364847]' value='[sqlproxy].[SCD (copy)_106890129500364847]' />
      <map key='[SUGGESTION_DATE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[SUGGESTION_DATE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[SUGGESTION_DATE]' value='[sqlproxy].[SUGGESTION_DATE]' />
      <map key='[USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' value='[sqlproxy].[USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' />
      <map key='[USE_CASE_NAME]' value='[sqlproxy].[USE_CASE_NAME]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='Count' caption='Action Type (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' name='[ACTION_TYPE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Action Type (Vw Dse Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' name='[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Sum' caption='SCD' datatype='real' default-type='quantitative' name='[Calculation_106890129499590702]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='ROUND((([HCP_COUNT] / [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)' />
  </column>
  <column aggregation='Sum' caption='Hcp Count (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='integer' default-type='quantitative' name='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' caption='Hcp Count (Vw Dse Suggestion Hcp Count Rpt1)' datatype='integer' default-type='quantitative' name='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Label Type Ext Id (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' name='[LABEL_TYPE_EXT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Id (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='integer' default-type='ordinal' name='[PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Product Id (Vw Dse Suggestion Hcp Count Rpt1)' datatype='integer' default-type='ordinal' name='[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[PRODUCT_ID]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Product Name (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' name='[PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Name (Vw Dse Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' name='[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Sum' caption='All' datatype='real' default-type='quantitative' name='[SCD (copy)_106890129500364847]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='ROUND((([HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] / [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)' />
  </column>
  <column aggregation='Year' caption='Suggestion Date (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='date' default-type='ordinal' name='[SUGGESTION_DATE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' caption='Suggestion Date (Vw Dse Suggestion Hcp Count Rpt1)' datatype='date' default-type='ordinal' name='[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' caption='Use Case Name (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' name='[USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='dse_published_vs_scd_publisged_by_actiontype' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <column-instance column='[Calculation_106890129499590702]' derivation='Attribute' name='[attr:Calculation_106890129499590702:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_106890129499590702]' derivation='Avg' name='[avg:Calculation_106890129499590702:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_106890129499590702]' derivation='Sum' name='[sum:Calculation_106890129499590702:qk]' pivot='key' type='quantitative' />
  <column-instance column='[PRODUCT_ID]' derivation='Sum' name='[sum:PRODUCT_ID:qk]' pivot='key' type='quantitative' />
  <column-instance column='[SCD (copy)_106890129500364847]' derivation='Sum' name='[sum:SCD (copy)_106890129500364847:qk]' pivot='key' type='quantitative' />
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[:Measure Names]' palette='tableau-20' type='palette'>
        <map to='\#17becf'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:SCD (copy)_106890129500364847:qk]&quot;</bucket>
        </map>
        <map to='\#499894'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:SCD_HCP_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#4e79a7'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype]&quot;</bucket>
        </map>
        <map to='\#4e79a7'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:scd_total_hcp_count (copy)_106890129499299885:qk]&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:PRODUCT_ID (Custom SQL Query1):qk]&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:Calculation_106890129271197696:qk]&quot;</bucket>
        </map>
        <map to='\#86bcb6'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:TOTAL_DSE_HCP_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#8cd17d'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[none:RANGE_MAX:qk]&quot;</bucket>
        </map>
        <map to='\#8cd17d'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:RANGE_MAX:qk]&quot;</bucket>
        </map>
        <map to='\#9467bd'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:Calculation_106890129499590702:qk]&quot;</bucket>
        </map>
        <map to='\#a0cbe8'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[__tableau_internal_object_id__].[cnt:_7FAB931817534B06B9F22DF45BC2C644:qk]&quot;</bucket>
        </map>
        <map to='\#b07aa1'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:Calculation_106890129271480321:qk]&quot;</bucket>
        </map>
        <map to='\#b6992d'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[none:RANGE_MIN:qk]&quot;</bucket>
        </map>
        <map to='\#b6992d'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:RANGE_MIN:qk]&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:TOTAL_SCD_HCP_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[attr:Calculation_106890129499590702:qk]&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[avg:Calculation_106890129499590702:qk]&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[none:Calculation_106890129494589483:qk]&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:Calculation_106890129494589483:qk]&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:DSE_HCP_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#ffbe7d'>
          <bucket>&quot;[scd_published_vs_dse_published_with_actiontype].[sum:PRODUCT_ID:qk]&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='dse_published_vs_scd_publisged_by_actiontype' id='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='hyper' version='0.0.0'>
  </primary-dialect>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>true</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
              <attribute datatype='string' name='update-time'>&quot;3/30/2021 7:09:16 AM&quot;</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='sunday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='Count' caption='Action Type (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Action Type (Vw Dse Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' caption='SCD' datatype='real' default-type='quantitative' layered='true' name='[Calculation_106890129499590702]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='ROUND((([HCP_COUNT] / [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)' />
      </column>
      <column aggregation='Sum' caption='Hcp Count (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' caption='Hcp Count (Vw Dse Suggestion Hcp Count Rpt1)' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Label Type Ext Id (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' layered='true' name='[LABEL_TYPE_EXT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[LABEL_TYPE_EXT_ID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Id (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='integer' default-type='ordinal' layered='true' name='[PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Product Id (Vw Dse Suggestion Hcp Count Rpt1)' datatype='integer' default-type='ordinal' layered='true' name='[PRODUCT_ID (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[PRODUCT_ID]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Product Name (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Name (Vw Dse Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' caption='All' datatype='real' default-type='quantitative' layered='true' name='[SCD (copy)_106890129500364847]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='ROUND((([HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] / [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)' />
      </column>
      <column aggregation='Year' caption='Suggestion Date (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' caption='Suggestion Date (Vw Dse Suggestion Hcp Count Rpt1)' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' caption='Use Case Name (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='dse_published_vs_scd_publisged_by_actiontype' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <column-instance column='[Calculation_106890129499590702]' derivation='Attribute' name='[attr:Calculation_106890129499590702:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_106890129499590702]' derivation='Avg' name='[avg:Calculation_106890129499590702:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_106890129499590702]' derivation='Sum' name='[sum:Calculation_106890129499590702:qk]' pivot='key' type='quantitative' />
      <column-instance column='[PRODUCT_ID]' derivation='Sum' name='[sum:PRODUCT_ID:qk]' pivot='key' type='quantitative' />
      <column-instance column='[SCD (copy)_106890129500364847]' derivation='Sum' name='[sum:SCD (copy)_106890129500364847:qk]' pivot='key' type='quantitative' />
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[:Measure Names]' palette='tableau-20' type='palette'>
            <map to='#17becf'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:SCD (copy)_106890129500364847:qk]&quot;</bucket>
            </map>
            <map to='#499894'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:SCD_HCP_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#4e79a7'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4]&quot;</bucket>
            </map>
            <map to='#4e79a7'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:scd_total_hcp_count (copy)_106890129499299885:qk]&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:PRODUCT_ID (Custom SQL Query1):qk]&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129271197696:qk]&quot;</bucket>
            </map>
            <map to='#86bcb6'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:TOTAL_DSE_HCP_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#8cd17d'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:RANGE_MAX:qk]&quot;</bucket>
            </map>
            <map to='#8cd17d'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:RANGE_MAX:qk]&quot;</bucket>
            </map>
            <map to='#9467bd'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]&quot;</bucket>
            </map>
            <map to='#a0cbe8'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[__tableau_internal_object_id__].[cnt:_7FAB931817534B06B9F22DF45BC2C644:qk]&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129271480321:qk]&quot;</bucket>
            </map>
            <map to='#b6992d'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:RANGE_MIN:qk]&quot;</bucket>
            </map>
            <map to='#b6992d'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:RANGE_MIN:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:TOTAL_SCD_HCP_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[attr:Calculation_106890129499590702:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[avg:Calculation_106890129499590702:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:Calculation_106890129494589483:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129494589483:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:DSE_HCP_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#ffbe7d'>
              <bucket>&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:PRODUCT_ID:qk]&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='dse_published_vs_scd_publisged_by_actiontype' id='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_8F52DB9872934F7198032ED11F9961A9'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
    <datasource caption='scd_published_by_usecase' inline='true' name='sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5' version='18.1'>
      <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/datasources/scd_published_by_usecase?rev=1.4' id='${name}' path='/t/${site}/datasources' revision='2.0' site='${site}' />
      <connection channel='https' class='sqlproxy' dbname='scd_published_by_usecase' directory='/dataserver' port='443' server='10ay.online.tableau.com' server-oauth='' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <metadata-records>
          <metadata-record class='column'>
            <remote-name>ACTION_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACTION_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTION_TYPE</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>HCP_COUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[HCP_COUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_COUNT</remote-alias>
            <ordinal>6</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LABEL_TYPE_EXT_ID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[LABEL_TYPE_EXT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LABEL_TYPE_EXT_ID</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>PRODUCT_ID</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PRODUCT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_DATE</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SUGGESTION_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_DATE</remote-alias>
            <ordinal>0</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USE_CASE_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USE_CASE_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USE_CASE_NAME</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_CASE_INSENSITIVE_CONTAINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORT_ANALYTIC_FUNCTIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='scd_published_by_usecase' inline='true' version='18.1' xml:base='https://10ay.online.tableau.com' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/datasources/scd_published_by_usecase?rev=1.4' id='${name}' path='/t/${site}/datasources' revision='2.0' site='${site}' />
  <connection channel='https' class='sqlproxy' dbname='scd_published_by_usecase' directory='/dataserver' port='443' server='10ay.online.tableau.com'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <cols>
      <map key='[ACTION_TYPE]' value='[sqlproxy].[ACTION_TYPE]' />
      <map key='[HCP_COUNT]' value='[sqlproxy].[HCP_COUNT]' />
      <map key='[LABEL_TYPE_EXT_ID]' value='[sqlproxy].[LABEL_TYPE_EXT_ID]' />
      <map key='[PRODUCT_ID]' value='[sqlproxy].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME]' value='[sqlproxy].[PRODUCT_NAME]' />
      <map key='[SUGGESTION_DATE]' value='[sqlproxy].[SUGGESTION_DATE]' />
      <map key='[USE_CASE_NAME]' value='[sqlproxy].[USE_CASE_NAME]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' name='[LABEL_TYPE_EXT_ID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <column-instance column='[LABEL_TYPE_EXT_ID]' derivation='None' name='[none:LABEL_TYPE_EXT_ID:nk]' pivot='key' type='nominal' />
  <group caption='LABEL_TYPE_EXT_ID Set' name='[LABEL_TYPE_EXT_ID Set]' name-style='unqualified' user:ui-builder='filter-group'>
    <groupfilter count='[Parameters].[Parameter 1]' end='top' function='end' units='records' user:ui-marker='end' user:ui-top-by-field='true'>
      <groupfilter direction='DESC' expression='SUM([HCP_COUNT])' function='order' user:ui-marker='order'>
        <groupfilter function='except' user:ui-domain='database' user:ui-enumeration='exclusive' user:ui-marker='enumerate'>
          <groupfilter function='level-members' level='[LABEL_TYPE_EXT_ID]' />
          <groupfilter function='member' level='[LABEL_TYPE_EXT_ID]' member='&quot;All&quot;' />
        </groupfilter>
      </groupfilter>
    </groupfilter>
  </group>
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.676223' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.323777' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[none:LABEL_TYPE_EXT_ID:nk]' palette='tableau20_10_0' type='palette'>
        <map to='\#4e79a7'>
          <bucket>&quot;Account Sales Volume Increase&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;All&quot;</bucket>
        </map>
        <map to='\#a0cbe8'>
          <bucket>&quot;Brick Sales Volume Decrease&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;Brick Sales Volume Increase&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <datasource-dependencies datasource='Parameters'>
    <column caption='Top N Label Types' datatype='integer' name='[Parameter 1]' param-domain-type='range' role='measure' type='quantitative' value='3'>
      <calculation class='tableau' formula='3' />
      <range max='16' min='1' />
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT' id='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='hyper' version='0.0.0'>
  </primary-dialect>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>true</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
              <attribute datatype='string' name='update-time'>&quot;3/30/2021 5:28:37 PM&quot;</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='sunday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[LABEL_TYPE_EXT_ID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[PRODUCT_ID]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <column-instance column='[LABEL_TYPE_EXT_ID]' derivation='None' name='[none:LABEL_TYPE_EXT_ID:nk]' pivot='key' type='nominal' />
      <group caption='LABEL_TYPE_EXT_ID Set' layered='true' name='[LABEL_TYPE_EXT_ID Set]' name-style='unqualified' user:ui-builder='filter-group'>
        <groupfilter count='[Parameters].[Parameter 1]' end='top' function='end' units='records' user:ui-marker='end' user:ui-top-by-field='true'>
          <groupfilter direction='DESC' expression='SUM([HCP_COUNT])' function='order' user:ui-marker='order'>
            <groupfilter function='except' user:ui-domain='database' user:ui-enumeration='exclusive' user:ui-marker='enumerate'>
              <groupfilter function='level-members' level='[LABEL_TYPE_EXT_ID]' />
              <groupfilter function='member' level='[LABEL_TYPE_EXT_ID]' member='&quot;All&quot;' />
            </groupfilter>
          </groupfilter>
        </groupfilter>
      </group>
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.682968' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.317032' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[none:LABEL_TYPE_EXT_ID:nk]' palette='tableau20_10_0' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Account Sales Volume Increase&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;All&quot;</bucket>
            </map>
            <map to='#a0cbe8'>
              <bucket>&quot;Brick Sales Volume Decrease&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Brick Sales Volume Increase&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Top N Label Types' datatype='integer' name='[Parameter 1]' param-domain-type='range' role='measure' type='quantitative' value='3'>
          <calculation class='tableau' formula='3' />
          <range max='16' min='1' />
        </column>
      </datasource-dependencies>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT' id='VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT (SCD.VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)_3E4861F75F614EF5A8BACD560D3A73FF'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
    <datasource caption='scd_triggered_vs_scd_published' inline='true' name='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f' version='18.1'>
      <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/datasources/scd_triggered_vs_scd_published?rev=1.4' id='${name}' path='/t/${site}/datasources' revision='2.0' site='${site}' />
      <connection channel='https' class='sqlproxy' dbname='scd_triggered_vs_scd_published' directory='/dataserver' port='443' server='10ay.online.tableau.com' server-oauth='' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <calculations>
          <calculation column='[Calculation_159455581011263491]' formula='IF [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] = NULL or [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] = 0 THEN 0 &#10;ELSEIF [HCP_COUNT] = NULL or [HCP_COUNT] = 0 THEN 0&#10;ELSE ROUND((([HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] / [HCP_COUNT]) * 100), 0)&#10;END' />
          <calculation column='[Calculation_3496341431263195138]' formula='[HCP_COUNT]' />
          <calculation column='[Calculation_3496341431263289347]' formula='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' />
          <calculation column='[Number of Records]' formula='1' />
        </calculations>
        <metadata-records>
          <metadata-record class='column'>
            <remote-name>ACTION_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACTION_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTION_TYPE</remote-alias>
            <ordinal>9</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_159455581011263491</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_159455581011263491]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_159455581011263491</remote-alias>
            <ordinal>17</ordinal>
            <layered>true</layered>
            <caption>Percentage</caption>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] = NULL or [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] = 0 THEN 0 
ELSEIF [HCP_COUNT] = NULL or [HCP_COUNT] = 0 THEN 0
ELSE ROUND((([HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] / [HCP_COUNT]) * 100), 0)
END&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_3496341431263195138</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_3496341431263195138]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_3496341431263195138</remote-alias>
            <ordinal>18</ordinal>
            <layered>true</layered>
            <caption>Triggered</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;[HCP_COUNT]&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_3496341431263289347</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_3496341431263289347]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_3496341431263289347</remote-alias>
            <ordinal>19</ordinal>
            <layered>true</layered>
            <caption>Published</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>20</remote-type>
            <local-name>[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>12</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>HCP_COUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[HCP_COUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_COUNT</remote-alias>
            <ordinal>0</ordinal>
            <layered>true</layered>
            <family>VW_SCD_TRIGGERED_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LABEL_TYPE_EXT_ID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[LABEL_TYPE_EXT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LABEL_TYPE_EXT_ID</remote-alias>
            <ordinal>11</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Number of Records</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Number of Records]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Number of Records</remote-alias>
            <ordinal>20</ordinal>
            <layered>true</layered>
            <caption>Number of Records</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;1&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>7</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>PRODUCT_ID</remote-name>
            <remote-type>20</remote-type>
            <local-name>[PRODUCT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <family>VW_SCD_TRIGGERED_HCP_COUNT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>8</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <family>VW_SCD_TRIGGERED_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCD_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCD_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCD_NAME</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <family>VW_SCD_TRIGGERED_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCD_TRIGGER_DATE</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SCD_TRIGGER_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCD_TRIGGER_DATE</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <family>VW_SCD_TRIGGERED_HCP_COUNT_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_DATE</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SUGGESTION_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_DATE</remote-alias>
            <ordinal>6</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)</remote-alias>
            <ordinal>10</ordinal>
            <layered>true</layered>
            <family>VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USE_CASE_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USE_CASE_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USE_CASE_NAME</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <family>VW_SCD_TRIGGERED_HCP_COUNT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_CASE_INSENSITIVE_CONTAINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORT_ANALYTIC_FUNCTIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='scd_triggered_vs_scd_published' inline='true' version='18.1' xml:base='https://10ay.online.tableau.com' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/datasources/scd_triggered_vs_scd_published?rev=1.4' id='${name}' path='/t/${site}/datasources' revision='2.0' site='${site}' />
  <connection channel='https' class='sqlproxy' dbname='scd_triggered_vs_scd_published' directory='/dataserver' port='443' server='10ay.online.tableau.com'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <cols>
      <map key='[ACTION_TYPE]' value='[sqlproxy].[ACTION_TYPE]' />
      <map key='[Calculation_159455581011263491]' value='[sqlproxy].[Calculation_159455581011263491]' />
      <map key='[Calculation_3496341431263195138]' value='[sqlproxy].[Calculation_3496341431263195138]' />
      <map key='[Calculation_3496341431263289347]' value='[sqlproxy].[Calculation_3496341431263289347]' />
      <map key='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[HCP_COUNT]' value='[sqlproxy].[HCP_COUNT]' />
      <map key='[LABEL_TYPE_EXT_ID]' value='[sqlproxy].[LABEL_TYPE_EXT_ID]' />
      <map key='[Number of Records]' value='[sqlproxy].[Number of Records]' />
      <map key='[PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[PRODUCT_ID]' value='[sqlproxy].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[PRODUCT_NAME]' value='[sqlproxy].[PRODUCT_NAME]' />
      <map key='[SCD_NAME]' value='[sqlproxy].[SCD_NAME]' />
      <map key='[SCD_TRIGGER_DATE]' value='[sqlproxy].[SCD_TRIGGER_DATE]' />
      <map key='[SUGGESTION_DATE]' value='[sqlproxy].[SUGGESTION_DATE]' />
      <map key='[USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' value='[sqlproxy].[USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' />
      <map key='[USE_CASE_NAME]' value='[sqlproxy].[USE_CASE_NAME]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='Sum' caption='Percentage' datatype='real' default-type='quantitative' name='[Calculation_159455581011263491]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='IF [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] = NULL or [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] = 0 THEN 0 &\#10;ELSEIF [HCP_COUNT] = NULL or [HCP_COUNT] = 0 THEN 0&\#10;ELSE ROUND((([HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] / [HCP_COUNT]) * 100), 0)&\#10;END' />
  </column>
  <column aggregation='Sum' caption='Triggered' datatype='integer' default-type='quantitative' name='[Calculation_3496341431263195138]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='[HCP_COUNT]' />
  </column>
  <column aggregation='Sum' caption='Published' datatype='integer' default-type='quantitative' name='[Calculation_3496341431263289347]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' />
  </column>
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[Number of Records]' pivot='key' role='measure' type='quantitative' user-datatype='integer' user:auto-column='numrec' visual-totals='Default'>
    <calculation class='tableau' formula='1' />
  </column>
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='scd trigger vs scd publish' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <column-instance column='[Calculation_159455581011263491]' derivation='Attribute' name='[attr:Calculation_159455581011263491:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_159455581011263491]' derivation='None' name='[none:Calculation_159455581011263491:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_3496341431263289347]' derivation='None' name='[none:Calculation_3496341431263289347:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_159455581011263491]' derivation='Sum' name='[sum:Calculation_159455581011263491:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_3496341431263195138]' derivation='Sum' name='[sum:Calculation_3496341431263195138:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_3496341431263289347]' derivation='Sum' name='[sum:Calculation_3496341431263289347:qk]' pivot='key' type='quantitative' />
  <column-instance column='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' derivation='Sum' name='[sum:HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT):qk]' pivot='key' type='quantitative' />
  <column-instance column='[HCP_COUNT]' derivation='Sum' name='[sum:HCP_COUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.907343' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.0926573' show-aliased-fields='true' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[:Measure Names]' palette='tableau10_10_0' type='palette'>
        <map to='\#386592'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:SCD_IN_PRODUCT_CONFIG_KEY:qk]&quot;</bucket>
        </map>
        <map to='\#4472a0'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:SCD_BI_CONTROL_RANGE_KEY:qk]&quot;</bucket>
        </map>
        <map to='\#4878a6'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:Calculation_159455581011263491:qk]&quot;</bucket>
        </map>
        <map to='\#4878a6'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT):qk]&quot;</bucket>
        </map>
        <map to='\#4e79a7'>
          <bucket>&quot;[scd_triggered_vs_scd_published]&quot;</bucket>
        </map>
        <map to='\#4e79a7'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[none:Calculation_3496341431263289347:qk]&quot;</bucket>
        </map>
        <map to='\#4e79a7'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:Calculation_3496341431263289347:qk]&quot;</bucket>
        </map>
        <map to='\#5081ae'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[none:RANGE_MIN:qk]&quot;</bucket>
        </map>
        <map to='\#5081ae'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:RANGE_MIN:qk]&quot;</bucket>
        </map>
        <map to='\#5f90ba'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[none:RANGE_MAX:qk]&quot;</bucket>
        </map>
        <map to='\#5f90ba'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:RANGE_MAX:qk]&quot;</bucket>
        </map>
        <map to='\#6798c1'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[attr:Calculation_159455581011263491:qk]&quot;</bucket>
        </map>
        <map to='\#6798c1'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[none:Calculation_159455581011263491:qk]&quot;</bucket>
        </map>
        <map to='\#90bede'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:Number of Records:qk]&quot;</bucket>
        </map>
        <map to='\#92c0df'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[attr:HCP_COUNT_PUBLISHED:qk]&quot;</bucket>
        </map>
        <map to='\#92c0df'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:Calculation_3496341431263195138:qk]&quot;</bucket>
        </map>
        <map to='\#92c0df'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:HCP_COUNT_PUBLISHED:qk]&quot;</bucket>
        </map>
        <map to='\#92c0df'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:HCP_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#b9ddf1'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[__tableau_internal_object_id__].[cnt:_ABDE6613615E424794C38B65D0694839:qk]&quot;</bucket>
        </map>
        <map to='\#b9ddf1'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:Calculation_591941882634362903:qk]&quot;</bucket>
        </map>
        <map to='\#b9ddf1'>
          <bucket>&quot;[scd_triggered_vs_scd_published].[sum:HCP_COUNT_TRIGGERED:qk]&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='scd trigger vs scd publish' id='VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='hyper' version='0.0.0'>
  </primary-dialect>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>true</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
              <attribute datatype='string' name='update-time'>&quot;3/31/2021 6:42:31 AM&quot;</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='sunday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' caption='Percentage' datatype='real' default-type='quantitative' layered='true' name='[Calculation_159455581011263491]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='IF [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] = NULL or [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] = 0 THEN 0 &#10;ELSEIF [HCP_COUNT] = NULL or [HCP_COUNT] = 0 THEN 0&#10;ELSE ROUND((([HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] / [HCP_COUNT]) * 100), 0)&#10;END' />
      </column>
      <column aggregation='Sum' caption='Triggered' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_3496341431263195138]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='[HCP_COUNT]' />
      </column>
      <column aggregation='Sum' caption='Published' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_3496341431263289347]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' />
      </column>
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[LABEL_TYPE_EXT_ID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[Number of Records]' pivot='key' role='measure' type='quantitative' user-datatype='integer' user:auto-column='numrec' visual-totals='Default'>
        <calculation class='tableau' formula='1' />
      </column>
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[PRODUCT_ID (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[PRODUCT_ID]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SCD_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SCD_TRIGGER_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='scd trigger vs scd publish' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <column-instance column='[Calculation_159455581011263491]' derivation='Attribute' name='[attr:Calculation_159455581011263491:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_159455581011263491]' derivation='None' name='[none:Calculation_159455581011263491:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_3496341431263289347]' derivation='None' name='[none:Calculation_3496341431263289347:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_159455581011263491]' derivation='Sum' name='[sum:Calculation_159455581011263491:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_3496341431263195138]' derivation='Sum' name='[sum:Calculation_3496341431263195138:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_3496341431263289347]' derivation='Sum' name='[sum:Calculation_3496341431263289347:qk]' pivot='key' type='quantitative' />
      <column-instance column='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' derivation='Sum' name='[sum:HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT):qk]' pivot='key' type='quantitative' />
      <column-instance column='[HCP_COUNT]' derivation='Sum' name='[sum:HCP_COUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.922428' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.0775717' show-aliased-fields='true' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[:Measure Names]' palette='tableau10_10_0' type='palette'>
            <map to='#386592'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:SCD_IN_PRODUCT_CONFIG_KEY:qk]&quot;</bucket>
            </map>
            <map to='#4472a0'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:SCD_BI_CONTROL_RANGE_KEY:qk]&quot;</bucket>
            </map>
            <map to='#4878a6'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_159455581011263491:qk]&quot;</bucket>
            </map>
            <map to='#4878a6'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT):qk]&quot;</bucket>
            </map>
            <map to='#4e79a7'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f]&quot;</bucket>
            </map>
            <map to='#4e79a7'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:Calculation_3496341431263289347:qk]&quot;</bucket>
            </map>
            <map to='#4e79a7'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263289347:qk]&quot;</bucket>
            </map>
            <map to='#5081ae'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:RANGE_MIN:qk]&quot;</bucket>
            </map>
            <map to='#5081ae'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:RANGE_MIN:qk]&quot;</bucket>
            </map>
            <map to='#5f90ba'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:RANGE_MAX:qk]&quot;</bucket>
            </map>
            <map to='#5f90ba'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:RANGE_MAX:qk]&quot;</bucket>
            </map>
            <map to='#6798c1'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[attr:Calculation_159455581011263491:qk]&quot;</bucket>
            </map>
            <map to='#6798c1'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:Calculation_159455581011263491:qk]&quot;</bucket>
            </map>
            <map to='#90bede'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Number of Records:qk]&quot;</bucket>
            </map>
            <map to='#92c0df'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[attr:HCP_COUNT_PUBLISHED:qk]&quot;</bucket>
            </map>
            <map to='#92c0df'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263195138:qk]&quot;</bucket>
            </map>
            <map to='#92c0df'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:HCP_COUNT_PUBLISHED:qk]&quot;</bucket>
            </map>
            <map to='#92c0df'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:HCP_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#b9ddf1'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[__tableau_internal_object_id__].[cnt:_ABDE6613615E424794C38B65D0694839:qk]&quot;</bucket>
            </map>
            <map to='#b9ddf1'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_591941882634362903:qk]&quot;</bucket>
            </map>
            <map to='#b9ddf1'>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:HCP_COUNT_TRIGGERED:qk]&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='scd trigger vs scd publish' id='VW_SCD_TRIGGERED_HCP_COUNT_RPT (SCD.VW_SCD_TRIGGERED_HCP_COUNT_RPT)_CB8FA81FAE3E40229A20975558F3535F'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
  </datasources>
  <worksheets>
    <worksheet name='SCD Published by Detector'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontname='Helvetica Neue' fontsize='11'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='scd_published_by_usecase' name='sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5' />
            <datasource caption='scd_triggered_vs_scd_published' name='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Top N Label Types' datatype='integer' name='[Parameter 1]' param-domain-type='range' role='measure' type='quantitative' value='3'>
              <calculation class='tableau' formula='3' />
              <range max='16' min='1' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5'>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[LABEL_TYPE_EXT_ID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column-instance column='[LABEL_TYPE_EXT_ID Set]' derivation='InOut' name='[io:LABEL_TYPE_EXT_ID Set:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='Month' name='[mn:SUGGESTION_DATE:ok]' pivot='key' type='ordinal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='MY' name='[my:SUGGESTION_DATE:ok]' pivot='key' type='ordinal' />
            <column-instance column='[ACTION_TYPE]' derivation='None' name='[none:ACTION_TYPE:nk]' pivot='key' type='nominal' />
            <column-instance column='[LABEL_TYPE_EXT_ID]' derivation='None' name='[none:LABEL_TYPE_EXT_ID:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[HCP_COUNT]' derivation='Sum' name='[sum:HCP_COUNT:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <datasource-dependencies datasource='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f'>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[LABEL_TYPE_EXT_ID Set]' />
          <filter class='categorical' column='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:ACTION_TYPE:nk]'>
            <groupfilter function='member' level='[none:ACTION_TYPE:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <natural-sort column='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]' direction='ASC' />
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='member' level='[none:PRODUCT_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:PRODUCT_NAME:nk]' />
          </filter>
          <filter class='quantitative' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' filter-group='6' included-values='in-range'>
            <min>#2019-05-18#</min>
            <max>#2020-02-08#</max>
            <target field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:SUGGESTION_DATE:qk]' />
          </filter>
          <slices>
            <column>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:ACTION_TYPE:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]</column>
            <column>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[LABEL_TYPE_EXT_ID Set]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[sum:HCP_COUNT:qk]' scope='rows' value='' />
          </style-rule>
          <style-rule element='label'>
            <format attr='display' field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]' value='false' />
            <format attr='display' field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[io:LABEL_TYPE_EXT_ID Set:nk]' value='false' />
            <format attr='font-size' field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[sum:HCP_COUNT:qk]' value='8' />
            <format attr='display' field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[mn:SUGGESTION_DATE:ok]' value='true' />
            <format attr='font-size' field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[mn:SUGGESTION_DATE:ok]' value='8' />
            <format attr='display' field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[my:SUGGESTION_DATE:ok]' value='true' />
            <format attr='font-size' field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[my:SUGGESTION_DATE:ok]' value='8' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='in-tooltip' field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[io:LABEL_TYPE_EXT_ID Set:nk]' value='false' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='line-visibility' scope='cols' value='on' />
            <format attr='line-pattern-only' scope='cols' value='dotted' />
          </style-rule>
          <style-rule element='legend'>
            <format attr='font-size' value='8' />
          </style-rule>
          <style-rule element='legend-title-text'>
            <format attr='color' field='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]' value='Label Types' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-disallow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]' />
            </encodings>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Month, Year of Suggestion date:	</run>
                <run bold='true'><![CDATA[<[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[my:SUGGESTION_DATE:ok]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Label Type:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Count:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[sum:HCP_COUNT:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
          </pane>
        </panes>
        <rows>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[sum:HCP_COUNT:qk]</rows>
        <cols>([sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[my:SUGGESTION_DATE:ok] / ([sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[io:LABEL_TYPE_EXT_ID Set:nk] / [sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]))</cols>
      </table>
      <simple-id uuid='{0C4888AB-AD49-4F90-9424-8438B5792F1B}' />
    </worksheet>
    <worksheet name='SCD Published by action-taken (Percentage)'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontname='Helvetica Neue' fontsize='11'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='scd_published_vs_dse_published_with_actiontype' name='sqlproxy.176n3kc0h2wiuu14rjs660rnxot4' />
            <datasource caption='scd_triggered_vs_scd_published' name='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f' />
          </datasources>
          <datasource-dependencies datasource='sqlproxy.176n3kc0h2wiuu14rjs660rnxot4'>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='SCD' datatype='real' default-type='quantitative' layered='true' name='[Calculation_106890129499590702]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='ROUND((([HCP_COUNT] / [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)' />
            </column>
            <column aggregation='Sum' caption='Hcp Count (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[ACTION_TYPE]' derivation='None' name='[none:ACTION_TYPE:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' derivation='None' name='[none:SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]' pivot='key' type='quantitative' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_106890129499590702]' derivation='Sum' name='[sum:Calculation_106890129499590702:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <datasource-dependencies datasource='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f'>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <filter class='categorical' column='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]'>
            <groupfilter function='member' level='[:Measure Names]' member='&quot;[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]&quot;' user:op='manual' />
          </filter>
          <filter class='categorical' column='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]'>
            <groupfilter function='member' level='[none:ACTION_TYPE:nk]' member='&quot;Accepted/Complete&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='member' level='[none:PRODUCT_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:PRODUCT_NAME:nk]' />
          </filter>
          <filter class='quantitative' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' filter-group='6' included-values='in-range'>
            <min>#2019-05-18#</min>
            <max>#2020-02-08#</max>
            <target field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE:qk]' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' filter-group='3'>
            <groupfilter function='member' level='[none:USE_CASE_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:USE_CASE_NAME:nk]' />
          </filter>
          <slices>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]</column>
            <column>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]</column>
            <column>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]' scope='cols' value='' />
            <format attr='title' class='0' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]' scope='rows' value='' />
          </style-rule>
          <style-rule element='header'>
            <format attr='height-header' value='10' />
          </style-rule>
          <style-rule element='label'>
            <format attr='font-size' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]' value='8' />
            <format attr='font-size' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]' value='8' />
            <format attr='text-format' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]' value='n#,##0&quot;%&quot;;-#,##0&quot;%&quot;' />
          </style-rule>
          <style-rule element='pane'>
            <format attr='background-color' value='#e6e6e6' />
          </style-rule>
          <style-rule element='table'>
            <format attr='background-color' value='#ffffff' />
          </style-rule>
          <style-rule element='refline'>
            <format attr='text-format' id='refline0' value='n#,##0&quot;%&quot;;-#,##0&quot;%&quot;' />
          </style-rule>
          <style-rule element='refband'>
            <format attr='fill-color' id='refline0' value='#ffffff' />
            <format attr='reverse-palette' id='refline0' value='false' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='stroke-size' value='1' />
            <format attr='line-visibility' value='on' />
            <format attr='line-pattern-only' value='dotted' />
            <format attr='stroke-color' value='#f5f5f5' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' value='Filter by Action Type' />
            <format attr='font-size' value='8' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-disallow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]' />
            </encodings>
            <reference-line axis-column='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]' enable-instant-analytics='true' fill-above='false' fill-below='false' formula='stdev' id='refline0' label-type='none' scope='per-table' symmetric='true' tooltip='Range: &lt;Value&gt;' tooltip-type='custom' type='population' value-column='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]' z-order='1'>
              <reference-line-value factor='-3' />
              <reference-line-value factor='3' />
            </reference-line>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>SCD:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-color' value='#b07aa1' />
                <format attr='mark-transparency' value='231' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]</rows>
        <cols>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT):qk]</cols>
      </table>
      <simple-id uuid='{6D489EFC-C369-40C9-9E44-FBE37B8D6E63}' />
    </worksheet>
    <worksheet name='SCD Published by action-taken (vs. All)'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontname='Helvetica Neue' fontsize='11'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='scd_published_vs_dse_published_with_actiontype' name='sqlproxy.176n3kc0h2wiuu14rjs660rnxot4' />
            <datasource caption='scd_triggered_vs_scd_published' name='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f' />
          </datasources>
          <datasource-dependencies datasource='sqlproxy.176n3kc0h2wiuu14rjs660rnxot4'>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='SCD' datatype='real' default-type='quantitative' layered='true' name='[Calculation_106890129499590702]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='ROUND((([HCP_COUNT] / [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)' />
            </column>
            <column aggregation='Sum' caption='Hcp Count (Vw Dse Scd Suggestion Hcp Count Rpt1)' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' caption='Hcp Count (Vw Dse Suggestion Hcp Count Rpt1)' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='All' datatype='real' default-type='quantitative' layered='true' name='[SCD (copy)_106890129500364847]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='ROUND((([HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] / [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT1)]) * 100), 0)' />
            </column>
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[ACTION_TYPE]' derivation='None' name='[none:ACTION_TYPE:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_106890129499590702]' derivation='Sum' name='[sum:Calculation_106890129499590702:qk]' pivot='key' type='quantitative' />
            <column-instance column='[SCD (copy)_106890129500364847]' derivation='Sum' name='[sum:SCD (copy)_106890129500364847:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <datasource-dependencies datasource='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f'>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <filter class='categorical' column='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]'>
            <groupfilter function='member' level='[none:ACTION_TYPE:nk]' member='&quot;Accepted/Complete&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='member' level='[none:PRODUCT_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:PRODUCT_NAME:nk]' />
          </filter>
          <filter class='quantitative' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' filter-group='6' included-values='in-range'>
            <min>#2019-05-18#</min>
            <max>#2020-02-08#</max>
            <target field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE:qk]' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' filter-group='3'>
            <groupfilter function='member' level='[none:USE_CASE_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:USE_CASE_NAME:nk]' />
          </filter>
          <slices>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]</column>
            <column>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='height' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE:qk]' value='30' />
            <encoding attr='space' class='0' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:SCD (copy)_106890129500364847:qk]' field-type='quantitative' fold='true' scope='rows' synchronized='true' type='space' />
            <format attr='display' class='0' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:SCD (copy)_106890129500364847:qk]' scope='rows' value='false' />
            <format attr='title' class='0' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]' scope='rows' value='' />
            <format attr='title' class='0' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE:qk]' scope='cols' value='' />
            <format attr='auto-subtitle' class='0' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE:qk]' scope='cols' value='true' />
          </style-rule>
          <style-rule element='label'>
            <format attr='font-size' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE:qk]' value='8' />
            <format attr='font-size' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]' value='8' />
            <format attr='text-format' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]' value='n#,##0&quot;%&quot;;-#,##0&quot;%&quot;' />
            <format attr='text-format' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:SCD (copy)_106890129500364847:qk]' value='n#,##0&quot;%&quot;;-#,##0&quot;%&quot;' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='line-visibility' scope='cols' value='on' />
            <format attr='line-pattern-only' scope='cols' value='dotted' />
          </style-rule>
          <style-rule element='legend'>
            <format attr='col-width' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]' value='60' />
            <format attr='font-size' value='8' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' value='Filter by Action Type' />
            <format attr='font-size' value='8' />
          </style-rule>
        </style>
        <panes>
          <pane id='6' selection-relaxation-option='selection-relaxation-disallow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]' />
            </encodings>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>SCD:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>All:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:SCD (copy)_106890129500364847:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-transparency' value='231' />
                <format attr='mark-markers-mode' value='auto' />
              </style-rule>
            </style>
          </pane>
          <pane id='7' selection-relaxation-option='selection-relaxation-disallow' y-axis-name='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]' />
            </encodings>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>SCD:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>All:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:SCD (copy)_106890129500364847:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-transparency' value='231' />
                <format attr='mark-markers-mode' value='auto' />
              </style-rule>
            </style>
          </pane>
          <pane id='8' selection-relaxation-option='selection-relaxation-disallow' y-axis-name='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:SCD (copy)_106890129500364847:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]' />
            </encodings>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>SCD:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>All:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:SCD (copy)_106890129500364847:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-transparency' value='231' />
                <format attr='mark-markers-mode' value='auto' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>([sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:Calculation_106890129499590702:qk] + [sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[sum:SCD (copy)_106890129500364847:qk])</rows>
        <cols>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:SUGGESTION_DATE:qk]</cols>
      </table>
      <simple-id uuid='{A0CCA987-CB5F-4488-97EF-789CBDB1DDA1}' />
    </worksheet>
    <worksheet name='SCD Suggestions vs. All (Percentage)'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontname='Helvetica Neue' fontsize='12'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='scd_published_vs_dse_published' name='sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li' />
            <datasource caption='scd_triggered_vs_scd_published' name='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f' />
          </datasources>
          <datasource-dependencies datasource='sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li'>
            <column aggregation='Sum' caption='scd_by_dse_hcp_count' datatype='real' default-type='quantitative' layered='true' name='[Calculation_106890129488527402]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='IF [HCP_COUNT] = NULL or [HCP_COUNT] = 0 THEN 0 &#10;ELSEIF [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] = NULL or [HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)] = 0 THEN 0&#10;ELSE ROUND((([HCP_COUNT]/[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]) * 100), 0)&#10;END' />
            </column>
            <column aggregation='Sum' caption='Hcp Count (Vw Dse Suggestion Hcp Count Rpt)' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_106890129488527402]' derivation='Sum' name='[sum:Calculation_106890129488527402:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <datasource-dependencies datasource='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f'>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='member' level='[none:PRODUCT_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:PRODUCT_NAME:nk]' />
          </filter>
          <filter class='quantitative' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' filter-group='6' included-values='in-range'>
            <min>#2019-05-18#</min>
            <max>#2020-02-08#</max>
            <target field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' filter-group='3'>
            <groupfilter function='member' level='[none:USE_CASE_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:USE_CASE_NAME:nk]' />
          </filter>
          <slices>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]' scope='cols' value='' />
            <format attr='title' class='0' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_106890129488527402:qk]' scope='rows' value='' />
            <format attr='width' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_106890129488527402:qk]' value='32' />
            <format attr='height' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]' value='29' />
          </style-rule>
          <style-rule element='header'>
            <format attr='background-color' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]' value='#ffffff' />
          </style-rule>
          <style-rule element='label'>
            <format attr='font-size' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]' value='8' />
            <format attr='font-size' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_106890129488527402:qk]' value='8' />
            <format attr='text-format' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_106890129488527402:qk]' value='n#,##0&quot;%&quot;;-#,##0&quot;%&quot;' />
          </style-rule>
          <style-rule element='pane'>
            <format attr='background-color' value='#e6e6e6' />
          </style-rule>
          <style-rule element='table'>
            <format attr='background-color' value='#ffffff' />
          </style-rule>
          <style-rule element='refline'>
            <format attr='text-format' id='refline0' value='n#,##0&quot;%&quot;;-#,##0&quot;%&quot;' />
          </style-rule>
          <style-rule element='refband'>
            <format attr='fill-color' id='refline0' value='#ffffff' />
            <format attr='reverse-palette' id='refline0' value='false' />
            <format attr='palette' id='refline0' value='gray88' />
            <format attr='line-pattern-only' id='refline0' value='solid' />
            <format attr='stroke-size' id='refline0' value='0' />
            <format attr='line-visibility' id='refline0' value='off' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='stroke-size' value='1' />
            <format attr='line-visibility' value='on' />
            <format attr='line-pattern-only' value='dotted' />
            <format attr='stroke-color' value='#f5f5f5' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-disallow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Line' />
            <reference-line axis-column='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_106890129488527402:qk]' enable-instant-analytics='true' fill-above='false' fill-below='false' formula='stdev' id='refline0' label-type='none' scope='per-table' symmetric='false' tooltip='Range: &lt;Value&gt;' tooltip-type='custom' type='population' value-column='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_106890129488527402:qk]' z-order='1'>
              <reference-line-value factor='-3' />
              <reference-line-value factor='3' />
            </reference-line>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>SCD:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_106890129488527402:qk]>]]></run>
                <run>Æ&#10;</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-color' value='#b07aa1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_106890129488527402:qk]</rows>
        <cols>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]</cols>
      </table>
      <simple-id uuid='{B2E97772-B174-46B3-9CE1-9AE4D7678EF8}' />
    </worksheet>
    <worksheet name='SCD Suggestions vs. All (Volume)'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontname='Helvetica Neue' fontsize='11'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='scd_published_vs_dse_published' name='sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li' />
            <datasource caption='scd_triggered_vs_scd_published' name='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f' />
          </datasources>
          <datasource-dependencies datasource='sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li'>
            <column aggregation='Sum' caption='ALL' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_3496341431263809540]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' />
            </column>
            <column aggregation='Sum' caption='SCD' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_3496341431263932421]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='[HCP_COUNT]' />
            </column>
            <column aggregation='Sum' caption='Hcp Count (Vw Dse Suggestion Hcp Count Rpt)' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_3496341431263809540]' derivation='Sum' name='[sum:Calculation_3496341431263809540:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Calculation_3496341431263932421]' derivation='Sum' name='[sum:Calculation_3496341431263932421:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <datasource-dependencies datasource='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f'>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='member' level='[none:PRODUCT_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:PRODUCT_NAME:nk]' />
          </filter>
          <filter class='quantitative' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' filter-group='6' included-values='in-range'>
            <min>#2019-05-18#</min>
            <max>#2020-02-08#</max>
            <target field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' filter-group='3'>
            <groupfilter function='member' level='[none:USE_CASE_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:USE_CASE_NAME:nk]' />
          </filter>
          <slices>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]' scope='cols' value='' />
            <encoding attr='space' class='0' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263932421:qk]' field-type='quantitative' fold='true' scope='rows' synchronized='true' type='space' />
            <format attr='display' class='0' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263932421:qk]' scope='rows' value='false' />
            <format attr='title' class='0' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263809540:qk]' scope='rows' value='' />
          </style-rule>
          <style-rule element='label'>
            <format attr='font-size' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]' value='8' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='line-visibility' scope='cols' value='on' />
            <format attr='line-pattern-only' scope='cols' value='dotted' />
          </style-rule>
          <style-rule element='legend'>
            <format attr='font-size' value='8' />
            <format attr='col-width' field='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[:Measure Names]' value='60' />
          </style-rule>
          <style-rule element='legend-title'>
            <format attr='text-align' value='center' />
          </style-rule>
        </style>
        <panes>
          <pane id='3' selection-relaxation-option='selection-relaxation-disallow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Area' />
            <encodings>
              <color column='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[:Measure Names]' />
            </encodings>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:  &#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>All:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263809540:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>SCD:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263932421:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-transparency' value='255' />
              </style-rule>
            </style>
          </pane>
          <pane id='4' selection-relaxation-option='selection-relaxation-disallow' y-axis-name='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263809540:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Area' />
            <encodings>
              <color column='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[:Measure Names]' />
            </encodings>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:  &#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>All:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263809540:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>SCD:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263932421:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-transparency' value='180' />
              </style-rule>
            </style>
          </pane>
          <pane id='5' selection-relaxation-option='selection-relaxation-disallow' y-axis-name='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263932421:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Area' />
            <encodings>
              <color column='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[:Measure Names]' />
            </encodings>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:  &#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>All:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263809540:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>SCD:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263932421:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-transparency' value='255' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>([sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263809540:qk] + [sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[sum:Calculation_3496341431263932421:qk])</rows>
        <cols>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SUGGESTION_DATE:qk]</cols>
      </table>
      <simple-id uuid='{2CE0DF7A-420C-491C-9B7A-0923296AA94C}' />
    </worksheet>
    <worksheet name='SCD Triggered HCP Count By Threshold'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontname='Helvetica Neue' fontsize='11'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='scd_hcp_count_per_threshold' name='sqlproxy.16438650nafte01d7oe5u1vstoux' />
            <datasource caption='scd_triggered_vs_scd_published' name='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f' />
          </datasources>
          <datasource-dependencies datasource='sqlproxy.16438650nafte01d7oe5u1vstoux'>
            <column aggregation='Year' caption='SUGGESTION_DATE' datatype='date' default-type='ordinal' layered='true' name='[Calculation_5079849300315119616]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='[SCD_TRIGGER_DATE]' />
            </column>
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[PERCENTAGE]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SCD_TRIGGER_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[Calculation_5079849300315119616]' derivation='None' name='[none:Calculation_5079849300315119616:qk]' pivot='key' type='quantitative' />
            <column-instance column='[PERCENTAGE]' derivation='None' name='[none:PERCENTAGE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[HCP_COUNT]' derivation='Sum' name='[sum:HCP_COUNT:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <datasource-dependencies datasource='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f'>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='member' level='[none:PRODUCT_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.16438650nafte01d7oe5u1vstoux].[none:PRODUCT_NAME:nk]' />
          </filter>
          <filter class='quantitative' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' filter-group='6' included-values='in-range'>
            <min>#2019-05-18#</min>
            <max>#2020-02-08#</max>
            <target field='[sqlproxy.16438650nafte01d7oe5u1vstoux].[none:Calculation_5079849300315119616:qk]' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' filter-group='3'>
            <groupfilter function='member' level='[none:USE_CASE_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.16438650nafte01d7oe5u1vstoux].[none:USE_CASE_NAME:nk]' />
          </filter>
          <slices>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[sqlproxy.16438650nafte01d7oe5u1vstoux].[sum:HCP_COUNT:qk]' scope='rows' value='' />
            <format attr='title' class='0' field='[sqlproxy.16438650nafte01d7oe5u1vstoux].[none:PERCENTAGE:qk]' scope='cols' value='' />
            <format attr='width' field='[sqlproxy.16438650nafte01d7oe5u1vstoux].[sum:HCP_COUNT:qk]' value='32' />
            <encoding attr='space' class='0' field='[sqlproxy.16438650nafte01d7oe5u1vstoux].[none:PERCENTAGE:qk]' field-type='quantitative' major-origin='0' major-spacing='10' scope='cols' type='space' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-format' field='[sqlproxy.16438650nafte01d7oe5u1vstoux].[none:PERCENTAGE:qk]' value='n#,##0&quot;%&quot;;-#,##0&quot;%&quot;' />
          </style-rule>
        </style>
        <panes>
          <pane id='4' selection-relaxation-option='selection-relaxation-disallow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <mark-sizing mark-sizing-setting='marks-scaling-off' />
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Threshold:&#9;&gt;= </run>
                <run bold='true'><![CDATA[<[sqlproxy.16438650nafte01d7oe5u1vstoux].[none:PERCENTAGE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>HCP Count:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.16438650nafte01d7oe5u1vstoux].[sum:HCP_COUNT:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='false' />
                <format attr='size' value='1.9890055656433105' />
                <format attr='mark-color' value='#a0cbe8' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[sqlproxy.16438650nafte01d7oe5u1vstoux].[sum:HCP_COUNT:qk]</rows>
        <cols>[sqlproxy.16438650nafte01d7oe5u1vstoux].[none:PERCENTAGE:qk]</cols>
      </table>
      <simple-id uuid='{3EDE5742-9AF9-4A76-8754-2825F65441CC}' />
    </worksheet>
    <worksheet name='SCD Triggered vs SCD Published (Volume)'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontname='Helvetica Neue' fontsize='11'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='scd_triggered_vs_scd_published' name='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f' />
          </datasources>
          <datasource-dependencies datasource='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f'>
            <column aggregation='Sum' caption='Triggered' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_3496341431263195138]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='[HCP_COUNT]' />
            </column>
            <column aggregation='Sum' caption='Published' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_3496341431263289347]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' />
            </column>
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SCD_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SCD_NAME]' derivation='None' name='[none:SCD_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_3496341431263195138]' derivation='Sum' name='[sum:Calculation_3496341431263195138:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Calculation_3496341431263289347]' derivation='Sum' name='[sum:Calculation_3496341431263289347:qk]' pivot='key' type='quantitative' />
            <column-instance column='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' derivation='Sum' name='[sum:HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT):qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]'>
            <groupfilter function='union' user:op='manual'>
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263289347:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263195138:qk]&quot;' />
            </groupfilter>
          </filter>
          <manual-sort column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]' direction='ASC'>
            <dictionary>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_591941882634362903:qk]&quot;</bucket>
              <bucket>&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT):qk]&quot;</bucket>
            </dictionary>
          </manual-sort>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='member' level='[none:PRODUCT_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SCD_NAME:nk]'>
            <groupfilter function='member' level='[none:SCD_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' filter-group='6' included-values='in-range'>
            <min>#2019-05-18#</min>
            <max>#2020-02-08#</max>
            <target field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' filter-group='3'>
            <groupfilter function='member' level='[none:USE_CASE_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' />
          </filter>
          <slices>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SCD_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' scope='cols' value='' />
            <encoding attr='space' class='0' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263289347:qk]' field-type='quantitative' fold='true' scope='rows' synchronized='true' type='space' />
            <format attr='display' class='0' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263289347:qk]' scope='rows' value='false' />
            <format attr='title' class='0' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263195138:qk]' scope='rows' value='' />
          </style-rule>
          <style-rule element='label'>
            <format attr='font-size' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' value='8' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='line-visibility' scope='rows' value='on' />
            <format attr='line-pattern-only' scope='rows' value='dotted' />
            <format attr='stroke-color' scope='rows' value='#e6e6e6' />
            <format attr='line-visibility' scope='cols' value='on' />
            <format attr='line-pattern-only' scope='cols' value='dotted' />
          </style-rule>
          <style-rule element='legend'>
            <format attr='col-width' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]' value='80' />
            <format attr='font-size' value='8' />
          </style-rule>
          <style-rule element='legend-title'>
            <format attr='text-align' value='center' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' value='Product'>
              <formatted-text>
                <run>Product</run>
              </formatted-text>
            </format>
            <format attr='title' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' value='Detector type'>
              <formatted-text>
                <run>Detector type</run>
              </formatted-text>
            </format>
            <format attr='title' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' value='Date'>
              <formatted-text>
                <run>Date</run>
              </formatted-text>
            </format>
          </style-rule>
        </style>
        <panes>
          <pane id='7' selection-relaxation-option='selection-relaxation-disallow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Area' />
            <encodings>
              <color column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]' />
            </encodings>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Published: </run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263289347:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Triggered: </run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263195138:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-transparency' value='255' />
              </style-rule>
            </style>
          </pane>
          <pane id='8' selection-relaxation-option='selection-relaxation-disallow' y-axis-name='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263289347:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Area' />
            <encodings>
              <color column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]' />
            </encodings>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Published: </run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263289347:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Triggered: </run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263195138:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-transparency' value='255' />
              </style-rule>
            </style>
          </pane>
          <pane id='9' selection-relaxation-option='selection-relaxation-disallow' y-axis-name='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263195138:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Area' />
            <encodings>
              <color column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]' />
            </encodings>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Published: </run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263289347:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Triggered: </run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263195138:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-transparency' value='255' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>([sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263195138:qk] + [sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_3496341431263289347:qk])</rows>
        <cols>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]</cols>
      </table>
      <simple-id uuid='{F921C05E-941C-4C45-B2E0-D9F4EDB15AA5}' />
    </worksheet>
    <worksheet name='SCD Triggered vs. SCD Published (Percentage)'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontname='Helvetica Neue' fontsize='11'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='scd_triggered_vs_scd_published' name='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f' />
          </datasources>
          <datasource-dependencies datasource='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f'>
            <column aggregation='Sum' caption='Percentage' datatype='real' default-type='quantitative' layered='true' name='[Calculation_159455581011263491]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='IF [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] = NULL or [HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] = 0 THEN 0 &#10;ELSEIF [HCP_COUNT] = NULL or [HCP_COUNT] = 0 THEN 0&#10;ELSE ROUND((([HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)] / [HCP_COUNT]) * 100), 0)&#10;END' />
            </column>
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT (VW_DSE_SCD_SUGGESTION_HCP_COUNT_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[HCP_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SCD_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SCD_NAME]' derivation='None' name='[none:SCD_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_159455581011263491]' derivation='Sum' name='[sum:Calculation_159455581011263491:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]'>
            <groupfilter function='member' level='[:Measure Names]' member='&quot;[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_159455581011263491:qk]&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='member' level='[none:PRODUCT_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SCD_NAME:nk]'>
            <groupfilter function='member' level='[none:SCD_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' filter-group='6' included-values='in-range'>
            <min>#2019-05-18#</min>
            <max>#2020-02-08#</max>
            <target field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' />
          </filter>
          <filter class='categorical' column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' filter-group='3'>
            <groupfilter function='member' level='[none:USE_CASE_NAME:nk]' member='&quot;All&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
            <target field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' />
          </filter>
          <slices>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]</column>
            <column>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SCD_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_159455581011263491:qk]' scope='rows' value='' />
            <format attr='title' class='0' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' scope='cols' value='' />
            <format attr='stroke-size' scope='cols' value='1' />
            <format attr='line-visibility' scope='cols' value='on' />
            <format attr='line-pattern-only' scope='cols' value='solid' />
          </style-rule>
          <style-rule element='header'>
            <format attr='height-header' value='10' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-format' field='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_159455581011263491:qk]' value='n#,##0&quot;%&quot;;-#,##0&quot;%&quot;' />
          </style-rule>
          <style-rule element='pane'>
            <format attr='band-color' scope='rows' value='#f5f5f5' />
            <format attr='background-color' value='#e6e6e6' />
          </style-rule>
          <style-rule element='table'>
            <format attr='background-color' value='#ffffff' />
            <format attr='band-size' scope='rows' value='1' />
          </style-rule>
          <style-rule element='refband'>
            <format attr='reverse-palette' id='refline0' value='false' />
            <format attr='fill-color' id='refline0' value='#ffffff' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='stroke-size' value='1' />
            <format attr='line-visibility' value='on' />
            <format attr='line-pattern-only' value='dotted' />
            <format attr='stroke-color' value='#f5f5f5' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-disallow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <reference-line axis-column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_159455581011263491:qk]' enable-instant-analytics='true' fill-above='false' fill-below='false' formula='stdev' id='refline0' label-type='none' scope='per-table' symmetric='false' tooltip='Range: &lt;Value&gt;' tooltip-type='custom' type='population' value-column='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_159455581011263491:qk]' z-order='1'>
              <reference-line-value factor='-3' />
              <reference-line-value factor='3' />
            </reference-line>
            <customized-tooltip show-buttons='false'>
              <formatted-text>
                <run fontcolor='#787878'>Suggestion Date:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>SCD:&#9;</run>
                <run bold='true'><![CDATA[<[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_159455581011263491:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
          </pane>
        </panes>
        <rows>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[sum:Calculation_159455581011263491:qk]</rows>
        <cols>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]</cols>
      </table>
      <simple-id uuid='{3347A94F-236B-42B1-986F-DC52E87DF761}' />
    </worksheet>
  </worksheets>
  <dashboards>
    <dashboard name='Dashboard'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontname='Tableau Medium'>Sales Change Detection BI Reports</run>
          </formatted-text>
        </title>
      </layout-options>
      <repository-location derived-from='https://10ay.online.tableau.com/t/aktana/workbooks/SCD/Dashboard?rev=' id='${name}' path='/t/aktana/workbooks/SCD' revision='' site='${site}' />
      <style>
        <style-rule element='parameter-ctrl'>
          <format attr='font-size' value='8' />
        </style-rule>
      </style>
      <size minheight='2250' minwidth='1020' sizing-mode='range' />
      <datasources>
        <datasource name='Parameters' />
        <datasource caption='scd_published_vs_dse_published_with_actiontype' name='sqlproxy.176n3kc0h2wiuu14rjs660rnxot4' />
        <datasource caption='scd_triggered_vs_scd_published' name='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f' />
      </datasources>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Top N Label Types' datatype='integer' name='[Parameter 1]' param-domain-type='range' role='measure' type='quantitative' value='3'>
          <calculation class='tableau' formula='3' />
          <range max='16' min='1' />
        </column>
      </datasource-dependencies>
      <datasource-dependencies datasource='sqlproxy.176n3kc0h2wiuu14rjs660rnxot4'>
        <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[ACTION_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column-instance column='[ACTION_TYPE]' derivation='None' name='[none:ACTION_TYPE:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <datasource-dependencies datasource='sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f'>
        <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTION_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
        <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[USE_CASE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SUGGESTION_DATE]' derivation='None' name='[none:SUGGESTION_DATE:qk]' pivot='key' type='quantitative' />
        <column-instance column='[USE_CASE_NAME]' derivation='None' name='[none:USE_CASE_NAME:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <zones>
        <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='100000' id='2' w='100000' x='0' y='0'>
          <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99288' id='51' param='horz' w='98832' x='584' y='356'>
            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99288' id='48' param='horz' w='98832' x='584' y='356'>
              <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99288' id='45' param='horz' w='98832' x='584' y='356'>
                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99288' id='41' param='horz' w='98832' x='584' y='356'>
                  <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='99288' id='39' w='98832' x='584' y='356'>
                    <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='83774' id='14' param='horz' w='98832' x='584' y='356'>
                      <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='83774' id='25' is-fixed='true' w='98832' x='584' y='356'>
                        <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='79330' id='26' param='vert' w='98832' x='584' y='4800'>
                          <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='79330' id='4' w='98832' x='584' y='4800'>
                            <zone h='17864' id='6' name='SCD Triggered HCP Count By Threshold' w='48251' x='584' y='4800'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone h='18832' id='8' name='SCD Triggered vs SCD Published (Volume)' w='48251' x='584' y='24354'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone h='18796' id='9' name='SCD Triggered vs. SCD Published (Percentage)' w='48278' x='51138' y='24354'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone h='19066' id='10' name='SCD Suggestions vs. All (Volume)' w='48251' x='584' y='44644'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone h='19112' id='11' name='SCD Suggestions vs. All (Percentage)' w='48278' x='51138' y='44598'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone h='18965' id='12' name='SCD Published by action-taken (vs. All)' w='48251' x='584' y='65165'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone h='18952' id='16' name='SCD Published by action-taken (Percentage)' w='48278' x='51138' y='65178'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='79330' id='29' w='2303' x='48835' y='4800'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1690' id='30' w='48251' x='584' y='22664'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1458' id='32' w='48251' x='584' y='43186'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1448' id='33' w='48278' x='51138' y='43150'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1455' id='34' w='48251' x='584' y='63710'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1468' id='35' w='48278' x='51138' y='63710'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='4955' id='59' param='vert' w='48278' x='51138' y='19399' />
                            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='2400' id='60' param='horz' w='13422' x='51138' y='4800' />
                            <zone _.fcp.SetMembershipControl.false...type='text' _.fcp.SetMembershipControl.true...type-v2='text' forceUpdate='true' h='2400' id='61' w='34856' x='64560' y='4800'>
                              <formatted-text>
                                <run fontcolor='#333333' fontname='Tableau Medium' fontsize='11'>Global Filters</run>
                              </formatted-text>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='3555' id='22' mode='dropdown' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' show-all='false' show-exclude='false' show-mode='false' values='database' w='13374' x='64560' y='10889'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='4955' id='21' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' show-filter-state='false' show-null-ctrls='false' values='database' w='29498' x='64560' y='14444'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3689' id='62' param='horz' w='13422' x='51138' y='7200' />
                            <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='3689' id='23' mode='dropdown' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' show-all='false' show-exclude='false' show-mode='false' values='database' w='13374' x='64560' y='7200'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3689' id='63' param='horz' w='21482' x='77934' y='7200' />
                            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3555' id='64' param='horz' w='13422' x='51138' y='10889' />
                            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3555' id='65' param='horz' w='21482' x='77934' y='10889' />
                            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='4955' id='66' param='horz' w='13422' x='51138' y='14444' />
                            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='4955' id='67' param='horz' w='5358' x='94058' y='14444' />
                          </zone>
                        </zone>
                        <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1434' id='37' w='38678' x='584' y='3366'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                        <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1434' id='38' w='60154' x='39262' y='3366'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                        <zone _.fcp.SetMembershipControl.false...type='title' _.fcp.SetMembershipControl.true...type-v2='title' fixed-size='28' h='3010' id='5' is-fixed='true' w='98832' x='584' y='356'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                      </zone>
                    </zone>
                    <zone h='14094' id='18' name='SCD Published by Detector' w='84836' x='584' y='85550'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1420' id='36' w='98832' x='584' y='84130'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone _.fcp.SetMembershipControl.false...type='paramctrl' _.fcp.SetMembershipControl.true...type-v2='paramctrl' h='4332' hide-buttons='true' id='19' mode='slider' param='[Parameters].[Parameter 1]' w='13996' x='85420' y='85550'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='9762' id='42' name='SCD Published by Detector' pane-specification-id='0' param='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]' w='13996' x='85420' y='89882'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                  </zone>
                </zone>
              </zone>
            </zone>
          </zone>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='none' />
            <format attr='border-width' value='0' />
            <format attr='margin' value='8' />
            <format attr='background-color' value='#ffffff' />
          </zone-style>
        </zone>
        <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='2489' id='15' mode='dropdown' name='SCD Published by action-taken (vs. All)' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' show-all='false' show-exclude='false' show-mode='false' w='12181' x='32093' y='65067' />
        <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='2489' id='17' mode='dropdown' name='SCD Published by action-taken (Percentage)' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' show-all='false' show-exclude='false' show-mode='false' w='12181' x='84902' y='65200' />
        <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='1422' id='46' leg-item-layout='horz' name='SCD Triggered vs SCD Published (Volume)' pane-specification-id='9' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]' show-title='false' w='17285' x='31445' y='24533' />
        <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='1378' id='49' leg-item-layout='horz' name='SCD Suggestions vs. All (Volume)' pane-specification-id='4' param='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[:Measure Names]' show-title='false' w='12764' x='32823' y='44800' />
        <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='1289' id='52' leg-item-layout='horz' name='SCD Published by action-taken (vs. All)' pane-specification-id='7' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]' show-title='false' w='11962' x='32312' y='67911' />
      </zones>
      <devicelayouts>
        <devicelayout name='Desktop'>
          <layout-options>
            <title>
              <formatted-text>
                <run bold='true' fontname='Tableau Medium'>Sales Change Detection BI Reports</run>
              </formatted-text>
            </title>
          </layout-options>
          <zones>
            <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='100000' id='2' w='100000' x='0' y='0'>
              <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99288' id='51' param='horz' w='98750' x='625' y='356'>
                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99288' id='48' param='horz' w='98750' x='625' y='356'>
                  <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99288' id='45' param='horz' w='98750' x='625' y='356'>
                    <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99288' id='41' param='horz' w='98750' x='625' y='356'>
                      <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='99288' id='39' w='98750' x='625' y='356'>
                        <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='83774' id='14' param='horz' w='98750' x='625' y='356'>
                          <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='83774' id='25' is-fixed='true' w='98750' x='625' y='356'>
                            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='79330' id='26' param='vert' w='98750' x='625' y='4800'>
                              <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='79330' id='4' w='98750' x='625' y='4800'>
                                <zone h='17864' id='6' name='SCD Triggered HCP Count By Threshold' w='48220' x='625' y='4800'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='18832' id='8' name='SCD Triggered vs SCD Published (Volume)' w='48220' x='625' y='24354'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='18796' id='9' name='SCD Triggered vs. SCD Published (Percentage)' w='48228' x='51147' y='24354'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='19066' id='10' name='SCD Suggestions vs. All (Volume)' w='48220' x='625' y='44644'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='19112' id='11' name='SCD Suggestions vs. All (Percentage)' w='48228' x='51147' y='44598'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='18965' id='12' name='SCD Published by action-taken (vs. All)' w='48220' x='625' y='65165'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='18952' id='16' name='SCD Published by action-taken (Percentage)' w='48228' x='51147' y='65178'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='79330' id='29' w='2302' x='48845' y='4800'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1690' id='30' w='48220' x='625' y='22664'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1458' id='32' w='48220' x='625' y='43186'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1448' id='33' w='48228' x='51147' y='43150'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1455' id='34' w='48220' x='625' y='63710'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1468' id='35' w='48228' x='51147' y='63710'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='4955' id='59' param='vert' w='48228' x='51147' y='19399' />
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='2400' id='60' param='horz' w='13410' x='51147' y='4800' />
                                <zone _.fcp.SetMembershipControl.false...type='text' _.fcp.SetMembershipControl.true...type-v2='text' forceUpdate='true' h='2400' id='61' w='34818' x='64557' y='4800'>
                                  <formatted-text>
                                    <run fontcolor='#333333' fontname='Tableau Medium' fontsize='11'>Global Filters</run>
                                  </formatted-text>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='3555' id='22' mode='dropdown' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' show-all='false' show-exclude='false' show-mode='false' values='database' w='13363' x='64557' y='10889'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='4955' id='21' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' show-filter-state='false' show-null-ctrls='false' values='database' w='29474' x='64557' y='14444'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3689' id='62' param='horz' w='13410' x='51147' y='7200' />
                                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='3689' id='23' mode='dropdown' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' show-all='false' show-exclude='false' show-mode='false' values='database' w='13363' x='64557' y='7200'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3689' id='63' param='horz' w='21455' x='77920' y='7200' />
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3555' id='64' param='horz' w='13410' x='51147' y='10889' />
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3555' id='65' param='horz' w='21455' x='77920' y='10889' />
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='4955' id='66' param='horz' w='13410' x='51147' y='14444' />
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='4955' id='67' param='horz' w='5344' x='94031' y='14444' />
                              </zone>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1434' id='37' w='38655' x='625' y='3366'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1434' id='38' w='60095' x='39280' y='3366'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='title' _.fcp.SetMembershipControl.true...type-v2='title' fixed-size='28' h='3010' id='5' is-fixed='true' w='98750' x='625' y='356'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                          </zone>
                        </zone>
                        <zone h='14094' id='18' name='SCD Published by Detector' w='84775' x='625' y='85550'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                        <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1420' id='36' w='98750' x='625' y='84130'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                        <zone _.fcp.SetMembershipControl.false...type='paramctrl' _.fcp.SetMembershipControl.true...type-v2='paramctrl' h='4332' hide-buttons='true' id='19' mode='slider' param='[Parameters].[Parameter 1]' w='13975' x='85400' y='85550'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                        <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='9762' id='42' name='SCD Published by Detector' pane-specification-id='0' param='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]' w='13975' x='85400' y='89882'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                      </zone>
                    </zone>
                  </zone>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='8' />
                <format attr='background-color' value='#ffffff' />
              </zone-style>
            </zone>
            <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='2489' id='15' mode='dropdown' name='SCD Published by action-taken (vs. All)' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' show-all='false' show-exclude='false' show-mode='false' w='12181' x='32093' y='65067' />
            <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='2489' id='17' mode='dropdown' name='SCD Published by action-taken (Percentage)' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' show-all='false' show-exclude='false' show-mode='false' w='12181' x='84902' y='65200' />
            <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='1422' id='46' leg-item-layout='horz' name='SCD Triggered vs SCD Published (Volume)' pane-specification-id='9' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]' show-title='false' w='17285' x='31445' y='24533' />
            <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='1378' id='49' leg-item-layout='horz' name='SCD Suggestions vs. All (Volume)' pane-specification-id='4' param='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[:Measure Names]' show-title='false' w='12764' x='32823' y='44800' />
            <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='1289' id='52' leg-item-layout='horz' name='SCD Published by action-taken (vs. All)' pane-specification-id='7' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]' show-title='false' w='11962' x='32312' y='67911' />
          </zones>
        </devicelayout>
        <devicelayout name='Tablet'>
          <layout-options>
            <title>
              <formatted-text>
                <run bold='true' fontname='Tableau Medium'>Sales Change Detection BI Reports</run>
              </formatted-text>
            </title>
          </layout-options>
          <size maxheight='2350' minheight='2350' sizing-mode='vscroll' />
          <zones>
            <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='100000' id='2' w='100000' x='0' y='0'>
              <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99320' id='51' param='horz' w='98438' x='781' y='340'>
                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99320' id='48' param='horz' w='98438' x='781' y='340'>
                  <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99320' id='45' param='horz' w='98438' x='781' y='340'>
                    <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99320' id='41' param='horz' w='98438' x='781' y='340'>
                      <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='99320' id='39' w='98438' x='781' y='340'>
                        <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='83801' id='14' param='horz' w='98438' x='781' y='340'>
                          <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='83801' id='25' is-fixed='true' w='98438' x='781' y='340'>
                            <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='79356' id='26' param='vert' w='98438' x='781' y='4785'>
                              <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='79356' id='4' w='98438' x='781' y='4785'>
                                <zone h='17869' id='6' name='SCD Triggered HCP Count By Threshold' w='48068' x='781' y='4785'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='18838' id='8' name='SCD Triggered vs SCD Published (Volume)' w='48068' x='781' y='24345'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='18802' id='9' name='SCD Triggered vs. SCD Published (Percentage)' w='48076' x='51143' y='24345'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='19072' id='10' name='SCD Suggestions vs. All (Volume)' w='48068' x='781' y='44642'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='19118' id='11' name='SCD Suggestions vs. All (Percentage)' w='48076' x='51143' y='44596'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='18972' id='12' name='SCD Published by action-taken (vs. All)' w='48068' x='781' y='65169'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='18959' id='16' name='SCD Published by action-taken (Percentage)' w='48076' x='51143' y='65182'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='79356' id='29' w='2294' x='48849' y='4785'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1691' id='30' w='48068' x='781' y='22654'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1459' id='32' w='48068' x='781' y='43183'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1449' id='33' w='48076' x='51143' y='43147'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1455' id='34' w='48068' x='781' y='63714'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1468' id='35' w='48076' x='51143' y='63714'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='4957' id='59' param='vert' w='48076' x='51143' y='19388' />
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='2400' id='60' param='horz' w='13368' x='51143' y='4785' />
                                <zone _.fcp.SetMembershipControl.false...type='text' _.fcp.SetMembershipControl.true...type-v2='text' forceUpdate='true' h='2400' id='61' w='34708' x='64511' y='4785'>
                                  <formatted-text>
                                    <run fontcolor='#333333' fontname='Tableau Medium' fontsize='11'>Global Filters</run>
                                  </formatted-text>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='3557' id='22' mode='dropdown' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' show-all='false' show-exclude='false' show-mode='false' values='database' w='13321' x='64511' y='10875'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='4956' id='21' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' show-filter-state='false' show-null-ctrls='false' values='database' w='29381' x='64511' y='14432'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3690' id='62' param='horz' w='13368' x='51143' y='7185' />
                                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='3690' id='23' mode='dropdown' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' show-all='false' show-exclude='false' show-mode='false' values='database' w='13321' x='64511' y='7185'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='none' />
                                    <format attr='border-width' value='0' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3690' id='63' param='horz' w='21387' x='77832' y='7185' />
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3557' id='64' param='horz' w='13368' x='51143' y='10875' />
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='3557' id='65' param='horz' w='21387' x='77832' y='10875' />
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='4956' id='66' param='horz' w='13368' x='51143' y='14432' />
                                <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='4956' id='67' param='horz' w='5327' x='93892' y='14432' />
                              </zone>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1435' id='37' w='38533' x='781' y='3350'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1435' id='38' w='59905' x='39314' y='3350'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                            <zone _.fcp.SetMembershipControl.false...type='title' _.fcp.SetMembershipControl.true...type-v2='title' fixed-size='28' h='3010' id='5' is-fixed='true' w='98438' x='781' y='340'>
                              <zone-style>
                                <format attr='border-color' value='#000000' />
                                <format attr='border-style' value='none' />
                                <format attr='border-width' value='0' />
                                <format attr='margin' value='4' />
                              </zone-style>
                            </zone>
                          </zone>
                        </zone>
                        <zone h='14099' id='18' name='SCD Published by Detector' w='84507' x='781' y='85561'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                        <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' h='1420' id='36' w='98438' x='781' y='84141'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                        <zone _.fcp.SetMembershipControl.false...type='paramctrl' _.fcp.SetMembershipControl.true...type-v2='paramctrl' h='4333' hide-buttons='true' id='19' mode='slider' param='[Parameters].[Parameter 1]' w='13931' x='85288' y='85561'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                        <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='9766' id='42' name='SCD Published by Detector' pane-specification-id='0' param='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]' w='13931' x='85288' y='89894'>
                          <zone-style>
                            <format attr='border-color' value='#000000' />
                            <format attr='border-style' value='none' />
                            <format attr='border-width' value='0' />
                            <format attr='margin' value='4' />
                          </zone-style>
                        </zone>
                      </zone>
                    </zone>
                  </zone>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='8' />
                <format attr='background-color' value='#ffffff' />
              </zone-style>
            </zone>
            <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='2489' id='15' mode='dropdown' name='SCD Published by action-taken (vs. All)' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' show-all='false' show-exclude='false' show-mode='false' w='12181' x='32093' y='65067' />
            <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='2489' id='17' mode='dropdown' name='SCD Published by action-taken (Percentage)' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' show-all='false' show-exclude='false' show-mode='false' w='12181' x='84902' y='65200' />
            <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='1422' id='46' leg-item-layout='horz' name='SCD Triggered vs SCD Published (Volume)' pane-specification-id='9' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]' show-title='false' w='17285' x='31445' y='24533' />
            <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='1378' id='49' leg-item-layout='horz' name='SCD Suggestions vs. All (Volume)' pane-specification-id='4' param='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[:Measure Names]' show-title='false' w='12764' x='32823' y='44800' />
            <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='1289' id='52' leg-item-layout='horz' name='SCD Published by action-taken (vs. All)' pane-specification-id='7' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]' show-title='false' w='11962' x='32312' y='67911' />
          </zones>
        </devicelayout>
        <devicelayout name='Phone'>
          <layout-options>
            <title>
              <formatted-text>
                <run bold='true' fontname='Tableau Medium'>SCD BI Reports</run>
              </formatted-text>
            </title>
          </layout-options>
          <size maxheight='3100' minheight='3100' sizing-mode='vscroll' />
          <zones>
            <zone _.fcp.SetMembershipControl.false...type='layout-basic' _.fcp.SetMembershipControl.true...type-v2='layout-basic' h='100000' id='69' w='100000' x='0' y='0'>
              <zone _.fcp.SetMembershipControl.false...type='layout-flow' _.fcp.SetMembershipControl.true...type-v2='layout-flow' h='99484' id='68' param='vert' w='95556' x='2222' y='258'>
                <zone _.fcp.SetMembershipControl.false...type='title' _.fcp.SetMembershipControl.true...type-v2='title' fixed-size='28' h='1323' id='5' w='95556' x='2222' y='258'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' fixed-size='24' h='1032' id='70' is-fixed='true' w='95556' x='2222' y='1581'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='text' _.fcp.SetMembershipControl.true...type-v2='text' forceUpdate='true' h='935' id='61' w='95556' x='2222' y='2613'>
                  <formatted-text>
                    <run fontcolor='#333333' fontname='Tableau Medium' fontsize='11'>Global Filters</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='1558' id='22' mode='dropdown' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]' show-all='false' show-exclude='false' show-mode='false' values='database' w='95556' x='2222' y='3548'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='1558' id='23' mode='dropdown' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]' show-all='false' show-exclude='false' show-mode='false' values='database' w='95556' x='2222' y='5106'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='1558' id='21' name='SCD Triggered vs SCD Published (Volume)' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SUGGESTION_DATE:qk]' show-filter-state='false' show-null-ctrls='false' values='database' w='95556' x='2222' y='6664'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' fixed-size='24' h='1032' id='71' is-fixed='true' w='95556' x='2222' y='8222'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='9290' id='6' is-fixed='true' name='SCD Triggered HCP Count By Threshold' w='95556' x='2222' y='9254'>
                  <layout-cache minheight='163' type-h='scalable' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' fixed-size='24' h='1032' id='72' is-fixed='true' w='95556' x='2222' y='18544'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='9290' id='8' is-fixed='true' name='SCD Triggered vs SCD Published (Volume)' w='95556' x='2222' y='19576'>
                  <layout-cache minheight='164' type-h='scalable' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='903' id='46' leg-item-layout='horz' name='SCD Triggered vs SCD Published (Volume)' pane-specification-id='9' param='[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]' show-title='false' w='95556' x='2222' y='28866'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' fixed-size='24' h='1032' id='73' is-fixed='true' w='95556' x='2222' y='29769'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='9290' id='9' is-fixed='true' name='SCD Triggered vs. SCD Published (Percentage)' w='95556' x='2222' y='30801'>
                  <layout-cache minheight='182' type-h='scalable' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' fixed-size='24' h='1032' id='74' is-fixed='true' w='95556' x='2222' y='40091'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='9290' id='11' is-fixed='true' name='SCD Suggestions vs. All (Percentage)' w='95556' x='2222' y='41123'>
                  <layout-cache minheight='164' type-h='scalable' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' fixed-size='24' h='1032' id='75' is-fixed='true' w='95556' x='2222' y='50413'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='9290' id='10' is-fixed='true' name='SCD Suggestions vs. All (Volume)' w='95556' x='2222' y='51445'>
                  <layout-cache minheight='164' type-h='scalable' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='903' id='49' leg-item-layout='horz' name='SCD Suggestions vs. All (Volume)' pane-specification-id='4' param='[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[:Measure Names]' show-title='false' w='95556' x='2222' y='60735'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='1558' id='15' mode='dropdown' name='SCD Published by action-taken (vs. All)' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' show-all='false' show-exclude='false' show-mode='false' w='95556' x='2222' y='61638'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' fixed-size='24' h='1032' id='76' is-fixed='true' w='95556' x='2222' y='63196'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='9290' id='12' is-fixed='true' name='SCD Published by action-taken (vs. All)' w='95556' x='2222' y='64228'>
                  <layout-cache minheight='164' type-h='scalable' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='903' id='52' leg-item-layout='horz' name='SCD Published by action-taken (vs. All)' pane-specification-id='7' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]' show-title='false' w='95556' x='2222' y='73518'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='filter' _.fcp.SetMembershipControl.true...type-v2='filter' h='1558' id='17' mode='dropdown' name='SCD Published by action-taken (Percentage)' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' show-all='false' show-exclude='false' show-mode='false' w='95556' x='2222' y='74421'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' fixed-size='24' h='1032' id='77' is-fixed='true' w='95556' x='2222' y='75979'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='9290' id='16' is-fixed='true' name='SCD Published by action-taken (Percentage)' w='95556' x='2222' y='77011'>
                  <layout-cache minheight='163' type-h='scalable' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='empty' _.fcp.SetMembershipControl.true...type-v2='empty' fixed-size='24' h='1032' id='78' is-fixed='true' w='95556' x='2222' y='86301'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='9290' id='18' is-fixed='true' name='SCD Published by Detector' w='95556' x='2222' y='87333'>
                  <layout-cache minheight='174' type-h='scalable' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='color' _.fcp.SetMembershipControl.true...type-v2='color' h='1558' id='42' name='SCD Published by Detector' pane-specification-id='0' param='[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]' w='95556' x='2222' y='96623'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone _.fcp.SetMembershipControl.false...type='paramctrl' _.fcp.SetMembershipControl.true...type-v2='paramctrl' h='1561' hide-buttons='true' id='19' mode='slider' param='[Parameters].[Parameter 1]' w='95556' x='2222' y='98181'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='8' />
                <format attr='background-color' value='#ffffff' />
              </zone-style>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{F919BA58-DDC8-4109-B81B-55042913F38A}' />
    </dashboard>
  </dashboards>
  <windows source-height='114'>
    <window class='worksheet' name='SCD Triggered vs SCD Published (Volume)'>
      <cards>
        <edge name='left'>
          <strip size='229'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]</field>
            <field>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:ACTION_TYPE:nk]</field>
            <field>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:PRODUCT_NAME:nk]</field>
            <field>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SCD_BI_CONTROL_RANGE_KEY:ok]</field>
            <field>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SCD_NAME:nk]</field>
            <field>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:USE_CASE_NAME:nk]</field>
            <field>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[yr:SUGGESTION_DATE:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{2FD7F3D9-98B7-41C6-AE8E-53FC54AF9CE3}' />
    </window>
    <window class='worksheet' name='SCD Triggered vs. SCD Published (Percentage)'>
      <cards>
        <edge name='left'>
          <strip size='187'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[none:SCD_NAME:nk]</field>
            <field>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[yr:SUGGESTION_DATE:ok]</field>
          </color-one-way>
        </highlight>
        <default-map-tool-selection tool='2' />
      </viewpoint>
      <simple-id uuid='{46B6DD15-7CA6-4213-8173-E161A531F8DA}' />
    </window>
    <window class='worksheet' name='SCD Suggestions vs. All (Volume)'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[:Measure Names]</field>
            <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:ACTION_TYPE:nk]</field>
            <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:HCP_COUNT:qk]</field>
            <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:LABEL_TYPE_EXT_ID:nk]</field>
            <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SCD_BI_CONTROL_RANGE_KEY:ok]</field>
            <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[yr:SUGGESTION_DATE:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{DF764839-AC2C-4175-94D1-AB6AC58996F4}' />
    </window>
    <window class='worksheet' name='SCD Suggestions vs. All (Percentage)'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:ACTION_TYPE:nk]</field>
            <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:LABEL_TYPE_EXT_ID:nk]</field>
            <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[none:SCD_BI_CONTROL_RANGE_KEY:ok]</field>
            <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[yr:SUGGESTION_DATE:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{8D8A3A8B-A58A-430B-A8AF-07DB47C61B85}' />
    </window>
    <window class='worksheet' name='SCD Published by action-taken (vs. All)'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card mode='dropdown' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' show-all='false' type='filter' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]</field>
            <field>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]</field>
            <field>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:LABEL_TYPE_EXT_ID:nk]</field>
            <field>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[yr:SUGGESTION_DATE:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{5EECF475-222F-4F9F-B56C-62870B5729D7}' />
    </window>
    <window class='worksheet' name='SCD Published by action-taken (Percentage)'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card mode='dropdown' param='[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]' show-all='false' type='filter' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]</field>
            <field>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:ACTION_TYPE:nk]</field>
            <field>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[none:LABEL_TYPE_EXT_ID:nk]</field>
            <field>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[yr:SUGGESTION_DATE (VW_DSE_SUGGESTION_HCP_COUNT_RPT):ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{F9CF3C31-E8BC-487D-9A67-3C2B4C901AF0}' />
    </window>
    <window class='worksheet' name='SCD Published by Detector'>
      <cards>
        <edge name='left'>
          <strip size='272'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card hide-buttons='true' mode='slider' param='[Parameters].[Parameter 1]' type='parameter' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[LABEL_TYPE_EXT_ID Set]</field>
            <field>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[Top N usecase Types]</field>
            <field>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[io:LABEL_TYPE_EXT_ID Set:nk]</field>
            <field>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[io:Top N usecase Types:nk]</field>
            <field>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:ACTION_TYPE:nk]</field>
            <field>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]</field>
            <field>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[yr:SUGGESTION_DATE:ok]</field>
          </color-one-way>
        </highlight>
        <default-map-tool-selection tool='2' />
      </viewpoint>
      <simple-id uuid='{9F75A80B-90E9-4A42-87CD-D8911588B5F9}' />
    </window>
    <window class='worksheet' name='SCD Triggered HCP Count By Threshold'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[sqlproxy.16438650nafte01d7oe5u1vstoux].[:Measure Names]</field>
            <field>[sqlproxy.16438650nafte01d7oe5u1vstoux].[none:PERCENTAGE:qk]</field>
            <field>[sqlproxy.16438650nafte01d7oe5u1vstoux].[none:PRODUCT_NAME:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{4DEADA3B-D551-4AEE-8A2F-18A8402EB4B7}' />
    </window>
    <window class='dashboard' maximized='true' name='Dashboard'>
      <viewpoints>
        <viewpoint name='SCD Published by Detector'>
          <zoom type='entire-view' />
          <highlight>
            <color-one-way>
              <field>[sqlproxy.17ctf7p05nsy1w1cecawy1jgvzv5].[none:LABEL_TYPE_EXT_ID:nk]</field>
            </color-one-way>
          </highlight>
        </viewpoint>
        <viewpoint name='SCD Published by action-taken (Percentage)'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='SCD Published by action-taken (vs. All)'>
          <zoom type='entire-view' />
          <highlight>
            <color-one-way>
              <field>[sqlproxy.176n3kc0h2wiuu14rjs660rnxot4].[:Measure Names]</field>
            </color-one-way>
          </highlight>
        </viewpoint>
        <viewpoint name='SCD Suggestions vs. All (Percentage)'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='SCD Suggestions vs. All (Volume)'>
          <zoom type='entire-view' />
          <highlight>
            <color-one-way>
              <field>[sqlproxy.0zjqtdr0xf116o1e7yj8y1iv67li].[:Measure Names]</field>
            </color-one-way>
          </highlight>
        </viewpoint>
        <viewpoint name='SCD Triggered HCP Count By Threshold'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='SCD Triggered vs SCD Published (Volume)'>
          <zoom type='entire-view' />
          <highlight>
            <color-one-way>
              <field>[sqlproxy.1du5j7q1mgv0x71fctglt0ie9t5f].[:Measure Names]</field>
            </color-one-way>
          </highlight>
        </viewpoint>
        <viewpoint name='SCD Triggered vs. SCD Published (Percentage)'>
          <zoom type='entire-view' />
        </viewpoint>
      </viewpoints>
      <active id='15' />
      <device-preview visible='true'>
        <device name='Medium Desktop Monitor' type='Desktop' />
        <device name='Nexus 9, 10' type='Tablet' />
        <device is-portrait='true' name='Samsung Galaxy S Series' type='Phone' />
      </device-preview>
      <simple-id uuid='{5F98A900-0FE2-418E-805D-AE07002B34D3}' />
    </window>
  </windows>
</workbook>
