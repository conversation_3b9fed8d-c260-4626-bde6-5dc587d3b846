[1] "a00A0000000quNsIAI"
[1] "start clustering ..."
[1] "end clustering."
[1] "a00A0000000quNwIAI"
[1] "start clustering ..."
[1] "end clustering."
[1] "Expired Messages:"
 [1] "a3RA0000000e0taMAA" "a3RA0000000e0u4MAA" "a3RA0000000e0u5MAA"
 [4] "a3RA0000000e0uGMAQ" "a3RA0000000e0vJMAQ" "a3RA0000000e0vLMAQ"
 [7] "a3RA0000000e0vMMAQ" "a3RA0000000e0vNMAQ" "a3RA0000000e0vOMAQ"
[10] "a3RA0000000e0vPMAQ" "a3RA0000000e0w8MAA" "a3RA0000000e0w9MAA"
[13] "a3RA0000000e0wAMAQ" "a3RA0000000e0wBMAQ" "a3RA0000000e47fMAA"
[16] "a3RA0000000e47gMAA" "a3RA0000000e47hMAA" "a3RA0000000e47iMAA"
[19] "a3RA0000000e47jMAA" "a3RA0000000e47kMAA" "a3RA0000000e47lMAA"
[22] "a3RA0000000e483MAA" "a3RA0000000e486MAA" "a3RA0000000e488MAA"
[25] "a3RA0000000HDR6MAO" "a3RA0000000HDR8MAO" "a3RA0000000HDR9MAO"
[28] "a3RA0000000HDRAMA4" "a3RA0000000HEKIMA4" "a3RA0000000HEKJMA4"
[31] "a3RA0000000HEolMAG" "a3RA0000000HFLbMAO" "a3RA0000000HFLcMAO"
[34] "a3RA0000000HGg8MAG" "a3RA0000000HGg9MAG" "a3RA0000000HGgAMAW"
[37] "a3RA0000000HGgDMAW" "a3RA0000000HGgIMAW" "a3RA0000000HGgJMAW"
[40] "a3RA0000000HGgKMAW" "a3RA0000000HGMKMA4" "a3RA0000000HGMLMA4"
[43] "a3RA0000000HGMMMA4" "a3RA0000000HGMPMA4" "a3RA0000000HHeIMAW"
[46] "a3RA0000000HIbrMAG" "a3RA0000000HNz6MAG" "a3RA0000000HNz7MAG"
[49] "a3RA0000000HNzBMAW" "a3RA0000000HNzGMAW" "a3RA0000000HNzLMAW"
[52] "a3RA0000000HNzMMAW" "a3RA0000000HO6iMAG" "a3RA0000000MfthMAC"
[55] "a3RA0000000Mfu2MAC" "a3RA0000000Mfu3MAC" "a3RA0000000MfuCMAS"
[58] "a3RA0000000Mg37MAC" "a3RA0000000Mg3CMAS" "a3RA0000000Mg3HMAS"
[61] "a3RA0000000Mg3IMAS" "a3RA0000000Mg3JMAS" "a3RA0000000Mg3KMAS"
[64] "a3RA0000000Mg3LMAS" "a3RA0000000Mg3MMAS"
[1] "memory after data read and cleanup"
                        accountPredictorNames 
                                         3400 
                               accountProduct 
                                       636312 
                                         args 
                                          880 
                                    BUILD_UID 
                                          136 
                          changeModelSavePath 
                                        10248 
                      checkNumOfFilesInFolder 
                                          952 
                              cleanUpMSOBuild 
                                        15488 
                                          con 
                                          680 
                                       con_cs 
                                          680 
                                        con_l 
                                          680 
                                    con_stage 
                                          680 
                                       config 
                                         9464 
                                    configUID 
                                          152 
                             copyMockBuildDir 
                                       151664 
                                     customer 
                                          120 
                                       dbhost 
                                          120 
                                       dbname 
                                          120 
                                   dbpassword 
                                          112 
                                       dbuser 
                                          112 
                                 DRIVERMODULE 
                                          136 
                            emailTopicNameMap 
                                         4048 
                              emailTopicNames 
                                         2136 
                           expect_file_exists 
                                         3496 
                       expect_file_not_exists 
                                         3680 
                           expect_num_of_file 
                                         5472 
                         expect_str_end_match 
                                         1736 
                             expect_str_match 
                                        15552 
                      expect_str_pattern_find 
                                         5472 
                   expect_str_pattern_notfind 
                                         9072 
                       expect_str_start_match 
                                         2520 
                              expiredMessages 
                                         5768 
                                         func 
                                          136 
                  get_message_topic_labels_cn 
                                        24304 
                              getDBConnection 
                                        36520 
                            getDBConnectionCS 
                                         4496 
                      getDBConnectionLearning 
                                         3032 
                         getDBConnectionStage 
                                         3032 
                   getOptimalParametersFromDb 
                                         2696 
                                      homedir 
                                          152 
                                            i 
                                           56 
                                 interactions 
                                       108472 
                                interactionsP 
                                       807576 
                                  isOptimized 
                                           56 
                      loadMessageSequenceData 
                                       246224 
loadMessageSequenceData.accountPredictorNames 
                                         4816 
       loadMessageSequenceData.accountProduct 
                                       144648 
    loadMessageSequenceData.emailTopicNameMap 
                                         3832 
      loadMessageSequenceData.emailTopicRates 
                                        49256 
               loadMessageSequenceData.events 
                                         5600 
      loadMessageSequenceData.expiredMessages 
                                         4440 
         loadMessageSequenceData.interactions 
                                         4144 
        loadMessageSequenceData.interactionsP 
                                         7576 
             loadMessageSequenceData.messages 
                                         2584 
           loadMessageSequenceData.messageSet 
                                          976 
    loadMessageSequenceData.messageSetMessage 
                                          992 
         loadMessageSequenceData.messageTopic 
                                         6040 
                            messageClustering 
                                       410512 
                                     messages 
                                        86536 
                              messageSequence 
                                       161896 
   messageSequence.prepareDynamicDesignMatrix 
                                        30816 
                                   messageSet 
                                         1800 
                            messageSetMessage 
                                         1464 
                                 messageTopic 
                                        14904 
                                          now 
                                          136 
                                  numberCores 
                                           56 
                                         port 
                                           56 
                                processConfig 
                                         5056 
                                     products 
                                         1520 
                                   productUID 
                                          136 
                                   readConfig 
                                         6352 
                                  readLogFile 
                                         2488 
                              readLogFileBase 
                                         2352 
                                 readMockData 
                                         3568 
                             readModuleConfig 
                                         5568 
                                resetMockData 
                                       244072 
                                      RUN_UID 
                                          152 
                                  runSettings 
                                         2840 
                             saveBuildModelDB 
                                        22728 
                          saveBuildModelExcel 
                                       116592 
                            setupMockBuildDir 
                                         9512 
                                   setupTable 
                                        33560 
                                   startTimer 
                                          344 
                                  targetNames 
                                          496 
                                      testdir 
                                          136 
     updateLearningConfigForOptimalParameters 
                                        13584 
                                   versionUID 
                                          152 
                            writeMockDataToDB 
                                        41472 
[1] "after rm accountProduct"
        accountProduct                     AP           APpredictors 
                636312                1460928                  22328 
                  chrV                   cols      droppedPredictors 
                  4392                   4496                     48 
       emailTopicNames                      i         logDropProcess 
                  2136                    136                     56 
                     n            newColnames                   numV 
                    56                  55768                   3496 
      originalColnames predictorNamesAPColMap                  prods 
                 74352                 159792                  18024 
                     q                     sM                      t 
                   584                  58136                   8848 
                    tt 
                  7088 

  |                                                                            
  |                                                                      |   0%
  |                                                                            
  |======================================================================| 100%

  |                                                                            
  |                                                                      |   0%
  |                                                                            
  |===                                                                   |   4%
  |                                                                            
  |=====                                                                 |   7%
  |                                                                            
  |======                                                                |   9%
  |                                                                            
  |========                                                              |  12%
  |                                                                            
  |========================                                              |  34%
  |                                                                            
  |=========================================                             |  58%
  |                                                                            
  |=========================================================             |  82%
  |                                                                            
  |===============================================================       |  89%
  |                                                                            
  |===================================================================== |  99%
  |                                                                            
  |======================================================================| 100%
[1] "Message: OPEN___a3RA00000001MtAMAU"
Model Details:
==============

H2OBinomialModel: drf
Model ID:  DRF_model_R_1564617654693_1 
Model Summary: 
  number_of_trees number_of_internal_trees model_size_in_bytes min_depth
1             100                      100               30236         5
  max_depth mean_depth min_leaves max_leaves mean_leaves
1        10    9.53000          8         31    19.28000


H2OBinomialMetrics: drf
** Reported on training data. **
** Metrics reported on Out-Of-Bag training samples **

MSE:  0.04289471
RMSE:  0.2071104
LogLoss:  0.1541344
Mean Per-Class Error:  0.1572464
AUC:  0.9403382
pr_auc:  0.724133
Gini:  0.8806763

Confusion Matrix (vertical: actual; across: predicted) for F1-optimal threshold:
         0  1    Error    Rate
0      204  3 0.014493  =3/207
1        6 14 0.300000   =6/20
Totals 210 17 0.039648  =9/227

Maximum Metrics: Maximum metrics at their respective thresholds
                        metric threshold    value idx
1                       max f1  0.239348 0.756757  16
2                       max f2  0.123914 0.782609  34
3                 max f0point5  0.317536 0.808824  11
4                 max accuracy  0.239348 0.960352  16
5                max precision  0.969512 1.000000   0
6                   max recall  0.027108 1.000000 136
7              max specificity  0.969512 1.000000   0
8             max absolute_mcc  0.239348 0.738207  16
9   max min_per_class_accuracy  0.123914 0.900000  34
10 max mean_per_class_accuracy  0.123914 0.908937  34

Gains/Lift Table: Extract with `h2o.gainsLift(<model>, <data>)` or `h2o.gainsLift(<model>, valid=<T/F>, xval=<T/F>)`

H2OBinomialMetrics: drf
** Reported on cross-validation data. **
** 5-fold cross-validation on training data (Metrics computed for combined holdout predictions) **

MSE:  0.04611939
RMSE:  0.2147543
LogLoss:  0.1693152
Mean Per-Class Error:  0.1870773
AUC:  0.9169082
pr_auc:  0.6715831
Gini:  0.8338164

Confusion Matrix (vertical: actual; across: predicted) for F1-optimal threshold:
         0  1    Error     Rate
0      202  5 0.024155   =5/207
1        7 13 0.350000    =7/20
Totals 209 18 0.052863  =12/227

Maximum Metrics: Maximum metrics at their respective thresholds
                        metric threshold    value idx
1                       max f1  0.217145 0.684211  17
2                       max f2  0.121111 0.726496  36
3                 max f0point5  0.438478 0.769231   7
4                 max accuracy  0.438478 0.947137   7
5                max precision  0.972500 1.000000   0
6                   max recall  0.016280 1.000000 165
7              max specificity  0.972500 1.000000   0
8             max absolute_mcc  0.217145 0.656535  17
9   max min_per_class_accuracy  0.121111 0.850000  36
10 max mean_per_class_accuracy  0.121111 0.876691  36

Gains/Lift Table: Extract with `h2o.gainsLift(<model>, <data>)` or `h2o.gainsLift(<model>, valid=<T/F>, xval=<T/F>)`
Cross-Validation Metrics Summary: 
                               mean          sd  cv_1_valid cv_2_valid
accuracy                  0.9594268 0.020076595  0.97959185  0.9574468
auc                       0.9484834 0.036057334   0.9893617  0.9756098
err                     0.040573195 0.020076595 0.020408163 0.04255319
err_count                       1.8  0.82462114         1.0        2.0
f0point5                  0.7853859  0.08470074  0.71428573  0.8333333
f1                       0.81757575 0.070813194         0.8  0.8333333
f2                       0.85617715  0.06255083  0.90909094  0.8333333
lift_top_group            14.286667   5.1197352        24.5  7.8333335
logloss                  0.17252685  0.05202467  0.10008938 0.21246335
max_per_class_error     0.117588654  0.06252601 0.021276595 0.16666667
mcc                      0.79979193  0.07996756  0.80776376  0.8089431
mean_per_class_accuracy  0.92816883  0.03935384   0.9893617  0.9044715
mean_per_class_error    0.071831174  0.03935384 0.010638298 0.09552845
mse                     0.046995696 0.019015802 0.019714335 0.06970066
precision                0.76666665 0.094280906   0.6666667  0.8333333
r2                       0.46272826  0.10689053  0.49644557  0.3741108
recall                   0.88666666  0.06599663         1.0  0.8333333
rmse                     0.20542677  0.04896703  0.14040774 0.26400882
specificity              0.96967095 0.014983122   0.9787234  0.9756098
                         cv_3_valid cv_4_valid  cv_5_valid
accuracy                  0.9411765  0.9189189         1.0
auc                      0.90869564    0.86875         1.0
err                      0.05882353 0.08108108         0.0
err_count                       3.0        3.0         0.0
f0point5                  0.6896552  0.6896552         1.0
f1                       0.72727275 0.72727275         1.0
f2                        0.7692308  0.7692308         1.0
lift_top_group                 10.2        7.4        21.5
logloss                  0.19422947  0.2771587 0.078693375
max_per_class_error             0.2        0.2         0.0
mcc                      0.69823796 0.68401486         1.0
mean_per_class_accuracy  0.87826085    0.86875         1.0
mean_per_class_error     0.12173913    0.13125         0.0
mse                     0.055209775 0.07895653  0.01139719
precision                 0.6666667  0.6666667         1.0
r2                       0.37564945  0.3244282  0.74300724
recall                          0.8        0.8         1.0
rmse                      0.2349676 0.28099203 0.106757626
specificity              0.95652175     0.9375         1.0
[1] 0

  |                                                                            
  |                                                                      |   0%
  |                                                                            
  |======================================================================| 100%

  |                                                                            
  |                                                                      |   0%
  |                                                                            
  |=                                                                     |   2%
  |                                                                            
  |================================================                      |  69%
  |                                                                            
  |======================================================================| 100%
[1] "Message: OPEN___a3RA0000000e486MAA"
Model Details:
==============

H2OBinomialModel: drf
Model ID:  DRF_model_R_1564617654693_271 
Model Summary: 
  number_of_trees number_of_internal_trees model_size_in_bytes min_depth
1             100                      100               23727         4
  max_depth mean_depth min_leaves max_leaves mean_leaves
1        10    8.63000          5         21    14.11000


H2OBinomialMetrics: drf
** Reported on training data. **
** Metrics reported on Out-Of-Bag training samples **

MSE:  0.1091105
RMSE:  0.3303188
LogLoss:  0.3573932
Mean Per-Class Error:  0.1446078
AUC:  0.9235294
pr_auc:  0.8153641
Gini:  0.8470588

Confusion Matrix (vertical: actual; across: predicted) for F1-optimal threshold:
        0  1    Error   Rate
0      49  2 0.039216  =2/51
1       5 15 0.250000  =5/20
Totals 54 17 0.098592  =7/71

Maximum Metrics: Maximum metrics at their respective thresholds
                        metric threshold    value idx
1                       max f1  0.404706 0.810811  16
2                       max f2  0.315476 0.825243  22
3                 max f0point5  0.404706 0.852273  16
4                 max accuracy  0.404706 0.901408  16
5                max precision  0.900000 1.000000   0
6                   max recall  0.175926 1.000000  41
7              max specificity  0.900000 1.000000   0
8             max absolute_mcc  0.404706 0.749232  16
9   max min_per_class_accuracy  0.315476 0.850000  22
10 max mean_per_class_accuracy  0.315476 0.866176  22

Gains/Lift Table: Extract with `h2o.gainsLift(<model>, <data>)` or `h2o.gainsLift(<model>, valid=<T/F>, xval=<T/F>)`

H2OBinomialMetrics: drf
** Reported on cross-validation data. **
** 5-fold cross-validation on training data (Metrics computed for combined holdout predictions) **

MSE:  0.1105221
RMSE:  0.3324486
LogLoss:  0.3712709
Mean Per-Class Error:  0.1044118
AUC:  0.9156863
pr_auc:  0.8073374
Gini:  0.8313725

Confusion Matrix (vertical: actual; across: predicted) for F1-optimal threshold:
        0  1    Error   Rate
0      48  3 0.058824  =3/51
1       3 17 0.150000  =3/20
Totals 51 20 0.084507  =6/71

Maximum Metrics: Maximum metrics at their respective thresholds
                        metric threshold    value idx
1                       max f1  0.337018 0.850000  19
2                       max f2  0.238826 0.855856  30
3                 max f0point5  0.337018 0.850000  19
4                 max accuracy  0.337018 0.915493  19
5                max precision  0.923452 1.000000   0
6                   max recall  0.077493 1.000000  65
7              max specificity  0.923452 1.000000   0
8             max absolute_mcc  0.337018 0.791176  19
9   max min_per_class_accuracy  0.337018 0.850000  19
10 max mean_per_class_accuracy  0.337018 0.895588  19

Gains/Lift Table: Extract with `h2o.gainsLift(<model>, <data>)` or `h2o.gainsLift(<model>, valid=<T/F>, xval=<T/F>)`
Cross-Validation Metrics Summary: 
                               mean          sd  cv_1_valid cv_2_valid
accuracy                  0.9265801  0.04415652         1.0        1.0
auc                       0.9457071 0.034981813         1.0        1.0
err                      0.07341991  0.04415652         0.0        0.0
err_count                       1.0   0.6324555         0.0        0.0
f0point5                  0.8447205 0.101225086         1.0        1.0
f1                        0.8814286   0.0725343         1.0        1.0
f2                       0.92971736 0.042208053         1.0        1.0
lift_top_group            4.0119047  0.99775827         2.5       5.25
logloss                  0.37419987  0.06620659  0.33768368 0.27336425
max_per_class_error      0.08715729  0.05275389         0.0        0.0
mcc                       0.8405185 0.093369424         1.0        1.0
mean_per_class_accuracy   0.9439213 0.036828894         1.0        1.0
mean_per_class_error    0.056078643 0.036828894         0.0        0.0
mse                     0.112895854 0.026245361 0.106433645 0.06644818
precision                 0.8247619  0.11746375         1.0        1.0
r2                        0.4136313   0.0914483   0.5565265   0.569064
recall                    0.9714286   0.0404061         1.0        1.0
rmse                     0.33158895  0.03837067   0.3262417 0.25777546
specificity              0.91641414 0.051083807         1.0        1.0
                         cv_3_valid cv_4_valid cv_5_valid
accuracy                 0.90909094  0.8666667 0.85714287
auc                       0.9444444      0.875 0.90909094
err                      0.09090909 0.13333334 0.14285715
err_count                       1.0        2.0        2.0
f0point5                 0.71428573 0.85714287 0.65217394
f1                              0.8 0.85714287       0.75
f2                       0.90909094 0.85714287 0.88235295
lift_top_group                  5.5   2.142857  4.6666665
logloss                   0.3119736  0.5393497 0.40862817
max_per_class_error      0.11111111 0.14285715 0.18181819
mcc                      0.76980036 0.73214287   0.700649
mean_per_class_accuracy   0.9444444  0.8660714 0.90909094
mean_per_class_error    0.055555556 0.13392857 0.09090909
mse                      0.09053968 0.17657457  0.1244832
precision                 0.6666667 0.85714287        0.6
r2                       0.39137217 0.29054862 0.26064524
recall                          1.0 0.85714287        1.0
rmse                     0.30089813 0.42020777 0.35282177
specificity               0.8888889      0.875  0.8181818
[1] 0
