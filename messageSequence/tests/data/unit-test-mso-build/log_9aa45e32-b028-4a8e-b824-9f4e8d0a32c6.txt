INFO [2019-07-31 17:00:58] Run directory = /Users/<USER>/Documents/learning/bin/../builds/unit-test-mso-build
INFO [2019-07-31 17:00:58] Run initialized at: 1564617658.59403
INFO [2019-07-31 17:00:58] start running messageClustering
WARN [2019-07-31 17:01:01] Sparsity will be lost - worth to calculate similarity instead of distance.
WARN [2019-07-31 17:01:01] Sparsity will be lost - worth to calculate similarity instead of distance.
INFO [2019-07-31 17:01:02] get messageTopic Time = 0.**************
INFO [2019-07-31 17:01:02] Return from messageClustering
INFO [2019-07-31 17:01:02] Return from initConfiguration
INFO [2019-07-31 17:01:02] CONFIGURATION: No configurationName LE_MS_isOptimizedVersion
INFO [2019-07-31 17:01:02] loadMessageSequenceData: interactions,accountProduct,products,messageSet,messageSetMessage,messages,emailTopicNames,messageTopic,interactionsP,expiredMessages,accountPredictorNames,emailTopicNameMap
INFO [2019-07-31 17:01:02] read V3db data: interactions,events,accountProduct,accounts,products,messageSet,messageSetMessage,messages
INFO [2019-07-31 17:01:02] read messages
INFO [2019-07-31 17:01:02] read messageSetMessage
INFO [2019-07-31 17:01:02] read messageSet
INFO [2019-07-31 17:01:02] get filtered accts list using Reps and RepAccountAssignment table
INFO [2019-07-31 17:01:02] read accounts
INFO [2019-07-31 17:01:02] read products
INFO [2019-07-31 17:01:02] read accountProduct: CHANTIX
INFO [2019-07-31 17:01:02] read interactions: CHANTIX
INFO [2019-07-31 17:01:02] read events: CHANTIX
INFO [2019-07-31 17:01:02] return from V3readFilter
INFO [2019-07-31 17:01:02] process messageSetMessage from V3db
INFO [2019-07-31 17:01:02] process messageSet from V3db
INFO [2019-07-31 17:01:02] save accountPredictorNames from accounts from V3db
INFO [2019-07-31 17:01:02] loading messageTopic (messageClusteringResult) from _stage DB
INFO [2019-07-31 17:01:02] process events from V3db
INFO [2019-07-31 17:01:02] estimate Message Topic propensities (get emailTopicRates)
INFO [2019-07-31 17:01:02] process interactions from V3db to get interactionsP (filtered interactions
INFO [2019-07-31 17:01:02] filtering internation data based on delivered_or_not information from Sent_Email_vod__c_arc
INFO [2019-07-31 17:01:02] process messages from V3db
INFO [2019-07-31 17:01:02] compose emailTopicNameMap from loaded message clustering result (messageTopic)
INFO [2019-07-31 17:01:02] get interactions from interactionsP, eventsP, messageTopic
INFO [2019-07-31 17:01:02] process accountProduct and merge with accounts from V3db
INFO [2019-07-31 17:01:02] Remove bad characters
INFO [2019-07-31 17:01:02] loading expired messages from _cs DB
INFO [2019-07-31 17:01:02] Return from loadMessageSequencedata
INFO [2019-07-31 17:01:03] Reading configuration
INFO [2019-07-31 17:01:03] Processing static predictors
INFO [2019-07-31 17:01:03] Processing emailTopics name opens and clicks
INFO [2019-07-31 17:01:03] Analyzing account product data
INFO [2019-07-31 17:01:03] 151 of 204 predictors from AccountProduct/Account dropped before loop to build models as they have less than 2 unique value: accessDesignation_akt,sampleCommercialAccess_akt,hasSharedTarget_akt,lastSlideUsed_akt,chxSampleEligibility_akt,xtandiBrandPriority_akt,nrx13w_akt,trx13w_akt,adopter_akt,dblAetna12mMedDTRxMktShare_akt,doseModificationCount_akt,prodChangeMktShare_akt,cmlTRXVolumeChange_akt,cmlTRXVolumeCurrent_akt,cmlTRXVolumePrior_akt,dblDuavee4wNRxCurrComMktShare_akt,dblDuavee4wNRxCurrMedDMktShare_akt,dblDuavee13wCopayDrops_akt,dblDuavee13wCopayRedemptions_akt,dblDuavee1wTRxVolume_akt,dblDuavee3mComDNRxVolume_akt,dblDuavee3mNRxComVolume_akt,dblDuavee3mMedDNRxVolume_akt,dblDuavee3mNRxComBusPct_akt,dblDuavee3mNRxMedDBusPct_akt,dblDuavee6mTRxVolume_akt,dblElq2mg3mTRxCurrShare_akt,dblElq3mNRxShareChgPct_akt,dblElq3mTRxShareChgPct_akt,dblElq4wNRxShareChgPct_akt,dblElq4wTRxShareChgPct_akt,dblElq3mNRxCurrMktShare_akt,dblElq3mTRxCurrMktShare_akt,dblElq4wNRxCurrMktShare_akt,dblElq4wTRxCurrMktShare_akt,visitsGoalElq_akt,dblElq3mNRxPrevMktShare_akt,dblElq3mTRxPrevMktShare_akt,dblElq4wNRxPrevMktShare_akt,dblElq4wTRxPrevMktShare_akt,visitsRemainingElq_akt,dblELQ3mTRxCurrComMktShare_akt,dblELQ3mNRxCurrMedDShareChgPct_akt,dblELQ3mTRxCurrMedDMktShare_akt,dblEstring4wTRxCurrComMktShare_akt,dblEstring4wTRxCurrMedDMktShare_akt,dblEstring4wTRxPriorComMktShare_akt,dblEstring4wTRxPriorMedDMktShare_akt,dblEstring13wCopayDrops_akt,dblEstring13wCopayRedemptions_akt,dblEstring3mTRxComVolume_akt,dblEstring3mNRxVolume_akt,dblEstring3mTRxVolume_akt,dblEstring4wComTRxShareChange_akt,dblEstring4wMedDTRxShareChange_akt,eucrisaNewWriterFlag_akt,visitsGoalEuc_akt,eucrisaR13wNRx_akt,eucrisaR13wNRxFlag_akt,visitsRemainingEuc_akt,fmdpn_balance_akt,likelyProgressionCount_akt,lostAdopter_akt,lyrCurr3mNRxShareChangePct_akt,lyrCurr4wNRxShareChangePct_akt,dblPVC4wTRxPriorComMktShare_akt,dblPVC4wTRxCurrMedDMktShare_akt,dblPVC4wTRxCurrComMktShare_akt,dblPVC3mNRxVolume_akt,productRelativeValue_akt,dblPVC4wTRxPriorMedDMktShare_akt,dblPO4wMedDNRxShareChange_akt,dblPO4wComNRxShareChange_akt,dblPO3mNRxVolume_akt,dblPO3mNRxMedDBusPct_akt,dblPO3mNRxComBusPct_akt,dblPO3mNRxMedDVolume_akt,dblPO3mMedDNRxVolume_akt,dblPO3mNRxComVolume_akt,dblPO3mComNRxVolume_akt,dblPO13wCopayRedemptions_akt,dblPO13wCopayDrops_akt,dblPO4wNRxPriorMedDMktShare_akt,dblPO4wNRxPriorComMktShare_akt,dblPO4wNRxCurrMedDMktShare_akt,dblPO4wNRxCurrComMktShare_akt,dblOAC3mTRxMedDBusPct_akt,dblOAC3mTRxComBusPct_akt,dblNOAC3mTRxPrevMktShare_akt,dblNOAC3mNRxPrevMktShare_akt,dblNOAC3mTRxCurrMktShare_akt,dblNOAC3mNRxVolume_akt,dblNOAC3mNRxCurrMktShare_akt,dblNOAC3mNRxShareChgPct_akt,bosNewPrescriberFlag_akt,newPatientCount_akt,dblPVC13wCopayDrops_akt,newPatientStartCount_akt,newWriter_akt,dblPVC13wCopayRedemptions_akt,dblPVC3mTRxComVolume_akt,dblPVC3mTRxVolume_akt,dblPVC4wComTRxShareChange_akt,dblPVC4wMedDTRxShareChange_akt,dblPVCEstring3mComTRxVolume_akt,dblPVCEstring3mTRxComBusPct_akt,dblPVCEstring3mTRxMedDBusPct_akt,dblPVCEstring3mMedDTRxVolume_akt,sampleAO_akt,samples_akt,sendProbability_akt,sendsCompleted_dse_akt,sendsGoal_dse_akt,sendsRemaining_dse_akt,dblSSI12mMedDTRxMktShare_akt,top80_akt,top10Down_akt,top10MktShareDown_akt,top10MktShareUp_akt,top10MktVolUp_akt,top10Up_akt,topWriterNoTRx_akt,tried13w_akt,dblUHG12mMedDTRxMktShare_akt,visitProbability_akt,unshippedReferralsFlag_akt,visitDetailsCompleted_dse_akt,visitDetailsGoal_dse_akt,visitDetailsRemaining_dse_akt,dblWarfarin3mNRxShareChgPct_akt,dblWarfarin3mNRxCurrMktShare_akt,dblWarfarin3mTRxCurrMktShare_akt,dblWar3mTRxCurrComMktShare_akt,dblWar3mTRxCurrMedDMktShare_akt,dblWarfarin3mNRxPrevMktShare_akt,dblWarfarin3mTRxPrevMktShare_akt,webEDetailsCompleted_dse_akt,webEDetailsGoal_dse_akt,webEDetailsRemaining_dse_akt,dblXarelto4wNRxCurrMktShare_akt,dblXarelto3mNRxCurrMktShare_akt,dblXar3mTRxCurrComMktShare_akt,dblXar3mTRxCurrMedDMktShare_akt,prodMktShare_akt,prodRank_akt,intXeljanzXRName_akt,dblLyr3mNRxCurrMktShare_akt,dblLyr4wNRxCurrMktShare_akt,lyricaOD_akt,dblLyr3mNRxPrevMktShare_akt,dblLyr4wNRxPrevMktShare_akt
INFO [2019-07-31 17:01:03] 1 of 21 numeric predictors (53 all predictors) from AccountProduct/Account dropped before loop to build models as there are either less than 5 unique values or having <2 quantile >0: priorityChantix_akt
INFO [2019-07-31 17:01:03] 0 of 52 remaining predictors from AccountProduct/Account dropped before loop to build models as they have less than 2 unique values (2nd check after processing numeric and character variable separately: 
INFO [2019-07-31 17:01:03] After doing dcast, num of predictors in AccountProduct/Account changed from 52 to 533
INFO [2019-07-31 17:01:03] After adding predictors from EmailTopic Open Rates, final num of predictors in AccountProduct/Account added 72 and becomes 605
INFO [2019-07-31 17:01:03] return from buildStaticDesignMatrix
INFO [2019-07-31 17:01:03] Processing dynamic predictors
INFO [2019-07-31 17:01:03] Adding priorVisit feature from Visit Channel to interactions
INFO [2019-07-31 17:01:03] Number of targets: 5
INFO [2019-07-31 17:01:03] Analyzing product: CHANTIX
INFO [2019-07-31 17:01:03] Starting target loop
INFO [2019-07-31 17:01:03] Analyze OPEN___a3RA00000001MtAMAU
INFO [2019-07-31 17:01:03] number of TargetName records: 29
INFO [2019-07-31 17:01:03] number of SendName records: 319
INFO [2019-07-31 17:01:03] build dynamic features in design matrix (account's previous open/send/click msg behavior)
INFO [2019-07-31 17:01:03] Number of accounts considered for dynamic features: 222
INFO [2019-07-31 17:01:03] processing priorVisit feature from Visit Channel, make it into binary
INFO [2019-07-31 17:01:03] merging dynamic and static features to get the full design matrix: dim(dynamic_feature) = (227,34)
INFO [2019-07-31 17:01:03] check for number of design matrix records > 20? dimension of design matrix = (315,638)
INFO [2019-07-31 17:01:03] check again to make sure targetName is one of columns in design matrix
INFO [2019-07-31 17:01:03] more than 5 positive target records needed to continue - found 20
INFO [2019-07-31 17:01:03] dimension of design matrix with positive send records: (227,638)
INFO [2019-07-31 17:01:03] 133 of 638 predictors from design matrix allModel(static + dynamic features) dropped in target loop as they have less than 2 unique values after subsetting with send and filling NA with 0: SEND___a3RA00000001MtAMAU,priorVisit_13_days_before_send,priorVisit_17_days_before_send,priorVisit_19_days_before_send,priorVisit_23_days_before_send,priorVisit_26_days_before_send,priorVisit_27_days_before_send,priorVisit_29_days_before_send,priorVisit_30_days_before_send,1638764e125e1e322d5f0f9aad65ab1d,2532079fa411887ab9d0d8eb2f16215f,e1fa7879bb11ba852e990903458dc1ad,6050d344fae03f4ade719f99f119884e,eee9def141f8fc54314b36a32c58915f,c3c253904585d329542d40aa38b8cb4e,b5f72817f2c055c4b5859aac54735e0a,30b7aecf0075959b9b239df0d2a5ca2c,d3fdefd5cd999adb9ffae9cf571390a6,544e87ab8349bf29c468463b0d680df6,dee870ae5e762ae26f5fac436d20baa9,375d1a846cf062aae4b0720b447b708f,1f8028f53888c795bfe999c264b8d1e3,c253a84d90f58dd304589127523a3d9c,3df87ece807f61a33efacd3bbb698076,226bc37e85b4a88664b291417feef7cc,732b93b6280dc53597bd32a39d689a96,8f4005c6cc08c49d209cd2d270fe3bf6,2c044e147fa46f574a427a6d0ceec0e5,f49d5d2e9e9bf2f35b05d9de4ed4d57a,bce8093b1d0c9d50dd5a8bd32ac95d5f,7a727a0672444bf64c3b28888b980429,5b3d29ffd7437f77d00bb7d2972ca682,1f9e7d0fc6cb68dd06dc477acac9ee49,c4d19366f3bc5e63016a967dae16ee44,c79404c493a0b88d1cddf677f0e6ae8c,d64e4e9b732e3aa9a3ca6037f0525bca,5b1cab026fed4b61eb84a64ed8ccc008,c4cbb38648a485afdb508df953d3e177,eced6a23fb2fbc67c50cfba06ee55a01,5e3cfd121d5eec12b5a24951f784d43b,75734b6b0e32919ca00f400d3ba5008f,2c8fbf6e35f8bfedb136c9a2d53f1243,8fe880c935d1f4280f7dd2a4713839f6,08ad7af58dccbee427c4dbbba8df2bb4,c5b3f9eb755b23d58b627584a88f9b39,32b2c25468593a1c4520d41c7c966eee,a02bb1fc77a53b1ad696c1f1c3abf471,175bbf801c3fbd915d2c431cbf327984,cf897bd95493d3b1f447012ae31e5eb5,792d69a38272a575ac44b27ba82e2b3f,07e9f9b0a7a77219f52372814bd5c71f,cd719c1cd5416595228bb557632fb9d7,d7b2ab24bc32793459f889f781b47540,82204235399429405fc5dfd861639eb6,3e64c36f77d0683f15c7b874882ef22c,2087b65a48c32210255c5758af8c3b4d,ad9f44ae296b3320bbaeea11a0a099e5,1f8aa615d00e75bd55408708656260ed,2c627621f1395280306db1a6f151a035,9bf6dd460bbd2590139ab09e2e194574,d6f1d73b1a61be37b8e59ef9f182840e,6c814fe8d6d0dc4147014cf9d2fb54ae,724697998bcbc9d94351a40ebfdfd28a,943f7bef3260b7e8a1e6442c1f293fd3,c34a352edc09788ad661517a97c180ba,39b279e2d153a9cff8c807dc645f6e8d,5203a8242366a55188e56696c0463f26,254e298985e6ab67c3a7461b97a6fffb,31024ded0e3a005ff1bac8a1c4f0ceb1,493bb934706b7ca58fbf1acec024ab14,4ed73d74682e278ecd16d0820a4ba6ee,73a967e68de66f397214e1824cc7add9,b806e840f95dde4030dbd45d1d82493d,8dfe86bd285a7bdd7b8919692a00ef2b,c364d86612330f48c1feb92fa0ac39b1,371aa61a3450363ad19e0b945c174137,61b546178e94c371e0c4b78319335a48,7403868ab8ab4e18856fe29606ed9db9,e3253109e617d9dcf0e416e779f79ee1,7b24c206e1fa802016928c9655c472d0,1147be9701393e2e8c9f352f73fc339d,70d539cc6a927cd49da21504cdb6a5ba,a1a80db072ceb0906578053912c157a4,c7baf61a2b22f3c77d2ea9355a74d107,f4d4ac4c54f35eb234251dd6a880259e,cad662ff2d48dabc4388a5128a1fc264,ea226b84587611bd85b3ea903d87f605,RTE_CLICK_2003_3,RTE_CLICK_2004_1,RTE_CLICK_2005_1,RTE_CLICK_2005_2,RTE_CLICK_2006_6,RTE_CLICK_2006_7,RTE_CLICK_2008_4,RTE_CLICK_2008_8,RTE_CLICK_2009_1,RTE_CLICK_2010_8,RTE_CLICK_2012_3,RTE_CLICK_2014_1,RTE_CLICK_2015_1,RTE_CLICK_2017_1,RTE_CLICK_2017_2,RTE_OPEN_2003_2,RTE_OPEN_2004_1,RTE_OPEN_2004_6,RTE_OPEN_2005_1,RTE_OPEN_2005_2,RTE_OPEN_2005_3,RTE_OPEN_2005_4,RTE_OPEN_2006_2,RTE_OPEN_2006_4,RTE_OPEN_2006_5,RTE_OPEN_2007_1,RTE_OPEN_2007_2,RTE_OPEN_2007_3,RTE_OPEN_2007_6,RTE_OPEN_2008_2,RTE_OPEN_2008_3,RTE_OPEN_2008_4,RTE_OPEN_2008_5,RTE_OPEN_2008_6,RTE_OPEN_2008_8,RTE_OPEN_2009_4,RTE_OPEN_2010_1,RTE_OPEN_2010_2,RTE_OPEN_2012_2,RTE_OPEN_2012_3,RTE_OPEN_2012_5,RTE_OPEN_2013_1,RTE_OPEN_2014_1,RTE_OPEN_2014_2,RTE_OPEN_2014_3,RTE_OPEN_2017_3
INFO [2019-07-31 17:01:03] 3 of 505 remaining predictors in the design matrix dropped because of learning.properties config setting: RTE_CLICK_2002_1,RTE_CLICK_2011_1,RTE_CLICK_2011_2
INFO [2019-07-31 17:01:03] final design matrix dimension = (227,502)
INFO [2019-07-31 17:01:05] H2O object tmp.hex rows : 227
INFO [2019-07-31 17:01:05] removed empty lines if any (insert by h2o). final tmp.hex rows = 227
INFO [2019-07-31 17:01:17] Finish modeling
INFO [2019-07-31 17:01:17] Analyze OPEN___a3RA0000000e0u5MAA
INFO [2019-07-31 17:01:17] number of TargetName records: 0
INFO [2019-07-31 17:01:17] number of SendName records: 4
INFO [2019-07-31 17:01:17] Finish modeling, Nothing to model as there are no targets in the data.
INFO [2019-07-31 17:01:17] Analyze OPEN___a3RA0000000e0wBMAQ
INFO [2019-07-31 17:01:17] number of TargetName records: 1
INFO [2019-07-31 17:01:17] number of SendName records: 7
INFO [2019-07-31 17:01:17] build dynamic features in design matrix (account's previous open/send/click msg behavior)
INFO [2019-07-31 17:01:17] Number of accounts considered for dynamic features: 7
INFO [2019-07-31 17:01:17] processing priorVisit feature from Visit Channel, make it into binary
INFO [2019-07-31 17:01:17] merging dynamic and static features to get the full design matrix: dim(dynamic_feature) = (7,37)
INFO [2019-07-31 17:01:17] check for number of design matrix records > 20? dimension of design matrix = (309,641)
INFO [2019-07-31 17:01:17] check again to make sure targetName is one of columns in design matrix
INFO [2019-07-31 17:01:17] more than 5 positive target records needed to continue - found 1
INFO [2019-07-31 17:01:17] Finish modeling, Nothing to model as there are not enough positive target records in design matrix to build model.
INFO [2019-07-31 17:01:17] Analyze OPEN___a3RA0000000e486MAA
INFO [2019-07-31 17:01:17] number of TargetName records: 24
INFO [2019-07-31 17:01:17] number of SendName records: 76
INFO [2019-07-31 17:01:17] build dynamic features in design matrix (account's previous open/send/click msg behavior)
INFO [2019-07-31 17:01:17] Number of accounts considered for dynamic features: 68
INFO [2019-07-31 17:01:17] processing priorVisit feature from Visit Channel, make it into binary
INFO [2019-07-31 17:01:17] merging dynamic and static features to get the full design matrix: dim(dynamic_feature) = (72,38)
INFO [2019-07-31 17:01:17] check for number of design matrix records > 20? dimension of design matrix = (313,642)
INFO [2019-07-31 17:01:17] check again to make sure targetName is one of columns in design matrix
INFO [2019-07-31 17:01:17] more than 5 positive target records needed to continue - found 21
INFO [2019-07-31 17:01:17] dimension of design matrix with positive send records: (71,642)
INFO [2019-07-31 17:01:17] 282 of 642 predictors from design matrix allModel(static + dynamic features) dropped in target loop as they have less than 2 unique values after subsetting with send and filling NA with 0: SEND___a3RA0000000e486MAA,priorVisit_3_days_before_send,priorVisit_5_days_before_send,priorVisit_7_days_before_send,priorVisit_10_days_before_send,priorVisit_15_days_before_send,priorVisit_20_days_before_send,priorVisit_22_days_before_send,priorVisit_23_days_before_send,priorVisit_26_days_before_send,priorVisit_29_days_before_send,21cd5b5cc277c8206a168cbb52cbbbc0,04155068243d1c35e60786282b619635,941c8a152435d960b462ffe0d4142833,a1889f9d00e416eff1180ccd832dc804,d3b30932f7241021ee123159110082ce,884350ef7912d225ca6eaed37cbc6000,2532079fa411887ab9d0d8eb2f16215f,95d2e8542e326b1a71512939b168de96,332dca28e355552aad6e48b8c08e2add,2ea3d6dc10b6fb675ba6ea52ced600bf,c0466aef331def4893a441f770f24b9f,da771374145c64cc3d2f782d37b4009e,7bc640ebe0bcc80060cdd0076560c303,b9465ef235954fb928d8db4dd04235a7,056ebb0f31ab3beb560e7b1831949dff,aacccf2e65d612dae664a017714ede0e,1cc42c644937a28e10d787aa81b2c5e4,26e6db99dee5f57567d24876893baaca,c4d5a644c1071302dbd14d042544734b,e283ba626c606bd8a4c5a2054795d8ca,596b525b659f8a52549a19718cdba2a9,5c3d863dcbf87fbe04ed5b489c725e44,11413eb6dc265f31eeb1d1b39581c137,b2f6bc545d96d2c9aa64842fd887bd7f,28ce08e7af93591f1905f64f44904254,82b6e30285431a1096eaccdb4c182f83,9f9512812dd4118b88a7689d03a227d1,106e900307f204c0fab7a4b0b3d978e6,6b6987a14553b2a5ebec8c46b33d9252,b0a6f21722495bd822aa1fa1836f82cd,e308251f761e80c3d5512c003514c428,5238df8c1f354b3a938b3f0f21e3122e,3af7126a141cb20e710db6cb0e511deb,d358a282e44cfe1ee546922ccc1f8b47,f17cb7ca2a64944f0b8b313865a57089,d13e494e1fd458ee703eed1d390e6b68,c7f458325111b73c77f7d5c96f244b99,d3fdefd5cd999adb9ffae9cf571390a6,3f07d11479170e63ed5ddf7e9348c04f,5dae069dd3bc5784b3547e53287a566d,fb1737332142f08b9963c2103d0215bc,375d1a846cf062aae4b0720b447b708f,edf3468ce91ac27ea290fa70734c0a68,aab11a22b8a41a3994e822f6d7a03300,495be1fd48b4f7de4746b421b7c0df64,2e5f2d71ac34d04381d0345895048599,6f74736c86c078c05a6faf9226900d76,9c649cc65bacde1bbb84e86131f8764c,1f62f7c75ea206e4700d59e14c28c878,1511ef2c54fc2d2d62b33cb78c44e6ec,5fa18f33983ddb5746a49fe7afb08e94,7e07987a133ec9ad7797d532b0e8734d,226bc37e85b4a88664b291417feef7cc,7c2b9776688f184517e805f55040f274,8f4005c6cc08c49d209cd2d270fe3bf6,4bde6297b35f3140a95242fa1c990924,2c044e147fa46f574a427a6d0ceec0e5,54fa23d4340e3c77cd7df988d525fddc,f401af3eb6903c11cd8e5c2eb959c1c5,b0496d4ba67689ad2802ff3749cc845c,1445f1e586af7e96f11495949371e524,51834d890f0e5f95c701f5133f23e7c1,50b14e74cb1699e2f8c06cf2ad47ff52,ecc82e76a5bd2bf11786d39b426c7893,e86e62c4bc8a7a8fe1a461e7dbf603cc,0b9c4cbe55baf3655e7de6cea2b9b901,713265a737c39b63b5a00363d00b316b,aeb46e4387551195b92374d7b233f982,159a80c18476c9c1ee53fdcf7a6b8d3c,c3629a31fa1967ff9d8c6d2f5e6f4074,c79404c493a0b88d1cddf677f0e6ae8c,05c0a1f62fc6aa7724618ad41ec86b6e,d29c486fa263c928dd735ec035e035f5,ed7d3b86e6563627c0ffb94876b6842e,36eeb0daf3829c56540a59a2145f00f4,1ff1615bb438db8a8323304620b70e05,f9f28d688aa76ec88b993b2200b1df60,14ccf950ba4870ed736e13efb8ae2889,43548d0aa62dff471b269d3123023db9,3da7b4a89333133c1b31712980598869,a9226a5af564e610c71e4012faeeac0a,3b97fd944c51d6b5507b0568988a9852,7c76927ac4e0c1e89906cd11a6dce60a,7bf3f49ca4e5889dd7b7307ce5fc193a,46eedda839bc8b831222c2c3b37ddc8f,5b1cab026fed4b61eb84a64ed8ccc008,2be328402766542496bc1bf3052f0fe2,01398dbfb797661ca7eae952ed3dc480,5b48c32c8b7582b27fb9555cdffdab37,b9469877fbf5f11867dfbf81c2442ac6,5df73578ce955a286b6b1d5f3c06e780,db8a27da94c558ba39d28640b12c9145,2e4bd84ce138462fb3bea925a89feb87,37417979b332041b3a0e96dad4d40cf7,0c7719a6910f13a569387eb0e27baac2,046084c59c3ca1a65f5fe30f8e851152,9578616e529d6361f4be38074f9959ee,c7394f29703cf2932324e66227d39c87,9b1031a4c3dd9a38f1a82a89b472de69,9dee15027654a93d388821f06b64ca71,50c152492892200ed1d7ed2319a96db9,aaa3d5f610053f449394a1a7c9bcdbb6,36cd73165c08e195a5a6f9b40f773084,a8ae18e8d4146e98bb19723ddeb8b8cd,0585420c604d3ccac437707c42bd6570,d9159ebb38d5c294dc56cb6a958e011c,d7fc2a51074393d1acc9451d0507a314,7220e6df8a96961c1f49e0f49577ad47,1aa664137dddcb3a72f1c87e020d3a00,284445d45af5baeaa1ef19b2cfe8ed73,350f6797945486aa1bd7a7677056049c,43075e81662cc713d07f8dc12237f808,341378ae583fcb672417054e02a50e4a,640f3c349789de2f39fd541c31bd2139,c18896800411a87cdfa19499897256cc,74b5089065d997067e87491d4c65103b,af45e40a5fcba2ac2a699f48bc43b6ff,46b28924b8af9246b49020041e1f104d,ef710f600877f8e0b7b287b13fe7d3cb,4402c9fbdbb7741e6a371e806bf3c2b1,d226f5abdbbf337ea0a12947f6443aab,ef7e894982401fc582c34d0b571dec3f,e8db44d36d6bff6c196ef9a15dfdef22,02a779ae6b5c306ffe4c7ef97fe58112,974d5f6b25ef1457c2c019770487b585,76c9dd346c07c4b1e7900709b227ad0f,a807eafe52faca08469af98d58d70092,f42a2868553bcce47057f97f6f529a18,54b027e566001abf5864bd59b729689d,c9e1ebd834107c8e8fa9b8adb3b2bc59,d97ba21d90658767d704163e54ed9182,162a5c04b64d2df8ec5df7d68a867151,20d7641fcc9376b4dc02d7512cf4f2ab,0ae5d6fcb162af8395296673ac32492e,1b22819ae6956f07d551b8c2df1dd5d5,ed00fdd91fa904b326bbd8e48b2d83a2,835523d36f5080bb12960c8c064332ec,4b5bd880a10734ffc98ef0e29bf12884,83c1eec5501c79cdb0c1650e2351a1f4,9c132677509d6a9c0a7ed46d91ddfbc4,688d2c4346ec4cd11129e62b9e41d15e,196438f0df47d7c96b22e0a3c2586bf2,9c5a4184622d513507c0082f77d8a91e,b4a2e33b8557c10bb0eedb5b4a9f1999,2c3d21360e4f1edc90b15aee87b612df,760848dc8c5f4d75eed5b464a8b57af1,10c39773e7dde70a1a761d73982ab6b8,238a0b7a431e88d74340af8813c94ae5,c757b4d497bfea1c1b24064c6fbe0021,bfed938dad333ab5331389288eab6dab,4b18f7963b17f21892ce03920e2435e8,16efd548c9686388d64eaae3079932f6,2d2cb3be54a38d9bd7532f5eb52c050a,2c627621f1395280306db1a6f151a035,0ca01162994f9a4c60454822e7a16f1f,cd13e813e1f61bf36493f6fefa162256,c7ed6bffb82f79722eb454153ad23d0d,e6c5701dbb4b5964535485c91c6ea2b3,d6f1d73b1a61be37b8e59ef9f182840e,a0e33cf489cc5cc4be98f26d9488d1cf,fb096fedef4d064798a62ffea49ec58f,de4fa3a1ef8f48ad9535545880daa9b4,873d2d7625de3757287be78111410cc6,9e9edaf004d50a41cdbaefbdd5dd8754,e39efd17897f7356fc9eb7af127c4a72,e98c4cdd381ca2248314a51d43ee0ad1,892ea877c5d8579d372851825ad0dbe4,c34a352edc09788ad661517a97c180ba,c7420cc56960d59aace3aed36b68411f,82c65cc117021a678325b8d136b300c4,21aeeaa124d9fef13c48eea897c7ce0b,08605d91383ee07bcc5efece4321b0f9,1814a8c2745ecb4936e4347e6c76cf03,1b9cacfcd1fef2b7f4e4397170bc9a07,437e963b06f250571249598d0cd70db7,16315dcd0abd64d2ef7fe1792c467c8a,4427d9bf9efd7a7314f6b3dcbfe35ced,17a4827b7084cf25e63d15b01e0ad6f7,bacb524845c278aa0eb3c7fbb6b0fe93,1ed7124518fcf19766137cccf36cd4c4,7bfaefda5fc0288261b8499c23ed49f1,e96891af260ecb6219444243a9ad680c,c14447d603e4f2945da90f34bfa3b9ab,9ebfd1bda7149215a0b2ec1a54e05804,fd0da869b6e54548a15840a4742d926d,3d6d89d9d5e5cfee112ec4621d6e50f1,82b24b263285cab09d8785ca65fb149c,fae2a65f94f54449d3769c36caf11ea1,861327434e5d3a1273744c776bfd1b8c,254e298985e6ab67c3a7461b97a6fffb,61f977b90ba5279a0d540c1ae4994a34,c9999b2c393c1a1b67337336c22bb620,d9e1b80cb39d71a50e870be75d206209,532e1c7e66458966004c2c2c13d15ad0,c554659fb89a7bbed1a5203e0f71bef1,7dc1b8d4ddc51e7a1babfcd9b599bc6a,3d99730f4ba8a045693eec807b955a04,5a7efbf067267aa4ae2ba5b2df7cf113,ac0124800976fc137b2f1f8003261d6e,7511a913e50e662dce937a9fd0d7d09e,ce40df4d028154171c4e3296d5092a8a,ea576ac98a08acce0ee6e72257181f74,a8ba67338c7a706472c68e8f547eae45,0e0101ede5c119b84dca8deb920f7721,00f329920341761c411b016e67e88704,830c77916d6b4c440696e41f08c36992,06da5473c25543652ee16748f8d8fe04,8a8b3a0e0b001e97d751a4456a382cd8,38441c7279d42db9d723053cc6d14712,89ff6f9cf9dc7273fb72d40f14cbf3c6,f39faf3f036cd0aba174e8208d9331d2,1124384c17fe503282e9942a36430e01,4009cee8611ff1b673f988931782ec2f,e31c4f9674f2864874f9edb0e21eb1c5,c1cc42276aa5c9355b0085781e69d020,5b09ded1326fd63f57fe6bd22bd81f2b,508943803e3d8520ae07fe72a702b457,fc130e6ecca60fca59a69471233df74d,40b4b1f0bea61aa4f444c1cf556c70ae,73091c00b9771db7e4b099a890542b11,df12634aa07267349f0456d51e1d20bd,039aebebb736101682a8b931f1518dbb,c0419a4dd86f8f5690c74c5cbf5a04ee,df06a44734e6bca26947bf233756e4af,afe5e356f9b5a27c4dce9ac6c978094f,e3253109e617d9dcf0e416e779f79ee1,b6b272ad505a060af1a0f422d76c5bed,00a87d44bdde9a87392dfc642a9655de,3d48f9068642ba39a8a0493b2b73bdd6,ecf905d51e3683029ea57acb82c2dda1,1fc5f95939b10aacce8c4ce6e80232b6,89b40a979416d7367e677564b65c4b57,7b24c206e1fa802016928c9655c472d0,c972092aa738f7109421881c8fc10710,c978aa25774e90275da4c676fbd2988f,4289eaafbe2f482ff78e5089c2268890,f8ff158bf95dc88ee721ad8be974030e,a1a80db072ceb0906578053912c157a4,e06160660c67d235486664f0a787f96e,eceafaa7f8f1c2865f4e6ac131b1cdf1,8839ac8cada974327fc0bbe60a2bf4c1,9fdb2b7c908b8109335abc9fe2fa9f24,6a7b3c45ee55b288da59b0ab2666b72f,e69ff13419b0fdf31f7ec7c256c0a510,4c2e024a7121707a6b04a75174e3bc63,b7dc591bfffa4b3cc89c680c3828b6bf,a2a5cffcc27466938bd884a85e0a69e5,06e068193e371f1930439084fe830501,d91145e93297f084c45ed55d2e12c1d1,6b0342cbd19e858a41cb5aeb75febc82,44c545cb75c7a7aba8ebf3cb76938f89,7f6b4ff4fb0f1696754052ab8a18645c,4efa53ed6b921194dc6c23b7f80f8eb0,8cc9f18a3f032584cc2de1a963d733ab,9d3528cf19ae1e3230209f2eed510542,5bb926cbe8d5726665bd454f3c57c05e,54ffe40c29eb9b39c44910efb457c13d,38f032be4af996477e251e522cf53f4d,da0723903e979b0b1d503c9e1b777c7a,c801297e8b49613aeac593f3799fc041,c33f673b40c6f36b61b9c425a2d23271,RTE_CLICK_2006_6,RTE_CLICK_2011_2,RTE_OPEN_2002_4,RTE_OPEN_2002_9,RTE_OPEN_2008_4,RTE_OPEN_2011_2,RTE_OPEN_2011_3,RTE_OPEN_2011_9,RTE_OPEN_2012_2,RTE_OPEN_2014_4
INFO [2019-07-31 17:01:17] 16 of 360 remaining predictors in the design matrix dropped because of learning.properties config setting: RTE_CLICK_2002_1,RTE_CLICK_2003_3,RTE_CLICK_2004_1,RTE_CLICK_2005_1,RTE_CLICK_2005_2,RTE_CLICK_2006_7,RTE_CLICK_2008_4,RTE_CLICK_2008_8,RTE_CLICK_2009_1,RTE_CLICK_2010_8,RTE_CLICK_2011_1,RTE_CLICK_2012_3,RTE_CLICK_2014_1,RTE_CLICK_2015_1,RTE_CLICK_2017_1,RTE_CLICK_2017_2
INFO [2019-07-31 17:01:17] final design matrix dimension = (71,344)
INFO [2019-07-31 17:01:18] H2O object tmp.hex rows : 71
INFO [2019-07-31 17:01:18] removed empty lines if any (insert by h2o). final tmp.hex rows = 71
INFO [2019-07-31 17:01:21] Finish modeling
INFO [2019-07-31 17:01:21] Analyze OPEN___a3RA0000000e47gMAA
INFO [2019-07-31 17:01:21] number of TargetName records: 1
INFO [2019-07-31 17:01:21] number of SendName records: 14
INFO [2019-07-31 17:01:21] build dynamic features in design matrix (account's previous open/send/click msg behavior)
INFO [2019-07-31 17:01:21] Number of accounts considered for dynamic features: 14
INFO [2019-07-31 17:01:21] processing priorVisit feature from Visit Channel, make it into binary
INFO [2019-07-31 17:01:21] merging dynamic and static features to get the full design matrix: dim(dynamic_feature) = (14,36)
INFO [2019-07-31 17:01:21] check for number of design matrix records > 20? dimension of design matrix = (309,640)
INFO [2019-07-31 17:01:21] check again to make sure targetName is one of columns in design matrix
INFO [2019-07-31 17:01:21] more than 5 positive target records needed to continue - found 1
INFO [2019-07-31 17:01:21] Finish modeling, Nothing to model as there are not enough positive target records in design matrix to build model.
INFO [2019-07-31 17:01:21] Model Build Time = 19.*************
INFO [2019-07-31 17:01:22] Starting processing accuracy and importance file
INFO [2019-07-31 17:01:22] getting MessageSet info for adding predictor and target related info to importance and accuracy file
INFO [2019-07-31 17:01:22] Finish modeling all messages, start processing output files
INFO [2019-07-31 17:01:22] load Account/AccountProduct names mapping from its column names using API
INFO [2019-07-31 17:01:22] get tokend successfully
INFO [2019-07-31 17:01:22] GET::https://learningteamdev-learning-api.aktana.com/api/v1.0/DSE/referencedata successful
INFO [2019-07-31 17:01:22] Adding predictor and target related info to importance and accuracy file
INFO [2019-07-31 17:01:22] Save workbook
INFO [2019-07-31 17:01:23] finish saving MSO build resulting accuracy and importance file， return from saveBuildModelExcel
INFO [2019-07-31 17:01:23] save output files to DB
INFO [2019-07-31 17:01:23] finish saving outfiles to LearningFile, return from saveBuildModelDB
INFO [2019-07-31 17:01:23] deployOnSuccess not passed from API, set to FALSE
