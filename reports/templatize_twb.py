import os
import sys
import re


script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)

from common.pyUtils.logger import get_module_logger
logger = get_module_logger("Templatization_Function_TWB")

def replace(data, start, end, replace):
    modifiedline = data
    match = re.match(r'(.+%s\s*).+?(\s*%s.+)' % (start, end), data, re.DOTALL)
    if match:
        modifiedline = match.group(1) + replace + match.group(2)
    return modifiedline

def newFilePath(currentFilePath):
    filepath_to_download = os.path.dirname(currentFilePath)
    base = os.path.basename(currentFilePath)
    filename = os.path.splitext(base)[0]
    final_file_name = filepath_to_download + "/" + filename + '.twb.template'
    return final_file_name

def templatizeFile(filepath):
    outputData = ''
    with open(filepath, 'r') as file:
        for line in file:
            data = line
            if data == "\n":  # if new line, or empty line, continue
                outputData = outputData + data
                continue
            data = replace(data, "site='", "'", "${site}")
            data = replace(data, "path='/t/", "/datasources'", "${site}")
            data = replace(data, "id='", "' path='/t/", "${name}")
            outputData = outputData + data
        file.close()
    return outputData

def main():
    # Validate the input argument to the script
    if len(sys.argv) != 2:
        logger.error("Invalid arguments passed to TWB templatization script")
        exit(0)

    # Retrieve the arguments
    filepath = sys.argv[1]

    # Get input file data templatized
    outputfiledata = templatizeFile(filepath)

    # Write templatized data into output filee
    output_file = open(newFilePath(filepath), "w")
    n = output_file.write(outputfiledata)
    output_file.close()


if __name__ == "__main__":
    main()