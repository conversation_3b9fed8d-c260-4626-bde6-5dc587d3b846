from reports.templatize_tdsx import *

def test_getFileName():
    assert getFileName("desktop/reports/test.tdsx") == "test"

def test_getPathToDownload():
    assert getPathToDownload("desktop/reports/test.tdsx") == "desktop/reports"

def test_templatizeTDSXFile():
    f = open("reports/tests/output_text_tdsx.txt", "r")
    assert templatizeTDSXFile("test", "reports/tests/input_text_tdsx.txt") == f.read()