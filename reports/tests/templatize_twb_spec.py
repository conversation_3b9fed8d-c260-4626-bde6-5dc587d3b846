from reports.templatize_twb import *

script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)

def test_newfilepath_function():
    assert newFilePath("desktop/reports/test.twb") == "desktop/reports/test.twb.template"

def test_replace_function():
    data = " site='aktana' "
    start = "site='"
    end = "'"
    replaceWith = "${site}"
    output = " site='${site}' "
    assert replace(data, start, end, replaceWith) == output

def test_replace_function_empty():
    data = ""
    start = "site='"
    end = "'"
    replaceWith = "${site}"
    output = ""
    assert replace(data, start, end, replaceWith) == output

def test_replace_function_negative():
    data = " site=aktana "
    start = "site='"
    end = "'"
    replaceWith = "${site}"
    output = " site=aktana "
    assert replace(data, start, end, replaceWith) == output

def test_templatizeFile_function():
    f = open("reports/tests/output_text_twb.txt", "r")
    assert templatizeFile("reports/tests/input_text_twb.txt") == f.read()
