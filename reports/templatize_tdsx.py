import os
import sys
import re
from zipfile import ZipFile
from pathlib import Path
import shutil

script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)

from reports.templatize_twb import *
from common.pyUtils.logger import get_module_logger
logger = get_module_logger("Templatization_Function_TDSX")

def getFileName(currentFilePath):
    base = os.path.basename(currentFilePath)
    filename = os.path.splitext(base)[0]
    return filename

def getPathToDownload(currentFilePath):
    filepath_to_download = os.path.dirname(currentFilePath)
    return filepath_to_download

def templatizeTDSXFile(filename, tds_file_path):
    outputData = ""
    with open(tds_file_path, 'r+') as file:
        for line in file:
            data = line
            if data == "\n":  # if new line, or empty line, continue
                outputData = outputData + data
                continue
            if "site='" in line:
                data = replace(data, "site='", "'", "${site}")
            if "path='/t/" in line:
                data = replace(data, "path='/t/", "/datasources'", "${site}")
            if "formatted-name='" in line:
                data = replace(data, "formatted-name='", "'", filename + "_${customer}")
            if "class='hyper'" in line:
                data = replace(data, "dbname='Data/Extracts/", "'", "${extract}")
            if "class='snowflake'" in line:
                data = replace(data, "dbname='", "'", "${dbname}")
            if "class='snowflake'" in line:
                data = replace(data, "schema='", "'", "${schema}")
            if "class='snowflake'" in line:
                data = replace(data, "server='", "'", "${server}")
            if "class='snowflake'" in line:
                data = replace(data, "username='", "'", "${user}")
            if "class='snowflake'" in line:
                data = replace(data, "warehouse='", "'", "${warehouse}")
            if "class='snowflake'" in line:
                data = replace(data, "service='", "'", "${role}")
            outputData = outputData + data
        file.close()
    return outputData

def main():
    # Validate the input argument to the script
    if len(sys.argv) != 2:
        logger.error("Invalid arguments passed to TDSX templatization script")
        exit(0)

    # Retrieve the arguments
    filepath = sys.argv[1]

    # get filename without extension
    filename = getFileName(filepath) # get filename without extension

    # get folder in which the given file is present
    filepath_to_download = getPathToDownload(filepath)

    # copy input file as duplicate zip file
    shutil.copy(filepath, filepath_to_download+'/duplicate.zip')

    # extract files into duplicate folder
    with ZipFile(filepath_to_download+'/duplicate.zip', 'r') as zip_ref:
        zip_ref.extractall(filepath_to_download + "/duplicate")

    # find .hyper file in duplicate/data/extract folder and rename it to filename.hyper.template
    for path in Path(filepath_to_download + "/duplicate/Data/Extracts/").iterdir():
        if path.is_file():
            path.rename(Path(filepath_to_download + "/duplicate/Data/Extracts/", filename+".hyper.template"))

    # find .tds file in duplicate folder and rename to filename.tds.template
    for path in Path(filepath_to_download + "/duplicate").iterdir():
        if path.is_file():
            path.rename(Path(filepath_to_download + "/duplicate", filename+".tds.template"))

    # templatize tds.template
    tds_file_path = filepath_to_download + "/duplicate/" + filename + ".tds.template"

    outputData = templatizeTDSXFile(filename, tds_file_path)

    output_file = open(tds_file_path, "r+")
    output_file.seek(0)
    output_file.truncate()
    n = output_file.write(outputData)
    output_file.close()

    # make finalname.zip from data folder and tds.template file
    shutil.make_archive(filepath_to_download + "/" + filename, 'zip', filepath_to_download + "/duplicate")

    # rename filename.zip to filename.tdsx.template
    os.rename(filepath_to_download + "/" + filename + ".zip", filepath_to_download + "/" + filename + ".tdsx.template")

    # clean up intermediate file / folder
    os.remove(filepath_to_download + "/" + "duplicate.zip")
    shutil.rmtree(filepath_to_download + "/" + "duplicate", ignore_errors=True)

if __name__ == "__main__":
    main()
