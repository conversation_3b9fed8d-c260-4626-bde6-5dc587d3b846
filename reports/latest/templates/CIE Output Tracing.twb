<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20231.23.0310.1045                               -->
<workbook locale='en_US' original-version='18.1' source-build='2021.4.16 (20214.23.0209.1538)' source-platform='mac' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.AccessibleZoneTabOrder.true...AccessibleZoneTabOrder />
    <_.fcp.AnimationOnByDefault.true...AnimationOnByDefault />
    <AutoCreateAndUpdateDSDPhoneLayouts />
    <ISO8601DefaultCalendarPref />
    <ISO8601PeriodTypes />
    <_.fcp.MarkAnimation.true...MarkAnimation />
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.ParameterDefaultValues.true...ParameterDefaultValues />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <SetMembershipControl />
    <SheetIdentifierTracking />
    <SortTagCleanup />
    <WindowsPersistSimpleIdentifiers />
    <ZoneBackgroundTransparency />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracing?rev=1.0' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracing' path='/t/${TABLEAU_SITE}/workbooks' revision='1.0' site='${TABLEAU_SITE}' />
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <_.fcp.AnimationOnByDefault.false...style>
    <_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule element='animation'>
      <_.fcp.AnimationOnByDefault.false...format attr='animation-on' value='ao-on' />
    </_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule>
  </_.fcp.AnimationOnByDefault.false...style>
  <datasources>
    <datasource hasconnection='false' inline='true' name='Parameters' version='18.1'>
      <aliases enabled='yes' />
      <column caption='Latest Run' datatype='string' datatype-customized='true' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;True&quot;'>
        <calculation class='tableau' formula='&quot;True&quot;' />
        <members>
          <member value='&quot;All&quot;' />
          <member value='&quot;True&quot;' />
        </members>
      </column>
      <column _.fcp.ParameterDefaultValues.true...source-field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[CUSTOMER_NAME]' caption='Customer' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;&quot;'>
        <calculation class='tableau' formula='&quot;&quot;' />
      </column>
      <column caption='Scenario Group' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
        <calculation class='tableau' formula='&quot;Published&quot;' />
        <members>
          <member value='&quot;(All)&quot;' />
          <member value='&quot;Published&quot;' />
        </members>
      </column>
      <column _.fcp.ParameterDefaultValues.true...default-value-field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.0jftwg81dwdpx011u1p600ratzos].[Date (-10 days) (copy) (copy)_490329422740410370]' caption='Start Date' datatype='date' name='[Parameter 4]' param-domain-type='any' role='measure' type='quantitative' value='#2021-12-02#'>
        <calculation class='tableau' formula='#2021-12-02#' />
      </column>
      <column _.fcp.ParameterDefaultValues.true...default-value-field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.0jftwg81dwdpx011u1p600ratzos].[Start Date (copy)_1324339778785996800]' caption='End Date' datatype='date' name='[Parameter 5]' param-domain-type='any' role='measure' type='quantitative' value='#2021-12-15#'>
        <calculation class='tableau' formula='#2021-12-15#' />
      </column>
    </datasource>
    <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model' inline='true' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh' version='18.1'>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracingDataModel?rev=1.0' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracingDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.0' site='${TABLEAU_SITE}' />
      <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracingDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
        <calculations>
          <calculation column='[ACCOUNT_CNT (new version) (copy)_497647790488961026]' formula='{FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], &#13;&#10;[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:&#13;&#10;countd([SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&#13;&#10;}' />
          <calculation column='[CANDIDATE_CNT (new version) (copy)_497647790489550852]' formula='count(iif([RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],null))' />
          <calculation column='[Calculation_1716434419576819713]' formula='[DCO_REASON_CODE] +&apos;. &apos;+[DCO_REASON_NAME]' />
          <calculation column='[Calculation_497647************]' formula='{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]:countd([USER_NAME])}' />
          <calculation column='[Calculation_5911010483556352]' formula='[SCENARIO_NAME]+ IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;, &apos;&apos;)' />
          <calculation column='[Channel (copy)_544865224190140424]' formula='[CHANNEL_NAME]' />
          <calculation column='[DAY_CNT (new version) (copy)_497647790489280515]' formula='count([PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])' />
          <calculation column='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' formula='[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433] +&apos; &apos;+[DCO_REASON_NAME]' />
          <calculation column='[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433]' formula='IF [DCO_REASON_TYPE_NAME] =&apos;Accept&apos; and [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]then &apos;Published&apos; else [DCO_REASON_TYPE_NAME]  end' />
          <calculation column='[Date (copy)_986006877293236225]' formula='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <calculation column='[Driver/Type (copy)_544865224190218249]' formula='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <calculation column='[FINAL_SCORE (copy)_540431991555641356]' formula='avg({FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],&#10;[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:&#10;sum([FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&#10;})' />
          <calculation column='[Factor (copy)_544865224189366278]' formula='[FACTOR_NAME]' />
          <calculation column='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' formula='CASE [Parameters].[Parameter 3]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED (VW_DIM_SCENARIO_RPT)])&#13;&#10;END' />
          <calculation column='[Product (copy)_544865224189894663]' formula='[PRODUCT_NAME]' />
          <calculation column='[REP_CNT (new version) (copy)_497647790488461313]' formula='{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]: countd([ACCOUNT_NAME])}' />
          <calculation column='[Suggested Date (copy)_986006877287538688]' formula='DATE(DATETRUNC(&apos;month&apos;,date([SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])))' />
        </calculations>
        <metadata-records>
          <metadata-record class='measure'>
            <remote-name>ACCOUNT_CNT (new version) (copy)_497647790488961026</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[ACCOUNT_CNT (new version) (copy)_497647790488961026]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACCOUNT_CNT (new version) (copy)_497647790488961026</remote-alias>
            <ordinal>146</ordinal>
            <layered>true</layered>
            <caption>DAY_CNT</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;{FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], &#13;
[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:&#13;
countd([SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&#13;
}&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ACCOUNT_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ACCOUNT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACCOUNT_ID</remote-alias>
            <ordinal>28</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ACCOUNT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACCOUNT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACCOUNT_NAME</remote-alias>
            <ordinal>33</ordinal>
            <layered>true</layered>
            <caption>Account</caption>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ACCOUNT_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACCOUNT_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACCOUNT_TYPE</remote-alias>
            <ordinal>34</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)</remote-alias>
            <ordinal>29</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <caption>ACCOUNT_UID</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>AFFILIATED_HCP_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[AFFILIATED_HCP_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>AFFILIATED_HCP_UID</remote-alias>
            <ordinal>32</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>CANDIDATE_CNT (new version) (copy)_497647790489550852</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[CANDIDATE_CNT (new version) (copy)_497647790489550852]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CANDIDATE_CNT (new version) (copy)_497647790489550852</remote-alias>
            <ordinal>147</ordinal>
            <layered>true</layered>
            <caption>RECOMMENDED_COUNT</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;count(iif([RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],null))&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CHANNEL_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CHANNEL_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL_CODE</remote-alias>
            <ordinal>50</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CHANNEL_LOCALIZABLE_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CHANNEL_LOCALIZABLE_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL_LOCALIZABLE_NAME</remote-alias>
            <ordinal>51</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CHANNEL_LOCAL_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CHANNEL_LOCAL_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL_LOCAL_NAME</remote-alias>
            <ordinal>52</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CHANNEL_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CHANNEL_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL_NAME</remote-alias>
            <ordinal>49</ordinal>
            <layered>true</layered>
            <caption>Channel</caption>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)</remote-alias>
            <ordinal>48</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>8</ordinal>
            <layered>true</layered>
            <caption>CHANNEL_UID</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>COUNTRY_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[COUNTRY_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>COUNTRY_CODE</remote-alias>
            <ordinal>63</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CUSTOMER_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>COUNTRY_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[COUNTRY_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>COUNTRY_NAME</remote-alias>
            <ordinal>64</ordinal>
            <layered>true</layered>
            <family>VW_DIM_CUSTOMER_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATEDAT_SRC</remote-name>
            <remote-type>7</remote-type>
            <local-name>[CREATEDAT_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATEDAT_SRC</remote-alias>
            <ordinal>99</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_BY</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CREATED_BY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_BY</remote-alias>
            <ordinal>129</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Created By</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>CREATED_DT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[CREATED_DT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_DT_KEY</remote-alias>
            <ordinal>95</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_TS</remote-name>
            <remote-type>7</remote-type>
            <local-name>[CREATED_TS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_TS</remote-alias>
            <ordinal>130</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Created Ts</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CUSTOMER_DESC</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CUSTOMER_DESC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CUSTOMER_DESC</remote-alias>
            <ordinal>61</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CUSTOMER_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>CUSTOMER_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[CUSTOMER_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CUSTOMER_ID</remote-alias>
            <ordinal>97</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CUSTOMER_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CUSTOMER_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CUSTOMER_NAME</remote-alias>
            <ordinal>60</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CUSTOMER_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1716434419576819713</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1716434419576819713]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1716434419576819713</remote-alias>
            <ordinal>151</ordinal>
            <layered>true</layered>
            <caption>Reasons</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[DCO_REASON_CODE] +&apos;. &apos;+[DCO_REASON_NAME]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_497647************</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_497647************]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_497647************</remote-alias>
            <ordinal>153</ordinal>
            <layered>true</layered>
            <caption>REP_CNT</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]:countd([USER_NAME])}&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_5911010483556352</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_5911010483556352]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_5911010483556352</remote-alias>
            <ordinal>154</ordinal>
            <layered>true</layered>
            <caption>New Scenario</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[SCENARIO_NAME]+ IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;, &apos;&apos;)&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Channel (copy)_544865224190140424</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Channel (copy)_544865224190140424]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Channel (copy)_544865224190140424</remote-alias>
            <ordinal>155</ordinal>
            <layered>true</layered>
            <caption>Channel </caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[CHANNEL_NAME]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DAY_CNT (new version) (copy)_497647790489280515</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[DAY_CNT (new version) (copy)_497647790489280515]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DAY_CNT (new version) (copy)_497647790489280515</remote-alias>
            <ordinal>156</ordinal>
            <layered>true</layered>
            <caption>CANDIDATE_CNT</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;count([PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_CODE</remote-alias>
            <ordinal>75</ordinal>
            <layered>true</layered>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_DESCRIPTION</remote-alias>
            <ordinal>77</ordinal>
            <layered>true</layered>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>347</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_NAME</remote-alias>
            <ordinal>76</ordinal>
            <layered>true</layered>
            <caption>Reason</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>22</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>12</ordinal>
            <layered>true</layered>
            <caption>Reasons_</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>26</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_TYPE_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_TYPE_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_TYPE_CODE</remote-alias>
            <ordinal>78</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>9</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_TYPE_NAME (copy)_660058877027909632</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[DCO_REASON_TYPE_NAME (copy)_660058877027909632]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_TYPE_NAME (copy)_660058877027909632</remote-alias>
            <ordinal>158</ordinal>
            <layered>true</layered>
            <caption>REASON</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433] +&apos; &apos;+[DCO_REASON_NAME]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433</remote-alias>
            <ordinal>159</ordinal>
            <layered>true</layered>
            <caption>DCO_REASON_TYPE_NAME (new)</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [DCO_REASON_TYPE_NAME] =&apos;Accept&apos; and [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]then &apos;Published&apos; else [DCO_REASON_TYPE_NAME]  end&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_TYPE_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_TYPE_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_TYPE_NAME</remote-alias>
            <ordinal>79</ordinal>
            <layered>true</layered>
            <caption>Status</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>9</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)</remote-alias>
            <ordinal>74</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>10</ordinal>
            <layered>true</layered>
            <caption>DCO_REASON_UID</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>26</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <caption>DCO_RUN_DATE</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <caption>DCO_RUN_UID</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DESCRIPTION</remote-alias>
            <ordinal>83</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Description</caption>
            <family>VW_DIM_DSE_CONFIG_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_ACCOUNT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_ACCOUNT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_ACCOUNT_KEY</remote-alias>
            <ordinal>25</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_ACCOUNT_TYPE_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_ACCOUNT_TYPE_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_ACCOUNT_TYPE_KEY</remote-alias>
            <ordinal>44</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_ACTION_GROUP_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_ACTION_GROUP_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_ACTION_GROUP_KEY</remote-alias>
            <ordinal>55</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_ACTIVITY_DELIVERY_MODE_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_ACTIVITY_DELIVERY_MODE_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_ACTIVITY_DELIVERY_MODE_KEY</remote-alias>
            <ordinal>56</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_BRAND_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_BRAND_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_BRAND_KEY</remote-alias>
            <ordinal>104</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CHANNEL_CATEGORY_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CHANNEL_CATEGORY_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CHANNEL_CATEGORY_KEY</remote-alias>
            <ordinal>54</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CHANNEL_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CHANNEL_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CHANNEL_KEY</remote-alias>
            <ordinal>47</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CHANNEL_TYPE_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CHANNEL_TYPE_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CHANNEL_TYPE_KEY</remote-alias>
            <ordinal>53</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_COUNTRY_KEY (VW_DIM_ACCOUNT_DCO_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_COUNTRY_KEY (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_COUNTRY_KEY (VW_DIM_ACCOUNT_DCO_RPT)</remote-alias>
            <ordinal>43</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_COUNTRY_KEY (VW_DIM_PRODUCT_DCO_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_COUNTRY_KEY (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_COUNTRY_KEY (VW_DIM_PRODUCT_DCO_RPT)</remote-alias>
            <ordinal>114</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_COUNTRY_KEY (VW_DIM_USER_DSE_DCO_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_COUNTRY_KEY (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_COUNTRY_KEY (VW_DIM_USER_DSE_DCO_RPT)</remote-alias>
            <ordinal>138</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Dim Country Key (Vw Dim User Dse Dco Rpt)</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_COUNTRY_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_COUNTRY_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_COUNTRY_KEY</remote-alias>
            <ordinal>62</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CUSTOMER_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CUSTOMER_KEY (VW_DIM_ACCOUNT_DCO_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CUSTOMER_KEY (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CUSTOMER_KEY (VW_DIM_ACCOUNT_DCO_RPT)</remote-alias>
            <ordinal>45</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)</remote-alias>
            <ordinal>59</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CUSTOMER_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CUSTOMER_KEY (VW_DIM_DCO_MESSAGE_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CUSTOMER_KEY (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CUSTOMER_KEY (VW_DIM_DCO_MESSAGE_RPT)</remote-alias>
            <ordinal>72</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Dim Customer Key (Vw Dim Dco Message Rpt)</caption>
            <family>VW_DIM_DCO_MESSAGE_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO_RPT)</remote-alias>
            <ordinal>94</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CUSTOMER_KEY (VW_DIM_PRODUCT_DCO_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CUSTOMER_KEY (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CUSTOMER_KEY (VW_DIM_PRODUCT_DCO_RPT)</remote-alias>
            <ordinal>113</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CUSTOMER_KEY (VW_DIM_USER_DSE_DCO_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CUSTOMER_KEY (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CUSTOMER_KEY (VW_DIM_USER_DSE_DCO_RPT)</remote-alias>
            <ordinal>139</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Dim Customer Key (Vw Dim User Dse Dco Rpt)</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>16</ordinal>
            <layered>true</layered>
            <caption>DIM_CUSTOMER_KEY</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DIM_FACTOR_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_FACTOR_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_FACTOR_KEY</remote-alias>
            <ordinal>86</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_MESSAGE_CHANNEL_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_MESSAGE_CHANNEL_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_MESSAGE_CHANNEL_KEY</remote-alias>
            <ordinal>67</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Dim Message Channel Key</caption>
            <family>VW_DIM_DCO_MESSAGE_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)</remote-alias>
            <ordinal>66</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Dim Message Key (Vw Dim Dco Message Rpt)</caption>
            <family>VW_DIM_DCO_MESSAGE_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>23</ordinal>
            <layered>true</layered>
            <caption>DIM_MESSAGE_KEY</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>126</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Dim Product Key (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_PRODUCT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_PRODUCT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_PRODUCT_KEY</remote-alias>
            <ordinal>102</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>123</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Dim Segment Key (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>20</ordinal>
            <layered>true</layered>
            <caption>DIM_SEGMENT_KEY</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_SUGGESTION_DELIVERY_MODE_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_SUGGESTION_DELIVERY_MODE_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_SUGGESTION_DELIVERY_MODE_KEY</remote-alias>
            <ordinal>57</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_CHANNEL_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_USER_DSE_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_USER_DSE_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_USER_DSE_KEY</remote-alias>
            <ordinal>137</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Dim User Dse Key</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>14</ordinal>
            <layered>true</layered>
            <caption>DRIVER_TYPE</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DW_CREATED_TS (VW_DIM_ACCOUNT_DCO_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DW_CREATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DW_CREATED_TS (VW_DIM_ACCOUNT_DCO_RPT)</remote-alias>
            <ordinal>26</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DW_CREATED_TS (VW_DIM_FACTOR_DCO_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DW_CREATED_TS (VW_DIM_FACTOR_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DW_CREATED_TS (VW_DIM_FACTOR_DCO_RPT)</remote-alias>
            <ordinal>87</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>17</ordinal>
            <layered>true</layered>
            <caption>Dw Created Ts (Vw F Daily Suggestion Movement Rpt V2)</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DW_DELETED_FLAG (VW_DIM_ACCOUNT_DCO_RPT)</remote-name>
            <remote-type>11</remote-type>
            <local-name>[DW_DELETED_FLAG (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DW_DELETED_FLAG (VW_DIM_ACCOUNT_DCO_RPT)</remote-alias>
            <ordinal>37</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DW_DELETED_FLAG (VW_DIM_FACTOR_DCO_RPT)</remote-name>
            <remote-type>11</remote-type>
            <local-name>[DW_DELETED_FLAG (VW_DIM_FACTOR_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DW_DELETED_FLAG (VW_DIM_FACTOR_DCO_RPT)</remote-alias>
            <ordinal>92</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DW_DELETED_FLAG</remote-name>
            <remote-type>11</remote-type>
            <local-name>[DW_DELETED_FLAG]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DW_DELETED_FLAG</remote-alias>
            <ordinal>141</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Dw Deleted Flag</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DW_UPDATED_TS (VW_DIM_ACCOUNT_DCO_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DW_UPDATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DW_UPDATED_TS (VW_DIM_ACCOUNT_DCO_RPT)</remote-alias>
            <ordinal>27</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DW_UPDATED_TS</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DW_UPDATED_TS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DW_UPDATED_TS</remote-alias>
            <ordinal>88</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Dw Updated Ts</caption>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Date (copy)_986006877293236225</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Date (copy)_986006877293236225]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Date (copy)_986006877293236225</remote-alias>
            <ordinal>176</ordinal>
            <layered>true</layered>
            <caption>Relative Date</caption>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Driver/Type (copy)_544865224190218249</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Driver/Type (copy)_544865224190218249]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Driver/Type (copy)_544865224190218249</remote-alias>
            <ordinal>177</ordinal>
            <layered>true</layered>
            <caption>Driver/Type </caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FACTOR_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[FACTOR_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FACTOR_NAME</remote-alias>
            <ordinal>89</ordinal>
            <layered>true</layered>
            <caption>Factor</caption>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FACTOR_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[FACTOR_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FACTOR_TYPE</remote-alias>
            <ordinal>91</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)</remote-alias>
            <ordinal>90</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>9</ordinal>
            <layered>true</layered>
            <caption>FACTOR_UID</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>5</remote-type>
            <local-name>[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>18</ordinal>
            <layered>true</layered>
            <caption>FINAL_SCORE</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>FINAL_SCORE (copy)_540431991555641356</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[FINAL_SCORE (copy)_540431991555641356]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FINAL_SCORE (copy)_540431991555641356</remote-alias>
            <ordinal>182</ordinal>
            <layered>true</layered>
            <caption>Avg FINAL_SCORE</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;avg({FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],
[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:
sum([FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])
})&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Factor (copy)_544865224189366278</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Factor (copy)_544865224189366278]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Factor (copy)_544865224189366278</remote-alias>
            <ordinal>183</ordinal>
            <layered>true</layered>
            <caption>Factor </caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[FACTOR_NAME]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>21</ordinal>
            <layered>true</layered>
            <caption>HCP_SEGMENT</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_ACTIVATED_SRC</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_ACTIVATED_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_ACTIVATED_SRC</remote-alias>
            <ordinal>142</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Is Activated Src</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_ACTIVE_SRC</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_ACTIVE_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_ACTIVE_SRC</remote-alias>
            <ordinal>110</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_COMPETITOR</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_COMPETITOR]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_COMPETITOR</remote-alias>
            <ordinal>111</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_DELETED_SRC (VW_DIM_ACCOUNT_DCO_RPT)</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_DELETED_SRC (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_DELETED_SRC (VW_DIM_ACCOUNT_DCO_RPT)</remote-alias>
            <ordinal>41</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_DELETED_SRC (VW_DIM_USER_DSE_DCO_RPT)</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_DELETED_SRC (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_DELETED_SRC (VW_DIM_USER_DSE_DCO_RPT)</remote-alias>
            <ordinal>143</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Is Deleted Src (Vw Dim User Dse Dco Rpt)</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_DELETED_SRC</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_DELETED_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_DELETED_SRC</remote-alias>
            <ordinal>112</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_PERSON_ACCOUNT</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_PERSON_ACCOUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_PERSON_ACCOUNT</remote-alias>
            <ordinal>42</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752</remote-alias>
            <ordinal>184</ordinal>
            <layered>true</layered>
            <caption>Scenario group</caption>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Parameter 3]&#13;
WHEN &apos;(All)&apos; THEN 1&#13;
WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED (VW_DIM_SCENARIO_RPT)])&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_PUBLISHED (VW_DIM_SCENARIO_RPT)</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_PUBLISHED (VW_DIM_SCENARIO_RPT)</remote-alias>
            <ordinal>119</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>15</ordinal>
            <layered>true</layered>
            <caption>IS_PUBLISHED</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LAST_DCO_RUN_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[LAST_DCO_RUN_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
            <ordinal>121</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LAST_DCO_RUN_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[LAST_DCO_RUN_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_DCO_RUN_UID</remote-alias>
            <ordinal>120</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>MESSAGE_CHANNEL_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[MESSAGE_CHANNEL_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>MESSAGE_CHANNEL_ID</remote-alias>
            <ordinal>70</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Message Channel Id</caption>
            <family>VW_DIM_DCO_MESSAGE_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)</remote-alias>
            <ordinal>71</ordinal>
            <layered>true</layered>
            <caption>Message Channel</caption>
            <family>VW_DIM_DCO_MESSAGE_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>MESSAGE_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[MESSAGE_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>MESSAGE_ID</remote-alias>
            <ordinal>68</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Message Id</caption>
            <family>VW_DIM_DCO_MESSAGE_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)</remote-alias>
            <ordinal>69</ordinal>
            <layered>true</layered>
            <caption>Message</caption>
            <family>VW_DIM_DCO_MESSAGE_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PARENT_ACCOUNT_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PARENT_ACCOUNT_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PARENT_ACCOUNT_UID</remote-alias>
            <ordinal>31</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[PRODUCT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID</remote-alias>
            <ordinal>128</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Product Id</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME</remote-alias>
            <ordinal>105</ordinal>
            <layered>true</layered>
            <caption>Product</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME_ENGLISH</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME_ENGLISH]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME_ENGLISH</remote-alias>
            <ordinal>106</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_TYPE</remote-alias>
            <ordinal>107</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)</remote-alias>
            <ordinal>103</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>127</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Product Uid (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>200</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>7</ordinal>
            <layered>true</layered>
            <caption>PRODUCT_UID</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Product (copy)_544865224189894663</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Product (copy)_544865224189894663]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Product (copy)_544865224189894663</remote-alias>
            <ordinal>188</ordinal>
            <layered>true</layered>
            <caption>Product </caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[PRODUCT_NAME]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>11</remote-type>
            <local-name>[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>11</ordinal>
            <layered>true</layered>
            <caption>RECOMMENDED</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECORD_END_DATE (VW_DIM_ACCOUNT_DCO_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[RECORD_END_DATE (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECORD_END_DATE (VW_DIM_ACCOUNT_DCO_RPT)</remote-alias>
            <ordinal>39</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECORD_END_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[RECORD_END_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECORD_END_DATE</remote-alias>
            <ordinal>109</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECORD_START_DATE (VW_DIM_ACCOUNT_DCO_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[RECORD_START_DATE (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECORD_START_DATE (VW_DIM_ACCOUNT_DCO_RPT)</remote-alias>
            <ordinal>38</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECORD_START_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[RECORD_START_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECORD_START_DATE</remote-alias>
            <ordinal>108</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>REP_CNT (new version) (copy)_497647790488461313</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[REP_CNT (new version) (copy)_497647790488461313]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>REP_CNT (new version) (copy)_497647790488461313</remote-alias>
            <ordinal>191</ordinal>
            <layered>true</layered>
            <caption>ACCOUNT_CNT</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]: countd([ACCOUNT_NAME])}&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>6</ordinal>
            <layered>true</layered>
            <caption>REP_TEAM_UID</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RPT_FACTOR_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[RPT_FACTOR_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RPT_FACTOR_UID</remote-alias>
            <ordinal>85</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Rpt Factor Uid</caption>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RUN_CONFIG_FACTOR_ID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[RUN_CONFIG_FACTOR_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RUN_CONFIG_FACTOR_ID</remote-alias>
            <ordinal>98</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SALES_REFERENCE_ACCOUNT_ID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SALES_REFERENCE_ACCOUNT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SALES_REFERENCE_ACCOUNT_ID</remote-alias>
            <ordinal>30</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_DESCRIPTION</remote-alias>
            <ordinal>118</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>255</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_NAME</remote-alias>
            <ordinal>117</ordinal>
            <layered>true</layered>
            <caption>Scenario</caption>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID (VW_DIM_SCENARIO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID (VW_DIM_SCENARIO_RPT)</remote-alias>
            <ordinal>116</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <caption>SCENARIO_UID</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_NAME</remote-alias>
            <ordinal>124</ordinal>
            <layered>true</layered>
            <caption>Segment</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>200</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_TYPE</remote-alias>
            <ordinal>125</ordinal>
            <layered>true</layered>
            <caption>Segment Type</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)</remote-alias>
            <ordinal>81</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Se Config Id (Vw Dim Dse Config Rpt)</caption>
            <family>VW_DIM_DSE_CONFIG_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_ID (VW_DIM_USER_DSE_DCO_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SE_CONFIG_ID (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_ID (VW_DIM_USER_DSE_DCO_RPT)</remote-alias>
            <ordinal>136</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Se Config Id (Vw Dim User Dse Dco Rpt)</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>22</ordinal>
            <layered>true</layered>
            <caption>SE_CONFIG_ID</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)</remote-alias>
            <ordinal>82</ordinal>
            <layered>true</layered>
            <caption>Config </caption>
            <family>VW_DIM_DSE_CONFIG_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SOURCE_SYSTEM_NAME (VW_DIM_ACCOUNT_DCO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SOURCE_SYSTEM_NAME (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SOURCE_SYSTEM_NAME (VW_DIM_ACCOUNT_DCO_RPT)</remote-alias>
            <ordinal>40</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SOURCE_SYSTEM_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SOURCE_SYSTEM_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SOURCE_SYSTEM_NAME</remote-alias>
            <ordinal>93</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Source System Name</caption>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SPECIALITY_1</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SPECIALITY_1]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SPECIALITY_1</remote-alias>
            <ordinal>35</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SPECIALITY_2</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SPECIALITY_2]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SPECIALITY_2</remote-alias>
            <ordinal>36</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <family>VW_DIM_ACCOUNT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>19</ordinal>
            <layered>true</layered>
            <caption>Suggested Date</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>date</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>13</ordinal>
            <layered>true</layered>
            <caption>Suggestion Reference Id</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Suggested Date (copy)_986006877287538688</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Suggested Date (copy)_986006877287538688]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Suggested Date (copy)_986006877287538688</remote-alias>
            <ordinal>200</ordinal>
            <layered>true</layered>
            <caption>Suggested Month</caption>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;DATE(DATETRUNC(&apos;month&apos;,date([SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])))&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UPDATEDAT_SRC</remote-name>
            <remote-type>7</remote-type>
            <local-name>[UPDATEDAT_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>UPDATEDAT_SRC</remote-alias>
            <ordinal>100</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>UPDATED_DT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[UPDATED_DT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>UPDATED_DT_KEY</remote-alias>
            <ordinal>96</ordinal>
            <layered>true</layered>
            <family>VW_DIM_FACTOR_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UPDATED_TS</remote-name>
            <remote-type>7</remote-type>
            <local-name>[UPDATED_TS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>UPDATED_TS</remote-alias>
            <ordinal>131</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>Updated Ts</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)</remote-alias>
            <ordinal>140</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>User Dse Uid (Vw Dim User Dse Dco Rpt)</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <caption>USER_DSE_UID</caption>
            <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USER_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[USER_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USER_ID</remote-alias>
            <ordinal>133</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>User Id</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USER_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USER_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USER_NAME</remote-alias>
            <ordinal>135</ordinal>
            <layered>true</layered>
            <caption>Actor</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>USER_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[USER_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>USER_UID</remote-alias>
            <ordinal>134</ordinal>
            <hidden>true</hidden>
            <layered>true</layered>
            <caption>User Uid</caption>
            <family>VW_DIM_USER_DSE_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_64BIT_CALCULATIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model' inline='true' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracingDataModel?rev=1.0' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracingDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.0' site='${TABLEAU_SITE}' />
  <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracingDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
    </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
    <cols>
      <map key='[ACCOUNT_CNT (new version) (copy)_497647790488961026]' value='[sqlproxy].[ACCOUNT_CNT (new version) (copy)_497647790488961026]' />
      <map key='[ACCOUNT_ID]' value='[sqlproxy].[ACCOUNT_ID]' />
      <map key='[ACCOUNT_NAME]' value='[sqlproxy].[ACCOUNT_NAME]' />
      <map key='[ACCOUNT_TYPE]' value='[sqlproxy].[ACCOUNT_TYPE]' />
      <map key='[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]' value='[sqlproxy].[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]' />
      <map key='[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[AFFILIATED_HCP_UID]' value='[sqlproxy].[AFFILIATED_HCP_UID]' />
      <map key='[CANDIDATE_CNT (new version) (copy)_497647790489550852]' value='[sqlproxy].[CANDIDATE_CNT (new version) (copy)_497647790489550852]' />
      <map key='[CHANNEL_CODE]' value='[sqlproxy].[CHANNEL_CODE]' />
      <map key='[CHANNEL_LOCALIZABLE_NAME]' value='[sqlproxy].[CHANNEL_LOCALIZABLE_NAME]' />
      <map key='[CHANNEL_LOCAL_NAME]' value='[sqlproxy].[CHANNEL_LOCAL_NAME]' />
      <map key='[CHANNEL_NAME]' value='[sqlproxy].[CHANNEL_NAME]' />
      <map key='[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]' value='[sqlproxy].[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]' />
      <map key='[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[COUNTRY_CODE]' value='[sqlproxy].[COUNTRY_CODE]' />
      <map key='[COUNTRY_NAME]' value='[sqlproxy].[COUNTRY_NAME]' />
      <map key='[CREATEDAT_SRC]' value='[sqlproxy].[CREATEDAT_SRC]' />
      <map key='[CREATED_BY]' value='[sqlproxy].[CREATED_BY]' />
      <map key='[CREATED_DT_KEY]' value='[sqlproxy].[CREATED_DT_KEY]' />
      <map key='[CREATED_TS]' value='[sqlproxy].[CREATED_TS]' />
      <map key='[CUSTOMER_DESC]' value='[sqlproxy].[CUSTOMER_DESC]' />
      <map key='[CUSTOMER_ID]' value='[sqlproxy].[CUSTOMER_ID]' />
      <map key='[CUSTOMER_NAME]' value='[sqlproxy].[CUSTOMER_NAME]' />
      <map key='[Calculation_1716434419576819713]' value='[sqlproxy].[Calculation_1716434419576819713]' />
      <map key='[Calculation_497647************]' value='[sqlproxy].[Calculation_497647************]' />
      <map key='[Calculation_5911010483556352]' value='[sqlproxy].[Calculation_5911010483556352]' />
      <map key='[Channel (copy)_544865224190140424]' value='[sqlproxy].[Channel (copy)_544865224190140424]' />
      <map key='[DAY_CNT (new version) (copy)_497647790489280515]' value='[sqlproxy].[DAY_CNT (new version) (copy)_497647790489280515]' />
      <map key='[DCO_REASON_CODE]' value='[sqlproxy].[DCO_REASON_CODE]' />
      <map key='[DCO_REASON_DESCRIPTION]' value='[sqlproxy].[DCO_REASON_DESCRIPTION]' />
      <map key='[DCO_REASON_NAME]' value='[sqlproxy].[DCO_REASON_NAME]' />
      <map key='[DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[DCO_REASON_TYPE_CODE]' value='[sqlproxy].[DCO_REASON_TYPE_CODE]' />
      <map key='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' value='[sqlproxy].[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' />
      <map key='[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433]' value='[sqlproxy].[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433]' />
      <map key='[DCO_REASON_TYPE_NAME]' value='[sqlproxy].[DCO_REASON_TYPE_NAME]' />
      <map key='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' value='[sqlproxy].[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
      <map key='[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[DESCRIPTION]' value='[sqlproxy].[DESCRIPTION]' />
      <map key='[DIM_ACCOUNT_KEY]' value='[sqlproxy].[DIM_ACCOUNT_KEY]' />
      <map key='[DIM_ACCOUNT_TYPE_KEY]' value='[sqlproxy].[DIM_ACCOUNT_TYPE_KEY]' />
      <map key='[DIM_ACTION_GROUP_KEY]' value='[sqlproxy].[DIM_ACTION_GROUP_KEY]' />
      <map key='[DIM_ACTIVITY_DELIVERY_MODE_KEY]' value='[sqlproxy].[DIM_ACTIVITY_DELIVERY_MODE_KEY]' />
      <map key='[DIM_BRAND_KEY]' value='[sqlproxy].[DIM_BRAND_KEY]' />
      <map key='[DIM_CHANNEL_CATEGORY_KEY]' value='[sqlproxy].[DIM_CHANNEL_CATEGORY_KEY]' />
      <map key='[DIM_CHANNEL_KEY]' value='[sqlproxy].[DIM_CHANNEL_KEY]' />
      <map key='[DIM_CHANNEL_TYPE_KEY]' value='[sqlproxy].[DIM_CHANNEL_TYPE_KEY]' />
      <map key='[DIM_COUNTRY_KEY (VW_DIM_ACCOUNT_DCO_RPT)]' value='[sqlproxy].[DIM_COUNTRY_KEY (VW_DIM_ACCOUNT_DCO_RPT)]' />
      <map key='[DIM_COUNTRY_KEY (VW_DIM_PRODUCT_DCO_RPT)]' value='[sqlproxy].[DIM_COUNTRY_KEY (VW_DIM_PRODUCT_DCO_RPT)]' />
      <map key='[DIM_COUNTRY_KEY (VW_DIM_USER_DSE_DCO_RPT)]' value='[sqlproxy].[DIM_COUNTRY_KEY (VW_DIM_USER_DSE_DCO_RPT)]' />
      <map key='[DIM_COUNTRY_KEY]' value='[sqlproxy].[DIM_COUNTRY_KEY]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_ACCOUNT_DCO_RPT)]' value='[sqlproxy].[DIM_CUSTOMER_KEY (VW_DIM_ACCOUNT_DCO_RPT)]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]' value='[sqlproxy].[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_DCO_MESSAGE_RPT)]' value='[sqlproxy].[DIM_CUSTOMER_KEY (VW_DIM_DCO_MESSAGE_RPT)]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO_RPT)]' value='[sqlproxy].[DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO_RPT)]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_PRODUCT_DCO_RPT)]' value='[sqlproxy].[DIM_CUSTOMER_KEY (VW_DIM_PRODUCT_DCO_RPT)]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_USER_DSE_DCO_RPT)]' value='[sqlproxy].[DIM_CUSTOMER_KEY (VW_DIM_USER_DSE_DCO_RPT)]' />
      <map key='[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[DIM_FACTOR_KEY]' value='[sqlproxy].[DIM_FACTOR_KEY]' />
      <map key='[DIM_MESSAGE_CHANNEL_KEY]' value='[sqlproxy].[DIM_MESSAGE_CHANNEL_KEY]' />
      <map key='[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]' value='[sqlproxy].[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]' />
      <map key='[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' />
      <map key='[DIM_PRODUCT_KEY]' value='[sqlproxy].[DIM_PRODUCT_KEY]' />
      <map key='[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]' />
      <map key='[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[DIM_SUGGESTION_DELIVERY_MODE_KEY]' value='[sqlproxy].[DIM_SUGGESTION_DELIVERY_MODE_KEY]' />
      <map key='[DIM_USER_DSE_KEY]' value='[sqlproxy].[DIM_USER_DSE_KEY]' />
      <map key='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[DW_CREATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]' value='[sqlproxy].[DW_CREATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]' />
      <map key='[DW_CREATED_TS (VW_DIM_FACTOR_DCO_RPT)]' value='[sqlproxy].[DW_CREATED_TS (VW_DIM_FACTOR_DCO_RPT)]' />
      <map key='[DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[DW_DELETED_FLAG (VW_DIM_ACCOUNT_DCO_RPT)]' value='[sqlproxy].[DW_DELETED_FLAG (VW_DIM_ACCOUNT_DCO_RPT)]' />
      <map key='[DW_DELETED_FLAG (VW_DIM_FACTOR_DCO_RPT)]' value='[sqlproxy].[DW_DELETED_FLAG (VW_DIM_FACTOR_DCO_RPT)]' />
      <map key='[DW_DELETED_FLAG]' value='[sqlproxy].[DW_DELETED_FLAG]' />
      <map key='[DW_UPDATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]' value='[sqlproxy].[DW_UPDATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]' />
      <map key='[DW_UPDATED_TS]' value='[sqlproxy].[DW_UPDATED_TS]' />
      <map key='[Date (copy)_986006877293236225]' value='[sqlproxy].[Date (copy)_986006877293236225]' />
      <map key='[Driver/Type (copy)_544865224190218249]' value='[sqlproxy].[Driver/Type (copy)_544865224190218249]' />
      <map key='[FACTOR_NAME]' value='[sqlproxy].[FACTOR_NAME]' />
      <map key='[FACTOR_TYPE]' value='[sqlproxy].[FACTOR_TYPE]' />
      <map key='[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]' value='[sqlproxy].[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]' />
      <map key='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[FINAL_SCORE (copy)_540431991555641356]' value='[sqlproxy].[FINAL_SCORE (copy)_540431991555641356]' />
      <map key='[Factor (copy)_544865224189366278]' value='[sqlproxy].[Factor (copy)_544865224189366278]' />
      <map key='[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[IS_ACTIVATED_SRC]' value='[sqlproxy].[IS_ACTIVATED_SRC]' />
      <map key='[IS_ACTIVE_SRC]' value='[sqlproxy].[IS_ACTIVE_SRC]' />
      <map key='[IS_COMPETITOR]' value='[sqlproxy].[IS_COMPETITOR]' />
      <map key='[IS_DELETED_SRC (VW_DIM_ACCOUNT_DCO_RPT)]' value='[sqlproxy].[IS_DELETED_SRC (VW_DIM_ACCOUNT_DCO_RPT)]' />
      <map key='[IS_DELETED_SRC (VW_DIM_USER_DSE_DCO_RPT)]' value='[sqlproxy].[IS_DELETED_SRC (VW_DIM_USER_DSE_DCO_RPT)]' />
      <map key='[IS_DELETED_SRC]' value='[sqlproxy].[IS_DELETED_SRC]' />
      <map key='[IS_PERSON_ACCOUNT]' value='[sqlproxy].[IS_PERSON_ACCOUNT]' />
      <map key='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' value='[sqlproxy].[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' />
      <map key='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]' value='[sqlproxy].[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]' />
      <map key='[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[LAST_DCO_RUN_DATE]' value='[sqlproxy].[LAST_DCO_RUN_DATE]' />
      <map key='[LAST_DCO_RUN_UID]' value='[sqlproxy].[LAST_DCO_RUN_UID]' />
      <map key='[MESSAGE_CHANNEL_ID]' value='[sqlproxy].[MESSAGE_CHANNEL_ID]' />
      <map key='[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]' value='[sqlproxy].[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]' />
      <map key='[MESSAGE_ID]' value='[sqlproxy].[MESSAGE_ID]' />
      <map key='[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]' value='[sqlproxy].[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]' />
      <map key='[PARENT_ACCOUNT_UID]' value='[sqlproxy].[PARENT_ACCOUNT_UID]' />
      <map key='[PRODUCT_ID]' value='[sqlproxy].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME]' value='[sqlproxy].[PRODUCT_NAME]' />
      <map key='[PRODUCT_NAME_ENGLISH]' value='[sqlproxy].[PRODUCT_NAME_ENGLISH]' />
      <map key='[PRODUCT_TYPE]' value='[sqlproxy].[PRODUCT_TYPE]' />
      <map key='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' value='[sqlproxy].[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
      <map key='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' />
      <map key='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[Product (copy)_544865224189894663]' value='[sqlproxy].[Product (copy)_544865224189894663]' />
      <map key='[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[RECORD_END_DATE (VW_DIM_ACCOUNT_DCO_RPT)]' value='[sqlproxy].[RECORD_END_DATE (VW_DIM_ACCOUNT_DCO_RPT)]' />
      <map key='[RECORD_END_DATE]' value='[sqlproxy].[RECORD_END_DATE]' />
      <map key='[RECORD_START_DATE (VW_DIM_ACCOUNT_DCO_RPT)]' value='[sqlproxy].[RECORD_START_DATE (VW_DIM_ACCOUNT_DCO_RPT)]' />
      <map key='[RECORD_START_DATE]' value='[sqlproxy].[RECORD_START_DATE]' />
      <map key='[REP_CNT (new version) (copy)_497647790488461313]' value='[sqlproxy].[REP_CNT (new version) (copy)_497647790488461313]' />
      <map key='[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[RPT_FACTOR_UID]' value='[sqlproxy].[RPT_FACTOR_UID]' />
      <map key='[RUN_CONFIG_FACTOR_ID]' value='[sqlproxy].[RUN_CONFIG_FACTOR_ID]' />
      <map key='[SALES_REFERENCE_ACCOUNT_ID]' value='[sqlproxy].[SALES_REFERENCE_ACCOUNT_ID]' />
      <map key='[SCENARIO_DESCRIPTION]' value='[sqlproxy].[SCENARIO_DESCRIPTION]' />
      <map key='[SCENARIO_NAME]' value='[sqlproxy].[SCENARIO_NAME]' />
      <map key='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' value='[sqlproxy].[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
      <map key='[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[SEGMENT_NAME]' value='[sqlproxy].[SEGMENT_NAME]' />
      <map key='[SEGMENT_TYPE]' value='[sqlproxy].[SEGMENT_TYPE]' />
      <map key='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]' value='[sqlproxy].[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]' />
      <map key='[SE_CONFIG_ID (VW_DIM_USER_DSE_DCO_RPT)]' value='[sqlproxy].[SE_CONFIG_ID (VW_DIM_USER_DSE_DCO_RPT)]' />
      <map key='[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' value='[sqlproxy].[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' />
      <map key='[SOURCE_SYSTEM_NAME (VW_DIM_ACCOUNT_DCO_RPT)]' value='[sqlproxy].[SOURCE_SYSTEM_NAME (VW_DIM_ACCOUNT_DCO_RPT)]' />
      <map key='[SOURCE_SYSTEM_NAME]' value='[sqlproxy].[SOURCE_SYSTEM_NAME]' />
      <map key='[SPECIALITY_1]' value='[sqlproxy].[SPECIALITY_1]' />
      <map key='[SPECIALITY_2]' value='[sqlproxy].[SPECIALITY_2]' />
      <map key='[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[Suggested Date (copy)_986006877287538688]' value='[sqlproxy].[Suggested Date (copy)_986006877287538688]' />
      <map key='[UPDATEDAT_SRC]' value='[sqlproxy].[UPDATEDAT_SRC]' />
      <map key='[UPDATED_DT_KEY]' value='[sqlproxy].[UPDATED_DT_KEY]' />
      <map key='[UPDATED_TS]' value='[sqlproxy].[UPDATED_TS]' />
      <map key='[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]' value='[sqlproxy].[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]' />
      <map key='[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[sqlproxy].[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      <map key='[USER_ID]' value='[sqlproxy].[USER_ID]' />
      <map key='[USER_NAME]' value='[sqlproxy].[USER_NAME]' />
      <map key='[USER_UID]' value='[sqlproxy].[USER_UID]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='Count' datatype='string' default-type='nominal' name='[:Measure Names]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <aliases>
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model]&quot;' value='Expected Value' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model].[avg:ACCOUNT_CNT (new version) (copy)_497647790488961026:qk]&quot;' value='Avg. Suggested Dates' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model].[avg:Calculation_497647************:qk]&quot;' value='Avg. Actors' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model].[avg:REP_CNT (new version) (copy)_497647790488461313:qk]&quot;' value='Avg. Accounts' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model].[usr:CANDIDATE_CNT (new version) (copy)_497647790489550852:qk]&quot;' value='Recommended Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model].[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]&quot;' value='Candidates Count' />
    </aliases>
  </column>
  <column aggregation='Sum' caption='DAY_CNT' datatype='integer' default-format='n\#,\#\#0;-\#,\#\#0' default-type='quantitative' name='[ACCOUNT_CNT (new version) (copy)_497647790488961026]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='{FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], &\#13;&\#10;[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:&\#13;&\#10;countd([SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&\#13;&\#10;}' />
  </column>
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[ACCOUNT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Account' datatype='string' default-type='nominal' name='[ACCOUNT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[ACCOUNT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='ACCOUNT_UID' datatype='string' default-type='nominal' name='[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[AFFILIATED_HCP_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='User' caption='RECOMMENDED_COUNT' datatype='integer' default-format='n\#,\#\#0;-\#,\#\#0' default-type='quantitative' name='[CANDIDATE_CNT (new version) (copy)_497647790489550852]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='count(iif([RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],null))' />
  </column>
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[CHANNEL_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[CHANNEL_LOCALIZABLE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[CHANNEL_LOCAL_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' name='[CHANNEL_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='CHANNEL_UID' datatype='string' default-type='nominal' name='[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[COUNTRY_CODE]' pivot='key' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' name='[COUNTRY_NAME]' pivot='key' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Createdat Src (Vw Dim Factor Dco)' datatype='datetime' default-type='ordinal' hidden='true' name='[CREATEDAT_SRC (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Count' caption='Created By' datatype='string' default-type='nominal' hidden='true' name='[CREATED_BY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Created Dt Key (Vw Dim Factor Dco)' datatype='integer' default-type='ordinal' hidden='true' name='[CREATED_DT_KEY (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Year' caption='Created Ts' datatype='datetime' default-type='ordinal' hidden='true' name='[CREATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[CUSTOMER_DESC]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Customer Id (Vw Dim Factor Dco)' datatype='integer' default-type='ordinal' hidden='true' name='[CUSTOMER_ID (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[CUSTOMER_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Reasons' datatype='string' default-type='nominal' name='[Calculation_1716434419576819713]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[DCO_REASON_CODE] +&apos;. &apos;+[DCO_REASON_NAME]' />
  </column>
  <column aggregation='Count' caption='Last Run (true)' datatype='boolean' default-type='nominal' hidden='true' name='[Calculation_465630037247307799]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default'>
    <calculation class='tableau' formula='IF [LATEST_RUN] then &apos;True&apos; end = [Parameters].[Parameter 1] &\#13;&\#10;OR [Parameters].[Parameter 1] = &apos;All&apos;' />
  </column>
  <column aggregation='Sum' caption='REP_CNT' datatype='integer' default-type='quantitative' name='[Calculation_497647************]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]:countd([USER_NAME])}' />
  </column>
  <column aggregation='Count' caption='New Scenario' datatype='string' default-type='nominal' name='[Calculation_5911010483556352]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[SCENARIO_NAME]+ IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;, &apos;&apos;)' />
  </column>
  <column aggregation='User' caption='Index' datatype='integer' default-type='ordinal' name='[Calculation_942378240705122304]' pivot='key' role='measure' type='ordinal' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='index()'>
      <table-calc ordering-type='Rows' />
    </calculation>
  </column>
  <column aggregation='Count' caption='Channel ' datatype='string' default-type='nominal' name='[Channel (copy)_544865224190140424]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[CHANNEL_NAME]' />
  </column>
  <column aggregation='User' caption='CANDIDATE_CNT' datatype='integer' default-format='n\#,\#\#0;-\#,\#\#0' default-type='quantitative' name='[DAY_CNT (new version) (copy)_497647790489280515]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='count([PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])' />
  </column>
  <column aggregation='Count' caption='Reason' datatype='string' default-type='nominal' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Reasons_' datatype='string' default-type='nominal' name='[DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[DCO_REASON_TEXT]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[DCO_REASON_TYPE_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='REASON' datatype='string' default-type='nominal' name='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433] +&apos; &apos;+[DCO_REASON_NAME]' />
  </column>
  <column aggregation='Count' caption='DCO_REASON_TYPE_NAME (new)' datatype='string' default-type='nominal' name='[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [DCO_REASON_TYPE_NAME] =&apos;Accept&apos; and [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]then &apos;Published&apos; else [DCO_REASON_TYPE_NAME]  end' />
  </column>
  <column aggregation='Count' caption='Status' datatype='string' default-type='nominal' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='DCO_REASON_UID' datatype='string' default-type='nominal' name='[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='DCO_RUN_DATE' datatype='date' default-type='ordinal' name='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' caption='DCO_RUN_UID' datatype='string' default-type='nominal' name='[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Description' datatype='string' default-type='nominal' hidden='true' name='[DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_ACCOUNT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_ACCOUNT_TYPE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_ACTION_GROUP_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_ACTIVITY_DELIVERY_MODE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_BRAND_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CHANNEL_CATEGORY_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CHANNEL_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CHANNEL_TYPE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_COUNTRY_KEY (VW_DIM_ACCOUNT_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_COUNTRY_KEY (VW_DIM_PRODUCT_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_COUNTRY_KEY (VW_DIM_REP_TEAM_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_COUNTRY_KEY (VW_DIM_RPT_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Country Key (Vw Dim User Dse Dco Rpt)' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_COUNTRY_KEY (VW_DIM_USER_DSE_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_COUNTRY_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_ACCOUNT_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Customer Key (Vw Dim Dco Message Rpt)' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_DCO_MESSAGE_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Customer Key (Vw Dim Factor Dco)' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_PRODUCT_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_REP_TEAM_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_RPT_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Customer Key (Vw Dim User Dse Dco Rpt)' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_USER_DSE_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='DIM_CUSTOMER_KEY' datatype='integer' default-type='ordinal' name='[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_DSE_CONFIG_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Factor Key (Vw Dim Factor Dco)' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_FACTOR_KEY (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Message Channel Key' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_MESSAGE_CHANNEL_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Message Key (Vw Dim Dco Message Rpt)' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='DIM_MESSAGE_KEY' datatype='integer' default-type='ordinal' name='[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Product Key (Vw Dim Segment Rpt)' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_PRODUCT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_REP_TEAM_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_RPT_FACTOR_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Segment Key (Vw Dim Segment Rpt)' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='DIM_SEGMENT_KEY' datatype='integer' default-type='ordinal' name='[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_SUGGESTION_DELIVERY_MODE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim User Dse Key' datatype='integer' default-type='ordinal' hidden='true' name='[DIM_USER_DSE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='DRIVER_TYPE' datatype='string' default-type='nominal' name='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' datatype='datetime' default-type='ordinal' hidden='true' name='[DW_CREATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Year' caption='Dw Created Ts (Vw Dim Factor Dco)' datatype='datetime' default-type='ordinal' hidden='true' name='[DW_CREATED_TS (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Year' datatype='datetime' default-type='ordinal' hidden='true' name='[DW_CREATED_TS (VW_DIM_REP_TEAM_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Year' datatype='datetime' default-type='ordinal' hidden='true' name='[DW_CREATED_TS (VW_DIM_RPT_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Year' caption='Dw Created Ts (Vw F Daily Suggestion Movement Rpt V2)' datatype='datetime' default-type='ordinal' name='[DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Year' datatype='datetime' default-type='ordinal' hidden='true' name='[DW_CREATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Count' datatype='boolean' default-type='nominal' hidden='true' name='[DW_DELETED_FLAG (VW_DIM_ACCOUNT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Dw Deleted Flag (Vw Dim Factor Dco)' datatype='boolean' default-type='nominal' hidden='true' name='[DW_DELETED_FLAG (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' datatype='boolean' default-type='nominal' hidden='true' name='[DW_DELETED_FLAG (VW_DIM_RPT_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Dw Deleted Flag' datatype='boolean' default-type='nominal' hidden='true' name='[DW_DELETED_FLAG]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Year' datatype='datetime' default-type='ordinal' hidden='true' name='[DW_UPDATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Year' datatype='datetime' default-type='ordinal' hidden='true' name='[DW_UPDATED_TS (VW_DIM_RPT_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Year' caption='Dw Updated Ts' datatype='datetime' default-type='ordinal' hidden='true' name='[DW_UPDATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='User' caption='Date (-10 days)' datatype='boolean' default-type='nominal' hidden='true' name='[Date (copy)_476748244939202591]' pivot='key' role='measure' type='nominal' user-datatype='boolean' visual-totals='Default'>
    <calculation class='tableau' formula='ATTR([DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]) &gt;= DATEADD(&apos;day&apos;, -10,WINDOW_MAX(MAX([DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])))'>
      <table-calc ordering-type='Rows' />
    </calculation>
  </column>
  <column aggregation='Year' caption='Relative Date' datatype='date' default-format='S' default-type='ordinal' name='[Date (copy)_986006877293236225]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
    <calculation class='tableau' formula='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
  </column>
  <column aggregation='Count' caption='Driver/Type ' datatype='string' default-type='nominal' name='[Driver/Type (copy)_544865224190218249]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
  </column>
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[EXTERNAL_ID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Factor' datatype='string' default-type='nominal' name='[FACTOR_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Factor Type (Vw Dim Factor Dco)' datatype='string' default-type='nominal' hidden='true' name='[FACTOR_TYPE (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Factor Uid (Vw Dim Factor Dco)' datatype='string' default-type='nominal' hidden='true' name='[FACTOR_UID (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='FACTOR_UID' datatype='string' default-type='nominal' name='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' hidden='true' name='[FACTOR_UNIQ_CNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' caption='FINAL_SCORE' datatype='real' default-type='quantitative' name='[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
  <column aggregation='User' caption='Avg FINAL_SCORE' datatype='real' default-type='quantitative' name='[FINAL_SCORE (copy)_540431991555641356]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='avg({FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],&\#10;[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:&\#10;sum([FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&\#10;})' />
  </column>
  <column aggregation='Count' caption='Factor ' datatype='string' default-type='nominal' name='[Factor (copy)_544865224189366278]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[FACTOR_NAME]' />
  </column>
  <column aggregation='Count' caption='HCP_SEGMENT' datatype='string' default-type='nominal' name='[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Is Activated Src' datatype='boolean' default-type='nominal' hidden='true' name='[IS_ACTIVATED_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' datatype='boolean' default-type='nominal' hidden='true' name='[IS_ACTIVE_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' datatype='boolean' default-type='nominal' hidden='true' name='[IS_COMPETITOR]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' datatype='boolean' default-type='nominal' hidden='true' name='[IS_DELETED_SRC (VW_DIM_ACCOUNT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Is Deleted Src (Vw Dim User Dse Dco Rpt)' datatype='boolean' default-type='nominal' hidden='true' name='[IS_DELETED_SRC (VW_DIM_USER_DSE_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' datatype='boolean' default-type='nominal' hidden='true' name='[IS_DELETED_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' datatype='boolean' default-type='nominal' hidden='true' name='[IS_PERSON_ACCOUNT]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario group' datatype='integer' default-type='ordinal' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 3]&\#13;&\#10;WHEN &apos;(All)&apos; THEN 1&\#13;&\#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED (VW_DIM_SCENARIO_RPT)])&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='IS_PUBLISHED' datatype='boolean' default-type='nominal' name='[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' datatype='boolean' default-type='nominal' hidden='true' name='[IS_TOTAL_RECORD]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[LAST_DCO_RUN_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Latest Run' datatype='boolean' default-type='nominal' hidden='true' name='[LATEST_RUN]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Message Channel Id' datatype='integer' default-type='ordinal' hidden='true' name='[MESSAGE_CHANNEL_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Message Channel' datatype='string' default-type='nominal' name='[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Message Id' datatype='integer' default-type='ordinal' hidden='true' name='[MESSAGE_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Message' datatype='string' default-type='nominal' name='[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[ORDER_NUM]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[PARENT_ACCOUNT_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Id' datatype='integer' default-type='ordinal' hidden='true' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Product' datatype='string' default-type='nominal' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[PRODUCT_NAME_ENGLISH]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[PRODUCT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Uid (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' hidden='true' name='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='PRODUCT_UID' datatype='string' default-type='nominal' name='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product ' datatype='string' default-type='nominal' name='[Product (copy)_544865224189894663]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[PRODUCT_NAME]' />
  </column>
  <column aggregation='Count' caption='RECOMMENDED' datatype='boolean' default-type='nominal' name='[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Year' datatype='date' default-type='ordinal' hidden='true' name='[RECORD_END_DATE (VW_DIM_ACCOUNT_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' datatype='date' default-type='ordinal' hidden='true' name='[RECORD_END_DATE (VW_DIM_RPT_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' datatype='date' default-type='ordinal' hidden='true' name='[RECORD_END_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' datatype='date' default-type='ordinal' hidden='true' name='[RECORD_START_DATE (VW_DIM_ACCOUNT_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' datatype='date' default-type='ordinal' hidden='true' name='[RECORD_START_DATE (VW_DIM_RPT_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' datatype='date' default-type='ordinal' hidden='true' name='[RECORD_START_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Sum' caption='ACCOUNT_CNT' datatype='integer' default-format='n\#,\#\#0;-\#,\#\#0' default-type='quantitative' name='[REP_CNT (new version) (copy)_497647790488461313]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]: countd([ACCOUNT_NAME])}' />
  </column>
  <column aggregation='Count' datatype='integer' default-type='ordinal' hidden='true' name='[REP_TEAM_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[REP_TEAM_UID (VW_DIM_REP_TEAM_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='REP_TEAM_UID' datatype='string' default-type='nominal' name='[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[RPT_FACTOR_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Rpt Factor Uid (Vw Dim Factor Dco)' datatype='string' default-type='nominal' hidden='true' name='[RPT_FACTOR_UID (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[RPT_FACTOR_UID (VW_DIM_RPT_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Rpt Factor Uid' datatype='string' default-type='nominal' hidden='true' name='[RPT_FACTOR_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Run Config Factor Id (Vw Dim Factor Dco)' datatype='string' default-type='nominal' hidden='true' name='[RUN_CONFIG_FACTOR_ID (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[SALES_REFERENCE_ACCOUNT_ID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[SCENARIO_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='SCENARIO_UID' datatype='string' default-type='nominal' name='[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario ver' datatype='string' default-type='nominal' hidden='true' name='[SCENARIO_VER]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Se Config Id (Vw Dim Dse Config Rpt)' datatype='integer' default-type='ordinal' hidden='true' name='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Se Config Id (Vw Dim User Dse Dco Rpt)' datatype='integer' default-type='ordinal' hidden='true' name='[SE_CONFIG_ID (VW_DIM_USER_DSE_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='SE_CONFIG_ID' datatype='integer' default-type='ordinal' name='[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Config ' datatype='string' default-type='nominal' name='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[SOURCE_SYSTEM_NAME (VW_DIM_ACCOUNT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Source System Name' datatype='string' default-type='nominal' hidden='true' name='[SOURCE_SYSTEM_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[SPECIALITY_1]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[SPECIALITY_2]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Suggested Date' datatype='date' datatype-customized='true' default-type='ordinal' name='[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' caption='Suggestion Reference Id' datatype='string' default-type='nominal' name='[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' hidden='true' name='[SUGG_UNIQ_CNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Year' caption='Suggested Month' datatype='date' datatype-customized='true' default-type='ordinal' name='[Suggested Date (copy)_986006877287538688]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
    <calculation class='tableau' formula='DATE(DATETRUNC(&apos;month&apos;,date([SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])))' />
  </column>
  <column aggregation='Count' caption='Accepted candidates' datatype='string' default-role='measure' default-type='quantitative' hidden='true' name='[Suggestion Id (copy)_721138898419408896]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='if [RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)] then [SUGGESTION_REFERENCE_ID] end' />
  </column>
  <column aggregation='Year' caption='Updatedat Src (Vw Dim Factor Dco)' datatype='datetime' default-type='ordinal' hidden='true' name='[UPDATEDAT_SRC (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Count' caption='Updated Dt Key (Vw Dim Factor Dco)' datatype='integer' default-type='ordinal' hidden='true' name='[UPDATED_DT_KEY (VW_DIM_FACTOR_DCO)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Year' caption='Updated Ts' datatype='datetime' default-type='ordinal' hidden='true' name='[UPDATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Count' caption='User Dse Uid (Vw Dim User Dse Dco Rpt)' datatype='string' default-type='nominal' hidden='true' name='[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='USER_DSE_UID' datatype='string' default-type='nominal' name='[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='User Id' datatype='integer' default-type='ordinal' hidden='true' name='[USER_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Actor' datatype='string' default-type='nominal' name='[USER_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='User Uid' datatype='string' default-type='nominal' hidden='true' name='[USER_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_ACCOUNT_DCO_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_CHANNEL_DCO_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_CUSTOMER_DCO_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_MESSAGE_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_REASON_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DSE_CONFIG_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_FACTOR_DCO_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_PRODUCT_DCO_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_REP_TEAM_DCO_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_REP_TEAM_DCO_RPT (DCO.VW_DIM_REP_TEAM_DCO_RPT)_AFEC62CA896C452282B0D101F3C4FBFC]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_FACTOR_DCO' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_RPT_FACTOR_DCO_RPT (DCO.VW_DIM_RPT_FACTOR_DCO_RPT)_C56BD711EEFC483D81C37F1189CAC1B5]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SCENARIO_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_USER_DSE_DCO_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT)_43DC2BC8E1B246D7892773D79AC11274]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <column-instance column='[ACCOUNT_CNT (new version) (copy)_497647790488961026]' derivation='Avg' name='[avg:ACCOUNT_CNT (new version) (copy)_497647790488961026:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_497647************]' derivation='Avg' name='[avg:Calculation_497647************:qk]' pivot='key' type='quantitative' />
  <column-instance column='[REP_CNT (new version) (copy)_497647790488461313]' derivation='Avg' name='[avg:REP_CNT (new version) (copy)_497647790488461313:qk]' pivot='key' type='quantitative' />
  <column-instance column='[DCO_REASON_NAME]' derivation='Min' name='[min:DCO_REASON_NAME:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_1716434419576819713]' derivation='None' name='[none:Calculation_1716434419576819713:nk]' pivot='key' type='nominal' />
  <column-instance column='[DCO_REASON_NAME]' derivation='None' name='[none:DCO_REASON_NAME:nk]' pivot='key' type='nominal' />
  <column-instance column='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' derivation='None' name='[none:DCO_REASON_TYPE_NAME (copy)_660058877027909632:nk]' pivot='key' type='nominal' />
  <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' pivot='key' type='ordinal' />
  <column-instance column='[CANDIDATE_CNT (new version) (copy)_497647790489550852]' derivation='User' name='[usr:CANDIDATE_CNT (new version) (copy)_497647790489550852:qk]' pivot='key' type='quantitative' />
  <column-instance column='[DAY_CNT (new version) (copy)_497647790489280515]' derivation='User' name='[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]' pivot='key' type='quantitative' />
  <group caption='Action (DCO_REASON_SUMMARY,DAY(Date),Scenario)' hidden='true' name='[Action (DCO_REASON_SUMMARY,DAY(Date),Scenario)]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[DCO_REASON_SUMMARY]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
      <groupfilter function='level-members' level='[SCENARIO_NAME]' />
    </groupfilter>
  </group>
  <group auto-hidden='true' caption='Action (REASON,DAY(Date))' hidden='true' name='[Action (REASON,DAY(Date))]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
    </groupfilter>
  </group>
  <group caption='Action (REASON,DAY(Date),Scenario)' hidden='true' name='[Action (REASON,DAY(Date),Scenario)]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
      <groupfilter function='level-members' level='[SCENARIO_NAME]' />
    </groupfilter>
  </group>
  <group caption='Action (REASON,DAY(Date),Scenario,Scenario ver)' hidden='true' name='[Action (REASON,DAY(Date),Scenario,Scenario ver)]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
      <groupfilter function='level-members' level='[SCENARIO_NAME]' />
      <groupfilter function='level-members' level='[SCENARIO_VER]' />
    </groupfilter>
  </group>
  <group caption='Action (Reasons,DAY(Date)) 1' hidden='true' name='[Action (Reasons,DAY(Date)) 1]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' />
    </groupfilter>
  </group>
  <group caption='Action (Reasons,DAY(Date))' hidden='true' name='[Action (Reasons,DAY(Date))]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
    </groupfilter>
  </group>
  <group caption='Action (Reasons,DAY(Date),Scenario) 1' hidden='true' name='[Action (Reasons,DAY(Date),Scenario) 1]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
      <groupfilter function='level-members' level='[SCENARIO_NAME]' />
    </groupfilter>
  </group>
  <group caption='Action (Reasons,DAY(Date),Scenario)' hidden='true' name='[Action (Reasons,DAY(Date),Scenario)]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[DCO_REASON_SUMMARY]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
      <groupfilter function='level-members' level='[SCENARIO_NAME]' />
    </groupfilter>
  </group>
  <drill-paths>
    <drill-path name='Suggestion Candidates Hierarchy'>
      <field>[USER_NAME]</field>
      <field>[ACCOUNT_NAME]</field>
      <field>[PRODUCT_NAME]</field>
      <field>[CHANNEL_NAME]</field>
      <field>[FACTOR_NAME]</field>
      <field>[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]</field>
      <field>[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]</field>
      <field>[DCO_REASON_TYPE_NAME]</field>
      <field>[Calculation_1716434419576819713]</field>
      <field>[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</field>
      <field>[Suggested Date (copy)_986006877287538688]</field>
      <field>[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</field>
    </drill-path>
  </drill-paths>
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.875375' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.124625' show-aliased-fields='true' show-hidden-fields='true' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[none:DCO_REASON_TYPE_NAME (copy)_660058877027909632:nk]' type='palette'>
        <map to='\#4e79a7'>
          <bucket>&quot;Accept 6. Required-cap accepted&quot;</bucket>
        </map>
        <map to='\#4e79a7'>
          <bucket>&quot;Reject Unknown&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;Accept 7. Additional-cap accepted&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;Reject NA&quot;</bucket>
        </map>
        <map to='\#9c755f'>
          <bucket>&quot;Reject Insufficient User capacity&quot;</bucket>
        </map>
        <map to='\#b07aa1'>
          <bucket>&quot;Recommend Recommended in second-pass&quot;</bucket>
        </map>
        <map to='\#bab0ac'>
          <bucket>&quot;Reject Better candidate selected&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;Reject 4. No Required-cap&quot;</bucket>
        </map>
        <map to='\#edc948'>
          <bucket>&quot;Recommend Recommended in first-pass&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;Reject 3. No RepCap&quot;</bucket>
        </map>
        <map to='\#ff9da7'>
          <bucket>&quot;Reject Auto-snoozed&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[min:DCO_REASON_NAME:nk]' type='palette'>
        <map to='\#4e79a7'>
          <bucket>&quot;3. No RepCap&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>\%null\%</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;NA&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;6. Required-cap accepted&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;4. No Required-cap&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:DCO_REASON_NAME:nk]' type='palette'>
        <map to='\#4e79a7'>
          <bucket>&quot;3. No RepCap&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>\%null\%</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;NA&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;6. Required-cap accepted&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;4. No Required-cap&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_1716434419576819713:nk]' type='palette'>
        <map to='\#4e79a7'>
          <bucket>&quot;00. This action is high enough priority to justify using the available capacity for the channel and engagement bandwith for the account&quot;</bucket>
        </map>
        <map to='\#4e79a7'>
          <bucket>&quot;02. Account-level conflict&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;00. Recommended&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;03. Factor-level conflict&quot;</bucket>
        </map>
        <map to='\#bab0ac'>
          <bucket>&quot;06. Unknown reason&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;01. Auto-expired&quot;</bucket>
        </map>
        <map to='\#edc948'>
          <bucket>&quot;05. Channel capacity&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;04. End user capacity&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;Belarus&quot;' />
  </semantic-values>
  <date-options start-of-week='monday' />
  <datasource-dependencies datasource='Parameters'>
    <column caption='Scenario Group' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
      <calculation class='tableau' formula='&quot;Published&quot;' />
      <members>
        <member value='&quot;(All)&quot;' />
        <member value='&quot;Published&quot;' />
      </members>
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='VW_DIM_ACCOUNT_DCO_RPT' id='VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_CHANNEL_DCO_RPT' id='VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_CUSTOMER_DCO_RPT' id='VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DCO_MESSAGE_RPT' id='VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DCO_REASON_RPT' id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DSE_CONFIG_RPT' id='VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_FACTOR_DCO_RPT' id='VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_PRODUCT_DCO_RPT' id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SCENARIO_RPT' id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SEGMENT_RPT' id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_USER_DSE_DCO_RPT' id='VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2' id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
    <relationships>
      <relationship>
        <expression op='='>
          <expression op='[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC' />
      </relationship>
    </relationships>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='snowflake' version='0.0.0'>
  </primary-dialect>
  <overlay-dialect-set>
    <overlay-dialect class='vizengine' version='0.1.0'>
    </overlay-dialect>
  </overlay-dialect-set>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>false</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='monday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[:Measure Names]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <aliases>
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh]&quot;' value='Expected Value' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:ACCOUNT_CNT (new version) (copy)_497647790488961026:qk]&quot;' value='Avg. Suggested Dates' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:Calculation_497647************:qk]&quot;' value='Avg. Actors' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:REP_CNT (new version) (copy)_497647790488461313:qk]&quot;' value='Avg. Accounts' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:CANDIDATE_CNT (new version) (copy)_497647790489550852:qk]&quot;' value='Recommended Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]&quot;' value='Candidates Count' />
        </aliases>
      </column>
      <column aggregation='Sum' caption='DAY_CNT' datatype='integer' default-format='n#,##0;-#,##0' default-type='quantitative' layered='true' name='[ACCOUNT_CNT (new version) (copy)_497647790488961026]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='{FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], &#13;&#10;[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:&#13;&#10;countd([SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&#13;&#10;}' />
      </column>
      <column aggregation='Count' caption='Account' datatype='string' default-type='nominal' layered='true' name='[ACCOUNT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='ACCOUNT_UID' datatype='string' default-type='nominal' layered='true' name='[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='User' caption='RECOMMENDED_COUNT' datatype='integer' default-format='n#,##0;-#,##0' default-type='quantitative' layered='true' name='[CANDIDATE_CNT (new version) (copy)_497647790489550852]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='count(iif([RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],null))' />
      </column>
      <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='CHANNEL_UID' datatype='string' default-type='nominal' layered='true' name='[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[COUNTRY_NAME]' pivot='key' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' datatype='datetime' default-type='ordinal' layered='true' name='[CREATEDAT_SRC]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[CREATED_DT_KEY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[CUSTOMER_ID]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Reasons' datatype='string' default-type='nominal' layered='true' name='[Calculation_1716434419576819713]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[DCO_REASON_CODE] +&apos;. &apos;+[DCO_REASON_NAME]' />
      </column>
      <column aggregation='Sum' caption='REP_CNT' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_497647************]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]:countd([USER_NAME])}' />
      </column>
      <column aggregation='Count' caption='New Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_5911010483556352]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[SCENARIO_NAME]+ IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;, &apos;&apos;)' />
      </column>
      <column aggregation='User' caption='Index' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_942378240705122304]' pivot='key' role='measure' type='ordinal' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='index()'>
          <table-calc ordering-type='Rows' />
        </calculation>
      </column>
      <column aggregation='Count' caption='Channel ' datatype='string' default-type='nominal' layered='true' name='[Channel (copy)_544865224190140424]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[CHANNEL_NAME]' />
      </column>
      <column aggregation='User' caption='CANDIDATE_CNT' datatype='integer' default-format='n#,##0;-#,##0' default-type='quantitative' layered='true' name='[DAY_CNT (new version) (copy)_497647790489280515]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='count([PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])' />
      </column>
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Reason' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Reasons_' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='REASON' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433] +&apos; &apos;+[DCO_REASON_NAME]' />
      </column>
      <column aggregation='Count' caption='DCO_REASON_TYPE_NAME (new)' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [DCO_REASON_TYPE_NAME] =&apos;Accept&apos; and [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]then &apos;Published&apos; else [DCO_REASON_TYPE_NAME]  end' />
      </column>
      <column aggregation='Count' caption='Status' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='DCO_REASON_UID' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' caption='DCO_RUN_DATE' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' caption='DCO_RUN_UID' datatype='string' default-type='nominal' layered='true' name='[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO_RPT)]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='DIM_CUSTOMER_KEY' datatype='integer' default-type='ordinal' layered='true' name='[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[DIM_FACTOR_KEY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='DIM_MESSAGE_KEY' datatype='integer' default-type='ordinal' layered='true' name='[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='DIM_SEGMENT_KEY' datatype='integer' default-type='ordinal' layered='true' name='[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='DRIVER_TYPE' datatype='string' default-type='nominal' layered='true' name='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' datatype='datetime' default-type='ordinal' layered='true' name='[DW_CREATED_TS (VW_DIM_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Year' caption='Dw Created Ts (Vw F Daily Suggestion Movement Rpt V2)' datatype='datetime' default-type='ordinal' layered='true' name='[DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[DW_DELETED_FLAG (VW_DIM_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Year' caption='Relative Date' datatype='date' default-format='S' default-type='ordinal' layered='true' name='[Date (copy)_986006877293236225]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
        <calculation class='tableau' formula='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      </column>
      <column aggregation='Count' caption='Driver/Type ' datatype='string' default-type='nominal' layered='true' name='[Driver/Type (copy)_544865224190218249]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
      </column>
      <column aggregation='Count' caption='Factor' datatype='string' default-type='nominal' layered='true' name='[FACTOR_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[FACTOR_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='FACTOR_UID' datatype='string' default-type='nominal' layered='true' name='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' caption='FINAL_SCORE' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
      <column aggregation='User' caption='Avg FINAL_SCORE' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE (copy)_540431991555641356]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='avg({FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],&#10;[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:&#10;sum([FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&#10;})' />
      </column>
      <column aggregation='Count' caption='Factor ' datatype='string' default-type='nominal' layered='true' name='[Factor (copy)_544865224189366278]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[FACTOR_NAME]' />
      </column>
      <column aggregation='Count' caption='HCP_SEGMENT' datatype='string' default-type='nominal' layered='true' name='[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Parameter 3]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED (VW_DIM_SCENARIO_RPT)])&#13;&#10;END' />
      </column>
      <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' caption='IS_PUBLISHED' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' caption='Message Channel' datatype='string' default-type='nominal' layered='true' name='[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Message' datatype='string' default-type='nominal' layered='true' name='[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='PRODUCT_UID' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product ' datatype='string' default-type='nominal' layered='true' name='[Product (copy)_544865224189894663]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[PRODUCT_NAME]' />
      </column>
      <column aggregation='Count' caption='RECOMMENDED' datatype='boolean' default-type='nominal' layered='true' name='[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Sum' caption='ACCOUNT_CNT' datatype='integer' default-format='n#,##0;-#,##0' default-type='quantitative' layered='true' name='[REP_CNT (new version) (copy)_497647790488461313]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]: countd([ACCOUNT_NAME])}' />
      </column>
      <column aggregation='Count' caption='REP_TEAM_UID' datatype='string' default-type='nominal' layered='true' name='[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[RUN_CONFIG_FACTOR_ID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='SCENARIO_UID' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' auto-hidden='true' caption='Scenario ver' datatype='string' default-type='nominal' hidden='true' layered='true' name='[SCENARIO_VER]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='SE_CONFIG_ID' datatype='integer' default-type='ordinal' layered='true' name='[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Config ' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Suggested Date' datatype='date' datatype-customized='true' default-type='ordinal' layered='true' name='[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' caption='Suggestion Reference Id' datatype='string' default-type='nominal' layered='true' name='[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' caption='Suggested Month' datatype='date' datatype-customized='true' default-type='ordinal' layered='true' name='[Suggested Date (copy)_986006877287538688]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
        <calculation class='tableau' formula='DATE(DATETRUNC(&apos;month&apos;,date([SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])))' />
      </column>
      <column aggregation='Year' datatype='datetime' default-type='ordinal' layered='true' name='[UPDATEDAT_SRC]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[UPDATED_DT_KEY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='USER_DSE_UID' datatype='string' default-type='nominal' layered='true' name='[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Actor' datatype='string' default-type='nominal' layered='true' name='[USER_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <column-instance column='[ACCOUNT_CNT (new version) (copy)_497647790488961026]' derivation='Avg' name='[avg:ACCOUNT_CNT (new version) (copy)_497647790488961026:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_497647************]' derivation='Avg' name='[avg:Calculation_497647************:qk]' pivot='key' type='quantitative' />
      <column-instance column='[REP_CNT (new version) (copy)_497647790488461313]' derivation='Avg' name='[avg:REP_CNT (new version) (copy)_497647790488461313:qk]' pivot='key' type='quantitative' />
      <column-instance column='[DCO_REASON_NAME]' derivation='Min' name='[min:DCO_REASON_NAME:nk]' pivot='key' type='nominal' />
      <column-instance column='[Calculation_1716434419576819713]' derivation='None' name='[none:Calculation_1716434419576819713:nk]' pivot='key' type='nominal' />
      <column-instance column='[DCO_REASON_NAME]' derivation='None' name='[none:DCO_REASON_NAME:nk]' pivot='key' type='nominal' />
      <column-instance column='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' derivation='None' name='[none:DCO_REASON_TYPE_NAME (copy)_660058877027909632:nk]' pivot='key' type='nominal' />
      <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' pivot='key' type='ordinal' />
      <column-instance column='[CANDIDATE_CNT (new version) (copy)_497647790489550852]' derivation='User' name='[usr:CANDIDATE_CNT (new version) (copy)_497647790489550852:qk]' pivot='key' type='quantitative' />
      <column-instance column='[DAY_CNT (new version) (copy)_497647790489280515]' derivation='User' name='[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]' pivot='key' type='quantitative' />
      <group auto-hidden='true' caption='Action (DCO_REASON_SUMMARY,DAY(Date),Scenario)' hidden='true' layered='true' name='[Action (DCO_REASON_SUMMARY,DAY(Date),Scenario)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[DCO_REASON_SUMMARY]' />
          <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
          <groupfilter function='level-members' level='[SCENARIO_NAME]' />
        </groupfilter>
      </group>
      <group auto-hidden='true' caption='Action (REASON,DAY(Date))' hidden='true' layered='true' name='[Action (REASON,DAY(Date))]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' />
          <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
        </groupfilter>
      </group>
      <group auto-hidden='true' caption='Action (REASON,DAY(Date),Scenario)' hidden='true' layered='true' name='[Action (REASON,DAY(Date),Scenario)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' />
          <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
          <groupfilter function='level-members' level='[SCENARIO_NAME]' />
        </groupfilter>
      </group>
      <group auto-hidden='true' caption='Action (REASON,DAY(Date),Scenario,Scenario ver)' hidden='true' layered='true' name='[Action (REASON,DAY(Date),Scenario,Scenario ver)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' />
          <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
          <groupfilter function='level-members' level='[SCENARIO_NAME]' />
          <groupfilter function='level-members' level='[SCENARIO_VER]' />
        </groupfilter>
      </group>
      <group auto-hidden='true' caption='Action (Reasons,DAY(Date)) 1' hidden='true' layered='true' name='[Action (Reasons,DAY(Date)) 1]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
          <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' />
        </groupfilter>
      </group>
      <group auto-hidden='true' caption='Action (Reasons,DAY(Date))' hidden='true' layered='true' name='[Action (Reasons,DAY(Date))]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
          <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
        </groupfilter>
      </group>
      <group auto-hidden='true' caption='Action (Reasons,DAY(Date),Scenario) 1' hidden='true' layered='true' name='[Action (Reasons,DAY(Date),Scenario) 1]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
          <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
          <groupfilter function='level-members' level='[SCENARIO_NAME]' />
        </groupfilter>
      </group>
      <group auto-hidden='true' caption='Action (Reasons,DAY(Date),Scenario)' hidden='true' layered='true' name='[Action (Reasons,DAY(Date),Scenario)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[DCO_REASON_SUMMARY]' />
          <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
          <groupfilter function='level-members' level='[SCENARIO_NAME]' />
        </groupfilter>
      </group>
      <drill-paths>
        <drill-path layered='true' name='Suggestion Candidates Hierarchy'>
          <field>[USER_NAME]</field>
          <field>[ACCOUNT_NAME]</field>
          <field>[PRODUCT_NAME]</field>
          <field>[CHANNEL_NAME]</field>
          <field>[FACTOR_NAME]</field>
          <field>[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]</field>
          <field>[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]</field>
          <field>[DCO_REASON_TYPE_NAME]</field>
          <field>[Calculation_1716434419576819713]</field>
          <field>[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</field>
          <field>[Suggested Date (copy)_986006877287538688]</field>
          <field>[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</field>
        </drill-path>
      </drill-paths>
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.825826' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.174174' show-aliased-fields='true' show-hidden-fields='true' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[none:DCO_REASON_TYPE_NAME (copy)_660058877027909632:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Accept 6. Required-cap accepted&quot;</bucket>
            </map>
            <map to='#4e79a7'>
              <bucket>&quot;Reject Unknown&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;Accept 7. Additional-cap accepted&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Reject NA&quot;</bucket>
            </map>
            <map to='#9c755f'>
              <bucket>&quot;Reject Insufficient User capacity&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;Recommend Recommended in second-pass&quot;</bucket>
            </map>
            <map to='#bab0ac'>
              <bucket>&quot;Reject Better candidate selected&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;Reject 4. No Required-cap&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;Recommend Recommended in first-pass&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Reject 3. No RepCap&quot;</bucket>
            </map>
            <map to='#ff9da7'>
              <bucket>&quot;Reject Auto-snoozed&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[min:DCO_REASON_NAME:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;3. No RepCap&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>%null%</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;6. Required-cap accepted&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;4. No Required-cap&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:DCO_REASON_NAME:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;3. No RepCap&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>%null%</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;6. Required-cap accepted&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;4. No Required-cap&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_1716434419576819713:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;00. This action is high enough priority to justify using the available capacity for the channel and engagement bandwith for the account&quot;</bucket>
            </map>
            <map to='#4e79a7'>
              <bucket>&quot;02. Account-level conflict&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;00. Recommended&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;03. Factor-level conflict&quot;</bucket>
            </map>
            <map to='#bab0ac'>
              <bucket>&quot;06. Unknown reason&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;01. Auto-expired&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;05. Channel capacity&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;04. End user capacity&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;Belarus&quot;' />
      </semantic-values>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Scenario Group' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
          <calculation class='tableau' formula='&quot;Published&quot;' />
        </column>
      </datasource-dependencies>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='VW_DIM_ACCOUNT_DCO_RPT' id='VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_CHANNEL_DCO_RPT' id='VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_CUSTOMER_DCO_RPT' id='VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_DCO_MESSAGE_RPT' id='VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_DCO_REASON_RPT' id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_DSE_CONFIG_RPT' id='VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_FACTOR_DCO_RPT' id='VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_PRODUCT_DCO_RPT' id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SCENARIO_RPT' id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SEGMENT_RPT' id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_USER_DSE_DCO_RPT' id='VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2' id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
        <relationships>
          <relationship>
            <expression op='='>
              <expression op='[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
              <expression op='[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
            <second-end-point object-id='VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC' />
          </relationship>
        </relationships>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
  </datasources>
  <shared-views>
    <shared-view name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh'>
      <datasources>
        <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh' />
      </datasources>
      <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh'>
        <column aggregation='Count' caption='Config ' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column-instance column='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' derivation='None' name='[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]' context='true'>
        <groupfilter function='level-members' level='[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
    </shared-view>
  </shared-views>
  <actions>
    <action caption='Filter 1 (generated)' name='[Action2_53C044E1B2B0483C8B6EC2F33DDEBEB6]'>
      <activation auto-clear='true' type='on-select' />
      <source dashboard='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing' type='sheet' worksheet='1.1 - Suggestion Candidate Count and %' />
      <command command='tsc:tsl-filter'>
        <param name='special-fields' value='all' />
        <param name='target' value='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing' />
      </command>
    </action>
  </actions>
  <worksheets>
    <worksheet name='1.1 - Suggestion Candidate Count and %'>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontname='Tableau Medium' fontsize='12'>Suggestion Candidate Count and %</run>
            <run fontalignment='1'>Æ&#10;</run>
            <run fontalignment='1' fontcolor='#898989' fontname='Tableau Medium' fontsize='8' italic='true'>(This chart shows only last 10 dates)</run>
            <run fontalignment='1'>Æ&#10;</run>
          </formatted-text>
        </title>
        <caption>
          <formatted-text>
            <run>This chart shows only last 10 dates.</run>
          </formatted-text>
        </caption>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh'>
            <column aggregation='Count' caption='ACCOUNT_UID' datatype='string' default-type='nominal' layered='true' name='[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='CHANNEL_UID' datatype='string' default-type='nominal' layered='true' name='[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Reasons' datatype='string' default-type='nominal' layered='true' name='[Calculation_1716434419576819713]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[DCO_REASON_CODE] +&apos;. &apos;+[DCO_REASON_NAME]' />
            </column>
            <column aggregation='Count' caption='New Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_5911010483556352]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+ IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;, &apos;&apos;)' />
            </column>
            <column aggregation='User' caption='Index' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_942378240705122304]' pivot='key' role='measure' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='index()'>
                <table-calc ordering-type='Rows' />
              </calculation>
            </column>
            <column aggregation='User' caption='CANDIDATE_CNT' datatype='integer' default-format='n#,##0;-#,##0' default-type='quantitative' layered='true' name='[DAY_CNT (new version) (copy)_497647790489280515]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='count([PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])' />
            </column>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Reason' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='DCO_REASON_UID' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='DCO_RUN_DATE' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='DCO_RUN_UID' datatype='string' default-type='nominal' layered='true' name='[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='DIM_CUSTOMER_KEY' datatype='integer' default-type='ordinal' layered='true' name='[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='DIM_MESSAGE_KEY' datatype='integer' default-type='ordinal' layered='true' name='[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='DIM_SEGMENT_KEY' datatype='integer' default-type='ordinal' layered='true' name='[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='DRIVER_TYPE' datatype='string' default-type='nominal' layered='true' name='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='FACTOR_UID' datatype='string' default-type='nominal' layered='true' name='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='FINAL_SCORE' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
            <column aggregation='User' caption='Avg FINAL_SCORE' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE (copy)_540431991555641356]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='avg({FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],&#10;[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:&#10;sum([FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&#10;})' />
            </column>
            <column aggregation='Count' caption='HCP_SEGMENT' datatype='string' default-type='nominal' layered='true' name='[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 3]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED (VW_DIM_SCENARIO_RPT)])&#13;&#10;END' />
            </column>
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Count' caption='IS_PUBLISHED' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='PRODUCT_UID' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='REP_TEAM_UID' datatype='string' default-type='nominal' layered='true' name='[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='SCENARIO_UID' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='SE_CONFIG_ID' datatype='integer' default-type='ordinal' layered='true' name='[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Config ' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='USER_DSE_UID' datatype='string' default-type='nominal' layered='true' name='[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[DCO_REASON_DESCRIPTION]' derivation='Attribute' name='[attr:DCO_REASON_DESCRIPTION:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='Min' name='[min:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_1716434419576819713]' derivation='None' name='[none:Calculation_1716434419576819713:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_5911010483556352]' derivation='None' name='[none:Calculation_5911010483556352:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='None' name='[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' pivot='key' type='quantitative' />
            <column-instance column='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' derivation='None' name='[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]' pivot='key' type='ordinal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE]' derivation='None' name='[none:SEGMENT_TYPE:nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' derivation='None' name='[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[DAY_CNT (new version) (copy)_497647790489280515]' derivation='User' name='[pcto:usr:DAY_CNT (new version) (copy)_497647790489280515:qk:2]' pivot='key' type='quantitative'>
              <table-calc ordering-type='CellInPane' type='PctTotal' />
            </column-instance>
            <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_942378240705122304]' derivation='User' name='[usr:Calculation_942378240705122304:ok:1]' pivot='key' type='ordinal'>
              <table-calc ordering-type='Field'>
                <order field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' />
                <sort direction='DESC' using='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[min:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' />
              </table-calc>
            </column-instance>
            <column-instance column='[DAY_CNT (new version) (copy)_497647790489280515]' derivation='User' name='[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]' pivot='key' type='quantitative' />
            <column-instance column='[FINAL_SCORE (copy)_540431991555641356]' derivation='User' name='[usr:FINAL_SCORE (copy)_540431991555641356:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' context='true' filter-group='9'>
            <groupfilter function='level-members' level='[none:Calculation_1716434419576819713:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <natural-sort column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' direction='DESC' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]' context='true' filter-group='16'>
            <groupfilter function='level-members' level='[none:Calculation_5911010483556352:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' context='true' filter-group='5' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]' context='true' filter-group='6'>
            <groupfilter function='member' level='[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_NAME:nk]' context='true' filter-group='8'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_TYPE:nk]' context='true' filter-group='7'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:Calculation_942378240705122304:ok:1]'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[usr:Calculation_942378240705122304:ok:1]' member='1' />
              <groupfilter function='member' level='[usr:Calculation_942378240705122304:ok:1]' member='2' />
              <groupfilter function='member' level='[usr:Calculation_942378240705122304:ok:1]' member='3' />
              <groupfilter function='member' level='[usr:Calculation_942378240705122304:ok:1]' member='4' />
              <groupfilter function='member' level='[usr:Calculation_942378240705122304:ok:1]' member='5' />
              <groupfilter function='member' level='[usr:Calculation_942378240705122304:ok:1]' member='6' />
              <groupfilter function='member' level='[usr:Calculation_942378240705122304:ok:1]' member='7' />
              <groupfilter function='member' level='[usr:Calculation_942378240705122304:ok:1]' member='8' />
              <groupfilter function='member' level='[usr:Calculation_942378240705122304:ok:1]' member='9' />
              <groupfilter function='member' level='[usr:Calculation_942378240705122304:ok:1]' member='10' />
            </groupfilter>
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_TYPE:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:Calculation_942378240705122304:ok:1]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='display' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[pcto:usr:DAY_CNT (new version) (copy)_497647790489280515:qk:2]' scope='rows' value='false' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='font-family' data-class='subtotal' value='Tableau Book' />
            <format attr='color' value='#1b1b1b' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:FINAL_SCORE (copy)_540431991555641356:qk]' value='n!en_US!#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[pcto:usr:DAY_CNT (new version) (copy)_497647790489280515:qk:2]' value='p0.0%' />
          </style-rule>
          <style-rule element='label'>
            <format attr='display' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:Calculation_942378240705122304:ok:1]' value='true' />
            <format attr='text-orientation' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:Calculation_942378240705122304:ok:1]' value='0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' value='*m/d/yy' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:FINAL_SCORE (copy)_540431991555641356:qk]' value='n!en_US!#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[pcto:usr:DAY_CNT (new version) (copy)_497647790489280515:qk:2]' value='p0.0%' />
          </style-rule>
          <style-rule element='table'>
            <format attr='show-null-value-warning' value='false' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#f0f3fa' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' value='Run Date Range' />
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]' value=' Scenario' />
          </style-rule>
        </style>
        <panes>
          <pane id='4' selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[attr:DCO_REASON_DESCRIPTION:nk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:FINAL_SCORE (copy)_540431991555641356:qk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[pcto:usr:DAY_CNT (new version) (copy)_497647790489280515:qk:2]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#898989'>Average Expected Value:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:FINAL_SCORE (copy)_540431991555641356:qk]>]]></run>
                <run fontcolor='#898989'>Æ&#9;</run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Reason:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Reason description:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[attr:DCO_REASON_DESCRIPTION:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Date:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Candidates Count:               </run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]> ( <[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[pcto:usr:DAY_CNT (new version) (copy)_497647790489280515:qk:2]>)]]></run>
                <run>Æ&#10;&#10;</run>
              </formatted-text>
            </customized-tooltip>
            <customized-label>
              <formatted-text>
                <run> (&lt;</run>
                <run>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]</run>
                <run>&gt;)&#10;&lt;</run>
                <run>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[pcto:usr:DAY_CNT (new version) (copy)_497647790489280515:qk:2]</run>
                <run>&gt;</run>
              </formatted-text>
            </customized-label>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
                <format attr='mark-labels-mode' value='all' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[pcto:usr:DAY_CNT (new version) (copy)_497647790489280515:qk:2]</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]</cols>
      </table>
      <simple-id uuid='{B1110F58-F9A6-4DFD-AE43-1200D751A6FF}' />
    </worksheet>
    <worksheet name='1.2 - Suggestion Candidate Summary by Factor'>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontname='Tableau Medium' fontsize='12'>Suggestion Candidate Summary by Factor</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh'>
            <column aggregation='Count' caption='Account' datatype='string' default-type='nominal' layered='true' name='[ACCOUNT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='User' caption='RECOMMENDED_COUNT' datatype='integer' default-format='n#,##0;-#,##0' default-type='quantitative' layered='true' name='[CANDIDATE_CNT (new version) (copy)_497647790489550852]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='count(iif([RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],null))' />
            </column>
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Reasons' datatype='string' default-type='nominal' layered='true' name='[Calculation_1716434419576819713]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[DCO_REASON_CODE] +&apos;. &apos;+[DCO_REASON_NAME]' />
            </column>
            <column aggregation='Sum' caption='REP_CNT' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_497647************]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]:countd([USER_NAME])}' />
            </column>
            <column aggregation='Count' caption='New Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_5911010483556352]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+ IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;, &apos;&apos;)' />
            </column>
            <column aggregation='Count' caption='Channel ' datatype='string' default-type='nominal' layered='true' name='[Channel (copy)_544865224190140424]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[CHANNEL_NAME]' />
            </column>
            <column aggregation='User' caption='CANDIDATE_CNT' datatype='integer' default-format='n#,##0;-#,##0' default-type='quantitative' layered='true' name='[DAY_CNT (new version) (copy)_497647790489280515]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='count([PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])' />
            </column>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Reason' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='DCO_RUN_DATE' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='DRIVER_TYPE' datatype='string' default-type='nominal' layered='true' name='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Driver/Type ' datatype='string' default-type='nominal' layered='true' name='[Driver/Type (copy)_544865224190218249]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
            </column>
            <column aggregation='Count' caption='Factor' datatype='string' default-type='nominal' layered='true' name='[FACTOR_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='FACTOR_UID' datatype='string' default-type='nominal' layered='true' name='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Factor ' datatype='string' default-type='nominal' layered='true' name='[Factor (copy)_544865224189366278]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[FACTOR_NAME]' />
            </column>
            <column aggregation='Count' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 3]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED (VW_DIM_SCENARIO_RPT)])&#13;&#10;END' />
            </column>
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='PRODUCT_UID' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Product ' datatype='string' default-type='nominal' layered='true' name='[Product (copy)_544865224189894663]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[PRODUCT_NAME]' />
            </column>
            <column aggregation='Count' caption='RECOMMENDED' datatype='boolean' default-type='nominal' layered='true' name='[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Sum' caption='ACCOUNT_CNT' datatype='integer' default-format='n#,##0;-#,##0' default-type='quantitative' layered='true' name='[REP_CNT (new version) (copy)_497647790488461313]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]: countd([ACCOUNT_NAME])}' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config ' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Suggested Date' datatype='date' datatype-customized='true' default-type='ordinal' layered='true' name='[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Actor' datatype='string' default-type='nominal' layered='true' name='[USER_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[Calculation_497647************]' derivation='Avg' name='[avg:Calculation_497647************:qk]' pivot='key' type='quantitative' />
            <column-instance column='[REP_CNT (new version) (copy)_497647790488461313]' derivation='Avg' name='[avg:REP_CNT (new version) (copy)_497647790488461313:qk]' pivot='key' type='quantitative' />
            <column-instance column='[ACCOUNT_NAME]' derivation='None' name='[none:ACCOUNT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[CHANNEL_NAME]' derivation='None' name='[none:CHANNEL_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1716434419576819713]' derivation='None' name='[none:Calculation_1716434419576819713:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_5911010483556352]' derivation='None' name='[none:Calculation_5911010483556352:nk]' pivot='key' type='nominal' />
            <column-instance column='[Channel (copy)_544865224190140424]' derivation='None' name='[none:Channel (copy)_544865224190140424:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='None' name='[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' pivot='key' type='quantitative' />
            <column-instance column='[Driver/Type (copy)_544865224190218249]' derivation='None' name='[none:Driver/Type (copy)_544865224190218249:nk]' pivot='key' type='nominal' />
            <column-instance column='[FACTOR_NAME]' derivation='None' name='[none:FACTOR_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Factor (copy)_544865224189366278]' derivation='None' name='[none:Factor (copy)_544865224189366278:nk]' pivot='key' type='nominal' />
            <column-instance column='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' derivation='None' name='[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]' pivot='key' type='ordinal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Product (copy)_544865224189894663]' derivation='None' name='[none:Product (copy)_544865224189894663:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE]' derivation='None' name='[none:SEGMENT_TYPE:nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' derivation='None' name='[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[USER_NAME]' derivation='None' name='[none:USER_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' pivot='key' type='ordinal' />
            <column-instance column='[CANDIDATE_CNT (new version) (copy)_497647790489550852]' derivation='User' name='[usr:CANDIDATE_CNT (new version) (copy)_497647790489550852:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DAY_CNT (new version) (copy)_497647790489280515]' derivation='User' name='[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]'>
            <groupfilter function='union' user:op='manual'>
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:Calculation_497647************:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:REP_CNT (new version) (copy)_497647790488461313:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:CANDIDATE_CNT (new version) (copy)_497647790489550852:qk]&quot;' />
            </groupfilter>
          </filter>
          <manual-sort column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]' direction='ASC'>
            <dictionary>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:Calculation_497647************:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:REP_CNT (new version) (copy)_497647790488461313:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:ACCOUNT_CNT (copy)_544865224192114702:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:CANDIDATE_CNT (new version) (copy)_497647790489550852:qk]&quot;</bucket>
            </dictionary>
          </manual-sort>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[Action (Reasons,DAY(Date)) 1]'>
            <groupfilter function='crossjoin' user:ui-action-filter='[Action2_53C044E1B2B0483C8B6EC2F33DDEBEB6]' user:ui-enumeration='all' user:ui-marker='enumerate'>
              <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
              <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk]' context='true' filter-group='12'>
            <groupfilter function='level-members' level='[none:ACCOUNT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]' context='true' filter-group='13'>
            <groupfilter function='level-members' level='[none:CHANNEL_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' context='true' filter-group='9'>
            <groupfilter function='level-members' level='[none:Calculation_1716434419576819713:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]' context='true' filter-group='16'>
            <groupfilter function='level-members' level='[none:Calculation_5911010483556352:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' context='true' filter-group='5' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk]' context='true' filter-group='14'>
            <groupfilter function='level-members' level='[none:Driver/Type (copy)_544865224190218249:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME:nk]' context='true' filter-group='15'>
            <groupfilter function='level-members' level='[none:FACTOR_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]' context='true' filter-group='6'>
            <groupfilter function='member' level='[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_NAME:nk]' context='true' filter-group='8'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_TYPE:nk]' context='true' filter-group='7'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:USER_NAME:nk]' context='true' filter-group='11'>
            <groupfilter function='level-members' level='[none:USER_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_TYPE:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:USER_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[Action (Reasons,DAY(Date)) 1]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]' value='96' />
            <format attr='color' data-class='subtotal' value='#666666' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[Multiple Values]' value='N' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:Calculation_497647************:qk]' value='n!en_US!#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:REP_CNT (new version) (copy)_497647790488461313:qk]' value='n!en_US!#,##0' />
          </style-rule>
          <style-rule element='header'>
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk]' value='113' />
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]' value='128' />
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]' value='185' />
            <format attr='band-color' scope='rows' value='#********' />
            <format attr='border-width' data-class='subtotal' value='0' />
            <format attr='border-style' data-class='subtotal' value='none' />
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]' value='28' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:Calculation_497647************:qk]' value='n!en_US!#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:REP_CNT (new version) (copy)_497647790488461313:qk]' value='n!en_US!#,##0' />
          </style-rule>
          <style-rule element='pane'>
            <format attr='band-color' scope='rows' value='#********' />
            <format attr='border-width' data-class='subtotal' value='0' />
            <format attr='border-style' data-class='subtotal' value='none' />
          </style-rule>
          <style-rule element='table'>
            <format attr='band-size' scope='rows' value='1' />
          </style-rule>
          <style-rule element='table-div'>
            <format attr='stroke-size' scope='cols' value='0' />
            <format attr='line-visibility' scope='cols' value='off' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#f0f3fa' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' value='Outcome  Code'>
              <formatted-text>
                <run>Outcome  </run>
                <run>Code</run>
              </formatted-text>
            </format>
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[Multiple Values]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Factor (copy)_544865224189366278:nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Product (copy)_544865224189894663:nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Channel (copy)_544865224190140424:nk] / [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk])))</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]</cols>
      </table>
      <simple-id uuid='{7E6EA912-9DF9-4DEC-8DF8-8E77BCBCD5F5}' />
    </worksheet>
    <worksheet name='1.3 - Suggestion Candidates'>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontname='Tableau Medium' fontsize='12'>Suggestion Candidates</run>
            <run fontalignment='1'>Æ&#10;</run>
            <run fontalignment='1' fontcolor='#898989' fontname='Tableau Medium' fontsize='8' italic='true'>( Hierarchy: </run>
            <run fontalignment='1' fontcolor='#898989' fontname='Tableau Medium' fontsize='8' italic='true'>Actor -&gt; Account -&gt; Product -&gt; Channel -&gt; Driver/Type -&gt; Factor -&gt; Message -&gt; Message Channel -&gt; Status -&gt; Reasons -&gt; Suggestion Reference ID -&gt; Suggested Month -&gt; Suggested Date )</run>
          </formatted-text>
        </title>
        <caption>
          <formatted-text>
            <run fontsize='8'>Hierarchy: </run>
            <run fontsize='8' italic='true'>Actor -&gt; Account -&gt; Product -&gt; Channel -&gt; Driver/Type -&gt; Factor -&gt; Message -&gt; Message Channel -&gt; Status -&gt; Reasons -&gt; Suggestion Reference ID -&gt; Suggested Month -&gt; Suggested Date</run>
          </formatted-text>
        </caption>
      </layout-options>
      <repository-location id='1_3-SuggestionCandidates' path='/t/${TABLEAU_SITE}/workbooks/PFIZERDEPROD${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracing_16692161306780' revision='' site='${TABLEAU_SITE}' />
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh'>
            <column aggregation='Count' caption='Account' datatype='string' default-type='nominal' layered='true' name='[ACCOUNT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Reasons' datatype='string' default-type='nominal' layered='true' name='[Calculation_1716434419576819713]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[DCO_REASON_CODE] +&apos;. &apos;+[DCO_REASON_NAME]' />
            </column>
            <column aggregation='Count' caption='New Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_5911010483556352]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+ IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;, &apos;&apos;)' />
            </column>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Reason' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Status' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='DCO_RUN_DATE' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='DRIVER_TYPE' datatype='string' default-type='nominal' layered='true' name='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='Relative Date' datatype='date' default-format='S' default-type='ordinal' layered='true' name='[Date (copy)_986006877293236225]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
            </column>
            <column aggregation='Count' caption='Driver/Type ' datatype='string' default-type='nominal' layered='true' name='[Driver/Type (copy)_544865224190218249]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
            </column>
            <column aggregation='Count' caption='Factor' datatype='string' default-type='nominal' layered='true' name='[FACTOR_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='FINAL_SCORE' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 3]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED (VW_DIM_SCENARIO_RPT)])&#13;&#10;END' />
            </column>
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Message Channel' datatype='string' default-type='nominal' layered='true' name='[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Message' datatype='string' default-type='nominal' layered='true' name='[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Product' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config ' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Actor' datatype='string' default-type='nominal' layered='true' name='[USER_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='Avg' name='[avg:FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' pivot='key' type='quantitative' />
            <column-instance column='[ACCOUNT_NAME]' derivation='None' name='[none:ACCOUNT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[CHANNEL_NAME]' derivation='None' name='[none:CHANNEL_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1716434419576819713]' derivation='None' name='[none:Calculation_1716434419576819713:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_5911010483556352]' derivation='None' name='[none:Calculation_5911010483556352:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='None' name='[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' pivot='key' type='quantitative' />
            <column-instance column='[Date (copy)_986006877293236225]' derivation='None' name='[none:Date (copy)_986006877293236225:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Driver/Type (copy)_544865224190218249]' derivation='None' name='[none:Driver/Type (copy)_544865224190218249:nk]' pivot='key' type='nominal' />
            <column-instance column='[FACTOR_NAME]' derivation='None' name='[none:FACTOR_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' derivation='None' name='[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]' pivot='key' type='ordinal' />
            <column-instance column='[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]' derivation='None' name='[none:MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]' derivation='None' name='[none:MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE]' derivation='None' name='[none:SEGMENT_TYPE:nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' derivation='None' name='[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[USER_NAME]' derivation='None' name='[none:USER_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' pivot='key' type='ordinal' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[Action (Reasons,DAY(Date)) 1]'>
            <groupfilter function='crossjoin' user:ui-action-filter='[Action2_53C044E1B2B0483C8B6EC2F33DDEBEB6]' user:ui-enumeration='all' user:ui-marker='enumerate'>
              <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
              <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk]' context='true' filter-group='12'>
            <groupfilter function='level-members' level='[none:ACCOUNT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]' context='true' filter-group='13'>
            <groupfilter function='level-members' level='[none:CHANNEL_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' context='true' filter-group='9'>
            <groupfilter function='level-members' level='[none:Calculation_1716434419576819713:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]' context='true' filter-group='16'>
            <groupfilter function='level-members' level='[none:Calculation_5911010483556352:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' context='true' included-values='all' />
          <filter class='relative-date' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Date (copy)_986006877293236225:qk]' context='true' first-period='-6' include-future='true' include-null='false' last-period='0' period-type-v2='day' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk]' context='true' filter-group='14'>
            <groupfilter function='level-members' level='[none:Driver/Type (copy)_544865224190218249:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME:nk]' context='true' filter-group='15'>
            <groupfilter function='level-members' level='[none:FACTOR_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]' context='true' filter-group='6'>
            <groupfilter function='member' level='[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_NAME:nk]' context='true' filter-group='8'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_TYPE:nk]' context='true' filter-group='7'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:USER_NAME:nk]' context='true' filter-group='11'>
            <groupfilter function='level-members' level='[none:USER_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Date (copy)_986006877293236225:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_TYPE:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:USER_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[Action (Reasons,DAY(Date)) 1]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='border-width' value='0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' value='n!en_US!#,##0.00' />
          </style-rule>
          <style-rule element='header'>
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk]' value='125' />
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]' value='103' />
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk]' value='74' />
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_REASON_TYPE_NAME:nk]' value='65' />
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]' value='80' />
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' value='168' />
            <format attr='band-color' scope='rows' value='#********' />
            <format attr='border-width' value='0' />
            <format attr='border-style' data-class='subtotal' value='none' />
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT):nk]' value='113' />
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT):nk]' value='72' />
          </style-rule>
          <style-rule element='label'>
            <format attr='font-family' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]' value='Tableau Medium' />
            <format attr='color' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]' value='#333333' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' value='n!en_US!#,##0.00' />
          </style-rule>
          <style-rule element='pane'>
            <format attr='band-color' scope='rows' value='#********' />
            <format attr='border-width' value='0' />
            <format attr='border-style' data-class='subtotal' value='none' />
          </style-rule>
          <style-rule element='table'>
            <format attr='band-size' scope='rows' value='1' />
          </style-rule>
          <style-rule element='table-div'>
            <format attr='stroke-size' scope='cols' value='0' />
            <format attr='line-visibility' scope='cols' value='off' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#f0f3fa' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' value='Run Date Range'>
              <formatted-text>
                <run>Run Date Range</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Date (copy)_986006877293236225:qk]' value='Run Date Range'>
              <formatted-text>
                <run>Run Date Range</run>
              </formatted-text>
            </format>
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878' fontname='Tableau Book' fontsize='10'>Average Expected Value:&#9;</run>
                <run bold='true' fontcolor='#333333' fontname='Tableau Book' fontsize='10'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[avg:FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Account:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Reasons:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Channel:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Status:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_REASON_TYPE_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Driver/Type:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Message Channel:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_CHANNEL_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Message:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Product:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Actor:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:REP_TEAM_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Factor:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:RPT_FACTOR_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Suggested Date:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SUGGESTED_DATE:ok]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Suggestion Reference Id:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SUGGESTION_REFERENCE_ID:nk]>]]></run>
                <run>Æ&#10;</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
                <format attr='minheight' value='-1' />
                <format attr='maxheight' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:USER_NAME:nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME:nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT):nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT):nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_REASON_TYPE_NAME:nk] / [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk])))))))))</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]</cols>
      </table>
      <simple-id uuid='{4FA9C83B-534E-403B-8D99-B5D028386F83}' />
    </worksheet>
  </worksheets>
  <dashboards>
    <dashboard _.fcp.AccessibleZoneTabOrder.true...enable-sort-zone-taborder='true' name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing'>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracing/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracing?rev=' id='8010332' path='/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracing' revision='' site='${TABLEAU_SITE}' />
      <style>
        <style-rule element='parameter-ctrl-title'>
          <format attr='text-align' value='left' />
        </style-rule>
      </style>
      <size sizing-mode='automatic' />
      <datasources>
        <datasource name='Parameters' />
        <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh' />
      </datasources>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Scenario Group' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
          <calculation class='tableau' formula='&quot;Published&quot;' />
          <members>
            <member value='&quot;(All)&quot;' />
            <member value='&quot;Published&quot;' />
          </members>
        </column>
      </datasource-dependencies>
      <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh'>
        <column aggregation='Count' caption='Account' datatype='string' default-type='nominal' layered='true' name='[ACCOUNT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Reasons' datatype='string' default-type='nominal' layered='true' name='[Calculation_1716434419576819713]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='[DCO_REASON_CODE] +&apos;. &apos;+[DCO_REASON_NAME]' />
        </column>
        <column aggregation='Count' caption='New Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_5911010483556352]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='[SCENARIO_NAME]+ IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;, &apos;&apos;)' />
        </column>
        <column aggregation='Year' caption='DCO_RUN_DATE' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
        <column aggregation='Year' caption='Relative Date' datatype='date' default-format='S' default-type='ordinal' layered='true' name='[Date (copy)_986006877293236225]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
          <calculation class='tableau' formula='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
        </column>
        <column aggregation='Count' caption='Driver/Type ' datatype='string' default-type='nominal' layered='true' name='[Driver/Type (copy)_544865224190218249]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
        </column>
        <column aggregation='Count' caption='Factor' datatype='string' default-type='nominal' layered='true' name='[FACTOR_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Product' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Config ' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Actor' datatype='string' default-type='nominal' layered='true' name='[USER_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column-instance column='[ACCOUNT_NAME]' derivation='None' name='[none:ACCOUNT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[CHANNEL_NAME]' derivation='None' name='[none:CHANNEL_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[Calculation_1716434419576819713]' derivation='None' name='[none:Calculation_1716434419576819713:nk]' pivot='key' type='nominal' />
        <column-instance column='[Calculation_5911010483556352]' derivation='None' name='[none:Calculation_5911010483556352:nk]' pivot='key' type='nominal' />
        <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='None' name='[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' pivot='key' type='quantitative' />
        <column-instance column='[Date (copy)_986006877293236225]' derivation='None' name='[none:Date (copy)_986006877293236225:qk]' pivot='key' type='quantitative' />
        <column-instance column='[Driver/Type (copy)_544865224190218249]' derivation='None' name='[none:Driver/Type (copy)_544865224190218249:nk]' pivot='key' type='nominal' />
        <column-instance column='[FACTOR_NAME]' derivation='None' name='[none:FACTOR_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SEGMENT_TYPE]' derivation='None' name='[none:SEGMENT_TYPE:nk]' pivot='key' type='nominal' />
        <column-instance column='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' derivation='None' name='[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]' pivot='key' type='nominal' />
        <column-instance column='[USER_NAME]' derivation='None' name='[none:USER_NAME:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <zones>
        <zone h='100000' id='4' type-v2='layout-basic' w='100000' x='0' y='0'>
          <zone h='98034' id='1147' param='horz' type-v2='layout-flow' w='98884' x='558' y='983'>
            <zone h='98034' id='633' type-v2='layout-basic' w='98884' x='558' y='983'>
              <zone h='37520' id='627' param='horz' type-v2='layout-flow' w='98884' x='558' y='983'>
                <zone h='37520' id='625' type-v2='layout-basic' w='98884' x='558' y='983'>
                  <zone forceUpdate='true' h='4838' id='5' type-v2='text' w='98884' x='558' y='983'>
                    <formatted-text>
                      <run bold='true' fontalignment='1' fontcolor='#242424' fontname='Tableau Medium' fontsize='15'>${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing </run>
                    </formatted-text>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='none' />
                      <format attr='border-width' value='0' />
                      <format attr='margin' value='4' />
                    </zone-style>
                  </zone>
                  <zone h='32682' id='624' name='1.1 - Suggestion Candidate Count and %' w='59261' x='558' y='5821'>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='none' />
                      <format attr='border-width' value='0' />
                      <format attr='margin' value='4' />
                    </zone-style>
                  </zone>
                  <zone h='32682' id='641' name='1.1 - Suggestion Candidate Count and %' pane-specification-id='4' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' show-title='false' type-v2='color' w='11508' x='59819' y='5821'>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='none' />
                      <format attr='border-width' value='0' />
                      <format attr='margin' value='4' />
                    </zone-style>
                  </zone>
                  <zone h='32682' id='1176' layout-strategy-id='distribute-evenly' param='vert' type-v2='layout-flow' w='13978' x='71327' y='5821'>
                    <zone h='8231' id='636' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' type-v2='filter' values='relevant' w='13957' x='71319' y='5774'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone h='8231' id='959' mode='checkdropdown' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='13957' x='71319' y='14005'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone h='8108' id='1070' mode='checkdropdown' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_TYPE:nk]' show-apply='true' type-v2='filter' values='relevant' w='13957' x='71319' y='22236'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone h='8108' id='1128' mode='checkdropdown' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]' show-apply='true' type-v2='filter' values='relevant' w='13957' x='71319' y='30344'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='none' />
                      <format attr='border-width' value='1' />
                    </zone-style>
                  </zone>
                  <zone h='32682' id='1177' layout-strategy-id='distribute-evenly' param='vert' type-v2='layout-flow' w='14137' x='85305' y='5821'>
                    <zone fixed-size='33' h='8231' id='1148' is-fixed='true' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' show-apply='true' type-v2='filter' values='relevant' w='14166' x='85276' y='5774'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone h='8231' id='1111' mode='checkdropdown' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]' show-apply='true' type-v2='filter' values='relevant' w='14166' x='85276' y='14005'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone h='8108' id='1027' mode='checkdropdown' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='14166' x='85276' y='22236'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone fixed-size='55' h='8108' id='1032' is-fixed='true' mode='compact' param='[Parameters].[Parameter 3]' type-v2='paramctrl' w='14166' x='85276' y='30344'>
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                      </zone-style>
                    </zone>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='none' />
                      <format attr='border-width' value='1' />
                    </zone-style>
                  </zone>
                </zone>
              </zone>
              <zone h='29971' id='632' name='1.3 - Suggestion Candidates' w='89723' x='558' y='69046'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='margin-top' value='8' />
                  <format attr='margin-right' value='0' />
                  <format attr='margin-bottom' value='0' />
                  <format attr='margin-left' value='0' />
                </zone-style>
              </zone>
              <zone h='24914' id='648' name='1.2 - Suggestion Candidate Summary by Factor' w='98884' x='558' y='44132'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                </zone-style>
              </zone>
              <zone h='5629' id='1079' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='19825' x='20166' y='38503'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                </zone-style>
              </zone>
              <zone h='5629' id='1082' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='19823' x='39991' y='38503'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                </zone-style>
              </zone>
              <zone h='5629' id='1085' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk]' show-apply='true' type-v2='filter' values='relevant' w='19763' x='59814' y='38503'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                </zone-style>
              </zone>
              <zone h='5629' id='1138' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:USER_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='19608' x='558' y='38503'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                </zone-style>
              </zone>
              <zone h='5629' id='1141' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='19865' x='79577' y='38503'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                </zone-style>
              </zone>
              <zone h='29971' id='1223' layout-strategy-id='distribute-evenly' param='vert' type-v2='layout-flow' w='9161' x='90281' y='69046'>
                <zone fixed-size='70' h='14987' id='1212' is-fixed='true' name='1.3 - Suggestion Candidates' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Date (copy)_986006877293236225:qk]' type-v2='filter' values='database' w='9142' x='90300' y='69042'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone fixed-size='81' h='14988' id='1216' is-fixed='true' name='1.3 - Suggestion Candidates' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' type-v2='filter' values='relevant' w='9142' x='90300' y='84029'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
              </zone>
            </zone>
          </zone>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='none' />
            <format attr='border-width' value='0' />
            <format attr='margin' value='8' />
          </zone-style>
        </zone>
      </zones>
      <devicelayouts>
        <devicelayout auto-generated='true' name='Phone'>
          <size maxheight='1750' minheight='1750' sizing-mode='vscroll' />
          <zones>
            <zone h='100000' id='1280' type-v2='layout-basic' w='100000' x='0' y='0'>
              <zone h='98034' id='1279' param='vert' type-v2='layout-flow' w='98884' x='558' y='983'>
                <zone forceUpdate='true' h='4838' id='5' type-v2='text' w='98884' x='558' y='983'>
                  <formatted-text>
                    <run bold='true' fontalignment='1' fontcolor='#242424' fontname='Tableau Medium' fontsize='15'>${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing </run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='8231' id='636' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' type-v2='filter' values='relevant' w='13957' x='71319' y='5774'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='8231' id='959' mode='checkdropdown' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='13957' x='71319' y='14005'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='8108' id='1070' mode='checkdropdown' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_TYPE:nk]' show-apply='true' type-v2='filter' values='relevant' w='13957' x='71319' y='22236'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='8108' id='1128' mode='checkdropdown' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]' show-apply='true' type-v2='filter' values='relevant' w='13957' x='71319' y='30344'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='8231' id='1111' mode='checkdropdown' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]' show-apply='true' type-v2='filter' values='relevant' w='14166' x='85276' y='14005'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='8108' id='1027' mode='checkdropdown' name='1.1 - Suggestion Candidate Count and %' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='14166' x='85276' y='22236'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='253' h='32682' id='624' is-fixed='true' name='1.1 - Suggestion Candidate Count and %' w='59261' x='558' y='5821'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='32682' id='641' name='1.1 - Suggestion Candidate Count and %' pane-specification-id='4' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' show-title='false' type-v2='color' w='11508' x='59819' y='5821'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='55' h='8108' id='1032' mode='compact' param='[Parameters].[Parameter 3]' type-v2='paramctrl' w='14166' x='85276' y='30344'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='33' h='8231' id='1148' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' show-apply='true' type-v2='filter' values='relevant' w='14166' x='85276' y='5774'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='5629' id='1079' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='19825' x='20166' y='38503'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='5629' id='1082' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='19823' x='39991' y='38503'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='5629' id='1085' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk]' show-apply='true' type-v2='filter' values='relevant' w='19763' x='59814' y='38503'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='5629' id='1138' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:USER_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='19608' x='558' y='38503'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='5629' id='1141' mode='checkdropdown' name='1.2 - Suggestion Candidate Summary by Factor' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='19865' x='79577' y='38503'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='191' h='24914' id='648' is-fixed='true' name='1.2 - Suggestion Candidate Summary by Factor' w='98884' x='558' y='44132'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='70' h='14987' id='1212' name='1.3 - Suggestion Candidates' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Date (copy)_986006877293236225:qk]' type-v2='filter' values='database' w='9142' x='90300' y='69042'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='81' h='14988' id='1216' name='1.3 - Suggestion Candidates' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):qk]' type-v2='filter' values='relevant' w='9142' x='90300' y='84029'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='232' h='29971' id='632' is-fixed='true' name='1.3 - Suggestion Candidates' w='89723' x='558' y='69046'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='8' />
              </zone-style>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{8E686681-6A5E-42FA-AFC9-6D026736A2AE}' />
    </dashboard>
  </dashboards>
  <windows source-height='30'>
    <window class='dashboard' maximized='true' name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing'>
      <viewpoints>
        <viewpoint name='1.1 - Suggestion Candidate Count and %'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='1.2 - Suggestion Candidate Summary by Factor'>
          <zoom type='fit-width' />
        </viewpoint>
        <viewpoint name='1.3 - Suggestion Candidates'>
          <zoom type='fit-width' />
        </viewpoint>
      </viewpoints>
      <active id='624' />
      <simple-id uuid='{C18AB0A0-155E-4BFE-BB49-CF8484EF0245}' />
    </window>
    <window class='worksheet' hidden='true' name='1.1 - Suggestion Candidate Count and %'>
      <cards>
        <edge name='left'>
          <strip size='224'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='25'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='212'>
            <card param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' type='filter' />
            <card param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]' type='filter' />
            <card mode='compact' param='[Parameters].[Parameter 3]' type='parameter' />
            <card pane-specification-id='4' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]' type='color' />
          </strip>
        </edge>
        <edge name='bottom'>
          <strip size='68'>
            <card type='caption' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[Action (REASON,DAY(Date))]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[attr:Date (copy)_942378240710193157:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:COUNTRY_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1468455010902638592:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_465630037247307799:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_5911010483556352:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_REASON_DESCRIPTION:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_REASON_TYPE_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_RUN_UID:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DRIVER_TYPE:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DS_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT):nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:LATEST_RUN:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_CHANNEL_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Product Flag (copy)_1468455011170881539:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:RPT_FACTOR_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SCENARIO_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SCENARIO_VER:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SEGMENT_TYPE:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT):nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SE_CONFIG_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SUGGESTION_REFERENCE_ID:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:Calculation_942378240705122304:ok:10]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:Calculation_942378240705122304:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:Date (-10 days) (copy)_454863575089328167:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:Date (copy)_476748244939202591:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[usr:Index (copy)_942378240710500358:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[yr:DCO_RUN_DATE:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{27185F1F-CBC0-499F-894A-82D6F43F4974}' />
    </window>
    <window class='worksheet' hidden='true' name='1.2 - Suggestion Candidate Summary by Factor'>
      <cards>
        <edge name='left'>
          <strip size='200'>
            <card type='pages' />
            <card size='7' type='filters' />
            <card type='marks' />
            <card size='2' type='measures' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='fit-width' />
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1468455010902638592:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_465630037247307799:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Channel (copy)_544865224190140424:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DAY_CNT:qk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DRIVER_TYPE:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DS_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Driver/Type (copy)_544865224190218249:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME (VW_DIM_FACTOR_DCO):nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_UID:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Factor (copy)_544865224189366278:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT):nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Product (copy)_544865224189894663:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Product Flag (copy)_1468455011170881539:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:REP_TEAM_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:RPT_FACTOR_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SCENARIO_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SCENARIO_VER:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:USER_NAME:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{85FA20EF-731A-461F-A81B-46DA3B2D6BD8}' />
    </window>
    <window class='worksheet' hidden='true' name='1.3 - Suggestion Candidates'>
      <cards>
        <edge name='left'>
          <strip size='200'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='24'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card mode='compact' param='[Parameters].[Parameter 3]' type='parameter' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='fit-width' />
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[:Measure Names]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[max:DCO_REASON_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:ACCOUNT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:CHANNEL_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1468455010902638592:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_1716434419576819713:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Calculation_465630037247307799:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DAY_CNT:qk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_REASON_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DCO_REASON_TYPE_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DRIVER_TYPE:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:DS_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Date (copy)_986006877293236225:qk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME (VW_DIM_FACTOR_DCO):nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:FACTOR_UID:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:IS_PUBLISHED (VW_DIM_SCENARIO_RPT):nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT):nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_CHANNEL_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT):nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:MESSAGE_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:PRODUCT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:Product Flag (copy)_1468455011170881539:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:REP_TEAM_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:RPT_FACTOR_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SCENARIO_VER:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SUGGESTED_DATE:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:SUGGESTION_REFERENCE_ID:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1cer0jp0me64jo13u9r6m0pnudoh].[none:USER_NAME:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{7F7BF402-07EE-456F-8F6D-FE8B16955928}' />
    </window>
  </windows>
</workbook>
