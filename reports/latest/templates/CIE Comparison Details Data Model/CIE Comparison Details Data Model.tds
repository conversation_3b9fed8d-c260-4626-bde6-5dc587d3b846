<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20231.23.0310.1045                               -->
<datasource formatted-name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model' inline='true' source-platform='linux' version='18.1' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location id='${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonDetailsDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.0' site='${TABLEAU_SITE}' />
  <connection class='federated'>
    <named-connections>
      <named-connection caption='${SNOW_SERVER}' name='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.1p6alz40rrmfq815bmjr10slcx00'>
        <connection authentication='Username Password' class='snowflake' dbname='${SNOW_DBNAME}' odbc-connect-string-extras='' one-time-sql='' schema='DCO' server='${SNOW_SERVER}' server-oauth='' service='${SNOW_ROLE}' username='${SNOW_USERNAME}' warehouse='${SNOW_WAREHOUSE}' workgroup-auth-mode='prompt' />
      </named-connection>
    </named-connections>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.1p6alz40rrmfq815bmjr10slcx00' name='Custom SQL Query' type='text'>SELECT c.SE_CONFIG_NAME,  DCO_RUN_DATE, n.SCENARIO_NAME, &apos;DCO 3&apos; flag, 
USER_NAME REP_TEAM_NAME, ACCOUNT_NAME, CHANNEL_NAME channel, date(suggested_date) SUGGESTED_DATE,  
PRODUCT_NAME, FACTOR_NAME, SUGGESTIONCOUNT, HCP_SEGMENT, n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_SUGGESTION_COUNT_DETAIL_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT  c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID
LEFT JOIN DCO.VW_DIM_USER_DSE_DCO_RPT r on r.user_uid =s.REP_UID
LEFT join DCO.VW_DIM_ACCOUNT_DCO_RPT a on a.ACCOUNT_UID =s.ACCOUNT_UID
LEFT join DCO.VW_DIM_PRODUCT_DCO_RPT p on p.PRODUCT_UID =s.PRODUCT_UID
LEFT join DCO.VW_DIM_FACTOR_DCO f on f.FACTOR_UID =s.FACTOR_UID
LEFT join DCO.VW_DIM_CHANNEL_DCO_RPT h on h.CHANNEL_UID =s.CHANNEL

UNION ALL 

SELECT c.SE_CONFIG_NAME,  DCO_RUN_DATE, n.SCENARIO_NAME, &apos;DCO 3 2&apos; flag, 
USER_NAME REP_TEAM_NAME, ACCOUNT_NAME, CHANNEL_NAME channel, date(suggested_date) SUGGESTED_DATE, 
PRODUCT_NAME, FACTOR_NAME, SUGGESTIONCOUNT, HCP_SEGMENT,n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_SUGGESTION_COUNT_DETAIL_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID
LEFT JOIN DCO.VW_DIM_USER_DSE_DCO_RPT r on r.user_uid =s.REP_UID
LEFT join DCO.VW_DIM_ACCOUNT_DCO_RPT a on a.ACCOUNT_UID =s.ACCOUNT_UID
LEFT join DCO.VW_DIM_PRODUCT_DCO_RPT p on p.PRODUCT_UID =s.PRODUCT_UID
LEFT join DCO.VW_DIM_FACTOR_DCO f on f.FACTOR_UID =s.FACTOR_UID
LEFT join DCO.VW_DIM_CHANNEL_DCO_RPT h on h.CHANNEL_UID =s.CHANNEL</_.fcp.ObjectModelEncapsulateLegacy.false...relation>
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.1p6alz40rrmfq815bmjr10slcx00' name='Custom SQL Query' type='text'>SELECT c.SE_CONFIG_NAME,  DCO_RUN_DATE, n.SCENARIO_NAME, &apos;DCO 3&apos; flag, 
USER_NAME REP_TEAM_NAME, ACCOUNT_NAME, CHANNEL_NAME channel, date(suggested_date) SUGGESTED_DATE,  
PRODUCT_NAME, FACTOR_NAME, SUGGESTIONCOUNT, HCP_SEGMENT, n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_SUGGESTION_COUNT_DETAIL_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT  c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID
LEFT JOIN DCO.VW_DIM_USER_DSE_DCO_RPT r on r.user_uid =s.REP_UID
LEFT join DCO.VW_DIM_ACCOUNT_DCO_RPT a on a.ACCOUNT_UID =s.ACCOUNT_UID
LEFT join DCO.VW_DIM_PRODUCT_DCO_RPT p on p.PRODUCT_UID =s.PRODUCT_UID
LEFT join DCO.VW_DIM_FACTOR_DCO f on f.FACTOR_UID =s.FACTOR_UID
LEFT join DCO.VW_DIM_CHANNEL_DCO_RPT h on h.CHANNEL_UID =s.CHANNEL

UNION ALL 

SELECT c.SE_CONFIG_NAME,  DCO_RUN_DATE, n.SCENARIO_NAME, &apos;DCO 3 2&apos; flag, 
USER_NAME REP_TEAM_NAME, ACCOUNT_NAME, CHANNEL_NAME channel, date(suggested_date) SUGGESTED_DATE, 
PRODUCT_NAME, FACTOR_NAME, SUGGESTIONCOUNT, HCP_SEGMENT,n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_SUGGESTION_COUNT_DETAIL_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID
LEFT JOIN DCO.VW_DIM_USER_DSE_DCO_RPT r on r.user_uid =s.REP_UID
LEFT join DCO.VW_DIM_ACCOUNT_DCO_RPT a on a.ACCOUNT_UID =s.ACCOUNT_UID
LEFT join DCO.VW_DIM_PRODUCT_DCO_RPT p on p.PRODUCT_UID =s.PRODUCT_UID
LEFT join DCO.VW_DIM_FACTOR_DCO f on f.FACTOR_UID =s.FACTOR_UID
LEFT join DCO.VW_DIM_CHANNEL_DCO_RPT h on h.CHANNEL_UID =s.CHANNEL</_.fcp.ObjectModelEncapsulateLegacy.true...relation>
    <refresh increment-key='' incremental-updates='false' />
    <metadata-records>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SE_CONFIG_NAME]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SE_CONFIG_NAME</remote-alias>
        <ordinal>1</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_RUN_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DCO_RUN_DATE]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>DCO_RUN_DATE</remote-alias>
        <ordinal>2</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_NAME]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SCENARIO_NAME</remote-alias>
        <ordinal>3</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>100</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>FLAG</remote-name>
        <remote-type>129</remote-type>
        <local-name>[FLAG]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>FLAG</remote-alias>
        <ordinal>4</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>7</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>REP_TEAM_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[REP_TEAM_NAME]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>REP_TEAM_NAME</remote-alias>
        <ordinal>5</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>ACCOUNT_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[ACCOUNT_NAME]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>ACCOUNT_NAME</remote-alias>
        <ordinal>6</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CHANNEL</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CHANNEL]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>CHANNEL</remote-alias>
        <ordinal>7</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SUGGESTED_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[SUGGESTED_DATE]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SUGGESTED_DATE</remote-alias>
        <ordinal>8</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_NAME]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>PRODUCT_NAME</remote-alias>
        <ordinal>9</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>FACTOR_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[FACTOR_NAME]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>FACTOR_NAME</remote-alias>
        <ordinal>10</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SUGGESTIONCOUNT</remote-name>
        <remote-type>131</remote-type>
        <local-name>[SUGGESTIONCOUNT]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SUGGESTIONCOUNT</remote-alias>
        <ordinal>11</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>18</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>HCP_SEGMENT</remote-name>
        <remote-type>129</remote-type>
        <local-name>[HCP_SEGMENT]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>HCP_SEGMENT</remote-alias>
        <ordinal>12</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>LAST_DCO_RUN_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[LAST_DCO_RUN_DATE]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
        <ordinal>13</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
    </metadata-records>
  </connection>
  <aliases enabled='yes' />
  <column datatype='string' name='[:Measure Names]' role='dimension' type='nominal'>
    <aliases>
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[sum:accountChannelCount1:qk]&quot;' value='Suggested Account/Channel Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[sum:accountCount1:qk]&quot;' value='Suggested Account Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[sum:factorCount1:qk]&quot;' value='Factors fired Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[sum:repCount1:qk]&quot;' value='Suggested Rep Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[sum:suggestionCount1:qk]&quot;' value='Suggestion Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[usr:Count (copy 2)_981221801228656666:qk]&quot;' value='Source 2' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[usr:Count (copy)_981221801228464153:qk]&quot;' value='Source 1 ' />
    </aliases>
  </column>
  <column caption='ACCOUNT' datatype='string' name='[ACCOUNT_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[ACCOUNT_UID]' role='dimension' type='nominal' />
  <column caption='Account Filter 2' datatype='string' name='[Account (copy)_512495611506294790]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [ACCOUNT_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
  </column>
  <column caption='Segment Filter 1' datatype='string' name='[Account Filter (copy)_981221801042362389]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [HCP_SEGMENT]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
  </column>
  <column caption='Suggested Date Filter 1' datatype='date' name='[Account combo (copy)_981221801282629672]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SUGGESTED_DATE] &#13;&#10;    END' />
  </column>
  <column caption='Suggested Date level' datatype='date' name='[Account level (copy)_837106628427710466]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403021361155] = &apos;DCO 3 2&apos;&#13;&#10;THEN [SUGGESTED_DATE] &#13;&#10;END' />
  </column>
  <column datatype='string' hidden='true' name='[CHANNELID]' role='dimension' type='nominal' />
  <column caption='Level 1' datatype='string' name='[Calculation_526921184320745495]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
  </column>
  <column caption='Factor level' datatype='string' name='[Calculation_837106628427227136]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403021361155] = &apos;DCO 3 2&apos;&#13;&#10;THEN [FACTOR_NAME] &#13;&#10;END' />
  </column>
  <column caption='Channel Filter 1' datatype='string' name='[Calculation_981221801239351324]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [CHANNEL] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
  </column>
  <column caption='Filters non matched' datatype='boolean' name='[Calculation_981221801308008490]' role='measure' type='nominal'>
    <calculation class='tableau' formula='ISNULL([Count (copy)_981221801228464153]) AND ISNULL([Count (copy 2)_981221801228656666])' />
  </column>
  <column caption='Channel Filter 2' datatype='string' name='[Channel (copy)_512495611506069509]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [CHANNEL]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
  </column>
  <column caption='Product Filter 1' datatype='string' name='[Channel combo (copy)_981221801243471901]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [PRODUCT_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
  </column>
  <column caption='Account Filter 1' datatype='string' name='[Channel combo (copy)_981221801280938018]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [ACCOUNT_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
  </column>
  <column caption='Config Filter 2' datatype='string' name='[Config (copy)_512495611509768203]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [SE_CONFIG_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
  </column>
  <column caption='Scenario Filter 1' datatype='string' name='[Config (copy)_981221801310863404]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
  </column>
  <column caption='Run Date Filter 1' datatype='date' name='[Config (copy)_981221801311191085]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [DCO_RUN_DATE]&#13;&#10;    END' />
  </column>
  <column caption='Count Source 2' datatype='integer' name='[Count (copy 2)_981221801228656666]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403021361155] THEN ([SUGGESTIONCOUNT]) END)' />
  </column>
  <column caption='Count Source 1' datatype='integer' name='[Count (copy)_981221801228464153]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)' />
  </column>
  <column caption='RUNDATE' datatype='date' name='[DCO_RUN_DATE]' role='dimension' type='ordinal' />
  <column caption='FACTOR' datatype='string' name='[FACTOR_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[FACTOR_UID]' role='dimension' type='nominal' />
  <column caption='Factor Filter 2' datatype='string' name='[Factor (copy)_512495611506483207]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [FACTOR_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
  </column>
  <column caption='Account level' datatype='string' name='[Factor level (copy)_837106628427595777]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403021361155] = &apos;DCO 3 2&apos;&#13;&#10;THEN [ACCOUNT_NAME] &#13;&#10;END' />
  </column>
  <column caption='Product level' datatype='string' name='[Factor level (copy)_837106628427808771]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403021361155] = &apos;DCO 3 2&apos;&#13;&#10;THEN [PRODUCT_NAME] &#13;&#10;END' />
  </column>
  <column caption='Rep level ' datatype='string' name='[Factor level (copy)_837106628428029957]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403021361155] = &apos;DCO 3 2&apos;&#13;&#10;THEN [REP_TEAM_NAME] &#13;&#10;END' />
  </column>
  <column caption='Segment level' datatype='string' name='[Factor level (copy)_837106628428517382]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403021361155] = &apos;DCO 3 2&apos;&#13;&#10;THEN [HCP_SEGMENT]&#13;&#10;END' />
  </column>
  <column caption='Level 2' datatype='string' name='[Level 1 (copy)_526921184321654809]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 1 (copy)_526921184321613848]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
  </column>
  <column caption='Level 4' datatype='string' name='[Level 2 (copy) (copy)_526921184322039839]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 3 (copy)_526921184321929243]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
  </column>
  <column caption='Level 3' datatype='string' name='[Level 2 (copy)_526921184322031646]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 2 (copy)_526921184321875994]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
  </column>
  <column caption='Level 6' datatype='string' name='[Level 4 (copy) (copy)_526921184322179105]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 5 (copy)_526921184321998877]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
  </column>
  <column caption='Level 5' datatype='string' name='[Level 4 (copy)_526921184322166816]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 3 (copy) (copy)_526921184321937436]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
  </column>
  <column caption='Level 7' datatype='string' name='[Level 6 (copy)_2155535403012546561]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 6 (copy)_2155535403012243456]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
  </column>
  <column caption='PRODUCT' datatype='string' name='[PRODUCT_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[PRODUCT_UID]' role='dimension' type='nominal' />
  <column caption='Product Filter 2' datatype='string' name='[Product (copy)_512495611509055496]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [PRODUCT_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
  </column>
  <column caption='Factor Filter 1' datatype='string' name='[Product combo  (copy)_981221801281835044]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [FACTOR_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
  </column>
  <column caption='Rep Filter 1' datatype='string' name='[Product combo  (copy)_981221801282273318]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [REP_TEAM_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
  </column>
  <column caption='REP' datatype='string' name='[REP_TEAM_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[REP_UID]' role='dimension' type='nominal' />
  <column caption='Rep Filter 2' datatype='string' name='[Rep (copy)_512495611509235721]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [REP_TEAM_NAME]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
  </column>
  <column caption='Run Date Filter 2' datatype='date' name='[Run Date  (copy)_512495611510464526]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [DCO_RUN_DATE]&#13;&#10;    END' />
  </column>
  <column caption='SCENARIO NEW' datatype='string' name='[SCENARIO (copy)_1347983701206884354]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
  </column>
  <column caption='SCENARIO' datatype='string' name='[SCENARIO_NAME]' role='dimension' type='nominal' />
  <column datatype='integer' hidden='true' name='[SE_CONFIG_ID]' role='measure' type='quantitative' />
  <column caption='CONFIG' datatype='string' name='[SE_CONFIG_NAME]' role='dimension' type='nominal' />
  <column caption='SuggestedDATE' datatype='date' name='[SUGGESTED_DATE]' role='dimension' type='ordinal' />
  <column caption='Scenario Filter 2' datatype='string' name='[Scenario (copy)_512495611510235149]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
  </column>
  <column caption='Segment Filter 2' datatype='string' name='[Segment (copy)_512495611509411850]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [HCP_SEGMENT]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
  </column>
  <column caption='Channel level' datatype='string' name='[Segment level (copy)_837106628427919364]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403021361155] = &apos;DCO 3 2&apos;&#13;&#10;THEN [CHANNEL] &#13;&#10;END' />
  </column>
  <column caption='Suggested Date Filter 2' datatype='date' name='[Suggested Date (copy)_512495611509981196]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [SUGGESTED_DATE] &#13;&#10;    END' />
  </column>
  <column caption='Config Filter 1' datatype='string' name='[Suggested Date (copy)_981221801310244907]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
  </column>
  <_.fcp.ObjectModelTableType.true...column caption='Snowflake' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[_3A533CA42284488BB56AA31CE554622E]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='MySQL' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[_3CEE7E99CC7146BA96A53F15A77B5338]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='Snowflake Details' datatype='table' name='[__tableau_internal_object_id__].[_CFFC621BD5F74AC1A47F5DA01AE0557A]' role='measure' type='quantitative' />
  <column datatype='string' hidden='true' name='[accountId]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[channelId]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[factorUID]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[productUID]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[repUID]' role='dimension' type='nominal' />
  <column datatype='integer' hidden='true' name='[seConfigId]' role='measure' type='quantitative' />
  <column-instance column='[Count (copy 2)_981221801228656666]' derivation='User' name='[usr:Count (copy 2)_981221801228656666:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Count (copy)_981221801228464153]' derivation='User' name='[usr:Count (copy)_981221801228464153:qk]' pivot='key' type='quantitative' />
  <drill-paths>
    <drill-path name='Levels'>
      <field>[Calculation_526921184320745495]</field>
      <field>[Level 1 (copy)_526921184321654809]</field>
      <field>[Level 2 (copy)_526921184322031646]</field>
      <field>[Level 2 (copy) (copy)_526921184322039839]</field>
      <field>[Level 4 (copy)_526921184322166816]</field>
      <field>[Level 4 (copy) (copy)_526921184322179105]</field>
      <field>[Level 6 (copy)_2155535403012546561]</field>
    </drill-path>
  </drill-paths>
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.706977' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.293023' show-aliased-fields='true' show-hidden-fields='true' show-structure='true' />
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <datasource-dependencies datasource='Parameters'>
    <column alias='Engine output' caption='Source 1' datatype='string' name='[Choose data sources (copy)_2155535403020820482]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3&quot;'>
      <calculation class='tableau' formula='&quot;DCO 3&quot;' />
      <aliases>
        <alias key='&quot;DCO 3&quot;' value='Engine output' />
        <alias key='&quot;DCO 4&quot;' value='Post-proc output' />
        <alias key='&quot;DSE 1&quot;' value='Traditional' />
        <alias key='&quot;DSE 2&quot;' value='Next Gen' />
      </aliases>
      <members>
        <member alias='Traditional' value='&quot;DSE 1&quot;' />
        <member alias='Next Gen' value='&quot;DSE 2&quot;' />
        <member alias='Engine output' value='&quot;DCO 3&quot;' />
        <member alias='Post-proc output' value='&quot;DCO 4&quot;' />
      </members>
    </column>
    <column caption='Level 2' datatype='string' name='[Level 1 (copy)_526921184321613848]' param-domain-type='list' role='measure' type='nominal' value='&quot;Segment&quot;'>
      <calculation class='tableau' formula='&quot;Segment&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 3' datatype='string' name='[Level 2 (copy)_526921184321875994]' param-domain-type='list' role='measure' type='nominal' value='&quot;Product&quot;'>
      <calculation class='tableau' formula='&quot;Product&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 5' datatype='string' name='[Level 3 (copy) (copy)_526921184321937436]' param-domain-type='list' role='measure' type='nominal' value='&quot;Rep&quot;'>
      <calculation class='tableau' formula='&quot;Rep&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 4' datatype='string' name='[Level 3 (copy)_526921184321929243]' param-domain-type='list' role='measure' type='nominal' value='&quot;Factor&quot;'>
      <calculation class='tableau' formula='&quot;Factor&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 6' datatype='string' name='[Level 5 (copy)_526921184321998877]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account&quot;'>
      <calculation class='tableau' formula='&quot;Account&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 7' datatype='string' name='[Level 6 (copy)_2155535403012243456]' param-domain-type='list' role='measure' type='nominal' value='&quot;Suggested Date&quot;'>
      <calculation class='tableau' formula='&quot;Suggested Date&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 1' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
      <calculation class='tableau' formula='&quot;Channel&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column alias='Engine output' caption='Source 2' datatype='string' name='[Source 1 (copy)_2155535403021361155]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3 2&quot;'>
      <calculation class='tableau' formula='&quot;DCO 3 2&quot;' />
      <aliases>
        <alias key='&quot;DCO 3 2&quot;' value='Engine output' />
        <alias key='&quot;DCO 4 2&quot;' value='Post-proc output' />
        <alias key='&quot;DSE 1 2&quot;' value='Traditional' />
        <alias key='&quot;DSE 2 2&quot;' value='Next Gen' />
      </aliases>
      <members>
        <member alias='Traditional' value='&quot;DSE 1 2&quot;' />
        <member alias='Next Gen' value='&quot;DSE 2 2&quot;' />
        <member alias='Engine output' value='&quot;DCO 3 2&quot;' />
        <member alias='Post-proc output' value='&quot;DCO 4 2&quot;' />
      </members>
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='Snowflake Details' id='_CFFC621BD5F74AC1A47F5DA01AE0557A'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.1p6alz40rrmfq815bmjr10slcx00' name='Custom SQL Query' type='text'>SELECT c.SE_CONFIG_NAME,  DCO_RUN_DATE, n.SCENARIO_NAME, &apos;DCO 3&apos; flag, 
USER_NAME REP_TEAM_NAME, ACCOUNT_NAME, CHANNEL_NAME channel, date(suggested_date) SUGGESTED_DATE,  
PRODUCT_NAME, FACTOR_NAME, SUGGESTIONCOUNT, HCP_SEGMENT, n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_SUGGESTION_COUNT_DETAIL_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT  c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID
LEFT JOIN DCO.VW_DIM_USER_DSE_DCO_RPT r on r.user_uid =s.REP_UID
LEFT join DCO.VW_DIM_ACCOUNT_DCO_RPT a on a.ACCOUNT_UID =s.ACCOUNT_UID
LEFT join DCO.VW_DIM_PRODUCT_DCO_RPT p on p.PRODUCT_UID =s.PRODUCT_UID
LEFT join DCO.VW_DIM_FACTOR_DCO f on f.FACTOR_UID =s.FACTOR_UID
LEFT join DCO.VW_DIM_CHANNEL_DCO_RPT h on h.CHANNEL_UID =s.CHANNEL

UNION ALL 

SELECT c.SE_CONFIG_NAME,  DCO_RUN_DATE, n.SCENARIO_NAME, &apos;DCO 3 2&apos; flag, 
USER_NAME REP_TEAM_NAME, ACCOUNT_NAME, CHANNEL_NAME channel, date(suggested_date) SUGGESTED_DATE, 
PRODUCT_NAME, FACTOR_NAME, SUGGESTIONCOUNT, HCP_SEGMENT,n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_SUGGESTION_COUNT_DETAIL_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID
LEFT JOIN DCO.VW_DIM_USER_DSE_DCO_RPT r on r.user_uid =s.REP_UID
LEFT join DCO.VW_DIM_ACCOUNT_DCO_RPT a on a.ACCOUNT_UID =s.ACCOUNT_UID
LEFT join DCO.VW_DIM_PRODUCT_DCO_RPT p on p.PRODUCT_UID =s.PRODUCT_UID
LEFT join DCO.VW_DIM_FACTOR_DCO f on f.FACTOR_UID =s.FACTOR_UID
LEFT join DCO.VW_DIM_CHANNEL_DCO_RPT h on h.CHANNEL_UID =s.CHANNEL</relation>
        </properties>
      </object>
    </objects>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
