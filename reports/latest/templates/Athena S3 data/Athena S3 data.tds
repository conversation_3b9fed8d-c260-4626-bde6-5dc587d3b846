<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20231.23.0404.1109                               -->
<datasource formatted-name='Athena S3 data' inline='true' source-platform='mac' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <_.fcp.VConnDownstreamExtractsWithWarnings.true...VConnDownstreamExtractsWithWarnings />
  </document-format-change-manifest>
  <repository-location id='AthenaS3data' path='/t/${TABLEAU_SITE}/datasources' revision='1.0' site='${TABLEAU_SITE}' />
  <connection class='federated'>
    <named-connections>
      <named-connection caption='athena.${AWS_REGION}.amazonaws.com' name='athena.0oqf5ed1bzmnov14mbnf60xygwoc'>
        <connection class='athena' dbname='AwsDataCatalog' one-time-sql='' port='443' schema='impact_${CUSTOMER_NAME}_${ENVIRONMENT}' server='athena.${AWS_REGION}.amazonaws.com' server-oauth='' username='AKIAU2ZWYTBAWWRYIEXW' warehouse='s3://aktana-bdp${REGION}-glue/dbt/${ENVIRONMENT}/${CUSTOMER_NAME}' workgroup-auth-mode='prompt'>
          <connection-customization class='athena' enabled='false' version='18.1'>
            <vendor name='athena' />
            <driver name='athena' />
            <customizations>
              <customization name='CAP_AUTH_DB_IMPERSONATE' value='no' />
              <customization name='CAP_AUTH_KERBEROS_IMPERSONATE' value='no' />
              <customization name='CAP_COLLECT_TABLE_STATISTICS' value='no' />
              <customization name='CAP_CONNECT_STORED_PROCEDURE' value='no' />
              <customization name='CAP_CREATE_TEMP_TABLES' value='no' />
              <customization name='CAP_DATASERVER_MAGIC_NUMBER' value='no' />
              <customization name='CAP_DEFERS_CONNECTION_VERIFICATION' value='no' />
              <customization name='CAP_DISABLE_EXTRACT_TABLE_INDEXING' value='no' />
              <customization name='CAP_EQUALITY_JOINS_ONLY' value='no' />
              <customization name='CAP_EQUIJOINS_ONLY' value='no' />
              <customization name='CAP_FAST_METADATA' value='yes' />
              <customization name='CAP_FORCE_COUNT_FOR_NUMBEROFRECORDS' value='no' />
              <customization name='CAP_GREENPLUM_TRUST_METADATA_CONTAINSNULL' value='no' />
              <customization name='CAP_HIVE_FIX_METADATA_NAMES' value='no' />
              <customization name='CAP_INDEX_TEMP_TABLES' value='no' />
              <customization name='CAP_INSERT_TEMP_EXEC_STORED_PROCEDURE' value='no' />
              <customization name='CAP_JDBC_QUERY_USE_PREPARE_PARAMETER_MARKER' value='no' />
              <customization name='CAP_LOCAL_ALIASES_CASE_INSENSITIVE' value='no' />
              <customization name='CAP_ODBC_ALWAYS_THROW_CONNECT_ERRORS' value='no' />
              <customization name='CAP_ODBC_BIND_FORCE_DATETIME_AS_CHAR' value='no' />
              <customization name='CAP_ODBC_BIND_FORCE_DATE_AS_CHAR' value='no' />
              <customization name='CAP_ODBC_BIND_FORCE_MAX_STRING_BUFFERS' value='no' />
              <customization name='CAP_ODBC_BIND_FORCE_MEDIUM_STRING_BUFFERS' value='no' />
              <customization name='CAP_ODBC_BIND_FORCE_SIGNED' value='no' />
              <customization name='CAP_ODBC_BIND_FORCE_SMALL_STRING_BUFFERS' value='no' />
              <customization name='CAP_ODBC_BIND_SUPPRESS_INT64' value='no' />
              <customization name='CAP_ODBC_BIND_SUPPRESS_PREFERRED_TYPES' value='no' />
              <customization name='CAP_ODBC_BIND_SUPPRESS_WIDE_CHAR' value='no' />
              <customization name='CAP_ODBC_CURSOR_DYNAMIC' value='no' />
              <customization name='CAP_ODBC_CURSOR_FORWARD_ONLY' value='no' />
              <customization name='CAP_ODBC_CURSOR_KEYSET_DRIVEN' value='no' />
              <customization name='CAP_ODBC_CURSOR_STATIC' value='no' />
              <customization name='CAP_ODBC_DRIVER_HIVE_ASSUME_LATEST' value='no' />
              <customization name='CAP_ODBC_ERROR_IGNORE_FALSE_ALARM' value='no' />
              <customization name='CAP_ODBC_FETCH_BUFFERS_RESIZABLE' value='no' />
              <customization name='CAP_ODBC_FETCH_BUFFERS_SIZE_MASSIVE' value='no' />
              <customization name='CAP_ODBC_FETCH_CONTINUE_ON_ERROR' value='no' />
              <customization name='CAP_ODBC_FORCE_SINGLE_ROW_BINDING' value='no' />
              <customization name='CAP_ODBC_METADATA_STRING_LENGTH_UNKNOWN' value='no' />
              <customization name='CAP_ODBC_METADATA_SUPPRESS_SQLCOLUMNS_API' value='no' />
              <customization name='CAP_ODBC_METADATA_SUPPRESS_SQLFOREIGNKEYS_API' value='no' />
              <customization name='CAP_ODBC_METADATA_SUPPRESS_SQLPRIMARYKEYS_API' value='no' />
              <customization name='CAP_ODBC_METADATA_SUPPRESS_SQLSTATISTICS_API' value='no' />
              <customization name='CAP_ODBC_QUERY_USE_PREPARE_PARAMETER_MARKER' value='no' />
              <customization name='CAP_ODBC_REBIND_SKIP_UNBIND' value='no' />
              <customization name='CAP_ODBC_TRIM_VARCHAR_PADDING' value='no' />
              <customization name='CAP_ODBC_UNBIND_AUTO' value='no' />
              <customization name='CAP_ODBC_UNBIND_BATCH' value='no' />
              <customization name='CAP_ODBC_UNBIND_EACH' value='no' />
              <customization name='CAP_ODBC_USE_NATIVE_PROTOCOL' value='no' />
              <customization name='CAP_PADDED_SEMANTICS_NCHAR_NVARCHAR' value='no' />
              <customization name='CAP_PERSIST_SQL_RELATION_ON_LEAF_CONNECTION' value='no' />
              <customization name='CAP_QUERY_ALLOW_JOIN_REORDER' value='yes' />
              <customization name='CAP_QUERY_ALLOW_PARTIAL_AGGREGATION' value='yes' />
              <customization name='CAP_QUERY_ALWAYS_USE_AQ_CACHE' value='no' />
              <customization name='CAP_QUERY_AVOID_EXPRESSION_INLINING' value='no' />
              <customization name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES' value='no' />
              <customization name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES' value='yes' />
              <customization name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES' value='yes' />
              <customization name='CAP_QUERY_BOOLEXPR_TO_INTEXPR' value='no' />
              <customization name='CAP_QUERY_BOOL_IDENTIFIER_TO_LOGICAL' value='no' />
              <customization name='CAP_QUERY_CAST_MONEY_AS_NUMERIC' value='no' />
              <customization name='CAP_QUERY_EMPTY_DOMAIN_TOP' value='no' />
              <customization name='CAP_QUERY_FORCE_AGGREGATE_MEASURES' value='no' />
              <customization name='CAP_QUERY_GROUP_BY_ALIAS' value='no' />
              <customization name='CAP_QUERY_GROUP_BY_DEGREE' value='no' />
              <customization name='CAP_QUERY_HAVING_REQUIRES_GROUP_BY' value='no' />
              <customization name='CAP_QUERY_HAVING_UNSUPPORTED' value='no' />
              <customization name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL' value='no' />
              <customization name='CAP_QUERY_IGNORE_HINT_MAKE_DOMAIN_PREDICATE' value='no' />
              <customization name='CAP_QUERY_JOIN_ACROSS_SCHEMAS' value='yes' />
              <customization name='CAP_QUERY_JOIN_ASSUME_CONSTRAINED' value='no' />
              <customization name='CAP_QUERY_JOIN_PUSH_DOWN_CONDITION_EXPRESSIONS' value='no' />
              <customization name='CAP_QUERY_JOIN_REQUIRES_SCOPE' value='no' />
              <customization name='CAP_QUERY_JOIN_REQUIRES_SUBQUERY' value='no' />
              <customization name='CAP_QUERY_MINMAX_FORCE_GROUPBYS' value='no' />
              <customization name='CAP_QUERY_NULL_REQUIRES_CAST' value='yes' />
              <customization name='CAP_QUERY_OUTER_JOIN_CONDITION_NO_TRIVIAL' value='no' />
              <customization name='CAP_QUERY_RECOMPILE_FAILED_QUERY' value='no' />
              <customization name='CAP_QUERY_SELECT_ALIASES_SORTED' value='no' />
              <customization name='CAP_QUERY_SORT_BY' value='yes' />
              <customization name='CAP_QUERY_SORT_BY_DEGREE' value='yes' />
              <customization name='CAP_QUERY_SUBQUERIES' value='yes' />
              <customization name='CAP_QUERY_SUBQUERIES_WITH_TOP' value='yes' />
              <customization name='CAP_QUERY_SUBQUERIES_WITH_TOP_ALTERNATIVE' value='no' />
              <customization name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT' value='yes' />
              <customization name='CAP_QUERY_SUPPORTS_LODJOINS' value='yes' />
              <customization name='CAP_QUERY_SUPPORT_ANALYTIC_FUNCTIONS' value='no' />
              <customization name='CAP_QUERY_SUPPORT_EMPTY_GROUPBY' value='no' />
              <customization name='CAP_QUERY_SUPPRESS_CHECK_DOMAIN_LITERALS_THRESHOLD' value='no' />
              <customization name='CAP_QUERY_SUPPRESS_NULL_CHECK_QUERIES' value='no' />
              <customization name='CAP_QUERY_TAGS_IN_SQL_TEXT' value='no' />
              <customization name='CAP_QUERY_TIME_REQUIRES_CAST' value='no' />
              <customization name='CAP_QUERY_TOPSTYLE_LIMIT' value='no' />
              <customization name='CAP_QUERY_TOPSTYLE_ROWNUM' value='no' />
              <customization name='CAP_QUERY_TOPSTYLE_TOP' value='no' />
              <customization name='CAP_QUERY_TOP_0_METADATA' value='yes' />
              <customization name='CAP_QUERY_TOP_N' value='yes' />
              <customization name='CAP_QUERY_USE_DOMAIN_EXCEPT_OPTIMIZATION' value='yes' />
              <customization name='CAP_QUERY_USE_DOMAIN_RANGES_OPTIMIZATION' value='yes' />
              <customization name='CAP_QUERY_USE_DOMAIN_RANGES_OPTIMIZATION_STRINGS' value='no' />
              <customization name='CAP_QUERY_USE_QUERY_FUSION' value='yes' />
              <customization name='CAP_QUERY_WHERE_FALSE_METADATA' value='no' />
              <customization name='CAP_RELDATEFILT_CASTTODATE' value='no' />
              <customization name='CAP_RENAME_TABLE_USE_LEAF_CONNECTION_DIALECT' value='no' />
              <customization name='CAP_SELECT_INTO' value='no' />
              <customization name='CAP_SELECT_TOP_INTO' value='no' />
              <customization name='CAP_STORED_PROCEDURE_PREFER_TEMP_TABLE' value='no' />
              <customization name='CAP_STORED_PROCEDURE_REPAIR_TEMP_TABLE_STRINGS' value='no' />
              <customization name='CAP_STORED_PROCEDURE_TEMP_TABLE_FROM_BUFFER' value='no' />
              <customization name='CAP_STORED_PROCEDURE_TEMP_TABLE_FROM_NEW_PROTOCOL' value='no' />
              <customization name='CAP_SUPPRESS_CONNECTION_POOLING' value='no' />
              <customization name='CAP_SUPPRESS_DISCOVERY_QUERIES' value='no' />
              <customization name='CAP_SUPPRESS_ENUMERATE_SCHEMAS_VIA_SQL' value='no' />
              <customization name='CAP_SUPPRESS_ENUMERATE_TABLES_VIA_SQL' value='no' />
              <customization name='CAP_SUPPRESS_GET_SERVER_TIME' value='yes' />
              <customization name='CAP_SUPPRESS_QUICK_FILTER_ACCELERATION_VIEWS' value='no' />
              <customization name='CAP_SUPPRESS_TEMP_TABLE_CHECKS' value='no' />
            </customizations>
          </connection-customization>
        </connection>
      </named-connection>
    </named-connections>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation connection='athena.0oqf5ed1bzmnov14mbnf60xygwoc' name='Custom SQL Query' type='text'>SELECT *&#13;
FROM &quot;impact_${CUSTOMER_NAME}_${ENVIRONMENT}&quot;.&quot;msrscenario_strategic_coverage_v&quot; &quot;msrscenario_strate&quot;</_.fcp.ObjectModelEncapsulateLegacy.false...relation>
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation connection='athena.0oqf5ed1bzmnov14mbnf60xygwoc' name='Custom SQL Query' type='text'>SELECT *&#13;
FROM &quot;impact_${CUSTOMER_NAME}_${ENVIRONMENT}&quot;.&quot;msrscenario_strategic_coverage_v&quot; &quot;msrscenario_strate&quot;</_.fcp.ObjectModelEncapsulateLegacy.true...relation>
    <metadata-records>
      <metadata-record class='column'>
        <remote-name>msrscenariouid</remote-name>
        <remote-type>129</remote-type>
        <local-name>[msrscenariouid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>msrscenariouid</remote-alias>
        <ordinal>1</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>eventdatetimeutc</remote-name>
        <remote-type>135</remote-type>
        <local-name>[eventdatetimeutc]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>eventdatetimeutc</remote-alias>
        <ordinal>2</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>interactionuid</remote-name>
        <remote-type>129</remote-type>
        <local-name>[interactionuid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>interactionuid</remote-alias>
        <ordinal>3</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>accountuid</remote-name>
        <remote-type>129</remote-type>
        <local-name>[accountuid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>accountuid</remote-alias>
        <ordinal>4</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>productuid</remote-name>
        <remote-type>129</remote-type>
        <local-name>[productuid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>productuid</remote-alias>
        <ordinal>5</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>productname</remote-name>
        <remote-type>129</remote-type>
        <local-name>[productname]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>productname</remote-alias>
        <ordinal>6</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>segmentname</remote-name>
        <remote-type>129</remote-type>
        <local-name>[segmentname]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>segmentname</remote-alias>
        <ordinal>7</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>actoruid</remote-name>
        <remote-type>129</remote-type>
        <local-name>[actoruid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>actoruid</remote-alias>
        <ordinal>8</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>seConfigId</remote-name>
        <remote-type>3</remote-type>
        <local-name>[seConfigId]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>seConfigId</remote-alias>
        <ordinal>9</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>configCountryCode</remote-name>
        <remote-type>129</remote-type>
        <local-name>[configCountryCode]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>configCountryCode</remote-alias>
        <ordinal>10</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>repteamid</remote-name>
        <remote-type>3</remote-type>
        <local-name>[repteamid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>repteamid</remote-alias>
        <ordinal>11</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>repteamname</remote-name>
        <remote-type>129</remote-type>
        <local-name>[repteamname]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>repteamname</remote-alias>
        <ordinal>12</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>repTypeId</remote-name>
        <remote-type>3</remote-type>
        <local-name>[repTypeId]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>repTypeId</remote-alias>
        <ordinal>13</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>repTypeName</remote-name>
        <remote-type>129</remote-type>
        <local-name>[repTypeName]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>repTypeName</remote-alias>
        <ordinal>14</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>eventtypename</remote-name>
        <remote-type>129</remote-type>
        <local-name>[eventtypename]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>eventtypename</remote-alias>
        <ordinal>15</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>factorname</remote-name>
        <remote-type>129</remote-type>
        <local-name>[factorname]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>factorname</remote-alias>
        <ordinal>16</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>usecasename</remote-name>
        <remote-type>129</remote-type>
        <local-name>[usecasename]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>usecasename</remote-alias>
        <ordinal>17</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>suggestiondriver</remote-name>
        <remote-type>129</remote-type>
        <local-name>[suggestiondriver]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>suggestiondriver</remote-alias>
        <ordinal>18</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>internalsuggestionreferenceid</remote-name>
        <remote-type>129</remote-type>
        <local-name>[internalsuggestionreferenceid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>internalsuggestionreferenceid</remote-alias>
        <ordinal>19</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>interaction_channelid</remote-name>
        <remote-type>3</remote-type>
        <local-name>[interaction_channelid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>interaction_channelid</remote-alias>
        <ordinal>20</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>interaction_channelname</remote-name>
        <remote-type>129</remote-type>
        <local-name>[interaction_channelname]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>interaction_channelname</remote-alias>
        <ordinal>21</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>suggestion_channelid</remote-name>
        <remote-type>3</remote-type>
        <local-name>[suggestion_channelid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>suggestion_channelid</remote-alias>
        <ordinal>22</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>suggestion_channelname</remote-name>
        <remote-type>129</remote-type>
        <local-name>[suggestion_channelname]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>suggestion_channelname</remote-alias>
        <ordinal>23</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>actiontaken</remote-name>
        <remote-type>129</remote-type>
        <local-name>[actiontaken]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>actiontaken</remote-alias>
        <ordinal>24</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>issuggestioncompleteddirect</remote-name>
        <remote-type>3</remote-type>
        <local-name>[issuggestioncompleteddirect]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>issuggestioncompleteddirect</remote-alias>
        <ordinal>25</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>issuggestioncompletedinfer</remote-name>
        <remote-type>3</remote-type>
        <local-name>[issuggestioncompletedinfer]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>issuggestioncompletedinfer</remote-alias>
        <ordinal>26</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>is_candidate_usecase</remote-name>
        <remote-type>3</remote-type>
        <local-name>[is_candidate_usecase]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>is_candidate_usecase</remote-alias>
        <ordinal>27</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>is_candidate_same_product</remote-name>
        <remote-type>3</remote-type>
        <local-name>[is_candidate_same_product]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>is_candidate_same_product</remote-alias>
        <ordinal>28</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>is_candidate_same_event_type</remote-name>
        <remote-type>3</remote-type>
        <local-name>[is_candidate_same_event_type]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>is_candidate_same_event_type</remote-alias>
        <ordinal>29</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>insight_matchclosenessdays</remote-name>
        <remote-type>20</remote-type>
        <local-name>[insight_matchclosenessdays]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>insight_matchclosenessdays</remote-alias>
        <ordinal>30</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>19</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>insight_viewcount</remote-name>
        <remote-type>20</remote-type>
        <local-name>[insight_viewcount]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>insight_viewcount</remote-alias>
        <ordinal>31</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>19</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>event_order</remote-name>
        <remote-type>20</remote-type>
        <local-name>[event_order]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>event_order</remote-alias>
        <ordinal>32</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>19</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>repActionTypeId</remote-name>
        <remote-type>3</remote-type>
        <local-name>[repActionTypeId]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>repActionTypeId</remote-alias>
        <ordinal>33</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>detailrepactiontypeid</remote-name>
        <remote-type>3</remote-type>
        <local-name>[detailrepactiontypeid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>detailrepactiontypeid</remote-alias>
        <ordinal>34</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>strategicActionCategoryId</remote-name>
        <remote-type>3</remote-type>
        <local-name>[strategicActionCategoryId]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>strategicActionCategoryId</remote-alias>
        <ordinal>35</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>strategicActionCategoryUID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[strategicActionCategoryUID]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>strategicActionCategoryUID</remote-alias>
        <ordinal>36</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>52</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>engagementsegment</remote-name>
        <remote-type>129</remote-type>
        <local-name>[engagementsegment]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>engagementsegment</remote-alias>
        <ordinal>37</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>6</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>engagementsegment_monthly</remote-name>
        <remote-type>129</remote-type>
        <local-name>[engagementsegment_monthly]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>engagementsegment_monthly</remote-alias>
        <ordinal>38</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>engagementsegment_quarterly</remote-name>
        <remote-type>129</remote-type>
        <local-name>[engagementsegment_quarterly]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>engagementsegment_quarterly</remote-alias>
        <ordinal>39</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>6</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>engagementrate_monthly</remote-name>
        <remote-type>5</remote-type>
        <local-name>[engagementrate_monthly]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>engagementrate_monthly</remote-alias>
        <ordinal>40</ordinal>
        <local-type>real</local-type>
        <aggregation>Sum</aggregation>
        <precision>53</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>driven_category_ids</remote-name>
        <remote-type>129</remote-type>
        <local-name>[driven_category_ids]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>driven_category_ids</remote-alias>
        <ordinal>41</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>partially_driven_category_ids</remote-name>
        <remote-type>129</remote-type>
        <local-name>[partially_driven_category_ids]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>partially_driven_category_ids</remote-alias>
        <ordinal>42</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>aligned_category_ids</remote-name>
        <remote-type>129</remote-type>
        <local-name>[aligned_category_ids]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>aligned_category_ids</remote-alias>
        <ordinal>43</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>driven</remote-name>
        <remote-type>3</remote-type>
        <local-name>[driven]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>driven</remote-alias>
        <ordinal>44</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>partially_driven</remote-name>
        <remote-type>3</remote-type>
        <local-name>[partially_driven]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>partially_driven</remote-alias>
        <ordinal>45</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>aligned</remote-name>
        <remote-type>3</remote-type>
        <local-name>[aligned]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>aligned</remote-alias>
        <ordinal>46</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>10</precision>
        <contains-null>true</contains-null>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>strategicCategoryGroupUid</remote-name>
        <remote-type>129</remote-type>
        <local-name>[strategicCategoryGroupUid]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>strategicCategoryGroupUid</remote-alias>
        <ordinal>47</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>1</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>strategicCategoryGroup</remote-name>
        <remote-type>129</remote-type>
        <local-name>[strategicCategoryGroup]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>strategicCategoryGroup</remote-alias>
        <ordinal>48</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>18</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
    </metadata-records>
  </connection>
  <aliases enabled='yes' />
  <column caption='% CNT Driven vs Aligned' datatype='real' name='[% CNT Driven vs Aligned (copy)_2079537134428987429]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='IF [Actual Audience CNT (copy)_2079537134831333454] = 0  THEN -1&#13;&#10;ELSEIF [Actual Driven CNT (copy)_2079537134871355471] =0 THEN 1&#13;&#10;ELSE ([Actual Audience CNT (copy)_2079537134831333454] - [Actual Driven CNT (copy)_2079537134871355471]) /[Actual Driven CNT (copy)_2079537134871355471]&#13;&#10;END' />
  </column>
  <column caption='Aligned Frequency' datatype='real' name='[% Driven Frequency (copy)_2079537134876905554]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='ZN(COUNT( IF [Calculation_1113796484456726534] and [Strategy (copy)_1431300260991889426]=&apos;Aligned by Strategy (Not Driven)&apos; &#13;&#10;THEN 1 END )&#13;&#10;/&#13;&#10;COUNTD( IF [Calculation_1113796484456726534] and [Strategy (copy)_1431300260991889426]=&apos;Aligned by Strategy (Not Driven)&apos; &#13;&#10;THEN [accountuid] END ))' />
  </column>
  <column caption='% SUM PP vs CC (color)' datatype='string' datatype-customized='true' name='[% PP vs CC (copy)_1113796484737118221]' role='measure' type='nominal'>
    <calculation class='tableau' formula='STR(If [Calculation_1113796484736237580] &lt; 0 THEN 1 END)' />
  </column>
  <column datatype='string' name='[:Measure Names]' role='dimension' type='nominal'>
    <aliases>
      <alias key='&quot;[Athena S3 data]&quot;' value='Total Audience Evaluated:' />
      <alias key='&quot;[Athena S3 data].[usr:Calculation_2079537134428155937:qk]&quot;' value='Audience Reached' />
      <alias key='&quot;[Athena S3 data].[usr:Calculation_2079537134428303396:qk]&quot;' value='Activity Frequency' />
    </aliases>
  </column>
  <column caption='Action Taken Calc (color)' datatype='string' name='[Action Taken Calc (copy)_2079537134574747717]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Calculation_1431300260932198411]&#13;&#10;WHEN &apos;Driven by Strategy&apos; THEN &apos;Driven - Activities that are aligned with Strategy and driven by Aktana&apos;&#13;&#10;WHEN &apos;Aligned by Strategy (Not Driven)&apos; THEN &apos;Aligned - Activities that are aligned with Strategy&apos;&#13;&#10;ELSE &apos;Not Aligned - Activities that are not aligned with Strategy&apos;&#13;&#10;END' />
  </column>
  <column caption='CP Driven CNT' datatype='integer' name='[Actual Audience CNT (copy)_2079537134831333454]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNT( IF [Calculation_1113796484456726534] and [Strategy (copy)_1431300260991889426]=&apos;Driven by Strategy&apos; &#13;&#10;THEN 1 END )' />
  </column>
  <column caption='Total Frequency' datatype='real' name='[Actual Driven CNT (copy)_2079537134871355471]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='(ZN([Frequency % (copy)_2079537134874546257])- ZN([% Driven Frequency (copy)_2079537134876905554])) /ZN([% Driven Frequency (copy)_2079537134876905554])' />
  </column>
  <column caption='Aligned and Driven Frequency (copy)' datatype='real' name='[Aligned and Driven Frequency (copy)_89030536**********]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='(COUNT( IF [Calculation_1113796484456726534] &#13;&#10;and [Strategy (copy)_1431300260991889426]IN (&apos;Driven by Strategy&apos;,&apos;Aligned by Strategy (Not Driven)&apos;)&#13;&#10;&#13;&#10;THEN 1 END )&#13;&#10;/&#13;&#10;COUNTD( IF [Calculation_1113796484456726534] &#13;&#10;and [Strategy (copy)_1431300260991889426]IN (&apos;Driven by Strategy&apos;,&apos;Aligned by Strategy (Not Driven)&apos;)&#13;&#10;THEN [accountuid] END ))' />
  </column>
  <column caption='PP Driven CNT' datatype='integer' name='[CP Driven CNT (copy)_2079537134889267286]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNT( IF [Calculation_1113796484730302474] and [Strategy (copy)_1431300260991889426]=&apos;Driven by Strategy&apos; &#13;&#10;THEN 1 END )' />
  </column>
  <column caption='Date filter' datatype='boolean' name='[Calculation_1113796484456726534]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[eventdatetimeutc] &gt;= [PP Start Date (copy)_1431300258546532354] AND [eventdatetimeutc] &lt;= [PP End Date (copy)_1431300258546819075]' />
  </column>
  <column caption='Last Period' datatype='boolean' name='[Calculation_1113796484730302474]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[eventdatetimeutc] &gt;= [Last period (copy)_1431300258541797376]&#13;&#10;AND [eventdatetimeutc] &lt;= [PP Start Date (copy)_1431300258544062465]' />
    <aliases>
      <alias key='false' value='Current Period' />
      <alias key='true' value='Prior Period' />
    </aliases>
  </column>
  <column caption='CP or PP' datatype='boolean' name='[Calculation_1113796484731543563]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[Calculation_1113796484456726534] OR [Calculation_1113796484730302474]' />
  </column>
  <column caption='% CP vs PP CNT' datatype='real' name='[Calculation_1113796484736237580]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='IF [Actual Audience CNT (copy)_2079537134831333454] = 0 THEN -1&#13;&#10;ELSEIF [CP Driven CNT (copy)_2079537134889267286] = 0 THEN 1&#13;&#10;ELSE (([Actual Audience CNT (copy)_2079537134831333454] - [CP Driven CNT (copy)_2079537134889267286])&#13;&#10;/&#13;&#10;[CP Driven CNT (copy)_2079537134889267286])&#13;&#10;END' />
  </column>
  <column caption='PP Text' datatype='string' name='[Calculation_1431300258547851269]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 4]&#13;&#10;WHEN &apos;Day&apos; THEN STR([Last period (copy)_1431300258541797376])&#13;&#10;WHEN &apos;Week&apos; THEN STR([Last period (copy)_1431300258541797376])+&apos; to &apos;+ STR([PP Start Date (copy)_1431300258544062465])&#13;&#10;WHEN &apos;Month&apos; THEN LEFT(DATENAME(&apos;month&apos;, [Last period (copy)_1431300258541797376]),3)&#13;&#10;+ &apos; &apos;+ STR(YEAR([Last period (copy)_1431300258541797376]))&#13;&#10;WHEN &apos;Quarter&apos; THEN &apos;Q&apos;+ DATENAME(&apos;quarter&apos;, [Last period (copy)_1431300258541797376])&#13;&#10;+ &apos; &apos;+ STR(YEAR([Last period (copy)_1431300258541797376]))&#13;&#10;WHEN &apos;Year&apos; THEN STR(YEAR([Last period (copy)_1431300258541797376]))&#13;&#10;END' />
  </column>
  <column caption='Action Taken Calc' datatype='string' name='[Calculation_1431300260932198411]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF  [strategicActionCategoryId] &gt; 100 and [strategicActionCategoryId] &lt; 200 &#13;&#10;then &apos;Driven by Strategy&apos;&#13;&#10;ELSEIF [strategicActionCategoryId]  &gt; 200 and [strategicActionCategoryId] &lt; 300&#13;&#10;then &apos;Driven by Strategy&apos;&#13;&#10;ELSEIF [strategicActionCategoryId]  &gt; 300 and [strategicActionCategoryId] &lt; 400&#13;&#10;then &apos;Aligned by Strategy (Not Driven)&apos;&#13;&#10;ELSEIF [strategicActionCategoryId]  &gt; 400 and [strategicActionCategoryId] &lt; 500&#13;&#10;then &apos;Aligned by Strategy (Not Driven)&apos;&#13;&#10;ELSE &apos;Not Aligned&apos;&#13;&#10;END' />
  </column>
  <column caption='% Strategy Aligment ' datatype='real' name='[Calculation_2015360901499039777]' role='measure' type='ordinal'>
    <calculation class='tableau' formula='SUM(If [Strategy (copy)_1431300260985085968] =1 and [Calculation_1113796484456726534]&#13;&#10;THEN 1&#13;&#10;END)&#13;&#10;/&#13;&#10;SUM(If [Calculation_1113796484456726534]&#13;&#10;THEN 1&#13;&#10;END)' />
  </column>
  <column caption='View' datatype='string' name='[Calculation_2015360901519757348]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Strategy&apos; THEN IFNULL([usecasename], &apos; Null&apos;)&#13;&#10;WHEN &apos;Channel&apos; THEN [interaction_channelname]&#13;&#10;WHEN &apos;Product&apos; THEN IFNULL([productname], &apos; Null&apos;)&#13;&#10;WHEN &apos;Actor&apos; THEN IFNULL([repTypeName], &apos; Null&apos;)&#13;&#10;WHEN &apos;Segment&apos; THEN IFNULL([segmentname], &apos; Null&apos;)&#13;&#10;END' />
  </column>
  <column caption='AVG' datatype='real' name='[Calculation_2015360901525987369]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='count(1) / countd([accountuid])' />
  </column>
  <column caption='Text' datatype='string' name='[Calculation_2079537134441418803]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='&apos;A&apos;' />
  </column>
  <column caption='Total Audience CNT' datatype='integer' name='[Calculation_2079537134827331661]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNTD( IF [Calculation_1113796484456726534] THEN [accountuid] END )' />
  </column>
  <column caption='AUDIENCE COUNT text' datatype='string' name='[Calculation_2079537134914502751]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='&apos;AUDIENCE COUNT&apos;' />
  </column>
  <column caption='AVG ACTIVITIES PER HCP text' datatype='string' name='[Calculation_2079537134915121248]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='&apos;AVG ACTIVITIES PER HCP&apos;' />
  </column>
  <column caption='Total Details' datatype='integer' name='[Calculation_479914847162810369]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='If [Calculation_1113796484456726534]then 1 END' />
  </column>
  <column caption='Channel (copy)' datatype='string' name='[Channel (copy)_2079537133424140289]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[interaction_channelname]' />
  </column>
  <column caption='Product' datatype='string' hidden='true' name='[Channel Name (new) (copy)_1268044779676872705]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [PRODUCT_NAME]&#13;&#10;WHEN &apos;All Product&apos; THEN &apos; All Products&apos;&#13;&#10;ELSE IFNULL([PRODUCT_NAME], &apos;No Product Specified&apos;)&#13;&#10;END' />
  </column>
  <column caption='Aligned CNT' datatype='integer' name='[Driven Audience CNT (copy)_2079537134884651092]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNTD( IF [Calculation_1113796484456726534] And [Strategy (copy)_1431300260991889426] =&apos;Aligned by Strategy (Not Driven)&apos; THEN [accountuid] END )' />
  </column>
  <column caption='Not Covered Audience CNT' datatype='integer' name='[Driven Audience CNT (copy)_2079537134911742046]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNTD( IF [Calculation_1113796484456726534] And [Strategy (copy)_1431300260991889426] =&apos;Not Covered&apos; THEN [accountuid] END )' />
  </column>
  <column caption='Driven Frequency' datatype='real' name='[Frequency % (copy)_2079537134874546257]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='ZN((COUNT( IF [Calculation_1113796484456726534] and [Strategy (copy)_1431300260991889426]=&apos;Driven by Strategy&apos; &#13;&#10;THEN 1 END )&#13;&#10;/&#13;&#10;COUNTD( IF [Calculation_1113796484456726534] and [Strategy (copy)_1431300260991889426]=&apos;Driven by Strategy&apos; &#13;&#10;THEN [accountuid] END )))' />
  </column>
  <column caption='PP Start Date' datatype='date' datatype-customized='true' default-format='*mm/dd/yyyy' name='[Last period (copy)_1431300258541797376]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='DATE(CASE [Parameters].[Parameter 4]&#13;&#10;WHEN &apos;Day&apos; THEN DATEADD(&apos;day&apos;, -1, [Parameters].[Period Parameter])&#13;&#10;WHEN &apos;Month&apos; THEN DATEADD(&apos;month&apos;, -1, DATETRUNC(&apos;month&apos;,[Parameters].[Period Parameter]))&#13;&#10;WHEN &apos;Week&apos; THEN DATEADD(&apos;week&apos;, -1, DATETRUNC(&apos;week&apos;,[Parameters].[Period Parameter]))&#13;&#10;WHEN &apos;Quarter&apos; THEN DATEADD(&apos;quarter&apos;, -1, DATETRUNC(&apos;quarter&apos;,[Parameters].[Period Parameter]))&#13;&#10;WHEN &apos;Year&apos; THEN DATEADD(&apos;year&apos;, -1, DATETRUNC(&apos;year&apos;,[Parameters].[Period Parameter]))&#13;&#10;END)' />
  </column>
  <column caption='CP End Date' datatype='date' datatype-customized='true' name='[PP End Date (copy)_1431300258546819075]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='DATE(CASE [Parameters].[Parameter 4]&#13;&#10;WHEN &apos;Day&apos; THEN [Parameters].[Period Parameter]&#13;&#10;WHEN &apos;Month&apos; THEN DATEADD(&apos;day&apos;, -1,DATEADD(&apos;month&apos;, 1, [PP Start Date (copy)_1431300258546532354]))&#13;&#10;WHEN &apos;Week&apos; THEN DATEADD(&apos;day&apos;, -1,DATEADD(&apos;week&apos;, 1, [PP Start Date (copy)_1431300258546532354]))&#13;&#10;WHEN &apos;Quarter&apos; THEN DATEADD(&apos;day&apos;, -1,DATEADD(&apos;quarter&apos;, 1, [PP Start Date (copy)_1431300258546532354]))&#13;&#10;WHEN &apos;Year&apos; THEN DATEADD(&apos;day&apos;, -1,DATEADD(&apos;year&apos;, 1, [PP Start Date (copy)_1431300258546532354]))&#13;&#10;END)' />
  </column>
  <column caption='PP End Date' datatype='date' datatype-customized='true' default-format='*mm/dd/yyyy' name='[PP Start Date (copy)_1431300258544062465]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='DATE(CASE [Parameters].[Parameter 4]&#13;&#10;WHEN &apos;Day&apos; THEN DATEADD(&apos;day&apos;, -1, [Parameters].[Period Parameter])&#13;&#10;WHEN &apos;Month&apos; THEN DATEADD(&apos;day&apos;, -1,DATEADD(&apos;month&apos;, 1, [Last period (copy)_1431300258541797376]))&#13;&#10;WHEN &apos;Week&apos; THEN DATEADD(&apos;day&apos;, -1,DATEADD(&apos;week&apos;, 1, [Last period (copy)_1431300258541797376]))&#13;&#10;WHEN &apos;Quarter&apos; THEN DATEADD(&apos;day&apos;, -1,DATEADD(&apos;quarter&apos;, 1, [Last period (copy)_1431300258541797376]))&#13;&#10;WHEN &apos;Year&apos; THEN DATEADD(&apos;day&apos;, -1,DATEADD(&apos;year&apos;, 1, [Last period (copy)_1431300258541797376]))&#13;&#10;END)' />
  </column>
  <column caption='CP Start Date' datatype='date' datatype-customized='true' name='[PP Start Date (copy)_1431300258546532354]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='DATE(CASE [Parameters].[Parameter 4]&#13;&#10;WHEN &apos;Day&apos; THEN [Parameters].[Period Parameter]&#13;&#10;WHEN &apos;Month&apos; THEN  DATETRUNC(&apos;month&apos;,[Parameters].[Period Parameter])&#13;&#10;WHEN &apos;Week&apos; THEN DATETRUNC(&apos;week&apos;,[Parameters].[Period Parameter])&#13;&#10;WHEN &apos;Quarter&apos; THEN  DATETRUNC(&apos;quarter&apos;,[Parameters].[Period Parameter])&#13;&#10;WHEN &apos;Year&apos; THEN DATETRUNC(&apos;year&apos;,[Parameters].[Period Parameter])&#13;&#10;END)' />
  </column>
  <column caption='СP Text' datatype='string' name='[PP Text (copy)_1431300258560622598]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 4]&#13;&#10;WHEN &apos;Day&apos; THEN STR([PP Start Date (copy)_1431300258546532354])&#13;&#10;WHEN &apos;Week&apos; THEN STR([PP Start Date (copy)_1431300258546532354])+ &apos; to &apos; + STR([PP End Date (copy)_1431300258546819075])&#13;&#10;WHEN &apos;Month&apos; THEN LEFT(DATENAME(&apos;month&apos;, [PP Start Date (copy)_1431300258546532354]),3)&#13;&#10;+ &apos; &apos;+ STR(YEAR([PP Start Date (copy)_1431300258546532354]))&#13;&#10;WHEN &apos;Quarter&apos; THEN &apos;Q&apos;+ DATENAME(&apos;quarter&apos;, [PP Start Date (copy)_1431300258546532354])&#13;&#10;+ &apos; &apos;+ STR(YEAR([PP Start Date (copy)_1431300258546532354]))&#13;&#10;WHEN &apos;Year&apos; THEN STR(YEAR([PP Start Date (copy)_1431300258546532354]))&#13;&#10;END' />
  </column>
  <column caption='Strategy  (copy)' datatype='string' name='[Strategy  (copy)_2079537133456670722]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[usecasename]' />
  </column>
  <column caption='Strategy Filter' datatype='integer' name='[Strategy (copy)_1431300260985085968]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='IF  [Calculation_1431300260932198411] &lt;&gt; &apos;Not Aligned&apos;&#13;&#10;THEN 1&#13;&#10;END' />
  </column>
  <column caption='Strategic Category Group' datatype='string' name='[Strategy (copy)_1431300260991889426]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF  [strategicActionCategoryId] &gt; 100 and [strategicActionCategoryId] &lt; 200 &#13;&#10;then &apos;Driven by Strategy&apos;&#13;&#10;ELSEIF [strategicActionCategoryId]  &gt; 200 and [strategicActionCategoryId] &lt; 300&#13;&#10;then &apos;Driven by Strategy&apos;&#13;&#10;ELSEIF [strategicActionCategoryId]  &gt; 300 and [strategicActionCategoryId] &lt; 400&#13;&#10;then &apos;Aligned by Strategy (Not Driven)&apos;&#13;&#10;ELSEIF [strategicActionCategoryId]  &gt; 400 and [strategicActionCategoryId] &lt; 500&#13;&#10;then &apos;Aligned by Strategy (Not Driven)&apos;&#13;&#10;ELSE &apos;Not Covered&apos;&#13;&#10;END' />
  </column>
  <column caption='Driven Audience CNT' datatype='integer' name='[Total Audience CNT (copy)_2079537134883893331]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNTD( IF [Calculation_1113796484456726534] And [Strategy (copy)_1431300260991889426] =&apos;Driven by Strategy&apos; THEN [accountuid] END )' />
  </column>
  <_.fcp.ObjectModelTableType.true...column caption='msrscenario_strate' datatype='table' name='[__tableau_internal_object_id__].[msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177]' role='measure' type='quantitative' />
  <column aggregation='CountD' caption='Accountuid' datatype='string' default-role='measure' default-type='quantitative' name='[accountuid]' role='dimension' type='nominal' />
  <column caption='Actiontaken' datatype='string' name='[actiontaken]' role='dimension' type='nominal' />
  <column caption='Actoruid' datatype='string' name='[actoruid]' role='dimension' type='nominal' />
  <column datatype='string' name='[configCountryCode]' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' />
  <column caption='Detailrepactiontypeid' datatype='integer' name='[detailrepactiontypeid]' role='measure' type='quantitative' />
  <column caption='Event Order' datatype='integer' name='[event_order]' role='measure' type='quantitative' />
  <column caption='Date' datatype='datetime' name='[eventdatetimeutc]' role='dimension' type='ordinal' />
  <column caption='Eventtypename' datatype='string' name='[eventtypename]' role='dimension' type='nominal' />
  <column caption='Tactic' datatype='string' name='[factorname]' role='dimension' type='nominal' />
  <column caption='Insight Matchclosenessdays' datatype='integer' name='[insight_matchclosenessdays]' role='measure' type='quantitative' />
  <column caption='Insight Viewcount' datatype='integer' name='[insight_viewcount]' role='measure' type='quantitative' />
  <column caption='Interaction Channelid' datatype='integer' name='[interaction_channelid]' role='measure' type='quantitative' />
  <column caption='Channel' datatype='string' name='[interaction_channelname]' role='dimension' type='nominal' />
  <column caption='Interactionuid' datatype='string' name='[interactionuid]' role='dimension' type='nominal' />
  <column caption='Internalsuggestionreferenceid' datatype='string' name='[internalsuggestionreferenceid]' role='dimension' type='nominal' />
  <column caption='Is Candidate Usecase' datatype='integer' name='[is_candidate_usecase]' role='measure' type='quantitative' />
  <column caption='Issuggestioncompleteddirect' datatype='integer' name='[issuggestioncompleteddirect]' role='measure' type='quantitative' />
  <column caption='Issuggestioncompletedinfer' datatype='integer' name='[issuggestioncompletedinfer]' role='measure' type='quantitative' />
  <column caption='Msrscenariouid' datatype='string' name='[msrscenariouid]' role='dimension' type='nominal' />
  <column caption='Product ' datatype='string' name='[productname]' role='dimension' type='nominal' />
  <column caption='Productuid' datatype='string' name='[productuid]' role='dimension' type='nominal' />
  <column caption='Actor' datatype='string' name='[repTypeName]' role='dimension' type='nominal' />
  <column caption='Repteamid' datatype='integer' name='[repteamid]' role='measure' type='quantitative' />
  <column caption='Sales Team' datatype='string' name='[repteamname]' role='dimension' type='nominal' />
  <column caption='Segment' datatype='string' name='[segmentname]' role='dimension' type='nominal' />
  <column caption='strategiccategorygroup (copy)' datatype='string' name='[strategiccategorygroup (copy)_2079537134422409235]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[strategiccategorygroup]' />
  </column>
  <column caption='Suggestion Channelid' datatype='integer' name='[suggestion_channelid]' role='measure' type='quantitative' />
  <column caption='Suggestion Channelname' datatype='string' name='[suggestion_channelname]' role='dimension' type='nominal' />
  <column caption='Suggestiondriver' datatype='string' name='[suggestiondriver]' role='dimension' type='nominal' />
  <column caption='Strategy ' datatype='string' name='[usecasename]' role='dimension' type='nominal' />
  <column caption='СP vs PP Text ' datatype='string' name='[СP Text (copy)_2079537134776127560]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[PP Text (copy)_1431300258560622598] +&apos; vs &apos; + [Calculation_1431300258547851269]' />
  </column>
  <column-instance column='[accountuid]' derivation='Count' name='[cnt:accountuid:qk]' pivot='key' type='quantitative' />
  <column-instance column='[accountuid]' derivation='CountD' name='[ctd:accountuid:qk]' pivot='key' type='quantitative' />
  <column-instance column='[accountuid]' derivation='CountD' name='[ctd:accountuid:vtsum:qk]' pivot='key' type='quantitative' visual-totals='Sum' />
  <column-instance column='[Action Taken Calc (copy)_2079537134574747717]' derivation='None' name='[none:Action Taken Calc (copy)_2079537134574747717:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_1113796484456726534]' derivation='None' name='[none:Calculation_1113796484456726534:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_1113796484730302474]' derivation='None' name='[none:Calculation_1113796484730302474:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_1431300258547851269]' derivation='None' name='[none:Calculation_1431300258547851269:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_1431300260932198411]' derivation='None' name='[none:Calculation_1431300260932198411:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_2079537134441418803]' derivation='None' name='[none:Calculation_2079537134441418803:nk]' pivot='key' type='nominal' />
  <column-instance column='[Strategy (copy)_1431300260991889426]' derivation='None' name='[none:Strategy (copy)_1431300260991889426:nk]' pivot='key' type='nominal' />
  <column-instance column='[accountuid]' derivation='None' name='[none:accountuid:nk]' pivot='key' type='nominal' />
  <column-instance column='[actiontaken]' derivation='None' name='[none:actiontaken:nk]' pivot='key' type='nominal' />
  <column-instance column='[accountuid]' derivation='Count' name='[pcto:cnt:accountuid:qk:2]' pivot='key' type='quantitative'>
    <table-calc ordering-type='Rows' type='PctTotal' />
  </column-instance>
  <column-instance column='[accountuid]' derivation='Count' name='[pcto:cnt:accountuid:qk]' pivot='key' type='quantitative'>
    <table-calc ordering-type='Columns' type='PctTotal' />
  </column-instance>
  <column-instance column='[accountuid]' derivation='CountD' name='[pcto:ctd:accountuid:qk:2]' pivot='key' type='quantitative'>
    <table-calc ordering-type='Rows' type='PctTotal' />
  </column-instance>
  <column-instance column='[accountuid]' derivation='CountD' name='[pcto:ctd:accountuid:qk:4]' pivot='key' type='quantitative'>
    <table-calc ordering-type='CellInPane' type='PctTotal' />
  </column-instance>
  <column-instance column='[accountuid]' derivation='CountD' name='[pcto:ctd:accountuid:qk:5]' pivot='key' type='quantitative'>
    <table-calc ordering-field='[Athena S3 data].[Calculation_2015360901519757348]' ordering-type='Field' type='PctTotal' />
  </column-instance>
  <column-instance column='[accountuid]' derivation='CountD' name='[pcto:ctd:accountuid:qk:6]' pivot='key' type='quantitative'>
    <table-calc ordering-type='Table' type='PctTotal' />
  </column-instance>
  <column-instance column='[accountuid]' derivation='CountD' name='[pcto:ctd:accountuid:qk]' pivot='key' type='quantitative'>
    <table-calc ordering-type='Columns' type='PctTotal' />
  </column-instance>
  <column-instance column='[Calculation_2079537134827331661]' derivation='User' name='[pcto:usr:Calculation_2079537134827331661:qk:2]' pivot='key' type='quantitative'>
    <table-calc ordering-type='Rows' type='PctTotal' />
  </column-instance>
  <column-instance column='[Calculation_2079537134827331661]' derivation='User' name='[pcto:usr:Calculation_2079537134827331661:qk]' pivot='key' type='quantitative'>
    <table-calc ordering-type='Columns' type='PctTotal' />
  </column-instance>
  <column-instance column='[% Driven Frequency (copy)_2079537134876905554]' derivation='User' name='[usr:% Driven Frequency (copy)_2079537134876905554:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Actual Audience CNT (copy)_2079537134831333454]' derivation='User' name='[usr:Actual Audience CNT (copy)_2079537134831333454:qk]' pivot='key' type='quantitative' />
  <column-instance column='[CP Driven CNT (copy)_2079537134889267286]' derivation='User' name='[usr:CP Driven CNT (copy)_2079537134889267286:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_1113796484736237580]' derivation='User' name='[usr:Calculation_1113796484736237580:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_2015360901499039777]' derivation='User' name='[usr:Calculation_2015360901499039777:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_2015360901525987369]' derivation='User' name='[usr:Calculation_2015360901525987369:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_2079537134827331661]' derivation='User' name='[usr:Calculation_2079537134827331661:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Frequency % (copy)_2079537134874546257]' derivation='User' name='[usr:Frequency % (copy)_2079537134874546257:qk]' pivot='key' type='quantitative' />
  <group hidden='true' name='[Exclusions (Date Period,Dismissal Reason)]' name-style='unqualified' user:auto-column='exclude'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[none:Date (copy)_100486576214441984:nk]' />
      <groupfilter function='level-members' level='[none:DISMISSAL_REASON (VW_DIM_DISMISSAL_REASON_RPT):nk]' />
    </groupfilter>
  </group>
  <drill-paths>
    <drill-path name='Strategy , Tactic'>
      <field>[usecasename]</field>
      <field>[factorname]</field>
    </drill-path>
  </drill-paths>
  <extract _.fcp.ObjectModelExtractV2.true...object-id='' _.fcp.VConnDownstreamExtractsWithWarnings.true...user-specific='false' count='-1' enabled='true' units='records'>
    <connection class='hyper' dbname='f0276de9-b8b8-486a-8481-147a768cd64a' default-settings='yes' extract-engine='true' schema='Extract' tablename='Extract' update-time='03/31/2023 03:16:12 PM'>
      <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='Extract' table='[Extract].[Extract]' type='table' />
      <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='Extract' table='[Extract].[Extract]' type='table' />
      <refresh>
        <refresh-event add-from-file-path='Athena S3 data' increment-value='%null%' refresh-type='create' rows-inserted='1178325' timestamp-start='2023-03-31 15:02:23.358' />
      </refresh>
      <metadata-records>
        <metadata-record class='column'>
          <remote-name>msrscenariouid</remote-name>
          <remote-type>129</remote-type>
          <local-name>[msrscenariouid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>msrscenariouid</remote-alias>
          <ordinal>0</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>eventdatetimeutc</remote-name>
          <remote-type>135</remote-type>
          <local-name>[eventdatetimeutc]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>eventdatetimeutc</remote-alias>
          <ordinal>1</ordinal>
          <family>Custom SQL Query</family>
          <local-type>datetime</local-type>
          <aggregation>Year</aggregation>
          <approx-count>283541</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>interactionuid</remote-name>
          <remote-type>129</remote-type>
          <local-name>[interactionuid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>interactionuid</remote-alias>
          <ordinal>2</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1059776</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>accountuid</remote-name>
          <remote-type>129</remote-type>
          <local-name>[accountuid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>accountuid</remote-alias>
          <ordinal>3</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>184647</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>productuid</remote-name>
          <remote-type>129</remote-type>
          <local-name>[productuid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>productuid</remote-alias>
          <ordinal>4</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>productname</remote-name>
          <remote-type>129</remote-type>
          <local-name>[productname]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>productname</remote-alias>
          <ordinal>5</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>segmentname</remote-name>
          <remote-type>129</remote-type>
          <local-name>[segmentname]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>segmentname</remote-alias>
          <ordinal>6</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>actoruid</remote-name>
          <remote-type>129</remote-type>
          <local-name>[actoruid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>actoruid</remote-alias>
          <ordinal>7</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>861</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>seConfigId</remote-name>
          <remote-type>20</remote-type>
          <local-name>[seConfigId]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>seConfigId</remote-alias>
          <ordinal>8</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>configCountryCode</remote-name>
          <remote-type>129</remote-type>
          <local-name>[configCountryCode]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>configCountryCode</remote-alias>
          <ordinal>9</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>repteamid</remote-name>
          <remote-type>20</remote-type>
          <local-name>[repteamid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>repteamid</remote-alias>
          <ordinal>10</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>repteamname</remote-name>
          <remote-type>129</remote-type>
          <local-name>[repteamname]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>repteamname</remote-alias>
          <ordinal>11</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>repTypeId</remote-name>
          <remote-type>20</remote-type>
          <local-name>[repTypeId]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>repTypeId</remote-alias>
          <ordinal>12</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>repTypeName</remote-name>
          <remote-type>129</remote-type>
          <local-name>[repTypeName]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>repTypeName</remote-alias>
          <ordinal>13</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>eventtypename</remote-name>
          <remote-type>129</remote-type>
          <local-name>[eventtypename]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>eventtypename</remote-alias>
          <ordinal>14</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>factorname</remote-name>
          <remote-type>129</remote-type>
          <local-name>[factorname]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>factorname</remote-alias>
          <ordinal>15</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>55</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>usecasename</remote-name>
          <remote-type>129</remote-type>
          <local-name>[usecasename]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>usecasename</remote-alias>
          <ordinal>16</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>18</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>suggestiondriver</remote-name>
          <remote-type>129</remote-type>
          <local-name>[suggestiondriver]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>suggestiondriver</remote-alias>
          <ordinal>17</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>internalsuggestionreferenceid</remote-name>
          <remote-type>129</remote-type>
          <local-name>[internalsuggestionreferenceid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>internalsuggestionreferenceid</remote-alias>
          <ordinal>18</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>417436</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>interaction_channelid</remote-name>
          <remote-type>20</remote-type>
          <local-name>[interaction_channelid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>interaction_channelid</remote-alias>
          <ordinal>19</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>interaction_channelname</remote-name>
          <remote-type>129</remote-type>
          <local-name>[interaction_channelname]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>interaction_channelname</remote-alias>
          <ordinal>20</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>suggestion_channelid</remote-name>
          <remote-type>20</remote-type>
          <local-name>[suggestion_channelid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>suggestion_channelid</remote-alias>
          <ordinal>21</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>suggestion_channelname</remote-name>
          <remote-type>129</remote-type>
          <local-name>[suggestion_channelname]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>suggestion_channelname</remote-alias>
          <ordinal>22</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>actiontaken</remote-name>
          <remote-type>129</remote-type>
          <local-name>[actiontaken]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>actiontaken</remote-alias>
          <ordinal>23</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>issuggestioncompleteddirect</remote-name>
          <remote-type>20</remote-type>
          <local-name>[issuggestioncompleteddirect]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>issuggestioncompleteddirect</remote-alias>
          <ordinal>24</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>issuggestioncompletedinfer</remote-name>
          <remote-type>20</remote-type>
          <local-name>[issuggestioncompletedinfer]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>issuggestioncompletedinfer</remote-alias>
          <ordinal>25</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>is_candidate_usecase</remote-name>
          <remote-type>20</remote-type>
          <local-name>[is_candidate_usecase]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>is_candidate_usecase</remote-alias>
          <ordinal>26</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>is_candidate_same_product</remote-name>
          <remote-type>20</remote-type>
          <local-name>[is_candidate_same_product]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>is_candidate_same_product</remote-alias>
          <ordinal>27</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>is_candidate_same_event_type</remote-name>
          <remote-type>20</remote-type>
          <local-name>[is_candidate_same_event_type]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>is_candidate_same_event_type</remote-alias>
          <ordinal>28</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>insight_matchclosenessdays</remote-name>
          <remote-type>20</remote-type>
          <local-name>[insight_matchclosenessdays]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>insight_matchclosenessdays</remote-alias>
          <ordinal>29</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>insight_viewcount</remote-name>
          <remote-type>20</remote-type>
          <local-name>[insight_viewcount]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>insight_viewcount</remote-alias>
          <ordinal>30</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>event_order</remote-name>
          <remote-type>20</remote-type>
          <local-name>[event_order]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>event_order</remote-alias>
          <ordinal>31</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>751</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>repActionTypeId</remote-name>
          <remote-type>20</remote-type>
          <local-name>[repActionTypeId]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>repActionTypeId</remote-alias>
          <ordinal>32</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>detailrepactiontypeid</remote-name>
          <remote-type>20</remote-type>
          <local-name>[detailrepactiontypeid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>detailrepactiontypeid</remote-alias>
          <ordinal>33</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>strategicActionCategoryId</remote-name>
          <remote-type>20</remote-type>
          <local-name>[strategicActionCategoryId]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>strategicActionCategoryId</remote-alias>
          <ordinal>34</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>10</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>strategicActionCategoryUID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[strategicActionCategoryUID]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>strategicActionCategoryUID</remote-alias>
          <ordinal>35</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>10</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>engagementsegment</remote-name>
          <remote-type>129</remote-type>
          <local-name>[engagementsegment]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>engagementsegment</remote-alias>
          <ordinal>36</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>engagementsegment_monthly</remote-name>
          <remote-type>129</remote-type>
          <local-name>[engagementsegment_monthly]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>engagementsegment_monthly</remote-alias>
          <ordinal>37</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>6</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>engagementsegment_quarterly</remote-name>
          <remote-type>129</remote-type>
          <local-name>[engagementsegment_quarterly]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>engagementsegment_quarterly</remote-alias>
          <ordinal>38</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>engagementrate_monthly</remote-name>
          <remote-type>5</remote-type>
          <local-name>[engagementrate_monthly]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>engagementrate_monthly</remote-alias>
          <ordinal>39</ordinal>
          <family>Custom SQL Query</family>
          <local-type>real</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>5276</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>driven_category_ids</remote-name>
          <remote-type>129</remote-type>
          <local-name>[driven_category_ids]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>driven_category_ids</remote-alias>
          <ordinal>40</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>partially_driven_category_ids</remote-name>
          <remote-type>129</remote-type>
          <local-name>[partially_driven_category_ids]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>partially_driven_category_ids</remote-alias>
          <ordinal>41</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>aligned_category_ids</remote-name>
          <remote-type>129</remote-type>
          <local-name>[aligned_category_ids]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>aligned_category_ids</remote-alias>
          <ordinal>42</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>driven</remote-name>
          <remote-type>20</remote-type>
          <local-name>[driven]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>driven</remote-alias>
          <ordinal>43</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>partially_driven</remote-name>
          <remote-type>20</remote-type>
          <local-name>[partially_driven]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>partially_driven</remote-alias>
          <ordinal>44</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>aligned</remote-name>
          <remote-type>20</remote-type>
          <local-name>[aligned]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>aligned</remote-alias>
          <ordinal>45</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>strategicCategoryGroupUid</remote-name>
          <remote-type>129</remote-type>
          <local-name>[strategicCategoryGroupUid]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>strategicCategoryGroupUid</remote-alias>
          <ordinal>46</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>strategicCategoryGroup</remote-name>
          <remote-type>129</remote-type>
          <local-name>[strategicCategoryGroup]</local-name>
          <parent-name>[Extract]</parent-name>
          <remote-alias>strategicCategoryGroup</remote-alias>
          <ordinal>47</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
      </metadata-records>
    </connection>
  </extract>
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.885714' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.114286' show-aliased-fields='true' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[none:actiontaken:nk]' type='palette'>
        <map to='#4e79a7'>
          <bucket>%null%</bucket>
        </map>
        <map to='#572b5e'>
          <bucket>&quot;Suggestions Completed&quot;</bucket>
        </map>
        <map to='#c079cd'>
          <bucket>&quot;Suggestions Dismissed&quot;</bucket>
        </map>
        <map to='#fff6d5'>
          <bucket>&quot;No Action Taken&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_1431300258547851269:nk]' type='palette'>
        <map to='#88d0ff'>
          <bucket>&quot;Q1 2022&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_2079537134441418803:nk]' type='palette'>
        <map to='#0089d2'>
          <bucket>&quot;A&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_1113796484456726534:nk]' type='palette'>
        <map to='#0089d2'>
          <bucket>true</bucket>
        </map>
        <map to='#d3d3d3'>
          <bucket>false</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_1113796484730302474:nk]' type='palette'>
        <map to='#0089d2'>
          <bucket>false</bucket>
        </map>
        <map to='#58c3c1'>
          <bucket>true</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[:Measure Names]' type='palette'>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[avg:SUGG_UNIQ_CNT:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[cnt:accountuid:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[cnt:EVENT_DATE_KEY:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[ctd:accountuid:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[ctd:accountuid:vtsum:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[ctd:Calculation_2015360901175549970:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[ctd:SUGGESTION_REFERENCE_ID:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[cum:usr:Calculation_2015360901522923559:qk:2]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[cum:usr:Calculation_2015360901522923559:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[min:Calculation_2079537134438862896:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[min:Calculation_2079537134439010353:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[none:accountuid:nk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[none:Action Taken Calc (copy)_2079537134574747717:nk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[none:Calculation_1431300260932198411:nk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[none:Calculation_2079537134429470758:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[none:Calculation_2079537134429716520:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[none:Calculation_2079537134827319372:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[none:Strategy (copy)_1431300260991889426:nk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcdf:sum:SUGG_UNIQ_CNT:qk:2]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcdf:sum:SUGG_UNIQ_CNT:qk:4]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcdf:sum:SUGG_UNIQ_CNT:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcto:cnt:accountuid:qk:2]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcto:cnt:accountuid:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcto:ctd:accountuid:qk:2]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcto:ctd:accountuid:qk:4]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcto:ctd:accountuid:qk:5]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcto:ctd:accountuid:qk:6]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcto:ctd:accountuid:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcto:usr:Calculation_2079537134827331661:qk:2]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[pcto:usr:Calculation_2079537134827331661:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[sum:Calculation_2079537134438862896:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[sum:EVENT_DATE_KEY:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[sum:SUGG_UNIQ_CNT:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Actual Audience CNT (copy)_2079537134831333454:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_1113796484736237580:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_1431300260986499089:qk:1]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901171236875:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901171286028:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901171388429:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901171470350:qk:2]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901171470350:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901497319455:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901522882598:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901522923559:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901524557864:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901525987369:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134199242755:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134199382020:qk:1]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134200000517:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134215311368:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134216830985:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134415917070:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134415962127:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134416031760:qk:1]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134425493529:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134425649179:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134428114975:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134428241954:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134429552679:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134429806633:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134437077035:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134437244972:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134440337458:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134443253813:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134821130313:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134827331661:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_890305360473403394:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_890305360475045896:qk]&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;[Athena S3 data].[usr:Driven Frequency (copy)_2079537134903767127:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[cnt:Calculation_2015360901158010886:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[cnt:strategicactioncategoryuid:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[ctd:Calculation_2015360901158010886:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[ctd:strategicactioncategoryuid:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[none:Calculation_2015360901794959414:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[none:strategicactioncategoryuid:nk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[pcdf:ctd:Calculation_2015360901158010886:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[pcto:ctd:Calculation_2015360901158010886:qk:2]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[pcto:ctd:Calculation_2015360901158010886:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[pcto:usr:Calculation_2015360901724561456:qk:2]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[pcto:usr:Calculation_2015360901724561456:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[pcto:usr:Calculation_2079537134447001663:qk:2]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[pcto:usr:Calculation_2079537134447001663:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[sum:Calculation_2015360901361426451:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:\% Driven Frequency (copy)_2079537134876905554:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_1113796484451270659:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_1113796484451352580:qk:3]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_1113796484451352580:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_1431300260936097804:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_1431300260937510927:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_1431300261007015955:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901497307166:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901499039777:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901724454959:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901724561456:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2015360901794971703:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134425227284:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134425628698:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134428155937:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134428303396:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537134447001663:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537135275380837:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537135275405414:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537135275728999:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537135276277864:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_2079537135277936745:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_890305360473427972:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_890305360475025414:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation_890305360475754507:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Calculation1:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:CP Driven CNT (copy)_2079537134889267286:qk]&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;[Athena S3 data].[usr:Frequency \% (copy)_2079537134874546257:qk]&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_1431300260932198411:nk]' type='palette'>
        <map to='#0089d2'>
          <bucket>&quot;Driven by Strategy&quot;</bucket>
        </map>
        <map to='#0089d2'>
          <bucket>&quot;driven&quot;</bucket>
        </map>
        <map to='#88d0ff'>
          <bucket>&quot;Aligned by Strategy (Not Driven)&quot;</bucket>
        </map>
        <map to='#88d0ff'>
          <bucket>&quot;aligned&quot;</bucket>
        </map>
        <map to='#d3d3d3'>
          <bucket>%null%</bucket>
        </map>
        <map to='#d3d3d3'>
          <bucket>&quot;Not Aligned&quot;</bucket>
        </map>
        <map to='#d3d3d3'>
          <bucket>&quot;Not Covered&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Strategy (copy)_1431300260991889426:nk]' type='palette'>
        <map to='#0089d2'>
          <bucket>&quot;Driven by Strategy&quot;</bucket>
        </map>
        <map to='#88d0ff'>
          <bucket>&quot;Aligned by Strategy (Not Driven)&quot;</bucket>
        </map>
        <map to='#d3d3d3'>
          <bucket>&quot;Not Covered&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <datasource-dependencies datasource='Parameters'>
    <column caption='pr_View' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Strategy&quot;'>
      <calculation class='tableau' formula='&quot;Strategy&quot;' />
      <members>
        <member value='&quot;Strategy&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Segment&quot;' />
        <member value='&quot;Actor&quot;' />
      </members>
    </column>
    <column caption='pr_Granularity' datatype='string' name='[Parameter 4]' param-domain-type='list' role='measure' type='nominal' value='&quot;Quarter&quot;'>
      <calculation class='tableau' formula='&quot;Quarter&quot;' />
      <members>
        <member value='&quot;Month&quot;' />
        <member value='&quot;Quarter&quot;' />
        <member value='&quot;Year&quot;' />
      </members>
    </column>
    <column caption='pr_End Date' datatype='date' datatype-customized='true' name='[Period Parameter]' param-domain-type='any' role='measure' type='quantitative' value='#2022-06-07#'>
      <calculation class='tableau' formula='#2022-06-07#' />
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='msrscenario_strate' id='msrscenario_strategic_coverage_v_E0ABD44085844318B999A05BBD6D4177'>
        <properties context=''>
          <relation connection='athena.0oqf5ed1bzmnov14mbnf60xygwoc' name='Custom SQL Query' type='text'>SELECT *&#13;
FROM &quot;impact_${CUSTOMER_NAME}_${ENVIRONMENT}&quot;.&quot;msrscenario_strategic_coverage_v&quot; &quot;msrscenario_strate&quot;</relation>
        </properties>
        <properties context='extract'>
          <relation name='Extract' table='[Extract].[Extract]' type='table' />
        </properties>
      </object>
    </objects>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
