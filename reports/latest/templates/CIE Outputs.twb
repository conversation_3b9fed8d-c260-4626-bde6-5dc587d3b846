<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20223.22.1108.1422                               -->
<workbook locale='en_US' source-build='2021.4.7 (20214.22.0516.1717)' source-platform='mac' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.AccessibleZoneTabOrder.true...AccessibleZoneTabOrder />
    <_.fcp.AnimationOnByDefault.true...AnimationOnByDefault />
    <AutoCreateAndUpdateDSDPhoneLayouts />
    <ISO8601DefaultCalendarPref />
    <_.fcp.MarkAnimation.true...MarkAnimation />
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <SetMembershipControl />
    <SheetIdentifierTracking />
    <SortTagCleanup />
    <WindowsPersistSimpleIdentifiers />
    <WorksheetBackgroundTransparency />
    <ZoneBackgroundTransparency />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputs?rev=1.3' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputs' path='/t/${TABLEAU_SITE}/workbooks' revision='1.3' site='${TABLEAU_SITE}' />
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <_.fcp.AnimationOnByDefault.false...style>
    <_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule element='animation'>
      <_.fcp.AnimationOnByDefault.false...format attr='animation-on' value='ao-on' />
    </_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule>
  </_.fcp.AnimationOnByDefault.false...style>
  <datasources>
    <datasource hasconnection='false' inline='true' name='Parameters' version='18.1'>
      <aliases enabled='yes' />
      <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
        <calculation class='tableau' formula='&quot;Channel&quot;' />
        <members>
          <member value='&quot;Channel&quot;' />
          <member value='&quot;Segment&quot;' />
        </members>
      </column>
      <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
        <calculation class='tableau' formula='&quot;Published&quot;' />
        <members>
          <member value='&quot;(All)&quot;' />
          <member value='&quot;Published&quot;' />
        </members>
      </column>
    </datasource>
    <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model' inline='true' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l' version='18.1'>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputbyChannelDataModel?rev=1.3' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputbyChannelDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.3' site='${TABLEAU_SITE}' />
      <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputbyChannelDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
        <calculations>
          <calculation column='[Calculation_1035546475303264257]' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
          <calculation column='[Calculation_1394708532141854720]' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
          <calculation column='[Calculation_184366111153672192]' formula='SUM([SUGGESTION_COUNT])' />
          <calculation column='[Calculation_458311642422099972]' formula='[DCO_REASON_CODE]+&apos;. &apos;+[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]' />
          <calculation column='[Calculation_487936923659948084]' formula='{ FIXED [DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)], [Calculation_458311642422099972]: SUM([SUGGESTION_CANDIDATES])} &#10;/ { FIXED[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]: SUM([SUGGESTION_CANDIDATES])}' />
          <calculation column='[Calculation_597008439445168139]' formula='SUM([ACTUALS_SUGGESTION_COUNT])/COUNTD([DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)])' />
          <calculation column='[Channel (copy)_1319554694635393025]' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
          <calculation column='[MIN_CAPACITY (copy)_1245104568322998288]' formula='[MAX_CAPACITY]' />
        </calculations>
        <metadata-records>
          <metadata-record class='measure'>
            <remote-name>ACTUALS_FINAL_SCORE_SUM</remote-name>
            <remote-type>5</remote-type>
            <local-name>[ACTUALS_FINAL_SCORE_SUM]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTUALS_FINAL_SCORE_SUM</remote-alias>
            <ordinal>13</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>ACTUALS_SUGGESTION_COUNT</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ACTUALS_SUGGESTION_COUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTUALS_SUGGESTION_COUNT</remote-alias>
            <ordinal>14</ordinal>
            <layered>true</layered>
            <caption>Actual Suggested</caption>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>30</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>CAPACITY_FINAL_SCORE_SUM</remote-name>
            <remote-type>5</remote-type>
            <local-name>[CAPACITY_FINAL_SCORE_SUM]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CAPACITY_FINAL_SCORE_SUM</remote-alias>
            <ordinal>11</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>CAPACITY_SUGGESTION_COUNT</remote-name>
            <remote-type>131</remote-type>
            <local-name>[CAPACITY_SUGGESTION_COUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CAPACITY_SUGGESTION_COUNT</remote-alias>
            <ordinal>12</ordinal>
            <layered>true</layered>
            <caption>Capacity Suggested</caption>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>30</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CHANNEL</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CHANNEL]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL</remote-alias>
            <ordinal>6</ordinal>
            <layered>true</layered>
            <caption>Channel</caption>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>COUNTRY_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[COUNTRY_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>COUNTRY_CODE</remote-alias>
            <ordinal>23</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_BY (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CREATED_BY (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_BY (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>76</ordinal>
            <layered>true</layered>
            <caption>Created By (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_BY</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CREATED_BY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_BY</remote-alias>
            <ordinal>40</ordinal>
            <layered>true</layered>
            <caption>Created By</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_TS (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[CREATED_TS (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_TS (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>77</ordinal>
            <layered>true</layered>
            <caption>Created Ts (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_TS</remote-name>
            <remote-type>7</remote-type>
            <local-name>[CREATED_TS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_TS</remote-alias>
            <ordinal>43</ordinal>
            <layered>true</layered>
            <caption>Created Ts</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1035546475303264257</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1035546475303264257]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1035546475303264257</remote-alias>
            <ordinal>84</ordinal>
            <layered>true</layered>
            <caption> Scenario</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1394708532141854720</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1394708532141854720]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1394708532141854720</remote-alias>
            <ordinal>85</ordinal>
            <layered>true</layered>
            <caption>Scenario group</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Parameter 2]&#13;
WHEN &apos;(All)&apos; THEN 1&#13;
WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_184366111153672192</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_184366111153672192]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_184366111153672192</remote-alias>
            <ordinal>86</ordinal>
            <layered>true</layered>
            <caption>Calc_Sum_Sugg</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM([SUGGESTION_COUNT])&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_458311642422099972</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_458311642422099972]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_458311642422099972</remote-alias>
            <ordinal>87</ordinal>
            <layered>true</layered>
            <caption>Reasons</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[DCO_REASON_CODE]+&apos;. &apos;+[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_487936923659948084</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_487936923659948084]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_487936923659948084</remote-alias>
            <ordinal>88</ordinal>
            <layered>true</layered>
            <caption>Fixed %</caption>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;{ FIXED [DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)], [Calculation_458311642422099972]: SUM([SUGGESTION_CANDIDATES])} 
/ { FIXED[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]: SUM([SUGGESTION_CANDIDATES])}&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_597008439445168139</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_597008439445168139]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_597008439445168139</remote-alias>
            <ordinal>89</ordinal>
            <layered>true</layered>
            <caption>calculate_suggestions</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM([ACTUALS_SUGGESTION_COUNT])/COUNTD([DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)])&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Channel (copy)_1319554694635393025</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Channel (copy)_1319554694635393025]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Channel (copy)_1319554694635393025</remote-alias>
            <ordinal>90</ordinal>
            <layered>true</layered>
            <caption>Channel / Segment</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Parameter 1]&#13;
WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;
WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_CODE</remote-alias>
            <ordinal>29</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Code</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_DESCRIPTION</remote-alias>
            <ordinal>31</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Description</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>347</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)</remote-alias>
            <ordinal>30</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Name (Vw Dim Dco Reason Rpt)</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>22</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_NAME</remote-alias>
            <ordinal>22</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>22</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_TYPE_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_TYPE_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_TYPE_CODE</remote-alias>
            <ordinal>32</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Type Code</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>9</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_TYPE_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_TYPE_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_TYPE_NAME</remote-alias>
            <ordinal>33</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Type Name</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>9</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)</remote-alias>
            <ordinal>28</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Uid (Vw Dim Dco Reason Rpt)</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_UID</remote-alias>
            <ordinal>21</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)</remote-alias>
            <ordinal>39</ordinal>
            <layered>true</layered>
            <caption>Date</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DCO_RUN_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_DATE</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <caption>Dco Run Date</caption>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_UID (VW_F_OUTPUT_BY_CHANNEL_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_RUN_UID (VW_F_OUTPUT_BY_CHANNEL_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_UID (VW_F_OUTPUT_BY_CHANNEL_RPT)</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_RUN_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_UID</remote-alias>
            <ordinal>35</ordinal>
            <layered>true</layered>
            <caption>Dco Run Uid</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DESCRIPTION</remote-alias>
            <ordinal>47</ordinal>
            <layered>true</layered>
            <caption>Description</caption>
            <family>VW_DIM_DSE_CONFIG</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_C3DC8B6F54CC41FDA63028F1927001F4]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_BRAND_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_BRAND_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_BRAND_KEY</remote-alias>
            <ordinal>51</ordinal>
            <layered>true</layered>
            <caption>Dim Brand Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_COUNTRY_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_COUNTRY_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_COUNTRY_KEY</remote-alias>
            <ordinal>61</ordinal>
            <layered>true</layered>
            <caption>Dim Country Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CUSTOMER_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CUSTOMER_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
            <ordinal>60</ordinal>
            <layered>true</layered>
            <caption>Dim Customer Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DIM_HCP_SEGMENT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_HCP_SEGMENT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_HCP_SEGMENT_KEY</remote-alias>
            <ordinal>7</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>73</ordinal>
            <layered>true</layered>
            <caption>Dim Product Key (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_PRODUCT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_PRODUCT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_PRODUCT_KEY</remote-alias>
            <ordinal>49</ordinal>
            <layered>true</layered>
            <caption>Dim Product Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DIM_SEGMENTATION_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_SEGMENTATION_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_SEGMENTATION_KEY</remote-alias>
            <ordinal>9</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_SEGMENT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_SEGMENT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_SEGMENT_KEY</remote-alias>
            <ordinal>70</ordinal>
            <layered>true</layered>
            <caption>Dim Segment Key</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_SEGMENT_TYPE_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_SEGMENT_TYPE_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_SEGMENT_TYPE_KEY</remote-alias>
            <ordinal>80</ordinal>
            <layered>true</layered>
            <caption>Dim Segment Type Key</caption>
            <family>VW_DIM_SEGMENT_TYPE_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DS_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DS_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DS_NAME</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>4</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>HCP_SEGMENT</remote-name>
            <remote-type>129</remote-type>
            <local-name>[HCP_SEGMENT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_SEGMENT</remote-alias>
            <ordinal>8</ordinal>
            <layered>true</layered>
            <caption>Hcp Segment</caption>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_ACTIVE_SRC</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_ACTIVE_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_ACTIVE_SRC</remote-alias>
            <ordinal>57</ordinal>
            <layered>true</layered>
            <caption>Is Active Src</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_COMPETITOR</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_COMPETITOR]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_COMPETITOR</remote-alias>
            <ordinal>58</ordinal>
            <layered>true</layered>
            <caption>Is Competitor</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_DELETED_SRC</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_DELETED_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_DELETED_SRC</remote-alias>
            <ordinal>59</ordinal>
            <layered>true</layered>
            <caption>Is Deleted Src</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_PUBLISHED</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_PUBLISHED]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_PUBLISHED</remote-alias>
            <ordinal>66</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LAST_DCO_RUN_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[LAST_DCO_RUN_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
            <ordinal>68</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LAST_DCO_RUN_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[LAST_DCO_RUN_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_DCO_RUN_UID</remote-alias>
            <ordinal>67</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>LAST_SCORE</remote-name>
            <remote-type>5</remote-type>
            <local-name>[LAST_SCORE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_SCORE</remote-alias>
            <ordinal>25</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LATEST_RUN</remote-name>
            <remote-type>11</remote-type>
            <local-name>[LATEST_RUN]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LATEST_RUN</remote-alias>
            <ordinal>38</ordinal>
            <layered>true</layered>
            <caption>Latest Run</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>MAX_CAPACITY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[MAX_CAPACITY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>MAX_CAPACITY</remote-alias>
            <ordinal>16</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>MIN_CAPACITY (copy)_1245104568322998288</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[MIN_CAPACITY (copy)_1245104568322998288]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>MIN_CAPACITY (copy)_1245104568322998288</remote-alias>
            <ordinal>91</ordinal>
            <layered>true</layered>
            <caption>max_capacity</caption>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;[MAX_CAPACITY]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>MIN_CAPACITY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[MIN_CAPACITY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>MIN_CAPACITY</remote-alias>
            <ordinal>15</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>POST_PROC_STATUS</remote-name>
            <remote-type>129</remote-type>
            <local-name>[POST_PROC_STATUS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>POST_PROC_STATUS</remote-alias>
            <ordinal>42</ordinal>
            <layered>true</layered>
            <caption>Post Proc Status</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[PRODUCT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID</remote-alias>
            <ordinal>75</ordinal>
            <layered>true</layered>
            <caption>Product Id</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME</remote-alias>
            <ordinal>52</ordinal>
            <layered>true</layered>
            <caption>Product Name</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME_ENGLISH</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME_ENGLISH]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME_ENGLISH</remote-alias>
            <ordinal>53</ordinal>
            <layered>true</layered>
            <caption>Product Name English</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_TYPE</remote-alias>
            <ordinal>54</ordinal>
            <layered>true</layered>
            <caption>Product Type</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)</remote-alias>
            <ordinal>50</ordinal>
            <layered>true</layered>
            <caption>Product Uid (Vw Dim Product Dco Rpt)</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>74</ordinal>
            <layered>true</layered>
            <caption>Product Uid (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>200</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <caption>Product Uid</caption>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECOMMENDED</remote-name>
            <remote-type>11</remote-type>
            <local-name>[RECOMMENDED]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECOMMENDED</remote-alias>
            <ordinal>24</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECORD_END_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[RECORD_END_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECORD_END_DATE</remote-alias>
            <ordinal>56</ordinal>
            <layered>true</layered>
            <caption>Record End Date</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECORD_START_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[RECORD_START_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECORD_START_DATE</remote-alias>
            <ordinal>55</ordinal>
            <layered>true</layered>
            <caption>Record Start Date</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>REMAINING_REP_CAPACITY</remote-name>
            <remote-type>129</remote-type>
            <local-name>[REMAINING_REP_CAPACITY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>REMAINING_REP_CAPACITY</remote-alias>
            <ordinal>19</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_DESCRIPTION</remote-alias>
            <ordinal>65</ordinal>
            <layered>true</layered>
            <caption>Scenario Description</caption>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>255</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_NAME</remote-alias>
            <ordinal>64</ordinal>
            <layered>true</layered>
            <caption>Scenario Name</caption>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID (VW_DIM_DCO_RUN_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID (VW_DIM_DCO_RUN_RPT)</remote-alias>
            <ordinal>36</ordinal>
            <layered>true</layered>
            <caption>Scenario Uid (Vw Dim Dco Run Rpt)</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID (VW_DIM_SCENARIO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID (VW_DIM_SCENARIO_RPT)</remote-alias>
            <ordinal>63</ordinal>
            <layered>true</layered>
            <caption>Scenario Uid (Vw Dim Scenario Rpt)</caption>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <caption>Scenario Uid</caption>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_VER</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_VER]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_VER</remote-alias>
            <ordinal>37</ordinal>
            <layered>true</layered>
            <caption>Scenario Ver</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENTATION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENTATION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENTATION</remote-alias>
            <ordinal>10</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_NAME</remote-alias>
            <ordinal>71</ordinal>
            <layered>true</layered>
            <caption>Segment Name</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>200</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)</remote-alias>
            <ordinal>81</ordinal>
            <layered>true</layered>
            <caption>Segment Type (Vw Dim Segment Type Rpt)</caption>
            <family>VW_DIM_SEGMENT_TYPE_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_TYPE</remote-alias>
            <ordinal>72</ordinal>
            <layered>true</layered>
            <caption>Segment Type</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_ID (VW_DIM_DSE_CONFIG)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_ID (VW_DIM_DSE_CONFIG)</remote-alias>
            <ordinal>45</ordinal>
            <layered>true</layered>
            <caption>Se Config Id (Vw Dim Dse Config)</caption>
            <family>VW_DIM_DSE_CONFIG</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_C3DC8B6F54CC41FDA63028F1927001F4]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>SE_CONFIG_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SE_CONFIG_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_ID</remote-alias>
            <ordinal>26</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SE_CONFIG_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_NAME</remote-alias>
            <ordinal>46</ordinal>
            <layered>true</layered>
            <caption>Config</caption>
            <family>VW_DIM_DSE_CONFIG</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_C3DC8B6F54CC41FDA63028F1927001F4]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>STATUS</remote-name>
            <remote-type>129</remote-type>
            <local-name>[STATUS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>STATUS</remote-alias>
            <ordinal>41</ordinal>
            <layered>true</layered>
            <caption>Status</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>SUGGESTION_CANDIDATES</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SUGGESTION_CANDIDATES]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_CANDIDATES</remote-alias>
            <ordinal>20</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>30</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>SUGGESTION_COUNT</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SUGGESTION_COUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_COUNT</remote-alias>
            <ordinal>18</ordinal>
            <layered>true</layered>
            <caption>Suggestion Count</caption>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>30</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TIME_SPENT_PER_DAY</remote-name>
            <remote-type>129</remote-type>
            <local-name>[TIME_SPENT_PER_DAY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>TIME_SPENT_PER_DAY</remote-alias>
            <ordinal>17</ordinal>
            <layered>true</layered>
            <family>VW_F_OUTPUT_BY_CHANNEL_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UPDATED_TS</remote-name>
            <remote-type>7</remote-type>
            <local-name>[UPDATED_TS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>UPDATED_TS</remote-alias>
            <ordinal>78</ordinal>
            <layered>true</layered>
            <caption>Updated Ts</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_64BIT_CALCULATIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model' inline='true' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputbyChannelDataModel?rev=1.3' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputbyChannelDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.3' site='${TABLEAU_SITE}' />
  <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputbyChannelDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
    </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
    <cols>
      <map key='[ACTUALS_FINAL_SCORE_SUM]' value='[sqlproxy].[ACTUALS_FINAL_SCORE_SUM]' />
      <map key='[ACTUALS_SUGGESTION_COUNT]' value='[sqlproxy].[ACTUALS_SUGGESTION_COUNT]' />
      <map key='[CAPACITY_FINAL_SCORE_SUM]' value='[sqlproxy].[CAPACITY_FINAL_SCORE_SUM]' />
      <map key='[CAPACITY_SUGGESTION_COUNT]' value='[sqlproxy].[CAPACITY_SUGGESTION_COUNT]' />
      <map key='[CHANNEL]' value='[sqlproxy].[CHANNEL]' />
      <map key='[COUNTRY_CODE]' value='[sqlproxy].[COUNTRY_CODE]' />
      <map key='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[CREATED_BY (VW_DIM_SEGMENT_RPT)]' />
      <map key='[CREATED_BY]' value='[sqlproxy].[CREATED_BY]' />
      <map key='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[CREATED_TS (VW_DIM_SEGMENT_RPT)]' />
      <map key='[CREATED_TS]' value='[sqlproxy].[CREATED_TS]' />
      <map key='[Calculation_1035546475303264257]' value='[sqlproxy].[Calculation_1035546475303264257]' />
      <map key='[Calculation_1394708532141854720]' value='[sqlproxy].[Calculation_1394708532141854720]' />
      <map key='[Calculation_184366111153672192]' value='[sqlproxy].[Calculation_184366111153672192]' />
      <map key='[Calculation_458311642422099972]' value='[sqlproxy].[Calculation_458311642422099972]' />
      <map key='[Calculation_487936923659948084]' value='[sqlproxy].[Calculation_487936923659948084]' />
      <map key='[Calculation_597008439445168139]' value='[sqlproxy].[Calculation_597008439445168139]' />
      <map key='[Channel (copy)_1319554694635393025]' value='[sqlproxy].[Channel (copy)_1319554694635393025]' />
      <map key='[DCO_REASON_CODE]' value='[sqlproxy].[DCO_REASON_CODE]' />
      <map key='[DCO_REASON_DESCRIPTION]' value='[sqlproxy].[DCO_REASON_DESCRIPTION]' />
      <map key='[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]' value='[sqlproxy].[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]' />
      <map key='[DCO_REASON_NAME]' value='[sqlproxy].[DCO_REASON_NAME]' />
      <map key='[DCO_REASON_TYPE_CODE]' value='[sqlproxy].[DCO_REASON_TYPE_CODE]' />
      <map key='[DCO_REASON_TYPE_NAME]' value='[sqlproxy].[DCO_REASON_TYPE_NAME]' />
      <map key='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' value='[sqlproxy].[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
      <map key='[DCO_REASON_UID]' value='[sqlproxy].[DCO_REASON_UID]' />
      <map key='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' value='[sqlproxy].[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' />
      <map key='[DCO_RUN_DATE]' value='[sqlproxy].[DCO_RUN_DATE]' />
      <map key='[DCO_RUN_UID (VW_F_OUTPUT_BY_CHANNEL_RPT)]' value='[sqlproxy].[DCO_RUN_UID (VW_F_OUTPUT_BY_CHANNEL_RPT)]' />
      <map key='[DCO_RUN_UID]' value='[sqlproxy].[DCO_RUN_UID]' />
      <map key='[DESCRIPTION]' value='[sqlproxy].[DESCRIPTION]' />
      <map key='[DIM_BRAND_KEY]' value='[sqlproxy].[DIM_BRAND_KEY]' />
      <map key='[DIM_COUNTRY_KEY]' value='[sqlproxy].[DIM_COUNTRY_KEY]' />
      <map key='[DIM_CUSTOMER_KEY]' value='[sqlproxy].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_HCP_SEGMENT_KEY]' value='[sqlproxy].[DIM_HCP_SEGMENT_KEY]' />
      <map key='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' />
      <map key='[DIM_PRODUCT_KEY]' value='[sqlproxy].[DIM_PRODUCT_KEY]' />
      <map key='[DIM_SEGMENTATION_KEY]' value='[sqlproxy].[DIM_SEGMENTATION_KEY]' />
      <map key='[DIM_SEGMENT_KEY]' value='[sqlproxy].[DIM_SEGMENT_KEY]' />
      <map key='[DIM_SEGMENT_TYPE_KEY]' value='[sqlproxy].[DIM_SEGMENT_TYPE_KEY]' />
      <map key='[DS_NAME]' value='[sqlproxy].[DS_NAME]' />
      <map key='[HCP_SEGMENT]' value='[sqlproxy].[HCP_SEGMENT]' />
      <map key='[IS_ACTIVE_SRC]' value='[sqlproxy].[IS_ACTIVE_SRC]' />
      <map key='[IS_COMPETITOR]' value='[sqlproxy].[IS_COMPETITOR]' />
      <map key='[IS_DELETED_SRC]' value='[sqlproxy].[IS_DELETED_SRC]' />
      <map key='[IS_PUBLISHED]' value='[sqlproxy].[IS_PUBLISHED]' />
      <map key='[LAST_DCO_RUN_DATE]' value='[sqlproxy].[LAST_DCO_RUN_DATE]' />
      <map key='[LAST_DCO_RUN_UID]' value='[sqlproxy].[LAST_DCO_RUN_UID]' />
      <map key='[LAST_SCORE]' value='[sqlproxy].[LAST_SCORE]' />
      <map key='[LATEST_RUN]' value='[sqlproxy].[LATEST_RUN]' />
      <map key='[MAX_CAPACITY]' value='[sqlproxy].[MAX_CAPACITY]' />
      <map key='[MIN_CAPACITY (copy)_1245104568322998288]' value='[sqlproxy].[MIN_CAPACITY (copy)_1245104568322998288]' />
      <map key='[MIN_CAPACITY]' value='[sqlproxy].[MIN_CAPACITY]' />
      <map key='[POST_PROC_STATUS]' value='[sqlproxy].[POST_PROC_STATUS]' />
      <map key='[PRODUCT_ID]' value='[sqlproxy].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME]' value='[sqlproxy].[PRODUCT_NAME]' />
      <map key='[PRODUCT_NAME_ENGLISH]' value='[sqlproxy].[PRODUCT_NAME_ENGLISH]' />
      <map key='[PRODUCT_TYPE]' value='[sqlproxy].[PRODUCT_TYPE]' />
      <map key='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' value='[sqlproxy].[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
      <map key='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' />
      <map key='[PRODUCT_UID]' value='[sqlproxy].[PRODUCT_UID]' />
      <map key='[RECOMMENDED]' value='[sqlproxy].[RECOMMENDED]' />
      <map key='[RECORD_END_DATE]' value='[sqlproxy].[RECORD_END_DATE]' />
      <map key='[RECORD_START_DATE]' value='[sqlproxy].[RECORD_START_DATE]' />
      <map key='[REMAINING_REP_CAPACITY]' value='[sqlproxy].[REMAINING_REP_CAPACITY]' />
      <map key='[SCENARIO_DESCRIPTION]' value='[sqlproxy].[SCENARIO_DESCRIPTION]' />
      <map key='[SCENARIO_NAME]' value='[sqlproxy].[SCENARIO_NAME]' />
      <map key='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' value='[sqlproxy].[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' />
      <map key='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' value='[sqlproxy].[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
      <map key='[SCENARIO_UID]' value='[sqlproxy].[SCENARIO_UID]' />
      <map key='[SCENARIO_VER]' value='[sqlproxy].[SCENARIO_VER]' />
      <map key='[SEGMENTATION]' value='[sqlproxy].[SEGMENTATION]' />
      <map key='[SEGMENT_NAME]' value='[sqlproxy].[SEGMENT_NAME]' />
      <map key='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' value='[sqlproxy].[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' />
      <map key='[SEGMENT_TYPE]' value='[sqlproxy].[SEGMENT_TYPE]' />
      <map key='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' value='[sqlproxy].[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' />
      <map key='[SE_CONFIG_ID]' value='[sqlproxy].[SE_CONFIG_ID]' />
      <map key='[SE_CONFIG_NAME]' value='[sqlproxy].[SE_CONFIG_NAME]' />
      <map key='[STATUS]' value='[sqlproxy].[STATUS]' />
      <map key='[SUGGESTION_CANDIDATES]' value='[sqlproxy].[SUGGESTION_CANDIDATES]' />
      <map key='[SUGGESTION_COUNT]' value='[sqlproxy].[SUGGESTION_COUNT]' />
      <map key='[TIME_SPENT_PER_DAY]' value='[sqlproxy].[TIME_SPENT_PER_DAY]' />
      <map key='[UPDATED_TS]' value='[sqlproxy].[UPDATED_TS]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='Sum' datatype='real' default-type='quantitative' name='[ACTUALS_FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
  <column aggregation='Sum' caption='Actual Suggested' datatype='integer' default-type='quantitative' name='[ACTUALS_SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' datatype='real' default-type='quantitative' name='[CAPACITY_FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
  <column aggregation='Sum' caption='Capacity Suggested' datatype='integer' default-type='quantitative' name='[CAPACITY_SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' name='[COUNTRY_CODE]' pivot='key' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Created By (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' name='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Created By' datatype='string' default-type='nominal' name='[CREATED_BY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Created Ts (Vw Dim Segment Rpt)' datatype='datetime' default-type='ordinal' name='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Year' caption='Created Ts' datatype='datetime' default-type='ordinal' name='[CREATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Count' caption=' Scenario' datatype='string' default-type='nominal' name='[Calculation_1035546475303264257]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
  </column>
  <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' name='[Calculation_1394708532141854720]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&\#13;&\#10;WHEN &apos;(All)&apos; THEN 1&\#13;&\#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&\#13;&\#10;END' />
  </column>
  <column aggregation='User' caption='Calc_Sum_Sugg' datatype='integer' default-type='quantitative' name='[Calculation_184366111153672192]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='SUM([SUGGESTION_COUNT])' />
  </column>
  <column aggregation='Count' caption='Reasons' datatype='string' default-type='nominal' name='[Calculation_458311642422099972]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[DCO_REASON_CODE]+&apos;. &apos;+[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]' />
  </column>
  <column aggregation='Sum' caption='Fixed \%' datatype='real' default-type='quantitative' name='[Calculation_487936923659948084]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='{ FIXED [DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)], [Calculation_458311642422099972]: SUM([SUGGESTION_CANDIDATES])} &\#10;/ { FIXED[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]: SUM([SUGGESTION_CANDIDATES])}' />
  </column>
  <column aggregation='User' caption='calculate_suggestions' datatype='real' default-type='quantitative' name='[Calculation_597008439445168139]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='SUM([ACTUALS_SUGGESTION_COUNT])/COUNTD([DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)])' />
  </column>
  <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' name='[Channel (copy)_1319554694635393025]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&\#13;&\#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&\#13;&\#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Dco Reason Code' datatype='string' default-type='nominal' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Description' datatype='string' default-type='nominal' name='[DCO_REASON_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Name (Vw Dim Dco Reason Rpt)' datatype='string' default-type='nominal' name='[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Type Code' datatype='string' default-type='nominal' name='[DCO_REASON_TYPE_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Type Name' datatype='string' default-type='nominal' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Uid (Vw Dim Dco Reason Rpt)' datatype='string' default-type='nominal' name='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Date' datatype='date' default-type='ordinal' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Run Uid' datatype='string' default-type='nominal' name='[DCO_RUN_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Description' datatype='string' default-type='nominal' name='[DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Brand Key' datatype='integer' default-type='ordinal' name='[DIM_BRAND_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Country Key' datatype='integer' default-type='ordinal' name='[DIM_COUNTRY_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Customer Key' datatype='integer' default-type='ordinal' name='[DIM_CUSTOMER_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[DIM_HCP_SEGMENT_KEY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Product Key (Vw Dim Segment Rpt)' datatype='integer' default-type='ordinal' name='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Product Key' datatype='integer' default-type='ordinal' name='[DIM_PRODUCT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[DIM_SEGMENTATION_KEY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Segment Key' datatype='integer' default-type='ordinal' name='[DIM_SEGMENT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Segment Type Key' datatype='integer' default-type='ordinal' name='[DIM_SEGMENT_TYPE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Hcp Segment' datatype='string' default-type='nominal' name='[HCP_SEGMENT]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Is Active Src' datatype='boolean' default-type='nominal' name='[IS_ACTIVE_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Is Competitor' datatype='boolean' default-type='nominal' name='[IS_COMPETITOR]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Is Deleted Src' datatype='boolean' default-type='nominal' name='[IS_DELETED_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Latest Run' datatype='boolean' default-type='nominal' name='[LATEST_RUN]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[MAX_CAPACITY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='max_capacity' datatype='integer' default-type='ordinal' name='[MIN_CAPACITY (copy)_1245104568322998288]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='[MAX_CAPACITY]' />
  </column>
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[MIN_CAPACITY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Post Proc Status' datatype='string' default-type='nominal' name='[POST_PROC_STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Id' datatype='integer' default-type='ordinal' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Name English' datatype='string' default-type='nominal' name='[PRODUCT_NAME_ENGLISH]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Type' datatype='string' default-type='nominal' name='[PRODUCT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Uid (Vw Dim Product Dco Rpt)' datatype='string' default-type='nominal' name='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Uid (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' name='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Uid' datatype='string' default-type='nominal' name='[PRODUCT_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Record End Date' datatype='date' default-type='ordinal' name='[RECORD_END_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' caption='Record Start Date' datatype='date' default-type='ordinal' name='[RECORD_START_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-role='measure' default-type='ordinal' name='[REMAINING_REP_CAPACITY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Description' datatype='string' default-type='nominal' name='[SCENARIO_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Uid (Vw Dim Dco Run Rpt)' datatype='string' default-type='nominal' name='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Uid (Vw Dim Scenario Rpt)' datatype='string' default-type='nominal' name='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Uid' datatype='string' default-type='nominal' name='[SCENARIO_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Ver' datatype='string' default-type='nominal' name='[SCENARIO_VER]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment Name' datatype='string' default-type='nominal' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Se Config Id (Vw Dim Dse Config)' datatype='integer' default-type='ordinal' name='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Status' datatype='string' default-type='nominal' name='[STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[SUGGESTION_CANDIDATES]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' caption='Suggestion Count' datatype='integer' default-type='quantitative' name='[SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-role='measure' default-type='ordinal' name='[TIME_SPENT_PER_DAY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Updated Ts' datatype='datetime' default-type='ordinal' name='[UPDATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_F_OUTPUT_BY_CHANNEL_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_REASON_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_RUN_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DSE_CONFIG' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_C3DC8B6F54CC41FDA63028F1927001F4]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_PRODUCT_DCO_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SCENARIO_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_TYPE_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <column-instance column='[__tableau_internal_object_id__].[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19:qk]' pivot='key' type='quantitative' />
  <column-instance column='[__tableau_internal_object_id__].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752:qk]' pivot='key' type='quantitative' />
  <column-instance column='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB:qk]' pivot='key' type='quantitative' />
  <column-instance column='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E:qk]' pivot='key' type='quantitative' />
  <column-instance column='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC:qk]' pivot='key' type='quantitative' />
  <column-instance column='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4:qk]' pivot='key' type='quantitative' />
  <column-instance column='[ACTUALS_SUGGESTION_COUNT]' derivation='Attribute' name='[attr:ACTUALS_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[CAPACITY_SUGGESTION_COUNT]' derivation='Attribute' name='[attr:CAPACITY_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[SEGMENT_NAME]' derivation='Attribute' name='[attr:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
  <column-instance column='[SUGGESTION_COUNT]' derivation='Sum' name='[cum:sum:SUGGESTION_COUNT:qk:2]' pivot='key' type='quantitative'>
    <table-calc aggregation='Sum' ordering-field='[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[CHANNEL]' ordering-type='Field' type='CumTotal' />
  </column-instance>
  <column-instance column='[SUGGESTION_COUNT]' derivation='Sum' name='[cum:sum:SUGGESTION_COUNT:qk]' pivot='key' type='quantitative'>
    <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
  </column-instance>
  <column-instance column='[CAPACITY_SUGGESTION_COUNT]' derivation='Sum' name='[diff:sum:CAPACITY_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative'>
    <table-calc diff-options='Relative' ordering-type='Columns' type='Difference'>
      <address>
        <value>-1</value>
      </address>
    </table-calc>
  </column-instance>
  <column-instance column='[MAX_CAPACITY]' derivation='Max' name='[max:MAX_CAPACITY:qk]' pivot='key' type='quantitative' />
  <column-instance column='[MIN_CAPACITY]' derivation='Min' name='[min:MIN_CAPACITY:qk]' pivot='key' type='quantitative' />
  <column-instance column='[ACTUALS_SUGGESTION_COUNT]' derivation='None' name='[none:ACTUALS_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_458311642422099972]' derivation='None' name='[none:Calculation_458311642422099972:nk]' pivot='key' type='nominal' />
  <column-instance column='[DCO_REASON_NAME]' derivation='None' name='[none:DCO_REASON_NAME:nk]' pivot='key' type='nominal' />
  <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
  <column-instance column='[SUGGESTION_CANDIDATES]' derivation='Sum' name='[pcto:sum:SUGGESTION_CANDIDATES:qk:2]' pivot='key' type='quantitative'>
    <table-calc ordering-type='CellInPane' type='PctTotal' />
  </column-instance>
  <column-instance column='[SUGGESTION_CANDIDATES]' derivation='Sum' name='[pcto:sum:SUGGESTION_CANDIDATES:qk]' pivot='key' type='quantitative'>
    <table-calc ordering-type='Rows' type='PctTotal' />
  </column-instance>
  <column-instance column='[ACTUALS_FINAL_SCORE_SUM]' derivation='Sum' name='[sum:ACTUALS_FINAL_SCORE_SUM:qk]' pivot='key' type='quantitative' />
  <column-instance column='[ACTUALS_SUGGESTION_COUNT]' derivation='Sum' name='[sum:ACTUALS_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[CAPACITY_FINAL_SCORE_SUM]' derivation='Sum' name='[sum:CAPACITY_FINAL_SCORE_SUM:qk]' pivot='key' type='quantitative' />
  <column-instance column='[CAPACITY_SUGGESTION_COUNT]' derivation='Sum' name='[sum:CAPACITY_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[DIM_HCP_SEGMENT_KEY]' derivation='Sum' name='[sum:DIM_HCP_SEGMENT_KEY:qk]' pivot='key' type='quantitative' />
  <column-instance column='[DIM_SEGMENTATION_KEY]' derivation='Sum' name='[sum:DIM_SEGMENTATION_KEY:qk]' pivot='key' type='quantitative' />
  <column-instance column='[MAX_CAPACITY]' derivation='Sum' name='[sum:MAX_CAPACITY:qk]' pivot='key' type='quantitative' />
  <column-instance column='[MIN_CAPACITY]' derivation='Sum' name='[sum:MIN_CAPACITY:qk]' pivot='key' type='quantitative' />
  <column-instance column='[REMAINING_REP_CAPACITY]' derivation='Sum' name='[sum:REMAINING_REP_CAPACITY:qk]' pivot='key' type='quantitative' />
  <column-instance column='[SUGGESTION_CANDIDATES]' derivation='Sum' name='[sum:SUGGESTION_CANDIDATES:qk]' pivot='key' type='quantitative' />
  <column-instance column='[SUGGESTION_COUNT]' derivation='Sum' name='[sum:SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[TIME_SPENT_PER_DAY]' derivation='Sum' name='[sum:TIME_SPENT_PER_DAY:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_184366111153672192]' derivation='User' name='[usr:Calculation_184366111153672192:qk]' pivot='key' type='quantitative' />
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[:Measure Names]' type='palette'>
        <map to='\#499894'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[__tableau_internal_object_id__].[cnt:VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC:qk]&quot;</bucket>
        </map>
        <map to='\#4e79a7'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:ACTUALS_FINAL_SCORE_SUM:qk]&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[attr:ACTUALS_SUGGESTION_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[none:ACTUALS_SUGGESTION_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:ACTUALS_SUGGESTION_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:Calculation_359443548019593217:qk]&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:Calculation_359443548020027394:qk]&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:CAPACITY_SUGGESTION_COUNT:qk]:1&quot;</bucket>
        </map>
        <map to='\#79706e'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:DIM_HCP_SEGMENT_KEY:qk]&quot;</bucket>
        </map>
        <map to='\#86bcb6'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[__tableau_internal_object_id__].[cnt:VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4:qk]&quot;</bucket>
        </map>
        <map to='\#8cd17d'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[__tableau_internal_object_id__].[cnt:VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752:qk]&quot;</bucket>
        </map>
        <map to='\#9d7660'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[pcto:sum:SUGGESTION_CANDIDATES:qk:2]&quot;</bucket>
        </map>
        <map to='\#9d7660'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[pcto:sum:SUGGESTION_CANDIDATES:qk]&quot;</bucket>
        </map>
        <map to='\#9d7660'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:SUGGESTION_CANDIDATES:qk]&quot;</bucket>
        </map>
        <map to='\#a0cbe8'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:TIME_SPENT_PER_DAY:qk]&quot;</bucket>
        </map>
        <map to='\#b07aa1'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[min:MIN_CAPACITY:qk]&quot;</bucket>
        </map>
        <map to='\#b07aa1'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:MIN_CAPACITY:qk]&quot;</bucket>
        </map>
        <map to='\#b6992d'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[__tableau_internal_object_id__].[cnt:VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB:qk]&quot;</bucket>
        </map>
        <map to='\#bab0ac'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[attr:CAPACITY_SUGGESTION_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#bab0ac'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[diff:sum:CAPACITY_SUGGESTION_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#bab0ac'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:CAPACITY_SUGGESTION_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#d37295'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:DIM_SEGMENTATION_KEY:qk]&quot;</bucket>
        </map>
        <map to='\#d4a6c8'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:REMAINING_REP_CAPACITY:qk]&quot;</bucket>
        </map>
        <map to='\#d7b5a6'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[cum:sum:SUGGESTION_COUNT:qk:2]&quot;</bucket>
        </map>
        <map to='\#d7b5a6'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[cum:sum:SUGGESTION_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#d7b5a6'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:SUGGESTION_COUNT:qk]&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model]&quot;</bucket>
        </map>
        <map to='\#f1ce63'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[__tableau_internal_object_id__].[cnt:VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E:qk]&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:CAPACITY_FINAL_SCORE_SUM:qk]&quot;</bucket>
        </map>
        <map to='\#fabfd2'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[max:MAX_CAPACITY:qk]&quot;</bucket>
        </map>
        <map to='\#fabfd2'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[sum:MAX_CAPACITY:qk]&quot;</bucket>
        </map>
        <map to='\#ff9d9a'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[__tableau_internal_object_id__].[cnt:VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19:qk]&quot;</bucket>
        </map>
        <map to='\#ffbe7d'>
          <bucket>&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model].[usr:Calculation_184366111153672192:qk]&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[attr:SEGMENT_NAME:nk]' type='palette'>
        <map to='\#4e79a7'>
          <bucket>&quot;notier&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;T3&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;T2&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;T1&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;ST&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:DCO_REASON_NAME:nk]' type='palette'>
        <map to='\#4e79a7'>
          <bucket>\%null\%</bucket>
        </map>
        <map to='\#4e79a7'>
          <bucket>&quot;This action is high enough priority to justify using the available capacity for the channel and engagement bandwith for the account&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;4. No Required-cap&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;Unknown reason&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;7. Additional-cap accepted&quot;</bucket>
        </map>
        <map to='\#b07aa1'>
          <bucket>&quot;Account-level conflict&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;6. Required-cap accepted&quot;</bucket>
        </map>
        <map to='\#edc948'>
          <bucket>&quot;End user capacity&quot;</bucket>
        </map>
        <map to='\#edc948'>
          <bucket>&quot;NA&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;3. No RepCap&quot;</bucket>
        </map>
        <map to='\#ff9da7'>
          <bucket>&quot;Auto-expired&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:CHANNEL:nk]' type='palette'>
        <map to='\#4e79a7'>
          <bucket>&quot;SEND_CHANNEL&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;VISIT&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;SEND&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
        </map>
        <map to='\#edc948'>
          <bucket>&quot;WEB_EDETAIL&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_458311642422099972:nk]' type='palette'>
        <map to='\#4e79a7'>
          <bucket>&quot;06. Unknown reason&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;00. Recommended&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;01. Auto-expired&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;02. Account-level conflict&quot;</bucket>
        </map>
        <map to='\#edc948'>
          <bucket>&quot;05. Channel capacity&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;04. End user capacity&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:SEGMENT_NAME:nk]' type='palette'>
        <map to='\#4e79a7'>
          <bucket>&quot;notier&quot;</bucket>
        </map>
        <map to='\#59a14f'>
          <bucket>&quot;T3&quot;</bucket>
        </map>
        <map to='\#76b7b2'>
          <bucket>&quot;T2&quot;</bucket>
        </map>
        <map to='\#e15759'>
          <bucket>&quot;T1&quot;</bucket>
        </map>
        <map to='\#f28e2b'>
          <bucket>&quot;ST&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <datasource-dependencies datasource='Parameters'>
    <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
      <calculation class='tableau' formula='&quot;Channel&quot;' />
      <members>
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
      <calculation class='tableau' formula='&quot;Published&quot;' />
      <members>
        <member value='&quot;(All)&quot;' />
        <member value='&quot;Published&quot;' />
      </members>
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='VW_F_OUTPUT_BY_CHANNEL_RPT' id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DCO_REASON_RPT' id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DCO_RUN_RPT' id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DSE_CONFIG' id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_C3DC8B6F54CC41FDA63028F1927001F4'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_PRODUCT_DCO_RPT' id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SCENARIO_RPT' id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SEGMENT_RPT' id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SEGMENT_TYPE_RPT' id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
    <relationships>
      <relationship>
        <expression op='='>
          <expression op='[DCO_REASON_UID]' />
          <expression op='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
        </expression>
        <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
        <second-end-point object-id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DCO_RUN_UID (VW_F_OUTPUT_BY_CHANNEL_RPT)]' />
          <expression op='[DCO_RUN_UID]' />
        </expression>
        <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
        <second-end-point object-id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SE_CONFIG_ID]' />
          <expression op='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' />
        </expression>
        <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
        <second-end-point object-id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_C3DC8B6F54CC41FDA63028F1927001F4' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[PRODUCT_UID]' />
          <expression op='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
        <second-end-point object-id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SCENARIO_UID]' />
          <expression op='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
        <second-end-point object-id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[HCP_SEGMENT]' />
          <expression op='[SEGMENT_NAME]' />
        </expression>
        <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
        <second-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SEGMENT_TYPE]' />
          <expression op='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' />
        </expression>
        <first-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC' />
        <second-end-point object-id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4' />
      </relationship>
    </relationships>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='snowflake' version='0.0.0'>
  </primary-dialect>
  <overlay-dialect-set>
    <overlay-dialect class='vizengine' version='0.1.0'>
    </overlay-dialect>
  </overlay-dialect-set>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>false</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='sunday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='Sum' datatype='real' default-type='quantitative' layered='true' name='[ACTUALS_FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
      <column aggregation='Sum' caption='Actual Suggested' datatype='integer' default-type='quantitative' layered='true' name='[ACTUALS_SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='real' default-type='quantitative' layered='true' name='[CAPACITY_FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
      <column aggregation='Sum' caption='Capacity Suggested' datatype='integer' default-type='quantitative' layered='true' name='[CAPACITY_SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[COUNTRY_CODE]' pivot='key' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Created By (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' layered='true' name='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Created By' datatype='string' default-type='nominal' layered='true' name='[CREATED_BY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' caption='Created Ts (Vw Dim Segment Rpt)' datatype='datetime' default-type='ordinal' layered='true' name='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Year' caption='Created Ts' datatype='datetime' default-type='ordinal' layered='true' name='[CREATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Count' caption=' Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_1035546475303264257]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
      </column>
      <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1394708532141854720]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
      </column>
      <column aggregation='User' caption='Calc_Sum_Sugg' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_184366111153672192]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='SUM([SUGGESTION_COUNT])' />
      </column>
      <column aggregation='Count' caption='Reasons' datatype='string' default-type='nominal' layered='true' name='[Calculation_458311642422099972]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[DCO_REASON_CODE]+&apos;. &apos;+[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]' />
      </column>
      <column aggregation='Sum' caption='Fixed %' datatype='real' default-type='quantitative' layered='true' name='[Calculation_487936923659948084]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='{ FIXED [DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)], [Calculation_458311642422099972]: SUM([SUGGESTION_CANDIDATES])} &#10;/ { FIXED[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]: SUM([SUGGESTION_CANDIDATES])}' />
      </column>
      <column aggregation='User' caption='calculate_suggestions' datatype='real' default-type='quantitative' layered='true' name='[Calculation_597008439445168139]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='SUM([ACTUALS_SUGGESTION_COUNT])/COUNTD([DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)])' />
      </column>
      <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' layered='true' name='[Channel (copy)_1319554694635393025]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Dco Reason Code' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Description' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Name (Vw Dim Dco Reason Rpt)' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Type Code' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Type Name' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Uid (Vw Dim Dco Reason Rpt)' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' caption='Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_RUN_UID (VW_F_OUTPUT_BY_CHANNEL_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Run Uid' datatype='string' default-type='nominal' layered='true' name='[DCO_RUN_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Description' datatype='string' default-type='nominal' layered='true' name='[DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Brand Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_BRAND_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Country Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_COUNTRY_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Customer Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_CUSTOMER_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[DIM_HCP_SEGMENT_KEY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Product Key (Vw Dim Segment Rpt)' datatype='integer' default-type='ordinal' layered='true' name='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Product Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_PRODUCT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[DIM_SEGMENTATION_KEY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Segment Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_SEGMENT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Segment Type Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_SEGMENT_TYPE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Hcp Segment' datatype='string' default-type='nominal' layered='true' name='[HCP_SEGMENT]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Is Active Src' datatype='boolean' default-type='nominal' layered='true' name='[IS_ACTIVE_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' caption='Is Competitor' datatype='boolean' default-type='nominal' layered='true' name='[IS_COMPETITOR]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' caption='Is Deleted Src' datatype='boolean' default-type='nominal' layered='true' name='[IS_DELETED_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[LAST_DCO_RUN_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' datatype='real' default-type='quantitative' layered='true' name='[LAST_SCORE]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
      <column aggregation='Count' caption='Latest Run' datatype='boolean' default-type='nominal' layered='true' name='[LATEST_RUN]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[MAX_CAPACITY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='max_capacity' datatype='integer' default-type='ordinal' layered='true' name='[MIN_CAPACITY (copy)_1245104568322998288]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='[MAX_CAPACITY]' />
      </column>
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[MIN_CAPACITY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Post Proc Status' datatype='string' default-type='nominal' layered='true' name='[POST_PROC_STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Id' datatype='integer' default-type='ordinal' layered='true' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Name English' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME_ENGLISH]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Type' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Uid (Vw Dim Product Dco Rpt)' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Uid (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Uid' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[RECOMMENDED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Year' caption='Record End Date' datatype='date' default-type='ordinal' layered='true' name='[RECORD_END_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' caption='Record Start Date' datatype='date' default-type='ordinal' layered='true' name='[RECORD_START_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-role='measure' default-type='ordinal' layered='true' name='[REMAINING_REP_CAPACITY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Description' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Uid (Vw Dim Dco Run Rpt)' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Uid (Vw Dim Scenario Rpt)' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Uid' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Ver' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_VER]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SEGMENTATION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment Name' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Se Config Id (Vw Dim Dse Config)' datatype='integer' default-type='ordinal' layered='true' name='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SE_CONFIG_ID]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Status' datatype='string' default-type='nominal' layered='true' name='[STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTION_CANDIDATES]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' caption='Suggestion Count' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-role='measure' default-type='ordinal' layered='true' name='[TIME_SPENT_PER_DAY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' caption='Updated Ts' datatype='datetime' default-type='ordinal' layered='true' name='[UPDATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_F_OUTPUT_BY_CHANNEL_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_REASON_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_RUN_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DSE_CONFIG' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_C3DC8B6F54CC41FDA63028F1927001F4]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_PRODUCT_DCO_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SCENARIO_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_TYPE_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <column-instance column='[__tableau_internal_object_id__].[VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19:qk]' pivot='key' type='quantitative' />
      <column-instance column='[__tableau_internal_object_id__].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752:qk]' pivot='key' type='quantitative' />
      <column-instance column='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB:qk]' pivot='key' type='quantitative' />
      <column-instance column='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E:qk]' pivot='key' type='quantitative' />
      <column-instance column='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC:qk]' pivot='key' type='quantitative' />
      <column-instance column='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4:qk]' pivot='key' type='quantitative' />
      <column-instance column='[ACTUALS_SUGGESTION_COUNT]' derivation='Attribute' name='[attr:ACTUALS_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[CAPACITY_SUGGESTION_COUNT]' derivation='Attribute' name='[attr:CAPACITY_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[SEGMENT_NAME]' derivation='Attribute' name='[attr:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
      <column-instance column='[SUGGESTION_COUNT]' derivation='Sum' name='[cum:sum:SUGGESTION_COUNT:qk:2]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[CHANNEL]' ordering-type='Field' type='CumTotal' />
      </column-instance>
      <column-instance column='[SUGGESTION_COUNT]' derivation='Sum' name='[cum:sum:SUGGESTION_COUNT:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
      </column-instance>
      <column-instance column='[CAPACITY_SUGGESTION_COUNT]' derivation='Sum' name='[diff:sum:CAPACITY_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative'>
        <table-calc diff-options='Relative' ordering-type='Columns' type='Difference'>
          <address>
            <value>-1</value>
          </address>
        </table-calc>
      </column-instance>
      <column-instance column='[MAX_CAPACITY]' derivation='Max' name='[max:MAX_CAPACITY:qk]' pivot='key' type='quantitative' />
      <column-instance column='[MIN_CAPACITY]' derivation='Min' name='[min:MIN_CAPACITY:qk]' pivot='key' type='quantitative' />
      <column-instance column='[ACTUALS_SUGGESTION_COUNT]' derivation='None' name='[none:ACTUALS_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
      <column-instance column='[Calculation_458311642422099972]' derivation='None' name='[none:Calculation_458311642422099972:nk]' pivot='key' type='nominal' />
      <column-instance column='[DCO_REASON_NAME]' derivation='None' name='[none:DCO_REASON_NAME:nk]' pivot='key' type='nominal' />
      <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
      <column-instance column='[SUGGESTION_CANDIDATES]' derivation='Sum' name='[pcto:sum:SUGGESTION_CANDIDATES:qk:2]' pivot='key' type='quantitative'>
        <table-calc ordering-type='CellInPane' type='PctTotal' />
      </column-instance>
      <column-instance column='[SUGGESTION_CANDIDATES]' derivation='Sum' name='[pcto:sum:SUGGESTION_CANDIDATES:qk]' pivot='key' type='quantitative'>
        <table-calc ordering-type='Rows' type='PctTotal' />
      </column-instance>
      <column-instance column='[ACTUALS_FINAL_SCORE_SUM]' derivation='Sum' name='[sum:ACTUALS_FINAL_SCORE_SUM:qk]' pivot='key' type='quantitative' />
      <column-instance column='[ACTUALS_SUGGESTION_COUNT]' derivation='Sum' name='[sum:ACTUALS_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[CAPACITY_FINAL_SCORE_SUM]' derivation='Sum' name='[sum:CAPACITY_FINAL_SCORE_SUM:qk]' pivot='key' type='quantitative' />
      <column-instance column='[CAPACITY_SUGGESTION_COUNT]' derivation='Sum' name='[sum:CAPACITY_SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[DIM_HCP_SEGMENT_KEY]' derivation='Sum' name='[sum:DIM_HCP_SEGMENT_KEY:qk]' pivot='key' type='quantitative' />
      <column-instance column='[DIM_SEGMENTATION_KEY]' derivation='Sum' name='[sum:DIM_SEGMENTATION_KEY:qk]' pivot='key' type='quantitative' />
      <column-instance column='[MAX_CAPACITY]' derivation='Sum' name='[sum:MAX_CAPACITY:qk]' pivot='key' type='quantitative' />
      <column-instance column='[MIN_CAPACITY]' derivation='Sum' name='[sum:MIN_CAPACITY:qk]' pivot='key' type='quantitative' />
      <column-instance column='[REMAINING_REP_CAPACITY]' derivation='Sum' name='[sum:REMAINING_REP_CAPACITY:qk]' pivot='key' type='quantitative' />
      <column-instance column='[SUGGESTION_CANDIDATES]' derivation='Sum' name='[sum:SUGGESTION_CANDIDATES:qk]' pivot='key' type='quantitative' />
      <column-instance column='[SUGGESTION_COUNT]' derivation='Sum' name='[sum:SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[TIME_SPENT_PER_DAY]' derivation='Sum' name='[sum:TIME_SPENT_PER_DAY:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_184366111153672192]' derivation='User' name='[usr:Calculation_184366111153672192:qk]' pivot='key' type='quantitative' />
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[:Measure Names]' type='palette'>
            <map to='#499894'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[__tableau_internal_object_id__].[cnt:VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC:qk]&quot;</bucket>
            </map>
            <map to='#4e79a7'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:ACTUALS_FINAL_SCORE_SUM:qk]&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[attr:ACTUALS_SUGGESTION_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:ACTUALS_SUGGESTION_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:ACTUALS_SUGGESTION_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:Calculation_359443548019593217:qk]&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:Calculation_359443548020027394:qk]&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:CAPACITY_SUGGESTION_COUNT:qk]:1&quot;</bucket>
            </map>
            <map to='#79706e'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:DIM_HCP_SEGMENT_KEY:qk]&quot;</bucket>
            </map>
            <map to='#86bcb6'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[__tableau_internal_object_id__].[cnt:VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4:qk]&quot;</bucket>
            </map>
            <map to='#8cd17d'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[__tableau_internal_object_id__].[cnt:VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752:qk]&quot;</bucket>
            </map>
            <map to='#9d7660'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[pcto:sum:SUGGESTION_CANDIDATES:qk:2]&quot;</bucket>
            </map>
            <map to='#9d7660'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[pcto:sum:SUGGESTION_CANDIDATES:qk]&quot;</bucket>
            </map>
            <map to='#9d7660'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_CANDIDATES:qk]&quot;</bucket>
            </map>
            <map to='#a0cbe8'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:TIME_SPENT_PER_DAY:qk]&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[min:MIN_CAPACITY:qk]&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:MIN_CAPACITY:qk]&quot;</bucket>
            </map>
            <map to='#b6992d'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[__tableau_internal_object_id__].[cnt:VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB:qk]&quot;</bucket>
            </map>
            <map to='#bab0ac'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[attr:CAPACITY_SUGGESTION_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#bab0ac'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[diff:sum:CAPACITY_SUGGESTION_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#bab0ac'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:CAPACITY_SUGGESTION_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#d37295'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:DIM_SEGMENTATION_KEY:qk]&quot;</bucket>
            </map>
            <map to='#d4a6c8'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:REMAINING_REP_CAPACITY:qk]&quot;</bucket>
            </map>
            <map to='#d7b5a6'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[cum:sum:SUGGESTION_COUNT:qk:2]&quot;</bucket>
            </map>
            <map to='#d7b5a6'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[cum:sum:SUGGESTION_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#d7b5a6'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_COUNT:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l]&quot;</bucket>
            </map>
            <map to='#f1ce63'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[__tableau_internal_object_id__].[cnt:VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:CAPACITY_FINAL_SCORE_SUM:qk]&quot;</bucket>
            </map>
            <map to='#fabfd2'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[max:MAX_CAPACITY:qk]&quot;</bucket>
            </map>
            <map to='#fabfd2'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:MAX_CAPACITY:qk]&quot;</bucket>
            </map>
            <map to='#ff9d9a'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[__tableau_internal_object_id__].[cnt:VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19:qk]&quot;</bucket>
            </map>
            <map to='#ffbe7d'>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_184366111153672192:qk]&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[attr:SEGMENT_NAME:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;notier&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;T3&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;T2&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;T1&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;ST&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:DCO_REASON_NAME:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>%null%</bucket>
            </map>
            <map to='#4e79a7'>
              <bucket>&quot;This action is high enough priority to justify using the available capacity for the channel and engagement bandwith for the account&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;4. No Required-cap&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;Unknown reason&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;7. Additional-cap accepted&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;Account-level conflict&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;6. Required-cap accepted&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;End user capacity&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;3. No RepCap&quot;</bucket>
            </map>
            <map to='#ff9da7'>
              <bucket>&quot;Auto-expired&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:CHANNEL:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;SEND_CHANNEL&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;VISIT&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;SEND&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;WEB_EDETAIL&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:SEGMENT_NAME:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;notier&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;T3&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;T2&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;T1&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;ST&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_458311642422099972:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;06. Unknown reason&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;00. Recommended&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;01. Auto-expired&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;02. Account-level conflict&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;05. Channel capacity&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;04. End user capacity&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
          <calculation class='tableau' formula='&quot;Channel&quot;' />
        </column>
        <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
          <calculation class='tableau' formula='&quot;Published&quot;' />
        </column>
      </datasource-dependencies>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='VW_F_OUTPUT_BY_CHANNEL_RPT' id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_DCO_REASON_RPT' id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_DCO_RUN_RPT' id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_DSE_CONFIG' id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_C3DC8B6F54CC41FDA63028F1927001F4'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_PRODUCT_DCO_RPT' id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SCENARIO_RPT' id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SEGMENT_RPT' id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SEGMENT_TYPE_RPT' id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
        <relationships>
          <relationship>
            <expression op='='>
              <expression op='[DCO_REASON_UID]' />
              <expression op='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
            </expression>
            <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
            <second-end-point object-id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_C0748877B0654761969F1844735FFE22' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[DCO_RUN_UID (VW_F_OUTPUT_BY_CHANNEL_RPT)]' />
              <expression op='[DCO_RUN_UID]' />
            </expression>
            <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
            <second-end-point object-id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_2DD91DAF26A34706B951B008DE175752' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SE_CONFIG_ID]' />
              <expression op='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' />
            </expression>
            <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
            <second-end-point object-id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_C3DC8B6F54CC41FDA63028F1927001F4' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[PRODUCT_UID]' />
              <expression op='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
            <second-end-point object-id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_E80065D697184E4DBDE04035AD1703AB' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SCENARIO_UID]' />
              <expression op='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
            <second-end-point object-id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_CF5283EDC3B846B49101439D2B4E750E' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[HCP_SEGMENT]' />
              <expression op='[SEGMENT_NAME]' />
            </expression>
            <first-end-point object-id='VW_DCO_RESULTS_LATEST_SUMMARY_RPT (DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT)_1C0771D503FA4AF5854D3B198E27FA19' />
            <second-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SEGMENT_TYPE]' />
              <expression op='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' />
            </expression>
            <first-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_B0A9C178F491484E8D167B061A0CC7EC' />
            <second-end-point object-id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_515DB4B67503473F81600A5327C210F4' />
          </relationship>
        </relationships>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
  </datasources>
  <shared-views>
    <shared-view name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l'>
      <datasources>
        <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l' />
      </datasources>
      <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l'>
        <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]' context='true'>
        <groupfilter function='level-members' level='[none:SE_CONFIG_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
    </shared-view>
  </shared-views>
  <worksheets>
    <worksheet name='1.0 View Filters'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontname='Tableau Medium' fontsize='15'>${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs  </run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l'>
            <column aggregation='Count' caption=' Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_1035546475303264257]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1394708532141854720]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Name' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='Suggestion Count' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column-instance column='[Calculation_1035546475303264257]' derivation='None' name='[none:Calculation_1035546475303264257:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1394708532141854720]' derivation='None' name='[none:Calculation_1394708532141854720:ok]' pivot='key' type='ordinal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_COUNT]' derivation='Sum' name='[sum:SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]' context='true' filter-group='9'>
            <groupfilter function='level-members' level='[none:Calculation_1035546475303264257:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]' context='true' filter-group='5'>
            <groupfilter function='member' level='[none:Calculation_1394708532141854720:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]' context='true' filter-group='6' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]' context='true' filter-group='8'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' context='true' filter-group='4'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='mark'>
            <encoding attr='color' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_COUNT:qk]' type='custom-interpolated'>
              <color-palette custom='true' name='' type='ordered-sequential'>
                <color>#f1f1f1</color>
                <color>#f2f2f2</color>
                <color>#f3f3f3</color>
                <color>#f5f5f5</color>
                <color>#f6f6f6</color>
                <color>#f8f8f8</color>
                <color>#f9f9f9</color>
                <color>#fafafa</color>
                <color>#fcfcfc</color>
                <color>#fdfdfd</color>
                <color>#ffffff</color>
              </color-palette>
            </encoding>
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' value='Segment Type'>
              <formatted-text>
                <run>Segment Type</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' value=' Segment'>
              <formatted-text>
                <run> Segment</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]' value='Product'>
              <formatted-text>
                <run>Product</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]' value='Run Date Range'>
              <formatted-text>
                <run>Run Date Range</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]' value=' Scenario'>
              <formatted-text>
                <run> Scenario</run>
              </formatted-text>
            </format>
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_COUNT:qk]' />
            </encodings>
          </pane>
        </panes>
        <rows />
        <cols />
      </table>
      <simple-id uuid='{85CE5F32-0150-4F7B-85BB-73BA1372AEFF}' />
    </worksheet>
    <worksheet name='1.1 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Channel'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontname='Tableau Medium' fontsize='12'>${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Channel</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l'>
            <column aggregation='Sum' caption='Actual Suggested' datatype='integer' default-type='quantitative' layered='true' name='[ACTUALS_SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption=' Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_1035546475303264257]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1394708532141854720]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='User' caption='calculate_suggestions' datatype='real' default-type='quantitative' layered='true' name='[Calculation_597008439445168139]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='SUM([ACTUALS_SUGGESTION_COUNT])/COUNTD([DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)])' />
            </column>
            <column aggregation='Year' caption='Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[MAX_CAPACITY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='max_capacity' datatype='integer' default-type='ordinal' layered='true' name='[MIN_CAPACITY (copy)_1245104568322998288]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='[MAX_CAPACITY]' />
            </column>
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[MIN_CAPACITY]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Name' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Status' datatype='string' default-type='nominal' layered='true' name='[STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[MAX_CAPACITY]' derivation='Max' name='[max:MAX_CAPACITY:qk]' pivot='key' type='quantitative' />
            <column-instance column='[MIN_CAPACITY (copy)_1245104568322998288]' derivation='Max' name='[max:MIN_CAPACITY (copy)_1245104568322998288:qk]' pivot='key' type='quantitative' />
            <column-instance column='[MIN_CAPACITY]' derivation='Min' name='[min:MIN_CAPACITY:qk]' pivot='key' type='quantitative' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1035546475303264257]' derivation='None' name='[none:Calculation_1035546475303264257:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1394708532141854720]' derivation='None' name='[none:Calculation_1394708532141854720:ok]' pivot='key' type='ordinal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DS_NAME]' derivation='None' name='[none:DS_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[STATUS]' derivation='None' name='[none:STATUS:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_597008439445168139]' derivation='User' name='[usr:Calculation_597008439445168139:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[:Measure Names]'>
            <groupfilter function='union' user:op='manual'>
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[max:MIN_CAPACITY (copy)_1245104568322998288:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[min:MIN_CAPACITY:qk]&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]' context='true' filter-group='9'>
            <groupfilter function='level-members' level='[none:Calculation_1035546475303264257:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]' context='true' filter-group='5'>
            <groupfilter function='member' level='[none:Calculation_1394708532141854720:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]' context='true' filter-group='6' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DS_NAME:nk]'>
            <groupfilter function='member' level='[none:DS_NAME:nk]' member='&quot;DS_1&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]' context='true' filter-group='8'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' context='true' filter-group='4'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:STATUS:nk]'>
            <groupfilter function='member' level='[none:STATUS:nk]' member='&quot;RunCompleted&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DS_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[:Measure Names]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:STATUS:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]' scope='rows' value='Recommended Count' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[max:MIN_CAPACITY (copy)_1245104568322998288:qk]' value='n!en_US!#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[min:MIN_CAPACITY:qk]' value='n!en_US!#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]' value='n!en_US!#,##0' />
          </style-rule>
          <style-rule element='header'>
            <format attr='height-header' value='18' />
          </style-rule>
          <style-rule element='field-labels-decoration'>
            <format attr='font-style' value='normal' />
            <format attr='font-weight' value='normal' />
            <format attr='text-orientation' value='0' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-orientation' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]' value='0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[max:MIN_CAPACITY (copy)_1245104568322998288:qk]' value='n!en_US!#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[min:MIN_CAPACITY:qk]' value='n!en_US!#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]' value='n!en_US!#,##0' />
            <format attr='display' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]' value='true' />
          </style-rule>
          <style-rule element='mark'>
            <encoding attr='color' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[max:MIN_CAPACITY (copy)_1245104568322998288:qk]' min='0' num-steps='2' palette='gold_purple_diverging_10_0' type='interpolated' />
          </style-rule>
          <style-rule element='caption'>
            <format attr='border-color' value='#f1f3f8' />
            <format attr='border-width' value='0' />
            <format attr='border-style' value='none' />
          </style-rule>
          <style-rule element='title'>
            <format attr='border-color' value='#969696' />
            <format attr='border-width' value='0' />
            <format attr='border-style' value='none' />
            <format attr='background-color' value='#f1f3f8' />
          </style-rule>
          <style-rule element='legend'>
            <format attr='font-size' value='8' />
          </style-rule>
          <style-rule element='legend-title'>
            <format attr='text-align' value='left' />
          </style-rule>
          <style-rule element='legend-title-text'>
            <format attr='color' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' value='Segments' />
          </style-rule>
        </style>
        <panes>
          <pane id='2' selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[min:MIN_CAPACITY:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[max:MAX_CAPACITY:qk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878'>Channel:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Segment:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Recommended Count: &#9;</run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Max capacity:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[max:MAX_CAPACITY:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Min capacity:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[min:MIN_CAPACITY:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='datalabel'>
                <format attr='color-mode' value='user' />
                <format attr='color' value='#333333' />
              </style-rule>
              <style-rule element='mark'>
                <format attr='mark-labels-cull' value='true' />
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-transparency' value='255' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]</cols>
      </table>
      <simple-id uuid='{4E89E27E-1FFB-4D52-8D0D-B5063E49C218}' />
    </worksheet>
    <worksheet name='1.2 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Segment'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontname='Tableau Medium' fontsize='12'>${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Segment</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l'>
            <column aggregation='Sum' caption='Actual Suggested' datatype='integer' default-type='quantitative' layered='true' name='[ACTUALS_SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption=' Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_1035546475303264257]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1394708532141854720]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='User' caption='calculate_suggestions' datatype='real' default-type='quantitative' layered='true' name='[Calculation_597008439445168139]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='SUM([ACTUALS_SUGGESTION_COUNT])/COUNTD([DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)])' />
            </column>
            <column aggregation='Year' caption='Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Name' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1035546475303264257]' derivation='None' name='[none:Calculation_1035546475303264257:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1394708532141854720]' derivation='None' name='[none:Calculation_1394708532141854720:ok]' pivot='key' type='ordinal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DS_NAME]' derivation='None' name='[none:DS_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_597008439445168139]' derivation='User' name='[usr:Calculation_597008439445168139:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]' context='true' filter-group='9'>
            <groupfilter function='level-members' level='[none:Calculation_1035546475303264257:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]' context='true' filter-group='5'>
            <groupfilter function='member' level='[none:Calculation_1394708532141854720:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]' context='true' filter-group='6' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DS_NAME:nk]'>
            <groupfilter function='member' level='[none:DS_NAME:nk]' member='&quot;DS_2&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]' context='true' filter-group='8'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' context='true' filter-group='4'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DS_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]' scope='rows' value='Recommended Count' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]' value='n!en_US!#,##0' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-orientation' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]' value='-90' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]' value='n!en_US!#,##0' />
            <format attr='display' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' value='true' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#f1f3f8' />
          </style-rule>
          <style-rule element='legend'>
            <format attr='font-size' value='8' />
          </style-rule>
          <style-rule element='legend-title-text'>
            <format attr='color' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]' value='Channels' />
          </style-rule>
        </style>
        <panes>
          <pane id='6' selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878'>Channel:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Segment:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Recommended Count: </run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_597008439445168139:qk]</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</cols>
      </table>
      <simple-id uuid='{C58EDDD9-522D-4CCA-B042-414DDE0D1BC5}' />
    </worksheet>
    <worksheet name='1.3 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs Over TIme'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontname='Tableau Medium' fontsize='12'>${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs Over Time</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
              <calculation class='tableau' formula='&quot;Channel&quot;' />
            </column>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l'>
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption=' Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_1035546475303264257]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1394708532141854720]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' layered='true' name='[Channel (copy)_1319554694635393025]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
            </column>
            <column aggregation='Year' caption='Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Name' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='Suggestion Count' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column-instance column='[Calculation_1035546475303264257]' derivation='None' name='[none:Calculation_1035546475303264257:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1394708532141854720]' derivation='None' name='[none:Calculation_1394708532141854720:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Channel (copy)_1319554694635393025]' derivation='None' name='[none:Channel (copy)_1319554694635393025:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DS_NAME]' derivation='None' name='[none:DS_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_COUNT]' derivation='Sum' name='[sum:SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]' context='true' filter-group='9'>
            <groupfilter function='level-members' level='[none:Calculation_1035546475303264257:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]' context='true' filter-group='5'>
            <groupfilter function='member' level='[none:Calculation_1394708532141854720:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]' context='true' filter-group='6' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DS_NAME:nk]'>
            <groupfilter function='member' level='[none:DS_NAME:nk]' member='&quot;DS_3&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]' context='true' filter-group='8'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' context='true' filter-group='4'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DS_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):qk]' scope='cols' value='' />
            <format attr='title' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_COUNT:qk]' scope='rows' value='' />
            <encoding attr='space' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_COUNT:qk]' field-type='quantitative' range-type='independent' scope='rows' type='space' />
          </style-rule>
          <style-rule element='header'>
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Channel (copy)_1319554694635393025:nk]' value='140' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):qk]' value='*m/d/yyyy' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#f1f3f8' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878'>Run Date:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Recommended Count:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_COUNT:qk]>]]></run>
                <run>Æ&#10;</run>
                <run bold='true' fontcolor='#969696'>Segment:</run>
                <run bold='true'><![CDATA[	<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]>]]></run>
              </formatted-text>
            </customized-tooltip>
          </pane>
        </panes>
        <rows>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Channel (copy)_1319554694635393025:nk] * [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_COUNT:qk])</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):qk]</cols>
      </table>
      <simple-id uuid='{DF12CD2B-4094-4186-A412-7A15E03DBCFF}' />
    </worksheet>
    <worksheet name='1.4 CIE Candidates by Stage'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontname='Tableau Medium' fontsize='12'>CIE Candidates by Stage</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
              <calculation class='tableau' formula='&quot;Channel&quot;' />
            </column>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l'>
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption=' Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_1035546475303264257]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1394708532141854720]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Reasons' datatype='string' default-type='nominal' layered='true' name='[Calculation_458311642422099972]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[DCO_REASON_CODE]+&apos;. &apos;+[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]' />
            </column>
            <column caption='SUM([SUGGESTION_CANDIDATES]) / TOTAL(SUM([SUGGESTION_CANDIDAT...' datatype='real' name='[Calculation_477733427793936386]' role='measure' type='quantitative' user:unnamed='1.4 Suggestion Reasons'>
              <calculation class='tableau' formula='SUM([SUGGESTION_CANDIDATES]) / TOTAL(SUM([SUGGESTION_CANDIDATES]))'>
                <table-calc ordering-type='Rows' />
              </calculation>
            </column>
            <column aggregation='Sum' caption='Fixed %' datatype='real' default-type='quantitative' layered='true' name='[Calculation_487936923659948084]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='{ FIXED [DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)], [Calculation_458311642422099972]: SUM([SUGGESTION_CANDIDATES])} &#10;/ { FIXED[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]: SUM([SUGGESTION_CANDIDATES])}' />
            </column>
            <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' layered='true' name='[Channel (copy)_1319554694635393025]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Dco Reason Code' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Dco Reason Description' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Dco Reason Name (Vw Dim Dco Reason Rpt)' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Name' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTION_CANDIDATES]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column-instance column='[DCO_REASON_DESCRIPTION]' derivation='Attribute' name='[attr:DCO_REASON_DESCRIPTION:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1035546475303264257]' derivation='None' name='[none:Calculation_1035546475303264257:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1394708532141854720]' derivation='None' name='[none:Calculation_1394708532141854720:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_458311642422099972]' derivation='None' name='[none:Calculation_458311642422099972:nk]' pivot='key' type='nominal' />
            <column-instance column='[Channel (copy)_1319554694635393025]' derivation='None' name='[none:Channel (copy)_1319554694635393025:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_REASON_NAME]' derivation='None' name='[none:DCO_REASON_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DS_NAME]' derivation='None' name='[none:DS_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_487936923659948084]' derivation='Sum' name='[sum:Calculation_487936923659948084:qk]' pivot='key' type='quantitative' />
            <column-instance column='[SUGGESTION_CANDIDATES]' derivation='Sum' name='[sum:SUGGESTION_CANDIDATES:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_477733427793936386]' derivation='User' name='[usr:Calculation_477733427793936386:qk:2]' pivot='key' type='quantitative'>
              <table-calc ordering-type='CellInPane' />
            </column-instance>
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]' context='true' filter-group='9'>
            <groupfilter function='level-members' level='[none:Calculation_1035546475303264257:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]' context='true' filter-group='5'>
            <groupfilter function='member' level='[none:Calculation_1394708532141854720:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]'>
            <groupfilter function='level-members' level='[none:Calculation_458311642422099972:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <natural-sort column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]' direction='DESC' />
          <manual-sort column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_REASON_NAME:nk]' direction='ASC'>
            <dictionary>
              <bucket>&quot;Auto-snoozed&quot;</bucket>
              <bucket>&quot;Insufficient User capacity&quot;</bucket>
              <bucket>&quot;Recommended in first-pass&quot;</bucket>
              <bucket>&quot;Recommended in second-pass&quot;</bucket>
            </dictionary>
          </manual-sort>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]' context='true' filter-group='6' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DS_NAME:nk]' context='true'>
            <groupfilter function='member' level='[none:DS_NAME:nk]' member='&quot;DS_4&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]' context='true' filter-group='8'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' context='true' filter-group='4'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DS_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_477733427793936386:qk:2]' scope='rows' value='' />
            <format attr='display' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_477733427793936386:qk:2]' scope='rows' value='false' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:Calculation_487936923659948084:qk]' value='p0.0%' />
          </style-rule>
          <style-rule element='header'>
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Channel (copy)_1319554694635393025:nk]' value='34' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' value='S' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:Calculation_487936923659948084:qk]' value='p0.0%' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='title'>
            <format attr='border-color' value='#f1f3f8' />
            <format attr='border-width' value='0' />
            <format attr='border-style' value='none' />
            <format attr='background-color' value='#f1f3f8' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]' value='Outcome Code'>
              <formatted-text>
                <run>Outcome Code</run>
              </formatted-text>
            </format>
          </style-rule>
        </style>
        <panes>
          <pane id='3' selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[attr:DCO_REASON_DESCRIPTION:nk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_REASON_NAME:nk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_CANDIDATES:qk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:Calculation_487936923659948084:qk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878' fontname='Tableau Book' fontsize='10'>Outcome Code</run>
                <run fontcolor='#787878'>: &#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Reason Description:&#9;</run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[attr:DCO_REASON_DESCRIPTION:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Run Date:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>% Candidates by Reason:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:Calculation_487936923659948084:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Total Candidates Count:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_CANDIDATES:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <customized-label>
              <formatted-text>
                <run fontalignment='1'>(&lt;</run>
                <run fontalignment='1'>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:SUGGESTION_CANDIDATES:qk]</run>
                <run fontalignment='1'>&gt;)</run>
                <run fontalignment='1'>Æ&#10;</run>
                <run fontalignment='1'>&lt;</run>
                <run fontalignment='1'>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[sum:Calculation_487936923659948084:qk]</run>
                <run fontalignment='1'>&gt;</run>
              </formatted-text>
            </customized-label>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[usr:Calculation_477733427793936386:qk:2]</rows>
        <cols>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Channel (copy)_1319554694635393025:nk] / [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok])</cols>
      </table>
      <simple-id uuid='{3CD56D43-1DD5-4AD6-B693-A7C74CC7DBCD}' />
    </worksheet>
  </worksheets>
  <dashboards>
    <dashboard _.fcp.AccessibleZoneTabOrder.true...enable-sort-zone-taborder='true' name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs'>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputs/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputs?rev=' id='7067916' path='/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputs' revision='' site='${TABLEAU_SITE}' />
      <style />
      <size sizing-mode='automatic' />
      <datasources>
        <datasource name='Parameters' />
        <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l' />
      </datasources>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
          <calculation class='tableau' formula='&quot;Channel&quot;' />
          <members>
            <member value='&quot;Channel&quot;' />
            <member value='&quot;Segment&quot;' />
          </members>
        </column>
        <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
          <calculation class='tableau' formula='&quot;Published&quot;' />
          <members>
            <member value='&quot;(All)&quot;' />
            <member value='&quot;Published&quot;' />
          </members>
        </column>
      </datasource-dependencies>
      <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l'>
        <column aggregation='Count' caption=' Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_1035546475303264257]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
        </column>
        <column aggregation='Count' caption='Reasons' datatype='string' default-type='nominal' layered='true' name='[Calculation_458311642422099972]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='[DCO_REASON_CODE]+&apos;. &apos;+[DCO_REASON_NAME (VW_DIM_DCO_REASON_RPT)]' />
        </column>
        <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
        <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Segment Name' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column-instance column='[Calculation_1035546475303264257]' derivation='None' name='[none:Calculation_1035546475303264257:nk]' pivot='key' type='nominal' />
        <column-instance column='[Calculation_458311642422099972]' derivation='None' name='[none:Calculation_458311642422099972:nk]' pivot='key' type='nominal' />
        <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
        <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
        <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <zones>
        <zone h='100000' id='95' type-v2='layout-basic' w='100000' x='0' y='0' />
        <zone h='5808' id='3' name='1.0 View Filters' w='99115' x='272' y='379' />
        <zone h='6610' id='4' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' type-v2='filter' values='relevant' w='18660' x='762' y='6610' />
        <zone h='6620' id='5' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' type-v2='filter' values='relevant' w='18648' x='20278' y='6620' />
        <zone h='6620' id='6' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]' type-v2='filter' values='relevant' w='18648' x='39831' y='6620' />
        <zone h='38136' id='10' name='1.2 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Segment' w='48058' x='49733' y='21695'>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
          </zone-style>
        </zone>
        <zone h='38211' id='11' name='1.3 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs Over TIme' w='42004' x='55824' y='60859'>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
          </zone-style>
        </zone>
        <zone h='38190' id='30' name='1.4 CIE Candidates by Stage' w='43681' x='324' y='60870'>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
          </zone-style>
        </zone>
        <zone h='7420' id='44' mode='compact' param='[Parameters].[Parameter 1]' type-v2='paramctrl' w='11196' x='44326' y='60424' />
        <zone h='6620' id='83' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]' type-v2='filter' values='relevant' w='18709' x='78515' y='6620' />
        <zone h='38136' id='94' name='1.1 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Channel' w='49200' x='457' y='21695'>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
          </zone-style>
        </zone>
        <zone h='22261' id='164' name='1.4 CIE Candidates by Stage' pane-specification-id='3' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]' type-v2='color' w='11653' x='44402' y='77032' />
        <zone h='13051' id='165' name='1.2 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Segment' pane-specification-id='6' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]' type-v2='color' w='13785' x='83778' y='21864' />
        <zone h='20141' id='166' name='1.1 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Channel' pane-specification-id='2' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' type-v2='color' w='8149' x='42041' y='22792' />
        <zone h='6537' id='213' mode='compact' param='[Parameters].[Parameter 2]' type-v2='paramctrl' w='18660' x='20259' y='13781' />
        <zone h='6714' id='216' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]' type-v2='filter' values='relevant' w='18660' x='59254' y='6537' />
        <zone h='6537' id='255' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]' type-v2='filter' values='relevant' w='18660' x='762' y='13781' />
        <zone h='7420' id='278' mode='checkdropdown' name='1.4 CIE Candidates by Stage' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]' type-v2='filter' values='relevant' w='11196' x='44326' y='68551' />
      </zones>
      <devicelayouts>
        <devicelayout auto-generated='true' name='Phone'>
          <size maxheight='2000' minheight='2000' sizing-mode='vscroll' />
          <zones>
            <zone h='100000' id='322' type-v2='layout-basic' w='100000' x='0' y='0'>
              <zone h='97980' id='321' param='vert' type-v2='layout-flow' w='98910' x='545' y='1010'>
                <zone h='6610' id='4' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' type-v2='filter' values='relevant' w='18660' x='762' y='6610'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6620' id='5' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' type-v2='filter' values='relevant' w='18648' x='20278' y='6620'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6620' id='6' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]' type-v2='filter' values='relevant' w='18648' x='39831' y='6620'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6620' id='83' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]' type-v2='filter' values='relevant' w='18709' x='78515' y='6620'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6714' id='216' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]' type-v2='filter' values='relevant' w='18660' x='59254' y='6537'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6537' id='255' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]' type-v2='filter' values='relevant' w='18660' x='762' y='13781'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='46' h='5808' id='3' is-fixed='true' name='1.0 View Filters' w='99115' x='272' y='379'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6537' id='213' mode='compact' param='[Parameters].[Parameter 2]' type-v2='paramctrl' w='18660' x='20259' y='13781'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='38136' id='94' is-fixed='true' name='1.1 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Channel' w='49200' x='457' y='21695'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='20141' id='166' name='1.1 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Channel' pane-specification-id='2' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' type-v2='color' w='8149' x='42041' y='22792'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='38136' id='10' is-fixed='true' name='1.2 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Segment' w='48058' x='49733' y='21695'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='13051' id='165' name='1.2 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Segment' pane-specification-id='6' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]' type-v2='color' w='13785' x='83778' y='21864'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7420' id='44' mode='compact' param='[Parameters].[Parameter 1]' type-v2='paramctrl' w='11196' x='44326' y='60424'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='38211' id='11' is-fixed='true' name='1.3 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs Over TIme' w='42004' x='55824' y='60859'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7420' id='278' mode='checkdropdown' name='1.4 CIE Candidates by Stage' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]' type-v2='filter' values='relevant' w='11196' x='44326' y='68551'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='38190' id='30' is-fixed='true' name='1.4 CIE Candidates by Stage' w='43681' x='324' y='60870'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='22261' id='164' name='1.4 CIE Candidates by Stage' pane-specification-id='3' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]' type-v2='color' w='11653' x='44402' y='77032'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
              </zone>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{362289AC-6061-4B8F-AFA1-502CF4D6E292}' />
    </dashboard>
  </dashboards>
  <windows source-height='30'>
    <window class='dashboard' maximized='true' name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs'>
      <viewpoints>
        <viewpoint name='1.0 View Filters'>
          <zoom type='fit-width' />
        </viewpoint>
        <viewpoint name='1.1 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Channel'>
          <zoom type='entire-view' />
          <highlight>
            <color-one-way>
              <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</field>
            </color-one-way>
          </highlight>
        </viewpoint>
        <viewpoint name='1.2 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Segment'>
          <zoom type='entire-view' />
          <highlight>
            <color-one-way>
              <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]</field>
            </color-one-way>
          </highlight>
        </viewpoint>
        <viewpoint name='1.3 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs Over TIme'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='1.4 CIE Candidates by Stage'>
          <zoom type='entire-view' />
          <highlight>
            <color-one-way>
              <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]</field>
            </color-one-way>
          </highlight>
        </viewpoint>
      </viewpoints>
      <active id='-1' />
      <simple-id uuid='{CB2BF5B5-FA4E-4B3A-89EB-055046D5CEFE}' />
    </window>
    <window class='worksheet' hidden='true' name='1.0 View Filters'>
      <cards>
        <edge name='left'>
          <strip size='272'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[attr:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1035546475303264257:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):qk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE:qk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DS_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:HCP_SEGMENT:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:LATEST_RUN:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SCENARIO_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{32E67324-1A29-4231-9064-A3EA0EBBBEAF}' />
    </window>
    <window class='worksheet' hidden='true' name='1.1 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Channel'>
      <cards>
        <edge name='left'>
          <strip size='300'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]' type='filter' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_REASON_DESCRIPTION:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:LATEST_RUN:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:MIN_CAPACITY (copy)_1245104568322998288:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SCENARIO_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:STATUS:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{327F420E-2EFE-4476-96C1-89593A4B1A5D}' />
    </window>
    <window class='worksheet' hidden='true' name='1.2 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs: Segment'>
      <cards>
        <edge name='left'>
          <strip size='300'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SE_CONFIG_NAME:nk]' type='filter' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{9C7BF59E-B8FD-49B0-9107-D95BAA9D04E2}' />
    </window>
    <window class='worksheet' hidden='true' name='1.3 ${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs Over TIme'>
      <cards>
        <edge name='left'>
          <strip size='300'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card mode='compact' param='[Parameters].[Parameter 1]' type='parameter' />
            <card pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]' type='color' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[Parameters].[Parameter 1]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Channel (copy)_1319554694635393025:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[yr:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{EB4C527F-86AB-4CB1-A26C-3FD5CB9EC3BB}' />
    </window>
    <window class='worksheet' hidden='true' name='1.4 CIE Candidates by Stage'>
      <cards>
        <edge name='left'>
          <strip size='300'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card mode='checkdropdown' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]' type='filter' values='relevant' />
            <card mode='compact' param='[Parameters].[Parameter 1]' type='parameter' />
            <card pane-specification-id='3' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]' type='color' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[Parameters].[Parameter 1]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:CHANNEL:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_1394708532141854720:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Calculation_458311642422099972:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:Channel (copy)_1319554694635393025:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_REASON_DESCRIPTION:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_REASON_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):qk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:DS_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:LATEST_RUN:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:PRODUCT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SCENARIO_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[none:SEGMENT_TYPE:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[yr:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.1n8dce51au2rei1c1wn6h0qwdi0l].[yr:DCO_RUN_DATE:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{B8AE1D27-AE19-4916-ACD4-02E422871FA3}' />
    </window>
  </windows>
</workbook>
