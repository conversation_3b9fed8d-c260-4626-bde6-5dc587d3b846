<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20231.23.0505.1444                               -->
<workbook locale='en_US' source-build='2021.4.16 (20214.23.0209.1538)' source-platform='mac' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.AccessibleZoneTabOrder.true...AccessibleZoneTabOrder />
    <_.fcp.AnimationOnByDefault.true...AnimationOnByDefault />
    <AutoCreateAndUpdateDSDPhoneLayouts />
    <ISO8601DefaultCalendarPref />
    <_.fcp.MarkAnimation.true...MarkAnimation />
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.ParameterDefaultValues.true...ParameterDefaultValues />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <SetMembershipControl />
    <SheetIdentifierTracking />
    <WindowsPersistSimpleIdentifiers />
    <ZoneBackgroundTransparency />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEScores?rev=1.1' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEScores' path='/t/${TABLEAU_SITE}/workbooks' revision='1.1' site='${TABLEAU_SITE}' />
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <_.fcp.AnimationOnByDefault.false...style>
    <_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule element='animation'>
      <_.fcp.AnimationOnByDefault.false...format attr='animation-on' value='ao-on' />
    </_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule>
  </_.fcp.AnimationOnByDefault.false...style>
  <datasources>
    <datasource hasconnection='false' inline='true' name='Parameters' version='18.1'>
      <aliases enabled='yes' />
      <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
        <calculation class='tableau' formula='&quot;Channel&quot;' />
        <members>
          <member value='&quot;Channel&quot;' />
          <member value='&quot;Segment&quot;' />
        </members>
      </column>
      <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
        <calculation class='tableau' formula='&quot;Published&quot;' />
        <members>
          <member value='&quot;(All)&quot;' />
          <member value='&quot;Published&quot;' />
        </members>
      </column>
      <column _.fcp.ParameterDefaultValues.true...source-field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[INFLUENCE_DISPLAY_NAME]' caption='P_Influence' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account Long-term Importance&quot;'>
        <calculation class='tableau' formula='&quot;Account Long-term Importance&quot;' />
      </column>
    </datasource>
    <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' inline='true' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750' version='18.1'>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel?rev=1.1' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.1' site='${TABLEAU_SITE}' />
      <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
        <calculations>
          <calculation column='[% Decisive Components (copy)_1879408442265681937]' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;LOW&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
          <calculation column='[% HIGH Decisive Components (copy)_1879408442266198035]' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;MED&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
          <calculation column='[% LOW Decisive Components (copy)_1879408442266107922]' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;HIGH&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
          <calculation column='[Average score (copy)_1879408442056417282]' formula='COUNTD(SUGGESTION_CANDIDATE_UID)&#13;&#10;/SUM([Calculation_1879408443317731348])' />
          <calculation column='[Calculation_1445936995299098631]' formula='[Parameters].[Parameter 3]=[INFLUENCE_DISPLAY_NAME]' />
          <calculation column='[Calculation_1506454080614776832]' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
          <calculation column='[Calculation_1604407389790982145]' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
          <calculation column='[Calculation_1879408442241851404]' formula='&apos;PERCENTAGE OF CANDIDATES IMPACTED&apos;' />
          <calculation column='[Calculation_1879408443317731348]' formula='{ FIXED RECOMMENDED: &#13;&#10;countd( SUGGESTION_CANDIDATE_UID)}' />
          <calculation column='[Calculation_527765595207630863]' formula='COUNTD([ACTIVE_ACCOUNTS])' />
          <calculation column='[Calculation_864972641503416320]' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
          <calculation column='[FINAL_SCORE_SUM (copy)_1327436000330481664]' formula='SUM([FINAL_SCORE_SUM])/countd([SUGGESTION_CANDIDATE_UID])' />
          <calculation column='[Text Decisive Components (copy)_1879408442255036430]' formula='SUM(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT = 1 THEN 1 END)&#13;&#10;/ SUM([Calculation_1879408443317731348])' />
        </calculations>
        <metadata-records>
          <metadata-record class='measure'>
            <remote-name>% Decisive Components (copy)_1879408442265681937</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[% Decisive Components (copy)_1879408442265681937]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>% Decisive Components (copy)_1879408442265681937</remote-alias>
            <ordinal>76</ordinal>
            <layered>true</layered>
            <caption>% LOW Decisive Components</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;
AND XAI_CONFIDENCE_DEGREE =&apos;LOW&apos;&#13;
and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;
/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;
and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>% HIGH Decisive Components (copy)_1879408442266198035</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[% HIGH Decisive Components (copy)_1879408442266198035]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>% HIGH Decisive Components (copy)_1879408442266198035</remote-alias>
            <ordinal>77</ordinal>
            <layered>true</layered>
            <caption>% MED Decisive Components</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;
AND XAI_CONFIDENCE_DEGREE =&apos;MED&apos;&#13;
and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;
/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;
and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>% LOW Decisive Components (copy)_1879408442266107922</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[% LOW Decisive Components (copy)_1879408442266107922]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>% LOW Decisive Components (copy)_1879408442266107922</remote-alias>
            <ordinal>78</ordinal>
            <layered>true</layered>
            <caption>% HIGH Decisive Components</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;
AND XAI_CONFIDENCE_DEGREE =&apos;HIGH&apos;&#13;
and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;
/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;
and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ACTIVE_ACCOUNTS</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACTIVE_ACCOUNTS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTIVE_ACCOUNTS</remote-alias>
            <ordinal>9</ordinal>
            <layered>true</layered>
            <caption>Active Accounts</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Average score (copy)_1879408442056417282</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Average score (copy)_1879408442056417282]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Average score (copy)_1879408442056417282</remote-alias>
            <ordinal>79</ordinal>
            <layered>true</layered>
            <caption>% of Candidates Impacted</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;COUNTD(SUGGESTION_CANDIDATE_UID)&#13;
/SUM([Calculation_1879408443317731348])&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CHANNEL</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CHANNEL]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL</remote-alias>
            <ordinal>6</ordinal>
            <layered>true</layered>
            <caption>Channel</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_BY (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CREATED_BY (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_BY (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>68</ordinal>
            <layered>true</layered>
            <caption>Created By (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_BY</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CREATED_BY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_BY</remote-alias>
            <ordinal>32</ordinal>
            <layered>true</layered>
            <caption>Created By</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_TS (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[CREATED_TS (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_TS (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>69</ordinal>
            <layered>true</layered>
            <caption>Created Ts (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_TS</remote-name>
            <remote-type>7</remote-type>
            <local-name>[CREATED_TS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_TS</remote-alias>
            <ordinal>35</ordinal>
            <layered>true</layered>
            <caption>Created Ts</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1445936995299098631</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1445936995299098631]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1445936995299098631</remote-alias>
            <ordinal>80</ordinal>
            <layered>true</layered>
            <caption>Influencer</caption>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[Parameters].[Parameter 3]=[INFLUENCE_DISPLAY_NAME]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1506454080614776832</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1506454080614776832]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1506454080614776832</remote-alias>
            <ordinal>81</ordinal>
            <layered>true</layered>
            <caption>Channel / Segment</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Parameter 1]&#13;
WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;
WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1604407389790982145</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1604407389790982145]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1604407389790982145</remote-alias>
            <ordinal>82</ordinal>
            <layered>true</layered>
            <caption>Scenario group</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Parameter 2]&#13;
WHEN &apos;(All)&apos; THEN 1&#13;
WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1879408442241851404</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1879408442241851404]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1879408442241851404</remote-alias>
            <ordinal>83</ordinal>
            <layered>true</layered>
            <caption>Text Component Prevalence</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;&apos;PERCENTAGE OF CANDIDATES IMPACTED&apos;&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_1879408443317731348</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1879408443317731348]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1879408443317731348</remote-alias>
            <ordinal>84</ordinal>
            <layered>true</layered>
            <caption>TotalCandidateCnt</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;{ FIXED RECOMMENDED: &#13;
countd( SUGGESTION_CANDIDATE_UID)}&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_527765595207630863</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_527765595207630863]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_527765595207630863</remote-alias>
            <ordinal>85</ordinal>
            <layered>true</layered>
            <caption>Calculate_accounts</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;COUNTD([ACTIVE_ACCOUNTS])&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_864972641503416320</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_864972641503416320]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_864972641503416320</remote-alias>
            <ordinal>86</ordinal>
            <layered>true</layered>
            <caption>Scenario</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_CODE</remote-alias>
            <ordinal>21</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Code</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_DESCRIPTION</remote-alias>
            <ordinal>23</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Description</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>347</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_NAME</remote-alias>
            <ordinal>22</ordinal>
            <layered>true</layered>
            <caption>Reason</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>22</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_TYPE_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_TYPE_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_TYPE_CODE</remote-alias>
            <ordinal>24</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Type Code</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>9</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_TYPE_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_TYPE_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_TYPE_NAME</remote-alias>
            <ordinal>25</ordinal>
            <layered>true</layered>
            <caption>Reason Type</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>9</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)</remote-alias>
            <ordinal>20</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Uid (Vw Dim Dco Reason Rpt)</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_UID</remote-alias>
            <ordinal>14</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>26</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)</remote-alias>
            <ordinal>31</ordinal>
            <layered>true</layered>
            <caption>Dco Run Date (Vw Dim Dco Run Rpt)</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DCO_RUN_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_DATE</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <caption>Dco Run Date</caption>
            <family>Custom SQL Query</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_UID (Custom SQL Query)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_RUN_UID (Custom SQL Query)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_UID (Custom SQL Query)</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_RUN_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_UID</remote-alias>
            <ordinal>27</ordinal>
            <layered>true</layered>
            <caption>Dco Run Uid</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DESCRIPTION</remote-alias>
            <ordinal>39</ordinal>
            <layered>true</layered>
            <caption>Description</caption>
            <family>VW_DIM_DSE_CONFIG</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_BRAND_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_BRAND_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_BRAND_KEY</remote-alias>
            <ordinal>43</ordinal>
            <layered>true</layered>
            <caption>Dim Brand Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_COUNTRY_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_COUNTRY_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_COUNTRY_KEY</remote-alias>
            <ordinal>53</ordinal>
            <layered>true</layered>
            <caption>Dim Country Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CUSTOMER_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CUSTOMER_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
            <ordinal>52</ordinal>
            <layered>true</layered>
            <caption>Dim Customer Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>65</ordinal>
            <layered>true</layered>
            <caption>Dim Product Key (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_PRODUCT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_PRODUCT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_PRODUCT_KEY</remote-alias>
            <ordinal>41</ordinal>
            <layered>true</layered>
            <caption>Dim Product Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_SEGMENT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_SEGMENT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_SEGMENT_KEY</remote-alias>
            <ordinal>62</ordinal>
            <layered>true</layered>
            <caption>Dim Segment Key</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_SEGMENT_TYPE_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_SEGMENT_TYPE_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_SEGMENT_TYPE_KEY</remote-alias>
            <ordinal>72</ordinal>
            <layered>true</layered>
            <caption>Dim Segment Type Key</caption>
            <family>VW_DIM_SEGMENT_TYPE_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>FINAL_SCORE_SUM (copy)_1327436000330481664</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[FINAL_SCORE_SUM (copy)_1327436000330481664]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FINAL_SCORE_SUM (copy)_1327436000330481664</remote-alias>
            <ordinal>87</ordinal>
            <layered>true</layered>
            <caption>Average score</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM([FINAL_SCORE_SUM])/countd([SUGGESTION_CANDIDATE_UID])&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>FINAL_SCORE_SUM</remote-name>
            <remote-type>5</remote-type>
            <local-name>[FINAL_SCORE_SUM]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FINAL_SCORE_SUM</remote-alias>
            <ordinal>7</ordinal>
            <layered>true</layered>
            <caption>FINAL_SCORE</caption>
            <family>Custom SQL Query</family>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>HCP_SEGMENT</remote-name>
            <remote-type>129</remote-type>
            <local-name>[HCP_SEGMENT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_SEGMENT</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <caption>Hcp Segment</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>INFLUENCE_DISPLAY_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[INFLUENCE_DISPLAY_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>INFLUENCE_DISPLAY_NAME</remote-alias>
            <ordinal>12</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>255</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>INFLUENCE_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[INFLUENCE_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>INFLUENCE_UID</remote-alias>
            <ordinal>10</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>INFLUENCE_VALUE</remote-name>
            <remote-type>5</remote-type>
            <local-name>[INFLUENCE_VALUE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>INFLUENCE_VALUE</remote-alias>
            <ordinal>11</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_ACTIVE_SRC</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_ACTIVE_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_ACTIVE_SRC</remote-alias>
            <ordinal>49</ordinal>
            <layered>true</layered>
            <caption>Is Active Src</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_COMPETITOR</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_COMPETITOR]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_COMPETITOR</remote-alias>
            <ordinal>50</ordinal>
            <layered>true</layered>
            <caption>Is Competitor</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_DELETED_SRC</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_DELETED_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_DELETED_SRC</remote-alias>
            <ordinal>51</ordinal>
            <layered>true</layered>
            <caption>Is Deleted Src</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>IS_MAX_XAI_COMPONENT</remote-name>
            <remote-type>131</remote-type>
            <local-name>[IS_MAX_XAI_COMPONENT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_MAX_XAI_COMPONENT</remote-alias>
            <ordinal>16</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>1</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_PUBLISHED</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_PUBLISHED]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_PUBLISHED</remote-alias>
            <ordinal>58</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_SUB_COMPONENT</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_SUB_COMPONENT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_SUB_COMPONENT</remote-alias>
            <ordinal>18</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LAST_DCO_RUN_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[LAST_DCO_RUN_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
            <ordinal>60</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LAST_DCO_RUN_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[LAST_DCO_RUN_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_DCO_RUN_UID</remote-alias>
            <ordinal>59</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LATEST_RUN</remote-name>
            <remote-type>11</remote-type>
            <local-name>[LATEST_RUN]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LATEST_RUN</remote-alias>
            <ordinal>30</ordinal>
            <layered>true</layered>
            <caption>Latest Run</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>POST_PROC_STATUS</remote-name>
            <remote-type>129</remote-type>
            <local-name>[POST_PROC_STATUS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>POST_PROC_STATUS</remote-alias>
            <ordinal>34</ordinal>
            <layered>true</layered>
            <caption>Post Proc Status</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[PRODUCT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID</remote-alias>
            <ordinal>67</ordinal>
            <layered>true</layered>
            <caption>Product Id</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME</remote-alias>
            <ordinal>44</ordinal>
            <layered>true</layered>
            <caption>Product Name</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME_ENGLISH</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME_ENGLISH]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME_ENGLISH</remote-alias>
            <ordinal>45</ordinal>
            <layered>true</layered>
            <caption>Product Name English</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_TYPE</remote-alias>
            <ordinal>46</ordinal>
            <layered>true</layered>
            <caption>Product Type</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)</remote-alias>
            <ordinal>42</ordinal>
            <layered>true</layered>
            <caption>Product Uid (Vw Dim Product Dco Rpt)</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>66</ordinal>
            <layered>true</layered>
            <caption>Product Uid (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>200</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <caption>Product Uid</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECOMMENDED</remote-name>
            <remote-type>11</remote-type>
            <local-name>[RECOMMENDED]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECOMMENDED</remote-alias>
            <ordinal>13</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECORD_END_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[RECORD_END_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECORD_END_DATE</remote-alias>
            <ordinal>48</ordinal>
            <layered>true</layered>
            <caption>Record End Date</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECORD_START_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[RECORD_START_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECORD_START_DATE</remote-alias>
            <ordinal>47</ordinal>
            <layered>true</layered>
            <caption>Record Start Date</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_DESCRIPTION</remote-alias>
            <ordinal>57</ordinal>
            <layered>true</layered>
            <caption>Scenario Description</caption>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>255</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_NAME</remote-alias>
            <ordinal>56</ordinal>
            <layered>true</layered>
            <caption>Scenario Name</caption>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID (VW_DIM_DCO_RUN_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID (VW_DIM_DCO_RUN_RPT)</remote-alias>
            <ordinal>28</ordinal>
            <layered>true</layered>
            <caption>Scenario Uid (Vw Dim Dco Run Rpt)</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID (VW_DIM_SCENARIO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID (VW_DIM_SCENARIO_RPT)</remote-alias>
            <ordinal>55</ordinal>
            <layered>true</layered>
            <caption>Scenario Uid (Vw Dim Scenario Rpt)</caption>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <caption>Scenario Uid</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_VER</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_VER]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_VER</remote-alias>
            <ordinal>29</ordinal>
            <layered>true</layered>
            <caption>Scenario Ver</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_NAME</remote-alias>
            <ordinal>63</ordinal>
            <layered>true</layered>
            <caption>Segment</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>200</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)</remote-alias>
            <ordinal>73</ordinal>
            <layered>true</layered>
            <caption>Segment Type (Vw Dim Segment Type Rpt)</caption>
            <family>VW_DIM_SEGMENT_TYPE_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_TYPE</remote-alias>
            <ordinal>64</ordinal>
            <layered>true</layered>
            <caption>Segment Type</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_ID (VW_DIM_DSE_CONFIG)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_ID (VW_DIM_DSE_CONFIG)</remote-alias>
            <ordinal>37</ordinal>
            <layered>true</layered>
            <caption>Se Config Id (Vw Dim Dse Config)</caption>
            <family>VW_DIM_DSE_CONFIG</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>SE_CONFIG_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SE_CONFIG_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_ID</remote-alias>
            <ordinal>15</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SE_CONFIG_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_NAME</remote-alias>
            <ordinal>38</ordinal>
            <layered>true</layered>
            <caption>Config</caption>
            <family>VW_DIM_DSE_CONFIG</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>STATUS</remote-name>
            <remote-type>129</remote-type>
            <local-name>[STATUS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>STATUS</remote-alias>
            <ordinal>33</ordinal>
            <layered>true</layered>
            <caption>Status</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_CANDIDATE_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SUGGESTION_CANDIDATE_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_CANDIDATE_UID</remote-alias>
            <ordinal>8</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Text Decisive Components (copy)_1879408442255036430</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Text Decisive Components (copy)_1879408442255036430]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Text Decisive Components (copy)_1879408442255036430</remote-alias>
            <ordinal>88</ordinal>
            <layered>true</layered>
            <caption>% Decisive Components</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;
and IS_MAX_XAI_COMPONENT = 1 THEN 1 END)&#13;
/ SUM([Calculation_1879408443317731348])&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UPDATED_TS</remote-name>
            <remote-type>7</remote-type>
            <local-name>[UPDATED_TS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>UPDATED_TS</remote-alias>
            <ordinal>70</ordinal>
            <layered>true</layered>
            <caption>Updated Ts</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>XAI_CONFIDENCE_DEGREE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[XAI_CONFIDENCE_DEGREE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>XAI_CONFIDENCE_DEGREE</remote-alias>
            <ordinal>17</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>6</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_64BIT_CALCULATIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' inline='true' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.ParameterDefaultValues.true...ParameterDefaultValues />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel?rev=1.1' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.1' site='${TABLEAU_SITE}' />
  <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
    </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
    <cols>
      <map key='[\% Decisive Components (copy)_1879408442265681937]' value='[sqlproxy].[\% Decisive Components (copy)_1879408442265681937]' />
      <map key='[\% HIGH Decisive Components (copy)_1879408442266198035]' value='[sqlproxy].[\% HIGH Decisive Components (copy)_1879408442266198035]' />
      <map key='[\% LOW Decisive Components (copy)_1879408442266107922]' value='[sqlproxy].[\% LOW Decisive Components (copy)_1879408442266107922]' />
      <map key='[ACTIVE_ACCOUNTS]' value='[sqlproxy].[ACTIVE_ACCOUNTS]' />
      <map key='[Average score (copy)_1879408442056417282]' value='[sqlproxy].[Average score (copy)_1879408442056417282]' />
      <map key='[CHANNEL]' value='[sqlproxy].[CHANNEL]' />
      <map key='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[CREATED_BY (VW_DIM_SEGMENT_RPT)]' />
      <map key='[CREATED_BY]' value='[sqlproxy].[CREATED_BY]' />
      <map key='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[CREATED_TS (VW_DIM_SEGMENT_RPT)]' />
      <map key='[CREATED_TS]' value='[sqlproxy].[CREATED_TS]' />
      <map key='[Calculation_1445936995299098631]' value='[sqlproxy].[Calculation_1445936995299098631]' />
      <map key='[Calculation_1506454080614776832]' value='[sqlproxy].[Calculation_1506454080614776832]' />
      <map key='[Calculation_1604407389790982145]' value='[sqlproxy].[Calculation_1604407389790982145]' />
      <map key='[Calculation_1879408442241851404]' value='[sqlproxy].[Calculation_1879408442241851404]' />
      <map key='[Calculation_1879408443317731348]' value='[sqlproxy].[Calculation_1879408443317731348]' />
      <map key='[Calculation_527765595207630863]' value='[sqlproxy].[Calculation_527765595207630863]' />
      <map key='[Calculation_864972641503416320]' value='[sqlproxy].[Calculation_864972641503416320]' />
      <map key='[DCO_REASON_CODE]' value='[sqlproxy].[DCO_REASON_CODE]' />
      <map key='[DCO_REASON_DESCRIPTION]' value='[sqlproxy].[DCO_REASON_DESCRIPTION]' />
      <map key='[DCO_REASON_NAME]' value='[sqlproxy].[DCO_REASON_NAME]' />
      <map key='[DCO_REASON_TYPE_CODE]' value='[sqlproxy].[DCO_REASON_TYPE_CODE]' />
      <map key='[DCO_REASON_TYPE_NAME]' value='[sqlproxy].[DCO_REASON_TYPE_NAME]' />
      <map key='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' value='[sqlproxy].[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
      <map key='[DCO_REASON_UID]' value='[sqlproxy].[DCO_REASON_UID]' />
      <map key='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' value='[sqlproxy].[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' />
      <map key='[DCO_RUN_DATE]' value='[sqlproxy].[DCO_RUN_DATE]' />
      <map key='[DCO_RUN_UID (Custom SQL Query)]' value='[sqlproxy].[DCO_RUN_UID (Custom SQL Query)]' />
      <map key='[DCO_RUN_UID]' value='[sqlproxy].[DCO_RUN_UID]' />
      <map key='[DESCRIPTION]' value='[sqlproxy].[DESCRIPTION]' />
      <map key='[DIM_BRAND_KEY]' value='[sqlproxy].[DIM_BRAND_KEY]' />
      <map key='[DIM_COUNTRY_KEY]' value='[sqlproxy].[DIM_COUNTRY_KEY]' />
      <map key='[DIM_CUSTOMER_KEY]' value='[sqlproxy].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' />
      <map key='[DIM_PRODUCT_KEY]' value='[sqlproxy].[DIM_PRODUCT_KEY]' />
      <map key='[DIM_SEGMENT_KEY]' value='[sqlproxy].[DIM_SEGMENT_KEY]' />
      <map key='[DIM_SEGMENT_TYPE_KEY]' value='[sqlproxy].[DIM_SEGMENT_TYPE_KEY]' />
      <map key='[FINAL_SCORE_SUM (copy)_1327436000330481664]' value='[sqlproxy].[FINAL_SCORE_SUM (copy)_1327436000330481664]' />
      <map key='[FINAL_SCORE_SUM]' value='[sqlproxy].[FINAL_SCORE_SUM]' />
      <map key='[HCP_SEGMENT]' value='[sqlproxy].[HCP_SEGMENT]' />
      <map key='[INFLUENCE_DISPLAY_NAME]' value='[sqlproxy].[INFLUENCE_DISPLAY_NAME]' />
      <map key='[INFLUENCE_UID]' value='[sqlproxy].[INFLUENCE_UID]' />
      <map key='[INFLUENCE_VALUE]' value='[sqlproxy].[INFLUENCE_VALUE]' />
      <map key='[IS_ACTIVE_SRC]' value='[sqlproxy].[IS_ACTIVE_SRC]' />
      <map key='[IS_COMPETITOR]' value='[sqlproxy].[IS_COMPETITOR]' />
      <map key='[IS_DELETED_SRC]' value='[sqlproxy].[IS_DELETED_SRC]' />
      <map key='[IS_MAX_XAI_COMPONENT]' value='[sqlproxy].[IS_MAX_XAI_COMPONENT]' />
      <map key='[IS_PUBLISHED]' value='[sqlproxy].[IS_PUBLISHED]' />
      <map key='[IS_SUB_COMPONENT]' value='[sqlproxy].[IS_SUB_COMPONENT]' />
      <map key='[LAST_DCO_RUN_DATE]' value='[sqlproxy].[LAST_DCO_RUN_DATE]' />
      <map key='[LAST_DCO_RUN_UID]' value='[sqlproxy].[LAST_DCO_RUN_UID]' />
      <map key='[LATEST_RUN]' value='[sqlproxy].[LATEST_RUN]' />
      <map key='[POST_PROC_STATUS]' value='[sqlproxy].[POST_PROC_STATUS]' />
      <map key='[PRODUCT_ID]' value='[sqlproxy].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME]' value='[sqlproxy].[PRODUCT_NAME]' />
      <map key='[PRODUCT_NAME_ENGLISH]' value='[sqlproxy].[PRODUCT_NAME_ENGLISH]' />
      <map key='[PRODUCT_TYPE]' value='[sqlproxy].[PRODUCT_TYPE]' />
      <map key='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' value='[sqlproxy].[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
      <map key='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' />
      <map key='[PRODUCT_UID]' value='[sqlproxy].[PRODUCT_UID]' />
      <map key='[RECOMMENDED]' value='[sqlproxy].[RECOMMENDED]' />
      <map key='[RECORD_END_DATE]' value='[sqlproxy].[RECORD_END_DATE]' />
      <map key='[RECORD_START_DATE]' value='[sqlproxy].[RECORD_START_DATE]' />
      <map key='[SCENARIO_DESCRIPTION]' value='[sqlproxy].[SCENARIO_DESCRIPTION]' />
      <map key='[SCENARIO_NAME]' value='[sqlproxy].[SCENARIO_NAME]' />
      <map key='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' value='[sqlproxy].[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' />
      <map key='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' value='[sqlproxy].[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
      <map key='[SCENARIO_UID]' value='[sqlproxy].[SCENARIO_UID]' />
      <map key='[SCENARIO_VER]' value='[sqlproxy].[SCENARIO_VER]' />
      <map key='[SEGMENT_NAME]' value='[sqlproxy].[SEGMENT_NAME]' />
      <map key='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' value='[sqlproxy].[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' />
      <map key='[SEGMENT_TYPE]' value='[sqlproxy].[SEGMENT_TYPE]' />
      <map key='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' value='[sqlproxy].[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' />
      <map key='[SE_CONFIG_ID]' value='[sqlproxy].[SE_CONFIG_ID]' />
      <map key='[SE_CONFIG_NAME]' value='[sqlproxy].[SE_CONFIG_NAME]' />
      <map key='[STATUS]' value='[sqlproxy].[STATUS]' />
      <map key='[SUGGESTION_CANDIDATE_UID]' value='[sqlproxy].[SUGGESTION_CANDIDATE_UID]' />
      <map key='[Text Decisive Components (copy)_1879408442255036430]' value='[sqlproxy].[Text Decisive Components (copy)_1879408442255036430]' />
      <map key='[UPDATED_TS]' value='[sqlproxy].[UPDATED_TS]' />
      <map key='[XAI_CONFIDENCE_DEGREE]' value='[sqlproxy].[XAI_CONFIDENCE_DEGREE]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='User' caption='\% LOW Decisive Components' datatype='real' default-type='quantitative' name='[\% Decisive Components (copy)_1879408442265681937]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &\#13;&\#10;AND XAI_CONFIDENCE_DEGREE =&apos;LOW&apos;&\#13;&\#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&\#13;&\#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &\#13;&\#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
  </column>
  <column aggregation='User' caption='\% MED Decisive Components' datatype='real' default-type='quantitative' name='[\% HIGH Decisive Components (copy)_1879408442266198035]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &\#13;&\#10;AND XAI_CONFIDENCE_DEGREE =&apos;MED&apos;&\#13;&\#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&\#13;&\#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &\#13;&\#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
  </column>
  <column aggregation='User' caption='\% HIGH Decisive Components' datatype='real' default-type='quantitative' name='[\% LOW Decisive Components (copy)_1879408442266107922]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &\#13;&\#10;AND XAI_CONFIDENCE_DEGREE =&apos;HIGH&apos;&\#13;&\#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&\#13;&\#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &\#13;&\#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
  </column>
  <column aggregation='Count' caption='Active Accounts' datatype='string' default-role='measure' default-type='ordinal' name='[ACTIVE_ACCOUNTS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='User' caption='\% of Candidates Impacted' datatype='real' default-type='quantitative' name='[Average score (copy)_1879408442056417282]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='COUNTD(SUGGESTION_CANDIDATE_UID)&\#13;&\#10;/SUM([Calculation_1879408443317731348])' />
  </column>
  <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Created By (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' name='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Created By' datatype='string' default-type='nominal' name='[CREATED_BY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Created Ts (Vw Dim Segment Rpt)' datatype='datetime' default-type='ordinal' name='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Year' caption='Created Ts' datatype='datetime' default-type='ordinal' name='[CREATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Count' caption='Influencer' datatype='boolean' default-type='nominal' name='[Calculation_1445936995299098631]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default'>
    <calculation class='tableau' formula='[Parameters].[Parameter 3]=[INFLUENCE_DISPLAY_NAME]' />
  </column>
  <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' name='[Calculation_1506454080614776832]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&\#13;&\#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&\#13;&\#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&\#13;&\#10;END' />
  </column>
  <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&\#13;&\#10;WHEN &apos;(All)&apos; THEN 1&\#13;&\#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Text Component Prevalence' datatype='string' default-type='nominal' name='[Calculation_1879408442241851404]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='&apos;PERCENTAGE OF CANDIDATES IMPACTED&apos;' />
  </column>
  <column aggregation='Sum' caption='TotalCandidateCnt' datatype='integer' default-type='quantitative' name='[Calculation_1879408443317731348]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='{ FIXED RECOMMENDED: &\#13;&\#10;countd( SUGGESTION_CANDIDATE_UID)}' />
  </column>
  <column aggregation='User' caption='Calculate_accounts' datatype='integer' default-type='quantitative' name='[Calculation_527765595207630863]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='COUNTD([ACTIVE_ACCOUNTS])' />
  </column>
  <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
  </column>
  <column aggregation='Count' caption='Dco Reason Code' datatype='string' default-type='nominal' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Description' datatype='string' default-type='nominal' name='[DCO_REASON_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Reason' datatype='string' default-type='nominal' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Type Code' datatype='string' default-type='nominal' name='[DCO_REASON_TYPE_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Uid (Vw Dim Dco Reason Rpt)' datatype='string' default-type='nominal' name='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Dco Run Date (Vw Dim Dco Run Rpt)' datatype='date' default-type='ordinal' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Run Uid' datatype='string' default-type='nominal' name='[DCO_RUN_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Description' datatype='string' default-type='nominal' name='[DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Brand Key' datatype='integer' default-type='ordinal' name='[DIM_BRAND_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Country Key' datatype='integer' default-type='ordinal' name='[DIM_COUNTRY_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Customer Key' datatype='integer' default-type='ordinal' name='[DIM_CUSTOMER_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Product Key (Vw Dim Segment Rpt)' datatype='integer' default-type='ordinal' name='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Product Key' datatype='integer' default-type='ordinal' name='[DIM_PRODUCT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Segment Key' datatype='integer' default-type='ordinal' name='[DIM_SEGMENT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Segment Type Key' datatype='integer' default-type='ordinal' name='[DIM_SEGMENT_TYPE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='User' caption='Average score' datatype='real' default-type='quantitative' name='[FINAL_SCORE_SUM (copy)_1327436000330481664]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='SUM([FINAL_SCORE_SUM])/countd([SUGGESTION_CANDIDATE_UID])' />
  </column>
  <column aggregation='Sum' caption='FINAL_SCORE' datatype='real' default-type='quantitative' name='[FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
  <column aggregation='Count' caption='Hcp Segment' datatype='string' default-type='nominal' name='[HCP_SEGMENT]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Is Active Src' datatype='boolean' default-type='nominal' name='[IS_ACTIVE_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Is Competitor' datatype='boolean' default-type='nominal' name='[IS_COMPETITOR]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Is Deleted Src' datatype='boolean' default-type='nominal' name='[IS_DELETED_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Latest Run' datatype='boolean' default-type='nominal' name='[LATEST_RUN]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Post Proc Status' datatype='string' default-type='nominal' name='[POST_PROC_STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Id' datatype='integer' default-type='ordinal' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Name English' datatype='string' default-type='nominal' name='[PRODUCT_NAME_ENGLISH]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Type' datatype='string' default-type='nominal' name='[PRODUCT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Uid (Vw Dim Product Dco Rpt)' datatype='string' default-type='nominal' name='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Uid (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' name='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Uid' datatype='string' default-type='nominal' name='[PRODUCT_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Record End Date' datatype='date' default-type='ordinal' name='[RECORD_END_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' caption='Record Start Date' datatype='date' default-type='ordinal' name='[RECORD_START_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Description' datatype='string' default-type='nominal' name='[SCENARIO_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Uid (Vw Dim Dco Run Rpt)' datatype='string' default-type='nominal' name='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Uid (Vw Dim Scenario Rpt)' datatype='string' default-type='nominal' name='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Uid' datatype='string' default-type='nominal' name='[SCENARIO_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Ver' datatype='string' default-type='nominal' name='[SCENARIO_VER]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Se Config Id (Vw Dim Dse Config)' datatype='integer' default-type='ordinal' name='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Status' datatype='string' default-type='nominal' name='[STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='User' caption='\% Decisive Components' datatype='real' default-type='quantitative' name='[Text Decisive Components (copy)_1879408442255036430]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='SUM(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &\#13;&\#10;and IS_MAX_XAI_COMPONENT = 1 THEN 1 END)&\#13;&\#10;/ SUM([Calculation_1879408443317731348])' />
  </column>
  <column aggregation='Year' caption='Updated Ts' datatype='datetime' default-type='ordinal' name='[UPDATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_REASON_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_RUN_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DSE_CONFIG' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_PRODUCT_DCO_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SCENARIO_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_TYPE_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_F_CIE_SCORING_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_1506454080614776832]' derivation='None' name='[none:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
  <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.78979' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.21021' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[none:CHANNEL:nk]' type='palette'>
        <map to='\#499894'>
          <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
        </map>
        <map to='\#a0cbe8'>
          <bucket>&quot;SEND_CHANNEL&quot;</bucket>
        </map>
        <map to='\#d37295'>
          <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_1506454080614776832:nk]' type='palette'>
        <map to='\#499894'>
          <bucket>&quot;T3&quot;</bucket>
        </map>
        <map to='\#86bcb6'>
          <bucket>&quot;SEND_CHANNEL&quot;</bucket>
        </map>
        <map to='\#8cd17d'>
          <bucket>&quot;T2&quot;</bucket>
        </map>
        <map to='\#a0cbe8'>
          <bucket>&quot;notier&quot;</bucket>
        </map>
        <map to='\#d7b5a6'>
          <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
        </map>
        <map to='\#f1ce63'>
          <bucket>&quot;ST&quot;</bucket>
        </map>
        <map to='\#ff9d9a'>
          <bucket>&quot;T1&quot;</bucket>
        </map>
        <map to='\#ff9d9a'>
          <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:DCO_REASON_TYPE_NAME:nk]' type='palette'>
        <map to='\#bab0ac'>
          <bucket>&quot;Reject&quot;</bucket>
        </map>
        <map to='\#ff9da7'>
          <bucket>&quot;Recommend&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <datasource-dependencies datasource='Parameters'>
    <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
      <calculation class='tableau' formula='&quot;Channel&quot;' />
      <members>
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
      <calculation class='tableau' formula='&quot;Published&quot;' />
      <members>
        <member value='&quot;(All)&quot;' />
        <member value='&quot;Published&quot;' />
      </members>
    </column>
    <column _.fcp.ParameterDefaultValues.true...source-field='[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model].[INFLUENCE_DISPLAY_NAME]' caption='P_Influence' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account Long-term Importance&quot;'>
      <calculation class='tableau' formula='&quot;Account Long-term Importance&quot;' />
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='VW_DIM_DCO_REASON_RPT' id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DCO_RUN_RPT' id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DSE_CONFIG' id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_PRODUCT_DCO_RPT' id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SCENARIO_RPT' id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SEGMENT_RPT' id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SEGMENT_TYPE_RPT' id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_F_CIE_SCORING_RPT' id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
    <relationships>
      <relationship>
        <expression op='='>
          <expression op='[DCO_REASON_UID]' />
          <expression op='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DCO_RUN_UID (Custom SQL Query)]' />
          <expression op='[DCO_RUN_UID]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SE_CONFIG_ID]' />
          <expression op='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[PRODUCT_UID]' />
          <expression op='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SCENARIO_UID]' />
          <expression op='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[HCP_SEGMENT]' />
          <expression op='[SEGMENT_NAME]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SEGMENT_TYPE]' />
          <expression op='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' />
        </expression>
        <first-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' />
        <second-end-point object-id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608' />
      </relationship>
    </relationships>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='snowflake' version='0.0.0'>
  </primary-dialect>
  <overlay-dialect-set>
    <overlay-dialect class='vizengine' version='0.1.0'>
    </overlay-dialect>
  </overlay-dialect-set>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>false</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='sunday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='User' caption='% LOW Decisive Components' datatype='real' default-type='quantitative' layered='true' name='[% Decisive Components (copy)_1879408442265681937]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;LOW&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
      </column>
      <column aggregation='User' caption='% MED Decisive Components' datatype='real' default-type='quantitative' layered='true' name='[% HIGH Decisive Components (copy)_1879408442266198035]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;MED&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
      </column>
      <column aggregation='User' caption='% HIGH Decisive Components' datatype='real' default-type='quantitative' layered='true' name='[% LOW Decisive Components (copy)_1879408442266107922]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;HIGH&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
      </column>
      <column aggregation='Count' caption='Active Accounts' datatype='string' default-role='measure' default-type='ordinal' layered='true' name='[ACTIVE_ACCOUNTS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='User' caption='% of Candidates Impacted' datatype='real' default-type='quantitative' layered='true' name='[Average score (copy)_1879408442056417282]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='COUNTD(SUGGESTION_CANDIDATE_UID)&#13;&#10;/SUM([Calculation_1879408443317731348])' />
      </column>
      <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Created By (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' layered='true' name='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Created By' datatype='string' default-type='nominal' layered='true' name='[CREATED_BY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' caption='Created Ts (Vw Dim Segment Rpt)' datatype='datetime' default-type='ordinal' layered='true' name='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Year' caption='Created Ts' datatype='datetime' default-type='ordinal' layered='true' name='[CREATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Count' caption='Influencer' datatype='boolean' default-type='nominal' layered='true' name='[Calculation_1445936995299098631]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default'>
        <calculation class='tableau' formula='[Parameters].[Parameter 3]=[INFLUENCE_DISPLAY_NAME]' />
      </column>
      <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' layered='true' name='[Calculation_1506454080614776832]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
      </column>
      <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Text Component Prevalence' datatype='string' default-type='nominal' layered='true' name='[Calculation_1879408442241851404]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='&apos;PERCENTAGE OF CANDIDATES IMPACTED&apos;' />
      </column>
      <column aggregation='Sum' caption='TotalCandidateCnt' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_1879408443317731348]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='{ FIXED RECOMMENDED: &#13;&#10;countd( SUGGESTION_CANDIDATE_UID)}' />
      </column>
      <column aggregation='User' caption='Calculate_accounts' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_527765595207630863]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='COUNTD([ACTIVE_ACCOUNTS])' />
      </column>
      <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
      </column>
      <column aggregation='Count' caption='Dco Reason Code' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Description' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Reason' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Type Code' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Uid (Vw Dim Dco Reason Rpt)' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' caption='Dco Run Date (Vw Dim Dco Run Rpt)' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_RUN_UID (Custom SQL Query)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Run Uid' datatype='string' default-type='nominal' layered='true' name='[DCO_RUN_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Description' datatype='string' default-type='nominal' layered='true' name='[DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Brand Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_BRAND_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Country Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_COUNTRY_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Customer Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_CUSTOMER_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Product Key (Vw Dim Segment Rpt)' datatype='integer' default-type='ordinal' layered='true' name='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Product Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_PRODUCT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Segment Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_SEGMENT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Segment Type Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_SEGMENT_TYPE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='User' caption='Average score' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM (copy)_1327436000330481664]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='SUM([FINAL_SCORE_SUM])/countd([SUGGESTION_CANDIDATE_UID])' />
      </column>
      <column aggregation='Sum' caption='FINAL_SCORE' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
      <column aggregation='Count' caption='Hcp Segment' datatype='string' default-type='nominal' layered='true' name='[HCP_SEGMENT]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[INFLUENCE_DISPLAY_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[INFLUENCE_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' datatype='real' default-type='quantitative' layered='true' name='[INFLUENCE_VALUE]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
      <column aggregation='Count' caption='Is Active Src' datatype='boolean' default-type='nominal' layered='true' name='[IS_ACTIVE_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' caption='Is Competitor' datatype='boolean' default-type='nominal' layered='true' name='[IS_COMPETITOR]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' caption='Is Deleted Src' datatype='boolean' default-type='nominal' layered='true' name='[IS_DELETED_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[IS_MAX_XAI_COMPONENT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_SUB_COMPONENT]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[LAST_DCO_RUN_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Latest Run' datatype='boolean' default-type='nominal' layered='true' name='[LATEST_RUN]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' caption='Post Proc Status' datatype='string' default-type='nominal' layered='true' name='[POST_PROC_STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Id' datatype='integer' default-type='ordinal' layered='true' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Name English' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME_ENGLISH]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Type' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Uid (Vw Dim Product Dco Rpt)' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Uid (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Uid' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[RECOMMENDED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Year' caption='Record End Date' datatype='date' default-type='ordinal' layered='true' name='[RECORD_END_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' caption='Record Start Date' datatype='date' default-type='ordinal' layered='true' name='[RECORD_START_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Description' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Uid (Vw Dim Dco Run Rpt)' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Uid (Vw Dim Scenario Rpt)' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Uid' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Ver' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_VER]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Se Config Id (Vw Dim Dse Config)' datatype='integer' default-type='ordinal' layered='true' name='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SE_CONFIG_ID]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Status' datatype='string' default-type='nominal' layered='true' name='[STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SUGGESTION_CANDIDATE_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='User' caption='% Decisive Components' datatype='real' default-type='quantitative' layered='true' name='[Text Decisive Components (copy)_1879408442255036430]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='SUM(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT = 1 THEN 1 END)&#13;&#10;/ SUM([Calculation_1879408443317731348])' />
      </column>
      <column aggregation='Year' caption='Updated Ts' datatype='datetime' default-type='ordinal' layered='true' name='[UPDATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[XAI_CONFIDENCE_DEGREE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_REASON_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_RUN_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DSE_CONFIG' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_PRODUCT_DCO_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SCENARIO_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_TYPE_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_F_CIE_SCORING_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
      <column-instance column='[Calculation_1506454080614776832]' derivation='None' name='[none:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
      <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.78979' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.21021' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[none:CHANNEL:nk]' type='palette'>
            <map to='#499894'>
              <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
            </map>
            <map to='#a0cbe8'>
              <bucket>&quot;SEND_CHANNEL&quot;</bucket>
            </map>
            <map to='#d37295'>
              <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_1506454080614776832:nk]' type='palette'>
            <map to='#499894'>
              <bucket>&quot;T3&quot;</bucket>
            </map>
            <map to='#86bcb6'>
              <bucket>&quot;SEND_CHANNEL&quot;</bucket>
            </map>
            <map to='#8cd17d'>
              <bucket>&quot;T2&quot;</bucket>
            </map>
            <map to='#a0cbe8'>
              <bucket>&quot;notier&quot;</bucket>
            </map>
            <map to='#d7b5a6'>
              <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
            </map>
            <map to='#f1ce63'>
              <bucket>&quot;ST&quot;</bucket>
            </map>
            <map to='#ff9d9a'>
              <bucket>&quot;T1&quot;</bucket>
            </map>
            <map to='#ff9d9a'>
              <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:DCO_REASON_TYPE_NAME:nk]' type='palette'>
            <map to='#bab0ac'>
              <bucket>&quot;Reject&quot;</bucket>
            </map>
            <map to='#ff9da7'>
              <bucket>&quot;Recommend&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
          <calculation class='tableau' formula='&quot;Channel&quot;' />
        </column>
        <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
          <calculation class='tableau' formula='&quot;Published&quot;' />
        </column>
        <column _.fcp.ParameterDefaultValues.true...source-field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[INFLUENCE_DISPLAY_NAME]' caption='P_Influence' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account Long-term Importance&quot;'>
          <calculation class='tableau' formula='&quot;Account Long-term Importance&quot;' />
        </column>
      </datasource-dependencies>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='VW_DIM_DCO_REASON_RPT' id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_DCO_RUN_RPT' id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_DSE_CONFIG' id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_PRODUCT_DCO_RPT' id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SCENARIO_RPT' id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SEGMENT_RPT' id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SEGMENT_TYPE_RPT' id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_F_CIE_SCORING_RPT' id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
        <relationships>
          <relationship>
            <expression op='='>
              <expression op='[DCO_REASON_UID]' />
              <expression op='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[DCO_RUN_UID (Custom SQL Query)]' />
              <expression op='[DCO_RUN_UID]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SE_CONFIG_ID]' />
              <expression op='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[PRODUCT_UID]' />
              <expression op='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SCENARIO_UID]' />
              <expression op='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[HCP_SEGMENT]' />
              <expression op='[SEGMENT_NAME]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SEGMENT_TYPE]' />
              <expression op='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' />
            </expression>
            <first-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' />
            <second-end-point object-id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608' />
          </relationship>
        </relationships>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
  </datasources>
  <shared-views>
    <shared-view name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750'>
      <datasources>
        <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750' />
      </datasources>
      <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750'>
        <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
          <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
        </column>
        <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
        </column>
        <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
        <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
        <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
        <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
        <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
        <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1604407389790982145:ok]' context='true'>
        <groupfilter function='member' level='[none:Calculation_1604407389790982145:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
      </filter>
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_864972641503416320:nk]' context='true'>
        <groupfilter function='level-members' level='[none:Calculation_864972641503416320:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
      <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]' context='true' included-values='all' />
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]' context='true'>
        <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]' context='true'>
        <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' />
      </filter>
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' context='true'>
        <groupfilter function='level-members' level='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SE_CONFIG_NAME:nk]' context='true'>
        <groupfilter function='level-members' level='[none:SE_CONFIG_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
    </shared-view>
  </shared-views>
  <worksheets>
    <worksheet name='1.0 View Filters'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontcolor='#000000' fontname='Tableau Medium'>${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scores</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750'>
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:CHANNEL:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1604407389790982145:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_864972641503416320:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SE_CONFIG_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='quick-filter'>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' value='Segment Type'>
              <formatted-text>
                <run fontcolor='#000000'>Segment Type</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]' value='Segment'>
              <formatted-text>
                <run fontcolor='#000000' fontname='Tableau Medium'>Segment</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]' value='Product'>
              <formatted-text>
                <run fontcolor='#000000' fontname='Tableau Medium'>Product</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]' value='Run Date Range'>
              <formatted-text>
                <run fontcolor='#000000'>Run Date Range</run>
              </formatted-text>
            </format>
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-cull' value='true' />
                <format attr='mark-labels-show' value='false' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows />
        <cols />
      </table>
      <simple-id uuid='{FD4BD652-DEF6-4AAD-AFB5-B2085C2A4B0D}' />
    </worksheet>
    <worksheet name='1.1 CIE Scoring by Channel '>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontcolor='#000000' fontname='Tableau Medium' fontsize='12'>Expected Value by Channel/Segment</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
              <calculation class='tableau' formula='&quot;Channel&quot;' />
            </column>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750'>
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' layered='true' name='[Calculation_1506454080614776832]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
            </column>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date (Vw Dim Dco Run Rpt)' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='User' caption='Average score' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM (copy)_1327436000330481664]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='SUM([FINAL_SCORE_SUM])/countd([SUGGESTION_CANDIDATE_UID])' />
            </column>
            <column aggregation='Sum' caption='FINAL_SCORE' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[INFLUENCE_DISPLAY_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SUGGESTION_CANDIDATE_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[Calculation_1506454080614776832]' derivation='Attribute' name='[attr:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_CANDIDATE_UID]' derivation='CountD' name='[ctd:SUGGESTION_CANDIDATE_UID:qk]' pivot='key' type='quantitative' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1506454080614776832]' derivation='None' name='[none:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[INFLUENCE_DISPLAY_NAME]' derivation='None' name='[none:INFLUENCE_DISPLAY_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[FINAL_SCORE_SUM]' derivation='Sum' name='[sum:FINAL_SCORE_SUM:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' pivot='key' type='ordinal' />
            <column-instance column='[FINAL_SCORE_SUM (copy)_1327436000330481664]' derivation='User' name='[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:CHANNEL:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]'>
            <groupfilter function='member' level='[none:INFLUENCE_DISPLAY_NAME:nk]' member='&quot;Expected Value&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1604407389790982145:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_864972641503416320:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='display' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' scope='cols' value='false' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]' value='93' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' value='n!en_US!#,##0.00' />
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' value='33' />
          </style-rule>
          <style-rule element='header'>
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' value='36' />
          </style-rule>
          <style-rule element='label'>
            <format attr='display' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' value='false' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' value='*m/d/yy' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' value='n!en_US!#,##0.00' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#f0f3fa' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[sum:FINAL_SCORE_SUM:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[ctd:SUGGESTION_CANDIDATE_UID:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[attr:Calculation_1506454080614776832:nk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878' fontsize='9'>Channel/Segment:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[attr:Calculation_1506454080614776832:nk]> ]]></run>
                <run bold='true' fontcolor='#787878' fontsize='9'>Æ </run>
                <run fontcolor='#787878' fontsize='9'>Æ     </run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='9'>Run Date:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='9'>Average Score:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#898989' fontsize='9'>Reason Type</run>
                <run fontcolor='#787878' fontsize='9'>:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]>]]></run>
                <run fontcolor='#787878' fontsize='9'>Æ </run>
                <run>Æ&#10;</run>
              </formatted-text>
            </customized-tooltip>
            <customized-label>
              <formatted-text>
                <run fontsize='8'>&lt;</run>
                <run fontsize='8'>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]</run>
                <run fontsize='8'>&gt;</run>
              </formatted-text>
            </customized-label>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minheight' value='-1' />
                <format attr='maxheight' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1506454080614776832:nk] / [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk])</rows>
        <cols>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok] * [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk])</cols>
      </table>
      <simple-id uuid='{1F0392D2-7827-44E7-B260-7E43FE9D63C5}' />
    </worksheet>
    <worksheet name='1.2 Overall Score: By Segment'>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontcolor='#1d1c1d' fontname='Tableau Medium' fontsize='12'>Accounts by Channel/Segment</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
              <calculation class='tableau' formula='&quot;Channel&quot;' />
            </column>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750'>
            <column aggregation='Count' caption='Active Accounts' datatype='string' default-role='measure' default-type='ordinal' layered='true' name='[ACTIVE_ACCOUNTS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' layered='true' name='[Calculation_1506454080614776832]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
            </column>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='User' caption='Calculate_accounts' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_527765595207630863]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='COUNTD([ACTIVE_ACCOUNTS])' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date (Vw Dim Dco Run Rpt)' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='User' caption='Average score' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM (copy)_1327436000330481664]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='SUM([FINAL_SCORE_SUM])/countd([SUGGESTION_CANDIDATE_UID])' />
            </column>
            <column aggregation='Sum' caption='FINAL_SCORE' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[INFLUENCE_DISPLAY_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SUGGESTION_CANDIDATE_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1506454080614776832]' derivation='None' name='[none:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[INFLUENCE_DISPLAY_NAME]' derivation='None' name='[none:INFLUENCE_DISPLAY_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_527765595207630863]' derivation='User' name='[usr:Calculation_527765595207630863:qk]' pivot='key' type='quantitative' />
            <column-instance column='[FINAL_SCORE_SUM (copy)_1327436000330481664]' derivation='User' name='[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:CHANNEL:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]'>
            <groupfilter function='member' level='[none:INFLUENCE_DISPLAY_NAME:nk]' member='&quot;Expected Value&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1604407389790982145:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_864972641503416320:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Calculation_527765595207630863:qk]' scope='rows' value='' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' value='57' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' value='*m/d/yy' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='title'>
            <format attr='border-color' value='#d4a6c8' />
            <format attr='border-width' value='0' />
            <format attr='border-style' value='none' />
            <format attr='background-color' value='#f0f3fa' />
          </style-rule>
        </style>
        <panes>
          <pane id='1' selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Calculation_527765595207630863:qk]' />
              <lod column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878' fontsize='9'>Channel/Segment:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1506454080614776832:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='9'>Run Date:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#898989' fontsize='9'>Active Accounts</run>
                <run fontcolor='#787878' fontsize='9'>:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Calculation_527765595207630863:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#898989' fontsize='9'>Average Score</run>
                <run fontcolor='#787878' fontsize='9'>:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#898989' fontsize='9'>Reason Type</run>
                <run fontcolor='#787878' fontsize='9'>:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <customized-label>
              <formatted-text>
                <run fontsize='8'>&lt;</run>
                <run fontsize='8'>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Calculation_527765595207630863:qk]</run>
                <run fontsize='8'>&gt;</run>
              </formatted-text>
            </customized-label>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-cull' value='true' />
                <format attr='mark-labels-show' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Calculation_527765595207630863:qk]</rows>
        <cols>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1506454080614776832:nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok] / [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]))</cols>
      </table>
      <simple-id uuid='{EB804495-EACA-49E7-A27A-0782A9A91F3A}' />
    </worksheet>
    <worksheet name='1.3 Component Prevalence'>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontcolor='#172b4d' fontname='Tableau Medium' fontsize='12'>Component Prevalence</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750'>
            <column aggregation='User' caption='% of Candidates Impacted' datatype='real' default-type='quantitative' layered='true' name='[Average score (copy)_1879408442056417282]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='COUNTD(SUGGESTION_CANDIDATE_UID)&#13;&#10;/SUM([Calculation_1879408443317731348])' />
            </column>
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Sum' caption='TotalCandidateCnt' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_1879408443317731348]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='{ FIXED RECOMMENDED: &#13;&#10;countd( SUGGESTION_CANDIDATE_UID)}' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[INFLUENCE_DISPLAY_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' datatype='real' default-type='quantitative' layered='true' name='[INFLUENCE_VALUE]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[RECOMMENDED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SUGGESTION_CANDIDATE_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[INFLUENCE_VALUE]' derivation='Avg' name='[avg:INFLUENCE_VALUE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[INFLUENCE_VALUE]' derivation='Max' name='[max:INFLUENCE_VALUE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[INFLUENCE_VALUE]' derivation='Median' name='[med:INFLUENCE_VALUE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[INFLUENCE_VALUE]' derivation='Min' name='[min:INFLUENCE_VALUE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[INFLUENCE_DISPLAY_NAME]' derivation='None' name='[none:INFLUENCE_DISPLAY_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[Average score (copy)_1879408442056417282]' derivation='User' name='[usr:Average score (copy)_1879408442056417282:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[:Measure Names]'>
            <groupfilter function='level-members' level='[:Measure Names]' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:CHANNEL:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]'>
            <groupfilter function='except' user:ui-domain='database' user:ui-enumeration='exclusive' user:ui-marker='enumerate'>
              <groupfilter function='level-members' level='[none:INFLUENCE_DISPLAY_NAME:nk]' />
              <groupfilter function='member' level='[none:INFLUENCE_DISPLAY_NAME:nk]' member='&quot;Expected Value&quot;' />
            </groupfilter>
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1604407389790982145:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_864972641503416320:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[:Measure Names]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='display' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Average score (copy)_1879408442056417282:qk]' scope='cols' value='false' />
            <format attr='tick-color' value='#00000000' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='width' value='171' />
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' value='18' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Average score (copy)_1879408442056417282:qk]' value='p0.00%' />
            <format attr='border-color' scope='rows' value='#000000' />
            <format attr='border-width' scope='rows' value='0' />
            <format attr='border-style' scope='rows' value='none' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[avg:INFLUENCE_VALUE:qk]' value='n#,##0.00;-#,##0.00' />
            <format attr='text-align' value='left' />
            <format attr='vertical-align' value='center' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[min:INFLUENCE_VALUE:qk]' value='n#,##0.00;-#,##0.00' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[max:INFLUENCE_VALUE:qk]' value='n#,##0.00;-#,##0.00' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[med:INFLUENCE_VALUE:qk]' value='n#,##0.00;-#,##0.00' />
          </style-rule>
          <style-rule element='header'>
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]' value='284' />
            <format attr='border-width' data-class='total' value='0' />
            <format attr='border-style' data-class='total' value='none' />
            <format attr='border-style' scope='rows' value='solid' />
            <format attr='border-color' scope='rows' value='#e6e6e6' />
            <format attr='border-width' data-class='subtotal' scope='rows' value='0' />
            <format attr='border-style' data-class='subtotal' scope='rows' value='none' />
          </style-rule>
          <style-rule element='label'>
            <format attr='display' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' value='false' />
            <format attr='text-align' value='left' />
            <format attr='vertical-align' value='center' />
          </style-rule>
          <style-rule element='pane'>
            <format attr='border-width' data-class='total' value='0' />
            <format attr='border-style' data-class='total' value='none' />
            <format attr='border-width' scope='rows' value='1' />
            <format attr='border-style' scope='rows' value='solid' />
            <format attr='border-color' scope='rows' value='#e6e6e6' />
            <format attr='border-width' data-class='subtotal' scope='rows' value='0' />
            <format attr='border-style' data-class='subtotal' scope='rows' value='none' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='display-field-labels' scope='rows' value='false' />
          </style-rule>
          <style-rule element='dropline'>
            <format attr='stroke-size' value='0' />
            <format attr='line-visibility' value='off' />
          </style-rule>
          <style-rule element='refline'>
            <format attr='stroke-size' value='0' />
            <format attr='line-visibility' value='off' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='stroke-size' scope='cols' value='0' />
            <format attr='line-visibility' scope='cols' value='off' />
            <format attr='line-pattern-only' scope='rows' value='solid' />
            <format attr='stroke-size' scope='rows' value='0' />
            <format attr='line-visibility' scope='rows' value='off' />
          </style-rule>
          <style-rule element='zeroline'>
            <format attr='stroke-size' value='0' />
            <format attr='line-visibility' value='off' />
          </style-rule>
          <style-rule element='table-div'>
            <format attr='stroke-size' scope='cols' value='0' />
            <format attr='line-visibility' scope='cols' value='off' />
            <format attr='stroke-size' scope='rows' value='0' />
            <format attr='line-visibility' scope='rows' value='off' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#f0f3fa' />
          </style-rule>
          <style-rule element='legend'>
            <format attr='col-width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' value='120' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <mark-sizing mark-sizing-setting='marks-scaling-off' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Average score (copy)_1879408442056417282:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[avg:INFLUENCE_VALUE:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[min:INFLUENCE_VALUE:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[max:INFLUENCE_VALUE:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[med:INFLUENCE_VALUE:qk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878' fontsize='9'>Influencer :&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='9'>Reason Type:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#898989' fontsize='9'>% of Candidates Impacted</run>
                <run fontcolor='#787878' fontsize='9'>:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Average score (copy)_1879408442056417282:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='9'>Avg. Influence Value:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[avg:INFLUENCE_VALUE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='9'>Min. Influence Value:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[min:INFLUENCE_VALUE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='9'>Max. Influence Value:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[max:INFLUENCE_VALUE:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='9'>Median Influence Value:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[med:INFLUENCE_VALUE:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <customized-label>
              <formatted-text>
                <run fontsize='8'>&lt;</run>
                <run fontsize='8'>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Average score (copy)_1879408442056417282:qk]</run>
                <run fontsize='8'>&gt;</run>
              </formatted-text>
            </customized-label>
            <style>
              <style-rule element='cell'>
                <format attr='text-align' value='right' />
              </style-rule>
              <style-rule element='mark'>
                <format attr='size' value='1' />
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
                <format attr='minheight' value='-1' />
                <format attr='maxheight' value='-1' />
              </style-rule>
              <style-rule element='trendline'>
                <format attr='stroke-size' value='0' />
                <format attr='line-visibility' value='off' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk] / [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk])</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Average score (copy)_1879408442056417282:qk]</cols>
      </table>
      <simple-id uuid='{3F019924-DFF8-4FE8-9C84-D3ADAB15FCDC}' />
    </worksheet>
    <worksheet name='1.3 Icon'>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750'>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Text Component Prevalence' datatype='string' default-type='nominal' layered='true' name='[Calculation_1879408442241851404]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='&apos;PERCENTAGE OF CANDIDATES IMPACTED&apos;' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_1879408442241851404]' derivation='None' name='[none:Calculation_1879408442241851404:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1604407389790982145:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_864972641503416320:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SE_CONFIG_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style />
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Shape' />
            <encodings>
              <lod column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1879408442241851404:nk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#1b1b1b' fontsize='9'>Percentage of Candidates Impacted - The % of candidates impacted by this component.</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='shape' value='Codex/Info-Icon.png' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows />
        <cols />
      </table>
      <simple-id uuid='{4B4B437C-8FB4-41DE-8A03-C048B9E04BA6}' />
    </worksheet>
    <worksheet name='1.4 Decisive Components'>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontcolor='#172b4d' fontname='Tableau Medium' fontsize='12'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750'>
            <column aggregation='User' caption='% LOW Decisive Components' datatype='real' default-type='quantitative' layered='true' name='[% Decisive Components (copy)_1879408442265681937]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;LOW&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
            </column>
            <column aggregation='User' caption='% MED Decisive Components' datatype='real' default-type='quantitative' layered='true' name='[% HIGH Decisive Components (copy)_1879408442266198035]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;MED&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
            </column>
            <column aggregation='User' caption='% HIGH Decisive Components' datatype='real' default-type='quantitative' layered='true' name='[% LOW Decisive Components (copy)_1879408442266107922]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;HIGH&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
            </column>
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Sum' caption='TotalCandidateCnt' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_1879408443317731348]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='{ FIXED RECOMMENDED: &#13;&#10;countd( SUGGESTION_CANDIDATE_UID)}' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[INFLUENCE_DISPLAY_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[IS_MAX_XAI_COMPONENT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_SUB_COMPONENT]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[RECOMMENDED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SUGGESTION_CANDIDATE_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='User' caption='% Decisive Components' datatype='real' default-type='quantitative' layered='true' name='[Text Decisive Components (copy)_1879408442255036430]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='SUM(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT = 1 THEN 1 END)&#13;&#10;/ SUM([Calculation_1879408443317731348])' />
            </column>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[XAI_CONFIDENCE_DEGREE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[INFLUENCE_DISPLAY_NAME]' derivation='None' name='[none:INFLUENCE_DISPLAY_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[IS_SUB_COMPONENT]' derivation='None' name='[none:IS_SUB_COMPONENT:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[% Decisive Components (copy)_1879408442265681937]' derivation='User' name='[usr:% Decisive Components (copy)_1879408442265681937:qk]' pivot='key' type='quantitative' />
            <column-instance column='[% HIGH Decisive Components (copy)_1879408442266198035]' derivation='User' name='[usr:% HIGH Decisive Components (copy)_1879408442266198035:qk]' pivot='key' type='quantitative' />
            <column-instance column='[% LOW Decisive Components (copy)_1879408442266107922]' derivation='User' name='[usr:% LOW Decisive Components (copy)_1879408442266107922:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Text Decisive Components (copy)_1879408442255036430]' derivation='User' name='[usr:Text Decisive Components (copy)_1879408442255036430:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[:Measure Names]'>
            <groupfilter function='level-members' level='[:Measure Names]' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]' context='true' filter-group='3'>
            <groupfilter function='level-members' level='[none:CHANNEL:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' context='true'>
            <groupfilter function='member' level='[none:DCO_REASON_TYPE_NAME:nk]' member='&quot;Recommend&quot;' user:ui-domain='database' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]'>
            <groupfilter function='except' user:ui-domain='database' user:ui-enumeration='exclusive' user:ui-marker='enumerate'>
              <groupfilter function='level-members' level='[none:INFLUENCE_DISPLAY_NAME:nk]' />
              <groupfilter function='member' level='[none:INFLUENCE_DISPLAY_NAME:nk]' member='&quot;Expected Value&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:IS_SUB_COMPONENT:nk]'>
            <groupfilter function='member' level='[none:IS_SUB_COMPONENT:nk]' member='false' user:ui-domain='database' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1604407389790982145:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_864972641503416320:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:IS_SUB_COMPONENT:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[:Measure Names]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='display' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Text Decisive Components (copy)_1879408442255036430:qk]' scope='cols' value='false' />
            <format attr='tick-color' value='#00000000' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='width' value='171' />
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]' value='21' />
            <format attr='border-color' scope='rows' value='#000000' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Text Decisive Components (copy)_1879408442255036430:qk]' value='p0.00%' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:% Decisive Components (copy)_1879408442265681937:qk]' value='p0.00%' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:% HIGH Decisive Components (copy)_1879408442266198035:qk]' value='p0.00%' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:% LOW Decisive Components (copy)_1879408442266107922:qk]' value='p0.00%' />
            <format attr='text-align' value='left' />
            <format attr='vertical-align' value='center' />
            <format attr='border-style' scope='cols' value='solid' />
            <format attr='border-width' scope='rows' value='1' />
            <format attr='border-style' scope='rows' value='solid' />
          </style-rule>
          <style-rule element='header'>
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]' value='284' />
            <format attr='height-header' value='10' />
            <format attr='border-width' data-class='total' value='0' />
            <format attr='border-style' data-class='total' value='none' />
            <format attr='border-style' scope='rows' value='solid' />
            <format attr='border-color' scope='rows' value='#e6e6e6' />
            <format attr='border-width' data-class='subtotal' scope='rows' value='0' />
            <format attr='border-style' data-class='subtotal' scope='rows' value='none' />
          </style-rule>
          <style-rule element='label'>
            <format attr='display' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' value='false' />
            <format attr='text-align' value='left' />
            <format attr='vertical-align' value='center' />
          </style-rule>
          <style-rule element='pane'>
            <format attr='border-width' data-class='total' value='0' />
            <format attr='border-style' data-class='total' value='none' />
            <format attr='border-width' scope='rows' value='1' />
            <format attr='border-style' scope='rows' value='solid' />
            <format attr='border-color' scope='rows' value='#e6e6e6' />
            <format attr='border-width' data-class='subtotal' scope='rows' value='0' />
            <format attr='border-style' data-class='subtotal' scope='rows' value='none' />
            <format attr='border-width' data-class='total' scope='rows' value='1' />
            <format attr='border-style' data-class='total' scope='rows' value='solid' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='display-field-labels' scope='rows' value='false' />
          </style-rule>
          <style-rule element='dropline'>
            <format attr='stroke-size' value='0' />
            <format attr='line-visibility' value='off' />
          </style-rule>
          <style-rule element='refline'>
            <format attr='stroke-size' value='0' />
            <format attr='line-visibility' value='off' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='stroke-size' scope='cols' value='0' />
            <format attr='line-visibility' scope='cols' value='off' />
            <format attr='line-pattern-only' scope='rows' value='solid' />
            <format attr='stroke-size' scope='rows' value='0' />
            <format attr='line-visibility' scope='rows' value='off' />
          </style-rule>
          <style-rule element='zeroline'>
            <format attr='stroke-size' value='0' />
            <format attr='line-visibility' value='off' />
          </style-rule>
          <style-rule element='table-div'>
            <format attr='stroke-size' scope='cols' value='0' />
            <format attr='line-visibility' scope='cols' value='off' />
            <format attr='stroke-size' scope='rows' value='1' />
            <format attr='line-visibility' scope='rows' value='on' />
            <format attr='line-pattern-only' scope='rows' value='solid' />
            <format attr='div-level' scope='rows' value='1' />
            <format attr='stroke-color' scope='rows' value='#e6e6e6' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#f0f3fa' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <mark-sizing mark-sizing-setting='marks-scaling-off' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:% Decisive Components (copy)_1879408442265681937:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:% LOW Decisive Components (copy)_1879408442266107922:qk]' />
              <tooltip column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:% HIGH Decisive Components (copy)_1879408442266198035:qk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Text Decisive Components (copy)_1879408442255036430:qk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#898989' fontsize='9'>Influencer :&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#898989' fontsize='9'>% Decisive Components:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Text Decisive Components (copy)_1879408442255036430:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#898989' fontsize='9'>HIGH:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:% LOW Decisive Components (copy)_1879408442266107922:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#898989' fontsize='9'>MEDIUM:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:% HIGH Decisive Components (copy)_1879408442266198035:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#898989' fontsize='9'>LOW:&#9;</run>
                <run bold='true' fontsize='9'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:% Decisive Components (copy)_1879408442265681937:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <customized-label>
              <formatted-text>
                <run fontsize='8'>&lt;</run>
                <run fontsize='8'>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Text Decisive Components (copy)_1879408442255036430:qk]</run>
                <run fontsize='8'>&gt;</run>
              </formatted-text>
            </customized-label>
            <style>
              <style-rule element='cell'>
                <format attr='text-align' value='right' />
              </style-rule>
              <style-rule element='mark'>
                <format attr='size' value='1' />
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
                <format attr='minheight' value='-1' />
                <format attr='maxheight' value='-1' />
              </style-rule>
              <style-rule element='trendline'>
                <format attr='stroke-size' value='0' />
                <format attr='line-visibility' value='off' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:INFLUENCE_DISPLAY_NAME:nk]</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[usr:Text Decisive Components (copy)_1879408442255036430:qk]</cols>
      </table>
      <simple-id uuid='{E6CF314E-94AB-4B39-8E10-6F1598BDCCA0}' />
    </worksheet>
    <worksheet name='1.4 Icon'>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750'>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Text Component Prevalence' datatype='string' default-type='nominal' layered='true' name='[Calculation_1879408442241851404]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='&apos;PERCENTAGE OF CANDIDATES IMPACTED&apos;' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_1879408442241851404]' derivation='None' name='[none:Calculation_1879408442241851404:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1604407389790982145:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_864972641503416320:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SE_CONFIG_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='width' value='59' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Shape' />
            <encodings>
              <lod column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_1879408442241851404:nk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#1b1b1b' fontsize='9'>Decisive Component Percentage - The % of time that the component score is the primary driver leading to the candidate’s recommendation or rejection.</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='shape' value='Codex/Info-Icon.png' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows />
        <cols />
      </table>
      <simple-id uuid='{6121286A-96BE-41DB-B6C1-62C7DC6632FB}' />
    </worksheet>
  </worksheets>
  <dashboards>
    <dashboard _.fcp.AccessibleZoneTabOrder.true...enable-sort-zone-taborder='true' name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scores'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontname='Tableau Medium' fontsize='14'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEScores/${CUSTOMER_NAME}${ENVIRONMENT}CIEScores?rev=' id='8175631' path='/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEScores' revision='' site='${TABLEAU_SITE}' />
      <style />
      <size sizing-mode='automatic' />
      <datasources>
        <datasource name='Parameters' />
        <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750' />
      </datasources>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
          <calculation class='tableau' formula='&quot;Channel&quot;' />
          <members>
            <member value='&quot;Channel&quot;' />
            <member value='&quot;Segment&quot;' />
          </members>
        </column>
        <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
          <calculation class='tableau' formula='&quot;Published&quot;' />
          <members>
            <member value='&quot;(All)&quot;' />
            <member value='&quot;Published&quot;' />
          </members>
        </column>
      </datasource-dependencies>
      <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750'>
        <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
        </column>
        <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
        <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
        <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
        <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
        <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
        <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <zones>
        <zone h='100000' id='4' type-v2='layout-basic' w='100000' x='0' y='0'>
          <zone h='100000' id='18' param='vert' type-v2='layout-flow' w='100000' x='0' y='0'>
            <zone fixed-size='37' h='5528' id='22' is-fixed='true' name='1.0 View Filters' w='100000' x='0' y='0'>
              <layout-cache type-h='fixed' type-w='fixed' />
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='94472' id='16' type-v2='layout-basic' w='100000' x='0' y='5528'>
              <zone h='78711' id='7' param='horz' type-v2='layout-flow' w='100000' x='0' y='21289'>
                <zone h='78711' id='5' type-v2='layout-basic' w='100000' x='0' y='21289'>
                  <zone h='39322' id='12' name='1.2 Overall Score: By Segment' w='50011' x='49989' y='21289'>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='solid' />
                      <format attr='border-width' value='1' />
                      <format attr='margin' value='4' />
                      <format attr='margin-left' value='10' />
                    </zone-style>
                  </zone>
                  <zone h='39322' id='13' name='1.1 CIE Scoring by Channel ' w='49989' x='0' y='21289'>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='solid' />
                      <format attr='border-width' value='1' />
                      <format attr='margin' value='4' />
                      <format attr='margin-right' value='10' />
                    </zone-style>
                  </zone>
                  <zone h='39389' id='47' param='vert' type-v2='layout-flow' w='49990' x='0' y='60611'>
                    <zone fixed-size='32' h='3931' id='105' is-fixed='true' param='horz' type-v2='layout-flow' w='48873' x='349' y='61225'>
                      <zone forceUpdate='true' h='3931' id='122' type-v2='text' w='46082' x='349' y='61225'>
                        <formatted-text>
                          <run fontalignment='1' fontcolor='#172b4d' fontname='Tableau Medium' fontsize='12'>Component Prevalence</run>
                        </formatted-text>
                        <zone-style>
                          <format attr='border-color' value='#000000' />
                          <format attr='border-style' value='none' />
                          <format attr='border-width' value='0' />
                          <format attr='margin' value='0' />
                          <format attr='background-color' value='#f0f3fa' />
                        </zone-style>
                      </zone>
                      <zone fixed-size='40' h='3931' id='114' is-fixed='true' name='1.3 Icon' show-title='false' w='2791' x='46431' y='61225'>
                        <layout-cache cell-count-h='1' type-h='cell' type-w='cell' />
                        <zone-style>
                          <format attr='border-color' value='#000000' />
                          <format attr='border-style' value='none' />
                          <format attr='border-width' value='0' />
                          <format attr='margin' value='0' />
                        </zone-style>
                      </zone>
                    </zone>
                    <zone h='123' id='3' name='1.3 Component Prevalence' show-title='false' w='48873' x='349' y='65156'>
                      <layout-cache fixed-size-h='1' minwidth='385' type-h='fixed' type-w='scalable' />
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='4' />
                        <format attr='margin-top' value='0' />
                        <format attr='margin-right' value='10' />
                        <format attr='margin-bottom' value='0' />
                        <format attr='margin-left' value='0' />
                      </zone-style>
                    </zone>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='solid' />
                      <format attr='border-width' value='1' />
                      <format attr='margin' value='4' />
                      <format attr='margin-right' value='10' />
                    </zone-style>
                  </zone>
                  <zone h='39389' id='50' param='vert' type-v2='layout-flow' w='50010' x='49990' y='60611'>
                    <zone fixed-size='32' h='3931' id='120' is-fixed='true' param='horz' type-v2='layout-flow' w='48893' x='50758' y='61225'>
                      <zone forceUpdate='true' h='3931' id='121' type-v2='text' w='46102' x='50758' y='61225'>
                        <formatted-text>
                          <run fontalignment='1' fontcolor='#000000' fontname='Tableau Medium' fontsize='12'>Decisive Components</run>
                        </formatted-text>
                        <zone-style>
                          <format attr='border-color' value='#000000' />
                          <format attr='border-style' value='none' />
                          <format attr='border-width' value='0' />
                          <format attr='margin' value='0' />
                          <format attr='background-color' value='#f0f3fa' />
                        </zone-style>
                      </zone>
                      <zone fixed-size='40' h='3931' id='119' is-fixed='true' name='1.4 Icon' show-title='false' w='2791' x='96860' y='61225'>
                        <layout-cache cell-count-h='1' type-h='cell' type-w='cell' />
                        <zone-style>
                          <format attr='border-color' value='#000000' />
                          <format attr='border-style' value='none' />
                          <format attr='border-width' value='0' />
                          <format attr='margin' value='0' />
                        </zone-style>
                      </zone>
                    </zone>
                    <zone h='0' id='49' name='1.4 Decisive Components' show-title='false' w='48893' x='50758' y='65156'>
                      <layout-cache minwidth='385' type-h='cell' type-w='scalable' />
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='0' />
                      </zone-style>
                    </zone>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='solid' />
                      <format attr='border-width' value='1' />
                      <format attr='margin' value='4' />
                      <format attr='margin-left' value='10' />
                    </zone-style>
                  </zone>
                </zone>
              </zone>
              <zone h='7750' id='14' layout-strategy-id='distribute-evenly' param='horz' type-v2='layout-flow' w='100000' x='0' y='5528'>
                <zone h='7740' id='27' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' show-apply='true' type-v2='filter' values='database' w='20028' x='0' y='5528'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='7740' id='29' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='20028' x='20028' y='5528'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='7740' id='21' mode='checkdropdown' name='1.1 CIE Scoring by Channel ' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='20028' x='40056' y='5528'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='7740' id='28' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SE_CONFIG_NAME:nk]' show-apply='true' type-v2='filter' values='database' w='19958' x='60084' y='5528'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='7740' id='34' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]' type-v2='filter' values='relevant' w='19958' x='80042' y='5528'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
              </zone>
              <zone h='8011' id='25' layout-strategy-id='distribute-evenly' param='horz' type-v2='layout-flow' w='100000' x='0' y='13278'>
                <zone h='7985' id='37' mode='compact' param='[Parameters].[Parameter 2]' type-v2='paramctrl' w='20028' x='0' y='13268'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='7985' id='38' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_864972641503416320:nk]' show-apply='true' type-v2='filter' values='relevant' w='20028' x='20028' y='13268'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='7985' id='20' mode='checkdropdown' name='1.1 CIE Scoring by Channel ' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]' show-apply='true' show-filter-state='false' type-v2='filter' values='database' w='20028' x='40056' y='13268'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='7985' id='9' mode='compact' param='[Parameters].[Parameter 1]' type-v2='paramctrl' w='19958' x='60084' y='13268'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='7985' id='8' leg-item-layout='horz' name='1.3 Component Prevalence' pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' type-v2='color' w='19958' x='80042' y='13268'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
              </zone>
            </zone>
          </zone>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='none' />
            <format attr='border-width' value='0' />
            <format attr='margin' value='0' />
          </zone-style>
        </zone>
      </zones>
      <devicelayouts>
        <devicelayout auto-generated='true' name='Phone'>
          <layout-options>
            <title>
              <formatted-text>
                <run bold='true' fontalignment='1' fontname='Tableau Medium' fontsize='14'>&lt;Sheet Name&gt;</run>
              </formatted-text>
            </title>
          </layout-options>
          <size maxheight='2050' minheight='2050' sizing-mode='vscroll' />
          <zones>
            <zone h='100000' id='148' type-v2='layout-basic' w='100000' x='0' y='0'>
              <zone h='98034' id='147' param='vert' type-v2='layout-flow' w='98884' x='558' y='983'>
                <zone h='7740' id='27' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' show-apply='true' type-v2='filter' values='database' w='20028' x='0' y='5528'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7740' id='29' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SEGMENT_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='20028' x='20028' y='5528'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7740' id='28' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:SE_CONFIG_NAME:nk]' show-apply='true' type-v2='filter' values='database' w='19958' x='60084' y='5528'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7740' id='34' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]' type-v2='filter' values='relevant' w='19958' x='80042' y='5528'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7985' id='38' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:Calculation_864972641503416320:nk]' show-apply='true' type-v2='filter' values='relevant' w='20028' x='20028' y='13268'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='36' h='5528' id='22' is-fixed='true' name='1.0 View Filters' w='100000' x='0' y='0'>
                  <layout-cache type-h='fixed' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7985' id='37' mode='compact' param='[Parameters].[Parameter 2]' type-v2='paramctrl' w='20028' x='0' y='13268'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7985' id='9' mode='compact' param='[Parameters].[Parameter 1]' type-v2='paramctrl' w='19958' x='60084' y='13268'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7740' id='21' mode='checkdropdown' name='1.1 CIE Scoring by Channel ' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:PRODUCT_NAME:nk]' show-apply='true' type-v2='filter' values='relevant' w='20028' x='40056' y='5528'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7985' id='20' mode='checkdropdown' name='1.1 CIE Scoring by Channel ' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:CHANNEL:nk]' show-apply='true' show-filter-state='false' type-v2='filter' values='database' w='20028' x='40056' y='13268'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='39322' id='13' is-fixed='true' name='1.1 CIE Scoring by Channel ' w='49989' x='0' y='21289'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='39322' id='12' is-fixed='true' name='1.2 Overall Score: By Segment' w='50011' x='49989' y='21289'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone forceUpdate='true' h='3931' id='122' type-v2='text' w='46082' x='349' y='61225'>
                  <formatted-text>
                    <run fontalignment='1' fontcolor='#172b4d' fontname='Tableau Medium' fontsize='12'>Component Prevalence</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#f0f3fa' />
                  </zone-style>
                </zone>
                <zone fixed-size='31' h='3931' id='114' is-fixed='true' name='1.3 Icon' show-title='false' w='2791' x='46431' y='61225'>
                  <layout-cache cell-count-h='1' type-h='cell' type-w='cell' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone forceUpdate='true' h='3931' id='121' type-v2='text' w='46102' x='50758' y='61225'>
                  <formatted-text>
                    <run fontalignment='1' fontcolor='#000000' fontname='Tableau Medium' fontsize='12'>Decisive Components</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#f0f3fa' />
                  </zone-style>
                </zone>
                <zone fixed-size='31' h='3931' id='119' is-fixed='true' name='1.4 Icon' show-title='false' w='2791' x='96860' y='61225'>
                  <layout-cache cell-count-h='1' type-h='cell' type-w='cell' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='123' id='3' is-fixed='true' name='1.3 Component Prevalence' show-title='false' w='48873' x='349' y='65156'>
                  <layout-cache fixed-size-h='1' minwidth='385' type-h='fixed' type-w='scalable' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7985' id='8' leg-item-layout='horz' name='1.3 Component Prevalence' pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' type-v2='color' w='19958' x='80042' y='13268'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='0' id='49' is-fixed='true' name='1.4 Decisive Components' show-title='false' w='48893' x='50758' y='65156'>
                  <layout-cache minwidth='385' type-h='cell' type-w='scalable' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='0' />
              </zone-style>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{AFA3F272-C824-4779-B87D-2A68C8A6211D}' />
    </dashboard>
  </dashboards>
  <windows source-height='30'>
    <window class='dashboard' maximized='true' name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scores'>
      <viewpoints>
        <viewpoint name='1.0 View Filters' />
        <viewpoint name='1.1 CIE Scoring by Channel '>
          <zoom type='fit-height' />
        </viewpoint>
        <viewpoint name='1.2 Overall Score: By Segment'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='1.3 Component Prevalence'>
          <zoom type='fit-width' />
        </viewpoint>
        <viewpoint name='1.3 Icon'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='1.4 Decisive Components'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='1.4 Icon'>
          <zoom type='entire-view' />
        </viewpoint>
      </viewpoints>
      <active id='-1' />
      <simple-id uuid='{6C3B5DB5-CF03-407B-804E-323240899E41}' />
    </window>
    <window class='worksheet' hidden='true' name='1.0 View Filters'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{6D59CEB9-F96B-4619-9B5E-D8B55B09A1BD}' />
    </window>
    <window class='worksheet' hidden='true' name='1.1 CIE Scoring by Channel '>
      <cards>
        <edge name='left'>
          <strip size='276'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card mode='compact' param='[Parameters].[Parameter 2]' type='parameter' />
            <card pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' type='color' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{3F377C81-9194-4137-A554-A02FD4AD379E}' />
    </window>
    <window class='worksheet' hidden='true' name='1.2 Overall Score: By Segment'>
      <cards>
        <edge name='left'>
          <strip size='248'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='1' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' type='color' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
      </viewpoint>
      <simple-id uuid='{1A09BA7F-9334-47A7-8CD6-2C70EA57BD45}' />
    </window>
    <window class='worksheet' hidden='true' name='1.3 Component Prevalence'>
      <cards>
        <edge name='left'>
          <strip size='300'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_RUN_DATE:qk]' show-domain='false' show-null-ctrls='false' type='filter' />
            <card mode='compact' param='[Parameters].[Parameter 1]' type='parameter' />
            <card pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' type='color' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{1CD9C1EA-DA31-49C7-BD6F-D64622A24964}' />
    </window>
    <window class='worksheet' hidden='true' name='1.3 Icon'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{A4F49B0C-61D3-47EC-929F-11A7F3AB9659}' />
    </window>
    <window class='worksheet' hidden='true' name='1.4 Decisive Components'>
      <cards>
        <edge name='left'>
          <strip size='300'>
            <card size='31' type='pages' />
            <card size='238' type='filters' />
            <card size='155' type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card mode='compact' param='[Parameters].[Parameter 1]' type='parameter' />
            <card pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.07t476o0t47bw710xsao01qal750].[none:DCO_REASON_TYPE_NAME:nk]' type='color' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{9CDCE78D-DA53-42BE-B5DB-78EA9820A67C}' />
    </window>
    <window class='worksheet' hidden='true' name='1.4 Icon'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{A9DB94A4-E19F-4A19-B6EF-19C03709F120}' />
    </window>
  </windows>
  <external>
    <shapes>
      <shape name='Codex/Icon.png'>
        iVBORw0KGgoAAAANSUhEUgAAAScAAAEnCAYAAADrWoVBAAAA4WlDQ1BzUkdCAAAYlWNgYDzN
        AARMDgwMuXklRUHuTgoRkVEKDEggMbm4gAE3YGRg+HYNRDIwXNYNLGHlx6MWG+AsAloIpD8A
        sUg6mM3IAmInQdgSIHZ5SUEJkK0DYicXFIHYQBcz8BSFBDkD2T5AtkI6EjsJiZ2SWpwMZOcA
        2fEIv+XPZ2Cw+MLAwDwRIZY0jYFhezsDg8QdhJjKQgYG/lYGhm2XEWKf/cH+ZRQ7VJJaUQIS
        8dN3ZChILEoESzODAjQtjYHh03IGBt5IBgbhCwwMXNEQd4ABazEwoEkMJ0IAAHLYNoSjH0ez
        AAAACXBIWXMAABJ0AAASdAHeZh94AAAgAElEQVR4nO1961rjyLJs6uoLdM+stc/7v+A5a8+a
        6Qbbup4flVEVVSpjAzYYnPF9GsmCaTCWQplZkZHFPM+zGAwGw42h/OxfwGAwGHIwcjIYDDcJ
        IyeDwXCTMHIyGAw3CSMng8FwkzByMhgMN4n6s3+Bu0Wq4BgHkeEg0u1Fet2nr/v9ct/vRQ47
        PT64beiWx37fi0xD2I/D8tw0iUyj2+Yp7Efdz1P+feRUKUVx5Lh0W1W5fZnsq0qkrEXqRqSq
        wzGfqxqRZiVSt8f37VqkWbt9uwnHi/3K7etVOMa+rI+/J8PVYJGTwWC4SRg5GQyGm4SRk8Fg
        uElYzekaSGsveM37Qvc4h9oP79MNdaCR9uOoNaLMhn8/t0ny828VuZoWvw+8z9w+3cZRpMLf
        Tv+mJfaV+7sXVTj2e/25ReG2WULdKd0DVpd6N4ycLgFPBFosniYRwY2CbQ5f4z2OURDvT2xD
        UvBG4RxF76FzN9TiWPfjEArhOMbNCvLD7yZzTHIilyOzeXZx+zyHn1OKyKw39SQiomQwiEjF
        5JqSMAr4g8jYu61uXEF8OIg0e5F+FYrdvOXORUX1lRbiS/19Snec2+NYivhcoa8Nr4KR02tw
        bGUKN0d0g9M2Dvk9E8M4JATDq2zd8mv+e9JzenNij1U4f25Y/l7TKDLT7zMRoQpHWxeOsvBv
        FvRzpjG8FnERJr5WEhGVg8hUh9977EWGRqTu3Cpe1QSCqluRpg3Hua1d0fcmx1WtK4a6glhW
        4bU/nxynG+PYCqYhgpHTucilQthPoz6xh7AHIfA5Jo0hQxwvkU7fhagg/X/4HEdE2eOUOEku
        4ImKohOOAOUISb0UTeFrRZFPdyFLAAmVJaW8+qOKyRFTpb8H5Ab4favavbeqFxlUdgBCAUmB
        sJo2f46JakFkRHZeysD/Rr3c+6/N+uaK+O/gU8TZCOoIjJxegzRy8iQ1BYLKRT4p2UTn6Xjs
        AwlxOobzTDIgoolep9HQOIT6FF4jlYxIiVPMkSKmKSajS9encGOCpFDPic4rYeH3K0b3uqyU
        kDhyyUQvTBhMWqluCiTEZOSJjEgrjaqORWJCpFxWIjOldSAjI6YXYeR0DhbREm1IRcYhkE0q
        oOwTISXOpd+XRl1RhJSQz5xJzbhmlIoocS5X/8q+nuOU6tIpXfjjanSkL8cxRBVI7yat2UxK
        TCPVdMYqkBVEnCWdS4kqOqev6yYfHXmSQi1qHYSaLYk4UZtq1yLNEOp0Iu6N4XdjIkJRnSNL
        QwQjp9eAC7K8YjSOgVRAQt0u3g58/Lz8Oqu3ecVuouNcpJMqt9OCO6dlUZo2L485zZpnkQk3
        WI6c3/C3y53z6Q2vemFFjCIqENRYUIGZSOqlgjWOPYHx+YrSMIquQFJV7chntXUKc95WW5EV
        vfafjb6/onSrf+Po9p509ff079eQg5HTq5AUajn9QVHWR0XPjoQOzyKHJ7ffP4XXezrXPTtC
        S2tEXCz3KVlCJEwyXLxOi9npOZGYaNKi97El/Kv8WRGl4Wdk0h4QVJGQWJFs6TkhIgMpRN9X
        xgVvTvsqJqcHR0Zr3a8edOWUIl48DEr9mVUtMrfilh7TCIkiK0MWRk7nYqGxYT3NKDIMYVUN
        kdL+SWT/22273+EYr3e/HFE9/3KElltF42M5RRiZr7/0PS+9zxzS++o98PdkjgzxPcWJn3Ws
        b694xfcUx9M+vG7XIuvHsG0eRTaHUBP0ERMISCOmqgl1v3LS1HSKoyarOx2FkZPIMpJIX3Px
        eLG6NTky8kREhHTs+PAUyOrw5DYmp9yKWkpOXx3nRGEf8naLUEwfj8gA5nH5i+HBhM9mGNzm
        xbEU8Q69q01V9G/65mY6RjQnEkd3/vV9wcjpmDiS6zq5+g9LATpN49JUjdO4Q5Lmoc7kJQJD
        XD+KxI/fiJRuEkQ2vOwv4s4PvUjZiZR7JRNaVYReDaux/T7UFfdPIuvfLg1s1nEdq85ID4qk
        VhbV08q7IygjJ07N0pUv/+Q7JnjUrdvHhe7sPimSY9UOqm22JknV2Ybrg1dh58mViURcUX6k
        eiKU3nioQfIB9T4+3/bJkdIOxfNNXnLAmqqcsLOsRKR2dSwjp3uDXmSpWJKX9FkCwH5KkRSA
        fJWi70m8liLfpUPQJy30Rix6NFwd8+yICG00Io6gSgnXRFmKdCjW8zXThc+23biHUdYzah1L
        EOAZhWNIGSaKqETutv3FyIl1SseipFwkdNiFNC0in8OyR244LBXgLLz0ERtFTCCmQmhJ33AV
        zLrClhKUb68p3GeF7wUx1RpNNe0ZfXvrID2ALMHv9QFVtyKjRlM+pVdiusOHlJGT72QnhXca
        5ex+0/K/7vlcvz+e8nkSyii7uZbl20YSHZXVmz4GvhUJuirRPRZHJCamoQvRTVcfV4ojbWvW
        KkN4cPvNY3g4zZrOt+uw8OFV8tXdpvdGTr4gPsb1A6Rp3U6X/n8d37p9pr8tbcBN+tci9TYp
        t/E7sQL9Di/MD4WXAOC1OMLg/jcmpsWKXnmk144U5+1aZPNDZPvTXVssQ0CENBMpFYXIAElD
        c5fXgJFTVHMirZInpidHQM//iDz/LfL0t8iOjp//duSUcxnwRfYhExVlBJQiS/3SHV6UnwL+
        e7MGahYRmcI1wkJOvy/CKtzCsYC0Ulx/RK3RNz5z6galeyUyNWGB5M5g5CRCq3UobmJlTZf9
        d7+UjP7rtt9/xXtolNKGWo6MDDeKnLA1822nPsK0ty8d3tCuNWI6xGk8nBiqSlUMWmOqa+ew
        UPP33he+NzmxTIBXwTxxTO6C4VpS1GqC+pJGSrtfVGM6xIVsX7ua481wX+Ba4TS5QjvEmoO6
        Sxx2If1DlDZNyWrwztWn/PHOFc+99qla9giW30sL9b3IKSUDOEzmJALoX/Pq7kQ4yeSEmpNX
        ej8HjVJUP+KGXCOou0KqkxJZaqUg2K2aEF2hcXwY3NfWWutcPzhCAkmtdm51j+1eUh8pqZeS
        gy9MVt+DnF7qI5tYHpCZ43aKnHKK724Xa5Qi3yOSABgv3R/QEM7unkJlg+HgVvdAIpHdjkZN
        7S5ETKyX63ZLP6lmJTK3IR2ciYy+uCXL1yenXAHZLwvTCtyBlNnRcabNJH3d7WJBJV5z20lO
        o2TsdGdgbRpppcrSrf4NXYiYZE5WiJN0rtPoCSkdHBGatdNKNWstK+iDsShdRMWOm8AXbTD+
        +uQkkicmXvrtDoGIfKqW1peek344Iqc02vJtJ6RRydmWGO4PLEuYpuDiidXc4aDfN8URU8Me
        XxQprR9CmufrUAf3eqKVvkpX9eZyGTF9QWIS+S7kJBKeWDj2KdYoMvXBYwn1o+dfwbKEUzcQ
        k/dZ2i0dKSOXSvJZ8ukcRU5GUveB3OfMuqVJtUzTHGqhdefICnooHz1x2xOleb1G69MgAuV6
        pU6e0yqWHBRaV5jlSxKTyHchp3SFDBtWSLpD8Fd6VlkAdErnkBP7cOesTNg9chZZaJYM94Ho
        4ZRopYpZZBDxxoRlJ9Kzd1TlVuO6nSMjLiG0VHuCP5RISOUqVaDnHDcZX4ykvjY5HUufcH6i
        0LnbB0Hl098iT385ndL+d0xISO9wrtstI6HccfrzDfeJhdo8KTNMg0RunXzcbrS+RNbOq63b
        up1byUOUDuV4vQoWwXw/5Ijoi6V3X5uceOmefbRx3B/I7E3TuP0vp1uCdmn/m5p5iZDY59tg
        OIkzHlCnnlmRHg9ROToPVFMHbRMshDGIAT180xALQHPHXwRfg5yOSQV4LNLQx8eT6kag4vab
        iil3iJiSlgI/3eQ+my0NnwhexEHEX7JDZuEICK0ysPvFQ3ocYuuVxQy9WhareLnjG8Ftk9Oi
        EVbi0BXTTo6N8e52of/t+e/QH/f8TyAoaJb6g6tNeYK6z34mw2eCOhrGXqTXwQwiWqaY1eOJ
        G5J5JbBzKV5uvHqzEplXsX+5UF0MuCGSum1yEjlS49HzXE/KpWOoMaFxl/eoL/kO8c4iJ8Pn
        Yp5DOjf0obFYJDwsOZKKIi19UKNGBb8o/6AtxA9dEMkXzW+ImES+AjmJyNFCNJ4W3S4MDUiH
        B7w09aRHG0oiD4jGMBkMHwQmm6IQGSAHUL8xb+MibvVvpogJFsGYDrN6CNcxpg7XTXgtEhKD
        wv/npnDb5BRphWaJxI7zrGndnlpQYG2CgvevIKzkFhRWgHOfXTSS6ZtNOzF8Aei1XYwiY5K2
        4fqUJGJihTkPaN32QeKCAvqgkgMeSlqUQepwY5qo2yYnkSVB8cjsNHJ6/ie2Ndn9Wk7X3Sev
        mZDSVT+LnAwfDW+3M4tURExlpR0Jc1xjAjFBCrPd5yOmdh3Oizp9wv7X9wPeDjGJ3Dw5sagy
        6fqfppBnH55DPen3XyK//1fk139cMTwdTJAej0NMfIv+OIPhg4Am4XHWAZxjPK24rJJGYb2W
        m1UYmgBvMZEwdbhuQw3K/5sifoCDaK9fMd8USd0uOaXyARZWgpyGIbhW7ily+vUfkb//r1uZ
        Qx+c74vr4tcIfXM/12D4SKRN60URm9wVZXBrbVeOiOBOAKeCoYsjJgxWgI+978eTMNRB5lss
        Od0wOU2qpmX9UurJ9Pt/g46JV+H2mBO3D6twfRfqSxOvyE2nfxeD4UNwhpATwsyhF+EBoCA2
        1F8bdS6oWrdCV+gq3yxL7RProcr6ZoSat0NO6QcRdW2rDmnsYh3T779cG8qz+npjhQ59STCR
        R7Mkz4ezYrfhK8K7HZC+aSRJAIZzoMm9aZV0ShcpyUyjq5KRViIiTSky34Zh3eeT0zH190w1
        JS5go+fo8Bz7ej//E/rksGLBI5lSYrL0zfDVwHIaXtUrCm0qnkOZo36KHTdFQilktaG5eSie
        ixbIk0kvn2hY9/nkBKSmcdMUFOBY9vez4n6Tt/c/NBHln9Arh4I3jxaPJusaQRm+IFB3lTEm
        C0RSVe3uF0yA8SJOmssIHRSPpsIEmXolUhIZfWKB/HPJaWEOR8dQycLbZveb2k7+oRoT+3sr
        aSFygo1uOhHFIifDVwVWrucirMpBdoBm92oX9975bgq1D4IOCsVxIWLCw/sYMX0gUd1G5MQr
        Biy4nAZXZ/IOlr9cjQmpXOpqyR7ffgABre6xDMFqToYvCZbWiDuG22Y5iiOaigYdzLF/eb93
        9xUWgiA3aFbhYX4jhnW3QU5AKhkYqOaEyOnpv0HH5PvpknoUyKnvlhqmeTZyMnxdgJhEdIjC
        JDLRgM9ZNGIq4gxkoNot66DqRnVSm9iTHEhJ6QPTvM8jp9S1kttS0rTukCjA//mPyK//F0io
        29MqHgktxz78LHewrG0ZDF8JUHRDB5UONPDkQakcHvDt2mUXIsFFs16J1Gv1Ju/itM63uRDu
        Jq1Lld+890uiujKHhl3UnJ7+G3RM6dbDYaD/1LdnMFwWyYM193yNHsTaMIxCeH8QafZBQoAV
        OzhtDloKqZpgajdjaGcpKie/g8iJmxmxzXRu/5vElaRh8gZxNAHFCzVHiXrvDIa7xOzuhXIM
        zcIM7/yqlkKse6pqJag6DPCskk3K3A+9OD6RnBKRZRr97H7H6m/WMMEczkvyk+ZdhL4Gw92B
        yiTjGGpRI90QPEy2WQdiKnWFb+xDOwxvIss074r4xJqTrsZF8+FpYOXu19K1EjIBbkthkSWv
        xFnkZLhbQKSpAQCfF4nLJRwxQbAJu99m7farZGXvg/C55AQvGrA4D7bc/dKo6W8SVz7H7pUg
        pdT2xIjJcM9gH6jo3BxU5LAZqhvSROkK39iHCcMz/o3CfR//m1fG55HTqKTi21OeRQ408HL/
        K/h/e7Hlk0ZXKhPwo8ApajL1t+GewVKc3Llx1KL4Lm4KLkgXhSK6L5GQWV37cY3ynxs5+VWE
        fSjOPdEwAjhb7n/FBXFICDhSivbmNGC4Y+D6H+dALhPqT6V7wMOJADWkAv8fd1IIiTRbkWkd
        k96V8cnkxH5Mz2Ea7++/QirH3t+p+tuncIleyiInw73CX/dwvJxd7YnHS/X7sPIWtbiMrjGY
        JwpXOlHYizSpF+/K+Hxy8jUnHXzpyenv0JYSjQgnOxQzijMYluCH84JECvdwR/E79SkfOvGD
        FDBVuFmL9NuQ6n0QrkdOUfSSUYP7lbldWDlAnxyiJW/YTkMv0UnNPUAGg0GR3BOLB3ZB2sJO
        ZKxFenIvwPipZu2ipZbsVWDg2B+CRIHHqossx029A9chJ7bTzSrAx7A6t6fIaDESnHrkYBgH
        VwGDwfBGzOEBP/Tk+aSkUtbh3gRJ7Z8CUe2ftLm4Ckpy7P3x+wnqeuTEoeLCT2nQdI227nlJ
        Vj2JLaEC94ZxBoPhTYjm4/UxMcns6kzds0i3cdvhyRnUHZ5EDhunfap1la+sgkaqqnVWwmWi
        pyumdVNQbw9Ix+AJ3lMKd2x7Tibx2rBLg+EymEPAwAVxSA2KUsear8OG19hDuFnpoE6IM4tC
        Fja/b8SVyIkiJwgt061LoqR9EkEdngOZ+b0NuzQY3g2ox3F/isTRVFm6+49rTnuKntq1uyfr
        Ni6zFKXIXF2sdey6NSe2BuXBBP2eVuIypNQ9i/S7fFroW1QMBsObwESEBapSyy1VLVKVlMqh
        hWVDvXhrFXOO8aogpAcXymyuW3PCqkDfkRJ8H7er8CpdWhRPHSxht2uRk8HwDsxBr4QsB4Xs
        QYvZ7WZZEOc0LzJsLII/1AXLLterOU0UNk5kEdprq0qXW6Gj893uiLDSBJYGw7uAycIIIiAF
        YGkAp3ReTrAJRfKy0K0UGSuRqXb6qAu6zF6/5jSqqTocLZHO7X4Hp4FjUgLDO5FJ/m9k1PRF
        cNZDyh5kMfCgf+FbioJcC7govlbnzJX7/2EJXKhYsxpE6vHGI6fUqwlpnHey1B46TOj1LSm9
        FbzPRqHcQ2Tjn3xl2Et67ruRE0fUuXOZliaLwF9G5D3eBQeDZ/J88i4gs/ihClUjMq4uVhN+
        HzkdG4iJiAnFb7bZff4ntkE5PLmICpKB6XLM+z1BitwifV0EQR0EcmUZn/tu5HSs8Ts9ntLz
        lNoYYqDPDn5ruIerxo0qx9goTH7BKPNmRcaPRFBFkT8+gbeT07F5cyIJ6+4D88JA7um/wQYF
        /kyInIycXkBCRD4SokipImFcJI6r9MI6pUH5QuTlSYfsmafc8RiWzmedViKjEwtalL7EInLa
        0/TgZAIwtE61Ngf79rIpfA8ahV85pPOykZMnJyqG9wcnqERahwkq3NTrp/Nae8pZKLQYyelb
        Ubjcv9aLpWqCkZh/XX9DchrjbaQuhGmKLZxHGkSJbn271DKASFPv39S9YKL5eHUbRkvBY21M
        AozFZJjz8DZyyuXv2Jh1YYfSPceR0++/lvPmLHI6jUKSaInICekbnmJ+S16f9ID+SuQ0hfQD
        3QjetrnWvdrOsoc2Fmy+0nv9SPA9XGqT7yziSWvogqapblUHtdXpLb2LVP0qIP7N5GecQVLv
        j5yifD8hJ28ktws1pydN69I5c1ZzOgNpSodGS9SUKMT2EzVW4bhp3fee/BlfBN52B17yvNeG
        1gF9Y0UomEPX85Xe60fCR6S6qIU5eBx0cCrXbkXWexc5YVGLZ99xdP8KvHO1DhGTSHaUOFbr
        DpnICT12UJDjtVmhvAxfc6IO8LKkJkxESquw/It9u/5+5IS+zbGj41pkqETKnm6Kgh6kWnb4
        TosDFwUR0SzhIVArMdVaJmhWwWscQ0fS0gxbsbgTZ/8WlyuI86oI/MG5IJ7WnHwdgNpT5tEi
        p3OAmhNEcNgqrTnVjUi7UkJSLx6I6L4TOU0T9Ws2IlXntqGK1c4i4h+auMaK8ku91Q+F7/Do
        Q/24pPoT/J5WW5H1o1obJUNHpsl9BhOiVPmggjg/gdBSgpWQcdRU7cQ20f/jLxrrm3sRBZMR
        VuFo2GHdijQaZrf6RMNE15WeOzna5wvdsdMUptT2mX3ViZR7F1X6tA6DMDLDJg0Bk9bkSlEv
        8ikEFGV53j1esYSlFBHdT+K+dgJvJCfyakon9WJarx/1RJ7fqSdTZEZHKaLhCKiHCStwflUu
        CbPXD+6ptnoIr1fflJz6ZBsOIn3raiB+HhtWjHDtniOruHewiFWDhknPeyukzv29WTK0fyJL
        Ff37l7XIjAfptSKnyHFAi49wqsQv61tSdhIZxi3cLK137lXwK3NVEL5hFa5pXagNUlo/imwe
        49frx+9HTv1BZNgvF1iw1TphBDVRPEjLzmpOxzDPIgXdjzgukDFJXDDHYFwMKtn9ptonHqCk
        eypKkfJ0evfOyKmPyQfHaeTUUSV/IB3EQt1rpPQikNZVFaVxtBrXbkKUtHkU2fwI21pff+DE
        1qtjmpSM1sHbul+Fm6VWI/9Zax2j1lEqMvg3HAfIaZoCuWMowgC3kU57Z2kOwOp3UIyPeo0K
        CTFP1j0d3l5zQgrnSQnm53vHnB0R09jFxbKIjGjv/yCGLKLIqQkXQLt2tabVVtM4ENNPt9/+
        DCRVNZ/9Li6HaQxE1GCkvZJTs3LnyzKOmIZDrHY25OEL17O7NQvdY+TUTJkTp3XQNNaN+/oq
        jZiqkCKewPsjJ2iZOg3pDs8hcvLTVfZxWgeVLgs4reZ0BlAQR+6ukoF6HfQmK07rlJh4O0lO
        X+iGnUZ3bbX7cHPUK70em/BepyFE+XVL5GSR08sAMYl40eosjmgg/cHDAf138H3yo8v1nsYi
        ztSEwOTiaR0PL/BCy0QJDmfLbhcKlpHIcqJ/C4Rkad1J5GpOTRtkA+uNyHob0rqtktPDHyJb
        3epvSE7dTqTZaXq7E+lIEV8I1UY0sqpri5zOwTwrMUkgE8gBUHMaVWPWU+S0XwVHTD//Thdx
        +P4/gbdFTiiIRwWxHUVNv+OiOJZ7ebWO/wC5Y8MS/oNOxJawUeW0bv2DIqc/RB7+FNn+eQY5
        fSFMo0vnoISvd8vWnXnWusjeEZePnM7pM7xjRPciCShxyKt1vqyjHIC/PRMTFm/G4Wy50Bur
        o5kWFb+MmJjILdpTEnIyvAJHIidYqa7SYvjPmJwe/nQXyHfBNOp1pwsCB121ZIJC0RzXZ5TW
        WeSUx5GAgflqHOK07qApddPG8o2SFm/qFUVOpwORt0sJUn9wXkb0K3VMTDZzzmD4NkBqxyTV
        7x0HlHXY6tatoA4UmFwtckJxjAvi3KKCtC4nI7C+OYPhm2BelnYOO6fGR98nR0zt4dUc8L6a
        U5rWdRo5sU/TcKB0zpwHDYZvARZi953TlHVQg6sWr9Hm82EbmoLH83ngfTUnFmFy5MQ6J0vr
        DIZviNmJqYfe9TB2e3KAUP1BrcJgSImGZLX+BE6TU8pwkeuARk6HXWyLsvsVmv8ijZOldQbD
        t4C3Qta0jhcY0DS82ogctsmiGITYGelQskDxdhEmnAd94x8R1OEpbsK0yMlg+F7ggnjZq6kf
        nS8Kt3IcBSjc9H+ttI5FmP1BFp5N+98hqkLR3OubLHIyGL4+SE7khZm0UCYicnik2nMXnEqv
        VhBnK17vZHmQqK9m/ySRnzOiLIucDIbvgWkSKTRC6oU6RwaRQWklZ5l0USnBOCxf+74aciLA
        UiJc8aKJGNTwa5HTOzGHdoLItx1Prin+2+fOvYiMMDEVK15CvLgI6zPXxanQP528cnTjMVHW
        x3kZUBubiMgwh7930bsVOxBTapsEe6X0Okq6F06T03BIXsOvuROZyAd8UtO5OXNhmB3KhTCH
        JxQ7E46Dfhb04Uf+7F14ckUolscF7wvVtcGkPv1/LvWedO93qUMFfQ9/3ctZyIc+er9d+Jsg
        csdkECOoyyB6SMLSV+j+H45cjwfndMB4NTl1+/j10JN3DpnKRy6XsO6dQrRkBPV++IgJ0RAp
        9flzeGnzSMhmQUpFfCylktQV3lPU9E3Hqa1O+hpjil7c+lDrmCiCn88ryhpeQOQqMolzUync
        Hg/N6PojY7r+sJQUrB+jl6fJqU8iJ3g4RU8nri1xxEQ2vEZQFwDfqFPydKIOcY5uo8kknfho
        YTHSHPtk7BS0K6XI5ccpJcSTPc54f6XOGPz+/E1AG5scgtTtOnwf2CVzmtz1ISIyinO5ZBtv
        b1lDnJEjpwSvj5xGnZ/eHZZPJu8pTsTEFxX0WYa3g2/MOSUmDp+T4yhyUjJaEBQRU6kXXini
        oiZcjJd8LyJRRDTPIjLFBMRTfRbnMg/KxYOTyg5R5GQPynfDu2PSZyei9U96cA4ZYur2J+uf
        r685gZzGzAUQEdSciZjsYngX0pt2HIMgNg2h2dN9pK2YJZ+20bmykkBEGjX5lE7//8u8Idoz
        MTEh8RCMKT4XvedM9OQ3XJdJVG94H0Du0xSuK3iDD8lDcqK0btBNrh058ZOZ59KnRnKW1l0I
        M924R4qOpyKnIkNOfrQ5wttKSUnEe3Bf47Ob5ziC4ggpql2Oy/04hIckyPfFmhOXG+xafBfw
        t8NzCrXDScnJL9RkIqfuUpFTrubEugUuhvsnOdvw+t/cLoZ3g27gqI0IKt1jK1d0LHNMRNGx
        vvYfE5wPJ/FMdfHAiSLBNDqaxoSEOZ3Fe++S992FJzO2aMVOV5XFZC3vBi9cpAsrpfLAkET0
        LD+6Ws0JDOjnoycFccPlwat1WBJH5LSInnI3bef+nYiUUPCm1yJJHQqRxjXeEA7TIvgU3idr
        5SL9XEY6cLLmZAXxi2IRgOjLxWod1ZsuVnPqM+SEfrkpeRrhgrIn0pXAUYbenGUpMqDAXYrU
        z+REWIb/Z9IUqG4lmsJalrRXu4u6XW4i+lQ8b6zPee8FqzpITdM9CDfd9yFq8lM/aJhGBzHw
        TmT3pIM3dlSKYFGw4W04dY+jhkgLN2lkO7+XnHKRU7RSl+TyhuuBazHjEIy9vLdzQd7YWifC
        06uHlapa1EZbSce18yRv12r/u3Y/syjcz6su9OCZJX4vEelkBKQ9X9jJU9iPJcNQzb1G9TQR
        pHt2ZAV/McxONFwHXDTFkdIAACAASURBVEOMFmyo5nRihuLrI6dpDBeID5lZdWu4HrjmhKcR
        FYCw0sbWFf6CUAvVunVGYBgvBVLCcdW4QQn+c8UqjE7PuNhnTEQ7HUnPeIIvxg+lZDSgtJDs
        felht3RlTacAGa4Drh36GXe6cNHvRab3klMaOWGQYZ9GTukKneHiADEVo8hYikgfP6GmSaPp
        WbwRGFxK2ydn/FU3gWgwy563uhHvWogGzbJy5yed3HqR9yKhqI+iadqriWZypGm+uVz3/T5e
        ABjpGOJMJrZOnVl9A6pdq1cDZCdMTqlK/CqRE3RO6RKt1ZquDPqwoWj1NSjqHZt6kfEgMuzD
        ZJJmFSaTVJhf3xBJNTS+hyOmyg3vbFYUSV3qvcwhAhwR9exDlAO75/1T8Anb077bkbauP3LM
        KWFHkdNgkdM1kSr5J4qcEP2+O3LKkZPXlpC2CUvANm3nevB1GpygFa2iCosT8Nmq9wkJEQFh
        thjICud4rj0ipmYlMm7k4n5c0XDWPh4zBD/6/e/gEZbuD8+ZFTzSfk3TkYL6cGGiNeQxh8+D
        0zoUxKfhxf/7NDkddvHrcRQ5oOBIfXW4cO0Dvx58Wifi/tal+7tDDjCUTu9UUXHb15T0dU3D
        OKMZb6swA08KkUKjqaZ16eDQX/aGTp+qixmIz8G88PnvsH/+x227f9zXU41U7viYhYpFTtcF
        PgeWFGDA6WGnnQjH8YbIaYqFbVYQ/0BATT2KzIWIjEHlLbIUU3oFOAktEQnVOhkD6R7OtRuV
        E9SBmNb7UIe65Gcc1SMo3Pfk9MuR0PPfIk9/izz9Vzc93v8OfxPuQogsVlg7lXQq2PV6PRxL
        60b6nN9PTolCfJoyMgKTEnwcErX9a++vqgkje9qDyLAWGXToYdM74js8kIPhwRWsL+5imrt4
        k6Ip5A885n73S2T3t8gzyMlwu5jz0RM+36uQE4qKTFDWr2QwGBg5ax/ur6veS05oeQBQZBz0
        KWtulwaDIQd2lmB/J8g+3q0Qfymt83oRFBeNnAwGg1B9jxrUx0ROUJYv/hNvJyfWklj0ZDAY
        UqRpHQKa6lLklKZ180y1JvNkNhgMR5Cu2M1jKAlV3RUip3kmO4phWRA3GAyGY+0rZS1SdqHh
        /AW8MXLqlikd2ldMIW4wGCJnUyIor3eqRaaXyeJtkRNLCby5nAnbDAYDYaaCeKXk1HfxVJ8X
        cEbk1C9/IEdNI/XVGTEZDAaAa07j6Kx7J3WhKPoLkFPanDfPQd+Ujn8yKYHBYPBQTog84dGU
        XV0pcloowy1qMhgMBK45lURM43DBtO6lyCklJyMog8HAYDFmOgz23eQ0ZshpoKjJOzAaQRkM
        BsJMaV0xBXufcZAwzPU4Xp/WiYSUbkjU4QaDwQCkqd2o5DToQI6Lp3UiIa2DEyP76BgMBoOI
        OklTWieIngrnR3aCLt4XObGUwFwwDQYDgwekjup7P8vZ5Z+3R04sJ/AKcYPBYJDYbXSaRMpC
        eUME8zZP4Qxyyvwr7EDADGgFcYPBkCIlKiFr6RdwBjllDKEig/hZzMvJYDDkQQJtDOcoiktF
        TkfIKZp0YQVxg8GQgnhhmkRgQjCKSHmJmlNuaMFihc5IyWAwHANNvSlQJD/9f70tcuJRO+lm
        MBgMIkk2pVXwaXZapzNGwb8xckqiJkvpDAZDFrMKLnmUGYriL+OM9pUjExLSAYVGTgaDIQWy
        qWlaDoA9gbdFTvxDmZgsrTMYDMA8KwnNFLvouYuIMF+a4ptqnAwGg4EREdT5xCQSFvcMBoPh
        pmDkZDAYbhJnpHVnhGCW0hkMhmPwqZ28iisscjIYDDcJIyeDwXCTuExaZzAYDC/hDTxikZPB
        YLhJGDkZDIabhJGTwWC4SRg5GQyGm8TpgvgZDXpWNDcYDC/iHB5JYJGTwWC4SRg5GQyGm8T7
        0jqkc6/oNDYYDHcG5pBXpHcWORkMhpuEkZPBYLhJnJHWHeGvyDjqdSZSBoPhTuDTuCIcn5na
        vZ2ceCTUrP8xgjIYDEBKTBf3EK+q5TkeauBHDIsRk8FgiAFCKstwXBQXIqds5KQzqMBFRRGi
        J4PBYPDgiAnEVF6InMpM5IQxL9Mk8QwqS+sMBoOiKJKtdBEUIqkTeFvkVIibylIUIjNY0UjJ
        YDDkQFGTIHK6BDnlIicpXMAk4sQI0+zOza/vnzEYDN8VFDWVZSClqroQOWUjp9n9sFFJyW8G
        g8HASOpNSOsuQk651bpCXOQ0zy5qQkEcLGl1J4PBAHD0VJYuG8tmZDHOSOsy34KAqRRHRKXK
        CsYXpgMbDIb7QZFL6aqw5XglwRnf0eR/8FhoJjdreje9ybPFYDB8UxRFXGeqlJQq3U7gjLQu
        FzlBgDkFzcKZwiqDwXBHYBkBSApp3Qm+eFtaV6oyvJxU81SSENNgMBhEYtGl1poKkFN9AXJK
        0zqOmuZJpJpE5lFkogjKSMpgMHDrChfC60akaq4QOaGXrqwcOU1jnNoZDAYDkAovSyqIXz1y
        wg81YjIYDEC6UoeoCcXw+hKRU1oQn2cXLZVVCNe4sc9gMBhElmldlRDU+8kpEzmNo0g1ioyD
        hmlMUobPQxEfF5nzZa1bRft0gyaFo+Jrpe2sIMZh0r3u04E0LcgJ+ZKC50IQbAXRj0ORfH5M
        TpeInJpV/HqmlK4eRSYlqFf4tBjeA5AON1LS6/SmTm0q6sZ9pvXK7XmrVyLtWqTd6LlWi5d1
        IIeLvg+E/Aj7G5GqdRt+v3Yj0h9Ehs5tY+8eitOo738mux72GZPwep6CB1l0TIaJhssD1x1H
        TE0rUrfus31/zamNXyOtGweRqY7TO7GVuusisaCQcklI/vOo4mgD5+pGb3wlAFwo2DdrkdVD
        IKi6dU85NGte8uFTFCKl/s4Q6dW1SNOIjEqi7Vpk2Cgp9e7am9Wqp6peIJ8pLN7Mo9vj/+Vz
        1mp1HeRqTpVG7ZWu1p2wTXlb5OTJaYifqhY1XRc+5aGIg31yiowKN7ooKheV4OnFm3+irUVW
        G0cKnpzwELpgXdFHe2VI06omRE+1EmXTibQULU0atc/i/h9PNlN87Pdj+H99xDW4roZitofp
        tTCL+M8X12Dd6KYPwneTUxo5TZP7AQPVLmzF7oOQ6VfiGlFVhacStCS4KPi8J6UmREY1hdvt
        1kVONUVOV0nriFgruoDHRmRqRQaNnCZ9EIJw0NxZ1RoNUWTko6MxENI4uKgLbVdYbYa9tOE6
        iEzmtObE1+DFI6dp0oipi5/OJaUYhiuCbui0kF3VSUS0WkZINREXXjOZNaskcmpC5HWNtM4T
        LKI9/b2mUaQZkojJ+0KLSOm+d0JUpGSUvh57V6tC/U0kEFMxXu69GJaIPt9KPiZyGjqKnmB/
        YFKCqyPtU0I6xCsgUaF7nSl6t/mIqmpcvaduqea0VoKjmtNl31CsHsbvBIJpNGKax3gEGSLH
        pnEkhBIDoiQ+HrSHC03qiKrK0R6k1wSLL6MHKT2ArhI5gZxKippMSvAxyBWRq9rVaurG1Y7a
        tSOWdrM8rts4SkH068mtCcTUrML3cHR8mTcSP1mr2i2w1E0go3kMhe1Fn5YSKVK2dD/0jqR6
        IicUyJHu2fV6XXgPp6TmhPpmziuO8PaaU8kpHRXE7QO/Io5FTnUcOdW6BL/aLvdMSP7/o7Sq
        bvT7EXVdqebEeqaSU7tRZGpEal14meZATv5JrO+1bx0JIXUDKY29KzuMvft/sHo3jiLVIDJW
        YXXZcD1E0VMSNTWtnDKcO01O7Tp+PU0iw8H94336VL102G+IUEiy+oGnkdaOECGttm5bP4Tj
        1dYVuvHESh8ufmWv0ZrTZrlaV1z4hvZES7/DPCkhSbzMXyTpHzQzgxLT0IuM3fI1Sg4+ahrc
        14rTlh2G96KINU4cNbXrC5BTk5DTOLonc4XaRRNHTobrIU2DvEYJgsU1kdLDcr9Scsqqwqkm
        wPUm/xlfoSAuHPZXInMdCKngrYhT2EYv8r5LCKkLxIRj3ABeVqBRlZUhrot0pc7rm0hPd/nI
        aRQZ9iJdoh5mlbjhSkhyeB8ik7J7tQ2EtH4UWT2G4/WDNly+JNSsRVoupqc6pwu+F5YSzLVI
        JUEf432g0/RVJQZ15zRQw4mtKGNt3tBdSfFuWADZFDf7NnS9vp+cNvHraRQ57ELIj/TAX2Tv
        eTeGF1EUlAIhncsQ0wpk9MPtN4/6+lHJqQwXDuul8BqfbU1p3bUU4vjZ3EpSFrSVQTk+NEow
        fagveSLSFpc+JaciREx95+pUPtq3B+l1QWkdy1caXaQ5YdX7+rRuGnUVh5ak8dS1AuN1geJx
        9CRaxbWmNUVNm0eRzQ+3rfUYDZfedD7pxSvLpQbq2gXxonJBEqIlNsMvBxVlDiJ1IhNAJDQc
        lJTSfScis8ig39ftkxqaXa9Xw7G0jtuk3k1OubQu1xSKD9s+8CuCa05o82hJKvDgit6rR4qc
        lJw2Px1ZVU2yssr9eUUIw327C7sYXPIBpKkbtC4gq7Jwrqqz1pimOlZ/+21SclIiag6uQbjp
        3L7WSGpW6Uu/F2l3IodrKd4NEY62r1Bad/HIaRzitgYriH8ccrqRZuVqRO3GrbKtt7o9iGwe
        4uhpq5FT6iDBliipqhetMT7SuvD7kdJFTbP+7LkUKblpl/vmyBEDhDUoKfW6PN0f9O/SONKa
        BkdMh+dE8W6R09WRNv0icqraK0VO4+BuhjqNnExKcHWkYXKdRE6rrYueVg8aPVHUtP3p9uxs
        yjdnlqiSqEouLSUoxau+59lFS972RGJbk9x+mjRaOjgCQsRUK2FVB3e9HnZJr6CR09XBGjYu
        Q9StPkyvETmVFDmVUInXEpmTnbyArWr+NlDkVHPNCTKCTRw1pbWnbUJOn4mIDN+IaVJSQt/g
        Pu4frBtXCD88iexT3ZY9TN+HU58bSgaoHXLf5yq0Rb2At0VO+JDbpPYUyQqERHRz8tpgMHwL
        +IdL8rDJ2aT4Yrg+TN9NTmlvXVnRUjM9obDcjG2eY3KajaAMhm+FKOUv4nNsHsg80RBBXS1y
        aqkojh/MntSoCXjL1PD7G0EZDF8cESnRMZT3bHLIbStcc3o3OdVJ5FRUoeLujcioiRTkNE0i
        VaEEpaZeRkoGw/dCzre+KOLG8hKuGZeuOR1L67ytK0VPyDGHTkQKt+RbinLT5JaLrRh+AVAY
        Hb0+tRkMl0TSgsT20WzDg7p06ld/8bRu6GPjsvpIQbwoRGA0WIp6NutNYvxkMHxtFGkqhz46
        6tVka57Ip/5aBfGifKEgzmmdiJSz289GTAbDtwMTVNqnyeSUWkNfrSDu07pEJV6h252kBMA8
        qdDulW/eYDDcNtLWp8jloloSU8u20e2L//RpciqTbynnQEbRaKFVsIQdqEGTo6V5Fl8cN7wN
        +BtyW8c8isz4m/ciUx/mvLE75NB/9m9/WUyTOhOc2nhQAln/2gLNO5A29dZxGgfnAXi/gSfK
        Jl5EewGnyWnxO0GWTrYdvn1CFckiwc4CFqoDWhQm46c3g9o2xlG77dUKpOtc20Z1EGn2tCKC
        h4em3tONKMQvAbSvoHVlcUyv02nB3pvc8CawtbJfFKMaNPzD2mSSzyu86F9PTiJJp3Ebxgmt
        t65VQmbtbapEem0RYMMvw9vgyX0MkSl8i/pDsATpeCxUYn/ynf7+IKfhBXLqdu7rcMiciJys
        zvB2IEDhkWK8eYtokFMbdJBnrhy/jZywVOi74pPIaZ7d1ztqY/E3lPUzvQscOU2pp1GrN+cq
        9Jx1ycy66s7I6VjkZKPI3wmowDVAQUlnpRbPmx8hcmrWiVHApSKn9B+JfKyb8It5szMlp0Jd
        Mf0ont5NvbBO8LcDkdM8hvrS0DmHyP7gPo++CRbKkVgWjpbfjJyGTFo3cOQEcjpo7c0ip4uA
        LXgbyp4azaBWD2FydDov8UzvtzfWnChy4prT+sFdDKECrmkIJl4cxMSA74CfIAIvo0Gk0poT
        q3ArWIfsSSDbuD6n70ZOIJ5FxETk1HUifR/IadS/oXHTO1Ak1j0ryp4enHcYUrsmU3M6A29P
        6xA58dSP/iCyVWtUPysMpvKHV/1ihhxmqt0l0RMulF7JqG9EOlLtV7VIrZ/Zd0FETke2bh9q
        TlYQvxy8dc+R7Gn9GCZHY5grIqczjSlfT06zxAXxRlmzUYIaujCnHit2/eHVxTBDBrOQCySI
        iRwry0oJqYkvBhbJfrfIKUdM0TlK6zw5DZbWvRvU3OsL4mua/vMYhrny4sxVC+KRux2x5rAN
        KyHwd4bJfL1/dUhnyIFrTrryWQ5hykhRhidZtw8COBBW9Q0L4qfIqduFgjiv1llB/H0ojqR1
        6wea9pNETpEX/Wm8La1j1qwpchoHFzWlS9xcEPMRlIIvkOhisQtngZl0TiCnoo9bCFILWtZE
        9YeT1qhfCtN0ejTU4Um3nRbLu6QoblgiY9+cHh+bm9hS5ISCeLuOI6czJ0e/v+aEyGkaJThe
        ToGY2p3IYZX55ST03M2zeT2dBdI5jQPZVOiXCwkf/EyLEWPvbt5u973IaZ7k5Djybh/Iqdvr
        lOBeycmutReBFbVZAjH5faJzjEaTUeQEaUGzCmWGq0VOLCWoG5GxFWkGagkQd+N0e70wnvNL
        iSKxWb0R1Gmg5hQRlP7RFs6jWNEjgWb7vOx7/MrwCy5YheO9HndKypze+cjJrrOjiFwHMm6X
        HDkhc0LkBN/6dpNICeo4ODmB9ynEkW9ORExlqRfFzhFTqnNAWucnvOqN5K2e7II5DlqtQ8Q0
        0LgkFHl9xKTz2rpdUO5+K3KaQ4EbhINhm5OWGDCzztejOHKytO5lJK4DPD4srTmng12jlTq+
        /8+fb/lGcirCKhAiJrbmnEZ3Q7RPyS/HzIkGViHHAkrzDEt46+NCpBjDOd/SwsMmVf/EE0mq
        9nstSPjOA9qwUjwN+regNA91KIucToMHF+TGhaHZ1xfDk5W6zY/EWC5T1jmB9/fWeWKic9Pg
        VKKHrch+HU9pqWsagFAEMRxuOrtgXgDrxySQVTk5shpHkUrV+H0Xhk5w1/h3GofkiZkHb47x
        OY6mWEpgkdNp8Ow5+DXBXM6PJiNP8NWGRpI9xD2dHJxcteZUYly0To/1PTaDyLhyH/zqd+ir
        adZxWleUemGg4Ebs7Ce/GkktMIuI6OTbYhKZ9Sk2UXF8TPx0Uo+d79Q+5OtsZIHixZU8NXik
        keZMYnaNZZFOgE7Tu7R9jQviq22YMB0ZUCYPyKu7EiDMmyetP01BSgDpOteceEKwFME6pRQd
        Nf2NbpyrADcgVui0VhfNDEvD8My574JoAUD30TG+hxZdFtODDceRGMn5h14pfqoKp3XcvrJ6
        iC172cL3qq4EnHsiFUORu5zceGhETO06FMz4ta8RaIjtMbveJ8MLoBvQ7i/DNVCWcamGrXfL
        KtzH6f3NG9wHfJ2K0sIz8HbRi9cn4aldav2jXFopeOXoD7eC1+1id8ahEynJqXEa7aYzGD4L
        vrmfm8apJapu3Gj7zY94Va5J6kpMSG8YP/9ORZ4XJimZEFGVWGokWfv6UWS7W7YclJXIoGw6
        jXJu2GcwGK6BpAMEfXNsJrf9g4SWW/F2vH7RBVmVUOnhdff1+8iJo6aZ8noINFtaYlw/imz3
        qjEZVLG7I8WoFi8rbX0xGAyfA7bhRrHbF7z1+OEPFz2hwXe1CSvyFUVO6bjyV+DtNScRaj/J
        vbHaMWmz1n6bvciGDL9K6lCe51BIP7OSbzAYrgS24E19mrBf/xRZU1rnhxnoFCYvWUkWYs4U
        YIpcInLCigf/QLgWNFpzWm+DZQV68LjHhn2fzFbFYPhkZCIn1IyxPfypxxo5YdQTtHW5GtMr
        g47LFMTT4jibUMG+c+iWPjpeJKeNqT3J2w0Gw+egIO0iW/CCoB7+cPvtj+UQAy/8LeN/L3d8
        ApepOaXHKevCSgXtKRwxoTm1U0vZysjJYPhUcFrXUFoHYnr4M9SbfOS01oI4huu+vxPhOv4Z
        bOHZrEK7AEr3ZRX7PqEx9ZVOeQaD4QqIpASti4z8otZPR05+pS4nJbhMi9SVzH0KkqxT57JQ
        fSoyoNcNHfSrnWRVvbljg8HwCmQ6B9JjbkVpt2Qgh4L4kYGZr2hNOQfXcx7j6GlsRRqyVZHC
        kdGaCKlTM7BWyck3clJT54hmTbNYNRjeDG5HQUsJGnuLMhCTN48jYsLWbII/eKUWvEXl/r0L
        ZT7XISceHzVpUc1HTfpHYFJaP+hrOscd5X5ScKHHRkwGw9uBzCZpScExE1OOpNYPQZSJdI7t
        UG46coosVJpYboD2FpBTn6RzICd48kAXVRQiYyEurSuTfjyDwXA2oiEldTylp6xjYvJRU0JQ
        vpkfQ1tfN833HFyv5pR6Pnm7Ba1FtTvNZ3eZ411wMZQyCLq8l5ERk8HwZnh/pipM5WGiaben
        N28eR+SGyOlCic31ak548+4FhY2Ni4iQznFqx2ndNMQSeHZ8NKmBwfAOcBcH6RGxcc0pTfG8
        kZxKBkBKTE43HTl5LVMdjqcp2PrCxpdTO6R1ICw2UPMmYjqo08jJYHg7iiSzaai519vtvkBM
        6wdamSvjSOzm07qiEJFSpzZoU++cSABWujrHe169mzBZhAz8x+FVHsQGgyGHYklMPE589XB6
        KyirSaUIN01OIqd/ydVG5KB/jMOz2/vi+D5Yp/AgyXFw0Rd68FIP6IW8wFb1DPeG5J5b3IMF
        FcJbas5XFfhqqxN7H+JiOLeptOsPyV4+b8IipAZ1G/4wWJmbpqBQXcy6IgcD7xd9TKRpMNwh
        XhJZSpEMwXyk8eGPQQW+1f45tKZgnPgHZi03Qk5rN7VhopE9RRkmtXiLFvTiqXsmJmiwsT1E
        miJGUIb7A3t+wxLXp2B6zDPmNo+x28D2p8jmZ2juxdTeltrLPqjm+8nklDQHTxN9jQYh+NQO
        2qdOLVgGWajHZ1GRphGT4V7BpJSqwCtHNCtyp938cJESIiZY8K4fQ+8cVOAfOPfw88jJWzKs
        iJjIR6ZuktW62a3Wjb325e3d96bq8UlEylmnuXzauzMYPgfRnLlKFkrwqo4n824ojXv4MzgO
        8Cqdn6DU3GNap6LKqtJhffAiliAhmFWU2WrBvG5JNS6U+ulMN4PhXsFSASYl6JoWNSe1Qnn8
        U+Tx3+41Vu/8ZJXVhw9lvYG0TsMbtlhZdY582L53oIip27mvu38o/JuY/jJdbjnTYPh6oOhp
        0aKSrM6tH12NafuHyMO/HDltf8SK8ahFpTr94y+EzyMn9PaIxPUnjI6umnCMGffdzskOalWy
        isRpX6n1p9TX3GC4G9CqXIWUrg7ElE7o9TUn9Wl6/FcyrbeOI6+7iZywNIlpwb5FZXLkhFU5
        +D61TyHUrFu1U5lFKiWlcnRRk5nVGe4aSd3JR07NkcjpR6g5Pf7bnff/f7K/D3LSP+BMKnKQ
        TTknilVd8vStLntHWs0uEFd/IB1GEcsIeGz1S+cMhptGZmhAeg4ZSLMKY8LxGvUjHuu0ftBm
        3k08mw6SBB6KeUH19zn4XHJKUdL8Oz/7Tqe39I+OgDAkoRCX4vX7MAOv24kcINosRPoyCDMn
        HawAtXkhpoMyfD0gZfM9bKRrKoogam43YcYcH6+2Ljp6+FPk8Q8VYG6DLxP3x2XFnB+HzyOn
        HNLxUpjggtE0Yx/ElkUpcngS2T+5J0TkPw4xJqKxya3gQRMlQupyg+ELwa/EUZrFqVdNGia0
        o6yoDWX9GCQDa9UyQSrAfkzHyOguIicGCtjwfRKhWpRaOEBwibE1TSuy++UYf9cmxbo5qMen
        0a34FRPpoMTcNA1fFBQpQSoAoWVZUTlE60nbH0FQiT1U4Dyxt9GBmP4Br/fkJ0RMwOeTUzrz
        zs/CK+PpLYh4/Ax35NWt+Amj7Pk0dDRBuFShpoS00aQGhq8IbkPJ2ezyStzmR5AJbH8GUto8
        LgcVsI4pN533E+6XzyWnlJhwTkQFmY3Iai2+V44Jq93EvT6FuBRuGkX6TqTei9R9UI8Dvjl4
        Cj/fYPgqYAtsFlh6uQBP6NUo6RHKb12R8wtNVJPyE3svMxDzEriNyEkkQ1Ka1lWtSCsuOkqH
        /GF1DinhmLS3jH14EoxCkZWJNA1fEFyg9h79cLNUqQAWkFYPrqa0/UNkqxKBx385oSW7Xtbw
        Al+F+ym9N+42rQMWfxCNkkRCH960ctonDD6oKmdo510ytSG424l0a0dSImFlrkz1UAbDF8Ms
        Se8cKcC93e7aRU7bHyFyevyXyI//cRsTWlnH+wva7L4Xt0NOKcpSpFAmn+oguvSFbm0UniaR
        QcmqU83T6tmt4jX78O+xwLOoRAo1szsp2LS0z/BROHUtQhPIbSmk/IbA0q/QJa0pP/5H5Mf/
        ydjqJsc3gtslJ25BYa1FSdMd0CSMpVOIM+EJVTXqrgmhZnJc1bqCRxqorIGdwfABWOiKSMsE
        4uBG3Nzx9mfQMW1/LgveJcsQPke/dC5ul5xERLhPSEonAYDhQFmEHLvduKcEyw1gqoV+PC/S
        1KbhThshMbQTEdlIEgSZLHAyfBxYIsA+TFz8zgkseYt65H6SmyVJBRZaqYQUbwQ3Tk6iEZSu
        xs36BJlERGbNk1WkOfSxi2ZVKzlpind4dqJN2LHgQ0INC03GkB0gejIYPhLcD5euxtUNCSu3
        ofUkmo7yGJwstz+DWVwqFeC2lBsjJeC2yQmpnd9rD16pEoC60Rw71UHBemUrsvvtPpz9b10q
        beipIS7NG3qRUlf2xj4Q02C+UIaPxAurcOiW8ASU+H5vkuMVfd9qG1blfOSEjOR2Ceq2ycmj
        CLVCkBVHTs06qMvxobZrkf2GPpR0UIKEQQplJTLQKgVqTzf2YRm+OZBuIVJqEj+ldh2U3t7v
        +4dKBn4Gz++W+ujQ0LsYUJD0zt0gbp+cUhEYK7yxUiFz8IeCDqo/uA8SHwz70bAuyq+AFM7a
        t8JU4dtZUjXcXoTEowAACuJJREFUCYoi1JfqRqRswuimeiXSaG3V65f+0PSN9l5QmdlQEMfP
        8tf37UVNIrdOTjlrCJnDYd2I77WrGlXHqip87N2qHI+XgpUKJAloafGkx8M7S7nVJ4rhu4LV
        35lJvC2bw5HnNwrgD3+6QjnqUz4dZD0TOVl+ovr7HNw2OQHH/ogzzOoqVZJPQRowjerxVIU8
        29un0BQX2K8wMVV1nOYZDB8BXq0DufA0Xgy5ZOfKH/9WDdO/nYSgWeX1SyhpfOD0lPfia5DT
        MbBZXUnaJO+Q2bgICh7kkA0MQ3ArQAGda1IFrWRwKimy1EDlVvVs8vAd4lTLB11PaQEa53yd
        aBtW5PAahXCkcBuqN8FtYP0YBn8s/J5uM3V7CV+bnLiAnTuPOhSEmsMhSA7KIlhMHFRusNqo
        5EBfdxunjRrHoIVKNVE45t/DZ6BGSncHruPw6zSKWYxtqpbk5Ecz4fgh1Ja2KH5TTfUUCRk5
        fQL4CRRFNHDTXImMG5fKQYqAvL5dOyJqN0EThdWOw5Mjp6F3EdhA9ayhFyl6kUFc9IYICn18
        Mx0b7gNppMJ+SOgVTXva+Jx3CziiZUJK572Zkmm8cBTIFbu/GDGJfBdyEiFiEgmhSyFSVG7V
        o1mr+nsO52EDDMP3A9oBqC3g8ByGLGDfHzT9E1WTy/EUT8QI6l6QS6OwoY4EBwB4kfHep3IP
        ceQUvX6ISatZh2m8KEfcgFHcJfA9yOklw7pKVz2mUWSF81XQjUAHgqkuudfwKUc/ni+wa2on
        opop8ifH72G4DywM2kiFjfJC1YQH3ypznZ1DTvh//UAC9f6+MaO4S+Drk9NLhnWiKnLUhGC9
        Urciw0akR8FxE2pOSOv2T+Gi2Wvby6Ghp9Mc5AjzFKxbSqEBCvp9hjtBQkxYXOEHYhQhUQS0
        otQtbUnhNI8jrSj6akJTr/91blsqcApfn5xElhGTPw8fcuq3g6Ic/XTdLjRSgpAO+jQ7rEX2
        +mTas0Zkdv9/1dHq3qRkpBfoZKR0l5jFPai4NYS9lyAN4DYUv9r2EA8jSI9X27jPjif53phR
        3CXwPcgJSD+IshSZNQWrapGpCdNYoIfq90Hk5sVum/gcT3aBhmronR0wbFdm/dloxyswrKGw
        4OkeENWYaBqKdxhAO8oqRETQK23Vc+kUObWbpWaJ5S9lhpy+ML4XOaUoCuf59BK6vRbDN2G/
        f4rrAmh5maawWtcfyFi+dJES5u5xeG/EdF+IiuDkAIBRZ61GTislpwdSeq8fAyGlq3QrXZm7
        I3xvcjoHvv1FU75xcEpzpIizkBULhei+wLlyBAfVOepQIx1PA0kcpnjPAz+B7LRiEWO6a+BI
        XSZtnYom35bJvgh2uandCcS97TrugUO0tFVB5foH1ZXQS9eG9O0LKbsvBSMnSQgHK3ssrEzJ
        iWtXq60jJ2if4G8+psdIJRMhJx/nVOj+teGqSNXb0TkSTvKx38qlfqlKjptV7CTAG2bHYeUO
        BW5vDneffZ5GThHhNIEkODQXIdeDNp4Ntn5U+99OBywc2Xw0NYTU0BvcFYEA51kiB06TJVwf
        OX+jKCriQQJUkOZzbG0SeXqTtzfSs81jKIivtsF/qdWICapvRE136pBh5ORrA9pjNxMrYLWP
        faLQJQ4zr8Nz8CbHNtBxtw9tM56o6LgonMpchLRSEjRTIkZMH4GcNinVKIFw+BgkUpM1iY9+
        2LIEgwfIZ4n3q+2S0MraXZffrNB9Loyc8GSsavHhCp/DSh1m5nWbkMphUAL2EGry+W4Xk9eg
        hMVPQ68sT7VSKknwjgqGqwC1xZIIKloBI41SSjh8nFvpze2PHXvnS5IIWOR0z6C0ToTqT6O6
        GgwhYuo7kbaL21mGLpBQNEghPdbv6epw0YuEFUDUuPwABxEZjZg+DDnxJI49WVBXQUsqbSi2
        fd0od7xO0r1VHCWxtz2PF8dmNac7BNeVikJkqsTPt6tVE8VDELjojX23jwcp7J/i103r9nWt
        EzWgidJhoCOcEiTUmuY5SBSMm66PyMoko1HyMgBKw9ak3D6m6sY5r5er48hoMdCSSDHaGznd
        H4pCREptPylFKll6NdWZ1TX4Qc0q5Nz9Flk9xQMVKn4iJhbBM2mmylqkHFzUVM5BM4WU7g6f
        mh8LWvzwXf1k1uZrTqnx22MocEcDBZJz8PXGuCeOiPzPqMLvkXU1uL9rwMhJ5PSHX5MWaUp0
        SlCZV1wshXkdaV+KI0pepG1VRaQ3xMelHue0UC/poLLp4InvuaUU8mQrRuYze+l7sjqmIpBD
        mk7hdbPOTDshg7d08sk6ISt42UfElxTd75B8TsHI6VykDcZF6YgJ4X9dh6K5ryHN4SKMUgN6
        +u6fRB5+qlZqiDcWdOLfjJw4M0LONOqbj5wTiS1evAfVR4lBzxQ/Rp31R16nGqX0dU5Aya+r
        Oq73pFuzWlqV5OxL/OQTFLohB6AVt2heXPp+DQwjp1fBi44k8o0qSIpQtyINNEsSwvZokoaa
        1Xc7kbUWzTE/j2tb0EXhmHsCZ9qQXqJW5iO8zDmO/tJjEJ5ITFzH1OsX+ZMeET76v+sRkskt
        +UcuAKUsitvck5ambV6JXce1IE9O27gQ7mtPmam79TpE0UUlrpio7wEtl0Lv2ZCFkdNrwDcN
        RyOVSg1mUpaXScTUrES6tUivEROkBpAW9IfE77yP97AX9tsgC8W53xLlOZ/j9plpEimT1+zq
        WUocUR0jpnMIK5du5aKdqM5SLsln8To18K/icy9udZCJvORSmcoFfO2JTAmxx9ebVfBZSh0q
        0yjQkIWR0zlILVnm5Gt4+s5t+B5WlPetyhA2SxkCH4/qdDD2+eMpTfuSFcS0VoWi/ZgQGaKt
        giKreRKRMSYniEAL0fd8jXoUR06FSJWkYDxJBFtF9TuuE9WZpfhj0VCq9kak01DdMBVcNiQB
        qJPj3GtPdjzMNZOeGkllYeT0GrDmKKqTlOFJ3Eic5lVqk9EOcTTk21c4OuKWl+Q1iCqNqtLz
        izaZ9FgJqsr1+BXURkPpHbf0XDKti25Qqsn44xf62bhWxASUHudGevNrENJC8Z28rrDV4bhO
        zqVRVzTRJ1NjMlJ6EUZO54IL4oviuIgvfk9Uw1hEMck+dTHgKCryLe+WX2PSSkmNG45BXFUy
        Hgs/tyjdMd8oE6Wm8EgvRbIj2lN75NzfLXccRUdH6kUcBRVVPirKEVBZJ2TUZggoHfV9JCLC
        pFz/c3POAy+s9PkUszzjb2JgGDm9Bml6B8xF0DGVlQo426RwPUtUoE6PQU6ntiEZtIC6VURe
        fXxc6l40okMkVZR6XIiM7sv6RjW9m4JKHecu/fdMG24jIzX0lqGNo8pERxz5NJKNfrylbWZE
        d+7cwgq3iYkzV2zn42yBvli+d8OLMHJ6C9ILq6rCcXb5PdEk8R7H4xBIBgVyLpZHxXPq34uO
        D8t6Ftw6q87dMEPvoruy0siKiGHA71qInygDMSivpl0iteN+tiK9mcs4Askpq7kZl1tB0rpP
        2tuWFrX91zL9cjhf1uF3TtMz1ktFr8UI6J0wcro0zr040xu8LMWtiFFUJVNcE5pHd1NOddhP
        mT6snHXrQg8kX6MYm/t78vtg/VCO3NJUy1ucNMsNuiQ+btpATrnfyXA1lKe/xWAwGD4eRk4G
        g+EmYeRkMBhuEsU831Knp8FgMDhY5GQwGG4SRk4Gg+EmYeRkMBhuEkZOBoPhJvH/AUvC47Iw
        c997AAAAAElFTkSuQmCC
      </shape>
      <shape name='Codex/Info-Icon.png'>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAA4WlDQ1BzUkdCAAAYlWNgYDzN
        AARMDgwMuXklRUHuTgoRkVEKDEggMbm4gAE3YGRg+HYNRDIwXNYNLGHlx6MWG+AsAloIpD8A
        sUg6mM3IAmInQdgSIHZ5SUEJkK0DYicXFIHYQBcz8BSFBDkD2T5AtkI6EjsJiZ2SWpwMZOcA
        2fEIv+XPZ2Cw+MLAwDwRIZY0jYFhezsDg8QdhJjKQgYG/lYGhm2XEWKf/cH+ZRQ7VJJaUQIS
        8dN3ZChILEoESzODAjQtjYHh03IGBt5IBgbhCwwMXNEQd4ABazEwoEkMJ0IAAHLYNoSjH0ez
        AAAACXBIWXMAAAsTAAALEwEAmpwYAAABOklEQVRIibWWfXHDMAzFfw2CQBCDDULGIAx2Q7Aw
        iBmsDFoKQ7CNQRl4DFoG3R+WL6lb13KavTtf7hzpPUmWP+A+WmAEvgAPnHUcdW4ApMBxE6IE
        R2AL9AmRAB2wV+FdjdC7OjnNwAKnwQwlw1HJzdHMIOo75gxi5EvIU5GrTISQ4iPkqcgF145Q
        x7XgCE1yoWiBN9q2hIq0G0K9noA3g+NZvxuD7RY4QUilMzhAyFaMtr1yVy2utUQxGA9T2hbE
        o8Js31QYL0ID/LJO/6cQ4BQFnv9J4NAAP9i7qAavwCfMNoXBqWaRPSANYTMcMBy1wIuOEkbg
        m1B+IHNALYSQ2VvDCiJC5riOcA+IRHJXMhwIKWZvpgTxYXA38lvR7Jku9I7rS78HPpheGPP/
        ZohGFV8YsU29zjkK7f0H0lNSPuu/nkIAAAAASUVORK5CYII=
      </shape>
    </shapes>
  </external>
</workbook>
