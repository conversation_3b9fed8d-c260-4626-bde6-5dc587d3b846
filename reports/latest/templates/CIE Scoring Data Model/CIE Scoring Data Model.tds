<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20231.23.0505.1444                               -->
<datasource formatted-name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' inline='true' source-platform='mac' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.ParameterDefaultValues.true...ParameterDefaultValues />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location id='${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.1' site='${TABLEAU_SITE}' />
  <connection class='federated'>
    <named-connections>
      <named-connection caption='${SNOW_SERVER}' name='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy'>
        <connection authentication='Username Password' class='snowflake' dbname='${SNOW_DBNAME}' odbc-connect-string-extras='' one-time-sql='' schema='DCO' server='${SNOW_SERVER}' server-oauth='' service='${SNOW_ROLE}' username='${SNOW_USERNAME}' warehouse='${SNOW_WAREHOUSE}' workgroup-auth-mode='prompt' />
      </named-connection>
    </named-connections>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_DCO_REASON_RPT' table='[DCO].[VW_DIM_DCO_REASON_RPT]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='Custom SQL Query' type='text'>SELECT *&#13;
FROM &quot;DCO&quot;.&quot;VW_F_CIE_SCORING_RPT_V3&quot;</relation>
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_DCO_REASON_RPT' table='[DCO].[VW_DIM_DCO_REASON_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_DCO_RUN_RPT' table='[DCO].[VW_DIM_DCO_RUN_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_DSE_CONFIG' table='[DCO].[VW_DIM_DSE_CONFIG]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_PRODUCT_DCO_RPT' table='[DCO].[VW_DIM_PRODUCT_DCO_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_SCENARIO_RPT' table='[DCO].[VW_DIM_SCENARIO_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_SEGMENT_RPT' table='[DCO].[VW_DIM_SEGMENT_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_SEGMENT_TYPE_RPT' table='[DCO].[VW_DIM_SEGMENT_TYPE_RPT]' type='table' />
    </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
    <cols>
      <map key='[ACTIVE_ACCOUNTS]' value='[Custom SQL Query].[ACTIVE_ACCOUNTS]' />
      <map key='[CHANNEL]' value='[Custom SQL Query].[CHANNEL]' />
      <map key='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT].[CREATED_BY]' />
      <map key='[CREATED_BY]' value='[VW_DIM_DCO_RUN_RPT].[CREATED_BY]' />
      <map key='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT].[CREATED_TS]' />
      <map key='[CREATED_TS]' value='[VW_DIM_DCO_RUN_RPT].[CREATED_TS]' />
      <map key='[DCO_REASON_CODE]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_CODE]' />
      <map key='[DCO_REASON_DESCRIPTION]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_DESCRIPTION]' />
      <map key='[DCO_REASON_NAME]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_NAME]' />
      <map key='[DCO_REASON_TYPE_CODE]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_TYPE_CODE]' />
      <map key='[DCO_REASON_TYPE_NAME]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_TYPE_NAME]' />
      <map key='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_UID]' />
      <map key='[DCO_REASON_UID]' value='[Custom SQL Query].[DCO_REASON_UID]' />
      <map key='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' value='[VW_DIM_DCO_RUN_RPT].[DCO_RUN_DATE]' />
      <map key='[DCO_RUN_DATE]' value='[Custom SQL Query].[DCO_RUN_DATE]' />
      <map key='[DCO_RUN_UID (Custom SQL Query)]' value='[Custom SQL Query].[DCO_RUN_UID]' />
      <map key='[DCO_RUN_UID]' value='[VW_DIM_DCO_RUN_RPT].[DCO_RUN_UID]' />
      <map key='[DESCRIPTION]' value='[VW_DIM_DSE_CONFIG].[DESCRIPTION]' />
      <map key='[DIM_BRAND_KEY]' value='[VW_DIM_PRODUCT_DCO_RPT].[DIM_BRAND_KEY]' />
      <map key='[DIM_COUNTRY_KEY]' value='[VW_DIM_PRODUCT_DCO_RPT].[DIM_COUNTRY_KEY]' />
      <map key='[DIM_CUSTOMER_KEY]' value='[VW_DIM_PRODUCT_DCO_RPT].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT].[DIM_PRODUCT_KEY]' />
      <map key='[DIM_PRODUCT_KEY]' value='[VW_DIM_PRODUCT_DCO_RPT].[DIM_PRODUCT_KEY]' />
      <map key='[DIM_SEGMENT_KEY]' value='[VW_DIM_SEGMENT_RPT].[DIM_SEGMENT_KEY]' />
      <map key='[DIM_SEGMENT_TYPE_KEY]' value='[VW_DIM_SEGMENT_TYPE_RPT].[DIM_SEGMENT_TYPE_KEY]' />
      <map key='[FINAL_SCORE_SUM]' value='[Custom SQL Query].[FINAL_SCORE_SUM]' />
      <map key='[HCP_SEGMENT]' value='[Custom SQL Query].[HCP_SEGMENT]' />
      <map key='[INFLUENCE_DISPLAY_NAME]' value='[Custom SQL Query].[INFLUENCE_DISPLAY_NAME]' />
      <map key='[INFLUENCE_UID]' value='[Custom SQL Query].[INFLUENCE_UID]' />
      <map key='[INFLUENCE_VALUE]' value='[Custom SQL Query].[INFLUENCE_VALUE]' />
      <map key='[IS_ACTIVE_SRC]' value='[VW_DIM_PRODUCT_DCO_RPT].[IS_ACTIVE_SRC]' />
      <map key='[IS_COMPETITOR]' value='[VW_DIM_PRODUCT_DCO_RPT].[IS_COMPETITOR]' />
      <map key='[IS_DELETED_SRC]' value='[VW_DIM_PRODUCT_DCO_RPT].[IS_DELETED_SRC]' />
      <map key='[IS_MAX_XAI_COMPONENT]' value='[Custom SQL Query].[IS_MAX_XAI_COMPONENT]' />
      <map key='[IS_PUBLISHED]' value='[VW_DIM_SCENARIO_RPT].[IS_PUBLISHED]' />
      <map key='[IS_SUB_COMPONENT]' value='[Custom SQL Query].[IS_SUB_COMPONENT]' />
      <map key='[LAST_DCO_RUN_DATE]' value='[VW_DIM_SCENARIO_RPT].[LAST_DCO_RUN_DATE]' />
      <map key='[LAST_DCO_RUN_UID]' value='[VW_DIM_SCENARIO_RPT].[LAST_DCO_RUN_UID]' />
      <map key='[LATEST_RUN]' value='[VW_DIM_DCO_RUN_RPT].[LATEST_RUN]' />
      <map key='[POST_PROC_STATUS]' value='[VW_DIM_DCO_RUN_RPT].[POST_PROC_STATUS]' />
      <map key='[PRODUCT_ID]' value='[VW_DIM_SEGMENT_RPT].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME]' value='[VW_DIM_PRODUCT_DCO_RPT].[PRODUCT_NAME]' />
      <map key='[PRODUCT_NAME_ENGLISH]' value='[VW_DIM_PRODUCT_DCO_RPT].[PRODUCT_NAME_ENGLISH]' />
      <map key='[PRODUCT_TYPE]' value='[VW_DIM_PRODUCT_DCO_RPT].[PRODUCT_TYPE]' />
      <map key='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' value='[VW_DIM_PRODUCT_DCO_RPT].[PRODUCT_UID]' />
      <map key='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT].[PRODUCT_UID]' />
      <map key='[PRODUCT_UID]' value='[Custom SQL Query].[PRODUCT_UID]' />
      <map key='[RECOMMENDED]' value='[Custom SQL Query].[RECOMMENDED]' />
      <map key='[RECORD_END_DATE]' value='[VW_DIM_PRODUCT_DCO_RPT].[RECORD_END_DATE]' />
      <map key='[RECORD_START_DATE]' value='[VW_DIM_PRODUCT_DCO_RPT].[RECORD_START_DATE]' />
      <map key='[SCENARIO_DESCRIPTION]' value='[VW_DIM_SCENARIO_RPT].[SCENARIO_DESCRIPTION]' />
      <map key='[SCENARIO_NAME]' value='[VW_DIM_SCENARIO_RPT].[SCENARIO_NAME]' />
      <map key='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' value='[VW_DIM_DCO_RUN_RPT].[SCENARIO_UID]' />
      <map key='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' value='[VW_DIM_SCENARIO_RPT].[SCENARIO_UID]' />
      <map key='[SCENARIO_UID]' value='[Custom SQL Query].[SCENARIO_UID]' />
      <map key='[SCENARIO_VER]' value='[VW_DIM_DCO_RUN_RPT].[SCENARIO_VER]' />
      <map key='[SEGMENT_NAME]' value='[VW_DIM_SEGMENT_RPT].[SEGMENT_NAME]' />
      <map key='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' value='[VW_DIM_SEGMENT_TYPE_RPT].[SEGMENT_TYPE]' />
      <map key='[SEGMENT_TYPE]' value='[VW_DIM_SEGMENT_RPT].[SEGMENT_TYPE]' />
      <map key='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' value='[VW_DIM_DSE_CONFIG].[SE_CONFIG_ID]' />
      <map key='[SE_CONFIG_ID]' value='[Custom SQL Query].[SE_CONFIG_ID]' />
      <map key='[SE_CONFIG_NAME]' value='[VW_DIM_DSE_CONFIG].[SE_CONFIG_NAME]' />
      <map key='[STATUS]' value='[VW_DIM_DCO_RUN_RPT].[STATUS]' />
      <map key='[SUGGESTION_CANDIDATE_UID]' value='[Custom SQL Query].[SUGGESTION_CANDIDATE_UID]' />
      <map key='[UPDATED_TS]' value='[VW_DIM_SEGMENT_RPT].[UPDATED_TS]' />
      <map key='[XAI_CONFIDENCE_DEGREE]' value='[Custom SQL Query].[XAI_CONFIDENCE_DEGREE]' />
    </cols>
    <metadata-records>
      <metadata-record class='column'>
        <remote-name>HCP_SEGMENT</remote-name>
        <remote-type>129</remote-type>
        <local-name>[HCP_SEGMENT]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>HCP_SEGMENT</remote-alias>
        <ordinal>1</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_UID]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>PRODUCT_UID</remote-alias>
        <ordinal>2</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_UID]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SCENARIO_UID</remote-alias>
        <ordinal>3</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_RUN_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_RUN_UID (Custom SQL Query)]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>DCO_RUN_UID</remote-alias>
        <ordinal>4</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_RUN_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DCO_RUN_DATE]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>DCO_RUN_DATE</remote-alias>
        <ordinal>5</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CHANNEL</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CHANNEL]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>CHANNEL</remote-alias>
        <ordinal>6</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>FINAL_SCORE_SUM</remote-name>
        <remote-type>5</remote-type>
        <local-name>[FINAL_SCORE_SUM]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>FINAL_SCORE_SUM</remote-alias>
        <ordinal>7</ordinal>
        <local-type>real</local-type>
        <aggregation>Sum</aggregation>
        <precision>15</precision>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DOUBLE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SUGGESTION_CANDIDATE_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SUGGESTION_CANDIDATE_UID]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SUGGESTION_CANDIDATE_UID</remote-alias>
        <ordinal>8</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>ACTIVE_ACCOUNTS</remote-name>
        <remote-type>129</remote-type>
        <local-name>[ACTIVE_ACCOUNTS]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>ACTIVE_ACCOUNTS</remote-alias>
        <ordinal>9</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>INFLUENCE_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[INFLUENCE_UID]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>INFLUENCE_UID</remote-alias>
        <ordinal>10</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>INFLUENCE_VALUE</remote-name>
        <remote-type>5</remote-type>
        <local-name>[INFLUENCE_VALUE]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>INFLUENCE_VALUE</remote-alias>
        <ordinal>11</ordinal>
        <local-type>real</local-type>
        <aggregation>Sum</aggregation>
        <precision>15</precision>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DOUBLE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>INFLUENCE_DISPLAY_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[INFLUENCE_DISPLAY_NAME]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>INFLUENCE_DISPLAY_NAME</remote-alias>
        <ordinal>12</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>RECOMMENDED</remote-name>
        <remote-type>11</remote-type>
        <local-name>[RECOMMENDED]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>RECOMMENDED</remote-alias>
        <ordinal>13</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_UID]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>DCO_REASON_UID</remote-alias>
        <ordinal>14</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>26</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[SE_CONFIG_ID]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SE_CONFIG_ID</remote-alias>
        <ordinal>15</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_MAX_XAI_COMPONENT</remote-name>
        <remote-type>131</remote-type>
        <local-name>[IS_MAX_XAI_COMPONENT]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>IS_MAX_XAI_COMPONENT</remote-alias>
        <ordinal>16</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>1</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>XAI_CONFIDENCE_DEGREE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[XAI_CONFIDENCE_DEGREE]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>XAI_CONFIDENCE_DEGREE</remote-alias>
        <ordinal>17</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>6</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_SUB_COMPONENT</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_SUB_COMPONENT]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>IS_SUB_COMPONENT</remote-alias>
        <ordinal>18</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_UID</remote-alias>
        <ordinal>20</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>2</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_CODE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_CODE]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_CODE</remote-alias>
        <ordinal>21</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>2</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_NAME]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_NAME</remote-alias>
        <ordinal>22</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>22</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_DESCRIPTION</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_DESCRIPTION]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_DESCRIPTION</remote-alias>
        <ordinal>23</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>347</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_TYPE_CODE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_TYPE_CODE]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_TYPE_CODE</remote-alias>
        <ordinal>24</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>9</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_TYPE_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_TYPE_NAME]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_TYPE_NAME</remote-alias>
        <ordinal>25</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>9</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_RUN_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_RUN_UID]</local-name>
        <parent-name>[VW_DIM_DCO_RUN_RPT]</parent-name>
        <remote-alias>DCO_RUN_UID</remote-alias>
        <ordinal>27</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]</local-name>
        <parent-name>[VW_DIM_DCO_RUN_RPT]</parent-name>
        <remote-alias>SCENARIO_UID</remote-alias>
        <ordinal>28</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_VER</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_VER]</local-name>
        <parent-name>[VW_DIM_DCO_RUN_RPT]</parent-name>
        <remote-alias>SCENARIO_VER</remote-alias>
        <ordinal>29</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>10</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>LATEST_RUN</remote-name>
        <remote-type>11</remote-type>
        <local-name>[LATEST_RUN]</local-name>
        <parent-name>[VW_DIM_DCO_RUN_RPT]</parent-name>
        <remote-alias>LATEST_RUN</remote-alias>
        <ordinal>30</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_RUN_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]</local-name>
        <parent-name>[VW_DIM_DCO_RUN_RPT]</parent-name>
        <remote-alias>DCO_RUN_DATE</remote-alias>
        <ordinal>31</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CREATED_BY</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CREATED_BY]</local-name>
        <parent-name>[VW_DIM_DCO_RUN_RPT]</parent-name>
        <remote-alias>CREATED_BY</remote-alias>
        <ordinal>32</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>STATUS</remote-name>
        <remote-type>129</remote-type>
        <local-name>[STATUS]</local-name>
        <parent-name>[VW_DIM_DCO_RUN_RPT]</parent-name>
        <remote-alias>STATUS</remote-alias>
        <ordinal>33</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>POST_PROC_STATUS</remote-name>
        <remote-type>129</remote-type>
        <local-name>[POST_PROC_STATUS]</local-name>
        <parent-name>[VW_DIM_DCO_RUN_RPT]</parent-name>
        <remote-alias>POST_PROC_STATUS</remote-alias>
        <ordinal>34</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CREATED_TS</remote-name>
        <remote-type>7</remote-type>
        <local-name>[CREATED_TS]</local-name>
        <parent-name>[VW_DIM_DCO_RUN_RPT]</parent-name>
        <remote-alias>CREATED_TS</remote-alias>
        <ordinal>35</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]</local-name>
        <parent-name>[VW_DIM_DSE_CONFIG]</parent-name>
        <remote-alias>SE_CONFIG_ID</remote-alias>
        <ordinal>37</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SE_CONFIG_NAME]</local-name>
        <parent-name>[VW_DIM_DSE_CONFIG]</parent-name>
        <remote-alias>SE_CONFIG_NAME</remote-alias>
        <ordinal>38</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DESCRIPTION</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DESCRIPTION]</local-name>
        <parent-name>[VW_DIM_DSE_CONFIG]</parent-name>
        <remote-alias>DESCRIPTION</remote-alias>
        <ordinal>39</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_PRODUCT_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_PRODUCT_KEY]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>DIM_PRODUCT_KEY</remote-alias>
        <ordinal>41</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>PRODUCT_UID</remote-alias>
        <ordinal>42</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_BRAND_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_BRAND_KEY]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>DIM_BRAND_KEY</remote-alias>
        <ordinal>43</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_NAME]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>PRODUCT_NAME</remote-alias>
        <ordinal>44</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_NAME_ENGLISH</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_NAME_ENGLISH]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>PRODUCT_NAME_ENGLISH</remote-alias>
        <ordinal>45</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_TYPE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_TYPE]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>PRODUCT_TYPE</remote-alias>
        <ordinal>46</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>RECORD_START_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[RECORD_START_DATE]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>RECORD_START_DATE</remote-alias>
        <ordinal>47</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>RECORD_END_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[RECORD_END_DATE]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>RECORD_END_DATE</remote-alias>
        <ordinal>48</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_ACTIVE_SRC</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_ACTIVE_SRC]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>IS_ACTIVE_SRC</remote-alias>
        <ordinal>49</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_COMPETITOR</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_COMPETITOR]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>IS_COMPETITOR</remote-alias>
        <ordinal>50</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_DELETED_SRC</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_DELETED_SRC]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>IS_DELETED_SRC</remote-alias>
        <ordinal>51</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CUSTOMER_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CUSTOMER_KEY]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
        <ordinal>52</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_COUNTRY_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_COUNTRY_KEY]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>DIM_COUNTRY_KEY</remote-alias>
        <ordinal>53</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>SCENARIO_UID</remote-alias>
        <ordinal>55</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_NAME]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>SCENARIO_NAME</remote-alias>
        <ordinal>56</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>100</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_DESCRIPTION</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_DESCRIPTION]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>SCENARIO_DESCRIPTION</remote-alias>
        <ordinal>57</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_PUBLISHED</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_PUBLISHED]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>IS_PUBLISHED</remote-alias>
        <ordinal>58</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>LAST_DCO_RUN_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[LAST_DCO_RUN_UID]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>LAST_DCO_RUN_UID</remote-alias>
        <ordinal>59</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>LAST_DCO_RUN_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[LAST_DCO_RUN_DATE]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
        <ordinal>60</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_SEGMENT_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_SEGMENT_KEY]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>DIM_SEGMENT_KEY</remote-alias>
        <ordinal>62</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>11</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SEGMENT_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SEGMENT_NAME]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>SEGMENT_NAME</remote-alias>
        <ordinal>63</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>200</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SEGMENT_TYPE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SEGMENT_TYPE]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>SEGMENT_TYPE</remote-alias>
        <ordinal>64</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>100</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_PRODUCT_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>DIM_PRODUCT_KEY</remote-alias>
        <ordinal>65</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>11</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>PRODUCT_UID</remote-alias>
        <ordinal>66</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>200</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[PRODUCT_ID]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>PRODUCT_ID</remote-alias>
        <ordinal>67</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>11</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CREATED_BY</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CREATED_BY (VW_DIM_SEGMENT_RPT)]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>CREATED_BY</remote-alias>
        <ordinal>68</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>50</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CREATED_TS</remote-name>
        <remote-type>7</remote-type>
        <local-name>[CREATED_TS (VW_DIM_SEGMENT_RPT)]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>CREATED_TS</remote-alias>
        <ordinal>69</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>UPDATED_TS</remote-name>
        <remote-type>7</remote-type>
        <local-name>[UPDATED_TS]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>UPDATED_TS</remote-alias>
        <ordinal>70</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_SEGMENT_TYPE_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_SEGMENT_TYPE_KEY]</local-name>
        <parent-name>[VW_DIM_SEGMENT_TYPE_RPT]</parent-name>
        <remote-alias>DIM_SEGMENT_TYPE_KEY</remote-alias>
        <ordinal>72</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>11</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SEGMENT_TYPE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]</local-name>
        <parent-name>[VW_DIM_SEGMENT_TYPE_RPT]</parent-name>
        <remote-alias>SEGMENT_TYPE</remote-alias>
        <ordinal>73</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>100</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
    </metadata-records>
  </connection>
  <aliases enabled='yes' />
  <column caption='% LOW Decisive Components' datatype='real' name='[% Decisive Components (copy)_1879408442265681937]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;LOW&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
  </column>
  <column caption='% MED Decisive Components' datatype='real' name='[% HIGH Decisive Components (copy)_1879408442266198035]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;MED&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
  </column>
  <column caption='% HIGH Decisive Components' datatype='real' name='[% LOW Decisive Components (copy)_1879408442266107922]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;AND XAI_CONFIDENCE_DEGREE =&apos;HIGH&apos;&#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)&#13;&#10;/ COUNT(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT=1 THEN 1 END)' />
  </column>
  <column caption='Active Accounts' datatype='string' default-role='measure' default-type='ordinal' name='[ACTIVE_ACCOUNTS]' role='dimension' type='nominal' />
  <column caption='% of Candidates Impacted' datatype='real' name='[Average score (copy)_1879408442056417282]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNTD(SUGGESTION_CANDIDATE_UID)&#13;&#10;/SUM([Calculation_1879408443317731348])' />
  </column>
  <column caption='Channel' datatype='string' name='[CHANNEL]' role='dimension' type='nominal' />
  <column caption='Created By (Vw Dim Segment Rpt)' datatype='string' name='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' role='dimension' type='nominal' />
  <column caption='Created By' datatype='string' name='[CREATED_BY]' role='dimension' type='nominal' />
  <column caption='Created Ts (Vw Dim Segment Rpt)' datatype='datetime' name='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' role='dimension' type='ordinal' />
  <column caption='Created Ts' datatype='datetime' name='[CREATED_TS]' role='dimension' type='ordinal' />
  <column caption='Influencer' datatype='boolean' name='[Calculation_1445936995299098631]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[Parameters].[Parameter 3]=[INFLUENCE_DISPLAY_NAME]' />
  </column>
  <column caption='Channel / Segment' datatype='string' name='[Calculation_1506454080614776832]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
  </column>
  <column aggregation='Sum' caption='Scenario group' datatype='integer' name='[Calculation_1604407389790982145]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
  </column>
  <column caption='Text Component Prevalence' datatype='string' name='[Calculation_1879408442241851404]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='&apos;PERCENTAGE OF CANDIDATES IMPACTED&apos;' />
  </column>
  <column caption='TotalCandidateCnt' datatype='integer' name='[Calculation_1879408443317731348]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='{ FIXED RECOMMENDED: &#13;&#10;countd( SUGGESTION_CANDIDATE_UID)}' />
  </column>
  <column caption='Calculate_accounts' datatype='integer' name='[Calculation_527765595207630863]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='COUNTD([ACTIVE_ACCOUNTS])' />
  </column>
  <column caption='Scenario' datatype='string' name='[Calculation_864972641503416320]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
  </column>
  <column caption='Dco Reason Code' datatype='string' name='[DCO_REASON_CODE]' role='dimension' type='nominal' />
  <column caption='Dco Reason Description' datatype='string' name='[DCO_REASON_DESCRIPTION]' role='dimension' type='nominal' />
  <column caption='Reason' datatype='string' name='[DCO_REASON_NAME]' role='dimension' type='nominal' />
  <column caption='Dco Reason Type Code' datatype='string' name='[DCO_REASON_TYPE_CODE]' role='dimension' type='nominal' />
  <column caption='Reason Type' datatype='string' name='[DCO_REASON_TYPE_NAME]' role='dimension' type='nominal' />
  <column caption='Dco Reason Uid (Vw Dim Dco Reason Rpt)' datatype='string' name='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' role='dimension' type='nominal' />
  <column caption='Dco Run Date (Vw Dim Dco Run Rpt)' datatype='date' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' role='dimension' type='ordinal' />
  <column caption='Dco Run Date' datatype='date' name='[DCO_RUN_DATE]' role='dimension' type='ordinal' />
  <column caption='Dco Run Uid' datatype='string' name='[DCO_RUN_UID]' role='dimension' type='nominal' />
  <column caption='Description' datatype='string' name='[DESCRIPTION]' role='dimension' type='nominal' />
  <column caption='Dim Brand Key' datatype='integer' name='[DIM_BRAND_KEY]' role='dimension' type='ordinal' />
  <column caption='Dim Country Key' datatype='integer' name='[DIM_COUNTRY_KEY]' role='dimension' type='ordinal' />
  <column caption='Dim Customer Key' datatype='integer' name='[DIM_CUSTOMER_KEY]' role='dimension' type='ordinal' />
  <column caption='Dim Product Key (Vw Dim Segment Rpt)' datatype='integer' name='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' role='dimension' type='ordinal' />
  <column caption='Dim Product Key' datatype='integer' name='[DIM_PRODUCT_KEY]' role='dimension' type='ordinal' />
  <column caption='Dim Segment Key' datatype='integer' name='[DIM_SEGMENT_KEY]' role='dimension' type='ordinal' />
  <column caption='Dim Segment Type Key' datatype='integer' name='[DIM_SEGMENT_TYPE_KEY]' role='dimension' type='ordinal' />
  <column caption='Average score' datatype='real' name='[FINAL_SCORE_SUM (copy)_1327436000330481664]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='SUM([FINAL_SCORE_SUM])/countd([SUGGESTION_CANDIDATE_UID])' />
  </column>
  <column caption='FINAL_SCORE' datatype='real' name='[FINAL_SCORE_SUM]' role='measure' type='quantitative' />
  <column caption='Hcp Segment' datatype='string' name='[HCP_SEGMENT]' role='dimension' type='nominal' />
  <column caption='Is Active Src' datatype='boolean' name='[IS_ACTIVE_SRC]' role='dimension' type='nominal' />
  <column caption='Is Competitor' datatype='boolean' name='[IS_COMPETITOR]' role='dimension' type='nominal' />
  <column caption='Is Deleted Src' datatype='boolean' name='[IS_DELETED_SRC]' role='dimension' type='nominal' />
  <column caption='Latest Run' datatype='boolean' name='[LATEST_RUN]' role='dimension' type='nominal' />
  <column caption='Post Proc Status' datatype='string' name='[POST_PROC_STATUS]' role='dimension' type='nominal' />
  <column caption='Product Id' datatype='integer' name='[PRODUCT_ID]' role='dimension' type='ordinal' />
  <column caption='Product Name' datatype='string' name='[PRODUCT_NAME]' role='dimension' type='nominal' />
  <column caption='Product Name English' datatype='string' name='[PRODUCT_NAME_ENGLISH]' role='dimension' type='nominal' />
  <column caption='Product Type' datatype='string' name='[PRODUCT_TYPE]' role='dimension' type='nominal' />
  <column caption='Product Uid (Vw Dim Product Dco Rpt)' datatype='string' name='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='Product Uid (Vw Dim Segment Rpt)' datatype='string' name='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' role='dimension' type='nominal' />
  <column caption='Product Uid' datatype='string' name='[PRODUCT_UID]' role='dimension' type='nominal' />
  <column caption='Record End Date' datatype='date' name='[RECORD_END_DATE]' role='dimension' type='ordinal' />
  <column caption='Record Start Date' datatype='date' name='[RECORD_START_DATE]' role='dimension' type='ordinal' />
  <column caption='Scenario Description' datatype='string' name='[SCENARIO_DESCRIPTION]' role='dimension' type='nominal' />
  <column caption='Scenario Name' datatype='string' name='[SCENARIO_NAME]' role='dimension' type='nominal' />
  <column caption='Scenario Uid (Vw Dim Dco Run Rpt)' datatype='string' name='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' role='dimension' type='nominal' />
  <column caption='Scenario Uid (Vw Dim Scenario Rpt)' datatype='string' name='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' role='dimension' type='nominal' />
  <column caption='Scenario Uid' datatype='string' name='[SCENARIO_UID]' role='dimension' type='nominal' />
  <column caption='Scenario Ver' datatype='string' name='[SCENARIO_VER]' role='dimension' type='nominal' />
  <column caption='Segment' datatype='string' name='[SEGMENT_NAME]' role='dimension' type='nominal' />
  <column caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' role='dimension' type='nominal' />
  <column caption='Segment Type' datatype='string' name='[SEGMENT_TYPE]' role='dimension' type='nominal' />
  <column caption='Se Config Id (Vw Dim Dse Config)' datatype='integer' name='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' role='dimension' type='ordinal' />
  <column caption='Config' datatype='string' name='[SE_CONFIG_NAME]' role='dimension' type='nominal' />
  <column caption='Status' datatype='string' name='[STATUS]' role='dimension' type='nominal' />
  <column caption='% Decisive Components' datatype='real' name='[Text Decisive Components (copy)_1879408442255036430]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='SUM(If NOT ISNULL([XAI_CONFIDENCE_DEGREE]) &#13;&#10;and IS_MAX_XAI_COMPONENT = 1 THEN 1 END)&#13;&#10;/ SUM([Calculation_1879408443317731348])' />
  </column>
  <column caption='Updated Ts' datatype='datetime' name='[UPDATED_TS]' role='dimension' type='ordinal' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_DCO_REASON_RPT' datatype='table' name='[__tableau_internal_object_id__].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_DCO_RUN_RPT' datatype='table' name='[__tableau_internal_object_id__].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_DSE_CONFIG' datatype='table' name='[__tableau_internal_object_id__].[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_PRODUCT_DCO_RPT' datatype='table' name='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_SCENARIO_RPT' datatype='table' name='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_SEGMENT_RPT' datatype='table' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_SEGMENT_TYPE_RPT' datatype='table' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_F_CIE_SCORING_RPT' datatype='table' name='[__tableau_internal_object_id__].[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]' role='measure' type='quantitative' />
  <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_1506454080614776832]' derivation='None' name='[none:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
  <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
  <extract _.fcp.ObjectModelExtractV2.true...object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' count='-1' enabled='false' units='records'>
    <connection access_mode='readonly' authentication='auth-none' author-locale='en_US' class='hyper' dbname='Data/tableau-temp/TEMP_1i9hsly1ykgndb198gb161ictdbd.hyper' schema='Extract' sslmode='' tablename='Extract' update-time='' updated-database='/Users/<USER>/Downloads/${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scores.twb Files/Data/Extracts/${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model.hyper' username=''>
      <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08' table='[Extract].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]' type='table' />
      <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
        <relation name='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' table='[Extract].[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]' type='table' />
        <relation name='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08' table='[Extract].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]' type='table' />
        <relation name='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46' table='[Extract].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]' type='table' />
        <relation name='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27' table='[Extract].[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]' type='table' />
        <relation name='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19' table='[Extract].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]' type='table' />
        <relation name='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41' table='[Extract].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]' type='table' />
        <relation name='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' table='[Extract].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]' type='table' />
        <relation name='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608' table='[Extract].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]' type='table' />
      </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
      <cols>
        <map key='[ACTIVE_ACCOUNTS]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[ACTIVE_ACCOUNTS]' />
        <map key='[CHANNEL]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[CHANNEL]' />
        <map key='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B].[CREATED_BY]' />
        <map key='[CREATED_BY]' value='[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46].[CREATED_BY]' />
        <map key='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B].[CREATED_TS]' />
        <map key='[CREATED_TS]' value='[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46].[CREATED_TS]' />
        <map key='[DCO_REASON_CODE]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08].[DCO_REASON_CODE]' />
        <map key='[DCO_REASON_DESCRIPTION]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08].[DCO_REASON_DESCRIPTION]' />
        <map key='[DCO_REASON_NAME]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08].[DCO_REASON_NAME]' />
        <map key='[DCO_REASON_TYPE_CODE]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08].[DCO_REASON_TYPE_CODE]' />
        <map key='[DCO_REASON_TYPE_NAME]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08].[DCO_REASON_TYPE_NAME]' />
        <map key='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08].[DCO_REASON_UID]' />
        <map key='[DCO_REASON_UID]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[DCO_REASON_UID]' />
        <map key='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' value='[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46].[DCO_RUN_DATE]' />
        <map key='[DCO_RUN_DATE]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[DCO_RUN_DATE]' />
        <map key='[DCO_RUN_UID (Custom SQL Query)]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[DCO_RUN_UID]' />
        <map key='[DCO_RUN_UID]' value='[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46].[DCO_RUN_UID]' />
        <map key='[DESCRIPTION]' value='[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27].[DESCRIPTION]' />
        <map key='[DIM_BRAND_KEY]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[DIM_BRAND_KEY]' />
        <map key='[DIM_COUNTRY_KEY]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[DIM_COUNTRY_KEY]' />
        <map key='[DIM_CUSTOMER_KEY]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[DIM_CUSTOMER_KEY]' />
        <map key='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B].[DIM_PRODUCT_KEY]' />
        <map key='[DIM_PRODUCT_KEY]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[DIM_PRODUCT_KEY]' />
        <map key='[DIM_SEGMENT_KEY]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B].[DIM_SEGMENT_KEY]' />
        <map key='[DIM_SEGMENT_TYPE_KEY]' value='[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608].[DIM_SEGMENT_TYPE_KEY]' />
        <map key='[FINAL_SCORE_SUM]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[FINAL_SCORE_SUM]' />
        <map key='[HCP_SEGMENT]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[HCP_SEGMENT]' />
        <map key='[INFLUENCE_DISPLAY_NAME]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[INFLUENCE_DISPLAY_NAME]' />
        <map key='[INFLUENCE_UID]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[INFLUENCE_UID]' />
        <map key='[INFLUENCE_VALUE]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[INFLUENCE_VALUE]' />
        <map key='[IS_ACTIVE_SRC]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[IS_ACTIVE_SRC]' />
        <map key='[IS_COMPETITOR]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[IS_COMPETITOR]' />
        <map key='[IS_DELETED_SRC]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[IS_DELETED_SRC]' />
        <map key='[IS_MAX_XAI_COMPONENT]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[IS_MAX_XAI_COMPONENT]' />
        <map key='[IS_PUBLISHED]' value='[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41].[IS_PUBLISHED]' />
        <map key='[IS_SUB_COMPONENT]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[IS_SUB_COMPONENT]' />
        <map key='[LAST_DCO_RUN_DATE]' value='[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41].[LAST_DCO_RUN_DATE]' />
        <map key='[LAST_DCO_RUN_UID]' value='[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41].[LAST_DCO_RUN_UID]' />
        <map key='[LATEST_RUN]' value='[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46].[LATEST_RUN]' />
        <map key='[POST_PROC_STATUS]' value='[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46].[POST_PROC_STATUS]' />
        <map key='[PRODUCT_ID]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B].[PRODUCT_ID]' />
        <map key='[PRODUCT_NAME]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[PRODUCT_NAME]' />
        <map key='[PRODUCT_NAME_ENGLISH]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[PRODUCT_NAME_ENGLISH]' />
        <map key='[PRODUCT_TYPE]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[PRODUCT_TYPE]' />
        <map key='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[PRODUCT_UID]' />
        <map key='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B].[PRODUCT_UID]' />
        <map key='[PRODUCT_UID]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[PRODUCT_UID]' />
        <map key='[RECOMMENDED]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[RECOMMENDED]' />
        <map key='[RECORD_END_DATE]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[RECORD_END_DATE]' />
        <map key='[RECORD_START_DATE]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19].[RECORD_START_DATE]' />
        <map key='[SCENARIO_DESCRIPTION]' value='[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41].[SCENARIO_DESCRIPTION]' />
        <map key='[SCENARIO_NAME]' value='[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41].[SCENARIO_NAME]' />
        <map key='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' value='[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46].[SCENARIO_UID]' />
        <map key='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' value='[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41].[SCENARIO_UID]' />
        <map key='[SCENARIO_UID]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[SCENARIO_UID]' />
        <map key='[SCENARIO_VER]' value='[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46].[SCENARIO_VER]' />
        <map key='[SEGMENT_NAME]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B].[SEGMENT_NAME]' />
        <map key='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' value='[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608].[SEGMENT_TYPE]' />
        <map key='[SEGMENT_TYPE]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B].[SEGMENT_TYPE]' />
        <map key='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' value='[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27].[SE_CONFIG_ID]' />
        <map key='[SE_CONFIG_ID]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[SE_CONFIG_ID]' />
        <map key='[SE_CONFIG_NAME]' value='[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27].[SE_CONFIG_NAME]' />
        <map key='[STATUS]' value='[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46].[STATUS]' />
        <map key='[SUGGESTION_CANDIDATE_UID]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[SUGGESTION_CANDIDATE_UID]' />
        <map key='[UPDATED_TS]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B].[UPDATED_TS]' />
        <map key='[XAI_CONFIDENCE_DEGREE]' value='[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF].[XAI_CONFIDENCE_DEGREE]' />
      </cols>
      <metadata-records>
        <metadata-record class='column'>
          <remote-name>HCP_SEGMENT</remote-name>
          <remote-type>129</remote-type>
          <local-name>[HCP_SEGMENT]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>HCP_SEGMENT</remote-alias>
          <ordinal>0</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>7</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>PRODUCT_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[PRODUCT_UID]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>PRODUCT_UID</remote-alias>
          <ordinal>1</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>8</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SCENARIO_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SCENARIO_UID]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>SCENARIO_UID</remote-alias>
          <ordinal>2</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>17</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_RUN_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_RUN_UID (Custom SQL Query)]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>DCO_RUN_UID</remote-alias>
          <ordinal>3</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>36</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_RUN_DATE</remote-name>
          <remote-type>133</remote-type>
          <local-name>[DCO_RUN_DATE]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>DCO_RUN_DATE</remote-alias>
          <ordinal>4</ordinal>
          <family>Custom SQL Query</family>
          <local-type>date</local-type>
          <aggregation>Year</aggregation>
          <approx-count>17</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CHANNEL</remote-name>
          <remote-type>129</remote-type>
          <local-name>[CHANNEL]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>CHANNEL</remote-alias>
          <ordinal>5</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>FINAL_SCORE_SUM</remote-name>
          <remote-type>5</remote-type>
          <local-name>[FINAL_SCORE_SUM]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>FINAL_SCORE_SUM</remote-alias>
          <ordinal>6</ordinal>
          <family>Custom SQL Query</family>
          <local-type>real</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>428709</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SUGGESTION_CANDIDATE_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SUGGESTION_CANDIDATE_UID]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>SUGGESTION_CANDIDATE_UID</remote-alias>
          <ordinal>7</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2277720</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>ACTIVE_ACCOUNTS</remote-name>
          <remote-type>129</remote-type>
          <local-name>[ACTIVE_ACCOUNTS]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>ACTIVE_ACCOUNTS</remote-alias>
          <ordinal>8</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>58698</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>INFLUENCE_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[INFLUENCE_UID]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>INFLUENCE_UID</remote-alias>
          <ordinal>9</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>17</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>INFLUENCE_VALUE</remote-name>
          <remote-type>5</remote-type>
          <local-name>[INFLUENCE_VALUE]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>INFLUENCE_VALUE</remote-alias>
          <ordinal>10</ordinal>
          <family>Custom SQL Query</family>
          <local-type>real</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>116822</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>INFLUENCE_DISPLAY_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[INFLUENCE_DISPLAY_NAME]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>INFLUENCE_DISPLAY_NAME</remote-alias>
          <ordinal>11</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>17</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>RECOMMENDED</remote-name>
          <remote-type>11</remote-type>
          <local-name>[RECOMMENDED]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>RECOMMENDED</remote-alias>
          <ordinal>12</ordinal>
          <family>Custom SQL Query</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_UID]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>DCO_REASON_UID</remote-alias>
          <ordinal>13</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>6</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SE_CONFIG_ID</remote-name>
          <remote-type>20</remote-type>
          <local-name>[SE_CONFIG_ID]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>SE_CONFIG_ID</remote-alias>
          <ordinal>14</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>8</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>IS_MAX_XAI_COMPONENT</remote-name>
          <remote-type>20</remote-type>
          <local-name>[IS_MAX_XAI_COMPONENT]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>IS_MAX_XAI_COMPONENT</remote-alias>
          <ordinal>15</ordinal>
          <family>Custom SQL Query</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>XAI_CONFIDENCE_DEGREE</remote-name>
          <remote-type>129</remote-type>
          <local-name>[XAI_CONFIDENCE_DEGREE]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>XAI_CONFIDENCE_DEGREE</remote-alias>
          <ordinal>16</ordinal>
          <family>Custom SQL Query</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>IS_SUB_COMPONENT</remote-name>
          <remote-type>11</remote-type>
          <local-name>[IS_SUB_COMPONENT]</local-name>
          <parent-name>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</parent-name>
          <remote-alias>IS_SUB_COMPONENT</remote-alias>
          <ordinal>17</ordinal>
          <family>Custom SQL Query</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</parent-name>
          <remote-alias>DCO_REASON_UID</remote-alias>
          <ordinal>18</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>7</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_CODE</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_CODE]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</parent-name>
          <remote-alias>DCO_REASON_CODE</remote-alias>
          <ordinal>19</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>7</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_NAME]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</parent-name>
          <remote-alias>DCO_REASON_NAME</remote-alias>
          <ordinal>20</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>7</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_DESCRIPTION</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_DESCRIPTION]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</parent-name>
          <remote-alias>DCO_REASON_DESCRIPTION</remote-alias>
          <ordinal>21</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>7</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_TYPE_CODE</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_TYPE_CODE]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</parent-name>
          <remote-alias>DCO_REASON_TYPE_CODE</remote-alias>
          <ordinal>22</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_TYPE_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_TYPE_NAME]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</parent-name>
          <remote-alias>DCO_REASON_TYPE_NAME</remote-alias>
          <ordinal>23</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_RUN_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_RUN_UID]</local-name>
          <parent-name>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</parent-name>
          <remote-alias>DCO_RUN_UID</remote-alias>
          <ordinal>24</ordinal>
          <family>VW_DIM_DCO_RUN_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>37</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SCENARIO_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]</local-name>
          <parent-name>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</parent-name>
          <remote-alias>SCENARIO_UID</remote-alias>
          <ordinal>25</ordinal>
          <family>VW_DIM_DCO_RUN_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>17</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SCENARIO_VER</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SCENARIO_VER]</local-name>
          <parent-name>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</parent-name>
          <remote-alias>SCENARIO_VER</remote-alias>
          <ordinal>26</ordinal>
          <family>VW_DIM_DCO_RUN_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>LATEST_RUN</remote-name>
          <remote-type>11</remote-type>
          <local-name>[LATEST_RUN]</local-name>
          <parent-name>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</parent-name>
          <remote-alias>LATEST_RUN</remote-alias>
          <ordinal>27</ordinal>
          <family>VW_DIM_DCO_RUN_RPT</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_RUN_DATE</remote-name>
          <remote-type>133</remote-type>
          <local-name>[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]</local-name>
          <parent-name>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</parent-name>
          <remote-alias>DCO_RUN_DATE</remote-alias>
          <ordinal>28</ordinal>
          <family>VW_DIM_DCO_RUN_RPT</family>
          <local-type>date</local-type>
          <aggregation>Year</aggregation>
          <approx-count>18</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CREATED_BY</remote-name>
          <remote-type>129</remote-type>
          <local-name>[CREATED_BY]</local-name>
          <parent-name>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</parent-name>
          <remote-alias>CREATED_BY</remote-alias>
          <ordinal>29</ordinal>
          <family>VW_DIM_DCO_RUN_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>5</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>STATUS</remote-name>
          <remote-type>129</remote-type>
          <local-name>[STATUS]</local-name>
          <parent-name>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</parent-name>
          <remote-alias>STATUS</remote-alias>
          <ordinal>30</ordinal>
          <family>VW_DIM_DCO_RUN_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>POST_PROC_STATUS</remote-name>
          <remote-type>129</remote-type>
          <local-name>[POST_PROC_STATUS]</local-name>
          <parent-name>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</parent-name>
          <remote-alias>POST_PROC_STATUS</remote-alias>
          <ordinal>31</ordinal>
          <family>VW_DIM_DCO_RUN_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CREATED_TS</remote-name>
          <remote-type>135</remote-type>
          <local-name>[CREATED_TS]</local-name>
          <parent-name>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</parent-name>
          <remote-alias>CREATED_TS</remote-alias>
          <ordinal>32</ordinal>
          <family>VW_DIM_DCO_RUN_RPT</family>
          <local-type>datetime</local-type>
          <aggregation>Year</aggregation>
          <approx-count>37</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SE_CONFIG_ID</remote-name>
          <remote-type>20</remote-type>
          <local-name>[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]</local-name>
          <parent-name>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</parent-name>
          <remote-alias>SE_CONFIG_ID</remote-alias>
          <ordinal>33</ordinal>
          <family>VW_DIM_DSE_CONFIG</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SE_CONFIG_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SE_CONFIG_NAME]</local-name>
          <parent-name>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</parent-name>
          <remote-alias>SE_CONFIG_NAME</remote-alias>
          <ordinal>34</ordinal>
          <family>VW_DIM_DSE_CONFIG</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DESCRIPTION</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DESCRIPTION]</local-name>
          <parent-name>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</parent-name>
          <remote-alias>DESCRIPTION</remote-alias>
          <ordinal>35</ordinal>
          <family>VW_DIM_DSE_CONFIG</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_PRODUCT_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_PRODUCT_KEY]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>DIM_PRODUCT_KEY</remote-alias>
          <ordinal>36</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>PRODUCT_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>PRODUCT_UID</remote-alias>
          <ordinal>37</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_BRAND_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_BRAND_KEY]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>DIM_BRAND_KEY</remote-alias>
          <ordinal>38</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>PRODUCT_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[PRODUCT_NAME]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>PRODUCT_NAME</remote-alias>
          <ordinal>39</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>PRODUCT_NAME_ENGLISH</remote-name>
          <remote-type>129</remote-type>
          <local-name>[PRODUCT_NAME_ENGLISH]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>PRODUCT_NAME_ENGLISH</remote-alias>
          <ordinal>40</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>PRODUCT_TYPE</remote-name>
          <remote-type>129</remote-type>
          <local-name>[PRODUCT_TYPE]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>PRODUCT_TYPE</remote-alias>
          <ordinal>41</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>RECORD_START_DATE</remote-name>
          <remote-type>133</remote-type>
          <local-name>[RECORD_START_DATE]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>RECORD_START_DATE</remote-alias>
          <ordinal>42</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>date</local-type>
          <aggregation>Year</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>RECORD_END_DATE</remote-name>
          <remote-type>133</remote-type>
          <local-name>[RECORD_END_DATE]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>RECORD_END_DATE</remote-alias>
          <ordinal>43</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>date</local-type>
          <aggregation>Year</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>IS_ACTIVE_SRC</remote-name>
          <remote-type>11</remote-type>
          <local-name>[IS_ACTIVE_SRC]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>IS_ACTIVE_SRC</remote-alias>
          <ordinal>44</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>IS_COMPETITOR</remote-name>
          <remote-type>11</remote-type>
          <local-name>[IS_COMPETITOR]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>IS_COMPETITOR</remote-alias>
          <ordinal>45</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>IS_DELETED_SRC</remote-name>
          <remote-type>11</remote-type>
          <local-name>[IS_DELETED_SRC]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>IS_DELETED_SRC</remote-alias>
          <ordinal>46</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_CUSTOMER_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_CUSTOMER_KEY]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
          <ordinal>47</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_COUNTRY_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_COUNTRY_KEY]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</parent-name>
          <remote-alias>DIM_COUNTRY_KEY</remote-alias>
          <ordinal>48</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SCENARIO_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]</local-name>
          <parent-name>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</parent-name>
          <remote-alias>SCENARIO_UID</remote-alias>
          <ordinal>49</ordinal>
          <family>VW_DIM_SCENARIO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>221</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SCENARIO_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SCENARIO_NAME]</local-name>
          <parent-name>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</parent-name>
          <remote-alias>SCENARIO_NAME</remote-alias>
          <ordinal>50</ordinal>
          <family>VW_DIM_SCENARIO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>183</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SCENARIO_DESCRIPTION</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SCENARIO_DESCRIPTION]</local-name>
          <parent-name>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</parent-name>
          <remote-alias>SCENARIO_DESCRIPTION</remote-alias>
          <ordinal>51</ordinal>
          <family>VW_DIM_SCENARIO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>155</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>IS_PUBLISHED</remote-name>
          <remote-type>11</remote-type>
          <local-name>[IS_PUBLISHED]</local-name>
          <parent-name>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</parent-name>
          <remote-alias>IS_PUBLISHED</remote-alias>
          <ordinal>52</ordinal>
          <family>VW_DIM_SCENARIO_RPT</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>LAST_DCO_RUN_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[LAST_DCO_RUN_UID]</local-name>
          <parent-name>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</parent-name>
          <remote-alias>LAST_DCO_RUN_UID</remote-alias>
          <ordinal>53</ordinal>
          <family>VW_DIM_SCENARIO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>12</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>LAST_DCO_RUN_DATE</remote-name>
          <remote-type>133</remote-type>
          <local-name>[LAST_DCO_RUN_DATE]</local-name>
          <parent-name>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</parent-name>
          <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
          <ordinal>54</ordinal>
          <family>VW_DIM_SCENARIO_RPT</family>
          <local-type>date</local-type>
          <aggregation>Year</aggregation>
          <approx-count>6</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_SEGMENT_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_SEGMENT_KEY]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</parent-name>
          <remote-alias>DIM_SEGMENT_KEY</remote-alias>
          <ordinal>55</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>10</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SEGMENT_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SEGMENT_NAME]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</parent-name>
          <remote-alias>SEGMENT_NAME</remote-alias>
          <ordinal>56</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>10</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SEGMENT_TYPE</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SEGMENT_TYPE]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</parent-name>
          <remote-alias>SEGMENT_TYPE</remote-alias>
          <ordinal>57</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_PRODUCT_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</parent-name>
          <remote-alias>DIM_PRODUCT_KEY</remote-alias>
          <ordinal>58</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>PRODUCT_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</parent-name>
          <remote-alias>PRODUCT_UID</remote-alias>
          <ordinal>59</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>PRODUCT_ID</remote-name>
          <remote-type>20</remote-type>
          <local-name>[PRODUCT_ID]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</parent-name>
          <remote-alias>PRODUCT_ID</remote-alias>
          <ordinal>60</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CREATED_BY</remote-name>
          <remote-type>129</remote-type>
          <local-name>[CREATED_BY (VW_DIM_SEGMENT_RPT)]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</parent-name>
          <remote-alias>CREATED_BY</remote-alias>
          <ordinal>61</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>3</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CREATED_TS</remote-name>
          <remote-type>135</remote-type>
          <local-name>[CREATED_TS (VW_DIM_SEGMENT_RPT)]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</parent-name>
          <remote-alias>CREATED_TS</remote-alias>
          <ordinal>62</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>datetime</local-type>
          <aggregation>Year</aggregation>
          <approx-count>6</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>UPDATED_TS</remote-name>
          <remote-type>135</remote-type>
          <local-name>[UPDATED_TS]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</parent-name>
          <remote-alias>UPDATED_TS</remote-alias>
          <ordinal>63</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>datetime</local-type>
          <aggregation>Year</aggregation>
          <approx-count>6</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_SEGMENT_TYPE_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_SEGMENT_TYPE_KEY]</local-name>
          <parent-name>[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]</parent-name>
          <remote-alias>DIM_SEGMENT_TYPE_KEY</remote-alias>
          <ordinal>64</ordinal>
          <family>VW_DIM_SEGMENT_TYPE_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SEGMENT_TYPE</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]</local-name>
          <parent-name>[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]</parent-name>
          <remote-alias>SEGMENT_TYPE</remote-alias>
          <ordinal>65</ordinal>
          <family>VW_DIM_SEGMENT_TYPE_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
        </metadata-record>
      </metadata-records>
    </connection>
  </extract>
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.78979' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.21021' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[none:CHANNEL:nk]' type='palette'>
        <map to='#499894'>
          <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
        </map>
        <map to='#a0cbe8'>
          <bucket>&quot;SEND_CHANNEL&quot;</bucket>
        </map>
        <map to='#d37295'>
          <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_1506454080614776832:nk]' type='palette'>
        <map to='#499894'>
          <bucket>&quot;T3&quot;</bucket>
        </map>
        <map to='#86bcb6'>
          <bucket>&quot;SEND_CHANNEL&quot;</bucket>
        </map>
        <map to='#8cd17d'>
          <bucket>&quot;T2&quot;</bucket>
        </map>
        <map to='#a0cbe8'>
          <bucket>&quot;notier&quot;</bucket>
        </map>
        <map to='#d7b5a6'>
          <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
        </map>
        <map to='#f1ce63'>
          <bucket>&quot;ST&quot;</bucket>
        </map>
        <map to='#ff9d9a'>
          <bucket>&quot;T1&quot;</bucket>
        </map>
        <map to='#ff9d9a'>
          <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:DCO_REASON_TYPE_NAME:nk]' type='palette'>
        <map to='#bab0ac'>
          <bucket>&quot;Reject&quot;</bucket>
        </map>
        <map to='#ff9da7'>
          <bucket>&quot;Recommend&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <datasource-dependencies datasource='Parameters'>
    <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
      <calculation class='tableau' formula='&quot;Channel&quot;' />
      <members>
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
      <calculation class='tableau' formula='&quot;Published&quot;' />
      <members>
        <member value='&quot;(All)&quot;' />
        <member value='&quot;Published&quot;' />
      </members>
    </column>
    <column _.fcp.ParameterDefaultValues.true...source-field='[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model].[INFLUENCE_DISPLAY_NAME]' caption='P_Influence' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account Long-term Importance&quot;'>
      <calculation class='tableau' formula='&quot;Account Long-term Importance&quot;' />
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='VW_DIM_DCO_REASON_RPT' id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_DCO_REASON_RPT' table='[DCO].[VW_DIM_DCO_REASON_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08' table='[Extract].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DCO_RUN_RPT' id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_DCO_RUN_RPT' table='[DCO].[VW_DIM_DCO_RUN_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46' table='[Extract].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DSE_CONFIG' id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_DSE_CONFIG' table='[DCO].[VW_DIM_DSE_CONFIG]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27' table='[Extract].[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_PRODUCT_DCO_RPT' id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_PRODUCT_DCO_RPT' table='[DCO].[VW_DIM_PRODUCT_DCO_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19' table='[Extract].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SCENARIO_RPT' id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_SCENARIO_RPT' table='[DCO].[VW_DIM_SCENARIO_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41' table='[Extract].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SEGMENT_RPT' id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_SEGMENT_RPT' table='[DCO].[VW_DIM_SEGMENT_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' table='[Extract].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SEGMENT_TYPE_RPT' id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='VW_DIM_SEGMENT_TYPE_RPT' table='[DCO].[VW_DIM_SEGMENT_TYPE_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608' table='[Extract].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]' type='table' />
        </properties>
      </object>
      <object caption='VW_F_CIE_SCORING_RPT' id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.14dpxim0p9ga2211k2s8h1nzxbfy' name='Custom SQL Query' type='text'>SELECT *&#13;
FROM &quot;DCO&quot;.&quot;VW_F_CIE_SCORING_RPT_V3&quot;</relation>
        </properties>
        <properties context='extract'>
          <relation name='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' table='[Extract].[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]' type='table' />
        </properties>
      </object>
    </objects>
    <relationships>
      <relationship>
        <expression op='='>
          <expression op='[DCO_REASON_UID]' />
          <expression op='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DCO_RUN_UID (Custom SQL Query)]' />
          <expression op='[DCO_RUN_UID]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SE_CONFIG_ID]' />
          <expression op='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[PRODUCT_UID]' />
          <expression op='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SCENARIO_UID]' />
          <expression op='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[HCP_SEGMENT]' />
          <expression op='[SEGMENT_NAME]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SEGMENT_TYPE]' />
          <expression op='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' />
        </expression>
        <first-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' />
        <second-end-point object-id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608' />
      </relationship>
    </relationships>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
