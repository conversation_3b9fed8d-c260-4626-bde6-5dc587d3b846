import argparse
import os
from posixpath import dirname
import sys

import mysql.connector
from tableauserverclient import models, ConnectionItem, ConnectionCredentials
import tableau_utils
import string

import shutil
import pathlib
import math

app_name = "TABLEAU"
expected_args = [
    {"name": "--customer_name", "default": "novartisfrqa"},
    {"name": "--environment", "default": "prod"},
    {"name": "--site_type", "default": "internal"},
    {"name": "--app_name", "default": app_name},
    {"name": "--ecosystem", "default": "prod"}]

logger = tableau_utils.get_logger(os.path.basename(__file__))
app_name = os.path.basename(sys.argv[0].split('.')[0])

sub_project_name = "DCO"

def get_project_id(name):
    try:
        req_option = TSC.RequestOptions()
        req_option.filter.add(TSC.Filter(TSC.RequestOptions.Field.Name,TSC.RequestOptions.Operator.Equals, name))
        all_project_items, pagination_item = server.projects.get(req_option)
        return all_project_items[0].id
    except Exception as e:
        logger.exception(e)


def get_datasources(project_name, sub_project_id):
    filtered_datasources = []
    page_size = 1000
    page_number = 1
    try:
        req_option = TSC.RequestOptions()
        req_option.pagesize = page_size
        all_datasources, pagination_item = server.datasources.get(req_option)
        no_of_iter = math.ceil(pagination_item.total_available/page_size) - 1
        while no_of_iter != 0:
            page_number +=1
            req_option.pagenumber = page_number
            all_datasources_n, pagination_item = server.datasources.get(req_option)
            all_datasources = all_datasources + all_datasources_n
            no_of_iter -= 1

        for datasource in all_datasources:
            if datasource.project_id == sub_project_id:
                filtered_datasources.append(datasource)

        return filtered_datasources
    except Exception as e:
        logger.exception(e)


def get_sub_project_id(parent_project_id):
    sub_project_id = None
    try:
        req_option = TSC.RequestOptions()
        req_option.filter.add(TSC.Filter(TSC.RequestOptions.Field.Name,TSC.RequestOptions.Operator.Equals, sub_project_name))
        all_project_items, pagination_item = server.projects.get(req_option)
        for project in all_project_items:
            if project.parent_id == parent_project_id:
                sub_project_id = project.id

        return sub_project_id
    except Exception as e:
        logger.exception(e)

def refresh_datasource(datasource_item):
    try:
        server.datasources.refresh(datasource_item)
    except Exception as e:
        logger.exception(e)


if __name__ == "__main__":
    server = None
    error = None
    try:
        parser = argparse.ArgumentParser(description=f'{app_name} arguments')
        for arg in expected_args:
            thetype = arg.get('type')
            parser.add_argument( arg.get('name'), help=arg.get('help'), required=arg.get('required'), default=arg.get('default'), type = thetype if thetype is None else locate(thetype))
        args = parser.parse_args()
        snowflake_tableau_info = tableau_utils.get_snowflake_tableau_connection_info(args.customer_name, args.environment, args.site_type, args.app_name, args.ecosystem, logger)
        assert snowflake_tableau_info is not None, f"\n\tCheck `aktanameta.CustomerTableauSite`, `aktanameta.TableauSite`, `aktanameta.TableauCustomerMap`, `aktanameta.CustomerSnowflakeConfigProperties` entries for customer: '{args.customer_name}',  environment: '{args.environment}', TableauSite type: '{args.site_type}', Snowflake appName '{args.app_name}'"

        (mysql_learningDB, mysql_portforward_rds, mysql_portforward_port, mysql_rds_server, mysql_rds_user, mysql_rds_password,
            account, endpoint, region, warehouse, db, role, snow_username, snow_password, dbregion, dbschema,
            site, url, tableau_username, tableau_password, token_name, token_secret, project, mysql_server, mysql_port,
            mysql_username, mysql_password) = snowflake_tableau_info

        import tableauserverclient as TSC
        if token_name is None or len(token_name) == 0:
            tableau_auth = TSC.TableauAuth(tableau_username, tableau_utils.decrypt_password(tableau_password), site)
            server = TSC.Server(url, use_server_version=True)
            server.auth.sign_in(tableau_auth)
        else:
            tableau_auth = TSC.PersonalAccessTokenAuth(token_name, token_secret, site)
            server = TSC.Server(url, use_server_version=True)
            server.auth.sign_in_with_personal_access_token(tableau_auth)

        project_name = f"{project} {args.environment}".upper()
        project_id = get_project_id(project_name)

        if project_id is None:
            logger.info(f"{project_name} - Project doesn't exists.")

        sub_project_id = get_sub_project_id(project_id)

        if sub_project_id is None:
            logger.info(f"{sub_project_name} - sub project doesn't exists.")

        if sub_project_id is not None:
            datasources = get_datasources(project_name, sub_project_id)
            datasources_refreshed = [refresh_datasource(ds) for ds in datasources]
            logger.info(f"Datasource refresh is initiated for all datasources in {sub_project_name} subfolder of {project_name}")

    except Exception as e:
        logger.exception(e)
        error = str(e)
    finally:
        if server is not None:
            server.auth.sign_out()
        assert error is None, (error)
