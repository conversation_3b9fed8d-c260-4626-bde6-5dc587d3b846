import sys
import logging
from logging import getLogger
import mysql.connector

def get_metadatards_connection_config(ecosystem):
    "Return metadards connection settings"

    return {
        'user':'appadmin',
        'password': 'Uwh27J0Pj31qH2HGn7h48oTm3gC9FS',
        'port': 3306,
        'db': 'aktanameta',
        'host':
            "metadatards.aktana.com" if ecosystem == "prod" else
            "aimqa-metadatards.aktana.com" if ecosystem == "aimqa" else
            "saasdev-metadatards.aktana.com" if ecosystem == "saasdev" else
            ""
    }

def decrypt_password(encrypted, seed = "13975109238571902385710293857192385710239"):
    encrypted = encrypted[10:]
    res = int(encrypted, 16) ^ int(seed)
    return ''.join(chr(a) for a in to_byte_array(res))

def to_byte_array(num):
    bytea = []
    n = num
    while n:
        bytea.append(n % 256)
        n //= 256
    n_bytes = len(bytea)
    if 2 ** (n_bytes * 8 - 1) <= num < 2 ** (n_bytes * 8):
        bytea.append(0)
    return bytearray(reversed(bytea))

def get_logger(name):
    "Get logger for application context"
    logging.basicConfig(
        stream=sys.stdout,
        level=logging.DEBUG,
        format='%(asctime)s %(filename)s:%(funcName)s: %(levelname)s - %(message)s',
        datefmt='%m/%d/%Y %I:%M:%S %p')
    for key in logging.Logger.manager.loggerDict.keys():
        if 'snowflake' in key or 'urllib3' in key or 'azure' in key or 'oauthlib' in key or 's3' in key or 'boto' in key or 'requests' in key:
            logging.getLogger(key).setLevel(logging.FATAL)
            logging.getLogger(key).propagate = False
    return getLogger(name)

def get_files(folder, extension):
    import os
    files=[]
    for root, directories, file in os.walk(folder):
        filter_object = filter(lambda f: f.endswith(extension), file)
        files.extend(list(map(lambda f: os.path.join(root,f), filter_object)))
    return files

def get_snowflake_tableau_connection_info(customer_name, environment, site_type, app_name, ecosystem, logger):
    try:
        config = get_metadatards_connection_config(ecosystem)
        logger.info(config)
        cnx = mysql.connector.connect(**config)
        cursor = cnx.cursor()
        cursor.execute(
"""select ce.learningdbName as mysql_learningDB, ce.rdsForwardUrl as mysql_portforward_rds, ce.rdsForwardPort as mysql_portforward_port,
    ce.rdsServer as mysql_rds_server, ce.rdsUserName as mysql_rds_user, ce.rdsUserPassword as mysql_rds_password,
    cscp.account, cscp.endPoint as endpoint, cscp.region as region, cscp.warehouse, cscp.db, cscp.role,
    cscp.user as snow_username, cscp.password as snow_password, cscp.dbregion, cscp.dbschema,
	ts.name as site, ts.url, ts.username as tableau_username, ts.password  as tableau_password, ts.tokenName as token_name, ts.tokenSecret as token_secret, tcm.customername  as project,
    td.hostname as mysql_server, td.port as mysql_port, td.username as mysql_username, td.password as mysql_password
    from aktanameta.Customer c
    join aktanameta.CustomerEnvironment ce on c.customerId = ce.customerId
    join aktanameta.CustomerSnowflakeConfigProperties cscp on c.customerId = cscp.customerId and ce.envName = cscp.envName
    JOIN aktanameta.CustomerTableauSite cts on c.customerId  = cts.customerid
    join aktanameta.TableauSite ts on cts.tableausiteid = ts.id
    join aktanameta.TableauCustomerMap tcm on tcm.customerid = c.customerId
    join aktanameta.TableauDatasource td on td.customerid = c.customerId
    where c.customerName = %s and ce.envName = %s and cts.`type` = %s and appName = %s""", (customer_name, environment, site_type, app_name))
        return cursor.fetchone()
    except Exception as e:
        logger.exception(str(e))
        raise e
