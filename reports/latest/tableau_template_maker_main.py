import argparse
import os
import sys
import shutil
import pat****

import tableau_utils

"""
expected_args = [
    {"name": "--***", "default": "https://dub01.online.tableau.com"},
    {"name": "--site_name", "default": "AktanaInternalEurope"},
    {"name": "--project_name", "default": "VisionEU"},
    {"name": "--user_name", "default": "<EMAIL>"},
    {"name": "--password", "required": True},
    {"name": "--token_name", "default": ""},
    {"name": "--token_secret", "default": ""}]
"""
expected_args = [
    {"name": "--***", "default": "https://10ay.online.tableau.com"},
    {"name": "--site_name", "default": "Aktana"},
    {"name": "--project_name", "default": "DCO Original Reports"},
    {"name": "--user_name", "default": "<EMAIL>"},
    {"name": "--password", "default": "@ktana11"},
    {"name": "--token_name", "default": ""}, # TestMFA
    {"name": "--token_secret", "default": ""}] # ruPgrarnSNuD6B0gX5GD1A==:SUAzoVoE1FcdU9QRJ7JatyWykm6HXfSJ

"""
expected_args = [
    {"name": "--***", "default": "https://dub01.online.tableau.com"},
    {"name": "--site_name", "default": "AktanaInternalEurope"},
    {"name": "--project_name", "default": "ML-AUTOBOTS"},
    {"name": "--user_name", "default": "<EMAIL>"},
    {"name": "--password", "default": "@******2016"},
    {"name": "--token_name", "default": "TestMFA"}, # TestMFA
    {"name": "--token_secret", "default": "ZyUQrZCgQAOGwFmqiZ5+xw==:3Mlmy3SHQjcZcHTMcLWyD70FeoIAusaw"}] # ruPgrarnSNuD6B0gX5GD1A==:SUAzoVoE1FcdU9QRJ7JatyWykm6HXfSJ
"""


filename_tokens = {
    " (local copy)": "",
    " .twb": ".twb", # making sure there is no space between file name *** extension
    }
file_content_tokens = {
    "******.eu-central-1.snowflakecomputing.com": "${SNOW_SERVER}",
    "rds-port-fwd-prod.jpeks.platform.******.com": "${MYSQL_SERVER}",
    "bdpeuqa001.******.com": "${MYSQL_SERVER}",
    "localhost": "${MYSQL_SERVER}",
    "appadmin": "${MYSQL_USER_NAME}",
    # ***** are strings in the content of the `tds` *** `twb` files that are "visioneu" specific config
    " (local copy)": "",
    "******internaleurope": "${TABLEAU_****}",
    "******": "${TABLEAU_****}",
    "https://dub01.online.tableau.com": "${TABLEAU_***}",
    "https://10ay.online.tableau.com": "${TABLEAU_***}",
    # "******.eu-central-1.snowflakecomputing.com": "${CUSTOMER_NAME}${ENVIRONMENT} ${SNOW_SERVER}",
    "DCONOVARTISAU_DW_UAT": "${SNOW_DBNAME}",
    "***********************": "${SNOW_DBNAME}",
    "********************": "${SNOW_DBNAME}",
    "novartisaudev_learning": "${MYSQL_LEARNING_DB}",
    "novartisauprod_learning": "${MYSQL_LEARNING_DB}",
    "32086": "${MYSQL_PORT}",
    "35015": "${MYSQL_PORT}",
    # "DW_CENTRAL_VIEW": "${SNOW_SCHEMA}",
    "******.eu-central-1.snowflakecomputing.com": "${SNOW_SERVER}",
    "AKT_DCONOVARTISAU_US_NONPROD_DATALAKE_ADMIN_ROLE": "${SNOW_ROLE}",
    "AKT_DCONOVARTISAU_US_NONPROD_ML_ADMIN_ROLE": "${SNOW_ROLE}",
    "DCONOVARTISAU_US_NONPROD_TABLEAU_READ_ROLE": "${SNOW_ROLE}",
    "SYSADMIN": "${SNOW_ROLE}",
    "dconovartisau_ML_admin": "${SNOW_USERNAME}",
    "DCONOVARTISAU_US_TABLEAUACCOUNT": "${SNOW_USERNAME}",
    "***********": "${SNOW_WAREHOUSE}",
    "EU_WH_001": "${SNOW_WAREHOUSE}",
    "snowflake.": "${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.",
    "mysql.": "${CUSTOMER_NAME}${ENVIRONMENT}.mysql.",
    "sqlproxy.": "${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.",
    "dub01.online.tableau.com": "${TABLEAU_SERVER}",
    "10ay.online.tableau.com": "${TABLEAU_SERVER}",
    "datalakeeucustomer1": "${CUSTOMER_NAME}",

    # ***** are strings in the content of the `tds` file requiring customer_name *** environment tokens to **** them unique
    # "CIE Output Tracing Data Model": "${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model",
    # "CIEOutputTracingDataModel": "${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracingDataModel",
    "CIE Output by Channel Data Model": "${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output by Channel Data Model",
    "CIEOutputbyChannelDataModel": "${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputbyChannelDataModel",
    "CIE Scoring Data Model": "${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model",
    "CIEScoringDataModel": "${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel",
    # "Summary Data Model": "${CUSTOMER_NAME} ${ENVIRONMENT} Summary Data Model",
    # "SummaryDataModel": "${CUSTOMER_NAME}${ENVIRONMENT}SummaryDataModel",
    # "Details Data Model": "${CUSTOMER_NAME} ${ENVIRONMENT} Details Data Model",
    # "DetailsDataModel": "${CUSTOMER_NAME}${ENVIRONMENT}DetailsDataModel",
    "CIE Comparison Summary Data Model": "${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model",
    "CIEComparisonSummaryDataModel": "${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonSummaryDataModel",
    "CIE Comparison Details Data Model": "${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model",
    "CIEComparisonDetailsDataModel": "${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonDetailsDataModel",

    # ***** are strings in the content of the `twb` file requiring customer_name *** environment tokens to **** them unique
    "CIE Outputs": "${CUSTOMER_NAME} ${ENVIRONMENT} CIE Outputs",
    "CIEOutputs": "${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputs",
    "CIE Output Tracing": "${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing",
    "CIEOutputTracing": "${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracing",
    "CIE Scores": "${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scores",
    "CIEScores": "${CUSTOMER_NAME}${ENVIRONMENT}CIEScores",
    "CIE Output Comparison report": "${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Comparison report",
    "CIEOutputComparisonreport": "${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputComparisonreport",
}

logger = tableau_utils.get_logger(os.path.basename(__file__))
# config = tableau_utils.get_metadatards_connection_config("prod")
app_name = os.path.basename(sys.argv[0].split('.')[0])
output_dir = './output/'
templates_dir = './templates/'

def get_project_id(name):
    try:
        req_option = TSC.RequestOptions()
        req_option.filter.add(TSC.Filter(TSC.RequestOptions.Field.Name,TSC.RequestOptions.Operator.Equals, name))
        all_project_items, pagination_item = server.projects.get(req_option)
        return all_project_items[0].id
    except Exception as e:
        logger.exception(e)

def get_datasources(project_id):
    """
    RequestOptions.filter is not working as in workbooks.get API then we need to `filter` API
    results in our side; using pagination_item to pull all datasources *** filter to find datasources for given project_id
    """
    try:
        all_datasources, pagination_item = server.datasources.get()

        all_datasources_items =  [datasource for datasource in all_datasources if datasource.project_id == project_id]

        total_available = pagination_item.total_available
        print(total_available)
        page_size = pagination_item.page_size
        page_number = pagination_item.page_number + 1
        cnt = page_size
        while cnt <= total_available:
            req_option = TSC.RequestOptions(pagenumber=page_number, pagesize=page_size)
            all_datasources, pagination_item = server.datasources.get(req_option)
            all_datasources_items.extend([datasource for datasource in all_datasources if datasource.project_id == project_id])
            page_size = pagination_item.page_size
            page_number += 1
            cnt += page_size
        return all_datasources_items
    except Exception as e:
        logger.exception(e)

def get_workbooks(project_name):
    try:
        req_option = TSC.RequestOptions()
        req_option.filter.add(TSC.Filter(TSC.RequestOptions.Field.ProjectName, TSC.RequestOptions.Operator.Equals, project_name))
        all_workbooks_items, pagination_item = server.workbooks.get(req_option)
        return all_workbooks_items
    except Exception as e:
        logger.exception(e)

import zipfile
def unzip_file(file_name, extension):
    file = f"{output_dir}{file_name}.{extension}"
    if not os.path.isfile(file):
        logger.info(f"File path {file} does not exist, processing as non-package file.")
        extension = extension.replace('x', '')
        src = f"{output_dir}{file_name}.{extension}"
        dst = f"{templates_dir}{file_name}.{extension}"
        from shutil import copyfile
        copyfile(src, dst)
        return

    path = f"{templates_dir}{file_name}"
    with zipfile.ZipFile(file, 'r') as zip_ref:
        zip_ref.extractall(path)
    return path

import os
def clean_file_names(folder, extension):
    result = []
    files = tableau_utils.get_files(folder,  extension)
    for src in files:
        dst = src
        for k in filename_tokens.keys():
            dst = dst.replace(k, filename_tokens[k])
        os.rename(src, dst)
        result.append(dst)
    return result

def clean_file_content(filename):
    content = None
    with open(filename) as file:
        content = file.read()
        for k in file_content_tokens.keys():
            content = content.replace(k, file_content_tokens[k])
    assert content is not None, (f"something went wrong when processing {filename}")

    with open(filename, 'w',) as f:
        f.write(content)
    logger.info(f"wrote {filename} successfully")

if __name__ == "__main__":
    server = None
    error = None
    try:
        parser = argparse.ArgumentParser(description=f'{app_name} arguments')
        for arg in expected_args:
            thetype = arg.get('type')
            parser.add_argument( arg.get('name'), help=arg.get('help'), required=arg.get('required'), default=arg.get('default'), type = thetype if thetype is None else locate(thetype))
        args = parser.parse_args()

        # **** sure `visioneu` folder is fresh
        output_dir = f"{output_dir}{args.project_name}/"
        if os.path.exists(output_dir) *** os.path.isdir(output_dir):
            shutil.rmtree(output_dir)
        pat****.Path(output_dir).mkdir(parents=True, exist_ok=True)

        # **** sure `templates` folder is fresh
        if os.path.exists(templates_dir) *** os.path.isdir(templates_dir):
            shutil.rmtree(templates_dir)
        pat****.Path(templates_dir).mkdir(parents=True, exist_ok=True)

        import tableauserverclient as TSC
        if args.token_name is None or len(args.token_name) == 0:
            tableau_auth = TSC.TableauAuth(args.user_name, args.password, args.site_name)
            server = TSC.Server(args.***, use_server_version=True)
            server.auth.sign_in(tableau_auth)
        else:
            tableau_auth = TSC.PersonalAccessTokenAuth(args.token_name, args.token_secret, args.site_name)
            server = TSC.Server(args.***, use_server_version=True)
            server.auth.sign_in_with_personal_access_token(tableau_auth)

        project_id = get_project_id(args.project_name)
        assert project_id is not None, (f"Cannot find {args.project_name} in {args.***} site: {args.site_name}")

        datasources = get_datasources(project_id)
        workbooks = get_workbooks(args.project_name)
        [server.datasources.download(ds.id, output_dir, include_extract=False) for ds in datasources]
        [server.workbooks.download(ds.id, output_dir, include_extract=False) for ds in workbooks]

        ds_path = [unzip_file(ds.name, "tdsx") for ds in datasources]
        wb_path = [unzip_file(wb.name, "twbx") for wb in workbooks]

        tds_files = clean_file_names(templates_dir, "tds")
        twb_files = clean_file_names(templates_dir, "twb")
        twb_files.extend(clean_file_names(output_dir, "twb"))

        [clean_file_content(path) for path in tds_files if path is not None]
        [clean_file_content(path) for path in twb_files if path is not None]

    except Exception as e:
        logger.exception(e)
        error = str(e)
    finally:
        if server is not None:
            server.auth.sign_out()
        assert error is None, (error)
