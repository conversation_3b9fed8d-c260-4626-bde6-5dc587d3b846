**Creating Templates of Tableau Workbooks and Datasources**
* This tool learning/reports/latest/tableau_template_maker_main.py helps you to generate Templates of datasources and workbooks so we can later publish them for every customer/environment; before running the tool you need to develop the workbook and datasources and publish them in a Tableau site's folder

1. Make sure you have access to Tableau site where you published your datasources and workbooks for development  and unit testing
2. Make sure you have latest code of **learning** in your development environment
3. Verify all values defined in **Line 17** of file tableau_template_maker_main.py are valid and adjust if required
3. Execute this utility and provide required arguments (open the script and get familiar with the functionality)

```
pip3 install tableauserverclient==0.17.0
pip3 install mysql-connector-python==8.0.28
cd learning/reports/latest
python3 tableau_template_maker_main.py --url https_tableau_url --site_name tableau_site --project_name tableau_project_name --user_name tableau_username --password password_to_access_tableau
```

4. The command generates output in this directory learning/reports/latest/templates
5. You MUST unit test the templates can be publish without errors (if errors show up fix them) by running this utility (open the script and get familiar with the functionality)

```
# Update **Line 7** object in learning/reports/latest/tableau_utils.py so you can connect to metadatards.aktana.com from your development environment
python3 tableau_publisher_main.py --customer real_customer_name --environment dev_uat_environment --site_type internal --hide_tableau_project No --app_name TABLEAU
```

6. Make sure to commit  **learning/reports/latest/templates** in the learning repository

**Publishing Tableau Workbooks and Datasources dev and qa notes**
* In tableau there **internal** and **customer** sites; **internal** exist for testing purposes whereas **customer** sites are customer facing sites
* Tableau talks to snowflake warehouse specifying a role that will determine what data will be visible in the report; role names follows this pattern **$CUSTOMER_$REGION_$ENVIRONMENT_TABLEAU_READ_ROLE**; **This is a pre-requiste for publishing Tableau datasources and workbooks**
* Snowflake table `$regiondb.Entitlement` **MUST** have an entry for this role; if missing Tableau views will not display any data

**Publishing Tableau Workbooks and Datasources Pre-requisites**
1. How to set up Tableau Online Site for new customers https://aktana.atlassian.net/wiki/spaces/MAIN/pages/566689856/How+to+set+up+Tableau+Online+Site+for+new+customers
2. Tableau metadata setup as described in https://aktana.atlassian.net/wiki/spaces/MAIN/pages/*********/Tableau+Metadata

**Publishing Tableau Workbooks and Datasources Snowflake Pre-requisites**
1. https://aktana.atlassian.net/wiki/spaces/ACH/pages/*********/Snowflake+Infrastructure+users+roles+setup
2. entry setup in table `aktanameta.CustomerSnowflakeConfigProperties` for appName **REPORTS**; and there must role matching this template name **$CUSTOMER_$REGION_$ENVIRONMENT_TABLEAU_READ_ROLE**

* This query must return data
```
select
	cscp.account, cscp.endPoint as endpoint, cscp.region as region, cscp.warehouse, cscp.db, cscp.role,
    cscp.user as snow_username, cscp.password as snow_password, cscp.dbregion, cscp.dbschema,
	ts.name as site, ts.url, ts.username as tableau_username, ts.password  as tableau_password, tcm.customername  as project
from aktanameta.Customer c
    join aktanameta.CustomerEnvironment ce on c.customerId = ce.customerId
    join aktanameta.CustomerSnowflakeConfigProperties cscp on c.customerId = cscp.customerId and ce.envName = cscp.envName
    JOIN aktanameta.CustomerTableauSite cts on c.customerId  = cts.customerid
    join aktanameta.TableauSite ts on cts.tableausiteid = ts.id
    join aktanameta.TableauCustomerMap tcm on tcm.customerid = c.customerId
where c.customerName = 'novartisfrqa' and ce.envName ='prod' and cts.`type` ='internal' and appName = 'REPORTS'```
