<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20231.23.0310.1045                               -->
<workbook locale='en_US' original-version='18.1' source-build='2021.4.16 (20214.23.0209.1538)' source-platform='mac' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.AccessibleZoneTabOrder.true...AccessibleZoneTabOrder />
    <_.fcp.AnimationOnByDefault.true...AnimationOnByDefault />
    <AutoCreateAndUpdateDSDPhoneLayouts />
    <ISO8601DefaultCalendarPref />
    <_.fcp.MarkAnimation.true...MarkAnimation />
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <SetMembershipControl />
    <SheetIdentifierTracking />
    <SortTagCleanup />
    <WindowsPersistSimpleIdentifiers />
    <ZoneBackgroundTransparency />
    <ZoneFriendlyName />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputComparisonreport?rev=1.0' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputComparisonreport' path='/t/${TABLEAU_SITE}/workbooks' revision='1.0' site='${TABLEAU_SITE}' />
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <_.fcp.AnimationOnByDefault.false...style>
    <_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule element='animation'>
      <_.fcp.AnimationOnByDefault.false...format attr='animation-on' value='ao-on' />
    </_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule>
  </_.fcp.AnimationOnByDefault.false...style>
  <datasources>
    <datasource hasconnection='false' inline='true' name='Parameters' version='18.1'>
      <aliases enabled='yes' />
      <column alias='Engine output' caption='Source 1' datatype='string' name='[Choose data sources (copy)_2155535403020820482]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3&quot;'>
        <calculation class='tableau' formula='&quot;DCO 3&quot;' />
        <aliases>
          <alias key='&quot;DCO 3&quot;' value='Engine output' />
          <alias key='&quot;DCO 4&quot;' value='Post-proc output' />
          <alias key='&quot;DSE 1&quot;' value='Traditional' />
          <alias key='&quot;DSE 2&quot;' value='Next Gen' />
        </aliases>
        <members>
          <member alias='Traditional' value='&quot;DSE 1&quot;' />
          <member alias='Next Gen' value='&quot;DSE 2&quot;' />
          <member alias='Engine output' value='&quot;DCO 3&quot;' />
          <member alias='Post-proc output' value='&quot;DCO 4&quot;' />
        </members>
      </column>
      <column caption='Level 2' datatype='string' name='[Level 1 (copy)_526921184321613848]' param-domain-type='list' role='measure' type='nominal' value='&quot;Segment&quot;'>
        <calculation class='tableau' formula='&quot;Segment&quot;' />
        <members>
          <member value='&quot;Account&quot;' />
          <member value='&quot;Channel&quot;' />
          <member value='&quot;Factor&quot;' />
          <member value='&quot;Product&quot;' />
          <member value='&quot;Rep&quot;' />
          <member value='&quot;Suggested Date&quot;' />
          <member value='&quot;Segment&quot;' />
        </members>
      </column>
      <column caption='Level 3' datatype='string' name='[Level 2 (copy)_526921184321875994]' param-domain-type='list' role='measure' type='nominal' value='&quot;Product&quot;'>
        <calculation class='tableau' formula='&quot;Product&quot;' />
        <members>
          <member value='&quot;Account&quot;' />
          <member value='&quot;Channel&quot;' />
          <member value='&quot;Factor&quot;' />
          <member value='&quot;Product&quot;' />
          <member value='&quot;Rep&quot;' />
          <member value='&quot;Suggested Date&quot;' />
          <member value='&quot;Segment&quot;' />
        </members>
      </column>
      <column caption='Level 5' datatype='string' name='[Level 3 (copy) (copy)_526921184321937436]' param-domain-type='list' role='measure' type='nominal' value='&quot;Rep&quot;'>
        <calculation class='tableau' formula='&quot;Rep&quot;' />
        <members>
          <member value='&quot;Account&quot;' />
          <member value='&quot;Channel&quot;' />
          <member value='&quot;Factor&quot;' />
          <member value='&quot;Product&quot;' />
          <member value='&quot;Rep&quot;' />
          <member value='&quot;Suggested Date&quot;' />
          <member value='&quot;Segment&quot;' />
        </members>
      </column>
      <column caption='Level 4' datatype='string' name='[Level 3 (copy)_526921184321929243]' param-domain-type='list' role='measure' type='nominal' value='&quot;Factor&quot;'>
        <calculation class='tableau' formula='&quot;Factor&quot;' />
        <members>
          <member value='&quot;Account&quot;' />
          <member value='&quot;Channel&quot;' />
          <member value='&quot;Factor&quot;' />
          <member value='&quot;Product&quot;' />
          <member value='&quot;Rep&quot;' />
          <member value='&quot;Suggested Date&quot;' />
          <member value='&quot;Segment&quot;' />
        </members>
      </column>
      <column caption='Level 6' datatype='string' name='[Level 5 (copy)_526921184321998877]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account&quot;'>
        <calculation class='tableau' formula='&quot;Account&quot;' />
        <members>
          <member value='&quot;Account&quot;' />
          <member value='&quot;Channel&quot;' />
          <member value='&quot;Factor&quot;' />
          <member value='&quot;Product&quot;' />
          <member value='&quot;Rep&quot;' />
          <member value='&quot;Suggested Date&quot;' />
          <member value='&quot;Segment&quot;' />
        </members>
      </column>
      <column caption='Level 7' datatype='string' name='[Level 6 (copy)_2155535403012243456]' param-domain-type='list' role='measure' type='nominal' value='&quot;Suggested Date&quot;'>
        <calculation class='tableau' formula='&quot;Suggested Date&quot;' />
        <members>
          <member value='&quot;Account&quot;' />
          <member value='&quot;Channel&quot;' />
          <member value='&quot;Factor&quot;' />
          <member value='&quot;Product&quot;' />
          <member value='&quot;Rep&quot;' />
          <member value='&quot;Suggested Date&quot;' />
          <member value='&quot;Segment&quot;' />
        </members>
      </column>
      <column caption='Level 1' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
        <calculation class='tableau' formula='&quot;Channel&quot;' />
        <members>
          <member value='&quot;Account&quot;' />
          <member value='&quot;Channel&quot;' />
          <member value='&quot;Factor&quot;' />
          <member value='&quot;Product&quot;' />
          <member value='&quot;Rep&quot;' />
          <member value='&quot;Suggested Date&quot;' />
          <member value='&quot;Segment&quot;' />
        </members>
      </column>
      <column alias='Engine output' caption='Source 2' datatype='string' name='[Source 1 (copy)_2155535403*********]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3 2&quot;'>
        <calculation class='tableau' formula='&quot;DCO 3 2&quot;' />
        <aliases>
          <alias key='&quot;DCO 3 2&quot;' value='Engine output' />
          <alias key='&quot;DCO 4 2&quot;' value='Post-proc output' />
          <alias key='&quot;DSE 1 2&quot;' value='Traditional' />
          <alias key='&quot;DSE 2 2&quot;' value='Next Gen' />
        </aliases>
        <members>
          <member alias='Traditional' value='&quot;DSE 1 2&quot;' />
          <member alias='Next Gen' value='&quot;DSE 2 2&quot;' />
          <member alias='Engine output' value='&quot;DCO 3 2&quot;' />
          <member alias='Post-proc output' value='&quot;DCO 4 2&quot;' />
        </members>
      </column>
    </datasource>
    <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model' inline='true' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y' version='18.1'>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonSummaryDataModel?rev=1.0' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonSummaryDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.0' site='${TABLEAU_SITE}' />
      <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonSummaryDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <calculations>
          <calculation column='[Average Suggestion Per Rep  (copy)_526921183993229317]' formula='ROUND(SUM([SUGGESTIONCOUNT])/SUM([REPCOUNT]), 2)' />
          <calculation column='[Calculation_1439181579318042630]' formula='&apos;Difference&apos;' />
          <calculation column='[Config (1) (copy)_2155535403100246044]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; THEN [DCO_RUN_DATE1]&#13;&#10;END' />
          <calculation column='[Config (copy)_2155535403097620507]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME1]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
          <calculation column='[Config Filter (copy)_2155535403107680289]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME2]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
          <calculation column='[DIFF 1 Factors fired Count (copy)_2155535403060961294]' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([FACTORCOUNT]) END)&#13;&#10;&#13;&#10;-&#13;&#10;&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([FACTORCOUNT]) END)' />
          <calculation column='[DIFF 1 Suggested Account Count (copy)_2155535403082145812]' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([ACCOUNTCOUNT]) END)' />
          <calculation column='[DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101]' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCHANNELCOUNT])END)  &#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([ACCOUNTCHANNELCOUNT])END)' />
          <calculation column='[DIFF 1 Suggested Count (copy)_2155535403081334802]' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)' />
          <calculation column='[DIFF 1 Suggested Rep Count (copy)_2155535403081949203]' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([REPCOUNT]) END)' />
          <calculation column='[DIFF Average Suggestion Per Rep (copy)_122160172807217152]' formula='ROUND( SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10; /&#13;&#10;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END), 2)&#13;&#10;&#13;&#10;-&#13;&#10;&#13;&#10;ROUND(SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10; /&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([REPCOUNT]) END)&#13;&#10;, 2)' />
          <calculation column='[Run Date (1) (copy)_2155535403100545053]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = [FLAG] THEN [SCENARIO_NAME1 (copy)_1347983701206401024]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
          <calculation column='[Run Date Filter (copy)_21555354031********]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos; THEN [DCO_RUN_DATE2]&#13;&#10;END' />
          <calculation column='[SCENARIO_NAME1 (copy)_1347983701206401024]' formula='[SCENARIO_NAME1]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
          <calculation column='[SCENARIO_NAME2 (copy)_1347983701206581249]' formula='[SCENARIO_NAME2]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
          <calculation column='[Scenario Filter (copy)_2155535403107311647]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO_NAME2 (copy)_1347983701206581249]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
        </calculations>
        <metadata-records>
          <metadata-record class='measure'>
            <remote-name>ACCOUNTCHANNELCOUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[ACCOUNTCHANNELCOUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACCOUNTCHANNELCOUNT</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>ACCOUNTCOUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[ACCOUNTCOUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACCOUNTCOUNT</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Average Suggestion Per Rep  (copy)_526921183993229317</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Average Suggestion Per Rep  (copy)_526921183993229317]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Average Suggestion Per Rep  (copy)_526921183993229317</remote-alias>
            <ordinal>17</ordinal>
            <layered>true</layered>
            <caption>AVERAGE Suggestion Per REP</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;ROUND(SUM([SUGGESTIONCOUNT])/SUM([REPCOUNT]), 2)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1439181579318042630</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1439181579318042630]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1439181579318042630</remote-alias>
            <ordinal>18</ordinal>
            <layered>true</layered>
            <caption>Difference</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;&apos;Difference&apos;&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Config (1) (copy)_2155535403100246044</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Config (1) (copy)_2155535403100246044]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Config (1) (copy)_2155535403100246044</remote-alias>
            <ordinal>19</ordinal>
            <layered>true</layered>
            <caption>Run Date Filter 1</caption>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; THEN [DCO_RUN_DATE1]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Config (copy)_2155535403097620507</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Config (copy)_2155535403097620507]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Config (copy)_2155535403097620507</remote-alias>
            <ordinal>20</ordinal>
            <layered>true</layered>
            <caption>Config Filter 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME1]
ELSE ' <Difference>'
END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Config Filter (copy)_2155535403107680289</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Config Filter (copy)_2155535403107680289]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Config Filter (copy)_2155535403107680289</remote-alias>
            <ordinal>21</ordinal>
            <layered>true</layered>
            <caption>Config Filter 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME2]
ELSE ' <Difference>'
END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_DATE1</remote-name>
            <remote-type>133</remote-type>
            <local-name>[DCO_RUN_DATE1]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_DATE1</remote-alias>
            <ordinal>10</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_DATE2</remote-name>
            <remote-type>133</remote-type>
            <local-name>[DCO_RUN_DATE2]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_DATE2</remote-alias>
            <ordinal>11</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DIFF 1 Factors fired Count (copy)_2155535403060961294</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[DIFF 1 Factors fired Count (copy)_2155535403060961294]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIFF 1 Factors fired Count (copy)_2155535403060961294</remote-alias>
            <ordinal>23</ordinal>
            <layered>true</layered>
            <caption>DIFF Factors fired Count</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([FACTORCOUNT]) END)&#13;
&#13;
-&#13;
&#13;
SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([FACTORCOUNT]) END)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DIFF 1 Suggested Account Count (copy)_2155535403082145812</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[DIFF 1 Suggested Account Count (copy)_2155535403082145812]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIFF 1 Suggested Account Count (copy)_2155535403082145812</remote-alias>
            <ordinal>24</ordinal>
            <layered>true</layered>
            <caption>DIFF Suggested Account Count</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCOUNT]) END)&#13;
-&#13;
SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([ACCOUNTCOUNT]) END)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101</remote-alias>
            <ordinal>25</ordinal>
            <layered>true</layered>
            <caption>DIFF Suggested Account/Channel Count</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCHANNELCOUNT])END)  &#13;
-&#13;
SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([ACCOUNTCHANNELCOUNT])END)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DIFF 1 Suggested Count (copy)_2155535403081334802</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[DIFF 1 Suggested Count (copy)_2155535403081334802]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIFF 1 Suggested Count (copy)_2155535403081334802</remote-alias>
            <ordinal>26</ordinal>
            <layered>true</layered>
            <caption>DIFF Suggested Count</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&#13;
-&#13;
SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DIFF 1 Suggested Rep Count (copy)_2155535403081949203</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[DIFF 1 Suggested Rep Count (copy)_2155535403081949203]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIFF 1 Suggested Rep Count (copy)_2155535403081949203</remote-alias>
            <ordinal>27</ordinal>
            <layered>true</layered>
            <caption>DIFF Suggested Rep Count</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END)&#13;
-&#13;
SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([REPCOUNT]) END)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>DIFF Average Suggestion Per Rep (copy)_122160172807217152</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[DIFF Average Suggestion Per Rep (copy)_122160172807217152]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIFF Average Suggestion Per Rep (copy)_122160172807217152</remote-alias>
            <ordinal>28</ordinal>
            <layered>true</layered>
            <caption>DIFF Average Suggestion Per Rep</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;ROUND( SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&#13;
 /&#13;
SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END), 2)&#13;
&#13;
-&#13;
&#13;
ROUND(SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)&#13;
 /&#13;
SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([REPCOUNT]) END)&#13;
, 2)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>FACTORCOUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[FACTORCOUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FACTORCOUNT</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FLAG</remote-name>
            <remote-type>129</remote-type>
            <local-name>[FLAG]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FLAG</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LAST_DCO_RUN_DATE</remote-name>
            <remote-type>133</remote-type>
            <local-name>[LAST_DCO_RUN_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
            <ordinal>12</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>REPCOUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[REPCOUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>REPCOUNT</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Run Date (1) (copy)_2155535403100545053</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Run Date (1) (copy)_2155535403100545053]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Run Date (1) (copy)_2155535403100545053</remote-alias>
            <ordinal>29</ordinal>
            <layered>true</layered>
            <caption>Scenario Filter 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Choose data sources (copy)_2155535403020820482] = [FLAG] THEN [SCENARIO_NAME1 (copy)_1347983701206401024]
ELSE ' <Difference>'
END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Run Date Filter (copy)_21555354031********</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Run Date Filter (copy)_21555354031********]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Run Date Filter (copy)_21555354031********</remote-alias>
            <ordinal>30</ordinal>
            <layered>true</layered>
            <caption>Run Date Filter 2</caption>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos; THEN [DCO_RUN_DATE2]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_NAME1 (copy)_1347983701206401024</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[SCENARIO_NAME1 (copy)_1347983701206401024]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_NAME1 (copy)_1347983701206401024</remote-alias>
            <ordinal>31</ordinal>
            <layered>true</layered>
            <caption>SCENARIO NEW</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[SCENARIO_NAME1]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))
+&apos;/&apos;
+STR(DAY([LAST_DCO_RUN_DATE]))
+&apos;/&apos;
+STR(YEAR([LAST_DCO_RUN_DATE]))
+&apos;)&apos;,&apos;&apos;)&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_NAME1</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_NAME1]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_NAME1</remote-alias>
            <ordinal>7</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_NAME2 (copy)_1347983701206581249</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[SCENARIO_NAME2 (copy)_1347983701206581249]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_NAME2 (copy)_1347983701206581249</remote-alias>
            <ordinal>32</ordinal>
            <layered>true</layered>
            <caption>SCENARIO NEW 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[SCENARIO_NAME2]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))
+&apos;/&apos;
+STR(DAY([LAST_DCO_RUN_DATE]))
+&apos;/&apos;
+STR(YEAR([LAST_DCO_RUN_DATE]))
+&apos;)&apos;,&apos;&apos;)&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_NAME2</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_NAME2]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_NAME2</remote-alias>
            <ordinal>9</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_NAME1</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SE_CONFIG_NAME1]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_NAME1</remote-alias>
            <ordinal>6</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_NAME2</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SE_CONFIG_NAME2]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_NAME2</remote-alias>
            <ordinal>8</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>SUGGESTIONCOUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[SUGGESTIONCOUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTIONCOUNT</remote-alias>
            <ordinal>0</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Scenario Filter (copy)_2155535403107311647</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Scenario Filter (copy)_2155535403107311647]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Scenario Filter (copy)_2155535403107311647</remote-alias>
            <ordinal>37</ordinal>
            <layered>true</layered>
            <caption>Scenario Filter 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO_NAME2 (copy)_1347983701206581249]
ELSE ' <Difference>'
END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_CASE_INSENSITIVE_CONTAINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORT_ANALYTIC_FUNCTIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model' inline='true' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonSummaryDataModel?rev=1.0' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonSummaryDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.0' site='${TABLEAU_SITE}' />
  <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonSummaryDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <cols>
      <map key='[ACCOUNTCHANNELCOUNT]' value='[sqlproxy].[ACCOUNTCHANNELCOUNT]' />
      <map key='[ACCOUNTCOUNT]' value='[sqlproxy].[ACCOUNTCOUNT]' />
      <map key='[Average Suggestion Per Rep  (copy)_526921183993229317]' value='[sqlproxy].[Average Suggestion Per Rep  (copy)_526921183993229317]' />
      <map key='[Calculation_1439181579318042630]' value='[sqlproxy].[Calculation_1439181579318042630]' />
      <map key='[Config (1) (copy)_2155535403100246044]' value='[sqlproxy].[Config (1) (copy)_2155535403100246044]' />
      <map key='[Config (copy)_2155535403097620507]' value='[sqlproxy].[Config (copy)_2155535403097620507]' />
      <map key='[Config Filter (copy)_2155535403107680289]' value='[sqlproxy].[Config Filter (copy)_2155535403107680289]' />
      <map key='[DCO_RUN_DATE1]' value='[sqlproxy].[DCO_RUN_DATE1]' />
      <map key='[DCO_RUN_DATE2]' value='[sqlproxy].[DCO_RUN_DATE2]' />
      <map key='[DIFF 1 Factors fired Count (copy)_2155535403060961294]' value='[sqlproxy].[DIFF 1 Factors fired Count (copy)_2155535403060961294]' />
      <map key='[DIFF 1 Suggested Account Count (copy)_2155535403082145812]' value='[sqlproxy].[DIFF 1 Suggested Account Count (copy)_2155535403082145812]' />
      <map key='[DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101]' value='[sqlproxy].[DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101]' />
      <map key='[DIFF 1 Suggested Count (copy)_2155535403081334802]' value='[sqlproxy].[DIFF 1 Suggested Count (copy)_2155535403081334802]' />
      <map key='[DIFF 1 Suggested Rep Count (copy)_2155535403081949203]' value='[sqlproxy].[DIFF 1 Suggested Rep Count (copy)_2155535403081949203]' />
      <map key='[DIFF Average Suggestion Per Rep (copy)_122160172807217152]' value='[sqlproxy].[DIFF Average Suggestion Per Rep (copy)_122160172807217152]' />
      <map key='[FACTORCOUNT]' value='[sqlproxy].[FACTORCOUNT]' />
      <map key='[FLAG]' value='[sqlproxy].[FLAG]' />
      <map key='[LAST_DCO_RUN_DATE]' value='[sqlproxy].[LAST_DCO_RUN_DATE]' />
      <map key='[REPCOUNT]' value='[sqlproxy].[REPCOUNT]' />
      <map key='[Run Date (1) (copy)_2155535403100545053]' value='[sqlproxy].[Run Date (1) (copy)_2155535403100545053]' />
      <map key='[Run Date Filter (copy)_21555354031********]' value='[sqlproxy].[Run Date Filter (copy)_21555354031********]' />
      <map key='[SCENARIO_NAME1 (copy)_1347983701206401024]' value='[sqlproxy].[SCENARIO_NAME1 (copy)_1347983701206401024]' />
      <map key='[SCENARIO_NAME1]' value='[sqlproxy].[SCENARIO_NAME1]' />
      <map key='[SCENARIO_NAME2 (copy)_1347983701206581249]' value='[sqlproxy].[SCENARIO_NAME2 (copy)_1347983701206581249]' />
      <map key='[SCENARIO_NAME2]' value='[sqlproxy].[SCENARIO_NAME2]' />
      <map key='[SE_CONFIG_NAME1]' value='[sqlproxy].[SE_CONFIG_NAME1]' />
      <map key='[SE_CONFIG_NAME2]' value='[sqlproxy].[SE_CONFIG_NAME2]' />
      <map key='[SUGGESTIONCOUNT]' value='[sqlproxy].[SUGGESTIONCOUNT]' />
      <map key='[Scenario Filter (copy)_2155535403107311647]' value='[sqlproxy].[Scenario Filter (copy)_2155535403107311647]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='Count' datatype='string' default-type='nominal' name='[:Measure Names]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <aliases>
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:ACCOUNTCHANNELCOUNT:qk]&quot;' value='Suggested Account/Channel Count ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:accountChannelCount1:qk]&quot;' value='Suggested Account/Channel Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:ACCOUNTCOUNT:qk]&quot;' value='Suggested Account Count ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:accountCount1:qk]&quot;' value='Suggested Account Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:FACTORCOUNT:qk]&quot;' value='Factors fired Count ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:factorCount1:qk]&quot;' value='Factors fired Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:REPCOUNT:qk]&quot;' value='Suggested Rep Count ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:repCount1:qk]&quot;' value='Suggested Rep Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:SUGGESTIONCOUNT:qk]&quot;' value='Suggestion Count ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:suggestionCount1:qk]&quot;' value='Suggestion Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Average Suggestion Per Rep  (copy)_526921183993229317:qk]&quot;' value='Average Suggestion Per Rep ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703754788887:qk]&quot;' value='Suggestion Count  ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703755186201:qk]&quot;' value='Suggested Rep Count  ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703755206682:qk]&quot;' value='Suggested Account Count  ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703755235355:qk]&quot;' value='Suggested Account/Channel Count  ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703755259932:qk]&quot;' value='Factors fired Count  ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703755272221:qk]&quot;' value='Average Suggestion Per Rep   ' />
    </aliases>
  </column>
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[ACCOUNTCHANNELCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[ACCOUNTCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='User' caption='AVERAGE Suggestion Per REP' datatype='real' default-type='quantitative' name='[Average Suggestion Per Rep  (copy)_526921183993229317]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='ROUND(SUM([SUGGESTIONCOUNT])/SUM([REPCOUNT]), 2)' />
  </column>
  <column aggregation='Count' caption='Difference' datatype='string' default-type='nominal' name='[Calculation_1439181579318042630]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='&apos;Difference&apos;' />
  </column>
  <column aggregation='Year' caption='Run Date Filter 1' datatype='date' default-type='ordinal' name='[Config (1) (copy)_2155535403100246044]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; THEN [DCO_RUN_DATE1]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Config Filter 1' datatype='string' default-type='nominal' name='[Config (copy)_2155535403097620507]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME1]&\#13;&\#10;ELSE &apos; &lt;Difference&gt;&apos;&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Config Filter 2' datatype='string' default-type='nominal' name='[Config Filter (copy)_2155535403107680289]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME2]&\#13;&\#10;ELSE &apos; &lt;Difference&gt;&apos;&\#13;&\#10;END' />
  </column>
  <column aggregation='Year' caption='Run Date' datatype='date' default-type='ordinal' hidden='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='User' caption='DIFF Factors fired Count' datatype='integer' default-type='quantitative' name='[DIFF 1 Factors fired Count (copy)_2155535403060961294]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([FACTORCOUNT]) END)&\#13;&\#10;&\#13;&\#10;-&\#13;&\#10;&\#13;&\#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([FACTORCOUNT]) END)' />
  </column>
  <column aggregation='User' caption='DIFF Suggested Account Count' datatype='integer' default-type='quantitative' name='[DIFF 1 Suggested Account Count (copy)_2155535403082145812]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCOUNT]) END)&\#13;&\#10;-&\#13;&\#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([ACCOUNTCOUNT]) END)' />
  </column>
  <column aggregation='User' caption='DIFF Suggested Account/Channel Count' datatype='integer' default-type='quantitative' name='[DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCHANNELCOUNT])END)  &\#13;&\#10;-&\#13;&\#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([ACCOUNTCHANNELCOUNT])END)' />
  </column>
  <column aggregation='User' caption='DIFF Suggested Count' datatype='integer' default-type='quantitative' name='[DIFF 1 Suggested Count (copy)_2155535403081334802]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&\#13;&\#10;-&\#13;&\#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)' />
  </column>
  <column aggregation='User' caption='DIFF Suggested Rep Count' datatype='integer' default-type='quantitative' name='[DIFF 1 Suggested Rep Count (copy)_2155535403081949203]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END)&\#13;&\#10;-&\#13;&\#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([REPCOUNT]) END)' />
  </column>
  <column aggregation='User' caption='DIFF Average Suggestion Per Rep' datatype='real' default-type='quantitative' name='[DIFF Average Suggestion Per Rep (copy)_122160172807217152]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='ROUND( SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&\#13;&\#10; /&\#13;&\#10;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END), 2)&\#13;&\#10;&\#13;&\#10;-&\#13;&\#10;&\#13;&\#10;ROUND(SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)&\#13;&\#10; /&\#13;&\#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([REPCOUNT]) END)&\#13;&\#10;, 2)' />
  </column>
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[FACTORCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' name='[FLAG]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <aliases>
      <alias key='&quot;DCO 3&quot;' value='Source 1' />
      <alias key='&quot;DCO 3 2&quot;' value='Source 2' />
    </aliases>
  </column>
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[REPCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Filter 1' datatype='string' default-type='nominal' name='[Run Date (1) (copy)_2155535403100545053]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = [FLAG] THEN [SCENARIO_NAME1 (copy)_1347983701206401024]&\#13;&\#10;ELSE &apos; &lt;Difference&gt;&apos;&\#13;&\#10;END' />
  </column>
  <column aggregation='Year' caption='Run Date Filter 2' datatype='date' default-type='ordinal' name='[Run Date Filter (copy)_21555354031********]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos; THEN [DCO_RUN_DATE2]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='SCENARIO NEW' datatype='string' default-type='nominal' name='[SCENARIO_NAME1 (copy)_1347983701206401024]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[SCENARIO_NAME1]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&\#10;+&apos;/&apos;&\#10;+STR(DAY([LAST_DCO_RUN_DATE]))&\#10;+&apos;/&apos;&\#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&\#10;+&apos;)&apos;,&apos;&apos;)' />
  </column>
  <column aggregation='Count' caption='SCENARIO NEW 2' datatype='string' default-type='nominal' name='[SCENARIO_NAME2 (copy)_1347983701206581249]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[SCENARIO_NAME2]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&\#10;+&apos;/&apos;&\#10;+STR(DAY([LAST_DCO_RUN_DATE]))&\#10;+&apos;/&apos;&\#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&\#10;+&apos;)&apos;,&apos;&apos;)' />
  </column>
  <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' hidden='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[SCENARIO_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' hidden='true' name='[SE_CONFIG_ID]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' hidden='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' name='[SUGGESTIONCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Filter 2' datatype='string' default-type='nominal' name='[Scenario Filter (copy)_2155535403107311647]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO_NAME2 (copy)_1347983701206581249]&\#13;&\#10;ELSE &apos; &lt;Difference&gt;&apos;&\#13;&\#10;END' />
  </column>
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='Snowflake Summary' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[_09814B39EDE341E2BFD5D4E067A74E4D]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='Snowflake' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[_3A533CA42284488BB56AA31CE554622E]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='MySQL' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[_3CEE7E99CC7146BA96A53F15A77B5338]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <column aggregation='Sum' datatype='real' default-type='quantitative' hidden='true' name='[enhancedInsightsCount1]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
  <column aggregation='Sum' datatype='real' default-type='quantitative' hidden='true' name='[insightsCount1]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' hidden='true' name='[seConfigId]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column-instance column='[ACCOUNTCHANNELCOUNT]' derivation='Sum' name='[sum:ACCOUNTCHANNELCOUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[ACCOUNTCOUNT]' derivation='Sum' name='[sum:ACCOUNTCOUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[FACTORCOUNT]' derivation='Sum' name='[sum:FACTORCOUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[REPCOUNT]' derivation='Sum' name='[sum:REPCOUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[SUGGESTIONCOUNT]' derivation='Sum' name='[sum:SUGGESTIONCOUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Average Suggestion Per Rep  (copy)_526921183993229317]' derivation='User' name='[usr:Average Suggestion Per Rep  (copy)_526921183993229317:qk]' pivot='key' type='quantitative' />
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-aliased-fields='true' show-hidden-fields='true' show-structure='true' />
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <datasource-dependencies datasource='Parameters'>
    <column alias='Engine output' caption='Source 1' datatype='string' name='[Choose data sources (copy)_2155535403020820482]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3&quot;'>
      <calculation class='tableau' formula='&quot;DCO 3&quot;' />
      <aliases>
        <alias key='&quot;DCO 3&quot;' value='Engine output' />
        <alias key='&quot;DCO 4&quot;' value='Post-proc output' />
        <alias key='&quot;DSE 1&quot;' value='Traditional' />
        <alias key='&quot;DSE 2&quot;' value='Next Gen' />
      </aliases>
      <members>
        <member alias='Traditional' value='&quot;DSE 1&quot;' />
        <member alias='Next Gen' value='&quot;DSE 2&quot;' />
        <member alias='Engine output' value='&quot;DCO 3&quot;' />
        <member alias='Post-proc output' value='&quot;DCO 4&quot;' />
      </members>
    </column>
    <column alias='Engine output' caption='Source 2' datatype='string' name='[Source 1 (copy)_2155535403*********]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3 2&quot;'>
      <calculation class='tableau' formula='&quot;DCO 3 2&quot;' />
      <aliases>
        <alias key='&quot;DCO 3 2&quot;' value='Engine output' />
        <alias key='&quot;DCO 4 2&quot;' value='Post-proc output' />
        <alias key='&quot;DSE 1 2&quot;' value='Traditional' />
        <alias key='&quot;DSE 2 2&quot;' value='Next Gen' />
      </aliases>
      <members>
        <member alias='Traditional' value='&quot;DSE 1 2&quot;' />
        <member alias='Next Gen' value='&quot;DSE 2 2&quot;' />
        <member alias='Engine output' value='&quot;DCO 3 2&quot;' />
        <member alias='Post-proc output' value='&quot;DCO 4 2&quot;' />
      </members>
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='Snowflake Summary' id='_09814B39EDE341E2BFD5D4E067A74E4D'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='hyper' version='0.0.0'>
  </primary-dialect>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>true</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
              <attribute datatype='string' name='update-time'>&quot;3/13/2023 10:33:44 PM&quot;</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='sunday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[:Measure Names]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <aliases>
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:ACCOUNTCHANNELCOUNT:qk]&quot;' value='Suggested Account/Channel Count ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:accountChannelCount1:qk]&quot;' value='Suggested Account/Channel Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:ACCOUNTCOUNT:qk]&quot;' value='Suggested Account Count ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:accountCount1:qk]&quot;' value='Suggested Account Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:FACTORCOUNT:qk]&quot;' value='Factors fired Count ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:factorCount1:qk]&quot;' value='Factors fired Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:REPCOUNT:qk]&quot;' value='Suggested Rep Count ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:repCount1:qk]&quot;' value='Suggested Rep Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:SUGGESTIONCOUNT:qk]&quot;' value='Suggestion Count ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:suggestionCount1:qk]&quot;' value='Suggestion Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:Average Suggestion Per Rep  (copy)_526921183993229317:qk]&quot;' value='Average Suggestion Per Rep ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:Calculation_1347983703754788887:qk]&quot;' value='Suggestion Count  ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:Calculation_1347983703755186201:qk]&quot;' value='Suggested Rep Count  ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:Calculation_1347983703755206682:qk]&quot;' value='Suggested Account Count  ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:Calculation_1347983703755235355:qk]&quot;' value='Suggested Account/Channel Count  ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:Calculation_1347983703755259932:qk]&quot;' value='Factors fired Count  ' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:Calculation_1347983703755272221:qk]&quot;' value='Average Suggestion Per Rep   ' />
        </aliases>
      </column>
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[ACCOUNTCHANNELCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[ACCOUNTCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='User' caption='AVERAGE Suggestion Per REP' datatype='real' default-type='quantitative' layered='true' name='[Average Suggestion Per Rep  (copy)_526921183993229317]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='ROUND(SUM([SUGGESTIONCOUNT])/SUM([REPCOUNT]), 2)' />
      </column>
      <column aggregation='Count' caption='Difference' datatype='string' default-type='nominal' layered='true' name='[Calculation_1439181579318042630]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='&apos;Difference&apos;' />
      </column>
      <column aggregation='Year' caption='Run Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Config (1) (copy)_2155535403100246044]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; THEN [DCO_RUN_DATE1]&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Config Filter 1' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_2155535403097620507]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME1]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Config Filter 2' datatype='string' default-type='nominal' layered='true' name='[Config Filter (copy)_2155535403107680289]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME2]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
      </column>
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE1]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE2]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='User' caption='DIFF Factors fired Count' datatype='integer' default-type='quantitative' layered='true' name='[DIFF 1 Factors fired Count (copy)_2155535403060961294]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([FACTORCOUNT]) END)&#13;&#10;&#13;&#10;-&#13;&#10;&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([FACTORCOUNT]) END)' />
      </column>
      <column aggregation='User' caption='DIFF Suggested Account Count' datatype='integer' default-type='quantitative' layered='true' name='[DIFF 1 Suggested Account Count (copy)_2155535403082145812]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([ACCOUNTCOUNT]) END)' />
      </column>
      <column aggregation='User' caption='DIFF Suggested Account/Channel Count' datatype='integer' default-type='quantitative' layered='true' name='[DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCHANNELCOUNT])END)  &#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([ACCOUNTCHANNELCOUNT])END)' />
      </column>
      <column aggregation='User' caption='DIFF Suggested Count' datatype='integer' default-type='quantitative' layered='true' name='[DIFF 1 Suggested Count (copy)_2155535403081334802]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)' />
      </column>
      <column aggregation='User' caption='DIFF Suggested Rep Count' datatype='integer' default-type='quantitative' layered='true' name='[DIFF 1 Suggested Rep Count (copy)_2155535403081949203]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([REPCOUNT]) END)' />
      </column>
      <column aggregation='User' caption='DIFF Average Suggestion Per Rep' datatype='real' default-type='quantitative' layered='true' name='[DIFF Average Suggestion Per Rep (copy)_122160172807217152]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='ROUND( SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10; /&#13;&#10;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END), 2)&#13;&#10;&#13;&#10;-&#13;&#10;&#13;&#10;ROUND(SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10; /&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([REPCOUNT]) END)&#13;&#10;, 2)' />
      </column>
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[FACTORCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[FLAG]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <aliases>
          <alias key='&quot;DCO 3&quot;' value='Source 1' />
          <alias key='&quot;DCO 3 2&quot;' value='Source 2' />
        </aliases>
      </column>
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[REPCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Filter 1' datatype='string' default-type='nominal' layered='true' name='[Run Date (1) (copy)_2155535403100545053]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = [FLAG] THEN [SCENARIO_NAME1 (copy)_1347983701206401024]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
      </column>
      <column aggregation='Year' caption='Run Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Run Date Filter (copy)_21555354031********]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos; THEN [DCO_RUN_DATE2]&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='SCENARIO NEW' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME1 (copy)_1347983701206401024]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[SCENARIO_NAME1]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
      </column>
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME1]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='SCENARIO NEW 2' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME2 (copy)_1347983701206581249]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[SCENARIO_NAME2]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
      </column>
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME2]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME1]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME2]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTIONCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Filter 2' datatype='string' default-type='nominal' layered='true' name='[Scenario Filter (copy)_2155535403107311647]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO_NAME2 (copy)_1347983701206581249]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
      </column>
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='Snowflake Summary' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[_09814B39EDE341E2BFD5D4E067A74E4D]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <column-instance column='[ACCOUNTCHANNELCOUNT]' derivation='Sum' name='[sum:ACCOUNTCHANNELCOUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[ACCOUNTCOUNT]' derivation='Sum' name='[sum:ACCOUNTCOUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[FACTORCOUNT]' derivation='Sum' name='[sum:FACTORCOUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[REPCOUNT]' derivation='Sum' name='[sum:REPCOUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[SUGGESTIONCOUNT]' derivation='Sum' name='[sum:SUGGESTIONCOUNT:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Average Suggestion Per Rep  (copy)_526921183993229317]' derivation='User' name='[usr:Average Suggestion Per Rep  (copy)_526921183993229317:qk]' pivot='key' type='quantitative' />
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-aliased-fields='true' show-hidden-fields='true' show-structure='true' />
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <datasource-dependencies datasource='Parameters'>
        <column alias='Engine output' caption='Source 1' datatype='string' name='[Choose data sources (copy)_2155535403020820482]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3&quot;'>
          <calculation class='tableau' formula='&quot;DCO 3&quot;' />
          <aliases>
            <alias key='&quot;DCO 3&quot;' value='Engine output' />
            <alias key='&quot;DCO 4&quot;' value='Post-proc output' />
            <alias key='&quot;DSE 1&quot;' value='Traditional' />
            <alias key='&quot;DSE 2&quot;' value='Next Gen' />
          </aliases>
        </column>
        <column alias='Engine output' caption='Source 2' datatype='string' name='[Source 1 (copy)_2155535403*********]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3 2&quot;'>
          <calculation class='tableau' formula='&quot;DCO 3 2&quot;' />
          <aliases>
            <alias key='&quot;DCO 3 2&quot;' value='Engine output' />
            <alias key='&quot;DCO 4 2&quot;' value='Post-proc output' />
            <alias key='&quot;DSE 1 2&quot;' value='Traditional' />
            <alias key='&quot;DSE 2 2&quot;' value='Next Gen' />
          </aliases>
        </column>
      </datasource-dependencies>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='Snowflake Summary' id='_09814B39EDE341E2BFD5D4E067A74E4D'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
    <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model' inline='true' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah' version='18.1'>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonDetailsDataModel?rev=1.0' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonDetailsDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.0' site='${TABLEAU_SITE}' />
      <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonDetailsDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <calculations>
          <calculation column='[Account (copy)_512495611506294790]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [ACCOUNT_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
          <calculation column='[Account Filter (copy)_981221801042362389]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [HCP_SEGMENT]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
          <calculation column='[Account combo (copy)_981221801282629672]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SUGGESTED_DATE] &#13;&#10;    END' />
          <calculation column='[Account level (copy)_837106628427710466]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [SUGGESTED_DATE] &#13;&#10;END' />
          <calculation column='[Calculation_526921184320745495]' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
          <calculation column='[Calculation_837106628427227136]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [FACTOR_NAME] &#13;&#10;END' />
          <calculation column='[Calculation_981221801239351324]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [CHANNEL] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
          <calculation column='[Calculation_981221801308008490]' formula='ISNULL([Count (copy)_981221801228464153]) AND ISNULL([Count (copy 2)_981221801228656666])' />
          <calculation column='[Channel (copy)_512495611506069509]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [CHANNEL]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
          <calculation column='[Channel combo (copy)_981221801243471901]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [PRODUCT_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
          <calculation column='[Channel combo (copy)_981221801280938018]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [ACCOUNT_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
          <calculation column='[Config (copy)_512495611509768203]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
          <calculation column='[Config (copy)_981221801310863404]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
          <calculation column='[Config (copy)_981221801311191085]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [DCO_RUN_DATE]&#13;&#10;    END' />
          <calculation column='[Count (copy 2)_981221801228656666]' formula='SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)' />
          <calculation column='[Count (copy)_981221801228464153]' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)' />
          <calculation column='[Factor (copy)_512495611506483207]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [FACTOR_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
          <calculation column='[Factor level (copy)_837106628427595777]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [ACCOUNT_NAME] &#13;&#10;END' />
          <calculation column='[Factor level (copy)_837106628427808771]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [PRODUCT_NAME] &#13;&#10;END' />
          <calculation column='[Factor level (copy)_837106628428029957]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [REP_TEAM_NAME] &#13;&#10;END' />
          <calculation column='[Factor level (copy)_837106628428517382]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [HCP_SEGMENT]&#13;&#10;END' />
          <calculation column='[Level 1 (copy)_526921184321654809]' formula='CASE [Parameters].[Level 1 (copy)_526921184321613848]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
          <calculation column='[Level 2 (copy) (copy)_526921184322039839]' formula='CASE [Parameters].[Level 3 (copy)_526921184321929243]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
          <calculation column='[Level 2 (copy)_526921184322031646]' formula='CASE [Parameters].[Level 2 (copy)_526921184321875994]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
          <calculation column='[Level 4 (copy) (copy)_526921184322179105]' formula='CASE [Parameters].[Level 5 (copy)_526921184321998877]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
          <calculation column='[Level 4 (copy)_526921184322166816]' formula='CASE [Parameters].[Level 3 (copy) (copy)_526921184321937436]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
          <calculation column='[Level 6 (copy)_2155535403012546561]' formula='CASE [Parameters].[Level 6 (copy)_2155535403012243456]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
          <calculation column='[Product (copy)_512495611509055496]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [PRODUCT_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
          <calculation column='[Product combo  (copy)_981221801281835044]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [FACTOR_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
          <calculation column='[Product combo  (copy)_981221801282273318]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [REP_TEAM_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
          <calculation column='[Rep (copy)_512495611509235721]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [REP_TEAM_NAME]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
          <calculation column='[Run Date  (copy)_512495611510464526]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [DCO_RUN_DATE]&#13;&#10;    END' />
          <calculation column='[SCENARIO (copy)_1347983701206884354]' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
          <calculation column='[Scenario (copy)_512495611510235149]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
          <calculation column='[Segment (copy)_512495611509411850]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [HCP_SEGMENT]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
          <calculation column='[Segment level (copy)_837106628427919364]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [CHANNEL] &#13;&#10;END' />
          <calculation column='[Suggested Date (copy)_512495611509981196]' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SUGGESTED_DATE] &#13;&#10;    END' />
          <calculation column='[Suggested Date (copy)_981221801310244907]' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </calculations>
        <metadata-records>
          <metadata-record class='column'>
            <remote-name>ACCOUNT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[ACCOUNT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACCOUNT_NAME</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <caption>ACCOUNT</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Account (copy)_512495611506294790</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Account (copy)_512495611506294790]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Account (copy)_512495611506294790</remote-alias>
            <ordinal>18</ordinal>
            <layered>true</layered>
            <caption>Account Filter 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [ACCOUNT_NAME] 
    ELSE '<stay checked>'
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Account Filter (copy)_981221801042362389</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Account Filter (copy)_981221801042362389]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Account Filter (copy)_981221801042362389</remote-alias>
            <ordinal>19</ordinal>
            <layered>true</layered>
            <caption>Segment Filter 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [HCP_SEGMENT]  
    ELSE '<stay checked>'
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Account combo (copy)_981221801282629672</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Account combo (copy)_981221801282629672]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Account combo (copy)_981221801282629672</remote-alias>
            <ordinal>20</ordinal>
            <layered>true</layered>
            <caption>Suggested Date Filter 1</caption>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SUGGESTED_DATE] &#13;
    END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Account level (copy)_837106628427710466</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Account level (copy)_837106628427710466]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Account level (copy)_837106628427710466</remote-alias>
            <ordinal>21</ordinal>
            <layered>true</layered>
            <caption>Suggested Date level</caption>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;
THEN [SUGGESTED_DATE] &#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CHANNEL</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CHANNEL]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL</remote-alias>
            <ordinal>6</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_526921184320745495</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_526921184320745495]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_526921184320745495</remote-alias>
            <ordinal>23</ordinal>
            <layered>true</layered>
            <caption>Level 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Parameter 2]&#13;
WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;
WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;
WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;
WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;
WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;
WHEN &apos;Suggested Date&apos;&#13;
THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;
WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_837106628427227136</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_837106628427227136]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_837106628427227136</remote-alias>
            <ordinal>24</ordinal>
            <layered>true</layered>
            <caption>Factor level</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;
THEN [FACTOR_NAME] &#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_981221801239351324</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_981221801239351324]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_981221801239351324</remote-alias>
            <ordinal>25</ordinal>
            <layered>true</layered>
            <caption>Channel Filter 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [CHANNEL] 
    ELSE '<stay checked>' 
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_981221801308008490</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_981221801308008490]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_981221801308008490</remote-alias>
            <ordinal>26</ordinal>
            <layered>true</layered>
            <caption>Filters non matched</caption>
            <local-type>boolean</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;ISNULL([Count (copy)_981221801228464153]) AND ISNULL([Count (copy 2)_981221801228656666])&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Channel (copy)_512495611506069509</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Channel (copy)_512495611506069509]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Channel (copy)_512495611506069509</remote-alias>
            <ordinal>27</ordinal>
            <layered>true</layered>
            <caption>Channel Filter 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [CHANNEL]  
    ELSE '<stay checked>'
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Channel combo (copy)_981221801243471901</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Channel combo (copy)_981221801243471901]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Channel combo (copy)_981221801243471901</remote-alias>
            <ordinal>28</ordinal>
            <layered>true</layered>
            <caption>Product Filter 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [PRODUCT_NAME]
    ELSE '<stay checked>'  
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Channel combo (copy)_981221801280938018</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Channel combo (copy)_981221801280938018]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Channel combo (copy)_981221801280938018</remote-alias>
            <ordinal>29</ordinal>
            <layered>true</layered>
            <caption>Account Filter 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [ACCOUNT_NAME] 
    ELSE '<stay checked>'
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Config (copy)_512495611509768203</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Config (copy)_512495611509768203]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Config (copy)_512495611509768203</remote-alias>
            <ordinal>30</ordinal>
            <layered>true</layered>
            <caption>Config Filter 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME]
    ELSE '<stay checked>'
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Config (copy)_981221801310863404</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Config (copy)_981221801310863404]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Config (copy)_981221801310863404</remote-alias>
            <ordinal>31</ordinal>
            <layered>true</layered>
            <caption>Scenario Filter 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SCENARIO (copy)_1347983701206884354]
    ELSE '<stay checked>' 
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Config (copy)_981221801311191085</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Config (copy)_981221801311191085]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Config (copy)_981221801311191085</remote-alias>
            <ordinal>32</ordinal>
            <layered>true</layered>
            <caption>Run Date Filter 1</caption>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [DCO_RUN_DATE]&#13;
    END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Count (copy 2)_981221801228656666</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Count (copy 2)_981221801228656666]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Count (copy 2)_981221801228656666</remote-alias>
            <ordinal>33</ordinal>
            <layered>true</layered>
            <caption>Count Source 2</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Count (copy)_981221801228464153</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Count (copy)_981221801228464153]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Count (copy)_981221801228464153</remote-alias>
            <ordinal>34</ordinal>
            <layered>true</layered>
            <caption>Count Source 1</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_DATE</remote-name>
            <remote-type>133</remote-type>
            <local-name>[DCO_RUN_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_DATE</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <caption>RUNDATE</caption>
            <family>Custom SQL Query</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FACTOR_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[FACTOR_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FACTOR_NAME</remote-alias>
            <ordinal>9</ordinal>
            <layered>true</layered>
            <caption>FACTOR</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FLAG</remote-name>
            <remote-type>129</remote-type>
            <local-name>[FLAG]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FLAG</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Factor (copy)_512495611506483207</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Factor (copy)_512495611506483207]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Factor (copy)_512495611506483207</remote-alias>
            <ordinal>36</ordinal>
            <layered>true</layered>
            <caption>Factor Filter 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [FACTOR_NAME] 
    ELSE '<stay checked>'
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Factor level (copy)_837106628427595777</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Factor level (copy)_837106628427595777]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Factor level (copy)_837106628427595777</remote-alias>
            <ordinal>37</ordinal>
            <layered>true</layered>
            <caption>Account level</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;
THEN [ACCOUNT_NAME] &#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Factor level (copy)_837106628427808771</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Factor level (copy)_837106628427808771]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Factor level (copy)_837106628427808771</remote-alias>
            <ordinal>38</ordinal>
            <layered>true</layered>
            <caption>Product level</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;
THEN [PRODUCT_NAME] &#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Factor level (copy)_837106628428029957</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Factor level (copy)_837106628428029957]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Factor level (copy)_837106628428029957</remote-alias>
            <ordinal>39</ordinal>
            <layered>true</layered>
            <caption>Rep level </caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;
THEN [REP_TEAM_NAME] &#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Factor level (copy)_837106628428517382</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Factor level (copy)_837106628428517382]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Factor level (copy)_837106628428517382</remote-alias>
            <ordinal>40</ordinal>
            <layered>true</layered>
            <caption>Segment level</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;
THEN [HCP_SEGMENT]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>HCP_SEGMENT</remote-name>
            <remote-type>129</remote-type>
            <local-name>[HCP_SEGMENT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_SEGMENT</remote-alias>
            <ordinal>11</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LAST_DCO_RUN_DATE</remote-name>
            <remote-type>133</remote-type>
            <local-name>[LAST_DCO_RUN_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
            <ordinal>12</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Level 1 (copy)_526921184321654809</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Level 1 (copy)_526921184321654809]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Level 1 (copy)_526921184321654809</remote-alias>
            <ordinal>41</ordinal>
            <layered>true</layered>
            <caption>Level 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Level 1 (copy)_526921184321613848]&#13;
WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;
WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;
WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;
WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;
WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;
WHEN &apos;Suggested Date&apos;&#13;
THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;
WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Level 2 (copy) (copy)_526921184322039839</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Level 2 (copy) (copy)_526921184322039839]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Level 2 (copy) (copy)_526921184322039839</remote-alias>
            <ordinal>42</ordinal>
            <layered>true</layered>
            <caption>Level 4</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Level 3 (copy)_526921184321929243]&#13;
WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;
WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;
WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;
WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;
WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;
WHEN &apos;Suggested Date&apos;&#13;
THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;
WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Level 2 (copy)_526921184322031646</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Level 2 (copy)_526921184322031646]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Level 2 (copy)_526921184322031646</remote-alias>
            <ordinal>43</ordinal>
            <layered>true</layered>
            <caption>Level 3</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Level 2 (copy)_526921184321875994]&#13;
WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;
WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;
WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;
WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;
WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;
WHEN &apos;Suggested Date&apos;&#13;
THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;
WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Level 4 (copy) (copy)_526921184322179105</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Level 4 (copy) (copy)_526921184322179105]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Level 4 (copy) (copy)_526921184322179105</remote-alias>
            <ordinal>44</ordinal>
            <layered>true</layered>
            <caption>Level 6</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Level 5 (copy)_526921184321998877]&#13;
WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;
WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;
WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;
WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;
WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;
WHEN &apos;Suggested Date&apos;&#13;
THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;
WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Level 4 (copy)_526921184322166816</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Level 4 (copy)_526921184322166816]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Level 4 (copy)_526921184322166816</remote-alias>
            <ordinal>45</ordinal>
            <layered>true</layered>
            <caption>Level 5</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Level 3 (copy) (copy)_526921184321937436]&#13;
WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;
WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;
WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;
WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;
WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;
WHEN &apos;Suggested Date&apos;&#13;
THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;
WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Level 6 (copy)_2155535403012546561</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Level 6 (copy)_2155535403012546561]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Level 6 (copy)_2155535403012546561</remote-alias>
            <ordinal>46</ordinal>
            <layered>true</layered>
            <caption>Level 7</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Level 6 (copy)_2155535403012243456]&#13;
WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;
WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;
WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;
WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;
WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;
WHEN &apos;Suggested Date&apos;&#13;
THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;
+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;
WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME</remote-alias>
            <ordinal>8</ordinal>
            <layered>true</layered>
            <caption>PRODUCT</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Product (copy)_512495611509055496</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Product (copy)_512495611509055496]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Product (copy)_512495611509055496</remote-alias>
            <ordinal>48</ordinal>
            <layered>true</layered>
            <caption>Product Filter 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [PRODUCT_NAME]
    ELSE '<stay checked>'  
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Product combo  (copy)_981221801281835044</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Product combo  (copy)_981221801281835044]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Product combo  (copy)_981221801281835044</remote-alias>
            <ordinal>49</ordinal>
            <layered>true</layered>
            <caption>Factor Filter 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [FACTOR_NAME] 
    ELSE '<stay checked>'
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Product combo  (copy)_981221801282273318</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Product combo  (copy)_981221801282273318]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Product combo  (copy)_981221801282273318</remote-alias>
            <ordinal>50</ordinal>
            <layered>true</layered>
            <caption>Rep Filter 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [REP_TEAM_NAME]
    ELSE '<stay checked>'  
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>REP_TEAM_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[REP_TEAM_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>REP_TEAM_NAME</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <caption>REP</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Rep (copy)_512495611509235721</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Rep (copy)_512495611509235721]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Rep (copy)_512495611509235721</remote-alias>
            <ordinal>52</ordinal>
            <layered>true</layered>
            <caption>Rep Filter 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [REP_TEAM_NAME]  
    ELSE '<stay checked>'
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Run Date  (copy)_512495611510464526</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Run Date  (copy)_512495611510464526]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Run Date  (copy)_512495611510464526</remote-alias>
            <ordinal>53</ordinal>
            <layered>true</layered>
            <caption>Run Date Filter 2</caption>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [DCO_RUN_DATE]&#13;
    END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO (copy)_1347983701206884354</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[SCENARIO (copy)_1347983701206884354]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO (copy)_1347983701206884354</remote-alias>
            <ordinal>54</ordinal>
            <layered>true</layered>
            <caption>SCENARIO NEW</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))
+&apos;/&apos;
+STR(DAY([LAST_DCO_RUN_DATE]))
+&apos;/&apos;
+STR(YEAR([LAST_DCO_RUN_DATE]))
+&apos;)&apos;,&apos;&apos;)&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_NAME</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <caption>SCENARIO</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SE_CONFIG_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_NAME</remote-alias>
            <ordinal>0</ordinal>
            <layered>true</layered>
            <caption>CONFIG</caption>
            <family>Custom SQL Query</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTED_DATE</remote-name>
            <remote-type>133</remote-type>
            <local-name>[SUGGESTED_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTED_DATE</remote-alias>
            <ordinal>7</ordinal>
            <layered>true</layered>
            <caption>SuggestedDATE</caption>
            <family>Custom SQL Query</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>SUGGESTIONCOUNT</remote-name>
            <remote-type>20</remote-type>
            <local-name>[SUGGESTIONCOUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTIONCOUNT</remote-alias>
            <ordinal>10</ordinal>
            <layered>true</layered>
            <family>Custom SQL Query</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Scenario (copy)_512495611510235149</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Scenario (copy)_512495611510235149]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Scenario (copy)_512495611510235149</remote-alias>
            <ordinal>56</ordinal>
            <layered>true</layered>
            <caption>Scenario Filter 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO (copy)_1347983701206884354]
    ELSE '<stay checked>' 
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Segment (copy)_512495611509411850</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Segment (copy)_512495611509411850]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Segment (copy)_512495611509411850</remote-alias>
            <ordinal>57</ordinal>
            <layered>true</layered>
            <caption>Segment Filter 2</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [HCP_SEGMENT]  
    ELSE '<stay checked>'
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Segment level (copy)_837106628427919364</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Segment level (copy)_837106628427919364]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Segment level (copy)_837106628427919364</remote-alias>
            <ordinal>58</ordinal>
            <layered>true</layered>
            <caption>Channel level</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;
THEN [CHANNEL] &#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Suggested Date (copy)_512495611509981196</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Suggested Date (copy)_512495611509981196]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Suggested Date (copy)_512495611509981196</remote-alias>
            <ordinal>59</ordinal>
            <layered>true</layered>
            <caption>Suggested Date Filter 2</caption>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SUGGESTED_DATE] &#13;
    END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Suggested Date (copy)_981221801310244907</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Suggested Date (copy)_981221801310244907]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Suggested Date (copy)_981221801310244907</remote-alias>
            <ordinal>60</ordinal>
            <layered>true</layered>
            <caption>Config Filter 1</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;<![CDATA[IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME]
    ELSE '<stay checked>'
    END]]>&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_CFFC621BD5F74AC1A47F5DA01AE0557A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_CASE_INSENSITIVE_CONTAINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORT_ANALYTIC_FUNCTIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model' inline='true' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonDetailsDataModel?rev=1.0' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonDetailsDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.0' site='${TABLEAU_SITE}' />
  <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonDetailsDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <cols>
      <map key='[ACCOUNT_NAME]' value='[sqlproxy].[ACCOUNT_NAME]' />
      <map key='[Account (copy)_512495611506294790]' value='[sqlproxy].[Account (copy)_512495611506294790]' />
      <map key='[Account Filter (copy)_981221801042362389]' value='[sqlproxy].[Account Filter (copy)_981221801042362389]' />
      <map key='[Account combo (copy)_981221801282629672]' value='[sqlproxy].[Account combo (copy)_981221801282629672]' />
      <map key='[Account level (copy)_837106628427710466]' value='[sqlproxy].[Account level (copy)_837106628427710466]' />
      <map key='[CHANNEL]' value='[sqlproxy].[CHANNEL]' />
      <map key='[Calculation_526921184320745495]' value='[sqlproxy].[Calculation_526921184320745495]' />
      <map key='[Calculation_837106628427227136]' value='[sqlproxy].[Calculation_837106628427227136]' />
      <map key='[Calculation_981221801239351324]' value='[sqlproxy].[Calculation_981221801239351324]' />
      <map key='[Calculation_981221801308008490]' value='[sqlproxy].[Calculation_981221801308008490]' />
      <map key='[Channel (copy)_512495611506069509]' value='[sqlproxy].[Channel (copy)_512495611506069509]' />
      <map key='[Channel combo (copy)_981221801243471901]' value='[sqlproxy].[Channel combo (copy)_981221801243471901]' />
      <map key='[Channel combo (copy)_981221801280938018]' value='[sqlproxy].[Channel combo (copy)_981221801280938018]' />
      <map key='[Config (copy)_512495611509768203]' value='[sqlproxy].[Config (copy)_512495611509768203]' />
      <map key='[Config (copy)_981221801310863404]' value='[sqlproxy].[Config (copy)_981221801310863404]' />
      <map key='[Config (copy)_981221801311191085]' value='[sqlproxy].[Config (copy)_981221801311191085]' />
      <map key='[Count (copy 2)_981221801228656666]' value='[sqlproxy].[Count (copy 2)_981221801228656666]' />
      <map key='[Count (copy)_981221801228464153]' value='[sqlproxy].[Count (copy)_981221801228464153]' />
      <map key='[DCO_RUN_DATE]' value='[sqlproxy].[DCO_RUN_DATE]' />
      <map key='[FACTOR_NAME]' value='[sqlproxy].[FACTOR_NAME]' />
      <map key='[FLAG]' value='[sqlproxy].[FLAG]' />
      <map key='[Factor (copy)_512495611506483207]' value='[sqlproxy].[Factor (copy)_512495611506483207]' />
      <map key='[Factor level (copy)_837106628427595777]' value='[sqlproxy].[Factor level (copy)_837106628427595777]' />
      <map key='[Factor level (copy)_837106628427808771]' value='[sqlproxy].[Factor level (copy)_837106628427808771]' />
      <map key='[Factor level (copy)_837106628428029957]' value='[sqlproxy].[Factor level (copy)_837106628428029957]' />
      <map key='[Factor level (copy)_837106628428517382]' value='[sqlproxy].[Factor level (copy)_837106628428517382]' />
      <map key='[HCP_SEGMENT]' value='[sqlproxy].[HCP_SEGMENT]' />
      <map key='[LAST_DCO_RUN_DATE]' value='[sqlproxy].[LAST_DCO_RUN_DATE]' />
      <map key='[Level 1 (copy)_526921184321654809]' value='[sqlproxy].[Level 1 (copy)_526921184321654809]' />
      <map key='[Level 2 (copy) (copy)_526921184322039839]' value='[sqlproxy].[Level 2 (copy) (copy)_526921184322039839]' />
      <map key='[Level 2 (copy)_526921184322031646]' value='[sqlproxy].[Level 2 (copy)_526921184322031646]' />
      <map key='[Level 4 (copy) (copy)_526921184322179105]' value='[sqlproxy].[Level 4 (copy) (copy)_526921184322179105]' />
      <map key='[Level 4 (copy)_526921184322166816]' value='[sqlproxy].[Level 4 (copy)_526921184322166816]' />
      <map key='[Level 6 (copy)_2155535403012546561]' value='[sqlproxy].[Level 6 (copy)_2155535403012546561]' />
      <map key='[PRODUCT_NAME]' value='[sqlproxy].[PRODUCT_NAME]' />
      <map key='[Product (copy)_512495611509055496]' value='[sqlproxy].[Product (copy)_512495611509055496]' />
      <map key='[Product combo  (copy)_981221801281835044]' value='[sqlproxy].[Product combo  (copy)_981221801281835044]' />
      <map key='[Product combo  (copy)_981221801282273318]' value='[sqlproxy].[Product combo  (copy)_981221801282273318]' />
      <map key='[REP_TEAM_NAME]' value='[sqlproxy].[REP_TEAM_NAME]' />
      <map key='[Rep (copy)_512495611509235721]' value='[sqlproxy].[Rep (copy)_512495611509235721]' />
      <map key='[Run Date  (copy)_512495611510464526]' value='[sqlproxy].[Run Date  (copy)_512495611510464526]' />
      <map key='[SCENARIO (copy)_1347983701206884354]' value='[sqlproxy].[SCENARIO (copy)_1347983701206884354]' />
      <map key='[SCENARIO_NAME]' value='[sqlproxy].[SCENARIO_NAME]' />
      <map key='[SE_CONFIG_NAME]' value='[sqlproxy].[SE_CONFIG_NAME]' />
      <map key='[SUGGESTED_DATE]' value='[sqlproxy].[SUGGESTED_DATE]' />
      <map key='[SUGGESTIONCOUNT]' value='[sqlproxy].[SUGGESTIONCOUNT]' />
      <map key='[Scenario (copy)_512495611510235149]' value='[sqlproxy].[Scenario (copy)_512495611510235149]' />
      <map key='[Segment (copy)_512495611509411850]' value='[sqlproxy].[Segment (copy)_512495611509411850]' />
      <map key='[Segment level (copy)_837106628427919364]' value='[sqlproxy].[Segment level (copy)_837106628427919364]' />
      <map key='[Suggested Date (copy)_512495611509981196]' value='[sqlproxy].[Suggested Date (copy)_512495611509981196]' />
      <map key='[Suggested Date (copy)_981221801310244907]' value='[sqlproxy].[Suggested Date (copy)_981221801310244907]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='Count' datatype='string' default-type='nominal' name='[:Measure Names]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <aliases>
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[sum:accountChannelCount1:qk]&quot;' value='Suggested Account/Channel Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[sum:accountCount1:qk]&quot;' value='Suggested Account Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[sum:factorCount1:qk]&quot;' value='Factors fired Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[sum:repCount1:qk]&quot;' value='Suggested Rep Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[sum:suggestionCount1:qk]&quot;' value='Suggestion Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[usr:Count (copy 2)_981221801228656666:qk]&quot;' value='Source 2' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model].[usr:Count (copy)_981221801228464153:qk]&quot;' value='Source 1 ' />
    </aliases>
  </column>
  <column aggregation='Count' caption='ACCOUNT' datatype='string' default-type='nominal' name='[ACCOUNT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[ACCOUNT_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Account Filter 2' datatype='string' default-type='nominal' name='[Account (copy)_512495611506294790]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [ACCOUNT_NAME] &\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;&\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Segment Filter 1' datatype='string' default-type='nominal' name='[Account Filter (copy)_981221801042362389]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [HCP_SEGMENT]  &\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;&\#13;&\#10;    END' />
  </column>
  <column aggregation='Year' caption='Suggested Date Filter 1' datatype='date' default-type='ordinal' name='[Account combo (copy)_981221801282629672]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SUGGESTED_DATE] &\#13;&\#10;    END' />
  </column>
  <column aggregation='Year' caption='Suggested Date level' datatype='date' default-type='ordinal' name='[Account level (copy)_837106628427710466]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&\#13;&\#10;THEN [SUGGESTED_DATE] &\#13;&\#10;END' />
  </column>
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[CHANNELID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Level 1' datatype='string' default-type='nominal' name='[Calculation_526921184320745495]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&\#13;&\#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&\#13;&\#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&\#13;&\#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&\#13;&\#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&\#13;&\#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&\#13;&\#10;WHEN &apos;Suggested Date&apos;&\#13;&\#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&\#13;&\#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Factor level' datatype='string' default-type='nominal' name='[Calculation_837106628427227136]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&\#13;&\#10;THEN [FACTOR_NAME] &\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Channel Filter 1' datatype='string' default-type='nominal' name='[Calculation_981221801239351324]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [CHANNEL] &\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos; &\#13;&\#10;    END' />
  </column>
  <column aggregation='User' caption='Filters non matched' datatype='boolean' default-type='nominal' name='[Calculation_981221801308008490]' pivot='key' role='measure' type='nominal' user-datatype='boolean' visual-totals='Default'>
    <calculation class='tableau' formula='ISNULL([Count (copy)_981221801228464153]) AND ISNULL([Count (copy 2)_981221801228656666])' />
  </column>
  <column aggregation='Count' caption='Channel Filter 2' datatype='string' default-type='nominal' name='[Channel (copy)_512495611506069509]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [CHANNEL]  &\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;&\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Product Filter 1' datatype='string' default-type='nominal' name='[Channel combo (copy)_981221801243471901]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [PRODUCT_NAME]&\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Account Filter 1' datatype='string' default-type='nominal' name='[Channel combo (copy)_981221801280938018]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [ACCOUNT_NAME] &\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;&\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Config Filter 2' datatype='string' default-type='nominal' name='[Config (copy)_512495611509768203]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME]&\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;&\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Scenario Filter 1' datatype='string' default-type='nominal' name='[Config (copy)_981221801310863404]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos; &\#13;&\#10;    END' />
  </column>
  <column aggregation='Year' caption='Run Date Filter 1' datatype='date' default-type='ordinal' name='[Config (copy)_981221801311191085]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [DCO_RUN_DATE]&\#13;&\#10;    END' />
  </column>
  <column aggregation='User' caption='Count Source 2' datatype='integer' default-type='quantitative' name='[Count (copy 2)_981221801228656666]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)' />
  </column>
  <column aggregation='User' caption='Count Source 1' datatype='integer' default-type='quantitative' name='[Count (copy)_981221801228464153]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)' />
  </column>
  <column aggregation='Year' caption='RUNDATE' datatype='date' default-type='ordinal' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' caption='FACTOR' datatype='string' default-type='nominal' name='[FACTOR_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[FACTOR_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Factor Filter 2' datatype='string' default-type='nominal' name='[Factor (copy)_512495611506483207]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [FACTOR_NAME] &\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;&\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Account level' datatype='string' default-type='nominal' name='[Factor level (copy)_837106628427595777]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&\#13;&\#10;THEN [ACCOUNT_NAME] &\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Product level' datatype='string' default-type='nominal' name='[Factor level (copy)_837106628427808771]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&\#13;&\#10;THEN [PRODUCT_NAME] &\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Rep level ' datatype='string' default-type='nominal' name='[Factor level (copy)_837106628428029957]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&\#13;&\#10;THEN [REP_TEAM_NAME] &\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Segment level' datatype='string' default-type='nominal' name='[Factor level (copy)_837106628428517382]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&\#13;&\#10;THEN [HCP_SEGMENT]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Level 2' datatype='string' default-type='nominal' name='[Level 1 (copy)_526921184321654809]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 1 (copy)_526921184321613848]&\#13;&\#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&\#13;&\#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&\#13;&\#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&\#13;&\#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&\#13;&\#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&\#13;&\#10;WHEN &apos;Suggested Date&apos;&\#13;&\#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&\#13;&\#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Level 4' datatype='string' default-type='nominal' name='[Level 2 (copy) (copy)_526921184322039839]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 3 (copy)_526921184321929243]&\#13;&\#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&\#13;&\#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&\#13;&\#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&\#13;&\#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&\#13;&\#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&\#13;&\#10;WHEN &apos;Suggested Date&apos;&\#13;&\#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&\#13;&\#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Level 3' datatype='string' default-type='nominal' name='[Level 2 (copy)_526921184322031646]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 2 (copy)_526921184321875994]&\#13;&\#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&\#13;&\#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&\#13;&\#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&\#13;&\#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&\#13;&\#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&\#13;&\#10;WHEN &apos;Suggested Date&apos;&\#13;&\#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&\#13;&\#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Level 6' datatype='string' default-type='nominal' name='[Level 4 (copy) (copy)_526921184322179105]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 5 (copy)_526921184321998877]&\#13;&\#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&\#13;&\#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&\#13;&\#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&\#13;&\#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&\#13;&\#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&\#13;&\#10;WHEN &apos;Suggested Date&apos;&\#13;&\#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&\#13;&\#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Level 5' datatype='string' default-type='nominal' name='[Level 4 (copy)_526921184322166816]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 3 (copy) (copy)_526921184321937436]&\#13;&\#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&\#13;&\#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&\#13;&\#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&\#13;&\#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&\#13;&\#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&\#13;&\#10;WHEN &apos;Suggested Date&apos;&\#13;&\#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&\#13;&\#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='Level 7' datatype='string' default-type='nominal' name='[Level 6 (copy)_2155535403012546561]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Level 6 (copy)_2155535403012243456]&\#13;&\#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&\#13;&\#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&\#13;&\#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&\#13;&\#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&\#13;&\#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&\#13;&\#10;WHEN &apos;Suggested Date&apos;&\#13;&\#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&\#13;&\#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&\#13;&\#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&\#13;&\#10;END' />
  </column>
  <column aggregation='Count' caption='PRODUCT' datatype='string' default-type='nominal' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[PRODUCT_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Filter 2' datatype='string' default-type='nominal' name='[Product (copy)_512495611509055496]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [PRODUCT_NAME]&\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Factor Filter 1' datatype='string' default-type='nominal' name='[Product combo  (copy)_981221801281835044]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [FACTOR_NAME] &\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;&\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Rep Filter 1' datatype='string' default-type='nominal' name='[Product combo  (copy)_981221801282273318]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [REP_TEAM_NAME]&\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='REP' datatype='string' default-type='nominal' name='[REP_TEAM_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[REP_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Rep Filter 2' datatype='string' default-type='nominal' name='[Rep (copy)_512495611509235721]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [REP_TEAM_NAME]  &\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;&\#13;&\#10;    END' />
  </column>
  <column aggregation='Year' caption='Run Date Filter 2' datatype='date' default-type='ordinal' name='[Run Date  (copy)_512495611510464526]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [DCO_RUN_DATE]&\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='SCENARIO NEW' datatype='string' default-type='nominal' name='[SCENARIO (copy)_1347983701206884354]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&\#10;+&apos;/&apos;&\#10;+STR(DAY([LAST_DCO_RUN_DATE]))&\#10;+&apos;/&apos;&\#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&\#10;+&apos;)&apos;,&apos;&apos;)' />
  </column>
  <column aggregation='Count' caption='SCENARIO' datatype='string' default-type='nominal' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' hidden='true' name='[SE_CONFIG_ID]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='CONFIG' datatype='string' default-type='nominal' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='SuggestedDATE' datatype='date' default-type='ordinal' name='[SUGGESTED_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Filter 2' datatype='string' default-type='nominal' name='[Scenario (copy)_512495611510235149]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos; &\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Segment Filter 2' datatype='string' default-type='nominal' name='[Segment (copy)_512495611509411850]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [HCP_SEGMENT]  &\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;&\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Channel level' datatype='string' default-type='nominal' name='[Segment level (copy)_837106628427919364]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&\#13;&\#10;THEN [CHANNEL] &\#13;&\#10;END' />
  </column>
  <column aggregation='Year' caption='Suggested Date Filter 2' datatype='date' default-type='ordinal' name='[Suggested Date (copy)_512495611509981196]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SUGGESTED_DATE] &\#13;&\#10;    END' />
  </column>
  <column aggregation='Count' caption='Config Filter 1' datatype='string' default-type='nominal' name='[Suggested Date (copy)_981221801310244907]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME]&\#13;&\#10;    ELSE &apos;&lt;stay checked&gt;&apos;&\#13;&\#10;    END' />
  </column>
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='Snowflake' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[_3A533CA42284488BB56AA31CE554622E]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='MySQL' datatype='table' default-type='quantitative' hidden='true' name='[__tableau_internal_object_id__].[_3CEE7E99CC7146BA96A53F15A77B5338]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='Snowflake Details' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[_CFFC621BD5F74AC1A47F5DA01AE0557A]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[accountId]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[channelId]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[factorUID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[productUID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' datatype='string' default-type='nominal' hidden='true' name='[repUID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Sum' datatype='integer' default-type='quantitative' hidden='true' name='[seConfigId]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column-instance column='[Count (copy 2)_981221801228656666]' derivation='User' name='[usr:Count (copy 2)_981221801228656666:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Count (copy)_981221801228464153]' derivation='User' name='[usr:Count (copy)_981221801228464153:qk]' pivot='key' type='quantitative' />
  <drill-paths>
    <drill-path name='Levels'>
      <field>[Calculation_526921184320745495]</field>
      <field>[Level 1 (copy)_526921184321654809]</field>
      <field>[Level 2 (copy)_526921184322031646]</field>
      <field>[Level 2 (copy) (copy)_526921184322039839]</field>
      <field>[Level 4 (copy)_526921184322166816]</field>
      <field>[Level 4 (copy) (copy)_526921184322179105]</field>
      <field>[Level 6 (copy)_2155535403012546561]</field>
    </drill-path>
  </drill-paths>
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.706977' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.293023' show-aliased-fields='true' show-hidden-fields='true' show-structure='true' />
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <datasource-dependencies datasource='Parameters'>
    <column alias='Engine output' caption='Source 1' datatype='string' name='[Choose data sources (copy)_2155535403020820482]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3&quot;'>
      <calculation class='tableau' formula='&quot;DCO 3&quot;' />
      <aliases>
        <alias key='&quot;DCO 3&quot;' value='Engine output' />
        <alias key='&quot;DCO 4&quot;' value='Post-proc output' />
        <alias key='&quot;DSE 1&quot;' value='Traditional' />
        <alias key='&quot;DSE 2&quot;' value='Next Gen' />
      </aliases>
      <members>
        <member alias='Traditional' value='&quot;DSE 1&quot;' />
        <member alias='Next Gen' value='&quot;DSE 2&quot;' />
        <member alias='Engine output' value='&quot;DCO 3&quot;' />
        <member alias='Post-proc output' value='&quot;DCO 4&quot;' />
      </members>
    </column>
    <column caption='Level 2' datatype='string' name='[Level 1 (copy)_526921184321613848]' param-domain-type='list' role='measure' type='nominal' value='&quot;Segment&quot;'>
      <calculation class='tableau' formula='&quot;Segment&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 3' datatype='string' name='[Level 2 (copy)_526921184321875994]' param-domain-type='list' role='measure' type='nominal' value='&quot;Product&quot;'>
      <calculation class='tableau' formula='&quot;Product&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 5' datatype='string' name='[Level 3 (copy) (copy)_526921184321937436]' param-domain-type='list' role='measure' type='nominal' value='&quot;Rep&quot;'>
      <calculation class='tableau' formula='&quot;Rep&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 4' datatype='string' name='[Level 3 (copy)_526921184321929243]' param-domain-type='list' role='measure' type='nominal' value='&quot;Factor&quot;'>
      <calculation class='tableau' formula='&quot;Factor&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 6' datatype='string' name='[Level 5 (copy)_526921184321998877]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account&quot;'>
      <calculation class='tableau' formula='&quot;Account&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 7' datatype='string' name='[Level 6 (copy)_2155535403012243456]' param-domain-type='list' role='measure' type='nominal' value='&quot;Suggested Date&quot;'>
      <calculation class='tableau' formula='&quot;Suggested Date&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Level 1' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
      <calculation class='tableau' formula='&quot;Channel&quot;' />
      <members>
        <member value='&quot;Account&quot;' />
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Factor&quot;' />
        <member value='&quot;Product&quot;' />
        <member value='&quot;Rep&quot;' />
        <member value='&quot;Suggested Date&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column alias='Engine output' caption='Source 2' datatype='string' name='[Source 1 (copy)_2155535403*********]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3 2&quot;'>
      <calculation class='tableau' formula='&quot;DCO 3 2&quot;' />
      <aliases>
        <alias key='&quot;DCO 3 2&quot;' value='Engine output' />
        <alias key='&quot;DCO 4 2&quot;' value='Post-proc output' />
        <alias key='&quot;DSE 1 2&quot;' value='Traditional' />
        <alias key='&quot;DSE 2 2&quot;' value='Next Gen' />
      </aliases>
      <members>
        <member alias='Traditional' value='&quot;DSE 1 2&quot;' />
        <member alias='Next Gen' value='&quot;DSE 2 2&quot;' />
        <member alias='Engine output' value='&quot;DCO 3 2&quot;' />
        <member alias='Post-proc output' value='&quot;DCO 4 2&quot;' />
      </members>
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='Snowflake Details' id='_CFFC621BD5F74AC1A47F5DA01AE0557A'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='hyper' version='0.0.0'>
  </primary-dialect>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>true</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
              <attribute datatype='string' name='update-time'>&quot;3/13/2023 10:28:51 PM&quot;</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='sunday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[:Measure Names]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <aliases>
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[sum:accountChannelCount1:qk]&quot;' value='Suggested Account/Channel Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[sum:accountCount1:qk]&quot;' value='Suggested Account Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[sum:factorCount1:qk]&quot;' value='Factors fired Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[sum:repCount1:qk]&quot;' value='Suggested Rep Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[sum:suggestionCount1:qk]&quot;' value='Suggestion Count' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[usr:Count (copy 2)_981221801228656666:qk]&quot;' value='Source 2' />
          <alias key='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[usr:Count (copy)_981221801228464153:qk]&quot;' value='Source 1 ' />
        </aliases>
      </column>
      <column aggregation='Count' caption='ACCOUNT' datatype='string' default-type='nominal' layered='true' name='[ACCOUNT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Account Filter 2' datatype='string' default-type='nominal' layered='true' name='[Account (copy)_512495611506294790]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [ACCOUNT_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Segment Filter 1' datatype='string' default-type='nominal' layered='true' name='[Account Filter (copy)_981221801042362389]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [HCP_SEGMENT]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
      </column>
      <column aggregation='Year' caption='Suggested Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Account combo (copy)_981221801282629672]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SUGGESTED_DATE] &#13;&#10;    END' />
      </column>
      <column aggregation='Year' caption='Suggested Date level' datatype='date' default-type='ordinal' layered='true' name='[Account level (copy)_837106628427710466]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [SUGGESTED_DATE] &#13;&#10;END' />
      </column>
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Level 1' datatype='string' default-type='nominal' layered='true' name='[Calculation_526921184320745495]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Factor level' datatype='string' default-type='nominal' layered='true' name='[Calculation_837106628427227136]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [FACTOR_NAME] &#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Channel Filter 1' datatype='string' default-type='nominal' layered='true' name='[Calculation_981221801239351324]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [CHANNEL] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
      </column>
      <column aggregation='User' caption='Filters non matched' datatype='boolean' default-type='nominal' layered='true' name='[Calculation_981221801308008490]' pivot='key' role='measure' type='nominal' user-datatype='boolean' visual-totals='Default'>
        <calculation class='tableau' formula='ISNULL([Count (copy)_981221801228464153]) AND ISNULL([Count (copy 2)_981221801228656666])' />
      </column>
      <column aggregation='Count' caption='Channel Filter 2' datatype='string' default-type='nominal' layered='true' name='[Channel (copy)_512495611506069509]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [CHANNEL]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Product Filter 1' datatype='string' default-type='nominal' layered='true' name='[Channel combo (copy)_981221801243471901]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [PRODUCT_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Account Filter 1' datatype='string' default-type='nominal' layered='true' name='[Channel combo (copy)_981221801280938018]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [ACCOUNT_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Config Filter 2' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_512495611509768203]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Scenario Filter 1' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_981221801310863404]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
      </column>
      <column aggregation='Year' caption='Run Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Config (copy)_981221801311191085]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [DCO_RUN_DATE]&#13;&#10;    END' />
      </column>
      <column aggregation='User' caption='Count Source 2' datatype='integer' default-type='quantitative' layered='true' name='[Count (copy 2)_981221801228656666]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)' />
      </column>
      <column aggregation='User' caption='Count Source 1' datatype='integer' default-type='quantitative' layered='true' name='[Count (copy)_981221801228464153]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)' />
      </column>
      <column aggregation='Year' caption='RUNDATE' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' caption='FACTOR' datatype='string' default-type='nominal' layered='true' name='[FACTOR_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[FLAG]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Factor Filter 2' datatype='string' default-type='nominal' layered='true' name='[Factor (copy)_512495611506483207]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [FACTOR_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Account level' datatype='string' default-type='nominal' layered='true' name='[Factor level (copy)_837106628427595777]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [ACCOUNT_NAME] &#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Product level' datatype='string' default-type='nominal' layered='true' name='[Factor level (copy)_837106628427808771]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [PRODUCT_NAME] &#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Rep level ' datatype='string' default-type='nominal' layered='true' name='[Factor level (copy)_837106628428029957]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [REP_TEAM_NAME] &#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Segment level' datatype='string' default-type='nominal' layered='true' name='[Factor level (copy)_837106628428517382]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [HCP_SEGMENT]&#13;&#10;END' />
      </column>
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[HCP_SEGMENT]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' caption='Level 2' datatype='string' default-type='nominal' layered='true' name='[Level 1 (copy)_526921184321654809]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Level 1 (copy)_526921184321613848]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Level 4' datatype='string' default-type='nominal' layered='true' name='[Level 2 (copy) (copy)_526921184322039839]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Level 3 (copy)_526921184321929243]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Level 3' datatype='string' default-type='nominal' layered='true' name='[Level 2 (copy)_526921184322031646]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Level 2 (copy)_526921184321875994]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Level 6' datatype='string' default-type='nominal' layered='true' name='[Level 4 (copy) (copy)_526921184322179105]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Level 5 (copy)_526921184321998877]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Level 5' datatype='string' default-type='nominal' layered='true' name='[Level 4 (copy)_526921184322166816]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Level 3 (copy) (copy)_526921184321937436]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='Level 7' datatype='string' default-type='nominal' layered='true' name='[Level 6 (copy)_2155535403012546561]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Level 6 (copy)_2155535403012243456]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
      </column>
      <column aggregation='Count' caption='PRODUCT' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Filter 2' datatype='string' default-type='nominal' layered='true' name='[Product (copy)_512495611509055496]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [PRODUCT_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Factor Filter 1' datatype='string' default-type='nominal' layered='true' name='[Product combo  (copy)_981221801281835044]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [FACTOR_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Rep Filter 1' datatype='string' default-type='nominal' layered='true' name='[Product combo  (copy)_981221801282273318]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [REP_TEAM_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='REP' datatype='string' default-type='nominal' layered='true' name='[REP_TEAM_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Rep Filter 2' datatype='string' default-type='nominal' layered='true' name='[Rep (copy)_512495611509235721]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [REP_TEAM_NAME]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
      </column>
      <column aggregation='Year' caption='Run Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Run Date  (copy)_512495611510464526]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [DCO_RUN_DATE]&#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='SCENARIO NEW' datatype='string' default-type='nominal' layered='true' name='[SCENARIO (copy)_1347983701206884354]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
      </column>
      <column aggregation='Count' caption='SCENARIO' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='CONFIG' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' caption='SuggestedDATE' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTED_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTIONCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Filter 2' datatype='string' default-type='nominal' layered='true' name='[Scenario (copy)_512495611510235149]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Segment Filter 2' datatype='string' default-type='nominal' layered='true' name='[Segment (copy)_512495611509411850]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [HCP_SEGMENT]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Channel level' datatype='string' default-type='nominal' layered='true' name='[Segment level (copy)_837106628427919364]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [CHANNEL] &#13;&#10;END' />
      </column>
      <column aggregation='Year' caption='Suggested Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Suggested Date (copy)_512495611509981196]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SUGGESTED_DATE] &#13;&#10;    END' />
      </column>
      <column aggregation='Count' caption='Config Filter 1' datatype='string' default-type='nominal' layered='true' name='[Suggested Date (copy)_981221801310244907]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
      </column>
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='Snowflake Details' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[_CFFC621BD5F74AC1A47F5DA01AE0557A]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <column-instance column='[Count (copy 2)_981221801228656666]' derivation='User' name='[usr:Count (copy 2)_981221801228656666:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Count (copy)_981221801228464153]' derivation='User' name='[usr:Count (copy)_981221801228464153:qk]' pivot='key' type='quantitative' />
      <drill-paths>
        <drill-path layered='true' name='Levels'>
          <field>[Calculation_526921184320745495]</field>
          <field>[Level 1 (copy)_526921184321654809]</field>
          <field>[Level 2 (copy)_526921184322031646]</field>
          <field>[Level 2 (copy) (copy)_526921184322039839]</field>
          <field>[Level 4 (copy)_526921184322166816]</field>
          <field>[Level 4 (copy) (copy)_526921184322179105]</field>
          <field>[Level 6 (copy)_2155535403012546561]</field>
        </drill-path>
      </drill-paths>
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.706977' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.293023' show-aliased-fields='true' show-hidden-fields='true' show-structure='true' />
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <datasource-dependencies datasource='Parameters'>
        <column alias='Engine output' caption='Source 1' datatype='string' name='[Choose data sources (copy)_2155535403020820482]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3&quot;'>
          <calculation class='tableau' formula='&quot;DCO 3&quot;' />
          <aliases>
            <alias key='&quot;DCO 3&quot;' value='Engine output' />
            <alias key='&quot;DCO 4&quot;' value='Post-proc output' />
            <alias key='&quot;DSE 1&quot;' value='Traditional' />
            <alias key='&quot;DSE 2&quot;' value='Next Gen' />
          </aliases>
        </column>
        <column caption='Level 2' datatype='string' name='[Level 1 (copy)_526921184321613848]' param-domain-type='list' role='measure' type='nominal' value='&quot;Segment&quot;'>
          <calculation class='tableau' formula='&quot;Segment&quot;' />
        </column>
        <column caption='Level 3' datatype='string' name='[Level 2 (copy)_526921184321875994]' param-domain-type='list' role='measure' type='nominal' value='&quot;Product&quot;'>
          <calculation class='tableau' formula='&quot;Product&quot;' />
        </column>
        <column caption='Level 5' datatype='string' name='[Level 3 (copy) (copy)_526921184321937436]' param-domain-type='list' role='measure' type='nominal' value='&quot;Rep&quot;'>
          <calculation class='tableau' formula='&quot;Rep&quot;' />
        </column>
        <column caption='Level 4' datatype='string' name='[Level 3 (copy)_526921184321929243]' param-domain-type='list' role='measure' type='nominal' value='&quot;Factor&quot;'>
          <calculation class='tableau' formula='&quot;Factor&quot;' />
        </column>
        <column caption='Level 6' datatype='string' name='[Level 5 (copy)_526921184321998877]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account&quot;'>
          <calculation class='tableau' formula='&quot;Account&quot;' />
        </column>
        <column caption='Level 7' datatype='string' name='[Level 6 (copy)_2155535403012243456]' param-domain-type='list' role='measure' type='nominal' value='&quot;Suggested Date&quot;'>
          <calculation class='tableau' formula='&quot;Suggested Date&quot;' />
        </column>
        <column caption='Level 1' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
          <calculation class='tableau' formula='&quot;Channel&quot;' />
        </column>
        <column alias='Engine output' caption='Source 2' datatype='string' name='[Source 1 (copy)_2155535403*********]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3 2&quot;'>
          <calculation class='tableau' formula='&quot;DCO 3 2&quot;' />
          <aliases>
            <alias key='&quot;DCO 3 2&quot;' value='Engine output' />
            <alias key='&quot;DCO 4 2&quot;' value='Post-proc output' />
            <alias key='&quot;DSE 1 2&quot;' value='Traditional' />
            <alias key='&quot;DSE 2 2&quot;' value='Next Gen' />
          </aliases>
        </column>
      </datasource-dependencies>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='Snowflake Details' id='_CFFC621BD5F74AC1A47F5DA01AE0557A'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
  </datasources>
  <shared-views>
    <shared-view name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y'>
      <datasources>
        <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y' />
      </datasources>
      <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y'>
        <column aggregation='Year' caption='Run Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Config (1) (copy)_2155535403100246044]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; THEN [DCO_RUN_DATE1]&#13;&#10;END' />
        </column>
        <column aggregation='Count' caption='Config Filter 1' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_2155535403097620507]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME1]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
        </column>
        <column aggregation='Count' caption='Config Filter 2' datatype='string' default-type='nominal' layered='true' name='[Config Filter (copy)_2155535403107680289]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME2]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
        </column>
        <column aggregation='Count' caption='Scenario Filter 1' datatype='string' default-type='nominal' layered='true' name='[Run Date (1) (copy)_2155535403100545053]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = [FLAG] THEN [SCENARIO_NAME1 (copy)_1347983701206401024]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
        </column>
        <column aggregation='Year' caption='Run Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Run Date Filter (copy)_21555354031********]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos; THEN [DCO_RUN_DATE2]&#13;&#10;END' />
        </column>
        <column aggregation='Count' caption='Scenario Filter 2' datatype='string' default-type='nominal' layered='true' name='[Scenario Filter (copy)_2155535403107311647]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO_NAME2 (copy)_1347983701206581249]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
        </column>
        <column-instance column='[Config (1) (copy)_2155535403100246044]' derivation='None' name='[none:Config (1) (copy)_2155535403100246044:qk]' pivot='key' type='quantitative' />
        <column-instance column='[Config (copy)_2155535403097620507]' derivation='None' name='[none:Config (copy)_2155535403097620507:nk]' pivot='key' type='nominal' />
        <column-instance column='[Config Filter (copy)_2155535403107680289]' derivation='None' name='[none:Config Filter (copy)_2155535403107680289:nk]' pivot='key' type='nominal' />
        <column-instance column='[Run Date (1) (copy)_2155535403100545053]' derivation='None' name='[none:Run Date (1) (copy)_2155535403100545053:nk]' pivot='key' type='nominal' />
        <column-instance column='[Run Date Filter (copy)_21555354031********]' derivation='None' name='[none:Run Date Filter (copy)_21555354031********:qk]' pivot='key' type='quantitative' />
        <column-instance column='[Scenario Filter (copy)_2155535403107311647]' derivation='None' name='[none:Scenario Filter (copy)_2155535403107311647:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (1) (copy)_2155535403100246044:qk]' included-values='all' />
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (copy)_2155535403097620507:nk]'>
        <groupfilter function='level-members' level='[none:Config (copy)_2155535403097620507:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config Filter (copy)_2155535403107680289:nk]'>
        <groupfilter function='level-members' level='[none:Config Filter (copy)_2155535403107680289:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date (1) (copy)_2155535403100545053:nk]'>
        <groupfilter function='level-members' level='[none:Run Date (1) (copy)_2155535403100545053:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
      <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date Filter (copy)_21555354031********:qk]' included-values='all' />
      <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Scenario Filter (copy)_2155535403107311647:nk]'>
        <groupfilter function='level-members' level='[none:Scenario Filter (copy)_2155535403107311647:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
    </shared-view>
  </shared-views>
  <worksheets>
    <worksheet name='Details'>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column alias='Engine output' caption='Source 1' datatype='string' name='[Choose data sources (copy)_2155535403020820482]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3&quot;'>
              <calculation class='tableau' formula='&quot;DCO 3&quot;' />
              <aliases>
                <alias key='&quot;DCO 3&quot;' value='Engine output' />
                <alias key='&quot;DCO 4&quot;' value='Post-proc output' />
                <alias key='&quot;DSE 1&quot;' value='Traditional' />
                <alias key='&quot;DSE 2&quot;' value='Next Gen' />
              </aliases>
            </column>
            <column caption='Level 2' datatype='string' name='[Level 1 (copy)_526921184321613848]' param-domain-type='list' role='measure' type='nominal' value='&quot;Segment&quot;'>
              <calculation class='tableau' formula='&quot;Segment&quot;' />
            </column>
            <column caption='Level 1' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
              <calculation class='tableau' formula='&quot;Channel&quot;' />
            </column>
            <column alias='Engine output' caption='Source 2' datatype='string' name='[Source 1 (copy)_2155535403*********]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3 2&quot;'>
              <calculation class='tableau' formula='&quot;DCO 3 2&quot;' />
              <aliases>
                <alias key='&quot;DCO 3 2&quot;' value='Engine output' />
                <alias key='&quot;DCO 4 2&quot;' value='Post-proc output' />
                <alias key='&quot;DSE 1 2&quot;' value='Traditional' />
                <alias key='&quot;DSE 2 2&quot;' value='Next Gen' />
              </aliases>
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah'>
            <column aggregation='Count' caption='ACCOUNT' datatype='string' default-type='nominal' layered='true' name='[ACCOUNT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Account Filter 2' datatype='string' default-type='nominal' layered='true' name='[Account (copy)_512495611506294790]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [ACCOUNT_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Segment Filter 1' datatype='string' default-type='nominal' layered='true' name='[Account Filter (copy)_981221801042362389]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [HCP_SEGMENT]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
            </column>
            <column aggregation='Year' caption='Suggested Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Account combo (copy)_981221801282629672]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SUGGESTED_DATE] &#13;&#10;    END' />
            </column>
            <column aggregation='Year' caption='Suggested Date level' datatype='date' default-type='ordinal' layered='true' name='[Account level (copy)_837106628427710466]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [SUGGESTED_DATE] &#13;&#10;END' />
            </column>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Level 1' datatype='string' default-type='nominal' layered='true' name='[Calculation_526921184320745495]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Factor level' datatype='string' default-type='nominal' layered='true' name='[Calculation_837106628427227136]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [FACTOR_NAME] &#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Channel Filter 1' datatype='string' default-type='nominal' layered='true' name='[Calculation_981221801239351324]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [CHANNEL] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Channel Filter 2' datatype='string' default-type='nominal' layered='true' name='[Channel (copy)_512495611506069509]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [CHANNEL]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Product Filter 1' datatype='string' default-type='nominal' layered='true' name='[Channel combo (copy)_981221801243471901]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [PRODUCT_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Account Filter 1' datatype='string' default-type='nominal' layered='true' name='[Channel combo (copy)_981221801280938018]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [ACCOUNT_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Config Filter 2' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_512495611509768203]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Scenario Filter 1' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_981221801310863404]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
            </column>
            <column aggregation='Year' caption='Run Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Config (copy)_981221801311191085]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [DCO_RUN_DATE]&#13;&#10;    END' />
            </column>
            <column aggregation='User' caption='Count Source 2' datatype='integer' default-type='quantitative' layered='true' name='[Count (copy 2)_981221801228656666]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)' />
            </column>
            <column aggregation='User' caption='Count Source 1' datatype='integer' default-type='quantitative' layered='true' name='[Count (copy)_981221801228464153]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)' />
            </column>
            <column aggregation='Year' caption='RUNDATE' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='FACTOR' datatype='string' default-type='nominal' layered='true' name='[FACTOR_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[FLAG]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Factor Filter 2' datatype='string' default-type='nominal' layered='true' name='[Factor (copy)_512495611506483207]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [FACTOR_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Account level' datatype='string' default-type='nominal' layered='true' name='[Factor level (copy)_837106628427595777]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [ACCOUNT_NAME] &#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Product level' datatype='string' default-type='nominal' layered='true' name='[Factor level (copy)_837106628427808771]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [PRODUCT_NAME] &#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Rep level ' datatype='string' default-type='nominal' layered='true' name='[Factor level (copy)_837106628428029957]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [REP_TEAM_NAME] &#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Segment level' datatype='string' default-type='nominal' layered='true' name='[Factor level (copy)_837106628428517382]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [HCP_SEGMENT]&#13;&#10;END' />
            </column>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[HCP_SEGMENT]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Level 2' datatype='string' default-type='nominal' layered='true' name='[Level 1 (copy)_526921184321654809]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Level 1 (copy)_526921184321613848]&#13;&#10;WHEN &apos;Factor&apos; THEN [Calculation_837106628427227136]&#13;&#10;WHEN &apos;Account&apos; THEN [Factor level (copy)_837106628427595777]&#13;&#10;WHEN &apos;Channel&apos; THEN [Segment level (copy)_837106628427919364]&#13;&#10;WHEN &apos;Product&apos; THEN [Factor level (copy)_837106628427808771]&#13;&#10;WHEN &apos;Rep&apos; THEN [Factor level (copy)_837106628428029957]&#13;&#10;WHEN &apos;Suggested Date&apos;&#13;&#10;THEN STR(MONTH([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(DAY([Account level (copy)_837106628427710466]))&#13;&#10;+&apos;/&apos;+STR(YEAR([Account level (copy)_837106628427710466]))&#13;&#10;WHEN &apos;Segment&apos; THEN [Factor level (copy)_837106628428517382]&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='PRODUCT' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Product Filter 2' datatype='string' default-type='nominal' layered='true' name='[Product (copy)_512495611509055496]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [PRODUCT_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Factor Filter 1' datatype='string' default-type='nominal' layered='true' name='[Product combo  (copy)_981221801281835044]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [FACTOR_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Rep Filter 1' datatype='string' default-type='nominal' layered='true' name='[Product combo  (copy)_981221801282273318]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [REP_TEAM_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='REP' datatype='string' default-type='nominal' layered='true' name='[REP_TEAM_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Rep Filter 2' datatype='string' default-type='nominal' layered='true' name='[Rep (copy)_512495611509235721]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [REP_TEAM_NAME]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
            </column>
            <column aggregation='Year' caption='Run Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Run Date  (copy)_512495611510464526]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [DCO_RUN_DATE]&#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='SCENARIO NEW' datatype='string' default-type='nominal' layered='true' name='[SCENARIO (copy)_1347983701206884354]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' caption='SCENARIO' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='CONFIG' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='SuggestedDATE' datatype='date' default-type='ordinal' layered='true' name='[SUGGESTED_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTIONCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Filter 2' datatype='string' default-type='nominal' layered='true' name='[Scenario (copy)_512495611510235149]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Segment Filter 2' datatype='string' default-type='nominal' layered='true' name='[Segment (copy)_512495611509411850]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [HCP_SEGMENT]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Channel level' datatype='string' default-type='nominal' layered='true' name='[Segment level (copy)_837106628427919364]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; OR [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos;&#13;&#10;THEN [CHANNEL] &#13;&#10;END' />
            </column>
            <column aggregation='Year' caption='Suggested Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Suggested Date (copy)_512495611509981196]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SUGGESTED_DATE] &#13;&#10;    END' />
            </column>
            <column aggregation='Count' caption='Config Filter 1' datatype='string' default-type='nominal' layered='true' name='[Suggested Date (copy)_981221801310244907]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
            </column>
            <column-instance column='[Account (copy)_512495611506294790]' derivation='None' name='[none:Account (copy)_512495611506294790:nk]' pivot='key' type='nominal' />
            <column-instance column='[Account Filter (copy)_981221801042362389]' derivation='None' name='[none:Account Filter (copy)_981221801042362389:nk]' pivot='key' type='nominal' />
            <column-instance column='[Account combo (copy)_981221801282629672]' derivation='None' name='[none:Account combo (copy)_981221801282629672:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Calculation_526921184320745495]' derivation='None' name='[none:Calculation_526921184320745495:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_981221801239351324]' derivation='None' name='[none:Calculation_981221801239351324:nk]' pivot='key' type='nominal' />
            <column-instance column='[Channel (copy)_512495611506069509]' derivation='None' name='[none:Channel (copy)_512495611506069509:nk]' pivot='key' type='nominal' />
            <column-instance column='[Channel combo (copy)_981221801243471901]' derivation='None' name='[none:Channel combo (copy)_981221801243471901:nk]' pivot='key' type='nominal' />
            <column-instance column='[Channel combo (copy)_981221801280938018]' derivation='None' name='[none:Channel combo (copy)_981221801280938018:nk]' pivot='key' type='nominal' />
            <column-instance column='[Config (copy)_512495611509768203]' derivation='None' name='[none:Config (copy)_512495611509768203:nk]' pivot='key' type='nominal' />
            <column-instance column='[Config (copy)_981221801310863404]' derivation='None' name='[none:Config (copy)_981221801310863404:nk]' pivot='key' type='nominal' />
            <column-instance column='[Config (copy)_981221801311191085]' derivation='None' name='[none:Config (copy)_981221801311191085:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Factor (copy)_512495611506483207]' derivation='None' name='[none:Factor (copy)_512495611506483207:nk]' pivot='key' type='nominal' />
            <column-instance column='[Level 1 (copy)_526921184321654809]' derivation='None' name='[none:Level 1 (copy)_526921184321654809:nk]' pivot='key' type='nominal' />
            <column-instance column='[Product (copy)_512495611509055496]' derivation='None' name='[none:Product (copy)_512495611509055496:nk]' pivot='key' type='nominal' />
            <column-instance column='[Product combo  (copy)_981221801281835044]' derivation='None' name='[none:Product combo  (copy)_981221801281835044:nk]' pivot='key' type='nominal' />
            <column-instance column='[Product combo  (copy)_981221801282273318]' derivation='None' name='[none:Product combo  (copy)_981221801282273318:nk]' pivot='key' type='nominal' />
            <column-instance column='[Rep (copy)_512495611509235721]' derivation='None' name='[none:Rep (copy)_512495611509235721:nk]' pivot='key' type='nominal' />
            <column-instance column='[Run Date  (copy)_512495611510464526]' derivation='None' name='[none:Run Date  (copy)_512495611510464526:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Scenario (copy)_512495611510235149]' derivation='None' name='[none:Scenario (copy)_512495611510235149:nk]' pivot='key' type='nominal' />
            <column-instance column='[Segment (copy)_512495611509411850]' derivation='None' name='[none:Segment (copy)_512495611509411850:nk]' pivot='key' type='nominal' />
            <column-instance column='[Suggested Date (copy)_512495611509981196]' derivation='None' name='[none:Suggested Date (copy)_512495611509981196:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Suggested Date (copy)_981221801310244907]' derivation='None' name='[none:Suggested Date (copy)_981221801310244907:nk]' pivot='key' type='nominal' />
            <column-instance column='[Count (copy 2)_981221801228656666]' derivation='User' name='[usr:Count (copy 2)_981221801228656666:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Count (copy)_981221801228464153]' derivation='User' name='[usr:Count (copy)_981221801228464153:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[:Measure Names]'>
            <groupfilter function='union' user:op='manual'>
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[usr:Count (copy)_981221801228464153:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[usr:Count (copy 2)_981221801228656666:qk]&quot;' />
            </groupfilter>
          </filter>
          <manual-sort column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[:Measure Names]' direction='ASC'>
            <dictionary>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[usr:Count (copy)_981221801228464153:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[usr:Count (copy 2)_981221801228656666:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[usr:Count Source 1 (copy)_981221801306689577:qk]&quot;</bucket>
            </dictionary>
          </manual-sort>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account (copy)_512495611506294790:nk]'>
            <groupfilter function='level-members' level='[none:Account (copy)_512495611506294790:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account Filter (copy)_981221801042362389:nk]'>
            <groupfilter function='level-members' level='[none:Account Filter (copy)_981221801042362389:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account combo (copy)_981221801282629672:qk]' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Calculation_981221801239351324:nk]'>
            <groupfilter function='level-members' level='[none:Calculation_981221801239351324:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel (copy)_512495611506069509:nk]'>
            <groupfilter function='level-members' level='[none:Channel (copy)_512495611506069509:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel combo (copy)_981221801243471901:nk]'>
            <groupfilter function='level-members' level='[none:Channel combo (copy)_981221801243471901:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel combo (copy)_981221801280938018:nk]'>
            <groupfilter function='level-members' level='[none:Channel combo (copy)_981221801280938018:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_512495611509768203:nk]'>
            <groupfilter function='level-members' level='[none:Config (copy)_512495611509768203:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801310863404:nk]'>
            <groupfilter function='level-members' level='[none:Config (copy)_981221801310863404:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801311191085:qk]' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Factor (copy)_512495611506483207:nk]'>
            <groupfilter function='level-members' level='[none:Factor (copy)_512495611506483207:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product (copy)_512495611509055496:nk]'>
            <groupfilter function='level-members' level='[none:Product (copy)_512495611509055496:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product combo  (copy)_981221801281835044:nk]'>
            <groupfilter function='level-members' level='[none:Product combo  (copy)_981221801281835044:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product combo  (copy)_981221801282273318:nk]'>
            <groupfilter function='level-members' level='[none:Product combo  (copy)_981221801282273318:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Rep (copy)_512495611509235721:nk]'>
            <groupfilter function='level-members' level='[none:Rep (copy)_512495611509235721:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Run Date  (copy)_512495611510464526:qk]' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Scenario (copy)_512495611510235149:nk]'>
            <groupfilter function='level-members' level='[none:Scenario (copy)_512495611510235149:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Segment (copy)_512495611509411850:nk]'>
            <groupfilter function='level-members' level='[none:Segment (copy)_512495611509411850:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_512495611509981196:qk]' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_981221801310244907:nk]'>
            <groupfilter function='level-members' level='[none:Suggested Date (copy)_981221801310244907:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[:Measure Names]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel combo (copy)_981221801280938018:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account (copy)_512495611506294790:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Calculation_981221801239351324:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel (copy)_512495611506069509:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product combo  (copy)_981221801281835044:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Factor (copy)_512495611506483207:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel combo (copy)_981221801243471901:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product (copy)_512495611509055496:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product combo  (copy)_981221801282273318:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Rep (copy)_512495611509235721:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801311191085:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Run Date  (copy)_512495611510464526:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account Filter (copy)_981221801042362389:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Segment (copy)_512495611509411850:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account combo (copy)_981221801282629672:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_512495611509981196:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_981221801310244907:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_512495611509768203:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801310863404:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Scenario (copy)_512495611510235149:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[:Measure Names]' value='116' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[usr:Count (copy 2)_981221801228656666:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[usr:Count (copy)_981221801228464153:qk]' value='n#,##0;-#,##0' />
            <format attr='font-weight' data-class='total' value='bold' />
          </style-rule>
          <style-rule element='header'>
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[:Measure Names]' value='24' />
            <format attr='font-weight' data-class='total' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Calculation_526921184320745495:nk]' value='bold' />
          </style-rule>
          <style-rule element='label'>
            <format attr='font-weight' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Calculation_526921184320745495:nk]' value='normal' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Run Date  (copy)_512495611510464526:qk]' value='Run Date'>
              <formatted-text>
                <run>Run Date</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_512495611509768203:nk]' value='Config'>
              <formatted-text>
                <run>Config</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Scenario (copy)_512495611510235149:nk]' value='Scenario'>
              <formatted-text>
                <run>Scenario</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel (copy)_512495611506069509:nk]' value='Channel'>
              <formatted-text>
                <run>Channel</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Segment (copy)_512495611509411850:nk]' value='Segment'>
              <formatted-text>
                <run>Segment</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product (copy)_512495611509055496:nk]' value='Product'>
              <formatted-text>
                <run>Product</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Factor (copy)_512495611506483207:nk]' value='Factor'>
              <formatted-text>
                <run>Factor</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Rep (copy)_512495611509235721:nk]' value='Rep'>
              <formatted-text>
                <run>Rep</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account (copy)_512495611506294790:nk]' value='Account'>
              <formatted-text>
                <run>Account</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_512495611509981196:qk]' value='Suggested Date'>
              <formatted-text>
                <run>Suggested Date</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801311191085:qk]' value='Run Date'>
              <formatted-text>
                <run>Run Date</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_981221801310244907:nk]' value='Config'>
              <formatted-text>
                <run>Config</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801310863404:nk]' value='Scenario'>
              <formatted-text>
                <run>Scenario</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Calculation_981221801239351324:nk]' value='Channel'>
              <formatted-text>
                <run>Channel</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account Filter (copy)_981221801042362389:nk]' value='Segment'>
              <formatted-text>
                <run>Segment</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel combo (copy)_981221801243471901:nk]' value='Product'>
              <formatted-text>
                <run>Product</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product combo  (copy)_981221801281835044:nk]' value='Factor'>
              <formatted-text>
                <run>Factor</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product combo  (copy)_981221801282273318:nk]' value='Rep'>
              <formatted-text>
                <run>Rep</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel combo (copy)_981221801280938018:nk]' value='Account'>
              <formatted-text>
                <run>Account</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account combo (copy)_981221801282629672:qk]' value='Suggested Date'>
              <formatted-text>
                <run>Suggested Date</run>
              </formatted-text>
            </format>
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[Multiple Values]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878'>Level 1:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Calculation_526921184320745495:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Level 2:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Level 1 (copy)_526921184321654809:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Level 3:&#9;</run>
                <run bold='true' fontcolor='#ff0000'>&lt;!Missing Field!&gt;</run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Level 4:&#9;</run>
                <run bold='true' fontcolor='#ff0000'>&lt;!Missing Field!&gt;</run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Level 5:&#9;</run>
                <run bold='true' fontcolor='#ff0000'>&lt;!Missing Field!&gt;</run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Level 6:&#9;</run>
                <run bold='true' fontcolor='#ff0000'>&lt;!Missing Field!&gt;</run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Level 7:&#9;</run>
                <run bold='true' fontcolor='#ff0000'>&lt;!Missing Field!&gt;</run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[:Measure Names]>:	]]></run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[Multiple Values]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows onTop='true' total='true'>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Calculation_526921184320745495:nk] / [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Level 1 (copy)_526921184321654809:nk])</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[:Measure Names]</cols>
        <tooltip-style tooltip-mode='none' />
      </table>
      <simple-id uuid='{A65FEAA7-7240-4B2E-BF53-C9B40885922D}' />
    </worksheet>
    <worksheet name='Summary'>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column alias='Engine output' caption='Source 1' datatype='string' name='[Choose data sources (copy)_2155535403020820482]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3&quot;'>
              <calculation class='tableau' formula='&quot;DCO 3&quot;' />
              <aliases>
                <alias key='&quot;DCO 3&quot;' value='Engine output' />
                <alias key='&quot;DCO 4&quot;' value='Post-proc output' />
                <alias key='&quot;DSE 1&quot;' value='Traditional' />
                <alias key='&quot;DSE 2&quot;' value='Next Gen' />
              </aliases>
            </column>
            <column alias='Engine output' caption='Source 2' datatype='string' name='[Source 1 (copy)_2155535403*********]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3 2&quot;'>
              <calculation class='tableau' formula='&quot;DCO 3 2&quot;' />
              <aliases>
                <alias key='&quot;DCO 3 2&quot;' value='Engine output' />
                <alias key='&quot;DCO 4 2&quot;' value='Post-proc output' />
                <alias key='&quot;DSE 1 2&quot;' value='Traditional' />
                <alias key='&quot;DSE 2 2&quot;' value='Next Gen' />
              </aliases>
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y'>
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[ACCOUNTCHANNELCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[ACCOUNTCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='User' caption='AVERAGE Suggestion Per REP' datatype='real' default-type='quantitative' layered='true' name='[Average Suggestion Per Rep  (copy)_526921183993229317]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='ROUND(SUM([SUGGESTIONCOUNT])/SUM([REPCOUNT]), 2)' />
            </column>
            <column aggregation='Year' caption='Run Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Config (1) (copy)_2155535403100246044]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; THEN [DCO_RUN_DATE1]&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Config Filter 1' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_2155535403097620507]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME1]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Config Filter 2' datatype='string' default-type='nominal' layered='true' name='[Config Filter (copy)_2155535403107680289]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME2]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
            </column>
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE1]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE2]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[FACTORCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[FLAG]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <aliases>
                <alias key='&quot;DCO 3&quot;' value='Source 1' />
                <alias key='&quot;DCO 3 2&quot;' value='Source 2' />
              </aliases>
            </column>
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[REPCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Filter 1' datatype='string' default-type='nominal' layered='true' name='[Run Date (1) (copy)_2155535403100545053]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = [FLAG] THEN [SCENARIO_NAME1 (copy)_1347983701206401024]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
            </column>
            <column aggregation='Year' caption='Run Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Run Date Filter (copy)_21555354031********]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos; THEN [DCO_RUN_DATE2]&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='SCENARIO NEW' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME1 (copy)_1347983701206401024]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME1]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME1]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='SCENARIO NEW 2' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME2 (copy)_1347983701206581249]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME2]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME2]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME1]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME2]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTIONCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Filter 2' datatype='string' default-type='nominal' layered='true' name='[Scenario Filter (copy)_2155535403107311647]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO_NAME2 (copy)_1347983701206581249]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
            </column>
            <column-instance column='[Config (1) (copy)_2155535403100246044]' derivation='None' name='[none:Config (1) (copy)_2155535403100246044:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Config (copy)_2155535403097620507]' derivation='None' name='[none:Config (copy)_2155535403097620507:nk]' pivot='key' type='nominal' />
            <column-instance column='[Config Filter (copy)_2155535403107680289]' derivation='None' name='[none:Config Filter (copy)_2155535403107680289:nk]' pivot='key' type='nominal' />
            <column-instance column='[FLAG]' derivation='None' name='[none:FLAG:nk]' pivot='key' type='nominal' />
            <column-instance column='[Run Date (1) (copy)_2155535403100545053]' derivation='None' name='[none:Run Date (1) (copy)_2155535403100545053:nk]' pivot='key' type='nominal' />
            <column-instance column='[Run Date Filter (copy)_21555354031********]' derivation='None' name='[none:Run Date Filter (copy)_21555354031********:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Scenario Filter (copy)_2155535403107311647]' derivation='None' name='[none:Scenario Filter (copy)_2155535403107311647:nk]' pivot='key' type='nominal' />
            <column-instance column='[ACCOUNTCHANNELCOUNT]' derivation='Sum' name='[sum:ACCOUNTCHANNELCOUNT:qk]' pivot='key' type='quantitative' />
            <column-instance column='[ACCOUNTCOUNT]' derivation='Sum' name='[sum:ACCOUNTCOUNT:qk]' pivot='key' type='quantitative' />
            <column-instance column='[FACTORCOUNT]' derivation='Sum' name='[sum:FACTORCOUNT:qk]' pivot='key' type='quantitative' />
            <column-instance column='[REPCOUNT]' derivation='Sum' name='[sum:REPCOUNT:qk]' pivot='key' type='quantitative' />
            <column-instance column='[SUGGESTIONCOUNT]' derivation='Sum' name='[sum:SUGGESTIONCOUNT:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Average Suggestion Per Rep  (copy)_526921183993229317]' derivation='User' name='[usr:Average Suggestion Per Rep  (copy)_526921183993229317:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]'>
            <groupfilter function='union' user:op='manual'>
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:SUGGESTIONCOUNT:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:REPCOUNT:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:ACCOUNTCOUNT:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:ACCOUNTCHANNELCOUNT:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:FACTORCOUNT:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:Average Suggestion Per Rep  (copy)_526921183993229317:qk]&quot;' />
            </groupfilter>
          </filter>
          <manual-sort column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]' direction='ASC'>
            <dictionary>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:SUGGESTIONCOUNT:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:Calculation_1439181579300421633:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:REPCOUNT:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:ACCOUNTCOUNT:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:ACCOUNTCHANNELCOUNT:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:FACTORCOUNT:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:INSIGHT_COUNT:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:ENH_INSIGHT_COUNT:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:suggestionCount1:qk]&quot;</bucket>
            </dictionary>
          </manual-sort>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (1) (copy)_2155535403100246044:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (copy)_2155535403097620507:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date (1) (copy)_2155535403100545053:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Scenario Filter (copy)_2155535403107311647:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date Filter (copy)_21555354031********:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config Filter (copy)_2155535403107680289:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:ACCOUNTCHANNELCOUNT:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:ACCOUNTCOUNT:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:ENH_INSIGHT_COUNT:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:FACTORCOUNT:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:INSIGHT_COUNT:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:REPCOUNT:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[sum:SUGGESTIONCOUNT:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:Average Suggestion Per Rep  (copy)_526921183993229317:qk]' value='n#,##0.00;-#,##0.00' />
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]' value='21' />
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:FLAG:nk]' value='102' />
          </style-rule>
          <style-rule element='header'>
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]' value='276' />
          </style-rule>
          <style-rule element='label'>
            <format attr='display' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]' value='true' />
            <format attr='display' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:FLAG:nk]' value='true' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='table-div'>
            <format attr='stroke-size' scope='cols' value='0' />
            <format attr='line-visibility' scope='cols' value='off' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (1) (copy)_2155535403100246044:qk]' value='Run Date'>
              <formatted-text>
                <run>Run Date</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (copy)_2155535403097620507:nk]' value='Config'>
              <formatted-text>
                <run>Config</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date (1) (copy)_2155535403100545053:nk]' value='Scenario'>
              <formatted-text>
                <run>Scenario</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date Filter (copy)_21555354031********:qk]' value='Run Date'>
              <formatted-text>
                <run>Run Date</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config Filter (copy)_2155535403107680289:nk]' value='Config'>
              <formatted-text>
                <run>Config</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Scenario Filter (copy)_2155535403107311647:nk]' value='Scenario'>
              <formatted-text>
                <run>Scenario</run>
              </formatted-text>
            </format>
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[Multiple Values]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
                <format attr='minheight' value='-1' />
                <format attr='maxheight' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:FLAG:nk]</cols>
        <tooltip-style tooltip-mode='none' />
      </table>
      <simple-id uuid='{F43E776F-7B5D-4091-8C3B-C3F17DB89CE7}' />
    </worksheet>
    <worksheet name='Summary Diff'>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column alias='Engine output' caption='Source 1' datatype='string' name='[Choose data sources (copy)_2155535403020820482]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3&quot;'>
              <calculation class='tableau' formula='&quot;DCO 3&quot;' />
              <aliases>
                <alias key='&quot;DCO 3&quot;' value='Engine output' />
                <alias key='&quot;DCO 4&quot;' value='Post-proc output' />
                <alias key='&quot;DSE 1&quot;' value='Traditional' />
                <alias key='&quot;DSE 2&quot;' value='Next Gen' />
              </aliases>
            </column>
            <column alias='Engine output' caption='Source 2' datatype='string' name='[Source 1 (copy)_2155535403*********]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3 2&quot;'>
              <calculation class='tableau' formula='&quot;DCO 3 2&quot;' />
              <aliases>
                <alias key='&quot;DCO 3 2&quot;' value='Engine output' />
                <alias key='&quot;DCO 4 2&quot;' value='Post-proc output' />
                <alias key='&quot;DSE 1 2&quot;' value='Traditional' />
                <alias key='&quot;DSE 2 2&quot;' value='Next Gen' />
              </aliases>
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y'>
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[ACCOUNTCHANNELCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[ACCOUNTCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Difference' datatype='string' default-type='nominal' layered='true' name='[Calculation_1439181579318042630]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='&apos;Difference&apos;' />
            </column>
            <column aggregation='Year' caption='Run Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Config (1) (copy)_2155535403100246044]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; THEN [DCO_RUN_DATE1]&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Config Filter 1' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_2155535403097620507]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME1]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Config Filter 2' datatype='string' default-type='nominal' layered='true' name='[Config Filter (copy)_2155535403107680289]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME2]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
            </column>
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE1]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE2]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='User' caption='DIFF Factors fired Count' datatype='integer' default-type='quantitative' layered='true' name='[DIFF 1 Factors fired Count (copy)_2155535403060961294]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([FACTORCOUNT]) END)&#13;&#10;&#13;&#10;-&#13;&#10;&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([FACTORCOUNT]) END)' />
            </column>
            <column aggregation='User' caption='DIFF Suggested Account Count' datatype='integer' default-type='quantitative' layered='true' name='[DIFF 1 Suggested Account Count (copy)_2155535403082145812]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([ACCOUNTCOUNT]) END)' />
            </column>
            <column aggregation='User' caption='DIFF Suggested Account/Channel Count' datatype='integer' default-type='quantitative' layered='true' name='[DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCHANNELCOUNT])END)  &#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([ACCOUNTCHANNELCOUNT])END)' />
            </column>
            <column aggregation='User' caption='DIFF Suggested Count' datatype='integer' default-type='quantitative' layered='true' name='[DIFF 1 Suggested Count (copy)_2155535403081334802]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)' />
            </column>
            <column aggregation='User' caption='DIFF Suggested Rep Count' datatype='integer' default-type='quantitative' layered='true' name='[DIFF 1 Suggested Rep Count (copy)_2155535403081949203]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([REPCOUNT]) END)' />
            </column>
            <column aggregation='User' caption='DIFF Average Suggestion Per Rep' datatype='real' default-type='quantitative' layered='true' name='[DIFF Average Suggestion Per Rep (copy)_122160172807217152]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='ROUND( SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10; /&#13;&#10;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END), 2)&#13;&#10;&#13;&#10;-&#13;&#10;&#13;&#10;ROUND(SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10; /&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403*********] THEN ([REPCOUNT]) END)&#13;&#10;, 2)' />
            </column>
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[FACTORCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[FLAG]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <aliases>
                <alias key='&quot;DCO 3&quot;' value='Source 1' />
                <alias key='&quot;DCO 3 2&quot;' value='Source 2' />
              </aliases>
            </column>
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[REPCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Filter 1' datatype='string' default-type='nominal' layered='true' name='[Run Date (1) (copy)_2155535403100545053]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = [FLAG] THEN [SCENARIO_NAME1 (copy)_1347983701206401024]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
            </column>
            <column aggregation='Year' caption='Run Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Run Date Filter (copy)_21555354031********]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos; THEN [DCO_RUN_DATE2]&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='SCENARIO NEW' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME1 (copy)_1347983701206401024]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME1]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME1]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='SCENARIO NEW 2' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME2 (copy)_1347983701206581249]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME2]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME2]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME1]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME2]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTIONCOUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Filter 2' datatype='string' default-type='nominal' layered='true' name='[Scenario Filter (copy)_2155535403107311647]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO_NAME2 (copy)_1347983701206581249]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
            </column>
            <column-instance column='[Calculation_1439181579318042630]' derivation='None' name='[none:Calculation_1439181579318042630:nk]' pivot='key' type='nominal' />
            <column-instance column='[Config (1) (copy)_2155535403100246044]' derivation='None' name='[none:Config (1) (copy)_2155535403100246044:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Config (copy)_2155535403097620507]' derivation='None' name='[none:Config (copy)_2155535403097620507:nk]' pivot='key' type='nominal' />
            <column-instance column='[Config Filter (copy)_2155535403107680289]' derivation='None' name='[none:Config Filter (copy)_2155535403107680289:nk]' pivot='key' type='nominal' />
            <column-instance column='[Run Date (1) (copy)_2155535403100545053]' derivation='None' name='[none:Run Date (1) (copy)_2155535403100545053:nk]' pivot='key' type='nominal' />
            <column-instance column='[Run Date Filter (copy)_21555354031********]' derivation='None' name='[none:Run Date Filter (copy)_21555354031********:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Scenario Filter (copy)_2155535403107311647]' derivation='None' name='[none:Scenario Filter (copy)_2155535403107311647:nk]' pivot='key' type='nominal' />
            <column-instance column='[DIFF 1 Factors fired Count (copy)_2155535403060961294]' derivation='User' name='[usr:DIFF 1 Factors fired Count (copy)_2155535403060961294:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DIFF 1 Suggested Account Count (copy)_2155535403082145812]' derivation='User' name='[usr:DIFF 1 Suggested Account Count (copy)_2155535403082145812:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101]' derivation='User' name='[usr:DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DIFF 1 Suggested Count (copy)_2155535403081334802]' derivation='User' name='[usr:DIFF 1 Suggested Count (copy)_2155535403081334802:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DIFF 1 Suggested Rep Count (copy)_2155535403081949203]' derivation='User' name='[usr:DIFF 1 Suggested Rep Count (copy)_2155535403081949203:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DIFF Average Suggestion Per Rep (copy)_122160172807217152]' derivation='User' name='[usr:DIFF Average Suggestion Per Rep (copy)_122160172807217152:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]'>
            <groupfilter function='union' user:op='manual'>
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Count (copy)_2155535403081334802:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Rep Count (copy)_2155535403081949203:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Account Count (copy)_2155535403082145812:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Factors fired Count (copy)_2155535403060961294:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF Average Suggestion Per Rep (copy)_122160172807217152:qk]&quot;' />
            </groupfilter>
          </filter>
          <manual-sort column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]' direction='ASC'>
            <dictionary>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Count (copy)_2155535403081334802:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Rep Count (copy)_2155535403081949203:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Account Count (copy)_2155535403082145812:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Factors fired Count (copy)_2155535403060961294:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF Average Suggestion Per Rep (copy)_122160172807217152:qk]&quot;</bucket>
              <bucket>&quot;[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Average Suggestion Per Rep (copy)_2155535403082465302:qk]&quot;</bucket>
            </dictionary>
          </manual-sort>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (copy)_2155535403097620507:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config Filter (copy)_2155535403107680289:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date (1) (copy)_2155535403100545053:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Scenario Filter (copy)_2155535403107311647:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (1) (copy)_2155535403100246044:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date Filter (copy)_21555354031********:qk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='width' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Calculation_1439181579318042630:nk]' value='102' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Factors fired Count (copy)_2155535403060961294:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Account Count (copy)_2155535403082145812:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Count (copy)_2155535403081334802:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Suggested Rep Count (copy)_2155535403081949203:qk]' value='n#,##0;-#,##0' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF 1 Average Suggestion Per Rep (copy)_2155535403082465302:qk]' value='n#,##0.00;-#,##0.00' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[usr:DIFF Average Suggestion Per Rep (copy)_122160172807217152:qk]' value='n#,##0.00;-#,##0.00' />
          </style-rule>
          <style-rule element='header'>
            <format attr='band-color' scope='rows' value='#00000000' />
          </style-rule>
          <style-rule element='label'>
            <format attr='display' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]' value='false' />
          </style-rule>
          <style-rule element='mark'>
            <encoding attr='color' center='0.0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[Multiple Values]' num-steps='2' type='custom-interpolated'>
              <color-palette custom='true' name='' type='ordered-diverging'>
                <color>#9e3d22</color>
                <color>#ad4523</color>
                <color>#bc4d23</color>
                <color>#cb5522</color>
                <color>#d95e21</color>
                <color>#e76820</color>
                <color>#ee7725</color>
                <color>#f58a30</color>
                <color>#fa9e3f</color>
                <color>#fdb053</color>
                <color>#ffc171</color>
                <color>#d9d5c9</color>
                <color>#c1d4c1</color>
                <color>#aad0aa</color>
                <color>#94cc94</color>
                <color>#7fc77f</color>
                <color>#6ac36a</color>
                <color>#56bf56</color>
                <color>#44bb44</color>
                <color>#31b631</color>
                <color>#20b220</color>
                <color>#0fae0f</color>
                <color>#00aa00</color>
              </color-palette>
            </encoding>
          </style-rule>
          <style-rule element='pane'>
            <format attr='band-color' scope='rows' value='#f5f5f5' />
          </style-rule>
          <style-rule element='table'>
            <format attr='band-size' scope='rows' value='1' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
        </style>
        <panes>
          <pane id='1' selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Text' />
            <encodings>
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[Multiple Values]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[:Measure Names]</rows>
        <cols>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Calculation_1439181579318042630:nk]</cols>
        <tooltip-style tooltip-mode='none' />
      </table>
      <simple-id uuid='{3B9B33C3-FB5D-49DB-B4A7-B7A0A220226A}' />
    </worksheet>
  </worksheets>
  <dashboards>
    <dashboard _.fcp.AccessibleZoneTabOrder.true...enable-sort-zone-taborder='true' name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Comparison report'>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontname='Tableau Medium'>&lt;Sheet Name&gt;</run>
          </formatted-text>
        </title>
      </layout-options>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputComparisonreport/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputComparisonreport?rev=' id='8000908' path='/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputComparisonreport' revision='' site='${TABLEAU_SITE}' />
      <style>
        <style-rule element='dash-text'>
          <format attr='vertical-align' id='dash-text_713' value='top' />
          <format attr='vertical-align' id='dash-text_712' value='top' />
          <format attr='vertical-align' id='dash-text_823' value='top' />
          <format attr='vertical-align' id='dash-text_824' value='top' />
        </style-rule>
      </style>
      <size maxheight='900' maxwidth='1500' minheight='900' minwidth='1500' preset-index='8' sizing-mode='fixed' />
      <datasources>
        <datasource name='Parameters' />
        <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y' />
        <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Details Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah' />
      </datasources>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Level 2' datatype='string' name='[Level 1 (copy)_526921184321613848]' param-domain-type='list' role='measure' type='nominal' value='&quot;Segment&quot;'>
          <calculation class='tableau' formula='&quot;Segment&quot;' />
          <members>
            <member value='&quot;Account&quot;' />
            <member value='&quot;Channel&quot;' />
            <member value='&quot;Factor&quot;' />
            <member value='&quot;Product&quot;' />
            <member value='&quot;Rep&quot;' />
            <member value='&quot;Suggested Date&quot;' />
            <member value='&quot;Segment&quot;' />
          </members>
        </column>
        <column caption='Level 3' datatype='string' name='[Level 2 (copy)_526921184321875994]' param-domain-type='list' role='measure' type='nominal' value='&quot;Product&quot;'>
          <calculation class='tableau' formula='&quot;Product&quot;' />
          <members>
            <member value='&quot;Account&quot;' />
            <member value='&quot;Channel&quot;' />
            <member value='&quot;Factor&quot;' />
            <member value='&quot;Product&quot;' />
            <member value='&quot;Rep&quot;' />
            <member value='&quot;Suggested Date&quot;' />
            <member value='&quot;Segment&quot;' />
          </members>
        </column>
        <column caption='Level 5' datatype='string' name='[Level 3 (copy) (copy)_526921184321937436]' param-domain-type='list' role='measure' type='nominal' value='&quot;Rep&quot;'>
          <calculation class='tableau' formula='&quot;Rep&quot;' />
          <members>
            <member value='&quot;Account&quot;' />
            <member value='&quot;Channel&quot;' />
            <member value='&quot;Factor&quot;' />
            <member value='&quot;Product&quot;' />
            <member value='&quot;Rep&quot;' />
            <member value='&quot;Suggested Date&quot;' />
            <member value='&quot;Segment&quot;' />
          </members>
        </column>
        <column caption='Level 4' datatype='string' name='[Level 3 (copy)_526921184321929243]' param-domain-type='list' role='measure' type='nominal' value='&quot;Factor&quot;'>
          <calculation class='tableau' formula='&quot;Factor&quot;' />
          <members>
            <member value='&quot;Account&quot;' />
            <member value='&quot;Channel&quot;' />
            <member value='&quot;Factor&quot;' />
            <member value='&quot;Product&quot;' />
            <member value='&quot;Rep&quot;' />
            <member value='&quot;Suggested Date&quot;' />
            <member value='&quot;Segment&quot;' />
          </members>
        </column>
        <column caption='Level 6' datatype='string' name='[Level 5 (copy)_526921184321998877]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account&quot;'>
          <calculation class='tableau' formula='&quot;Account&quot;' />
          <members>
            <member value='&quot;Account&quot;' />
            <member value='&quot;Channel&quot;' />
            <member value='&quot;Factor&quot;' />
            <member value='&quot;Product&quot;' />
            <member value='&quot;Rep&quot;' />
            <member value='&quot;Suggested Date&quot;' />
            <member value='&quot;Segment&quot;' />
          </members>
        </column>
        <column caption='Level 7' datatype='string' name='[Level 6 (copy)_2155535403012243456]' param-domain-type='list' role='measure' type='nominal' value='&quot;Suggested Date&quot;'>
          <calculation class='tableau' formula='&quot;Suggested Date&quot;' />
          <members>
            <member value='&quot;Account&quot;' />
            <member value='&quot;Channel&quot;' />
            <member value='&quot;Factor&quot;' />
            <member value='&quot;Product&quot;' />
            <member value='&quot;Rep&quot;' />
            <member value='&quot;Suggested Date&quot;' />
            <member value='&quot;Segment&quot;' />
          </members>
        </column>
        <column caption='Level 1' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
          <calculation class='tableau' formula='&quot;Channel&quot;' />
          <members>
            <member value='&quot;Account&quot;' />
            <member value='&quot;Channel&quot;' />
            <member value='&quot;Factor&quot;' />
            <member value='&quot;Product&quot;' />
            <member value='&quot;Rep&quot;' />
            <member value='&quot;Suggested Date&quot;' />
            <member value='&quot;Segment&quot;' />
          </members>
        </column>
      </datasource-dependencies>
      <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y'>
        <column aggregation='Year' caption='Run Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Config (1) (copy)_2155535403100246044]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; THEN [DCO_RUN_DATE1]&#13;&#10;END' />
        </column>
        <column aggregation='Count' caption='Config Filter 1' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_2155535403097620507]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME1]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
        </column>
        <column aggregation='Count' caption='Config Filter 2' datatype='string' default-type='nominal' layered='true' name='[Config Filter (copy)_2155535403107680289]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME2]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
        </column>
        <column aggregation='Count' caption='Scenario Filter 1' datatype='string' default-type='nominal' layered='true' name='[Run Date (1) (copy)_2155535403100545053]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = [FLAG] THEN [SCENARIO_NAME1 (copy)_1347983701206401024]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
        </column>
        <column aggregation='Year' caption='Run Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Run Date Filter (copy)_21555354031********]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = &apos;DCO 3 2&apos; THEN [DCO_RUN_DATE2]&#13;&#10;END' />
        </column>
        <column aggregation='Count' caption='Scenario Filter 2' datatype='string' default-type='nominal' layered='true' name='[Scenario Filter (copy)_2155535403107311647]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO_NAME2 (copy)_1347983701206581249]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
        </column>
        <column-instance column='[Config (1) (copy)_2155535403100246044]' derivation='None' name='[none:Config (1) (copy)_2155535403100246044:qk]' pivot='key' type='quantitative' />
        <column-instance column='[Config (copy)_2155535403097620507]' derivation='None' name='[none:Config (copy)_2155535403097620507:nk]' pivot='key' type='nominal' />
        <column-instance column='[Config Filter (copy)_2155535403107680289]' derivation='None' name='[none:Config Filter (copy)_2155535403107680289:nk]' pivot='key' type='nominal' />
        <column-instance column='[Run Date (1) (copy)_2155535403100545053]' derivation='None' name='[none:Run Date (1) (copy)_2155535403100545053:nk]' pivot='key' type='nominal' />
        <column-instance column='[Run Date Filter (copy)_21555354031********]' derivation='None' name='[none:Run Date Filter (copy)_21555354031********:qk]' pivot='key' type='quantitative' />
        <column-instance column='[Scenario Filter (copy)_2155535403107311647]' derivation='None' name='[none:Scenario Filter (copy)_2155535403107311647:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah'>
        <column aggregation='Count' caption='Account Filter 2' datatype='string' default-type='nominal' layered='true' name='[Account (copy)_512495611506294790]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [ACCOUNT_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Segment Filter 1' datatype='string' default-type='nominal' layered='true' name='[Account Filter (copy)_981221801042362389]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [HCP_SEGMENT]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </column>
        <column aggregation='Year' caption='Suggested Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Account combo (copy)_981221801282629672]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SUGGESTED_DATE] &#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Channel Filter 1' datatype='string' default-type='nominal' layered='true' name='[Calculation_981221801239351324]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [CHANNEL] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Channel Filter 2' datatype='string' default-type='nominal' layered='true' name='[Channel (copy)_512495611506069509]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [CHANNEL]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Product Filter 1' datatype='string' default-type='nominal' layered='true' name='[Channel combo (copy)_981221801243471901]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [PRODUCT_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Account Filter 1' datatype='string' default-type='nominal' layered='true' name='[Channel combo (copy)_981221801280938018]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [ACCOUNT_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Config Filter 2' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_512495611509768203]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SE_CONFIG_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Scenario Filter 1' datatype='string' default-type='nominal' layered='true' name='[Config (copy)_981221801310863404]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
        </column>
        <column aggregation='Year' caption='Run Date Filter 1' datatype='date' default-type='ordinal' layered='true' name='[Config (copy)_981221801311191085]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [DCO_RUN_DATE]&#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Factor Filter 2' datatype='string' default-type='nominal' layered='true' name='[Factor (copy)_512495611506483207]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [FACTOR_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Product Filter 2' datatype='string' default-type='nominal' layered='true' name='[Product (copy)_512495611509055496]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [PRODUCT_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Factor Filter 1' datatype='string' default-type='nominal' layered='true' name='[Product combo  (copy)_981221801281835044]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [FACTOR_NAME] &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Rep Filter 1' datatype='string' default-type='nominal' layered='true' name='[Product combo  (copy)_981221801282273318]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [REP_TEAM_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;  &#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Rep Filter 2' datatype='string' default-type='nominal' layered='true' name='[Rep (copy)_512495611509235721]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [REP_TEAM_NAME]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </column>
        <column aggregation='Year' caption='Run Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Run Date  (copy)_512495611510464526]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [DCO_RUN_DATE]&#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Scenario Filter 2' datatype='string' default-type='nominal' layered='true' name='[Scenario (copy)_512495611510235149]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SCENARIO (copy)_1347983701206884354]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos; &#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Segment Filter 2' datatype='string' default-type='nominal' layered='true' name='[Segment (copy)_512495611509411850]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [HCP_SEGMENT]  &#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </column>
        <column aggregation='Year' caption='Suggested Date Filter 2' datatype='date' default-type='ordinal' layered='true' name='[Suggested Date (copy)_512495611509981196]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403*********] = FLAG THEN [SUGGESTED_DATE] &#13;&#10;    END' />
        </column>
        <column aggregation='Count' caption='Config Filter 1' datatype='string' default-type='nominal' layered='true' name='[Suggested Date (copy)_981221801310244907]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME]&#13;&#10;    ELSE &apos;&lt;stay checked&gt;&apos;&#13;&#10;    END' />
        </column>
        <column-instance column='[Account (copy)_512495611506294790]' derivation='None' name='[none:Account (copy)_512495611506294790:nk]' pivot='key' type='nominal' />
        <column-instance column='[Account Filter (copy)_981221801042362389]' derivation='None' name='[none:Account Filter (copy)_981221801042362389:nk]' pivot='key' type='nominal' />
        <column-instance column='[Account combo (copy)_981221801282629672]' derivation='None' name='[none:Account combo (copy)_981221801282629672:qk]' pivot='key' type='quantitative' />
        <column-instance column='[Calculation_981221801239351324]' derivation='None' name='[none:Calculation_981221801239351324:nk]' pivot='key' type='nominal' />
        <column-instance column='[Channel (copy)_512495611506069509]' derivation='None' name='[none:Channel (copy)_512495611506069509:nk]' pivot='key' type='nominal' />
        <column-instance column='[Channel combo (copy)_981221801243471901]' derivation='None' name='[none:Channel combo (copy)_981221801243471901:nk]' pivot='key' type='nominal' />
        <column-instance column='[Channel combo (copy)_981221801280938018]' derivation='None' name='[none:Channel combo (copy)_981221801280938018:nk]' pivot='key' type='nominal' />
        <column-instance column='[Config (copy)_512495611509768203]' derivation='None' name='[none:Config (copy)_512495611509768203:nk]' pivot='key' type='nominal' />
        <column-instance column='[Config (copy)_981221801310863404]' derivation='None' name='[none:Config (copy)_981221801310863404:nk]' pivot='key' type='nominal' />
        <column-instance column='[Config (copy)_981221801311191085]' derivation='None' name='[none:Config (copy)_981221801311191085:qk]' pivot='key' type='quantitative' />
        <column-instance column='[Factor (copy)_512495611506483207]' derivation='None' name='[none:Factor (copy)_512495611506483207:nk]' pivot='key' type='nominal' />
        <column-instance column='[Product (copy)_512495611509055496]' derivation='None' name='[none:Product (copy)_512495611509055496:nk]' pivot='key' type='nominal' />
        <column-instance column='[Product combo  (copy)_981221801281835044]' derivation='None' name='[none:Product combo  (copy)_981221801281835044:nk]' pivot='key' type='nominal' />
        <column-instance column='[Product combo  (copy)_981221801282273318]' derivation='None' name='[none:Product combo  (copy)_981221801282273318:nk]' pivot='key' type='nominal' />
        <column-instance column='[Rep (copy)_512495611509235721]' derivation='None' name='[none:Rep (copy)_512495611509235721:nk]' pivot='key' type='nominal' />
        <column-instance column='[Run Date  (copy)_512495611510464526]' derivation='None' name='[none:Run Date  (copy)_512495611510464526:qk]' pivot='key' type='quantitative' />
        <column-instance column='[Scenario (copy)_512495611510235149]' derivation='None' name='[none:Scenario (copy)_512495611510235149:nk]' pivot='key' type='nominal' />
        <column-instance column='[Segment (copy)_512495611509411850]' derivation='None' name='[none:Segment (copy)_512495611509411850:nk]' pivot='key' type='nominal' />
        <column-instance column='[Suggested Date (copy)_512495611509981196]' derivation='None' name='[none:Suggested Date (copy)_512495611509981196:qk]' pivot='key' type='quantitative' />
        <column-instance column='[Suggested Date (copy)_981221801310244907]' derivation='None' name='[none:Suggested Date (copy)_981221801310244907:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <zones>
        <zone h='100000' id='4' type-v2='layout-basic' w='100000' x='0' y='0'>
          <zone h='41555' id='714' is-fixed='true' type-v2='layout-basic' w='98934' x='533' y='889'>
            <zone h='24111' id='34' param='vert' type-v2='layout-flow' w='98934' x='533' y='889'>
              <zone fixed-size='61' h='7667' id='35' is-fixed='true' type-v2='title' w='98934' x='533' y='889'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                </zone-style>
              </zone>
              <zone forceUpdate='true' h='3556' id='7' type-v2='text' w='98934' x='533' y='8556'>
                <formatted-text>
                  <run fontcolor='#000000' fontname='Tableau Medium' fontsize='13'>Summary</run>
                </formatted-text>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                </zone-style>
              </zone>
              <zone forceUpdate='true' h='2222' id='602' type-v2='text' w='98934' x='533' y='12112'>
                <formatted-text>
                  <run fontcolor='#555555' fontname='Benton Sans Book' fontsize='7'><![CDATA[Please reset all filters before changing sources. Please check <Difference> in the filters to display the Difference section.]]></run>
                </formatted-text>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                </zone-style>
              </zone>
              <zone h='5778' id='437' param='horz' type-v2='layout-flow' w='98934' x='533' y='14334'>
                <zone fixed-size='88' forceUpdate='true' h='5778' id='713' is-fixed='true' type-v2='text' w='6400' x='533' y='14334'>
                  <formatted-text>
                    <run bold='true' fontcolor='#333333'>Source 1</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='background-color' value='#f0f3fa' />
                  </zone-style>
                </zone>
                <zone h='5778' id='923' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (1) (copy)_2155535403100246044:qk]' type-v2='filter' values='database' w='20834' x='6933' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='5778' id='929' mode='checkdropdown' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (copy)_2155535403097620507:nk]' type-v2='filter' values='database' w='11200' x='27767' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='5778' id='927' mode='checkdropdown' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date (1) (copy)_2155535403100545053:nk]' type-v2='filter' values='database' w='11200' x='38967' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='5778' id='711' param='horz' type-v2='layout-flow' w='26900' x='50167' y='14334'>
                  <zone fixed-size='83' forceUpdate='true' h='5778' id='712' is-fixed='true' type-v2='text' w='6066' x='50167' y='14334'>
                    <formatted-text>
                      <run bold='true' fontcolor='#333333'>Source 2</run>
                    </formatted-text>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='none' />
                      <format attr='border-width' value='0' />
                      <format attr='margin' value='4' />
                      <format attr='background-color' value='#f0f3fa' />
                    </zone-style>
                  </zone>
                  <zone h='5778' id='924' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date Filter (copy)_21555354031********:qk]' type-v2='filter' values='database' w='20834' x='56233' y='14334'>
                    <zone-style>
                      <format attr='border-color' value='#000000' />
                      <format attr='border-style' value='none' />
                      <format attr='border-width' value='0' />
                      <format attr='margin' value='4' />
                    </zone-style>
                  </zone>
                </zone>
                <zone h='5778' id='930' mode='checkdropdown' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config Filter (copy)_2155535403107680289:nk]' type-v2='filter' values='database' w='11200' x='77067' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='5778' id='928' mode='checkdropdown' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Scenario Filter (copy)_2155535403107311647:nk]' type-v2='filter' values='database' w='11200' x='88267' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
              </zone>
            </zone>
            <zone h='17444' id='715' param='vert' type-v2='layout-flow' w='98934' x='533' y='25000'>
              <zone h='17444' id='13' param='horz' type-v2='layout-flow' w='98934' x='533' y='25000'>
                <zone h='17444' id='11' type-v2='layout-basic' w='98934' x='533' y='25000'>
                  <zone h='17444' id='195' param='horz' type-v2='layout-flow' w='98934' x='533' y='25000'>
                    <zone fixed-size='563' h='17444' id='198' is-fixed='true' name='Summary' show-title='false' w='37533' x='533' y='25000'>
                      <layout-cache type-h='fixed' type-w='cell' />
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='0' />
                      </zone-style>
                    </zone>
                    <zone fixed-size='132' h='17444' id='481' is-fixed='true' name='Summary Diff' show-title='false' w='8800' x='38066' y='25000'>
                      <layout-cache type-h='fixed' type-w='cell' />
                      <zone-style>
                        <format attr='border-color' value='#000000' />
                        <format attr='border-style' value='none' />
                        <format attr='border-width' value='0' />
                        <format attr='margin' value='0' />
                      </zone-style>
                    </zone>
                  </zone>
                </zone>
              </zone>
            </zone>
          </zone>
          <zone forceUpdate='true' h='4222' id='73' type-v2='text' w='98934' x='533' y='42444'>
            <formatted-text>
              <run fontcolor='#000000' fontname='Tableau Medium' fontsize='13'>Detailed Comparison</run>
            </formatted-text>
            <zone-style>
              <format attr='border-color' value='#000000' />
              <format attr='border-style' value='none' />
              <format attr='border-width' value='0' />
              <format attr='margin' value='4' />
            </zone-style>
          </zone>
          <zone h='32444' id='902' is-fixed='true' type-v2='layout-basic' w='98934' x='533' y='66667'>
            <zone friendly-name='Details' h='32444' id='337' param='horz' type-v2='layout-flow' w='85800' x='533' y='66667'>
              <zone fixed-size='1255' h='20556' id='338' is-fixed='true' name='Details' show-title='false' w='85800' x='533' y='66667'>
                <layout-cache fixed-size-h='185' type-h='fixed' type-w='fixed' />
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='0' />
                </zone-style>
              </zone>
            </zone>
            <zone friendly-name='Details' h='32444' id='903' param='horz' type-v2='layout-flow' w='13134' x='86333' y='66667'>
              <zone h='32444' id='694' param='vert' type-v2='layout-flow' w='13134' x='86333' y='66667'>
                <zone h='4634' id='79' mode='compact' param='[Parameters].[Parameter 2]' type-v2='paramctrl' w='13134' x='86333' y='66667'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='4634' id='376' mode='compact' param='[Parameters].[Level 1 (copy)_526921184321613848]' type-v2='paramctrl' w='13134' x='86333' y='71301'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='4634' id='377' mode='compact' param='[Parameters].[Level 2 (copy)_526921184321875994]' type-v2='paramctrl' w='13134' x='86333' y='75935'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='4634' id='378' mode='compact' param='[Parameters].[Level 3 (copy)_526921184321929243]' type-v2='paramctrl' w='13134' x='86333' y='80569'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='4634' id='379' mode='compact' param='[Parameters].[Level 3 (copy) (copy)_526921184321937436]' type-v2='paramctrl' w='13134' x='86333' y='85203'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='4634' id='380' mode='compact' param='[Parameters].[Level 5 (copy)_526921184321998877]' type-v2='paramctrl' w='13134' x='86333' y='89837'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
                <zone h='4640' id='418' mode='compact' param='[Parameters].[Level 6 (copy)_2155535403012243456]' type-v2='paramctrl' w='13134' x='86333' y='94471'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
              </zone>
            </zone>
          </zone>
          <zone h='9223' id='818' layout-strategy-id='distribute-evenly' param='horz' type-v2='layout-flow' w='93334' x='6133' y='57444'>
            <zone h='9223' id='821' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Run Date  (copy)_512495611510464526:qk]' type-v2='filter' values='relevant' w='9334' x='6133' y='57444'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='9223' id='811' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_512495611509768203:nk]' type-v2='filter' values='relevant' w='9333' x='15467' y='57444'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='9223' id='814' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Scenario (copy)_512495611510235149:nk]' type-v2='filter' values='relevant' w='9333' x='24800' y='57444'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='9223' id='810' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel (copy)_512495611506069509:nk]' type-v2='filter' values='relevant' w='9334' x='34133' y='57444'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='9223' id='815' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Segment (copy)_512495611509411850:nk]' type-v2='filter' values='relevant' w='9333' x='43467' y='57444'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='9223' id='820' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product (copy)_512495611509055496:nk]' type-v2='filter' values='relevant' w='9333' x='52800' y='57444'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='9223' id='812' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Factor (copy)_512495611506483207:nk]' type-v2='filter' values='relevant' w='9334' x='62133' y='57444'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='9223' id='813' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Rep (copy)_512495611509235721:nk]' type-v2='filter' values='relevant' w='9333' x='71467' y='57444'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='9223' id='809' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account (copy)_512495611506294790:nk]' type-v2='filter' values='relevant' w='9333' x='80800' y='57444'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='9223' id='816' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_512495611509981196:qk]' type-v2='filter' values='relevant' w='9334' x='90133' y='57444'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
          </zone>
          <zone h='7444' id='819' param='horz' type-v2='layout-flow' w='93334' x='6133' y='50000'>
            <zone h='7444' id='825' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801311191085:qk]' type-v2='filter' values='relevant' w='9333' x='6133' y='50000'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='7444' id='676' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_981221801310244907:nk]' type-v2='filter' values='relevant' w='9333' x='15466' y='50000'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='7444' id='677' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801310863404:nk]' type-v2='filter' values='relevant' w='9333' x='24799' y='50000'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='7444' id='652' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Calculation_981221801239351324:nk]' type-v2='filter' values='relevant' w='9333' x='34132' y='50000'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='7444' id='656' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account Filter (copy)_981221801042362389:nk]' type-v2='filter' values='relevant' w='9333' x='43465' y='50000'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='7444' id='654' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel combo (copy)_981221801243471901:nk]' type-v2='filter' values='relevant' w='9333' x='52798' y='50000'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='7444' id='653' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product combo  (copy)_981221801281835044:nk]' type-v2='filter' values='relevant' w='9333' x='62131' y='50000'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='7444' id='655' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product combo  (copy)_981221801282273318:nk]' type-v2='filter' values='relevant' w='9333' x='71464' y='50000'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='7444' id='651' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel combo (copy)_981221801280938018:nk]' type-v2='filter' values='relevant' w='9333' x='80797' y='50000'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='7444' id='826' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account combo (copy)_981221801282629672:qk]' type-v2='filter' values='relevant' w='9337' x='90130' y='50000'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
          </zone>
          <zone forceUpdate='true' h='8667' id='823' type-v2='text' w='5600' x='533' y='50000'>
            <formatted-text>
              <run bold='true' fontcolor='#333333'>Source 1</run>
            </formatted-text>
            <zone-style>
              <format attr='border-color' value='#000000' />
              <format attr='border-style' value='none' />
              <format attr='border-width' value='0' />
              <format attr='margin' value='4' />
              <format attr='background-color' value='#f0f3fa' />
            </zone-style>
          </zone>
          <zone forceUpdate='true' h='8000' id='824' type-v2='text' w='5600' x='533' y='58667'>
            <formatted-text>
              <run bold='true' fontcolor='#333333'>Source 2</run>
            </formatted-text>
            <zone-style>
              <format attr='border-color' value='#000000' />
              <format attr='border-style' value='none' />
              <format attr='border-width' value='0' />
              <format attr='margin' value='4' />
              <format attr='background-color' value='#f0f3fa' />
            </zone-style>
          </zone>
          <zone forceUpdate='true' h='3334' id='939' type-v2='text' w='98934' x='533' y='46666'>
            <formatted-text>
              <run fontcolor='#555555' fontname='Benton Sans Book' fontsize='7'>Please check &lt;stay checked&gt; .</run>
            </formatted-text>
            <zone-style>
              <format attr='border-color' value='#000000' />
              <format attr='border-style' value='none' />
              <format attr='border-width' value='0' />
              <format attr='margin' value='4' />
            </zone-style>
          </zone>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='none' />
            <format attr='border-width' value='0' />
            <format attr='margin' value='8' />
          </zone-style>
        </zone>
      </zones>
      <devicelayouts>
        <devicelayout auto-generated='true' name='Phone'>
          <layout-options>
            <title>
              <formatted-text>
                <run fontalignment='1' fontname='Tableau Medium'>&lt;Sheet Name&gt;</run>
              </formatted-text>
            </title>
          </layout-options>
          <size maxheight='3200' minheight='3200' sizing-mode='vscroll' />
          <zones>
            <zone h='100000' id='965' type-v2='layout-basic' w='100000' x='0' y='0'>
              <zone h='98222' id='964' param='vert' type-v2='layout-flow' w='98934' x='533' y='889'>
                <zone fixed-size='61' h='7667' id='35' type-v2='title' w='98934' x='533' y='889'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone forceUpdate='true' h='3556' id='7' type-v2='text' w='98934' x='533' y='8556'>
                  <formatted-text>
                    <run fontcolor='#000000' fontname='Tableau Medium' fontsize='13'>Summary</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone forceUpdate='true' h='2222' id='602' type-v2='text' w='98934' x='533' y='12112'>
                  <formatted-text>
                    <run fontcolor='#555555' fontname='Benton Sans Book' fontsize='7'><![CDATA[Please reset all filters before changing sources. Please check <Difference> in the filters to display the Difference section.]]></run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='88' forceUpdate='true' h='5778' id='713' type-v2='text' w='6400' x='533' y='14334'>
                  <formatted-text>
                    <run bold='true' fontcolor='#333333'>Source 1</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#f0f3fa' />
                  </zone-style>
                </zone>
                <zone fixed-size='83' forceUpdate='true' h='5778' id='712' type-v2='text' w='6066' x='50167' y='14334'>
                  <formatted-text>
                    <run bold='true' fontcolor='#333333'>Source 2</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#f0f3fa' />
                  </zone-style>
                </zone>
                <zone h='5778' id='923' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (1) (copy)_2155535403100246044:qk]' type-v2='filter' values='database' w='20834' x='6933' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='5778' id='929' mode='checkdropdown' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config (copy)_2155535403097620507:nk]' type-v2='filter' values='database' w='11200' x='27767' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='5778' id='927' mode='checkdropdown' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date (1) (copy)_2155535403100545053:nk]' type-v2='filter' values='database' w='11200' x='38967' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='5778' id='924' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Run Date Filter (copy)_21555354031********:qk]' type-v2='filter' values='database' w='20834' x='56233' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='5778' id='930' mode='checkdropdown' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Config Filter (copy)_2155535403107680289:nk]' type-v2='filter' values='database' w='11200' x='77067' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='5778' id='928' mode='checkdropdown' name='Summary' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.094fh9l1hsc82x1676xd31jg8l6y].[none:Scenario Filter (copy)_2155535403107311647:nk]' type-v2='filter' values='database' w='11200' x='88267' y='14334'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='157' h='17444' id='198' is-fixed='true' name='Summary' show-title='false' w='37533' x='533' y='25000'>
                  <layout-cache type-h='fixed' type-w='cell' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='157' h='17444' id='481' is-fixed='true' name='Summary Diff' show-title='false' w='8800' x='38066' y='25000'>
                  <layout-cache type-h='fixed' type-w='cell' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone forceUpdate='true' h='4222' id='73' type-v2='text' w='98934' x='533' y='42444'>
                  <formatted-text>
                    <run fontcolor='#000000' fontname='Tableau Medium' fontsize='13'>Detailed Comparison</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone forceUpdate='true' h='3334' id='939' type-v2='text' w='98934' x='533' y='46666'>
                  <formatted-text>
                    <run fontcolor='#555555' fontname='Benton Sans Book' fontsize='7'>Please check &lt;stay checked&gt; .</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone forceUpdate='true' h='8667' id='823' type-v2='text' w='5600' x='533' y='50000'>
                  <formatted-text>
                    <run bold='true' fontcolor='#333333'>Source 1</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#f0f3fa' />
                  </zone-style>
                </zone>
                <zone forceUpdate='true' h='8000' id='824' type-v2='text' w='5600' x='533' y='58667'>
                  <formatted-text>
                    <run bold='true' fontcolor='#333333'>Source 2</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#f0f3fa' />
                  </zone-style>
                </zone>
                <zone h='9223' id='821' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Run Date  (copy)_512495611510464526:qk]' type-v2='filter' values='relevant' w='9334' x='6133' y='57444'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='9223' id='811' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_512495611509768203:nk]' type-v2='filter' values='relevant' w='9333' x='15467' y='57444'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='9223' id='814' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Scenario (copy)_512495611510235149:nk]' type-v2='filter' values='relevant' w='9333' x='24800' y='57444'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='9223' id='810' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel (copy)_512495611506069509:nk]' type-v2='filter' values='relevant' w='9334' x='34133' y='57444'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='9223' id='815' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Segment (copy)_512495611509411850:nk]' type-v2='filter' values='relevant' w='9333' x='43467' y='57444'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='9223' id='820' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product (copy)_512495611509055496:nk]' type-v2='filter' values='relevant' w='9333' x='52800' y='57444'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='9223' id='812' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Factor (copy)_512495611506483207:nk]' type-v2='filter' values='relevant' w='9334' x='62133' y='57444'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='9223' id='813' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Rep (copy)_512495611509235721:nk]' type-v2='filter' values='relevant' w='9333' x='71467' y='57444'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='9223' id='809' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account (copy)_512495611506294790:nk]' type-v2='filter' values='relevant' w='9333' x='80800' y='57444'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='9223' id='816' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_512495611509981196:qk]' type-v2='filter' values='relevant' w='9334' x='90133' y='57444'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7444' id='825' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801311191085:qk]' type-v2='filter' values='relevant' w='9333' x='6133' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7444' id='676' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_981221801310244907:nk]' type-v2='filter' values='relevant' w='9333' x='15466' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7444' id='677' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801310863404:nk]' type-v2='filter' values='relevant' w='9333' x='24799' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7444' id='652' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Calculation_981221801239351324:nk]' type-v2='filter' values='relevant' w='9333' x='34132' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7444' id='656' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account Filter (copy)_981221801042362389:nk]' type-v2='filter' values='relevant' w='9333' x='43465' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7444' id='654' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel combo (copy)_981221801243471901:nk]' type-v2='filter' values='relevant' w='9333' x='52798' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7444' id='653' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product combo  (copy)_981221801281835044:nk]' type-v2='filter' values='relevant' w='9333' x='62131' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7444' id='655' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Product combo  (copy)_981221801282273318:nk]' type-v2='filter' values='relevant' w='9333' x='71464' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7444' id='651' mode='checkdropdown' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Channel combo (copy)_981221801280938018:nk]' type-v2='filter' values='relevant' w='9333' x='80797' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='7444' id='826' name='Details' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account combo (copy)_981221801282629672:qk]' type-v2='filter' values='relevant' w='9337' x='90130' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='185' h='20556' id='338' is-fixed='true' name='Details' show-title='false' w='85800' x='533' y='66667'>
                  <layout-cache fixed-size-h='185' type-h='fixed' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='4634' id='79' mode='compact' param='[Parameters].[Parameter 2]' type-v2='paramctrl' w='13134' x='86333' y='66667'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='4634' id='376' mode='compact' param='[Parameters].[Level 1 (copy)_526921184321613848]' type-v2='paramctrl' w='13134' x='86333' y='71301'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='4634' id='377' mode='compact' param='[Parameters].[Level 2 (copy)_526921184321875994]' type-v2='paramctrl' w='13134' x='86333' y='75935'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='4634' id='378' mode='compact' param='[Parameters].[Level 3 (copy)_526921184321929243]' type-v2='paramctrl' w='13134' x='86333' y='80569'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='4634' id='379' mode='compact' param='[Parameters].[Level 3 (copy) (copy)_526921184321937436]' type-v2='paramctrl' w='13134' x='86333' y='85203'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='4634' id='380' mode='compact' param='[Parameters].[Level 5 (copy)_526921184321998877]' type-v2='paramctrl' w='13134' x='86333' y='89837'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='4640' id='418' mode='compact' param='[Parameters].[Level 6 (copy)_2155535403012243456]' type-v2='paramctrl' w='13134' x='86333' y='94471'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='8' />
              </zone-style>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{99BD0470-C39C-4025-B16C-2D6FC928C776}' />
    </dashboard>
  </dashboards>
  <windows source-height='51'>
    <window class='dashboard' maximized='true' name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Comparison report'>
      <viewpoints>
        <viewpoint name='Details' />
        <viewpoint name='Summary'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='Summary Diff'>
          <zoom type='entire-view' />
        </viewpoint>
      </viewpoints>
      <active id='-1' />
      <simple-id uuid='{898344EA-5FE2-4016-A1BB-65074AB21DE3}' />
    </window>
    <window class='worksheet' hidden='true' name='Details'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card size='12' type='pages' />
            <card size='195' type='filters' />
            <card size='34' type='marks' />
            <card size='32' type='measures' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801311191085:qk]' show-domain='false' show-null-ctrls='false' type='filter' />
            <card mode='compact' param='[Parameters].[Parameter 2]' type='parameter' />
            <card mode='compact' param='[Parameters].[Level 1 (copy)_526921184321613848]' type='parameter' />
            <card mode='compact' param='[Parameters].[Level 2 (copy)_526921184321875994]' type='parameter' />
            <card mode='compact' param='[Parameters].[Level 3 (copy)_526921184321929243]' type='parameter' />
            <card mode='compact' param='[Parameters].[Level 3 (copy) (copy)_526921184321937436]' type='parameter' />
            <card mode='compact' param='[Parameters].[Level 5 (copy)_526921184321998877]' type='parameter' />
          </strip>
          <strip size='160'>
            <card param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Run Date  (copy)_512495611510464526:qk]' show-null-ctrls='false' type='filter' />
            <card param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Account combo (copy)_981221801282629672:qk]' show-null-ctrls='false' type='filter' />
            <card param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Suggested Date (copy)_512495611509981196:qk]' show-null-ctrls='false' type='filter' />
          </strip>
          <strip size='160'>
            <card mode='compact' param='[Parameters].[Choose data sources (copy)_2155535403020820482]' type='parameter' />
            <card mode='compact' param='[Parameters].[Source 1 (copy)_2155535403*********]' type='parameter' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.151vt2s1pw4bdq13h0uw1080o3ah].[none:Config (copy)_981221801311191085:qk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{1CF5C060-5CE2-42E0-8410-8640CE52930A}' />
    </window>
    <window class='worksheet' hidden='true' name='Summary'>
      <cards>
        <edge name='left'>
          <strip size='192'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
            <card type='measures' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='120'>
            <card mode='compact' param='[Parameters].[Choose data sources (copy)_2155535403020820482]' type='parameter' />
            <card mode='compact' param='[Parameters].[Source 1 (copy)_2155535403*********]' type='parameter' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{C1BC4918-4580-4A9B-953F-936BD67E1D92}' />
    </window>
    <window class='worksheet' hidden='true' name='Summary Diff'>
      <cards>
        <edge name='left'>
          <strip size='300'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
            <card type='measures' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card mode='compact' param='[Parameters].[Choose data sources (copy)_2155535403020820482]' type='parameter' />
            <card mode='compact' param='[Parameters].[Source 1 (copy)_2155535403*********]' type='parameter' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{471AD6F8-A6DE-4F42-A0A4-4B9A94B08542}' />
    </window>
  </windows>
</workbook>
