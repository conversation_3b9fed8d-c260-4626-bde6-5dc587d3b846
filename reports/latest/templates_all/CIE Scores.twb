<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20223.22.1108.1422                               -->
<workbook locale='en_US' source-build='2021.4.7 (20214.22.0516.1717)' source-platform='mac' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.AccessibleZoneTabOrder.true...AccessibleZoneTabOrder />
    <_.fcp.AnimationOnByDefault.true...AnimationOnByDefault />
    <AutoCreateAndUpdateDSDPhoneLayouts />
    <ISO8601DefaultCalendarPref />
    <IntuitiveSorting />
    <IntuitiveSorting_SP2 />
    <_.fcp.MarkAnimation.true...MarkAnimation />
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.ParameterDefaultValues.true...ParameterDefaultValues />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <SetMembershipControl />
    <SheetIdentifierTracking />
    <WindowsPersistSimpleIdentifiers />
    <WorksheetBackgroundTransparency />
    <ZoneBackgroundTransparency />
    <ZoneFriendlyName />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEScores?rev=1.3' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEScores' path='/t/${TABLEAU_SITE}/workbooks' revision='1.3' site='${TABLEAU_SITE}' />
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <_.fcp.AnimationOnByDefault.false...style>
    <_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule element='animation'>
      <_.fcp.AnimationOnByDefault.false...format attr='animation-on' value='ao-on' />
    </_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule>
  </_.fcp.AnimationOnByDefault.false...style>
  <datasources>
    <datasource hasconnection='false' inline='true' name='Parameters' version='18.1'>
      <aliases enabled='yes' />
      <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
        <calculation class='tableau' formula='&quot;Channel&quot;' />
        <members>
          <member value='&quot;Channel&quot;' />
          <member value='&quot;Segment&quot;' />
        </members>
      </column>
      <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
        <calculation class='tableau' formula='&quot;Published&quot;' />
        <members>
          <member value='&quot;(All)&quot;' />
          <member value='&quot;Published&quot;' />
        </members>
      </column>
      <column _.fcp.ParameterDefaultValues.true...source-field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[INFLUENCE_DISPLAY_NAME]' caption='P_Influence' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account Long-term Importance&quot;'>
        <calculation class='tableau' formula='&quot;Account Long-term Importance&quot;' />
      </column>
    </datasource>
    <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' inline='true' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66' version='18.1'>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel?rev=1.4' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.3' site='${TABLEAU_SITE}' />
      <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}' username='' workgroup-auth-mode='prompt'>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
        <calculations>
          <calculation column='[Calculation_1445936995299098631]' formula='[Parameters].[Parameter 3]=[INFLUENCE_DISPLAY_NAME]' />
          <calculation column='[Calculation_1506454080614776832]' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
          <calculation column='[Calculation_1604407389790982145]' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
          <calculation column='[Calculation_527765595207630863]' formula='SUM([ACTIVE_ACCOUNTS])' />
          <calculation column='[Calculation_864972641503416320]' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
          <calculation column='[FINAL_SCORE_SUM (copy)_1327436000330481664]' formula='SUM([FINAL_SCORE_SUM])/SUM([SUGGESTION_COUNT])' />
        </calculations>
        <metadata-records>
          <metadata-record class='measure'>
            <remote-name>ACCOUNT_SCORE</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ACCOUNT_SCORE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACCOUNT_SCORE</remote-alias>
            <ordinal>11</ordinal>
            <layered>true</layered>
            <caption>Account Score</caption>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>1</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>ACTIVE_ACCOUNTS</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ACTIVE_ACCOUNTS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>ACTIVE_ACCOUNTS</remote-alias>
            <ordinal>10</ordinal>
            <layered>true</layered>
            <caption>Active Accounts</caption>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>18</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CHANNEL</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CHANNEL]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL</remote-alias>
            <ordinal>7</ordinal>
            <layered>true</layered>
            <caption>Channel</caption>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>CHANNEL_SCORE</remote-name>
            <remote-type>131</remote-type>
            <local-name>[CHANNEL_SCORE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CHANNEL_SCORE</remote-alias>
            <ordinal>12</ordinal>
            <layered>true</layered>
            <caption>Channel Score</caption>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>1</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_BY (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CREATED_BY (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_BY (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>69</ordinal>
            <layered>true</layered>
            <caption>Created By (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_BY</remote-name>
            <remote-type>129</remote-type>
            <local-name>[CREATED_BY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_BY</remote-alias>
            <ordinal>33</ordinal>
            <layered>true</layered>
            <caption>Created By</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_TS (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[CREATED_TS (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_TS (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>70</ordinal>
            <layered>true</layered>
            <caption>Created Ts (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CREATED_TS</remote-name>
            <remote-type>7</remote-type>
            <local-name>[CREATED_TS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>CREATED_TS</remote-alias>
            <ordinal>36</ordinal>
            <layered>true</layered>
            <caption>Created Ts</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1445936995299098631</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1445936995299098631]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1445936995299098631</remote-alias>
            <ordinal>77</ordinal>
            <layered>true</layered>
            <caption>Influencer</caption>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[Parameters].[Parameter 3]=[INFLUENCE_DISPLAY_NAME]&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1506454080614776832</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1506454080614776832]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1506454080614776832</remote-alias>
            <ordinal>78</ordinal>
            <layered>true</layered>
            <caption>Channel / Segment</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Parameter 1]&#13;
WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;
WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_1604407389790982145</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_1604407389790982145]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_1604407389790982145</remote-alias>
            <ordinal>79</ordinal>
            <layered>true</layered>
            <caption>Scenario group</caption>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='string' name='formula'>&quot;CASE [Parameters].[Parameter 2]&#13;
WHEN &apos;(All)&apos; THEN 1&#13;
WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;
END&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>Calculation_527765595207630863</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_527765595207630863]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_527765595207630863</remote-alias>
            <ordinal>80</ordinal>
            <layered>true</layered>
            <caption>Calculate_accounts</caption>
            <local-type>integer</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM([ACTIVE_ACCOUNTS])&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Calculation_864972641503416320</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[Calculation_864972641503416320]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>Calculation_864972641503416320</remote-alias>
            <ordinal>81</ordinal>
            <layered>true</layered>
            <caption>Scenario</caption>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='string' name='formula'>&quot;[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)&quot;</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_CODE</remote-alias>
            <ordinal>22</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Code</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_DESCRIPTION</remote-alias>
            <ordinal>24</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Description</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>347</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_NAME</remote-alias>
            <ordinal>23</ordinal>
            <layered>true</layered>
            <caption>Reason</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>22</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_TYPE_CODE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_TYPE_CODE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_TYPE_CODE</remote-alias>
            <ordinal>25</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Type Code</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>9</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_TYPE_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_TYPE_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_TYPE_NAME</remote-alias>
            <ordinal>26</ordinal>
            <layered>true</layered>
            <caption>Reason Type</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>9</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)</remote-alias>
            <ordinal>21</ordinal>
            <layered>true</layered>
            <caption>Dco Reason Uid (Vw Dim Dco Reason Rpt)</caption>
            <family>VW_DIM_DCO_REASON_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_REASON_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_REASON_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_REASON_UID</remote-alias>
            <ordinal>17</ordinal>
            <layered>true</layered>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>26</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)</remote-alias>
            <ordinal>32</ordinal>
            <layered>true</layered>
            <caption>Dco Run Date (Vw Dim Dco Run Rpt)</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DCO_RUN_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_DATE</remote-alias>
            <ordinal>6</ordinal>
            <layered>true</layered>
            <caption>Dco Run Date</caption>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_UID (VW_F_CIE_SCORING_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_RUN_UID (VW_F_CIE_SCORING_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_UID (VW_F_CIE_SCORING_RPT)</remote-alias>
            <ordinal>5</ordinal>
            <layered>true</layered>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DCO_RUN_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DCO_RUN_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DCO_RUN_UID</remote-alias>
            <ordinal>28</ordinal>
            <layered>true</layered>
            <caption>Dco Run Uid</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DESCRIPTION</remote-alias>
            <ordinal>40</ordinal>
            <layered>true</layered>
            <caption>Description</caption>
            <family>VW_DIM_DSE_CONFIG</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_BRAND_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_BRAND_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_BRAND_KEY</remote-alias>
            <ordinal>44</ordinal>
            <layered>true</layered>
            <caption>Dim Brand Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_COUNTRY_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_COUNTRY_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_COUNTRY_KEY</remote-alias>
            <ordinal>54</ordinal>
            <layered>true</layered>
            <caption>Dim Country Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_CUSTOMER_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_CUSTOMER_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
            <ordinal>53</ordinal>
            <layered>true</layered>
            <caption>Dim Customer Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>66</ordinal>
            <layered>true</layered>
            <caption>Dim Product Key (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_PRODUCT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_PRODUCT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_PRODUCT_KEY</remote-alias>
            <ordinal>42</ordinal>
            <layered>true</layered>
            <caption>Dim Product Key</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_SEGMENT_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_SEGMENT_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_SEGMENT_KEY</remote-alias>
            <ordinal>63</ordinal>
            <layered>true</layered>
            <caption>Dim Segment Key</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DIM_SEGMENT_TYPE_KEY</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DIM_SEGMENT_TYPE_KEY]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DIM_SEGMENT_TYPE_KEY</remote-alias>
            <ordinal>73</ordinal>
            <layered>true</layered>
            <caption>Dim Segment Type Key</caption>
            <family>VW_DIM_SEGMENT_TYPE_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DS_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[DS_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>DS_NAME</remote-alias>
            <ordinal>1</ordinal>
            <layered>true</layered>
            <caption>Ds Name</caption>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>9</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>FINAL_SCORE_SUM (copy)_1327436000330481664</remote-name>
            <remote-type>-1</remote-type>
            <local-name>[FINAL_SCORE_SUM (copy)_1327436000330481664]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FINAL_SCORE_SUM (copy)_1327436000330481664</remote-alias>
            <ordinal>82</ordinal>
            <layered>true</layered>
            <caption>Average score</caption>
            <local-type>real</local-type>
            <aggregation>User</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
              <attribute datatype='string' name='formula'>&quot;SUM([FINAL_SCORE_SUM])/SUM([SUGGESTION_COUNT])&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>FINAL_SCORE_SUM</remote-name>
            <remote-type>5</remote-type>
            <local-name>[FINAL_SCORE_SUM]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>FINAL_SCORE_SUM</remote-alias>
            <ordinal>8</ordinal>
            <layered>true</layered>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>HCP_SEGMENT</remote-name>
            <remote-type>129</remote-type>
            <local-name>[HCP_SEGMENT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>HCP_SEGMENT</remote-alias>
            <ordinal>2</ordinal>
            <layered>true</layered>
            <caption>Hcp Segment</caption>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>INFLUENCE_DISPLAY_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[INFLUENCE_DISPLAY_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>INFLUENCE_DISPLAY_NAME</remote-alias>
            <ordinal>15</ordinal>
            <layered>true</layered>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>255</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>INFLUENCE_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[INFLUENCE_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>INFLUENCE_UID</remote-alias>
            <ordinal>13</ordinal>
            <layered>true</layered>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>INFLUENCE_VALUE</remote-name>
            <remote-type>5</remote-type>
            <local-name>[INFLUENCE_VALUE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>INFLUENCE_VALUE</remote-alias>
            <ordinal>14</ordinal>
            <layered>true</layered>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_ACTIVE_SRC</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_ACTIVE_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_ACTIVE_SRC</remote-alias>
            <ordinal>50</ordinal>
            <layered>true</layered>
            <caption>Is Active Src</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_COMPETITOR</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_COMPETITOR]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_COMPETITOR</remote-alias>
            <ordinal>51</ordinal>
            <layered>true</layered>
            <caption>Is Competitor</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_DELETED_SRC</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_DELETED_SRC]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_DELETED_SRC</remote-alias>
            <ordinal>52</ordinal>
            <layered>true</layered>
            <caption>Is Deleted Src</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>IS_PUBLISHED</remote-name>
            <remote-type>11</remote-type>
            <local-name>[IS_PUBLISHED]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>IS_PUBLISHED</remote-alias>
            <ordinal>59</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LAST_DCO_RUN_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[LAST_DCO_RUN_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
            <ordinal>61</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LAST_DCO_RUN_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[LAST_DCO_RUN_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LAST_DCO_RUN_UID</remote-alias>
            <ordinal>60</ordinal>
            <layered>true</layered>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LATEST_RUN</remote-name>
            <remote-type>11</remote-type>
            <local-name>[LATEST_RUN]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>LATEST_RUN</remote-alias>
            <ordinal>31</ordinal>
            <layered>true</layered>
            <caption>Latest Run</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>POST_PROC_STATUS</remote-name>
            <remote-type>129</remote-type>
            <local-name>[POST_PROC_STATUS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>POST_PROC_STATUS</remote-alias>
            <ordinal>35</ordinal>
            <layered>true</layered>
            <caption>Post Proc Status</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[PRODUCT_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_ID</remote-alias>
            <ordinal>68</ordinal>
            <layered>true</layered>
            <caption>Product Id</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>11</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME</remote-alias>
            <ordinal>45</ordinal>
            <layered>true</layered>
            <caption>Product Name</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_NAME_ENGLISH</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_NAME_ENGLISH]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_NAME_ENGLISH</remote-alias>
            <ordinal>46</ordinal>
            <layered>true</layered>
            <caption>Product Name English</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_TYPE</remote-alias>
            <ordinal>47</ordinal>
            <layered>true</layered>
            <caption>Product Type</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)</remote-alias>
            <ordinal>43</ordinal>
            <layered>true</layered>
            <caption>Product Uid (Vw Dim Product Dco Rpt)</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID (VW_DIM_SEGMENT_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID (VW_DIM_SEGMENT_RPT)</remote-alias>
            <ordinal>67</ordinal>
            <layered>true</layered>
            <caption>Product Uid (Vw Dim Segment Rpt)</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>200</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PRODUCT_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[PRODUCT_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>PRODUCT_UID</remote-alias>
            <ordinal>3</ordinal>
            <layered>true</layered>
            <caption>Product Uid</caption>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECOMMENDED</remote-name>
            <remote-type>11</remote-type>
            <local-name>[RECOMMENDED]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECOMMENDED</remote-alias>
            <ordinal>16</ordinal>
            <layered>true</layered>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECORD_END_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[RECORD_END_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECORD_END_DATE</remote-alias>
            <ordinal>49</ordinal>
            <layered>true</layered>
            <caption>Record End Date</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RECORD_START_DATE</remote-name>
            <remote-type>7</remote-type>
            <local-name>[RECORD_START_DATE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>RECORD_START_DATE</remote-alias>
            <ordinal>48</ordinal>
            <layered>true</layered>
            <caption>Record Start Date</caption>
            <family>VW_DIM_PRODUCT_DCO_RPT</family>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_DESCRIPTION</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_DESCRIPTION]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_DESCRIPTION</remote-alias>
            <ordinal>58</ordinal>
            <layered>true</layered>
            <caption>Scenario Description</caption>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>255</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_NAME</remote-alias>
            <ordinal>57</ordinal>
            <layered>true</layered>
            <caption>Scenario Name</caption>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID (VW_DIM_DCO_RUN_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID (VW_DIM_DCO_RUN_RPT)</remote-alias>
            <ordinal>29</ordinal>
            <layered>true</layered>
            <caption>Scenario Uid (Vw Dim Dco Run Rpt)</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID (VW_DIM_SCENARIO_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID (VW_DIM_SCENARIO_RPT)</remote-alias>
            <ordinal>56</ordinal>
            <layered>true</layered>
            <caption>Scenario Uid (Vw Dim Scenario Rpt)</caption>
            <family>VW_DIM_SCENARIO_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_UID</remote-alias>
            <ordinal>4</ordinal>
            <layered>true</layered>
            <caption>Scenario Uid</caption>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SCENARIO_VER</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SCENARIO_VER]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SCENARIO_VER</remote-alias>
            <ordinal>30</ordinal>
            <layered>true</layered>
            <caption>Scenario Ver</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_NAME</remote-alias>
            <ordinal>64</ordinal>
            <layered>true</layered>
            <caption>Segment</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>200</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)</remote-alias>
            <ordinal>74</ordinal>
            <layered>true</layered>
            <caption>Segment Type (Vw Dim Segment Type Rpt)</caption>
            <family>VW_DIM_SEGMENT_TYPE_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SEGMENT_TYPE</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SEGMENT_TYPE]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SEGMENT_TYPE</remote-alias>
            <ordinal>65</ordinal>
            <layered>true</layered>
            <caption>Segment Type</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>100</width>
            <contains-null>false</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_ID (VW_DIM_DSE_CONFIG)</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_ID (VW_DIM_DSE_CONFIG)</remote-alias>
            <ordinal>38</ordinal>
            <layered>true</layered>
            <caption>Se Config Id (Vw Dim Dse Config)</caption>
            <family>VW_DIM_DSE_CONFIG</family>
            <local-type>integer</local-type>
            <aggregation>Count</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>SE_CONFIG_ID</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SE_CONFIG_ID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_ID</remote-alias>
            <ordinal>18</ordinal>
            <layered>true</layered>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>38</precision>
            <scale>0</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SE_CONFIG_NAME</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SE_CONFIG_NAME]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SE_CONFIG_NAME</remote-alias>
            <ordinal>39</ordinal>
            <layered>true</layered>
            <caption>Config</caption>
            <family>VW_DIM_DSE_CONFIG</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>16777216</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>STATUS</remote-name>
            <remote-type>129</remote-type>
            <local-name>[STATUS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>STATUS</remote-alias>
            <ordinal>34</ordinal>
            <layered>true</layered>
            <caption>Status</caption>
            <family>VW_DIM_DCO_RUN_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SUGGESTION_CANDIDATE_UID</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SUGGESTION_CANDIDATE_UID]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_CANDIDATE_UID</remote-alias>
            <ordinal>19</ordinal>
            <layered>true</layered>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>40</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='binary' />
            <attributes>
              <attribute datatype='integer' name='field-type'>2</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='measure'>
            <remote-name>SUGGESTION_COUNT</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SUGGESTION_COUNT]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>SUGGESTION_COUNT</remote-alias>
            <ordinal>9</ordinal>
            <layered>true</layered>
            <family>VW_F_CIE_SCORING_RPT</family>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>18</precision>
            <scale>0</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UPDATED_TS</remote-name>
            <remote-type>7</remote-type>
            <local-name>[UPDATED_TS]</local-name>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias>UPDATED_TS</remote-alias>
            <ordinal>71</ordinal>
            <layered>true</layered>
            <caption>Updated Ts</caption>
            <family>VW_DIM_SEGMENT_RPT</family>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='integer' name='field-type'>1</attribute>
              <attribute datatype='integer' name='role'>0</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[sqlproxy]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='boolean' name='CAP_64BIT_CALCULATIONS'>true</attribute>
              <attribute datatype='boolean' name='CAP_CREATE_TEMP_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_ALWAYS_USE_LOCAL_MAPPING_TABLES'>false</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_PREFER_LOCAL_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_BLENDING_REMOTE_MAPPING_TABLES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_IGNORE_HINT_CHECK_NOT_NULL'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SORT_BY'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERIES'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUBQUERY_QUERY_CONTEXT'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_SUPPORTS_LODJOINS'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_TOP_N'>true</attribute>
              <attribute datatype='boolean' name='CAP_QUERY_USE_QUERY_FUSION'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_LEFT'>true</attribute>
              <attribute datatype='boolean' name='CAP_SUPPORTS_SPLIT_FROM_RIGHT'>true</attribute>
              <attribute datatype='integer' name='charset'>0</attribute>
              <attribute datatype='string' name='collation'>&quot;binary&quot;</attribute>
              <attribute datatype='string' name='datasource'>&quot;<![CDATA[<?xml version='1.0' encoding='utf-8' ?>

<datasource :source-version='18.1' formatted-name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' inline='true' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.ParameterDefaultValues.true...ParameterDefaultValues />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/datasources/${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel?rev=1.4' id='${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.3' site='${TABLEAU_SITE}' />
  <connection channel='https' class='sqlproxy' dbname='${CUSTOMER_NAME}${ENVIRONMENT}CIEScoringDataModel' directory='/dataserver' port='443' server='${TABLEAU_SERVER}'>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='sqlproxy' table='[sqlproxy]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
      <relation name='sqlproxy' table='[sqlproxy]' type='table' />
    </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
    <cols>
      <map key='[ACCOUNT_SCORE]' value='[sqlproxy].[ACCOUNT_SCORE]' />
      <map key='[ACTIVE_ACCOUNTS]' value='[sqlproxy].[ACTIVE_ACCOUNTS]' />
      <map key='[CHANNEL]' value='[sqlproxy].[CHANNEL]' />
      <map key='[CHANNEL_SCORE]' value='[sqlproxy].[CHANNEL_SCORE]' />
      <map key='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[CREATED_BY (VW_DIM_SEGMENT_RPT)]' />
      <map key='[CREATED_BY]' value='[sqlproxy].[CREATED_BY]' />
      <map key='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[CREATED_TS (VW_DIM_SEGMENT_RPT)]' />
      <map key='[CREATED_TS]' value='[sqlproxy].[CREATED_TS]' />
      <map key='[Calculation_1445936995299098631]' value='[sqlproxy].[Calculation_1445936995299098631]' />
      <map key='[Calculation_1506454080614776832]' value='[sqlproxy].[Calculation_1506454080614776832]' />
      <map key='[Calculation_1604407389790982145]' value='[sqlproxy].[Calculation_1604407389790982145]' />
      <map key='[Calculation_527765595207630863]' value='[sqlproxy].[Calculation_527765595207630863]' />
      <map key='[Calculation_864972641503416320]' value='[sqlproxy].[Calculation_864972641503416320]' />
      <map key='[DCO_REASON_CODE]' value='[sqlproxy].[DCO_REASON_CODE]' />
      <map key='[DCO_REASON_DESCRIPTION]' value='[sqlproxy].[DCO_REASON_DESCRIPTION]' />
      <map key='[DCO_REASON_NAME]' value='[sqlproxy].[DCO_REASON_NAME]' />
      <map key='[DCO_REASON_TYPE_CODE]' value='[sqlproxy].[DCO_REASON_TYPE_CODE]' />
      <map key='[DCO_REASON_TYPE_NAME]' value='[sqlproxy].[DCO_REASON_TYPE_NAME]' />
      <map key='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' value='[sqlproxy].[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
      <map key='[DCO_REASON_UID]' value='[sqlproxy].[DCO_REASON_UID]' />
      <map key='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' value='[sqlproxy].[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' />
      <map key='[DCO_RUN_DATE]' value='[sqlproxy].[DCO_RUN_DATE]' />
      <map key='[DCO_RUN_UID (VW_F_CIE_SCORING_RPT)]' value='[sqlproxy].[DCO_RUN_UID (VW_F_CIE_SCORING_RPT)]' />
      <map key='[DCO_RUN_UID]' value='[sqlproxy].[DCO_RUN_UID]' />
      <map key='[DESCRIPTION]' value='[sqlproxy].[DESCRIPTION]' />
      <map key='[DIM_BRAND_KEY]' value='[sqlproxy].[DIM_BRAND_KEY]' />
      <map key='[DIM_COUNTRY_KEY]' value='[sqlproxy].[DIM_COUNTRY_KEY]' />
      <map key='[DIM_CUSTOMER_KEY]' value='[sqlproxy].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' />
      <map key='[DIM_PRODUCT_KEY]' value='[sqlproxy].[DIM_PRODUCT_KEY]' />
      <map key='[DIM_SEGMENT_KEY]' value='[sqlproxy].[DIM_SEGMENT_KEY]' />
      <map key='[DIM_SEGMENT_TYPE_KEY]' value='[sqlproxy].[DIM_SEGMENT_TYPE_KEY]' />
      <map key='[DS_NAME]' value='[sqlproxy].[DS_NAME]' />
      <map key='[FINAL_SCORE_SUM (copy)_1327436000330481664]' value='[sqlproxy].[FINAL_SCORE_SUM (copy)_1327436000330481664]' />
      <map key='[FINAL_SCORE_SUM]' value='[sqlproxy].[FINAL_SCORE_SUM]' />
      <map key='[HCP_SEGMENT]' value='[sqlproxy].[HCP_SEGMENT]' />
      <map key='[INFLUENCE_DISPLAY_NAME]' value='[sqlproxy].[INFLUENCE_DISPLAY_NAME]' />
      <map key='[INFLUENCE_UID]' value='[sqlproxy].[INFLUENCE_UID]' />
      <map key='[INFLUENCE_VALUE]' value='[sqlproxy].[INFLUENCE_VALUE]' />
      <map key='[IS_ACTIVE_SRC]' value='[sqlproxy].[IS_ACTIVE_SRC]' />
      <map key='[IS_COMPETITOR]' value='[sqlproxy].[IS_COMPETITOR]' />
      <map key='[IS_DELETED_SRC]' value='[sqlproxy].[IS_DELETED_SRC]' />
      <map key='[IS_PUBLISHED]' value='[sqlproxy].[IS_PUBLISHED]' />
      <map key='[LAST_DCO_RUN_DATE]' value='[sqlproxy].[LAST_DCO_RUN_DATE]' />
      <map key='[LAST_DCO_RUN_UID]' value='[sqlproxy].[LAST_DCO_RUN_UID]' />
      <map key='[LATEST_RUN]' value='[sqlproxy].[LATEST_RUN]' />
      <map key='[POST_PROC_STATUS]' value='[sqlproxy].[POST_PROC_STATUS]' />
      <map key='[PRODUCT_ID]' value='[sqlproxy].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME]' value='[sqlproxy].[PRODUCT_NAME]' />
      <map key='[PRODUCT_NAME_ENGLISH]' value='[sqlproxy].[PRODUCT_NAME_ENGLISH]' />
      <map key='[PRODUCT_TYPE]' value='[sqlproxy].[PRODUCT_TYPE]' />
      <map key='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' value='[sqlproxy].[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
      <map key='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' value='[sqlproxy].[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' />
      <map key='[PRODUCT_UID]' value='[sqlproxy].[PRODUCT_UID]' />
      <map key='[RECOMMENDED]' value='[sqlproxy].[RECOMMENDED]' />
      <map key='[RECORD_END_DATE]' value='[sqlproxy].[RECORD_END_DATE]' />
      <map key='[RECORD_START_DATE]' value='[sqlproxy].[RECORD_START_DATE]' />
      <map key='[SCENARIO_DESCRIPTION]' value='[sqlproxy].[SCENARIO_DESCRIPTION]' />
      <map key='[SCENARIO_NAME]' value='[sqlproxy].[SCENARIO_NAME]' />
      <map key='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' value='[sqlproxy].[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' />
      <map key='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' value='[sqlproxy].[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
      <map key='[SCENARIO_UID]' value='[sqlproxy].[SCENARIO_UID]' />
      <map key='[SCENARIO_VER]' value='[sqlproxy].[SCENARIO_VER]' />
      <map key='[SEGMENT_NAME]' value='[sqlproxy].[SEGMENT_NAME]' />
      <map key='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' value='[sqlproxy].[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' />
      <map key='[SEGMENT_TYPE]' value='[sqlproxy].[SEGMENT_TYPE]' />
      <map key='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' value='[sqlproxy].[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' />
      <map key='[SE_CONFIG_ID]' value='[sqlproxy].[SE_CONFIG_ID]' />
      <map key='[SE_CONFIG_NAME]' value='[sqlproxy].[SE_CONFIG_NAME]' />
      <map key='[STATUS]' value='[sqlproxy].[STATUS]' />
      <map key='[SUGGESTION_CANDIDATE_UID]' value='[sqlproxy].[SUGGESTION_CANDIDATE_UID]' />
      <map key='[SUGGESTION_COUNT]' value='[sqlproxy].[SUGGESTION_COUNT]' />
      <map key='[UPDATED_TS]' value='[sqlproxy].[UPDATED_TS]' />
    </cols>
  </connection>
  <aliases enabled='yes' />
  <column aggregation='Sum' caption='Account Score' datatype='integer' default-type='quantitative' name='[ACCOUNT_SCORE]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Sum' caption='Active Accounts' datatype='integer' default-type='quantitative' name='[ACTIVE_ACCOUNTS]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Sum' caption='Channel Score' datatype='integer' default-type='quantitative' name='[CHANNEL_SCORE]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Created By (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' name='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Created By' datatype='string' default-type='nominal' name='[CREATED_BY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Created Ts (Vw Dim Segment Rpt)' datatype='datetime' default-type='ordinal' name='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Year' caption='Created Ts' datatype='datetime' default-type='ordinal' name='[CREATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <column aggregation='Count' caption='Influencer' datatype='boolean' default-type='nominal' name='[Calculation_1445936995299098631]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default'>
    <calculation class='tableau' formula='[Parameters].[Parameter 3]=[INFLUENCE_DISPLAY_NAME]' />
  </column>
  <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' name='[Calculation_1506454080614776832]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&\#13;&\#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&\#13;&\#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&\#13;&\#10;END' />
  </column>
  <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&\#13;&\#10;WHEN &apos;(All)&apos; THEN 1&\#13;&\#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&\#13;&\#10;END' />
  </column>
  <column aggregation='User' caption='Calculate_accounts' datatype='integer' default-type='quantitative' name='[Calculation_527765595207630863]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
    <calculation class='tableau' formula='SUM([ACTIVE_ACCOUNTS])' />
  </column>
  <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
    <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
  </column>
  <column aggregation='Count' caption='Dco Reason Code' datatype='string' default-type='nominal' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Description' datatype='string' default-type='nominal' name='[DCO_REASON_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Reason' datatype='string' default-type='nominal' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Type Code' datatype='string' default-type='nominal' name='[DCO_REASON_TYPE_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Reason Uid (Vw Dim Dco Reason Rpt)' datatype='string' default-type='nominal' name='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Dco Run Date (Vw Dim Dco Run Rpt)' datatype='date' default-type='ordinal' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' caption='Dco Run Uid' datatype='string' default-type='nominal' name='[DCO_RUN_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Description' datatype='string' default-type='nominal' name='[DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Brand Key' datatype='integer' default-type='ordinal' name='[DIM_BRAND_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Country Key' datatype='integer' default-type='ordinal' name='[DIM_COUNTRY_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Customer Key' datatype='integer' default-type='ordinal' name='[DIM_CUSTOMER_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Product Key (Vw Dim Segment Rpt)' datatype='integer' default-type='ordinal' name='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Product Key' datatype='integer' default-type='ordinal' name='[DIM_PRODUCT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Segment Key' datatype='integer' default-type='ordinal' name='[DIM_SEGMENT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Dim Segment Type Key' datatype='integer' default-type='ordinal' name='[DIM_SEGMENT_TYPE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Ds Name' datatype='string' default-type='nominal' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='User' caption='Average score' datatype='real' default-type='quantitative' name='[FINAL_SCORE_SUM (copy)_1327436000330481664]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
    <calculation class='tableau' formula='SUM([FINAL_SCORE_SUM])/SUM([SUGGESTION_COUNT])' />
  </column>
  <column aggregation='Count' caption='Hcp Segment' datatype='string' default-type='nominal' name='[HCP_SEGMENT]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Is Active Src' datatype='boolean' default-type='nominal' name='[IS_ACTIVE_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Is Competitor' datatype='boolean' default-type='nominal' name='[IS_COMPETITOR]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Is Deleted Src' datatype='boolean' default-type='nominal' name='[IS_DELETED_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Latest Run' datatype='boolean' default-type='nominal' name='[LATEST_RUN]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column aggregation='Count' caption='Post Proc Status' datatype='string' default-type='nominal' name='[POST_PROC_STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Id' datatype='integer' default-type='ordinal' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Name English' datatype='string' default-type='nominal' name='[PRODUCT_NAME_ENGLISH]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Type' datatype='string' default-type='nominal' name='[PRODUCT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Uid (Vw Dim Product Dco Rpt)' datatype='string' default-type='nominal' name='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Uid (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' name='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Product Uid' datatype='string' default-type='nominal' name='[PRODUCT_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Record End Date' datatype='date' default-type='ordinal' name='[RECORD_END_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Year' caption='Record Start Date' datatype='date' default-type='ordinal' name='[RECORD_START_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Description' datatype='string' default-type='nominal' name='[SCENARIO_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Uid (Vw Dim Dco Run Rpt)' datatype='string' default-type='nominal' name='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Uid (Vw Dim Scenario Rpt)' datatype='string' default-type='nominal' name='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Uid' datatype='string' default-type='nominal' name='[SCENARIO_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Scenario Ver' datatype='string' default-type='nominal' name='[SCENARIO_VER]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Se Config Id (Vw Dim Dse Config)' datatype='integer' default-type='ordinal' name='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
  <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Count' caption='Status' datatype='string' default-type='nominal' name='[STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
  <column aggregation='Year' caption='Updated Ts' datatype='datetime' default-type='ordinal' name='[UPDATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_REASON_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_RUN_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DSE_CONFIG' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_PRODUCT_DCO_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SCENARIO_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_TYPE_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_F_CIE_SCORING_RPT' datatype='table' default-type='quantitative' name='[__tableau_internal_object_id__].[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
  <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_1506454080614776832]' derivation='None' name='[none:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
  <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[none:CHANNEL:nk]' type='palette'>
        <map to='\#499894'>
          <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
        </map>
        <map to='\#a0cbe8'>
          <bucket>&quot;SEND_CHANNEL&quot;</bucket>
        </map>
        <map to='\#d37295'>
          <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_1506454080614776832:nk]' type='palette'>
        <map to='\#499894'>
          <bucket>&quot;T3&quot;</bucket>
        </map>
        <map to='\#86bcb6'>
          <bucket>&quot;SEND_CHANNEL&quot;</bucket>
        </map>
        <map to='\#8cd17d'>
          <bucket>&quot;T2&quot;</bucket>
        </map>
        <map to='\#a0cbe8'>
          <bucket>&quot;notier&quot;</bucket>
        </map>
        <map to='\#d7b5a6'>
          <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
        </map>
        <map to='\#f1ce63'>
          <bucket>&quot;ST&quot;</bucket>
        </map>
        <map to='\#ff9d9a'>
          <bucket>&quot;T1&quot;</bucket>
        </map>
        <map to='\#ff9d9a'>
          <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:DCO_REASON_TYPE_NAME:nk]' type='palette'>
        <map to='\#bab0ac'>
          <bucket>&quot;Reject&quot;</bucket>
        </map>
        <map to='\#ff9da7'>
          <bucket>&quot;Recommend&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <datasource-dependencies datasource='Parameters'>
    <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
      <calculation class='tableau' formula='&quot;Channel&quot;' />
      <members>
        <member value='&quot;Channel&quot;' />
        <member value='&quot;Segment&quot;' />
      </members>
    </column>
    <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
      <calculation class='tableau' formula='&quot;Published&quot;' />
      <members>
        <member value='&quot;(All)&quot;' />
        <member value='&quot;Published&quot;' />
      </members>
    </column>
    <column _.fcp.ParameterDefaultValues.true...source-field='[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model].[INFLUENCE_DISPLAY_NAME]' caption='P_Influence' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account Long-term Importance&quot;'>
      <calculation class='tableau' formula='&quot;Account Long-term Importance&quot;' />
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='VW_DIM_DCO_REASON_RPT' id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DCO_RUN_RPT' id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DSE_CONFIG' id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_PRODUCT_DCO_RPT' id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SCENARIO_RPT' id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SEGMENT_RPT' id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SEGMENT_TYPE_RPT' id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
      <object caption='VW_F_CIE_SCORING_RPT' id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF'>
        <properties context=''>
          <relation name='sqlproxy' table='[sqlproxy]' type='table' />
        </properties>
      </object>
    </objects>
    <relationships>
      <relationship>
        <expression op='='>
          <expression op='[DCO_REASON_UID]' />
          <expression op='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DCO_RUN_UID (VW_F_CIE_SCORING_RPT)]' />
          <expression op='[DCO_RUN_UID]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SE_CONFIG_ID]' />
          <expression op='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[PRODUCT_UID]' />
          <expression op='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SCENARIO_UID]' />
          <expression op='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[HCP_SEGMENT]' />
          <expression op='[SEGMENT_NAME]' />
        </expression>
        <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
        <second-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SEGMENT_TYPE]' />
          <expression op='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' />
        </expression>
        <first-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' />
        <second-end-point object-id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608' />
      </relationship>
    </relationships>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
]]>&quot;</attribute>
              <attribute datatype='string' name='dialect-definition'>&quot;<![CDATA[<dialect-definition>
  <primary-dialect class='snowflake' version='0.0.0'>
  </primary-dialect>
  <overlay-dialect-set>
    <overlay-dialect class='vizengine' version='0.1.0'>
    </overlay-dialect>
  </overlay-dialect-set>
</dialect-definition>
]]>&quot;</attribute>
              <attribute datatype='boolean' name='extract-active'>false</attribute>
              <attribute datatype='boolean' name='fast-get-server-time'>true</attribute>
            </attributes>
          </metadata-record>
        </metadata-records>
      </connection>
      <overridable-settings>
        <date-options fiscal-year-start='january' start-of-week='sunday' />
        <default-date-format />
        <default-calendar-type>Gregorian</default-calendar-type>
      </overridable-settings>
      <aliases enabled='yes' />
      <column aggregation='Sum' caption='Account Score' datatype='integer' default-type='quantitative' layered='true' name='[ACCOUNT_SCORE]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' caption='Active Accounts' datatype='integer' default-type='quantitative' layered='true' name='[ACTIVE_ACCOUNTS]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' caption='Channel Score' datatype='integer' default-type='quantitative' layered='true' name='[CHANNEL_SCORE]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Created By (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' layered='true' name='[CREATED_BY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Created By' datatype='string' default-type='nominal' layered='true' name='[CREATED_BY]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' caption='Created Ts (Vw Dim Segment Rpt)' datatype='datetime' default-type='ordinal' layered='true' name='[CREATED_TS (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Year' caption='Created Ts' datatype='datetime' default-type='ordinal' layered='true' name='[CREATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <column aggregation='Count' caption='Influencer' datatype='boolean' default-type='nominal' layered='true' name='[Calculation_1445936995299098631]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default'>
        <calculation class='tableau' formula='[Parameters].[Parameter 3]=[INFLUENCE_DISPLAY_NAME]' />
      </column>
      <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' layered='true' name='[Calculation_1506454080614776832]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
      </column>
      <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
      </column>
      <column aggregation='User' caption='Calculate_accounts' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_527765595207630863]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
        <calculation class='tableau' formula='SUM([ACTIVE_ACCOUNTS])' />
      </column>
      <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
        <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
      </column>
      <column aggregation='Count' caption='Dco Reason Code' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Description' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Reason' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Type Code' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_CODE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Reason Uid (Vw Dim Dco Reason Rpt)' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Year' caption='Dco Run Date (Vw Dim Dco Run Rpt)' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[DCO_RUN_UID (VW_F_CIE_SCORING_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dco Run Uid' datatype='string' default-type='nominal' layered='true' name='[DCO_RUN_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Description' datatype='string' default-type='nominal' layered='true' name='[DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Brand Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_BRAND_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Country Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_COUNTRY_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Customer Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_CUSTOMER_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Product Key (Vw Dim Segment Rpt)' datatype='integer' default-type='ordinal' layered='true' name='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Product Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_PRODUCT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Segment Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_SEGMENT_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Dim Segment Type Key' datatype='integer' default-type='ordinal' layered='true' name='[DIM_SEGMENT_TYPE_KEY]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Ds Name' datatype='string' default-type='nominal' layered='true' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='User' caption='Average score' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM (copy)_1327436000330481664]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
        <calculation class='tableau' formula='SUM([FINAL_SCORE_SUM])/SUM([SUGGESTION_COUNT])' />
      </column>
      <column aggregation='Sum' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
      <column aggregation='Count' caption='Hcp Segment' datatype='string' default-type='nominal' layered='true' name='[HCP_SEGMENT]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[INFLUENCE_DISPLAY_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[INFLUENCE_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' datatype='real' default-type='quantitative' layered='true' name='[INFLUENCE_VALUE]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
      <column aggregation='Count' caption='Is Active Src' datatype='boolean' default-type='nominal' layered='true' name='[IS_ACTIVE_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' caption='Is Competitor' datatype='boolean' default-type='nominal' layered='true' name='[IS_COMPETITOR]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' caption='Is Deleted Src' datatype='boolean' default-type='nominal' layered='true' name='[IS_DELETED_SRC]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[LAST_DCO_RUN_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Latest Run' datatype='boolean' default-type='nominal' layered='true' name='[LATEST_RUN]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Count' caption='Post Proc Status' datatype='string' default-type='nominal' layered='true' name='[POST_PROC_STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Id' datatype='integer' default-type='ordinal' layered='true' name='[PRODUCT_ID]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Name English' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME_ENGLISH]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Type' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Uid (Vw Dim Product Dco Rpt)' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Uid (Vw Dim Segment Rpt)' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Product Uid' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[RECOMMENDED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
      <column aggregation='Year' caption='Record End Date' datatype='date' default-type='ordinal' layered='true' name='[RECORD_END_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Year' caption='Record Start Date' datatype='date' default-type='ordinal' layered='true' name='[RECORD_START_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Description' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_DESCRIPTION]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Uid (Vw Dim Dco Run Rpt)' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Uid (Vw Dim Scenario Rpt)' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Uid' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Scenario Ver' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_VER]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Segment Type' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Se Config Id (Vw Dim Dse Config)' datatype='integer' default-type='ordinal' layered='true' name='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SE_CONFIG_ID]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' caption='Status' datatype='string' default-type='nominal' layered='true' name='[STATUS]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[SUGGESTION_CANDIDATE_UID]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
      <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
      <column aggregation='Year' caption='Updated Ts' datatype='datetime' default-type='ordinal' layered='true' name='[UPDATED_TS]' pivot='key' role='dimension' type='ordinal' user-datatype='datetime' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_REASON_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DCO_RUN_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_DSE_CONFIG' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_PRODUCT_DCO_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SCENARIO_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_DIM_SEGMENT_TYPE_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <_.fcp.ObjectModelTableType.true...column aggregation='Count' caption='VW_F_CIE_SCORING_RPT' datatype='table' default-type='quantitative' layered='true' name='[__tableau_internal_object_id__].[VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF]' pivot='key' role='measure' type='quantitative' user-datatype='table' visual-totals='Default' />
      <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
      <column-instance column='[Calculation_1506454080614776832]' derivation='None' name='[none:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
      <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[none:CHANNEL:nk]' type='palette'>
            <map to='#499894'>
              <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
            </map>
            <map to='#a0cbe8'>
              <bucket>&quot;SEND_CHANNEL&quot;</bucket>
            </map>
            <map to='#d37295'>
              <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_1506454080614776832:nk]' type='palette'>
            <map to='#499894'>
              <bucket>&quot;T3&quot;</bucket>
            </map>
            <map to='#86bcb6'>
              <bucket>&quot;SEND_CHANNEL&quot;</bucket>
            </map>
            <map to='#8cd17d'>
              <bucket>&quot;T2&quot;</bucket>
            </map>
            <map to='#a0cbe8'>
              <bucket>&quot;notier&quot;</bucket>
            </map>
            <map to='#d7b5a6'>
              <bucket>&quot;WEB_INTERACTIVE_CHANNEL&quot;</bucket>
            </map>
            <map to='#f1ce63'>
              <bucket>&quot;ST&quot;</bucket>
            </map>
            <map to='#ff9d9a'>
              <bucket>&quot;T1&quot;</bucket>
            </map>
            <map to='#ff9d9a'>
              <bucket>&quot;VISIT_CHANNEL&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:DCO_REASON_TYPE_NAME:nk]' type='palette'>
            <map to='#bab0ac'>
              <bucket>&quot;Reject&quot;</bucket>
            </map>
            <map to='#ff9da7'>
              <bucket>&quot;Recommend&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
          <calculation class='tableau' formula='&quot;Channel&quot;' />
        </column>
        <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
          <calculation class='tableau' formula='&quot;Published&quot;' />
        </column>
        <column _.fcp.ParameterDefaultValues.true...source-field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[INFLUENCE_DISPLAY_NAME]' caption='P_Influence' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account Long-term Importance&quot;'>
          <calculation class='tableau' formula='&quot;Account Long-term Importance&quot;' />
        </column>
      </datasource-dependencies>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='VW_DIM_DCO_REASON_RPT' id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_DCO_RUN_RPT' id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_DSE_CONFIG' id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_PRODUCT_DCO_RPT' id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SCENARIO_RPT' id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SEGMENT_RPT' id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_DIM_SEGMENT_TYPE_RPT' id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
          <object caption='VW_F_CIE_SCORING_RPT' id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF'>
            <properties context=''>
              <relation name='sqlproxy' table='[sqlproxy]' type='table' />
            </properties>
          </object>
        </objects>
        <relationships>
          <relationship>
            <expression op='='>
              <expression op='[DCO_REASON_UID]' />
              <expression op='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_B770B56CF10341C49F834BCEFF284A08' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[DCO_RUN_UID (VW_F_CIE_SCORING_RPT)]' />
              <expression op='[DCO_RUN_UID]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_DCO_RUN_RPT (DCO.VW_DIM_DCO_RUN_RPT)_E6D73A211E2E4F6CA9923733059C7B46' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SE_CONFIG_ID]' />
              <expression op='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG)]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_DSE_CONFIG (DCO.VW_DIM_DSE_CONFIG)_A90224156B964303A446B89A222C1D27' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[PRODUCT_UID]' />
              <expression op='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_0201C70DB55D4A7AAC30D3260B321A19' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SCENARIO_UID]' />
              <expression op='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_3DB8BA9CD51146748DC95B3932253F41' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[HCP_SEGMENT]' />
              <expression op='[SEGMENT_NAME]' />
            </expression>
            <first-end-point object-id='VW_F_CIE_SCORING_RPT (DCO.VW_F_CIE_SCORING_RPT)_E5DF248700954FA6B4C788D60D6AF3CF' />
            <second-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SEGMENT_TYPE]' />
              <expression op='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' />
            </expression>
            <first-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_4B47B4FE375449FCA512006FD1BF1F8B' />
            <second-end-point object-id='VW_DIM_SEGMENT_TYPE_RPT (DCO.VW_DIM_SEGMENT_TYPE_RPT)_1DB8CCB768874ACF9DF32BCD99A3E608' />
          </relationship>
        </relationships>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
  </datasources>
  <worksheets>
    <worksheet name='1.0 View Filters'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontcolor='#000000' fontname='Tableau Medium' fontsize='15'>${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scores</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66'>
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Ds Name' datatype='string' default-type='nominal' layered='true' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DS_NAME]' derivation='None' name='[none:DS_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SUGGESTION_COUNT]' derivation='Sum' name='[sum:SUGGESTION_COUNT:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]' filter-group='3'>
            <groupfilter function='level-members' level='[none:CHANNEL:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1604407389790982145:ok]' filter-group='13'>
            <groupfilter function='member' level='[none:Calculation_1604407389790982145:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_864972641503416320:nk]' filter-group='12'>
            <groupfilter function='level-members' level='[none:Calculation_864972641503416320:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]' filter-group='7' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DS_NAME:nk]'>
            <groupfilter function='union' user:ui-domain='database' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:DS_NAME:nk]' member='&quot;DS6&quot;' />
              <groupfilter function='member' level='[none:DS_NAME:nk]' member='&quot;DS_1|DS_2&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]' filter-group='9'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' filter-group='5'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SE_CONFIG_NAME:nk]' filter-group='6'>
            <groupfilter function='level-members' level='[none:SE_CONFIG_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_864972641503416320:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1604407389790982145:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DS_NAME:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='mark'>
            <encoding attr='color' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[sum:SUGGESTION_COUNT:qk]' type='custom-interpolated'>
              <color-palette custom='true' name='' type='ordered-sequential'>
                <color>#f1f1f1</color>
                <color>#f2f2f2</color>
                <color>#f3f3f3</color>
                <color>#f5f5f5</color>
                <color>#f6f6f6</color>
                <color>#f8f8f8</color>
                <color>#f9f9f9</color>
                <color>#fafafa</color>
                <color>#fcfcfc</color>
                <color>#fdfdfd</color>
                <color>#ffffff</color>
              </color-palette>
            </encoding>
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' value='Segment type'>
              <formatted-text>
                <run fontcolor='#000000' fontname='Tableau Medium'>Segment type</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]' value='Segment'>
              <formatted-text>
                <run fontcolor='#000000' fontname='Tableau Medium'>Segment</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]' value='Product'>
              <formatted-text>
                <run fontcolor='#000000' fontname='Tableau Medium'>Product</run>
              </formatted-text>
            </format>
            <format attr='title' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]' value='Run Date Range'>
              <formatted-text>
                <run fontcolor='#000000'>Run Date Range</run>
              </formatted-text>
            </format>
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[sum:SUGGESTION_COUNT:qk]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-cull' value='true' />
                <format attr='mark-labels-show' value='false' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows />
        <cols />
      </table>
      <simple-id uuid='{FD4BD652-DEF6-4AAD-AFB5-B2085C2A4B0D}' />
    </worksheet>
    <worksheet name='1.1 CIE Scoring by Channel '>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontcolor='#000000' fontname='Tableau Medium' fontsize='12'>Expected Value by Channel/Segment</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
              <calculation class='tableau' formula='&quot;Channel&quot;' />
            </column>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66'>
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' layered='true' name='[Calculation_1506454080614776832]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
            </column>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date (Vw Dim Dco Run Rpt)' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Ds Name' datatype='string' default-type='nominal' layered='true' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='User' caption='Average score' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM (copy)_1327436000330481664]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='SUM([FINAL_SCORE_SUM])/SUM([SUGGESTION_COUNT])' />
            </column>
            <column aggregation='Sum' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1506454080614776832]' derivation='None' name='[none:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DS_NAME]' derivation='None' name='[none:DS_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' pivot='key' type='ordinal' />
            <column-instance column='[FINAL_SCORE_SUM (copy)_1327436000330481664]' derivation='User' name='[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]' filter-group='3'>
            <groupfilter function='level-members' level='[none:CHANNEL:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1604407389790982145:ok]'>
            <groupfilter function='member' level='[none:Calculation_1604407389790982145:ok]' member='1' user:ui-domain='database' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]' filter-group='7' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DS_NAME:nk]'>
            <groupfilter function='member' level='[none:DS_NAME:nk]' member='&quot;DS_1|DS_2&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]' filter-group='9'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' filter-group='5'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SE_CONFIG_NAME:nk]' filter-group='6'>
            <groupfilter function='level-members' level='[none:SE_CONFIG_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <shelf-sorts>
            <shelf-sort-v2 dimension-to-sort='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]' direction='DESC' is-on-innermost-dimension='true' measure-to-sort-by='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' shelf='rows'>
              <sort-filter-info>
                <sort-filter level-name='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' member-value='#2021-04-12 00:00:00#' />
              </sort-filter-info>
            </shelf-sort-v2>
          </shelf-sorts>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DS_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1604407389790982145:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='display' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' scope='cols' value='false' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]' value='93' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' value='n!en_US!#,##0.00' />
          </style-rule>
          <style-rule element='header'>
            <format attr='height' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' value='36' />
          </style-rule>
          <style-rule element='label'>
            <format attr='display' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]' value='false' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' value='*m/d/yy' />
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' value='n!en_US!#,##0.00' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#f0f3fa' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878'>Channel/Segment:</run>
                <run bold='true'><![CDATA[    <[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1506454080614776832:nk]>  ]]></run>
                <run bold='true' fontcolor='#787878'>Æ </run>
                <run fontcolor='#787878'>Æ     </run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Run Date:&#9;          </run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Average Score:&#9;           </run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#555555'>Reason Type:                </run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]>]]></run>
                <run fontcolor='#787878'>Æ </run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minheight' value='-1' />
                <format attr='maxheight' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1506454080614776832:nk] / [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk])</rows>
        <cols>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok] * [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk])</cols>
      </table>
      <simple-id uuid='{1F0392D2-7827-44E7-B260-7E43FE9D63C5}' />
    </worksheet>
    <worksheet name='1.2 Overall Score: By Segment'>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontcolor='#1d1c1d' fontname='Tableau Medium' fontsize='12'>Accounts by Channel/Segment</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
              <calculation class='tableau' formula='&quot;Channel&quot;' />
            </column>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66'>
            <column aggregation='Sum' caption='Active Accounts' datatype='integer' default-type='quantitative' layered='true' name='[ACTIVE_ACCOUNTS]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' layered='true' name='[Calculation_1506454080614776832]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
            </column>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='User' caption='Calculate_accounts' datatype='integer' default-type='quantitative' layered='true' name='[Calculation_527765595207630863]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='SUM([ACTIVE_ACCOUNTS])' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date (Vw Dim Dco Run Rpt)' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Ds Name' datatype='string' default-type='nominal' layered='true' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='User' caption='Average score' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM (copy)_1327436000330481664]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default'>
              <calculation class='tableau' formula='SUM([FINAL_SCORE_SUM])/SUM([SUGGESTION_COUNT])' />
            </column>
            <column aggregation='Sum' datatype='real' default-type='quantitative' layered='true' name='[FINAL_SCORE_SUM]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' datatype='integer' default-type='quantitative' layered='true' name='[SUGGESTION_COUNT]' pivot='key' role='measure' type='quantitative' user-datatype='integer' visual-totals='Default' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1506454080614776832]' derivation='None' name='[none:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DS_NAME]' derivation='None' name='[none:DS_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_527765595207630863]' derivation='User' name='[usr:Calculation_527765595207630863:qk]' pivot='key' type='quantitative' />
            <column-instance column='[FINAL_SCORE_SUM (copy)_1327436000330481664]' derivation='User' name='[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]' filter-group='3'>
            <groupfilter function='level-members' level='[none:CHANNEL:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1604407389790982145:ok]' filter-group='13'>
            <groupfilter function='member' level='[none:Calculation_1604407389790982145:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_864972641503416320:nk]' filter-group='12'>
            <groupfilter function='level-members' level='[none:Calculation_864972641503416320:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]' filter-group='7' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DS_NAME:nk]'>
            <groupfilter function='member' level='[none:DS_NAME:nk]' member='&quot;DS_1|DS_2&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]' filter-group='9'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' filter-group='5'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SE_CONFIG_NAME:nk]' filter-group='6'>
            <groupfilter function='level-members' level='[none:SE_CONFIG_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DS_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1604407389790982145:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_864972641503416320:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:Calculation_527765595207630863:qk]' scope='rows' value='' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-format' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]' value='*m/d/yy' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='title'>
            <format attr='border-color' value='#d4a6c8' />
            <format attr='border-width' value='0' />
            <format attr='border-style' value='none' />
            <format attr='background-color' value='#f0f3fa' />
          </style-rule>
        </style>
        <panes>
          <pane id='1' selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]' />
              <text column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:Calculation_527765595207630863:qk]' />
              <lod column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]' />
            </encodings>
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878'>Channel/Segment:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1506454080614776832:nk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Run Date:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Active Accounts: &#9;</run>
                <run bold='true' fontcolor='#000000'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:Calculation_527765595207630863:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#666666'>Average Score</run>
                <run fontcolor='#787878'>:        </run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:FINAL_SCORE_SUM (copy)_1327436000330481664:qk]>]]></run>
                <run>Æ&#10;</run>
                <run fontcolor='#666666'>Reason Type:  </run>
                <run bold='true'><![CDATA[          <[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-cull' value='true' />
                <format attr='mark-labels-show' value='true' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[usr:Calculation_527765595207630863:qk]</rows>
        <cols>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1506454080614776832:nk] / ([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[tdy:DCO_RUN_DATE (VW_DIM_DCO_RUN_RPT):ok] / [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]))</cols>
      </table>
      <simple-id uuid='{EB804495-EACA-49E7-A27A-0782A9A91F3A}' />
    </worksheet>
    <worksheet name='1.3 Score Distribution'>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontname='Tableau Medium' fontsize='12'><![CDATA[<[Parameters].[Parameter 3]>: Score Distribution]]></run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66' />
            <datasource name='Parameters' />
          </datasources>
          <datasource-dependencies datasource='Parameters'>
            <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
              <calculation class='tableau' formula='&quot;Channel&quot;' />
            </column>
            <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
              <calculation class='tableau' formula='&quot;Published&quot;' />
            </column>
            <column _.fcp.ParameterDefaultValues.true...source-field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[INFLUENCE_DISPLAY_NAME]' caption='P_Influence' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account Long-term Importance&quot;'>
              <calculation class='tableau' formula='&quot;Account Long-term Importance&quot;' />
            </column>
          </datasource-dependencies>
          <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66'>
            <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Influencer' datatype='boolean' default-type='nominal' layered='true' name='[Calculation_1445936995299098631]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default'>
              <calculation class='tableau' formula='[Parameters].[Parameter 3]=[INFLUENCE_DISPLAY_NAME]' />
            </column>
            <column aggregation='Count' caption='Channel / Segment' datatype='string' default-type='nominal' layered='true' name='[Calculation_1506454080614776832]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 1]&#13;&#10;WHEN &apos;Channel&apos; THEN [CHANNEL]&#13;&#10;WHEN &apos;Segment&apos; THEN [SEGMENT_NAME]&#13;&#10;END' />
            </column>
            <column aggregation='Sum' caption='Scenario group' datatype='integer' default-type='ordinal' layered='true' name='[Calculation_1604407389790982145]' pivot='key' role='dimension' type='ordinal' user-datatype='integer' visual-totals='Default'>
              <calculation class='tableau' formula='CASE [Parameters].[Parameter 2]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED])&#13;&#10;END' />
            </column>
            <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
              <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
            </column>
            <column aggregation='Count' caption='Reason Type' datatype='string' default-type='nominal' layered='true' name='[DCO_REASON_TYPE_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Ds Name' datatype='string' default-type='nominal' layered='true' name='[DS_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' datatype='string' default-type='nominal' layered='true' name='[INFLUENCE_DISPLAY_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Sum' datatype='real' default-type='quantitative' layered='true' name='[INFLUENCE_VALUE]' pivot='key' role='measure' type='quantitative' user-datatype='real' visual-totals='Default' />
            <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
            <column aggregation='Year' datatype='date' default-type='ordinal' layered='true' name='[LAST_DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
            <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Scenario Name' datatype='string' default-type='nominal' layered='true' name='[SCENARIO_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
            <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1445936995299098631]' derivation='None' name='[none:Calculation_1445936995299098631:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1506454080614776832]' derivation='None' name='[none:Calculation_1506454080614776832:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_1604407389790982145]' derivation='None' name='[none:Calculation_1604407389790982145:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_REASON_TYPE_NAME]' derivation='None' name='[none:DCO_REASON_TYPE_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[DS_NAME]' derivation='None' name='[none:DS_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[INFLUENCE_VALUE]' derivation='None' name='[none:INFLUENCE_VALUE:qk]' pivot='key' type='quantitative' />
            <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
            <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
            <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]' filter-group='3'>
            <groupfilter function='level-members' level='[none:CHANNEL:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1445936995299098631:nk]'>
            <groupfilter function='member' level='[none:Calculation_1445936995299098631:nk]' member='true' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1604407389790982145:ok]' filter-group='13'>
            <groupfilter function='member' level='[none:Calculation_1604407389790982145:ok]' member='1' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_864972641503416320:nk]' filter-group='12'>
            <groupfilter function='level-members' level='[none:Calculation_864972641503416320:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='quantitative' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]' filter-group='7' included-values='all' />
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DS_NAME:nk]'>
            <groupfilter function='member' level='[none:DS_NAME:nk]' member='&quot;DS6&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]' filter-group='4'>
            <groupfilter function='level-members' level='[none:PRODUCT_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]' filter-group='9'>
            <groupfilter function='level-members' level='[none:SEGMENT_NAME:nk]' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' filter-group='5'>
            <groupfilter function='level-members' level='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SE_CONFIG_NAME:nk]' filter-group='6'>
            <groupfilter function='level-members' level='[none:SE_CONFIG_NAME:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DS_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1604407389790982145:ok]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SE_CONFIG_NAME:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_864972641503416320:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1445936995299098631:nk]</column>
            <column>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:INFLUENCE_VALUE:qk]' scope='rows' value='' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#f0f3fa' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Circle' />
            <encodings>
              <color column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]' />
            </encodings>
            <reference-line axis-column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:INFLUENCE_VALUE:qk]' boxplot-mark-exclusion='false' boxplot-whisker-type='standard' enable-instant-analytics='true' formula='average' id='refline0' label-type='automatic' probability='95' scope='per-cell' symmetric='false' value-column='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:INFLUENCE_VALUE:qk]' z-order='1' />
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878'>Channel / Segment: &#9;</run>
                <run>Æ&#10;</run>
                <run fontcolor='#787878'>Influence Value:&#9;</run>
                <run bold='true'><![CDATA[<[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:INFLUENCE_VALUE:qk]>]]></run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='size' value='0.25' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:INFLUENCE_VALUE:qk]</rows>
        <cols>([${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1506454080614776832:nk] / [${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk])</cols>
      </table>
      <simple-id uuid='{F4108823-4371-4C00-BED7-D6E0F50F79E7}' />
    </worksheet>
  </worksheets>
  <dashboards>
    <dashboard _.fcp.AccessibleZoneTabOrder.true...enable-sort-zone-taborder='true' name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scores'>
      <repository-location derived-from='${TABLEAU_URL}/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEScores/${CUSTOMER_NAME}${ENVIRONMENT}CIEScores?rev=' id='7067938' path='/t/${TABLEAU_SITE}/workbooks/${CUSTOMER_NAME}${ENVIRONMENT}CIEScores' revision='' site='${TABLEAU_SITE}' />
      <style />
      <size sizing-mode='automatic' />
      <datasources>
        <datasource name='Parameters' />
        <datasource caption='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scoring Data Model' name='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66' />
      </datasources>
      <datasource-dependencies datasource='Parameters'>
        <column caption='Channel / Segment' datatype='string' name='[Parameter 1]' param-domain-type='list' role='measure' type='nominal' value='&quot;Channel&quot;'>
          <calculation class='tableau' formula='&quot;Channel&quot;' />
          <members>
            <member value='&quot;Channel&quot;' />
            <member value='&quot;Segment&quot;' />
          </members>
        </column>
        <column caption='Scenario Group' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
          <calculation class='tableau' formula='&quot;Published&quot;' />
          <members>
            <member value='&quot;(All)&quot;' />
            <member value='&quot;Published&quot;' />
          </members>
        </column>
        <column _.fcp.ParameterDefaultValues.true...source-field='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[INFLUENCE_DISPLAY_NAME]' caption='P_Influence' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Account Long-term Importance&quot;'>
          <calculation class='tableau' formula='&quot;Account Long-term Importance&quot;' />
        </column>
      </datasource-dependencies>
      <datasource-dependencies datasource='${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66'>
        <column aggregation='Count' caption='Channel' datatype='string' default-type='nominal' layered='true' name='[CHANNEL]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Scenario' datatype='string' default-type='nominal' layered='true' name='[Calculation_864972641503416320]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default'>
          <calculation class='tableau' formula='[SCENARIO_NAME]+IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;,&apos;&apos;)' />
        </column>
        <column aggregation='Year' caption='Dco Run Date' datatype='date' default-type='ordinal' layered='true' name='[DCO_RUN_DATE]' pivot='key' role='dimension' type='ordinal' user-datatype='date' visual-totals='Default' />
        <column aggregation='Count' caption='Product Name' datatype='string' default-type='nominal' layered='true' name='[PRODUCT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Segment' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Segment Type (Vw Dim Segment Type Rpt)' datatype='string' default-type='nominal' layered='true' name='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column aggregation='Count' caption='Config' datatype='string' default-type='nominal' layered='true' name='[SE_CONFIG_NAME]' pivot='key' role='dimension' type='nominal' user-datatype='string' visual-totals='Default' />
        <column-instance column='[CHANNEL]' derivation='None' name='[none:CHANNEL:nk]' pivot='key' type='nominal' />
        <column-instance column='[Calculation_864972641503416320]' derivation='None' name='[none:Calculation_864972641503416320:nk]' pivot='key' type='nominal' />
        <column-instance column='[DCO_RUN_DATE]' derivation='None' name='[none:DCO_RUN_DATE:qk]' pivot='key' type='quantitative' />
        <column-instance column='[PRODUCT_NAME]' derivation='None' name='[none:PRODUCT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SEGMENT_NAME]' derivation='None' name='[none:SEGMENT_NAME:nk]' pivot='key' type='nominal' />
        <column-instance column='[SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT)]' derivation='None' name='[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' pivot='key' type='nominal' />
        <column-instance column='[SE_CONFIG_NAME]' derivation='None' name='[none:SE_CONFIG_NAME:nk]' pivot='key' type='nominal' />
      </datasource-dependencies>
      <zones>
        <zone h='100000' id='192' type-v2='layout-basic' w='100000' x='0' y='0' />
        <zone h='6736' id='5' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]' type-v2='filter' values='relevant' w='18709' x='79360' y='4878' />
        <zone h='6736' id='7' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]' type-v2='filter' values='relevant' w='18648' x='40314' y='4878' />
        <zone h='6736' id='10' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' type-v2='filter' values='relevant' w='18709' x='966' y='4878' />
        <zone h='42656' id='11' name='1.1 CIE Scoring by Channel ' w='45263' x='362' y='19624'>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
          </zone-style>
        </zone>
        <zone h='42656' id='21' name='1.2 Overall Score: By Segment' w='45263' x='52323' y='19624'>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
          </zone-style>
        </zone>
        <zone h='36237' id='23' name='1.3 Score Distribution' w='78696' x='18950' y='63182'>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
          </zone-style>
        </zone>
        <zone h='6736' id='62' mode='checkdropdown' name='1.1 CIE Scoring by Channel ' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]' type-v2='filter' values='relevant' w='18709' x='40314' y='11614' />
        <zone h='5343' id='4' name='1.0 View Filters' w='33313' x='33253' y='0' />
        <zone h='6736' id='112' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]' type-v2='filter' values='relevant' w='18709' x='20821' y='4878' />
        <zone h='9872' id='159' leg-item-layout='vert' name='1.1 CIE Scoring by Channel ' pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]' type-v2='color' w='6457' x='45987' y='20209' />
        <zone h='6736' id='162' mode='compact' param='[Parameters].[Parameter 2]' type-v2='paramctrl' w='18709' x='20821' y='11614' />
        <zone h='6736' id='171' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SE_CONFIG_NAME:nk]' type-v2='filter' values='relevant' w='18709' x='59928' y='4878' />
        <zone h='6736' id='203' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_864972641503416320:nk]' type-v2='filter' values='relevant' w='18709' x='966' y='11614' />
        <zone h='6736' id='206' mode='compact' param='[Parameters].[Parameter 1]' type-v2='paramctrl' w='18709' x='59928' y='11614'>
          <formatted-text>
            <run>Channel / Segment</run>
          </formatted-text>
        </zone>
        <zone custom-title='true' friendly-name='Expected Value/Components' h='7578' id='222' mode='compact' param='[Parameters].[Parameter 3]' type-v2='paramctrl' w='15052' x='767' y='65527'>
          <formatted-text>
            <run>Expected Value/Components</run>
            <run>Æ&#10;</run>
          </formatted-text>
        </zone>
      </zones>
      <devicelayouts>
        <devicelayout auto-generated='true' name='Phone'>
          <size maxheight='1650' minheight='1650' sizing-mode='vscroll' />
          <zones>
            <zone h='100000' id='267' type-v2='layout-basic' w='100000' x='0' y='0'>
              <zone h='98042' id='266' param='vert' type-v2='layout-flow' w='98910' x='545' y='979'>
                <zone h='6736' id='5' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_RUN_DATE:qk]' type-v2='filter' values='relevant' w='18709' x='79360' y='4878'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6736' id='7' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_NAME:nk]' type-v2='filter' values='relevant' w='18648' x='40314' y='4878'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6736' id='10' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_TYPE (VW_DIM_SEGMENT_TYPE_RPT):nk]' type-v2='filter' values='relevant' w='18709' x='966' y='4878'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6736' id='112' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SEGMENT_NAME:nk]' type-v2='filter' values='relevant' w='18709' x='20821' y='4878'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6736' id='171' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:SE_CONFIG_NAME:nk]' type-v2='filter' values='relevant' w='18709' x='59928' y='4878'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6736' id='203' mode='checkdropdown' name='1.0 View Filters' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_864972641503416320:nk]' type-v2='filter' values='relevant' w='18709' x='966' y='11614'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='43' h='5343' id='4' is-fixed='true' name='1.0 View Filters' w='33313' x='33253' y='0'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6736' id='162' mode='compact' param='[Parameters].[Parameter 2]' type-v2='paramctrl' w='18709' x='20821' y='11614'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6736' id='206' mode='compact' param='[Parameters].[Parameter 1]' type-v2='paramctrl' w='18709' x='59928' y='11614'>
                  <formatted-text>
                    <run>Channel / Segment</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='6736' id='62' mode='checkdropdown' name='1.1 CIE Scoring by Channel ' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:CHANNEL:nk]' type-v2='filter' values='relevant' w='18709' x='40314' y='11614'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='42656' id='11' is-fixed='true' name='1.1 CIE Scoring by Channel ' w='45263' x='362' y='19624'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='9872' id='159' leg-item-layout='vert' name='1.1 CIE Scoring by Channel ' pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]' type-v2='color' w='6457' x='45987' y='20209'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='42656' id='21' is-fixed='true' name='1.2 Overall Score: By Segment' w='45263' x='52323' y='19624'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='36237' id='23' is-fixed='true' name='1.3 Score Distribution' w='78696' x='18950' y='63182'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone custom-title='true' friendly-name='Expected Value/Components' h='7578' id='222' mode='compact' param='[Parameters].[Parameter 3]' type-v2='paramctrl' w='15052' x='767' y='65527'>
                  <formatted-text>
                    <run>Expected Value/Components</run>
                    <run>Æ&#10;</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
              </zone>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{20C0D16F-8DDA-47D7-B139-90266B29D7FF}' />
    </dashboard>
  </dashboards>
  <windows source-height='30'>
    <window class='dashboard' maximized='true' name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Scores'>
      <viewpoints>
        <viewpoint name='1.0 View Filters'>
          <zoom type='fit-width' />
        </viewpoint>
        <viewpoint name='1.1 CIE Scoring by Channel '>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='1.2 Overall Score: By Segment'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='1.3 Score Distribution'>
          <zoom type='entire-view' />
        </viewpoint>
      </viewpoints>
      <active id='-1' />
      <simple-id uuid='{B3FD28D4-34C7-40DA-85EF-914DE5A45153}' />
    </window>
    <window class='worksheet' hidden='true' name='1.0 View Filters'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[sum:SUGGESTION_COUNT:qk]' type='color' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DS_NAME:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{6D59CEB9-F96B-4619-9B5E-D8B55B09A1BD}' />
    </window>
    <window class='worksheet' hidden='true' name='1.1 CIE Scoring by Channel '>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]' type='color' />
            <card param='[Parameters].[Parameter 2]' type='parameter' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1506454080614776832:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1604407389790982145:ok]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_864972641503416320:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{3F377C81-9194-4137-A554-A02FD4AD379E}' />
    </window>
    <window class='worksheet' hidden='true' name='1.2 Overall Score: By Segment'>
      <cards>
        <edge name='left'>
          <strip size='248'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='1' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]' type='color' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1506454080614776832:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{1A09BA7F-9334-47A7-8CD6-2C70EA57BD45}' />
    </window>
    <window class='worksheet' hidden='true' name='1.3 Score Distribution'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card mode='compact' param='[Parameters].[Parameter 1]' type='parameter' />
            <card pane-specification-id='0' param='[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]' type='color' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:Calculation_1445936995299098631:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_CODE:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:DCO_REASON_TYPE_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:INFLUENCE_DISPLAY_NAME:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:PRODUCT_UID:nk]</field>
            <field>[${CUSTOMER_NAME}${ENVIRONMENT}.sqlproxy.019xwet1fghnl01ewrhm71720w66].[none:RECOMMENDED:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{03372FAE-CBD4-46E9-9CB2-2D228F43E343}' />
    </window>
  </windows>
</workbook>
