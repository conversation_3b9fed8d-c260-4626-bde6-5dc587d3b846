<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20231.23.0310.1045                               -->
<datasource formatted-name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model' inline='true' source-platform='linux' version='18.1' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location id='${CUSTOMER_NAME}${ENVIRONMENT}CIEComparisonSummaryDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.0' site='${TABLEAU_SITE}' />
  <connection class='federated'>
    <named-connections>
      <named-connection caption='${SNOW_SERVER}' name='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0g09arn0f6yyuw1apyas31tb03bh'>
        <connection authentication='Username Password' class='snowflake' dbname='${SNOW_DBNAME}' odbc-connect-string-extras='' one-time-sql='' schema='DCO' server='${SNOW_SERVER}' server-oauth='' service='${SNOW_ROLE}' username='${SNOW_USERNAME}' warehouse='${SNOW_WAREHOUSE}' workgroup-auth-mode='prompt' />
      </named-connection>
    </named-connections>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0g09arn0f6yyuw1apyas31tb03bh' name='Custom SQL Query' type='text'>SELECT s.SE_CONFIG_ID, s.SCENARIO_UID,  SE_CONFIG_NAME, n.SCENARIO_NAME, DCO_RUN_DATE, SUGGESTIONCOUNT, REPCOUNT, ACCOUNTCOUNT, FACTORCOUNT, ACCOUNTCHANNELCOUNT, &apos;DCO 3&apos; flag,
 SE_CONFIG_NAME SE_CONFIG_NAME1, n.SCENARIO_NAME SCENARIO_NAME1, null SE_CONFIG_NAME2, null SCENARIO_NAME2,
DCO_RUN_DATE DCO_RUN_DATE1, null DCO_RUN_DATE2, n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT  c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID

UNION ALL 

SELECT s.SE_CONFIG_ID, s.SCENARIO_UID,  SE_CONFIG_NAME, n.SCENARIO_NAME, DCO_RUN_DATE, SUGGESTIONCOUNT, REPCOUNT, ACCOUNTCOUNT, FACTORCOUNT, ACCOUNTCHANNELCOUNT, &apos;DCO 3 2&apos; flag, 
null, null,  SE_CONFIG_NAME, n.SCENARIO_NAME,
null DCO_RUN_DATE1, DCO_RUN_DATE DCO_RUN_DATE2, n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT  c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID</_.fcp.ObjectModelEncapsulateLegacy.false...relation>
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0g09arn0f6yyuw1apyas31tb03bh' name='Custom SQL Query' type='text'>SELECT s.SE_CONFIG_ID, s.SCENARIO_UID,  SE_CONFIG_NAME, n.SCENARIO_NAME, DCO_RUN_DATE, SUGGESTIONCOUNT, REPCOUNT, ACCOUNTCOUNT, FACTORCOUNT, ACCOUNTCHANNELCOUNT, &apos;DCO 3&apos; flag,
 SE_CONFIG_NAME SE_CONFIG_NAME1, n.SCENARIO_NAME SCENARIO_NAME1, null SE_CONFIG_NAME2, null SCENARIO_NAME2,
DCO_RUN_DATE DCO_RUN_DATE1, null DCO_RUN_DATE2, n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT  c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID

UNION ALL 

SELECT s.SE_CONFIG_ID, s.SCENARIO_UID,  SE_CONFIG_NAME, n.SCENARIO_NAME, DCO_RUN_DATE, SUGGESTIONCOUNT, REPCOUNT, ACCOUNTCOUNT, FACTORCOUNT, ACCOUNTCHANNELCOUNT, &apos;DCO 3 2&apos; flag, 
null, null,  SE_CONFIG_NAME, n.SCENARIO_NAME,
null DCO_RUN_DATE1, DCO_RUN_DATE DCO_RUN_DATE2, n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT  c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID</_.fcp.ObjectModelEncapsulateLegacy.true...relation>
    <refresh increment-key='' incremental-updates='false' />
    <metadata-records>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[SE_CONFIG_ID]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SE_CONFIG_ID</remote-alias>
        <ordinal>1</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_UID]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SCENARIO_UID</remote-alias>
        <ordinal>2</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SE_CONFIG_NAME]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SE_CONFIG_NAME</remote-alias>
        <ordinal>3</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_NAME]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SCENARIO_NAME</remote-alias>
        <ordinal>4</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>100</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_RUN_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DCO_RUN_DATE]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>DCO_RUN_DATE</remote-alias>
        <ordinal>5</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SUGGESTIONCOUNT</remote-name>
        <remote-type>131</remote-type>
        <local-name>[SUGGESTIONCOUNT]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SUGGESTIONCOUNT</remote-alias>
        <ordinal>6</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>18</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>REPCOUNT</remote-name>
        <remote-type>131</remote-type>
        <local-name>[REPCOUNT]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>REPCOUNT</remote-alias>
        <ordinal>7</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>18</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>ACCOUNTCOUNT</remote-name>
        <remote-type>131</remote-type>
        <local-name>[ACCOUNTCOUNT]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>ACCOUNTCOUNT</remote-alias>
        <ordinal>8</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>18</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>FACTORCOUNT</remote-name>
        <remote-type>131</remote-type>
        <local-name>[FACTORCOUNT]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>FACTORCOUNT</remote-alias>
        <ordinal>9</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>18</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>ACCOUNTCHANNELCOUNT</remote-name>
        <remote-type>131</remote-type>
        <local-name>[ACCOUNTCHANNELCOUNT]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>ACCOUNTCHANNELCOUNT</remote-alias>
        <ordinal>10</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>18</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>FLAG</remote-name>
        <remote-type>129</remote-type>
        <local-name>[FLAG]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>FLAG</remote-alias>
        <ordinal>11</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>7</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_NAME1</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SE_CONFIG_NAME1]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SE_CONFIG_NAME1</remote-alias>
        <ordinal>12</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_NAME1</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_NAME1]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SCENARIO_NAME1</remote-alias>
        <ordinal>13</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>100</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_NAME2</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SE_CONFIG_NAME2]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SE_CONFIG_NAME2</remote-alias>
        <ordinal>14</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>16777216</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_NAME2</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_NAME2]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>SCENARIO_NAME2</remote-alias>
        <ordinal>15</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>100</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_RUN_DATE1</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DCO_RUN_DATE1]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>DCO_RUN_DATE1</remote-alias>
        <ordinal>16</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_RUN_DATE2</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DCO_RUN_DATE2]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>DCO_RUN_DATE2</remote-alias>
        <ordinal>17</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>LAST_DCO_RUN_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[LAST_DCO_RUN_DATE]</local-name>
        <parent-name>[Custom SQL Query]</parent-name>
        <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
        <ordinal>18</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[_09814B39EDE341E2BFD5D4E067A74E4D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
    </metadata-records>
  </connection>
  <aliases enabled='yes' />
  <column datatype='string' name='[:Measure Names]' role='dimension' type='nominal'>
    <aliases>
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:ACCOUNTCHANNELCOUNT:qk]&quot;' value='Suggested Account/Channel Count ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:accountChannelCount1:qk]&quot;' value='Suggested Account/Channel Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:ACCOUNTCOUNT:qk]&quot;' value='Suggested Account Count ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:accountCount1:qk]&quot;' value='Suggested Account Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:FACTORCOUNT:qk]&quot;' value='Factors fired Count ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:factorCount1:qk]&quot;' value='Factors fired Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:REPCOUNT:qk]&quot;' value='Suggested Rep Count ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:repCount1:qk]&quot;' value='Suggested Rep Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:SUGGESTIONCOUNT:qk]&quot;' value='Suggestion Count ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[sum:suggestionCount1:qk]&quot;' value='Suggestion Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Average Suggestion Per Rep  (copy)_526921183993229317:qk]&quot;' value='Average Suggestion Per Rep ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703754788887:qk]&quot;' value='Suggestion Count  ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703755186201:qk]&quot;' value='Suggested Rep Count  ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703755206682:qk]&quot;' value='Suggested Account Count  ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703755235355:qk]&quot;' value='Suggested Account/Channel Count  ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703755259932:qk]&quot;' value='Factors fired Count  ' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Comparison Summary Data Model].[usr:Calculation_1347983703755272221:qk]&quot;' value='Average Suggestion Per Rep   ' />
    </aliases>
  </column>
  <column datatype='integer' name='[ACCOUNTCHANNELCOUNT]' role='measure' type='quantitative' />
  <column datatype='integer' name='[ACCOUNTCOUNT]' role='measure' type='quantitative' />
  <column caption='AVERAGE Suggestion Per REP' datatype='real' name='[Average Suggestion Per Rep  (copy)_526921183993229317]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='ROUND(SUM([SUGGESTIONCOUNT])/SUM([REPCOUNT]), 2)' />
  </column>
  <column caption='Difference' datatype='string' name='[Calculation_1439181579318042630]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='&apos;Difference&apos;' />
  </column>
  <column caption='Run Date Filter 1' datatype='date' name='[Config (1) (copy)_2155535403100246044]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = &apos;DCO 3&apos; THEN [DCO_RUN_DATE1]&#13;&#10;END' />
  </column>
  <column caption='Config Filter 1' datatype='string' name='[Config (copy)_2155535403097620507]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = FLAG THEN [SE_CONFIG_NAME1]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
  </column>
  <column caption='Config Filter 2' datatype='string' name='[Config Filter (copy)_2155535403107680289]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [SE_CONFIG_NAME2]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
  </column>
  <column caption='Run Date' datatype='date' hidden='true' name='[DCO_RUN_DATE]' role='dimension' type='ordinal' />
  <column caption='DIFF Factors fired Count' datatype='integer' name='[DIFF 1 Factors fired Count (copy)_2155535403060961294]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([FACTORCOUNT]) END)&#13;&#10;&#13;&#10;-&#13;&#10;&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403021361155] THEN ([FACTORCOUNT]) END)' />
  </column>
  <column caption='DIFF Suggested Account Count' datatype='integer' name='[DIFF 1 Suggested Account Count (copy)_2155535403082145812]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403021361155] THEN ([ACCOUNTCOUNT]) END)' />
  </column>
  <column caption='DIFF Suggested Account/Channel Count' datatype='integer' name='[DIFF 1 Suggested Account/Channel Count (copy)_2155535403082158101]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([ACCOUNTCHANNELCOUNT])END)  &#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403021361155] THEN ([ACCOUNTCHANNELCOUNT])END)' />
  </column>
  <column caption='DIFF Suggested Count' datatype='integer' name='[DIFF 1 Suggested Count (copy)_2155535403081334802]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403021361155] THEN ([SUGGESTIONCOUNT]) END)' />
  </column>
  <column caption='DIFF Suggested Rep Count' datatype='integer' name='[DIFF 1 Suggested Rep Count (copy)_2155535403081949203]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END)&#13;&#10;-&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403021361155] THEN ([REPCOUNT]) END)' />
  </column>
  <column caption='DIFF Average Suggestion Per Rep' datatype='real' name='[DIFF Average Suggestion Per Rep (copy)_122160172807217152]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='ROUND( SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10; /&#13;&#10;SUM(IF FLAG = [Parameters].[Choose data sources (copy)_2155535403020820482] THEN ([REPCOUNT]) END), 2)&#13;&#10;&#13;&#10;-&#13;&#10;&#13;&#10;ROUND(SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403021361155] THEN ([SUGGESTIONCOUNT]) END)&#13;&#10; /&#13;&#10;SUM(IF FLAG = [Parameters].[Source 1 (copy)_2155535403021361155] THEN ([REPCOUNT]) END)&#13;&#10;, 2)' />
  </column>
  <column datatype='integer' name='[FACTORCOUNT]' role='measure' type='quantitative' />
  <column datatype='string' name='[FLAG]' role='dimension' type='nominal'>
    <aliases>
      <alias key='&quot;DCO 3&quot;' value='Source 1' />
      <alias key='&quot;DCO 3 2&quot;' value='Source 2' />
    </aliases>
  </column>
  <column datatype='integer' name='[REPCOUNT]' role='measure' type='quantitative' />
  <column caption='Scenario Filter 1' datatype='string' name='[Run Date (1) (copy)_2155535403100545053]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Choose data sources (copy)_2155535403020820482] = [FLAG] THEN [SCENARIO_NAME1 (copy)_1347983701206401024]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
  </column>
  <column caption='Run Date Filter 2' datatype='date' name='[Run Date Filter (copy)_2155535403107491872]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = &apos;DCO 3 2&apos; THEN [DCO_RUN_DATE2]&#13;&#10;END' />
  </column>
  <column caption='SCENARIO NEW' datatype='string' name='[SCENARIO_NAME1 (copy)_1347983701206401024]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[SCENARIO_NAME1]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
  </column>
  <column caption='SCENARIO NEW 2' datatype='string' name='[SCENARIO_NAME2 (copy)_1347983701206581249]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[SCENARIO_NAME2]+IFNULL(&apos; (&apos;+STR(MONTH([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(DAY([LAST_DCO_RUN_DATE]))&#10;+&apos;/&apos;&#10;+STR(YEAR([LAST_DCO_RUN_DATE]))&#10;+&apos;)&apos;,&apos;&apos;)' />
  </column>
  <column caption='Scenario' datatype='string' hidden='true' name='[SCENARIO_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[SCENARIO_UID]' role='dimension' type='nominal' />
  <column datatype='integer' hidden='true' name='[SE_CONFIG_ID]' role='measure' type='quantitative' />
  <column caption='Config' datatype='string' hidden='true' name='[SE_CONFIG_NAME]' role='dimension' type='nominal' />
  <column datatype='integer' name='[SUGGESTIONCOUNT]' role='measure' type='quantitative' />
  <column caption='Scenario Filter 2' datatype='string' name='[Scenario Filter (copy)_2155535403107311647]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [Parameters].[Source 1 (copy)_2155535403021361155] = FLAG THEN [SCENARIO_NAME2 (copy)_1347983701206581249]&#13;&#10;ELSE &apos; &lt;Difference&gt;&apos;&#13;&#10;END' />
  </column>
  <_.fcp.ObjectModelTableType.true...column caption='Snowflake Summary' datatype='table' name='[__tableau_internal_object_id__].[_09814B39EDE341E2BFD5D4E067A74E4D]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='Snowflake' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[_3A533CA42284488BB56AA31CE554622E]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='MySQL' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[_3CEE7E99CC7146BA96A53F15A77B5338]' role='measure' type='quantitative' />
  <column datatype='real' hidden='true' name='[enhancedInsightsCount1]' role='measure' type='quantitative' />
  <column datatype='real' hidden='true' name='[insightsCount1]' role='measure' type='quantitative' />
  <column datatype='integer' hidden='true' name='[seConfigId]' role='measure' type='quantitative' />
  <column-instance column='[ACCOUNTCHANNELCOUNT]' derivation='Sum' name='[sum:ACCOUNTCHANNELCOUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[ACCOUNTCOUNT]' derivation='Sum' name='[sum:ACCOUNTCOUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[FACTORCOUNT]' derivation='Sum' name='[sum:FACTORCOUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[REPCOUNT]' derivation='Sum' name='[sum:REPCOUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[SUGGESTIONCOUNT]' derivation='Sum' name='[sum:SUGGESTIONCOUNT:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Average Suggestion Per Rep  (copy)_526921183993229317]' derivation='User' name='[usr:Average Suggestion Per Rep  (copy)_526921183993229317:qk]' pivot='key' type='quantitative' />
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-aliased-fields='true' show-hidden-fields='true' show-structure='true' />
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
  <datasource-dependencies datasource='Parameters'>
    <column alias='Engine output' caption='Source 1' datatype='string' name='[Choose data sources (copy)_2155535403020820482]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3&quot;'>
      <calculation class='tableau' formula='&quot;DCO 3&quot;' />
      <aliases>
        <alias key='&quot;DCO 3&quot;' value='Engine output' />
        <alias key='&quot;DCO 4&quot;' value='Post-proc output' />
        <alias key='&quot;DSE 1&quot;' value='Traditional' />
        <alias key='&quot;DSE 2&quot;' value='Next Gen' />
      </aliases>
      <members>
        <member alias='Traditional' value='&quot;DSE 1&quot;' />
        <member alias='Next Gen' value='&quot;DSE 2&quot;' />
        <member alias='Engine output' value='&quot;DCO 3&quot;' />
        <member alias='Post-proc output' value='&quot;DCO 4&quot;' />
      </members>
    </column>
    <column alias='Engine output' caption='Source 2' datatype='string' name='[Source 1 (copy)_2155535403021361155]' param-domain-type='list' role='measure' type='nominal' value='&quot;DCO 3 2&quot;'>
      <calculation class='tableau' formula='&quot;DCO 3 2&quot;' />
      <aliases>
        <alias key='&quot;DCO 3 2&quot;' value='Engine output' />
        <alias key='&quot;DCO 4 2&quot;' value='Post-proc output' />
        <alias key='&quot;DSE 1 2&quot;' value='Traditional' />
        <alias key='&quot;DSE 2 2&quot;' value='Next Gen' />
      </aliases>
      <members>
        <member alias='Traditional' value='&quot;DSE 1 2&quot;' />
        <member alias='Next Gen' value='&quot;DSE 2 2&quot;' />
        <member alias='Engine output' value='&quot;DCO 3 2&quot;' />
        <member alias='Post-proc output' value='&quot;DCO 4 2&quot;' />
      </members>
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='Snowflake Summary' id='_09814B39EDE341E2BFD5D4E067A74E4D'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0g09arn0f6yyuw1apyas31tb03bh' name='Custom SQL Query' type='text'>SELECT s.SE_CONFIG_ID, s.SCENARIO_UID,  SE_CONFIG_NAME, n.SCENARIO_NAME, DCO_RUN_DATE, SUGGESTIONCOUNT, REPCOUNT, ACCOUNTCOUNT, FACTORCOUNT, ACCOUNTCHANNELCOUNT, &apos;DCO 3&apos; flag,
 SE_CONFIG_NAME SE_CONFIG_NAME1, n.SCENARIO_NAME SCENARIO_NAME1, null SE_CONFIG_NAME2, null SCENARIO_NAME2,
DCO_RUN_DATE DCO_RUN_DATE1, null DCO_RUN_DATE2, n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT  c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID

UNION ALL 

SELECT s.SE_CONFIG_ID, s.SCENARIO_UID,  SE_CONFIG_NAME, n.SCENARIO_NAME, DCO_RUN_DATE, SUGGESTIONCOUNT, REPCOUNT, ACCOUNTCOUNT, FACTORCOUNT, ACCOUNTCHANNELCOUNT, &apos;DCO 3 2&apos; flag, 
null, null,  SE_CONFIG_NAME, n.SCENARIO_NAME,
null DCO_RUN_DATE1, DCO_RUN_DATE DCO_RUN_DATE2, n.LAST_DCO_RUN_DATE
FROM DCO.VW_DCO_RESULTS_LATEST_SUMMARY_RPT s
LEFT join DCO.VW_DIM_DSE_CONFIG_RPT  c on c.SE_CONFIG_ID = s.SE_CONFIG_ID
LEFT join DCO.VW_DIM_SCENARIO_RPT n on n.SCENARIO_UID =s.SCENARIO_UID</relation>
        </properties>
      </object>
    </objects>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
