<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20231.23.0310.1045                               -->
<datasource formatted-name='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model' inline='true' source-platform='mac' version='18.1' xml:base='${TABLEAU_URL}' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelExtractV2.true...ObjectModelExtractV2 />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
  </document-format-change-manifest>
  <repository-location id='${CUSTOMER_NAME}${ENVIRONMENT}CIEOutputTracingDataModel' path='/t/${TABLEAU_SITE}/datasources' revision='1.0' site='${TABLEAU_SITE}' />
  <connection class='federated'>
    <named-connections>
      <named-connection caption='${SNOW_SERVER}' name='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz'>
        <connection authentication='Username Password' class='snowflake' dbname='${SNOW_DBNAME}' odbc-connect-string-extras='' one-time-sql='' schema='DCO' server='${SNOW_SERVER}' server-oauth='' service='${SNOW_ROLE}' username='${SNOW_USERNAME}' warehouse='${SNOW_WAREHOUSE}' workgroup-auth-mode='prompt' />
      </named-connection>
    </named-connections>
    <_.fcp.ObjectModelEncapsulateLegacy.false...relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_ACCOUNT_DCO_RPT' table='[DCO].[VW_DIM_ACCOUNT_DCO_RPT]' type='table' />
    <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2' table='[DCO].[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_ACCOUNT_DCO_RPT' table='[DCO].[VW_DIM_ACCOUNT_DCO_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_CHANNEL_DCO_RPT' table='[DCO].[VW_DIM_CHANNEL_DCO_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_CUSTOMER_DCO_RPT' table='[DCO].[VW_DIM_CUSTOMER_DCO_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_DCO_MESSAGE_RPT' table='[DCO].[VW_DIM_DCO_MESSAGE_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_DCO_REASON_RPT' table='[DCO].[VW_DIM_DCO_REASON_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_DSE_CONFIG_RPT' table='[DCO].[VW_DIM_DSE_CONFIG_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_FACTOR_DCO_RPT' table='[DCO].[VW_DIM_FACTOR_DCO_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_PRODUCT_DCO_RPT' table='[DCO].[VW_DIM_PRODUCT_DCO_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_SCENARIO_RPT' table='[DCO].[VW_DIM_SCENARIO_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_SEGMENT_RPT' table='[DCO].[VW_DIM_SEGMENT_RPT]' type='table' />
      <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_USER_DSE_DCO_RPT' table='[DCO].[VW_DIM_USER_DSE_DCO_RPT]' type='table' />
    </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
    <cols>
      <map key='[ACCOUNT_ID]' value='[VW_DIM_ACCOUNT_DCO_RPT].[ACCOUNT_ID]' />
      <map key='[ACCOUNT_NAME]' value='[VW_DIM_ACCOUNT_DCO_RPT].[ACCOUNT_NAME]' />
      <map key='[ACCOUNT_TYPE]' value='[VW_DIM_ACCOUNT_DCO_RPT].[ACCOUNT_TYPE]' />
      <map key='[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT].[ACCOUNT_UID]' />
      <map key='[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[ACCOUNT_UID]' />
      <map key='[AFFILIATED_HCP_UID]' value='[VW_DIM_ACCOUNT_DCO_RPT].[AFFILIATED_HCP_UID]' />
      <map key='[CHANNEL_CODE]' value='[VW_DIM_CHANNEL_DCO_RPT].[CHANNEL_CODE]' />
      <map key='[CHANNEL_LOCALIZABLE_NAME]' value='[VW_DIM_CHANNEL_DCO_RPT].[CHANNEL_LOCALIZABLE_NAME]' />
      <map key='[CHANNEL_LOCAL_NAME]' value='[VW_DIM_CHANNEL_DCO_RPT].[CHANNEL_LOCAL_NAME]' />
      <map key='[CHANNEL_NAME]' value='[VW_DIM_CHANNEL_DCO_RPT].[CHANNEL_NAME]' />
      <map key='[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]' value='[VW_DIM_CHANNEL_DCO_RPT].[CHANNEL_UID]' />
      <map key='[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[CHANNEL_UID]' />
      <map key='[COUNTRY_CODE]' value='[VW_DIM_CUSTOMER_DCO_RPT].[COUNTRY_CODE]' />
      <map key='[COUNTRY_NAME]' value='[VW_DIM_CUSTOMER_DCO_RPT].[COUNTRY_NAME]' />
      <map key='[CREATEDAT_SRC]' value='[VW_DIM_FACTOR_DCO_RPT].[CREATEDAT_SRC]' />
      <map key='[CREATED_BY]' value='[VW_DIM_SEGMENT_RPT].[CREATED_BY]' />
      <map key='[CREATED_DT_KEY]' value='[VW_DIM_FACTOR_DCO_RPT].[CREATED_DT_KEY]' />
      <map key='[CREATED_TS]' value='[VW_DIM_SEGMENT_RPT].[CREATED_TS]' />
      <map key='[CUSTOMER_DESC]' value='[VW_DIM_CUSTOMER_DCO_RPT].[CUSTOMER_DESC]' />
      <map key='[CUSTOMER_ID]' value='[VW_DIM_FACTOR_DCO_RPT].[CUSTOMER_ID]' />
      <map key='[CUSTOMER_NAME]' value='[VW_DIM_CUSTOMER_DCO_RPT].[CUSTOMER_NAME]' />
      <map key='[DCO_REASON_CODE]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_CODE]' />
      <map key='[DCO_REASON_DESCRIPTION]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_DESCRIPTION]' />
      <map key='[DCO_REASON_NAME]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_NAME]' />
      <map key='[DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[DCO_REASON_SUMMARY]' />
      <map key='[DCO_REASON_TYPE_CODE]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_TYPE_CODE]' />
      <map key='[DCO_REASON_TYPE_NAME]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_TYPE_NAME]' />
      <map key='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' value='[VW_DIM_DCO_REASON_RPT].[DCO_REASON_UID]' />
      <map key='[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[DCO_REASON_UID]' />
      <map key='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[DCO_RUN_DATE]' />
      <map key='[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[DCO_RUN_UID]' />
      <map key='[DESCRIPTION]' value='[VW_DIM_DSE_CONFIG_RPT].[DESCRIPTION]' />
      <map key='[DIM_ACCOUNT_KEY]' value='[VW_DIM_ACCOUNT_DCO_RPT].[DIM_ACCOUNT_KEY]' />
      <map key='[DIM_ACCOUNT_TYPE_KEY]' value='[VW_DIM_ACCOUNT_DCO_RPT].[DIM_ACCOUNT_TYPE_KEY]' />
      <map key='[DIM_ACTION_GROUP_KEY]' value='[VW_DIM_CHANNEL_DCO_RPT].[DIM_ACTION_GROUP_KEY]' />
      <map key='[DIM_ACTIVITY_DELIVERY_MODE_KEY]' value='[VW_DIM_CHANNEL_DCO_RPT].[DIM_ACTIVITY_DELIVERY_MODE_KEY]' />
      <map key='[DIM_BRAND_KEY]' value='[VW_DIM_PRODUCT_DCO_RPT].[DIM_BRAND_KEY]' />
      <map key='[DIM_CHANNEL_CATEGORY_KEY]' value='[VW_DIM_CHANNEL_DCO_RPT].[DIM_CHANNEL_CATEGORY_KEY]' />
      <map key='[DIM_CHANNEL_KEY]' value='[VW_DIM_CHANNEL_DCO_RPT].[DIM_CHANNEL_KEY]' />
      <map key='[DIM_CHANNEL_TYPE_KEY]' value='[VW_DIM_CHANNEL_DCO_RPT].[DIM_CHANNEL_TYPE_KEY]' />
      <map key='[DIM_COUNTRY_KEY (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT].[DIM_COUNTRY_KEY]' />
      <map key='[DIM_COUNTRY_KEY (VW_DIM_PRODUCT_DCO_RPT)]' value='[VW_DIM_PRODUCT_DCO_RPT].[DIM_COUNTRY_KEY]' />
      <map key='[DIM_COUNTRY_KEY (VW_DIM_USER_DSE_DCO_RPT)]' value='[VW_DIM_USER_DSE_DCO_RPT].[DIM_COUNTRY_KEY]' />
      <map key='[DIM_COUNTRY_KEY]' value='[VW_DIM_CUSTOMER_DCO_RPT].[DIM_COUNTRY_KEY]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]' value='[VW_DIM_CUSTOMER_DCO_RPT].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_DCO_MESSAGE_RPT)]' value='[VW_DIM_DCO_MESSAGE_RPT].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO_RPT)]' value='[VW_DIM_FACTOR_DCO_RPT].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_PRODUCT_DCO_RPT)]' value='[VW_DIM_PRODUCT_DCO_RPT].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_CUSTOMER_KEY (VW_DIM_USER_DSE_DCO_RPT)]' value='[VW_DIM_USER_DSE_DCO_RPT].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[DIM_CUSTOMER_KEY]' />
      <map key='[DIM_FACTOR_KEY]' value='[VW_DIM_FACTOR_DCO_RPT].[DIM_FACTOR_KEY]' />
      <map key='[DIM_MESSAGE_CHANNEL_KEY]' value='[VW_DIM_DCO_MESSAGE_RPT].[DIM_MESSAGE_CHANNEL_KEY]' />
      <map key='[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]' value='[VW_DIM_DCO_MESSAGE_RPT].[DIM_MESSAGE_KEY]' />
      <map key='[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[DIM_MESSAGE_KEY]' />
      <map key='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT].[DIM_PRODUCT_KEY]' />
      <map key='[DIM_PRODUCT_KEY]' value='[VW_DIM_PRODUCT_DCO_RPT].[DIM_PRODUCT_KEY]' />
      <map key='[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT].[DIM_SEGMENT_KEY]' />
      <map key='[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[DIM_SEGMENT_KEY]' />
      <map key='[DIM_SUGGESTION_DELIVERY_MODE_KEY]' value='[VW_DIM_CHANNEL_DCO_RPT].[DIM_SUGGESTION_DELIVERY_MODE_KEY]' />
      <map key='[DIM_USER_DSE_KEY]' value='[VW_DIM_USER_DSE_DCO_RPT].[DIM_USER_DSE_KEY]' />
      <map key='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[DRIVER_TYPE]' />
      <map key='[DW_CREATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT].[DW_CREATED_TS]' />
      <map key='[DW_CREATED_TS (VW_DIM_FACTOR_DCO_RPT)]' value='[VW_DIM_FACTOR_DCO_RPT].[DW_CREATED_TS]' />
      <map key='[DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[DW_CREATED_TS]' />
      <map key='[DW_DELETED_FLAG (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT].[DW_DELETED_FLAG]' />
      <map key='[DW_DELETED_FLAG (VW_DIM_FACTOR_DCO_RPT)]' value='[VW_DIM_FACTOR_DCO_RPT].[DW_DELETED_FLAG]' />
      <map key='[DW_DELETED_FLAG]' value='[VW_DIM_USER_DSE_DCO_RPT].[DW_DELETED_FLAG]' />
      <map key='[DW_UPDATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT].[DW_UPDATED_TS]' />
      <map key='[DW_UPDATED_TS]' value='[VW_DIM_FACTOR_DCO_RPT].[DW_UPDATED_TS]' />
      <map key='[FACTOR_NAME]' value='[VW_DIM_FACTOR_DCO_RPT].[FACTOR_NAME]' />
      <map key='[FACTOR_TYPE]' value='[VW_DIM_FACTOR_DCO_RPT].[FACTOR_TYPE]' />
      <map key='[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]' value='[VW_DIM_FACTOR_DCO_RPT].[FACTOR_UID]' />
      <map key='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[FACTOR_UID]' />
      <map key='[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[FINAL_SCORE]' />
      <map key='[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[HCP_SEGMENT]' />
      <map key='[IS_ACTIVATED_SRC]' value='[VW_DIM_USER_DSE_DCO_RPT].[IS_ACTIVATED_SRC]' />
      <map key='[IS_ACTIVE_SRC]' value='[VW_DIM_PRODUCT_DCO_RPT].[IS_ACTIVE_SRC]' />
      <map key='[IS_COMPETITOR]' value='[VW_DIM_PRODUCT_DCO_RPT].[IS_COMPETITOR]' />
      <map key='[IS_DELETED_SRC (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT].[IS_DELETED_SRC]' />
      <map key='[IS_DELETED_SRC (VW_DIM_USER_DSE_DCO_RPT)]' value='[VW_DIM_USER_DSE_DCO_RPT].[IS_DELETED_SRC]' />
      <map key='[IS_DELETED_SRC]' value='[VW_DIM_PRODUCT_DCO_RPT].[IS_DELETED_SRC]' />
      <map key='[IS_PERSON_ACCOUNT]' value='[VW_DIM_ACCOUNT_DCO_RPT].[IS_PERSON_ACCOUNT]' />
      <map key='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]' value='[VW_DIM_SCENARIO_RPT].[IS_PUBLISHED]' />
      <map key='[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[IS_PUBLISHED]' />
      <map key='[LAST_DCO_RUN_DATE]' value='[VW_DIM_SCENARIO_RPT].[LAST_DCO_RUN_DATE]' />
      <map key='[LAST_DCO_RUN_UID]' value='[VW_DIM_SCENARIO_RPT].[LAST_DCO_RUN_UID]' />
      <map key='[MESSAGE_CHANNEL_ID]' value='[VW_DIM_DCO_MESSAGE_RPT].[MESSAGE_CHANNEL_ID]' />
      <map key='[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]' value='[VW_DIM_DCO_MESSAGE_RPT].[MESSAGE_CHANNEL_NAME]' />
      <map key='[MESSAGE_ID]' value='[VW_DIM_DCO_MESSAGE_RPT].[MESSAGE_ID]' />
      <map key='[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]' value='[VW_DIM_DCO_MESSAGE_RPT].[MESSAGE_NAME]' />
      <map key='[PARENT_ACCOUNT_UID]' value='[VW_DIM_ACCOUNT_DCO_RPT].[PARENT_ACCOUNT_UID]' />
      <map key='[PRODUCT_ID]' value='[VW_DIM_SEGMENT_RPT].[PRODUCT_ID]' />
      <map key='[PRODUCT_NAME]' value='[VW_DIM_PRODUCT_DCO_RPT].[PRODUCT_NAME]' />
      <map key='[PRODUCT_NAME_ENGLISH]' value='[VW_DIM_PRODUCT_DCO_RPT].[PRODUCT_NAME_ENGLISH]' />
      <map key='[PRODUCT_TYPE]' value='[VW_DIM_PRODUCT_DCO_RPT].[PRODUCT_TYPE]' />
      <map key='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' value='[VW_DIM_PRODUCT_DCO_RPT].[PRODUCT_UID]' />
      <map key='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT].[PRODUCT_UID]' />
      <map key='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[PRODUCT_UID]' />
      <map key='[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[RECOMMENDED]' />
      <map key='[RECORD_END_DATE (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT].[RECORD_END_DATE]' />
      <map key='[RECORD_END_DATE]' value='[VW_DIM_PRODUCT_DCO_RPT].[RECORD_END_DATE]' />
      <map key='[RECORD_START_DATE (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT].[RECORD_START_DATE]' />
      <map key='[RECORD_START_DATE]' value='[VW_DIM_PRODUCT_DCO_RPT].[RECORD_START_DATE]' />
      <map key='[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[REP_TEAM_UID]' />
      <map key='[RPT_FACTOR_UID]' value='[VW_DIM_FACTOR_DCO_RPT].[RPT_FACTOR_UID]' />
      <map key='[RUN_CONFIG_FACTOR_ID]' value='[VW_DIM_FACTOR_DCO_RPT].[RUN_CONFIG_FACTOR_ID]' />
      <map key='[SALES_REFERENCE_ACCOUNT_ID]' value='[VW_DIM_ACCOUNT_DCO_RPT].[SALES_REFERENCE_ACCOUNT_ID]' />
      <map key='[SCENARIO_DESCRIPTION]' value='[VW_DIM_SCENARIO_RPT].[SCENARIO_DESCRIPTION]' />
      <map key='[SCENARIO_NAME]' value='[VW_DIM_SCENARIO_RPT].[SCENARIO_NAME]' />
      <map key='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' value='[VW_DIM_SCENARIO_RPT].[SCENARIO_UID]' />
      <map key='[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[SCENARIO_UID]' />
      <map key='[SEGMENT_NAME]' value='[VW_DIM_SEGMENT_RPT].[SEGMENT_NAME]' />
      <map key='[SEGMENT_TYPE]' value='[VW_DIM_SEGMENT_RPT].[SEGMENT_TYPE]' />
      <map key='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]' value='[VW_DIM_DSE_CONFIG_RPT].[SE_CONFIG_ID]' />
      <map key='[SE_CONFIG_ID (VW_DIM_USER_DSE_DCO_RPT)]' value='[VW_DIM_USER_DSE_DCO_RPT].[SE_CONFIG_ID]' />
      <map key='[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[SE_CONFIG_ID]' />
      <map key='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' value='[VW_DIM_DSE_CONFIG_RPT].[SE_CONFIG_NAME]' />
      <map key='[SOURCE_SYSTEM_NAME (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT].[SOURCE_SYSTEM_NAME]' />
      <map key='[SOURCE_SYSTEM_NAME]' value='[VW_DIM_FACTOR_DCO_RPT].[SOURCE_SYSTEM_NAME]' />
      <map key='[SPECIALITY_1]' value='[VW_DIM_ACCOUNT_DCO_RPT].[SPECIALITY_1]' />
      <map key='[SPECIALITY_2]' value='[VW_DIM_ACCOUNT_DCO_RPT].[SPECIALITY_2]' />
      <map key='[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[SUGGESTED_DATE]' />
      <map key='[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[SUGGESTION_REFERENCE_ID]' />
      <map key='[UPDATEDAT_SRC]' value='[VW_DIM_FACTOR_DCO_RPT].[UPDATEDAT_SRC]' />
      <map key='[UPDATED_DT_KEY]' value='[VW_DIM_FACTOR_DCO_RPT].[UPDATED_DT_KEY]' />
      <map key='[UPDATED_TS]' value='[VW_DIM_SEGMENT_RPT].[UPDATED_TS]' />
      <map key='[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]' value='[VW_DIM_USER_DSE_DCO_RPT].[USER_DSE_UID]' />
      <map key='[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2].[USER_DSE_UID]' />
      <map key='[USER_ID]' value='[VW_DIM_USER_DSE_DCO_RPT].[USER_ID]' />
      <map key='[USER_NAME]' value='[VW_DIM_USER_DSE_DCO_RPT].[USER_NAME]' />
      <map key='[USER_UID]' value='[VW_DIM_USER_DSE_DCO_RPT].[USER_UID]' />
    </cols>
    <metadata-records>
      <metadata-record class='column'>
        <remote-name>DCO_RUN_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>DCO_RUN_UID</remote-alias>
        <ordinal>1</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>SCENARIO_UID</remote-alias>
        <ordinal>2</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_RUN_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>DCO_RUN_DATE</remote-alias>
        <ordinal>3</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>ACCOUNT_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>ACCOUNT_UID</remote-alias>
        <ordinal>4</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>USER_DSE_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>USER_DSE_UID</remote-alias>
        <ordinal>5</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>REP_TEAM_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>REP_TEAM_UID</remote-alias>
        <ordinal>6</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>PRODUCT_UID</remote-alias>
        <ordinal>7</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CHANNEL_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>CHANNEL_UID</remote-alias>
        <ordinal>8</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>FACTOR_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>FACTOR_UID</remote-alias>
        <ordinal>9</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>DCO_REASON_UID</remote-alias>
        <ordinal>10</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>26</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>RECOMMENDED</remote-name>
        <remote-type>11</remote-type>
        <local-name>[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>RECOMMENDED</remote-alias>
        <ordinal>11</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_SUMMARY</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>DCO_REASON_SUMMARY</remote-alias>
        <ordinal>12</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>26</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SUGGESTION_REFERENCE_ID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>SUGGESTION_REFERENCE_ID</remote-alias>
        <ordinal>13</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>50</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DRIVER_TYPE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>DRIVER_TYPE</remote-alias>
        <ordinal>14</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_PUBLISHED</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>IS_PUBLISHED</remote-alias>
        <ordinal>15</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CUSTOMER_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
        <ordinal>16</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DW_CREATED_TS</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>DW_CREATED_TS</remote-alias>
        <ordinal>17</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>FINAL_SCORE</remote-name>
        <remote-type>5</remote-type>
        <local-name>[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>FINAL_SCORE</remote-alias>
        <ordinal>18</ordinal>
        <local-type>real</local-type>
        <aggregation>Sum</aggregation>
        <precision>15</precision>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DOUBLE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SUGGESTED_DATE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>SUGGESTED_DATE</remote-alias>
        <ordinal>19</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_SEGMENT_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>DIM_SEGMENT_KEY</remote-alias>
        <ordinal>20</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>11</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>HCP_SEGMENT</remote-name>
        <remote-type>129</remote-type>
        <local-name>[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>HCP_SEGMENT</remote-alias>
        <ordinal>21</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>SE_CONFIG_ID</remote-alias>
        <ordinal>22</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_MESSAGE_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
        <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]</parent-name>
        <remote-alias>DIM_MESSAGE_KEY</remote-alias>
        <ordinal>23</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_ACCOUNT_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_ACCOUNT_KEY]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>DIM_ACCOUNT_KEY</remote-alias>
        <ordinal>25</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DW_CREATED_TS</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DW_CREATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>DW_CREATED_TS</remote-alias>
        <ordinal>26</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DW_UPDATED_TS</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DW_UPDATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>DW_UPDATED_TS</remote-alias>
        <ordinal>27</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>ACCOUNT_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[ACCOUNT_ID]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>ACCOUNT_ID</remote-alias>
        <ordinal>28</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>ACCOUNT_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>ACCOUNT_UID</remote-alias>
        <ordinal>29</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SALES_REFERENCE_ACCOUNT_ID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SALES_REFERENCE_ACCOUNT_ID]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>SALES_REFERENCE_ACCOUNT_ID</remote-alias>
        <ordinal>30</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PARENT_ACCOUNT_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PARENT_ACCOUNT_UID]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>PARENT_ACCOUNT_UID</remote-alias>
        <ordinal>31</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>AFFILIATED_HCP_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[AFFILIATED_HCP_UID]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>AFFILIATED_HCP_UID</remote-alias>
        <ordinal>32</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>ACCOUNT_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[ACCOUNT_NAME]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>ACCOUNT_NAME</remote-alias>
        <ordinal>33</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>ACCOUNT_TYPE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[ACCOUNT_TYPE]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>ACCOUNT_TYPE</remote-alias>
        <ordinal>34</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SPECIALITY_1</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SPECIALITY_1]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>SPECIALITY_1</remote-alias>
        <ordinal>35</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SPECIALITY_2</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SPECIALITY_2]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>SPECIALITY_2</remote-alias>
        <ordinal>36</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DW_DELETED_FLAG</remote-name>
        <remote-type>11</remote-type>
        <local-name>[DW_DELETED_FLAG (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>DW_DELETED_FLAG</remote-alias>
        <ordinal>37</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>RECORD_START_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[RECORD_START_DATE (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>RECORD_START_DATE</remote-alias>
        <ordinal>38</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>RECORD_END_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[RECORD_END_DATE (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>RECORD_END_DATE</remote-alias>
        <ordinal>39</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SOURCE_SYSTEM_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SOURCE_SYSTEM_NAME (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>SOURCE_SYSTEM_NAME</remote-alias>
        <ordinal>40</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_DELETED_SRC</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_DELETED_SRC (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>IS_DELETED_SRC</remote-alias>
        <ordinal>41</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_PERSON_ACCOUNT</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_PERSON_ACCOUNT]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>IS_PERSON_ACCOUNT</remote-alias>
        <ordinal>42</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_COUNTRY_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_COUNTRY_KEY (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>DIM_COUNTRY_KEY</remote-alias>
        <ordinal>43</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_ACCOUNT_TYPE_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_ACCOUNT_TYPE_KEY]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>DIM_ACCOUNT_TYPE_KEY</remote-alias>
        <ordinal>44</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CUSTOMER_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CUSTOMER_KEY (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_ACCOUNT_DCO_RPT]</parent-name>
        <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
        <ordinal>45</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CHANNEL_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CHANNEL_KEY]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>DIM_CHANNEL_KEY</remote-alias>
        <ordinal>47</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CHANNEL_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>CHANNEL_UID</remote-alias>
        <ordinal>48</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CHANNEL_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CHANNEL_NAME]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>CHANNEL_NAME</remote-alias>
        <ordinal>49</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CHANNEL_CODE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CHANNEL_CODE]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>CHANNEL_CODE</remote-alias>
        <ordinal>50</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CHANNEL_LOCALIZABLE_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CHANNEL_LOCALIZABLE_NAME]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>CHANNEL_LOCALIZABLE_NAME</remote-alias>
        <ordinal>51</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CHANNEL_LOCAL_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CHANNEL_LOCAL_NAME]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>CHANNEL_LOCAL_NAME</remote-alias>
        <ordinal>52</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CHANNEL_TYPE_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CHANNEL_TYPE_KEY]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>DIM_CHANNEL_TYPE_KEY</remote-alias>
        <ordinal>53</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CHANNEL_CATEGORY_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CHANNEL_CATEGORY_KEY]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>DIM_CHANNEL_CATEGORY_KEY</remote-alias>
        <ordinal>54</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_ACTION_GROUP_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_ACTION_GROUP_KEY]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>DIM_ACTION_GROUP_KEY</remote-alias>
        <ordinal>55</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_ACTIVITY_DELIVERY_MODE_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_ACTIVITY_DELIVERY_MODE_KEY]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>DIM_ACTIVITY_DELIVERY_MODE_KEY</remote-alias>
        <ordinal>56</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_SUGGESTION_DELIVERY_MODE_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_SUGGESTION_DELIVERY_MODE_KEY]</local-name>
        <parent-name>[VW_DIM_CHANNEL_DCO_RPT]</parent-name>
        <remote-alias>DIM_SUGGESTION_DELIVERY_MODE_KEY</remote-alias>
        <ordinal>57</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CUSTOMER_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_CUSTOMER_DCO_RPT]</parent-name>
        <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
        <ordinal>59</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CUSTOMER_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CUSTOMER_NAME]</local-name>
        <parent-name>[VW_DIM_CUSTOMER_DCO_RPT]</parent-name>
        <remote-alias>CUSTOMER_NAME</remote-alias>
        <ordinal>60</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CUSTOMER_DESC</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CUSTOMER_DESC]</local-name>
        <parent-name>[VW_DIM_CUSTOMER_DCO_RPT]</parent-name>
        <remote-alias>CUSTOMER_DESC</remote-alias>
        <ordinal>61</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_COUNTRY_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_COUNTRY_KEY]</local-name>
        <parent-name>[VW_DIM_CUSTOMER_DCO_RPT]</parent-name>
        <remote-alias>DIM_COUNTRY_KEY</remote-alias>
        <ordinal>62</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>COUNTRY_CODE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[COUNTRY_CODE]</local-name>
        <parent-name>[VW_DIM_CUSTOMER_DCO_RPT]</parent-name>
        <remote-alias>COUNTRY_CODE</remote-alias>
        <ordinal>63</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>COUNTRY_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[COUNTRY_NAME]</local-name>
        <parent-name>[VW_DIM_CUSTOMER_DCO_RPT]</parent-name>
        <remote-alias>COUNTRY_NAME</remote-alias>
        <ordinal>64</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_MESSAGE_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
        <parent-name>[VW_DIM_DCO_MESSAGE_RPT]</parent-name>
        <remote-alias>DIM_MESSAGE_KEY</remote-alias>
        <ordinal>66</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_MESSAGE_CHANNEL_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_MESSAGE_CHANNEL_KEY]</local-name>
        <parent-name>[VW_DIM_DCO_MESSAGE_RPT]</parent-name>
        <remote-alias>DIM_MESSAGE_CHANNEL_KEY</remote-alias>
        <ordinal>67</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>MESSAGE_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[MESSAGE_ID]</local-name>
        <parent-name>[VW_DIM_DCO_MESSAGE_RPT]</parent-name>
        <remote-alias>MESSAGE_ID</remote-alias>
        <ordinal>68</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>MESSAGE_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
        <parent-name>[VW_DIM_DCO_MESSAGE_RPT]</parent-name>
        <remote-alias>MESSAGE_NAME</remote-alias>
        <ordinal>69</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>MESSAGE_CHANNEL_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[MESSAGE_CHANNEL_ID]</local-name>
        <parent-name>[VW_DIM_DCO_MESSAGE_RPT]</parent-name>
        <remote-alias>MESSAGE_CHANNEL_ID</remote-alias>
        <ordinal>70</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>MESSAGE_CHANNEL_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
        <parent-name>[VW_DIM_DCO_MESSAGE_RPT]</parent-name>
        <remote-alias>MESSAGE_CHANNEL_NAME</remote-alias>
        <ordinal>71</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CUSTOMER_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CUSTOMER_KEY (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
        <parent-name>[VW_DIM_DCO_MESSAGE_RPT]</parent-name>
        <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
        <ordinal>72</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_UID</remote-alias>
        <ordinal>74</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>2</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_CODE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_CODE]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_CODE</remote-alias>
        <ordinal>75</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>2</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_NAME]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_NAME</remote-alias>
        <ordinal>76</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>22</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_DESCRIPTION</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_DESCRIPTION]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_DESCRIPTION</remote-alias>
        <ordinal>77</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>347</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_TYPE_CODE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_TYPE_CODE]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_TYPE_CODE</remote-alias>
        <ordinal>78</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>9</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DCO_REASON_TYPE_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DCO_REASON_TYPE_NAME]</local-name>
        <parent-name>[VW_DIM_DCO_REASON_RPT]</parent-name>
        <remote-alias>DCO_REASON_TYPE_NAME</remote-alias>
        <ordinal>79</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>9</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]</local-name>
        <parent-name>[VW_DIM_DSE_CONFIG_RPT]</parent-name>
        <remote-alias>SE_CONFIG_ID</remote-alias>
        <ordinal>81</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]</local-name>
        <parent-name>[VW_DIM_DSE_CONFIG_RPT]</parent-name>
        <remote-alias>SE_CONFIG_NAME</remote-alias>
        <ordinal>82</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DESCRIPTION</remote-name>
        <remote-type>129</remote-type>
        <local-name>[DESCRIPTION]</local-name>
        <parent-name>[VW_DIM_DSE_CONFIG_RPT]</parent-name>
        <remote-alias>DESCRIPTION</remote-alias>
        <ordinal>83</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>RPT_FACTOR_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[RPT_FACTOR_UID]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>RPT_FACTOR_UID</remote-alias>
        <ordinal>85</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_FACTOR_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_FACTOR_KEY]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>DIM_FACTOR_KEY</remote-alias>
        <ordinal>86</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DW_CREATED_TS</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DW_CREATED_TS (VW_DIM_FACTOR_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>DW_CREATED_TS</remote-alias>
        <ordinal>87</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DW_UPDATED_TS</remote-name>
        <remote-type>7</remote-type>
        <local-name>[DW_UPDATED_TS]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>DW_UPDATED_TS</remote-alias>
        <ordinal>88</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>FACTOR_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[FACTOR_NAME]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>FACTOR_NAME</remote-alias>
        <ordinal>89</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>FACTOR_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>FACTOR_UID</remote-alias>
        <ordinal>90</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>FACTOR_TYPE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[FACTOR_TYPE]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>FACTOR_TYPE</remote-alias>
        <ordinal>91</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DW_DELETED_FLAG</remote-name>
        <remote-type>11</remote-type>
        <local-name>[DW_DELETED_FLAG (VW_DIM_FACTOR_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>DW_DELETED_FLAG</remote-alias>
        <ordinal>92</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SOURCE_SYSTEM_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SOURCE_SYSTEM_NAME]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>SOURCE_SYSTEM_NAME</remote-alias>
        <ordinal>93</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CUSTOMER_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
        <ordinal>94</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CREATED_DT_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[CREATED_DT_KEY]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>CREATED_DT_KEY</remote-alias>
        <ordinal>95</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>UPDATED_DT_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[UPDATED_DT_KEY]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>UPDATED_DT_KEY</remote-alias>
        <ordinal>96</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CUSTOMER_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[CUSTOMER_ID]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>CUSTOMER_ID</remote-alias>
        <ordinal>97</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>RUN_CONFIG_FACTOR_ID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[RUN_CONFIG_FACTOR_ID]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>RUN_CONFIG_FACTOR_ID</remote-alias>
        <ordinal>98</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CREATEDAT_SRC</remote-name>
        <remote-type>7</remote-type>
        <local-name>[CREATEDAT_SRC]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>CREATEDAT_SRC</remote-alias>
        <ordinal>99</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>UPDATEDAT_SRC</remote-name>
        <remote-type>7</remote-type>
        <local-name>[UPDATEDAT_SRC]</local-name>
        <parent-name>[VW_DIM_FACTOR_DCO_RPT]</parent-name>
        <remote-alias>UPDATEDAT_SRC</remote-alias>
        <ordinal>100</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_PRODUCT_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_PRODUCT_KEY]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>DIM_PRODUCT_KEY</remote-alias>
        <ordinal>102</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>PRODUCT_UID</remote-alias>
        <ordinal>103</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_BRAND_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_BRAND_KEY]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>DIM_BRAND_KEY</remote-alias>
        <ordinal>104</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_NAME]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>PRODUCT_NAME</remote-alias>
        <ordinal>105</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_NAME_ENGLISH</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_NAME_ENGLISH]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>PRODUCT_NAME_ENGLISH</remote-alias>
        <ordinal>106</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_TYPE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_TYPE]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>PRODUCT_TYPE</remote-alias>
        <ordinal>107</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>RECORD_START_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[RECORD_START_DATE]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>RECORD_START_DATE</remote-alias>
        <ordinal>108</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>RECORD_END_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[RECORD_END_DATE]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>RECORD_END_DATE</remote-alias>
        <ordinal>109</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_ACTIVE_SRC</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_ACTIVE_SRC]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>IS_ACTIVE_SRC</remote-alias>
        <ordinal>110</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_COMPETITOR</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_COMPETITOR]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>IS_COMPETITOR</remote-alias>
        <ordinal>111</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_DELETED_SRC</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_DELETED_SRC]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>IS_DELETED_SRC</remote-alias>
        <ordinal>112</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CUSTOMER_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CUSTOMER_KEY (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
        <ordinal>113</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_COUNTRY_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_COUNTRY_KEY (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_PRODUCT_DCO_RPT]</parent-name>
        <remote-alias>DIM_COUNTRY_KEY</remote-alias>
        <ordinal>114</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>SCENARIO_UID</remote-alias>
        <ordinal>116</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>40</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_NAME]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>SCENARIO_NAME</remote-alias>
        <ordinal>117</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>100</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SCENARIO_DESCRIPTION</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SCENARIO_DESCRIPTION]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>SCENARIO_DESCRIPTION</remote-alias>
        <ordinal>118</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>255</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_PUBLISHED</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>IS_PUBLISHED</remote-alias>
        <ordinal>119</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>LAST_DCO_RUN_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[LAST_DCO_RUN_UID]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>LAST_DCO_RUN_UID</remote-alias>
        <ordinal>120</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>LAST_DCO_RUN_DATE</remote-name>
        <remote-type>7</remote-type>
        <local-name>[LAST_DCO_RUN_DATE]</local-name>
        <parent-name>[VW_DIM_SCENARIO_RPT]</parent-name>
        <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
        <ordinal>121</ordinal>
        <local-type>date</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_SEGMENT_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>DIM_SEGMENT_KEY</remote-alias>
        <ordinal>123</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>11</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SEGMENT_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SEGMENT_NAME]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>SEGMENT_NAME</remote-alias>
        <ordinal>124</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>200</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SEGMENT_TYPE</remote-name>
        <remote-type>129</remote-type>
        <local-name>[SEGMENT_TYPE]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>SEGMENT_TYPE</remote-alias>
        <ordinal>125</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>100</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_PRODUCT_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>DIM_PRODUCT_KEY</remote-alias>
        <ordinal>126</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>11</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>PRODUCT_UID</remote-alias>
        <ordinal>127</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>200</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>PRODUCT_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[PRODUCT_ID]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>PRODUCT_ID</remote-alias>
        <ordinal>128</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>11</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CREATED_BY</remote-name>
        <remote-type>129</remote-type>
        <local-name>[CREATED_BY]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>CREATED_BY</remote-alias>
        <ordinal>129</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>50</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>CREATED_TS</remote-name>
        <remote-type>7</remote-type>
        <local-name>[CREATED_TS]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>CREATED_TS</remote-alias>
        <ordinal>130</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>UPDATED_TS</remote-name>
        <remote-type>7</remote-type>
        <local-name>[UPDATED_TS]</local-name>
        <parent-name>[VW_DIM_SEGMENT_RPT]</parent-name>
        <remote-alias>UPDATED_TS</remote-alias>
        <ordinal>131</ordinal>
        <local-type>datetime</local-type>
        <aggregation>Year</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>USER_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[USER_ID]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>USER_ID</remote-alias>
        <ordinal>133</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>USER_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[USER_UID]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>USER_UID</remote-alias>
        <ordinal>134</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>USER_NAME</remote-name>
        <remote-type>129</remote-type>
        <local-name>[USER_NAME]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>USER_NAME</remote-alias>
        <ordinal>135</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>false</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>SE_CONFIG_ID</remote-name>
        <remote-type>131</remote-type>
        <local-name>[SE_CONFIG_ID (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>SE_CONFIG_ID</remote-alias>
        <ordinal>136</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_USER_DSE_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_USER_DSE_KEY]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>DIM_USER_DSE_KEY</remote-alias>
        <ordinal>137</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>false</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_COUNTRY_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_COUNTRY_KEY (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>DIM_COUNTRY_KEY</remote-alias>
        <ordinal>138</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DIM_CUSTOMER_KEY</remote-name>
        <remote-type>131</remote-type>
        <local-name>[DIM_CUSTOMER_KEY (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
        <ordinal>139</ordinal>
        <local-type>integer</local-type>
        <aggregation>Sum</aggregation>
        <precision>38</precision>
        <scale>0</scale>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>USER_DSE_UID</remote-name>
        <remote-type>129</remote-type>
        <local-name>[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>USER_DSE_UID</remote-alias>
        <ordinal>140</ordinal>
        <local-type>string</local-type>
        <aggregation>Count</aggregation>
        <width>********</width>
        <contains-null>true</contains-null>
        <collation flag='0' name='binary' />
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARCHAR&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_CHAR&quot;</attribute>
          <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>DW_DELETED_FLAG</remote-name>
        <remote-type>11</remote-type>
        <local-name>[DW_DELETED_FLAG]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>DW_DELETED_FLAG</remote-alias>
        <ordinal>141</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_ACTIVATED_SRC</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_ACTIVATED_SRC]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>IS_ACTIVATED_SRC</remote-alias>
        <ordinal>142</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
      <metadata-record class='column'>
        <remote-name>IS_DELETED_SRC</remote-name>
        <remote-type>11</remote-type>
        <local-name>[IS_DELETED_SRC (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
        <parent-name>[VW_DIM_USER_DSE_DCO_RPT]</parent-name>
        <remote-alias>IS_DELETED_SRC</remote-alias>
        <ordinal>143</ordinal>
        <local-type>boolean</local-type>
        <aggregation>Count</aggregation>
        <contains-null>true</contains-null>
        <attributes>
          <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
          <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
        </attributes>
        <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
      </metadata-record>
    </metadata-records>
  </connection>
  <aliases enabled='yes' />
  <column datatype='string' name='[:Measure Names]' role='dimension' type='nominal'>
    <aliases>
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model]&quot;' value='Expected Value' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model].[avg:ACCOUNT_CNT (new version) (copy)_497647790488961026:qk]&quot;' value='Avg. Suggested Dates' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model].[avg:Calculation_497647790467399680:qk]&quot;' value='Avg. Actors' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model].[avg:REP_CNT (new version) (copy)_497647790488461313:qk]&quot;' value='Avg. Accounts' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model].[usr:CANDIDATE_CNT (new version) (copy)_497647790489550852:qk]&quot;' value='Recommended Count' />
      <alias key='&quot;[${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model].[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]&quot;' value='Candidates Count' />
    </aliases>
  </column>
  <column caption='DAY_CNT' datatype='integer' default-format='n#,##0;-#,##0' name='[ACCOUNT_CNT (new version) (copy)_497647790488961026]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='{FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], &#13;&#10;[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:&#13;&#10;countd([SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&#13;&#10;}' />
  </column>
  <column datatype='integer' hidden='true' name='[ACCOUNT_ID]' role='dimension' type='ordinal' />
  <column caption='Account' datatype='string' name='[ACCOUNT_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[ACCOUNT_TYPE]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='ACCOUNT_UID' datatype='string' name='[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[AFFILIATED_HCP_UID]' role='dimension' type='nominal' />
  <column caption='RECOMMENDED_COUNT' datatype='integer' default-format='n#,##0;-#,##0' name='[CANDIDATE_CNT (new version) (copy)_497647790489550852]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='count(iif([RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],null))' />
  </column>
  <column datatype='string' hidden='true' name='[CHANNEL_CODE]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[CHANNEL_LOCALIZABLE_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[CHANNEL_LOCAL_NAME]' role='dimension' type='nominal' />
  <column caption='Channel' datatype='string' name='[CHANNEL_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='CHANNEL_UID' datatype='string' name='[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[COUNTRY_CODE]' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' />
  <column datatype='string' name='[COUNTRY_NAME]' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' />
  <column caption='Createdat Src (Vw Dim Factor Dco)' datatype='datetime' hidden='true' name='[CREATEDAT_SRC (VW_DIM_FACTOR_DCO)]' role='dimension' type='ordinal' />
  <column caption='Created By' datatype='string' hidden='true' name='[CREATED_BY]' role='dimension' type='nominal' />
  <column caption='Created Dt Key (Vw Dim Factor Dco)' datatype='integer' hidden='true' name='[CREATED_DT_KEY (VW_DIM_FACTOR_DCO)]' role='dimension' type='ordinal' />
  <column caption='Created Ts' datatype='datetime' hidden='true' name='[CREATED_TS]' role='dimension' type='ordinal' />
  <column datatype='string' hidden='true' name='[CUSTOMER_DESC]' role='dimension' type='nominal' />
  <column caption='Customer Id (Vw Dim Factor Dco)' datatype='integer' hidden='true' name='[CUSTOMER_ID (VW_DIM_FACTOR_DCO)]' role='dimension' type='ordinal' />
  <column datatype='string' hidden='true' name='[CUSTOMER_NAME]' role='dimension' type='nominal' />
  <column caption='Reasons' datatype='string' name='[Calculation_1716434419576819713]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[DCO_REASON_CODE] +&apos;. &apos;+[DCO_REASON_NAME]' />
  </column>
  <column caption='Last Run (true)' datatype='boolean' hidden='true' name='[Calculation_465630037247307799]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [LATEST_RUN] then &apos;True&apos; end = [Parameters].[Parameter 1] &#13;&#10;OR [Parameters].[Parameter 1] = &apos;All&apos;' />
  </column>
  <column caption='REP_CNT' datatype='integer' name='[Calculation_497647790467399680]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]:countd([USER_NAME])}' />
  </column>
  <column caption='New Scenario' datatype='string' name='[Calculation_5911010483556352]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[SCENARIO_NAME]+ IFNULL(&apos; (&apos;+STR([LAST_DCO_RUN_DATE])+&apos;)&apos;, &apos;&apos;)' />
  </column>
  <column caption='Index' datatype='integer' name='[Calculation_942378240705122304]' role='measure' type='ordinal'>
    <calculation class='tableau' formula='index()'>
      <table-calc ordering-type='Rows' />
    </calculation>
  </column>
  <column caption='Channel ' datatype='string' name='[Channel (copy)_544865224190140424]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[CHANNEL_NAME]' />
  </column>
  <column caption='CANDIDATE_CNT' datatype='integer' default-format='n#,##0;-#,##0' name='[DAY_CNT (new version) (copy)_497647790489280515]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='count([PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])' />
  </column>
  <column caption='Reason' datatype='string' name='[DCO_REASON_NAME]' role='dimension' type='nominal' />
  <column caption='Reasons_' datatype='string' name='[DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[DCO_REASON_TEXT]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[DCO_REASON_TYPE_CODE]' role='dimension' type='nominal' />
  <column caption='REASON' datatype='string' name='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433] +&apos; &apos;+[DCO_REASON_NAME]' />
  </column>
  <column caption='DCO_REASON_TYPE_NAME (new)' datatype='string' name='[DCO_REASON_TYPE_NAME (new) (copy)_660058877028626433]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='IF [DCO_REASON_TYPE_NAME] =&apos;Accept&apos; and [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]then &apos;Published&apos; else [DCO_REASON_TYPE_NAME]  end' />
  </column>
  <column caption='Status' datatype='string' name='[DCO_REASON_TYPE_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' role='dimension' type='nominal' />
  <column caption='DCO_REASON_UID' datatype='string' name='[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column caption='DCO_RUN_DATE' datatype='date' name='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='ordinal' />
  <column caption='DCO_RUN_UID' datatype='string' name='[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column caption='Description' datatype='string' hidden='true' name='[DESCRIPTION]' role='dimension' type='nominal' />
  <column datatype='integer' hidden='true' name='[DIM_ACCOUNT_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_ACCOUNT_TYPE_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_ACTION_GROUP_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_ACTIVITY_DELIVERY_MODE_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_BRAND_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_CHANNEL_CATEGORY_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_CHANNEL_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_CHANNEL_TYPE_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_COUNTRY_KEY (VW_DIM_ACCOUNT_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_COUNTRY_KEY (VW_DIM_PRODUCT_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_COUNTRY_KEY (VW_DIM_REP_TEAM_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_COUNTRY_KEY (VW_DIM_RPT_FACTOR_DCO_RPT)]' role='dimension' type='ordinal' />
  <column caption='Dim Country Key (Vw Dim User Dse Dco Rpt)' datatype='integer' hidden='true' name='[DIM_COUNTRY_KEY (VW_DIM_USER_DSE_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_COUNTRY_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_ACCOUNT_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]' role='dimension' type='ordinal' />
  <column caption='Dim Customer Key (Vw Dim Dco Message Rpt)' datatype='integer' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_DCO_MESSAGE_RPT)]' role='dimension' type='ordinal' />
  <column caption='Dim Customer Key (Vw Dim Factor Dco)' datatype='integer' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_PRODUCT_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_REP_TEAM_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_RPT_FACTOR_DCO_RPT)]' role='dimension' type='ordinal' />
  <column caption='Dim Customer Key (Vw Dim User Dse Dco Rpt)' datatype='integer' hidden='true' name='[DIM_CUSTOMER_KEY (VW_DIM_USER_DSE_DCO_RPT)]' role='dimension' type='ordinal' />
  <column caption='DIM_CUSTOMER_KEY' datatype='integer' name='[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_DSE_CONFIG_KEY]' role='dimension' type='ordinal' />
  <column caption='Dim Factor Key (Vw Dim Factor Dco)' datatype='integer' hidden='true' name='[DIM_FACTOR_KEY (VW_DIM_FACTOR_DCO)]' role='dimension' type='ordinal' />
  <column caption='Dim Message Channel Key' datatype='integer' hidden='true' name='[DIM_MESSAGE_CHANNEL_KEY]' role='dimension' type='ordinal' />
  <column caption='Dim Message Key (Vw Dim Dco Message Rpt)' datatype='integer' hidden='true' name='[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]' role='dimension' type='ordinal' />
  <column caption='DIM_MESSAGE_KEY' datatype='integer' name='[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='ordinal' />
  <column caption='Dim Product Key (Vw Dim Segment Rpt)' datatype='integer' hidden='true' name='[DIM_PRODUCT_KEY (VW_DIM_SEGMENT_RPT)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_PRODUCT_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_REP_TEAM_KEY]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_RPT_FACTOR_KEY]' role='dimension' type='ordinal' />
  <column caption='Dim Segment Key (Vw Dim Segment Rpt)' datatype='integer' hidden='true' name='[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]' role='dimension' type='ordinal' />
  <column caption='DIM_SEGMENT_KEY' datatype='integer' name='[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='ordinal' />
  <column datatype='integer' hidden='true' name='[DIM_SUGGESTION_DELIVERY_MODE_KEY]' role='dimension' type='ordinal' />
  <column caption='Dim User Dse Key' datatype='integer' hidden='true' name='[DIM_USER_DSE_KEY]' role='dimension' type='ordinal' />
  <column caption='DRIVER_TYPE' datatype='string' name='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column datatype='datetime' hidden='true' name='[DW_CREATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]' role='dimension' type='ordinal' />
  <column caption='Dw Created Ts (Vw Dim Factor Dco)' datatype='datetime' hidden='true' name='[DW_CREATED_TS (VW_DIM_FACTOR_DCO)]' role='dimension' type='ordinal' />
  <column datatype='datetime' hidden='true' name='[DW_CREATED_TS (VW_DIM_REP_TEAM_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='datetime' hidden='true' name='[DW_CREATED_TS (VW_DIM_RPT_FACTOR_DCO_RPT)]' role='dimension' type='ordinal' />
  <column caption='Dw Created Ts (Vw F Daily Suggestion Movement Rpt V2)' datatype='datetime' name='[DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='ordinal' />
  <column datatype='datetime' hidden='true' name='[DW_CREATED_TS]' role='dimension' type='ordinal' />
  <column datatype='boolean' hidden='true' name='[DW_DELETED_FLAG (VW_DIM_ACCOUNT_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='Dw Deleted Flag (Vw Dim Factor Dco)' datatype='boolean' hidden='true' name='[DW_DELETED_FLAG (VW_DIM_FACTOR_DCO)]' role='dimension' type='nominal' />
  <column datatype='boolean' hidden='true' name='[DW_DELETED_FLAG (VW_DIM_RPT_FACTOR_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='Dw Deleted Flag' datatype='boolean' hidden='true' name='[DW_DELETED_FLAG]' role='dimension' type='nominal' />
  <column datatype='datetime' hidden='true' name='[DW_UPDATED_TS (VW_DIM_ACCOUNT_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='datetime' hidden='true' name='[DW_UPDATED_TS (VW_DIM_RPT_FACTOR_DCO_RPT)]' role='dimension' type='ordinal' />
  <column caption='Dw Updated Ts' datatype='datetime' hidden='true' name='[DW_UPDATED_TS]' role='dimension' type='ordinal' />
  <column caption='Date (-10 days)' datatype='boolean' hidden='true' name='[Date (copy)_476748244939202591]' role='measure' type='nominal'>
    <calculation class='tableau' formula='ATTR([DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]) &gt;= DATEADD(&apos;day&apos;, -10,WINDOW_MAX(MAX([DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])))'>
      <table-calc ordering-type='Rows' />
    </calculation>
  </column>
  <column caption='Relative Date' datatype='date' default-format='S' name='[Date (copy)_986006877293236225]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
  </column>
  <column caption='Driver/Type ' datatype='string' name='[Driver/Type (copy)_544865224190218249]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
  </column>
  <column datatype='string' hidden='true' name='[EXTERNAL_ID]' role='dimension' type='nominal' />
  <column caption='Factor' datatype='string' name='[FACTOR_NAME]' role='dimension' type='nominal' />
  <column caption='Factor Type (Vw Dim Factor Dco)' datatype='string' hidden='true' name='[FACTOR_TYPE (VW_DIM_FACTOR_DCO)]' role='dimension' type='nominal' />
  <column caption='Factor Uid (Vw Dim Factor Dco)' datatype='string' hidden='true' name='[FACTOR_UID (VW_DIM_FACTOR_DCO)]' role='dimension' type='nominal' />
  <column caption='FACTOR_UID' datatype='string' name='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column datatype='integer' hidden='true' name='[FACTOR_UNIQ_CNT]' role='measure' type='quantitative' />
  <column caption='FINAL_SCORE' datatype='real' name='[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='measure' type='quantitative' />
  <column caption='Avg FINAL_SCORE' datatype='real' name='[FINAL_SCORE (copy)_540431991555641356]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='avg({FIXED [DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],  [ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)],&#10;[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]:&#10;sum([FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])&#10;})' />
  </column>
  <column caption='Factor ' datatype='string' name='[Factor (copy)_544865224189366278]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[FACTOR_NAME]' />
  </column>
  <column caption='HCP_SEGMENT' datatype='string' name='[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column caption='Is Activated Src' datatype='boolean' hidden='true' name='[IS_ACTIVATED_SRC]' role='dimension' type='nominal' />
  <column datatype='boolean' hidden='true' name='[IS_ACTIVE_SRC]' role='dimension' type='nominal' />
  <column datatype='boolean' hidden='true' name='[IS_COMPETITOR]' role='dimension' type='nominal' />
  <column datatype='boolean' hidden='true' name='[IS_DELETED_SRC (VW_DIM_ACCOUNT_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='Is Deleted Src (Vw Dim User Dse Dco Rpt)' datatype='boolean' hidden='true' name='[IS_DELETED_SRC (VW_DIM_USER_DSE_DCO_RPT)]' role='dimension' type='nominal' />
  <column datatype='boolean' hidden='true' name='[IS_DELETED_SRC]' role='dimension' type='nominal' />
  <column datatype='boolean' hidden='true' name='[IS_PERSON_ACCOUNT]' role='dimension' type='nominal' />
  <column caption='Scenario group' datatype='integer' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT) (copy)_965177717672394752]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='CASE [Parameters].[Parameter 3]&#13;&#10;WHEN &apos;(All)&apos; THEN 1&#13;&#10;WHEN &apos;Published&apos; THEN INT([IS_PUBLISHED (VW_DIM_SCENARIO_RPT)])&#13;&#10;END' />
  </column>
  <column aggregation='Count' datatype='boolean' default-type='nominal' layered='true' name='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]' pivot='key' role='dimension' type='nominal' user-datatype='boolean' visual-totals='Default' />
  <column caption='IS_PUBLISHED' datatype='boolean' name='[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column datatype='boolean' hidden='true' name='[IS_TOTAL_RECORD]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[LAST_DCO_RUN_UID]' role='dimension' type='nominal' />
  <column caption='Latest Run' datatype='boolean' hidden='true' name='[LATEST_RUN]' role='dimension' type='nominal' />
  <column caption='Message Channel Id' datatype='integer' hidden='true' name='[MESSAGE_CHANNEL_ID]' role='dimension' type='ordinal' />
  <column caption='Message Channel' datatype='string' name='[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]' role='dimension' type='nominal' />
  <column caption='Message Id' datatype='integer' hidden='true' name='[MESSAGE_ID]' role='dimension' type='ordinal' />
  <column caption='Message' datatype='string' name='[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]' role='dimension' type='nominal' />
  <column datatype='integer' hidden='true' name='[ORDER_NUM]' role='dimension' type='ordinal' />
  <column datatype='string' hidden='true' name='[PARENT_ACCOUNT_UID]' role='dimension' type='nominal' />
  <column caption='Product Id' datatype='integer' hidden='true' name='[PRODUCT_ID]' role='dimension' type='ordinal' />
  <column caption='Product' datatype='string' name='[PRODUCT_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[PRODUCT_NAME_ENGLISH]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[PRODUCT_TYPE]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='Product Uid (Vw Dim Segment Rpt)' datatype='string' hidden='true' name='[PRODUCT_UID (VW_DIM_SEGMENT_RPT)]' role='dimension' type='nominal' />
  <column caption='PRODUCT_UID' datatype='string' name='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column caption='Product ' datatype='string' name='[Product (copy)_544865224189894663]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='[PRODUCT_NAME]' />
  </column>
  <column caption='RECOMMENDED' datatype='boolean' name='[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column datatype='date' hidden='true' name='[RECORD_END_DATE (VW_DIM_ACCOUNT_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='date' hidden='true' name='[RECORD_END_DATE (VW_DIM_RPT_FACTOR_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='date' hidden='true' name='[RECORD_END_DATE]' role='dimension' type='ordinal' />
  <column datatype='date' hidden='true' name='[RECORD_START_DATE (VW_DIM_ACCOUNT_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='date' hidden='true' name='[RECORD_START_DATE (VW_DIM_RPT_FACTOR_DCO_RPT)]' role='dimension' type='ordinal' />
  <column datatype='date' hidden='true' name='[RECORD_START_DATE]' role='dimension' type='ordinal' />
  <column caption='ACCOUNT_CNT' datatype='integer' default-format='n#,##0;-#,##0' name='[REP_CNT (new version) (copy)_497647790488461313]' role='measure' type='quantitative'>
    <calculation class='tableau' formula='{FIXED [SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)], [PRODUCT_NAME], [CHANNEL_NAME], [Driver/Type (copy)_544865224190218249]: countd([ACCOUNT_NAME])}' />
  </column>
  <column datatype='integer' hidden='true' name='[REP_TEAM_ID]' role='dimension' type='ordinal' />
  <column datatype='string' hidden='true' name='[REP_TEAM_UID (VW_DIM_REP_TEAM_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='REP_TEAM_UID' datatype='string' name='[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[RPT_FACTOR_TYPE]' role='dimension' type='nominal' />
  <column caption='Rpt Factor Uid (Vw Dim Factor Dco)' datatype='string' hidden='true' name='[RPT_FACTOR_UID (VW_DIM_FACTOR_DCO)]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[RPT_FACTOR_UID (VW_DIM_RPT_FACTOR_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='Rpt Factor Uid' datatype='string' hidden='true' name='[RPT_FACTOR_UID]' role='dimension' type='nominal' />
  <column caption='Run Config Factor Id (Vw Dim Factor Dco)' datatype='string' hidden='true' name='[RUN_CONFIG_FACTOR_ID (VW_DIM_FACTOR_DCO)]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[SALES_REFERENCE_ACCOUNT_ID]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[SCENARIO_DESCRIPTION]' role='dimension' type='nominal' />
  <column caption='Scenario' datatype='string' name='[SCENARIO_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' role='dimension' type='nominal' />
  <column caption='SCENARIO_UID' datatype='string' name='[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column caption='Scenario ver' datatype='string' hidden='true' name='[SCENARIO_VER]' role='dimension' type='nominal' />
  <column caption='Segment' datatype='string' name='[SEGMENT_NAME]' role='dimension' type='nominal' />
  <column caption='Segment Type' datatype='string' name='[SEGMENT_TYPE]' role='dimension' type='nominal' />
  <column caption='Se Config Id (Vw Dim Dse Config Rpt)' datatype='integer' hidden='true' name='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]' role='dimension' type='ordinal' />
  <column caption='Se Config Id (Vw Dim User Dse Dco Rpt)' datatype='integer' hidden='true' name='[SE_CONFIG_ID (VW_DIM_USER_DSE_DCO_RPT)]' role='dimension' type='ordinal' />
  <column caption='SE_CONFIG_ID' datatype='integer' name='[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='ordinal' />
  <column caption='Config ' datatype='string' name='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[SOURCE_SYSTEM_NAME (VW_DIM_ACCOUNT_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='Source System Name' datatype='string' hidden='true' name='[SOURCE_SYSTEM_NAME]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[SPECIALITY_1]' role='dimension' type='nominal' />
  <column datatype='string' hidden='true' name='[SPECIALITY_2]' role='dimension' type='nominal' />
  <column aggregation='Count' caption='Suggested Date' datatype='date' datatype-customized='true' name='[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='ordinal' />
  <column caption='Suggestion Reference Id' datatype='string' name='[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column datatype='integer' hidden='true' name='[SUGG_UNIQ_CNT]' role='measure' type='quantitative' />
  <column caption='Suggested Month' datatype='date' datatype-customized='true' name='[Suggested Date (copy)_986006877287538688]' role='dimension' type='ordinal'>
    <calculation class='tableau' formula='DATE(DATETRUNC(&apos;month&apos;,date([SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)])))' />
  </column>
  <column caption='Accepted candidates' datatype='string' default-role='measure' default-type='quantitative' hidden='true' name='[Suggestion Id (copy)_721138898419408896]' role='dimension' type='nominal'>
    <calculation class='tableau' formula='if [RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)] then [SUGGESTION_REFERENCE_ID] end' />
  </column>
  <column caption='Updatedat Src (Vw Dim Factor Dco)' datatype='datetime' hidden='true' name='[UPDATEDAT_SRC (VW_DIM_FACTOR_DCO)]' role='dimension' type='ordinal' />
  <column caption='Updated Dt Key (Vw Dim Factor Dco)' datatype='integer' hidden='true' name='[UPDATED_DT_KEY (VW_DIM_FACTOR_DCO)]' role='dimension' type='ordinal' />
  <column caption='Updated Ts' datatype='datetime' hidden='true' name='[UPDATED_TS]' role='dimension' type='ordinal' />
  <column caption='User Dse Uid (Vw Dim User Dse Dco Rpt)' datatype='string' hidden='true' name='[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]' role='dimension' type='nominal' />
  <column caption='USER_DSE_UID' datatype='string' name='[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' role='dimension' type='nominal' />
  <column caption='User Id' datatype='integer' hidden='true' name='[USER_ID]' role='dimension' type='ordinal' />
  <column caption='Actor' datatype='string' name='[USER_NAME]' role='dimension' type='nominal' />
  <column caption='User Uid' datatype='string' hidden='true' name='[USER_UID]' role='dimension' type='nominal' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_ACCOUNT_DCO_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_CHANNEL_DCO_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_CUSTOMER_DCO_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_DCO_MESSAGE_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_DCO_REASON_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_DSE_CONFIG_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_FACTOR_DCO_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_PRODUCT_DCO_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_REP_TEAM_DCO_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_REP_TEAM_DCO_RPT (DCO.VW_DIM_REP_TEAM_DCO_RPT)_AFEC62CA896C452282B0D101F3C4FBFC]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_FACTOR_DCO' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_RPT_FACTOR_DCO_RPT (DCO.VW_DIM_RPT_FACTOR_DCO_RPT)_C56BD711EEFC483D81C37F1189CAC1B5]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_SCENARIO_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_SEGMENT_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_DIM_USER_DSE_DCO_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT' datatype='table' hidden='true' name='[__tableau_internal_object_id__].[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT)_43DC2BC8E1B246D7892773D79AC11274]' role='measure' type='quantitative' />
  <_.fcp.ObjectModelTableType.true...column caption='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2' datatype='table' name='[__tableau_internal_object_id__].[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]' role='measure' type='quantitative' />
  <column-instance column='[ACCOUNT_CNT (new version) (copy)_497647790488961026]' derivation='Avg' name='[avg:ACCOUNT_CNT (new version) (copy)_497647790488961026:qk]' pivot='key' type='quantitative' />
  <column-instance column='[Calculation_497647790467399680]' derivation='Avg' name='[avg:Calculation_497647790467399680:qk]' pivot='key' type='quantitative' />
  <column-instance column='[REP_CNT (new version) (copy)_497647790488461313]' derivation='Avg' name='[avg:REP_CNT (new version) (copy)_497647790488461313:qk]' pivot='key' type='quantitative' />
  <column-instance column='[DCO_REASON_NAME]' derivation='Min' name='[min:DCO_REASON_NAME:nk]' pivot='key' type='nominal' />
  <column-instance column='[Calculation_1716434419576819713]' derivation='None' name='[none:Calculation_1716434419576819713:nk]' pivot='key' type='nominal' />
  <column-instance column='[DCO_REASON_NAME]' derivation='None' name='[none:DCO_REASON_NAME:nk]' pivot='key' type='nominal' />
  <column-instance column='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' derivation='None' name='[none:DCO_REASON_TYPE_NAME (copy)_660058877027909632:nk]' pivot='key' type='nominal' />
  <column-instance column='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' derivation='Day-Trunc' name='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' pivot='key' type='ordinal' />
  <column-instance column='[CANDIDATE_CNT (new version) (copy)_497647790489550852]' derivation='User' name='[usr:CANDIDATE_CNT (new version) (copy)_497647790489550852:qk]' pivot='key' type='quantitative' />
  <column-instance column='[DAY_CNT (new version) (copy)_497647790489280515]' derivation='User' name='[usr:DAY_CNT (new version) (copy)_497647790489280515:qk]' pivot='key' type='quantitative' />
  <group caption='Action (DCO_REASON_SUMMARY,DAY(Date),Scenario)' hidden='true' name='[Action (DCO_REASON_SUMMARY,DAY(Date),Scenario)]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[DCO_REASON_SUMMARY]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
      <groupfilter function='level-members' level='[SCENARIO_NAME]' />
    </groupfilter>
  </group>
  <group auto-hidden='true' caption='Action (REASON,DAY(Date))' hidden='true' name='[Action (REASON,DAY(Date))]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
    </groupfilter>
  </group>
  <group caption='Action (REASON,DAY(Date),Scenario)' hidden='true' name='[Action (REASON,DAY(Date),Scenario)]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
      <groupfilter function='level-members' level='[SCENARIO_NAME]' />
    </groupfilter>
  </group>
  <group caption='Action (REASON,DAY(Date),Scenario,Scenario ver)' hidden='true' name='[Action (REASON,DAY(Date),Scenario,Scenario ver)]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[DCO_REASON_TYPE_NAME (copy)_660058877027909632]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
      <groupfilter function='level-members' level='[SCENARIO_NAME]' />
      <groupfilter function='level-members' level='[SCENARIO_VER]' />
    </groupfilter>
  </group>
  <group caption='Action (Reasons,DAY(Date)) 1' hidden='true' name='[Action (Reasons,DAY(Date)) 1]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2):ok]' />
    </groupfilter>
  </group>
  <group caption='Action (Reasons,DAY(Date))' hidden='true' name='[Action (Reasons,DAY(Date))]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
    </groupfilter>
  </group>
  <group caption='Action (Reasons,DAY(Date),Scenario) 1' hidden='true' name='[Action (Reasons,DAY(Date),Scenario) 1]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[Calculation_1716434419576819713]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
      <groupfilter function='level-members' level='[SCENARIO_NAME]' />
    </groupfilter>
  </group>
  <group caption='Action (Reasons,DAY(Date),Scenario)' hidden='true' name='[Action (Reasons,DAY(Date),Scenario)]' name-style='unqualified' user:auto-column='sheet_link'>
    <groupfilter function='crossjoin'>
      <groupfilter function='level-members' level='[DCO_REASON_SUMMARY]' />
      <groupfilter function='level-members' level='[tdy:DCO_RUN_DATE:ok]' />
      <groupfilter function='level-members' level='[SCENARIO_NAME]' />
    </groupfilter>
  </group>
  <drill-paths>
    <drill-path name='Suggestion Candidates Hierarchy'>
      <field>[USER_NAME]</field>
      <field>[ACCOUNT_NAME]</field>
      <field>[PRODUCT_NAME]</field>
      <field>[CHANNEL_NAME]</field>
      <field>[FACTOR_NAME]</field>
      <field>[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]</field>
      <field>[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]</field>
      <field>[DCO_REASON_TYPE_NAME]</field>
      <field>[Calculation_1716434419576819713]</field>
      <field>[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</field>
      <field>[Suggested Date (copy)_986006877287538688]</field>
      <field>[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</field>
    </drill-path>
  </drill-paths>
  <extract _.fcp.ObjectModelExtractV2.true...object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' count='-1' enabled='false' units='records'>
    <connection access_mode='readonly' authentication='auth-none' author-locale='en_US' class='hyper' dbname='Data/Extracts/${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model.hyper' default-settings='yes' schema='Extract' sslmode='' tablename='Extract' update-time='03/16/2023 05:54:47 PM' username=''>
      <_.fcp.ObjectModelEncapsulateLegacy.false...relation name='VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2' table='[Extract].[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]' type='table' />
      <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
        <relation name='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' table='[Extract].[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]' type='table' />
        <relation name='VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2' table='[Extract].[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]' type='table' />
        <relation name='VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77' table='[Extract].[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]' type='table' />
        <relation name='VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D' table='[Extract].[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]' type='table' />
        <relation name='VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8' table='[Extract].[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]' type='table' />
        <relation name='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504' table='[Extract].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]' type='table' />
        <relation name='VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834' table='[Extract].[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]' type='table' />
        <relation name='VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275' table='[Extract].[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]' type='table' />
        <relation name='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A' table='[Extract].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]' type='table' />
        <relation name='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D' table='[Extract].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]' type='table' />
        <relation name='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D' table='[Extract].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]' type='table' />
        <relation name='VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC' table='[Extract].[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]' type='table' />
      </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
      <cols>
        <map key='[ACCOUNT_NAME]' value='[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2].[ACCOUNT_NAME]' />
        <map key='[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]' value='[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2].[ACCOUNT_UID]' />
        <map key='[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[ACCOUNT_UID]' />
        <map key='[CHANNEL_NAME]' value='[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77].[CHANNEL_NAME]' />
        <map key='[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]' value='[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77].[CHANNEL_UID]' />
        <map key='[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[CHANNEL_UID]' />
        <map key='[COUNTRY_NAME]' value='[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D].[COUNTRY_NAME]' />
        <map key='[CREATEDAT_SRC]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[CREATEDAT_SRC]' />
        <map key='[CREATED_DT_KEY]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[CREATED_DT_KEY]' />
        <map key='[CUSTOMER_ID]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[CUSTOMER_ID]' />
        <map key='[DCO_REASON_CODE]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504].[DCO_REASON_CODE]' />
        <map key='[DCO_REASON_DESCRIPTION]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504].[DCO_REASON_DESCRIPTION]' />
        <map key='[DCO_REASON_NAME]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504].[DCO_REASON_NAME]' />
        <map key='[DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[DCO_REASON_SUMMARY]' />
        <map key='[DCO_REASON_TYPE_NAME]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504].[DCO_REASON_TYPE_NAME]' />
        <map key='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' value='[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504].[DCO_REASON_UID]' />
        <map key='[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[DCO_REASON_UID]' />
        <map key='[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[DCO_RUN_DATE]' />
        <map key='[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[DCO_RUN_UID]' />
        <map key='[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]' value='[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D].[DIM_CUSTOMER_KEY]' />
        <map key='[DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO_RPT)]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[DIM_CUSTOMER_KEY]' />
        <map key='[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[DIM_CUSTOMER_KEY]' />
        <map key='[DIM_FACTOR_KEY]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[DIM_FACTOR_KEY]' />
        <map key='[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]' value='[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8].[DIM_MESSAGE_KEY]' />
        <map key='[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[DIM_MESSAGE_KEY]' />
        <map key='[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D].[DIM_SEGMENT_KEY]' />
        <map key='[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[DIM_SEGMENT_KEY]' />
        <map key='[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[DRIVER_TYPE]' />
        <map key='[DW_CREATED_TS (VW_DIM_FACTOR_DCO_RPT)]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[DW_CREATED_TS]' />
        <map key='[DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[DW_CREATED_TS]' />
        <map key='[DW_DELETED_FLAG (VW_DIM_FACTOR_DCO_RPT)]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[DW_DELETED_FLAG]' />
        <map key='[FACTOR_NAME]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[FACTOR_NAME]' />
        <map key='[FACTOR_TYPE]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[FACTOR_TYPE]' />
        <map key='[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[FACTOR_UID]' />
        <map key='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[FACTOR_UID]' />
        <map key='[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[FINAL_SCORE]' />
        <map key='[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[HCP_SEGMENT]' />
        <map key='[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]' value='[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D].[IS_PUBLISHED]' />
        <map key='[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[IS_PUBLISHED]' />
        <map key='[LAST_DCO_RUN_DATE]' value='[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D].[LAST_DCO_RUN_DATE]' />
        <map key='[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]' value='[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8].[MESSAGE_CHANNEL_NAME]' />
        <map key='[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]' value='[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8].[MESSAGE_NAME]' />
        <map key='[PRODUCT_NAME]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A].[PRODUCT_NAME]' />
        <map key='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' value='[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A].[PRODUCT_UID]' />
        <map key='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[PRODUCT_UID]' />
        <map key='[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[RECOMMENDED]' />
        <map key='[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[REP_TEAM_UID]' />
        <map key='[RUN_CONFIG_FACTOR_ID]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[RUN_CONFIG_FACTOR_ID]' />
        <map key='[SCENARIO_NAME]' value='[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D].[SCENARIO_NAME]' />
        <map key='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' value='[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D].[SCENARIO_UID]' />
        <map key='[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[SCENARIO_UID]' />
        <map key='[SEGMENT_NAME]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D].[SEGMENT_NAME]' />
        <map key='[SEGMENT_TYPE]' value='[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D].[SEGMENT_TYPE]' />
        <map key='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]' value='[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834].[SE_CONFIG_ID]' />
        <map key='[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[SE_CONFIG_ID]' />
        <map key='[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]' value='[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834].[SE_CONFIG_NAME]' />
        <map key='[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[SUGGESTED_DATE]' />
        <map key='[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[SUGGESTION_REFERENCE_ID]' />
        <map key='[UPDATEDAT_SRC]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[UPDATEDAT_SRC]' />
        <map key='[UPDATED_DT_KEY]' value='[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275].[UPDATED_DT_KEY]' />
        <map key='[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]' value='[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC].[USER_DSE_UID]' />
        <map key='[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' value='[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF].[USER_DSE_UID]' />
        <map key='[USER_NAME]' value='[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC].[USER_NAME]' />
      </cols>
      <refresh>
        <refresh-event add-from-file-path='${CUSTOMER_NAME} ${ENVIRONMENT} CIE Output Tracing Data Model' increment-value='%null%' refresh-type='create' rows-inserted='17861' timestamp-start='2023-03-16 17:54:31.695' />
      </refresh>
      <metadata-records>
        <metadata-record class='column'>
          <remote-name>DCO_RUN_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_RUN_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>DCO_RUN_UID</remote-alias>
          <ordinal>0</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SCENARIO_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>SCENARIO_UID</remote-alias>
          <ordinal>1</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_RUN_DATE</remote-name>
          <remote-type>133</remote-type>
          <local-name>[DCO_RUN_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>DCO_RUN_DATE</remote-alias>
          <ordinal>2</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>date</local-type>
          <aggregation>Year</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>ACCOUNT_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>ACCOUNT_UID</remote-alias>
          <ordinal>3</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1883</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>USER_DSE_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>USER_DSE_UID</remote-alias>
          <ordinal>4</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>9</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>REP_TEAM_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[REP_TEAM_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>REP_TEAM_UID</remote-alias>
          <ordinal>5</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>PRODUCT_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>PRODUCT_UID</remote-alias>
          <ordinal>6</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CHANNEL_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>CHANNEL_UID</remote-alias>
          <ordinal>7</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>FACTOR_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>FACTOR_UID</remote-alias>
          <ordinal>8</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>DCO_REASON_UID</remote-alias>
          <ordinal>9</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>RECOMMENDED</remote-name>
          <remote-type>11</remote-type>
          <local-name>[RECOMMENDED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>RECOMMENDED</remote-alias>
          <ordinal>10</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_SUMMARY</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_SUMMARY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>DCO_REASON_SUMMARY</remote-alias>
          <ordinal>11</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SUGGESTION_REFERENCE_ID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SUGGESTION_REFERENCE_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>SUGGESTION_REFERENCE_ID</remote-alias>
          <ordinal>12</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>3663</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DRIVER_TYPE</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DRIVER_TYPE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>DRIVER_TYPE</remote-alias>
          <ordinal>13</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>IS_PUBLISHED</remote-name>
          <remote-type>11</remote-type>
          <local-name>[IS_PUBLISHED (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>IS_PUBLISHED</remote-alias>
          <ordinal>14</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_CUSTOMER_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
          <ordinal>15</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DW_CREATED_TS</remote-name>
          <remote-type>135</remote-type>
          <local-name>[DW_CREATED_TS (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>DW_CREATED_TS</remote-alias>
          <ordinal>16</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>datetime</local-type>
          <aggregation>Year</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>FINAL_SCORE</remote-name>
          <remote-type>5</remote-type>
          <local-name>[FINAL_SCORE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>FINAL_SCORE</remote-alias>
          <ordinal>17</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>real</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>38</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SUGGESTED_DATE</remote-name>
          <remote-type>133</remote-type>
          <local-name>[SUGGESTED_DATE (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>SUGGESTED_DATE</remote-alias>
          <ordinal>18</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>date</local-type>
          <aggregation>Year</aggregation>
          <approx-count>6</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_SEGMENT_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>DIM_SEGMENT_KEY</remote-alias>
          <ordinal>19</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>HCP_SEGMENT</remote-name>
          <remote-type>129</remote-type>
          <local-name>[HCP_SEGMENT (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>HCP_SEGMENT</remote-alias>
          <ordinal>20</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>4</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SE_CONFIG_ID</remote-name>
          <remote-type>20</remote-type>
          <local-name>[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>SE_CONFIG_ID</remote-alias>
          <ordinal>21</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_MESSAGE_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]</local-name>
          <parent-name>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</parent-name>
          <remote-alias>DIM_MESSAGE_KEY</remote-alias>
          <ordinal>22</ordinal>
          <family>VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>ACCOUNT_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]</local-name>
          <parent-name>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</parent-name>
          <remote-alias>ACCOUNT_UID</remote-alias>
          <ordinal>23</ordinal>
          <family>VW_DIM_ACCOUNT_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>ACCOUNT_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[ACCOUNT_NAME]</local-name>
          <parent-name>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</parent-name>
          <remote-alias>ACCOUNT_NAME</remote-alias>
          <ordinal>24</ordinal>
          <family>VW_DIM_ACCOUNT_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CHANNEL_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]</local-name>
          <parent-name>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</parent-name>
          <remote-alias>CHANNEL_UID</remote-alias>
          <ordinal>25</ordinal>
          <family>VW_DIM_CHANNEL_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CHANNEL_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[CHANNEL_NAME]</local-name>
          <parent-name>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</parent-name>
          <remote-alias>CHANNEL_NAME</remote-alias>
          <ordinal>26</ordinal>
          <family>VW_DIM_CHANNEL_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_CUSTOMER_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]</local-name>
          <parent-name>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</parent-name>
          <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
          <ordinal>27</ordinal>
          <family>VW_DIM_CUSTOMER_DCO_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>COUNTRY_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[COUNTRY_NAME]</local-name>
          <parent-name>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</parent-name>
          <remote-alias>COUNTRY_NAME</remote-alias>
          <ordinal>28</ordinal>
          <family>VW_DIM_CUSTOMER_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_MESSAGE_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
          <parent-name>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</parent-name>
          <remote-alias>DIM_MESSAGE_KEY</remote-alias>
          <ordinal>29</ordinal>
          <family>VW_DIM_DCO_MESSAGE_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>MESSAGE_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[MESSAGE_NAME (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
          <parent-name>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</parent-name>
          <remote-alias>MESSAGE_NAME</remote-alias>
          <ordinal>30</ordinal>
          <family>VW_DIM_DCO_MESSAGE_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>MESSAGE_CHANNEL_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[MESSAGE_CHANNEL_NAME (VW_DIM_DCO_MESSAGE_RPT)]</local-name>
          <parent-name>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</parent-name>
          <remote-alias>MESSAGE_CHANNEL_NAME</remote-alias>
          <ordinal>31</ordinal>
          <family>VW_DIM_DCO_MESSAGE_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</parent-name>
          <remote-alias>DCO_REASON_UID</remote-alias>
          <ordinal>32</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>7</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_CODE</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_CODE]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</parent-name>
          <remote-alias>DCO_REASON_CODE</remote-alias>
          <ordinal>33</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>7</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_NAME]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</parent-name>
          <remote-alias>DCO_REASON_NAME</remote-alias>
          <ordinal>34</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>7</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_DESCRIPTION</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_DESCRIPTION]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</parent-name>
          <remote-alias>DCO_REASON_DESCRIPTION</remote-alias>
          <ordinal>35</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>7</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DCO_REASON_TYPE_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[DCO_REASON_TYPE_NAME]</local-name>
          <parent-name>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</parent-name>
          <remote-alias>DCO_REASON_TYPE_NAME</remote-alias>
          <ordinal>36</ordinal>
          <family>VW_DIM_DCO_REASON_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SE_CONFIG_ID</remote-name>
          <remote-type>20</remote-type>
          <local-name>[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]</local-name>
          <parent-name>[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]</parent-name>
          <remote-alias>SE_CONFIG_ID</remote-alias>
          <ordinal>37</ordinal>
          <family>VW_DIM_DSE_CONFIG_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SE_CONFIG_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SE_CONFIG_NAME (VW_DIM_DSE_CONFIG_RPT)]</local-name>
          <parent-name>[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]</parent-name>
          <remote-alias>SE_CONFIG_NAME</remote-alias>
          <ordinal>38</ordinal>
          <family>VW_DIM_DSE_CONFIG_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_FACTOR_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_FACTOR_KEY]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>DIM_FACTOR_KEY</remote-alias>
          <ordinal>39</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DW_CREATED_TS</remote-name>
          <remote-type>135</remote-type>
          <local-name>[DW_CREATED_TS (VW_DIM_FACTOR_DCO_RPT)]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>DW_CREATED_TS</remote-alias>
          <ordinal>40</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>datetime</local-type>
          <aggregation>Year</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>FACTOR_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[FACTOR_NAME]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>FACTOR_NAME</remote-alias>
          <ordinal>41</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>FACTOR_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>FACTOR_UID</remote-alias>
          <ordinal>42</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>FACTOR_TYPE</remote-name>
          <remote-type>129</remote-type>
          <local-name>[FACTOR_TYPE]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>FACTOR_TYPE</remote-alias>
          <ordinal>43</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DW_DELETED_FLAG</remote-name>
          <remote-type>11</remote-type>
          <local-name>[DW_DELETED_FLAG (VW_DIM_FACTOR_DCO_RPT)]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>DW_DELETED_FLAG</remote-alias>
          <ordinal>44</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_CUSTOMER_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_CUSTOMER_KEY (VW_DIM_FACTOR_DCO_RPT)]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>DIM_CUSTOMER_KEY</remote-alias>
          <ordinal>45</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CREATED_DT_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[CREATED_DT_KEY]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>CREATED_DT_KEY</remote-alias>
          <ordinal>46</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>UPDATED_DT_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[UPDATED_DT_KEY]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>UPDATED_DT_KEY</remote-alias>
          <ordinal>47</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CUSTOMER_ID</remote-name>
          <remote-type>20</remote-type>
          <local-name>[CUSTOMER_ID]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>CUSTOMER_ID</remote-alias>
          <ordinal>48</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>RUN_CONFIG_FACTOR_ID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[RUN_CONFIG_FACTOR_ID]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>RUN_CONFIG_FACTOR_ID</remote-alias>
          <ordinal>49</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>CREATEDAT_SRC</remote-name>
          <remote-type>135</remote-type>
          <local-name>[CREATEDAT_SRC]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>CREATEDAT_SRC</remote-alias>
          <ordinal>50</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>datetime</local-type>
          <aggregation>Year</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>UPDATEDAT_SRC</remote-name>
          <remote-type>135</remote-type>
          <local-name>[UPDATEDAT_SRC]</local-name>
          <parent-name>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</parent-name>
          <remote-alias>UPDATEDAT_SRC</remote-alias>
          <ordinal>51</ordinal>
          <family>VW_DIM_FACTOR_DCO_RPT</family>
          <local-type>datetime</local-type>
          <aggregation>Year</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>PRODUCT_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</parent-name>
          <remote-alias>PRODUCT_UID</remote-alias>
          <ordinal>52</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>PRODUCT_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[PRODUCT_NAME]</local-name>
          <parent-name>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</parent-name>
          <remote-alias>PRODUCT_NAME</remote-alias>
          <ordinal>53</ordinal>
          <family>VW_DIM_PRODUCT_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SCENARIO_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]</local-name>
          <parent-name>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</parent-name>
          <remote-alias>SCENARIO_UID</remote-alias>
          <ordinal>54</ordinal>
          <family>VW_DIM_SCENARIO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1195</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SCENARIO_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SCENARIO_NAME]</local-name>
          <parent-name>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</parent-name>
          <remote-alias>SCENARIO_NAME</remote-alias>
          <ordinal>55</ordinal>
          <family>VW_DIM_SCENARIO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>168</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>IS_PUBLISHED</remote-name>
          <remote-type>11</remote-type>
          <local-name>[IS_PUBLISHED (VW_DIM_SCENARIO_RPT)]</local-name>
          <parent-name>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</parent-name>
          <remote-alias>IS_PUBLISHED</remote-alias>
          <ordinal>56</ordinal>
          <family>VW_DIM_SCENARIO_RPT</family>
          <local-type>boolean</local-type>
          <aggregation>Count</aggregation>
          <approx-count>2</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>LAST_DCO_RUN_DATE</remote-name>
          <remote-type>133</remote-type>
          <local-name>[LAST_DCO_RUN_DATE]</local-name>
          <parent-name>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</parent-name>
          <remote-alias>LAST_DCO_RUN_DATE</remote-alias>
          <ordinal>57</ordinal>
          <family>VW_DIM_SCENARIO_RPT</family>
          <local-type>date</local-type>
          <aggregation>Year</aggregation>
          <approx-count>8</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>DIM_SEGMENT_KEY</remote-name>
          <remote-type>20</remote-type>
          <local-name>[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</parent-name>
          <remote-alias>DIM_SEGMENT_KEY</remote-alias>
          <ordinal>58</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>integer</local-type>
          <aggregation>Sum</aggregation>
          <approx-count>10</approx-count>
          <contains-null>true</contains-null>
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SEGMENT_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SEGMENT_NAME]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</parent-name>
          <remote-alias>SEGMENT_NAME</remote-alias>
          <ordinal>59</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>10</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>SEGMENT_TYPE</remote-name>
          <remote-type>129</remote-type>
          <local-name>[SEGMENT_TYPE]</local-name>
          <parent-name>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</parent-name>
          <remote-alias>SEGMENT_TYPE</remote-alias>
          <ordinal>60</ordinal>
          <family>VW_DIM_SEGMENT_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>1</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>USER_NAME</remote-name>
          <remote-type>129</remote-type>
          <local-name>[USER_NAME]</local-name>
          <parent-name>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</parent-name>
          <remote-alias>USER_NAME</remote-alias>
          <ordinal>61</ordinal>
          <family>VW_DIM_USER_DSE_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
        <metadata-record class='column'>
          <remote-name>USER_DSE_UID</remote-name>
          <remote-type>129</remote-type>
          <local-name>[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]</local-name>
          <parent-name>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</parent-name>
          <remote-alias>USER_DSE_UID</remote-alias>
          <ordinal>62</ordinal>
          <family>VW_DIM_USER_DSE_DCO_RPT</family>
          <local-type>string</local-type>
          <aggregation>Count</aggregation>
          <approx-count>0</approx-count>
          <contains-null>true</contains-null>
          <collation flag='0' name='binary' />
          <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
        </metadata-record>
      </metadata-records>
    </connection>
  </extract>
  <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' _.fcp.SchemaViewerObjectModel.true...common-percentage='0.875375' _.fcp.SchemaViewerObjectModel.true...user-set-layout-v2='true' dim-ordering='alphabetic' measure-ordering='alphabetic' parameter-percentage='0.124625' show-aliased-fields='true' show-hidden-fields='true' show-structure='true' />
  <style>
    <style-rule element='mark'>
      <encoding attr='color' field='[none:DCO_REASON_TYPE_NAME (copy)_660058877027909632:nk]' type='palette'>
        <map to='#4e79a7'>
          <bucket>&quot;Accept 6. Required-cap accepted&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;Reject Unknown&quot;</bucket>
        </map>
        <map to='#59a14f'>
          <bucket>&quot;Accept 7. Additional-cap accepted&quot;</bucket>
        </map>
        <map to='#76b7b2'>
          <bucket>&quot;Reject NA&quot;</bucket>
        </map>
        <map to='#9c755f'>
          <bucket>&quot;Reject Insufficient User capacity&quot;</bucket>
        </map>
        <map to='#b07aa1'>
          <bucket>&quot;Recommend Recommended in second-pass&quot;</bucket>
        </map>
        <map to='#bab0ac'>
          <bucket>&quot;Reject Better candidate selected&quot;</bucket>
        </map>
        <map to='#e15759'>
          <bucket>&quot;Reject 4. No Required-cap&quot;</bucket>
        </map>
        <map to='#edc948'>
          <bucket>&quot;Recommend Recommended in first-pass&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;Reject 3. No RepCap&quot;</bucket>
        </map>
        <map to='#ff9da7'>
          <bucket>&quot;Reject Auto-snoozed&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[min:DCO_REASON_NAME:nk]' type='palette'>
        <map to='#4e79a7'>
          <bucket>&quot;3. No RepCap&quot;</bucket>
        </map>
        <map to='#59a14f'>
          <bucket>%null%</bucket>
        </map>
        <map to='#76b7b2'>
          <bucket>&quot;NA&quot;</bucket>
        </map>
        <map to='#e15759'>
          <bucket>&quot;6. Required-cap accepted&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;4. No Required-cap&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:DCO_REASON_NAME:nk]' type='palette'>
        <map to='#4e79a7'>
          <bucket>&quot;3. No RepCap&quot;</bucket>
        </map>
        <map to='#59a14f'>
          <bucket>%null%</bucket>
        </map>
        <map to='#76b7b2'>
          <bucket>&quot;NA&quot;</bucket>
        </map>
        <map to='#e15759'>
          <bucket>&quot;6. Required-cap accepted&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;4. No Required-cap&quot;</bucket>
        </map>
      </encoding>
      <encoding attr='color' field='[none:Calculation_1716434419576819713:nk]' type='palette'>
        <map to='#4e79a7'>
          <bucket>&quot;00. This action is high enough priority to justify using the available capacity for the channel and engagement bandwith for the account&quot;</bucket>
        </map>
        <map to='#4e79a7'>
          <bucket>&quot;02. Account-level conflict&quot;</bucket>
        </map>
        <map to='#59a14f'>
          <bucket>&quot;00. Recommended&quot;</bucket>
        </map>
        <map to='#76b7b2'>
          <bucket>&quot;03. Factor-level conflict&quot;</bucket>
        </map>
        <map to='#bab0ac'>
          <bucket>&quot;06. Unknown reason&quot;</bucket>
        </map>
        <map to='#e15759'>
          <bucket>&quot;01. Auto-expired&quot;</bucket>
        </map>
        <map to='#edc948'>
          <bucket>&quot;05. Channel capacity&quot;</bucket>
        </map>
        <map to='#f28e2b'>
          <bucket>&quot;04. End user capacity&quot;</bucket>
        </map>
      </encoding>
    </style-rule>
  </style>
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;Belarus&quot;' />
  </semantic-values>
  <date-options start-of-week='monday' />
  <datasource-dependencies datasource='Parameters'>
    <column caption='Scenario Group' datatype='string' name='[Parameter 3]' param-domain-type='list' role='measure' type='nominal' value='&quot;Published&quot;'>
      <calculation class='tableau' formula='&quot;Published&quot;' />
      <members>
        <member value='&quot;(All)&quot;' />
        <member value='&quot;Published&quot;' />
      </members>
    </column>
  </datasource-dependencies>
  <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    <objects>
      <object caption='VW_DIM_ACCOUNT_DCO_RPT' id='VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_ACCOUNT_DCO_RPT' table='[DCO].[VW_DIM_ACCOUNT_DCO_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2' table='[Extract].[VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_CHANNEL_DCO_RPT' id='VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_CHANNEL_DCO_RPT' table='[DCO].[VW_DIM_CHANNEL_DCO_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77' table='[Extract].[VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_CUSTOMER_DCO_RPT' id='VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_CUSTOMER_DCO_RPT' table='[DCO].[VW_DIM_CUSTOMER_DCO_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D' table='[Extract].[VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DCO_MESSAGE_RPT' id='VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_DCO_MESSAGE_RPT' table='[DCO].[VW_DIM_DCO_MESSAGE_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8' table='[Extract].[VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DCO_REASON_RPT' id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_DCO_REASON_RPT' table='[DCO].[VW_DIM_DCO_REASON_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504' table='[Extract].[VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_DSE_CONFIG_RPT' id='VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_DSE_CONFIG_RPT' table='[DCO].[VW_DIM_DSE_CONFIG_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834' table='[Extract].[VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_FACTOR_DCO_RPT' id='VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_FACTOR_DCO_RPT' table='[DCO].[VW_DIM_FACTOR_DCO_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275' table='[Extract].[VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_PRODUCT_DCO_RPT' id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_PRODUCT_DCO_RPT' table='[DCO].[VW_DIM_PRODUCT_DCO_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A' table='[Extract].[VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SCENARIO_RPT' id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_SCENARIO_RPT' table='[DCO].[VW_DIM_SCENARIO_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D' table='[Extract].[VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_SEGMENT_RPT' id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_SEGMENT_RPT' table='[DCO].[VW_DIM_SEGMENT_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D' table='[Extract].[VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D]' type='table' />
        </properties>
      </object>
      <object caption='VW_DIM_USER_DSE_DCO_RPT' id='VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_DIM_USER_DSE_DCO_RPT' table='[DCO].[VW_DIM_USER_DSE_DCO_RPT]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC' table='[Extract].[VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC]' type='table' />
        </properties>
      </object>
      <object caption='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2' id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF'>
        <properties context=''>
          <relation connection='${CUSTOMER_NAME}${ENVIRONMENT}.snowflake.0kemf7h1l7fxgm113lnw51rm75jz' name='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2' table='[DCO].[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2]' type='table' />
        </properties>
        <properties context='extract'>
          <relation name='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' table='[Extract].[VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF]' type='table' />
        </properties>
      </object>
    </objects>
    <relationships>
      <relationship>
        <expression op='='>
          <expression op='[ACCOUNT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[ACCOUNT_UID (VW_DIM_ACCOUNT_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_ACCOUNT_DCO_RPT (DCO.VW_DIM_ACCOUNT_DCO_RPT)_DE912F07FB3647C2A6A4FB4503AD76F2' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[CHANNEL_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[CHANNEL_UID (VW_DIM_CHANNEL_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_CHANNEL_DCO_RPT (DCO.VW_DIM_CHANNEL_DCO_RPT)_18CC9E9C9CF048DF884DAA4E9E4EDD77' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DIM_CUSTOMER_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[DIM_CUSTOMER_KEY (VW_DIM_CUSTOMER_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_CUSTOMER_DCO_RPT (DCO.VW_DIM_CUSTOMER_DCO_RPT)_63267D1535474D73AB0AD6C3702D243D' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DIM_MESSAGE_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[DIM_MESSAGE_KEY (VW_DIM_DCO_MESSAGE_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_DCO_MESSAGE_RPT (DCO.VW_DIM_DCO_MESSAGE_RPT)_B15F9B26C03D4190881875305F5743E8' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DCO_REASON_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[DCO_REASON_UID (VW_DIM_DCO_REASON_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_DCO_REASON_RPT (DCO.VW_DIM_DCO_REASON_RPT)_0A3D412657D245F38CA9ADEFF57B1504' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SE_CONFIG_ID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[SE_CONFIG_ID (VW_DIM_DSE_CONFIG_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_DSE_CONFIG_RPT (DCO.VW_DIM_DSE_CONFIG_RPT)_170DDF294A8C48C19ACCE9D586079834' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[FACTOR_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[FACTOR_UID (VW_DIM_FACTOR_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_FACTOR_DCO (DCO.VW_DIM_FACTOR_DCO)_359EE5F0C1CE4D46B3F88348D670D275' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[PRODUCT_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[PRODUCT_UID (VW_DIM_PRODUCT_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_PRODUCT_DCO_RPT (DCO.VW_DIM_PRODUCT_DCO_RPT)_6BF3508D0951410897F8568B161D121A' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[SCENARIO_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[SCENARIO_UID (VW_DIM_SCENARIO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_SCENARIO_RPT (DCO.VW_DIM_SCENARIO_RPT)_8BD63E062423474D8F08FF938A84FC3D' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[DIM_SEGMENT_KEY (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[DIM_SEGMENT_KEY (VW_DIM_SEGMENT_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_SEGMENT_RPT (DCO.VW_DIM_SEGMENT_RPT)_75C320C3D1474DA189C08943C34D4D9D' />
      </relationship>
      <relationship>
        <expression op='='>
          <expression op='[USER_DSE_UID (VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)]' />
          <expression op='[USER_DSE_UID (VW_DIM_USER_DSE_DCO_RPT)]' />
        </expression>
        <first-end-point object-id='VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2 (DCO.VW_F_DAILY_SUGGESTION_MOVEMENT_RPT_V2)_FDD16B0393274EB4BB65D95063271FBF' />
        <second-end-point object-id='VW_DIM_USER_DSE_DCO_RPT (DCO.VW_DIM_USER_DSE_DCO_RPT)_26B353952271411C80D04CC6E5F31ACC' />
      </relationship>
    </relationships>
  </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
</datasource>
