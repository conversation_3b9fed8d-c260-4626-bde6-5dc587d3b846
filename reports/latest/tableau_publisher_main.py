import argparse
import os
from posixpath import dirname
import sys

import mysql.connector
from tableauserverclient import models, ConnectionItem, ConnectionCredentials
import tableau_utils
import string

import shutil
import pathlib

app_name = "TABLEAU"
expected_args = [
    {"name": "--customer_name", "default": "novartisfrqa"},
    {"name": "--environment", "default": "prod"},
    {"name": "--site_type", "default": "internal"},
    {"name": "--hide_tableau_project", "default": "No"},
    {"name": "--app_name", "default": app_name},
    {"name": "--ecosystem", "default": "prod"}]

logger = tableau_utils.get_logger(os.path.basename(__file__))
app_name = os.path.basename(sys.argv[0].split('.')[0])
output_dir = os.path.dirname(os.path.realpath(__file__)) + '/output/'
templates_dir = os.path.dirname(os.path.realpath(__file__)) + '/templates/'

sub_project_name = "DCO"

def create_project(name):
    try:
        project_item = TSC.ProjectItem(name=name)
        project_item = server.projects.create(project_item)
        return project_item.id
    except Exception as e:
        logger.exception(e)

def create_sub_project(name, parent_project_id):
    try:
        project_item = TSC.ProjectItem(name=name, parent_id=parent_project_id)
        project_item = server.projects.create(project_item)
        return project_item.id
    except Exception as e:
        logger.exception(e)

def get_project_id(name):
    try:
        req_option = TSC.RequestOptions()
        req_option.filter.add(TSC.Filter(TSC.RequestOptions.Field.Name,TSC.RequestOptions.Operator.Equals, name))
        all_project_items, pagination_item = server.projects.get(req_option)
        return all_project_items[0].id
    except Exception as e:
        logger.exception(e)

def get_sub_project_id(parent_project_id):
    sub_project_id = None
    try:
        req_option = TSC.RequestOptions(pagesize=100)
        req_option.filter.add(TSC.Filter(TSC.RequestOptions.Field.Name,TSC.RequestOptions.Operator.Equals, sub_project_name))
        all_project_items, pagination_item = server.projects.get(req_option)

        for project in TSC.Pager(server.projects, req_option):
            if project.parent_id == parent_project_id:
                sub_project_id = project.id

        return sub_project_id

    except Exception as e:
        logger.exception(e)

def update_datasource_connection(datasources, snowflake_tableau_info):
    (mysql_learningDB, mysql_portforward_rds, mysql_portforward_port, mysql_rds_server, mysql_rds_user, mysql_rds_password,
        account, endpoint, region, warehouse, db, role, snow_username, snow_password, dbregion, dbschema,
        site, url, tableau_username, tableau_password, token_name, token_secret, project, mysql_server, mysql_port,
        mysql_username, mysql_password) = snowflake_tableau_info
    try:
        for datasource in datasources:
            server.datasources.populate_connections(datasource)
            for connection in datasource.connections:
                con = connection
                if con.connection_type == 'mysql':
                    con.username = mysql_username if mysql_portforward_rds in (None, '') else mysql_rds_user
                    con.password = tableau_utils.decrypt_password(mysql_password) if mysql_portforward_rds in (None, '') else mysql_rds_password
                    con.embed_password = True
                    con.server_address = mysql_server if mysql_portforward_rds in (None, '') else mysql_portforward_rds
                    con.server_port = mysql_port if mysql_portforward_rds in (None, '') else mysql_portforward_port
                else:
                    con.username = snow_username
                    con.password = snow_password
                    con.embed_password = True
                    con.server_address = f"{account}.{region}.{endpoint}"
                    con.server_port = ''
                server.datasources.update_connection(datasource, con)

    except Exception as e:
        logger.exception(e)
        raise

def get_datasource_connection(datasources, snowflake_tableau_info):
    (mysql_learningDB, mysql_portforward_rds, mysql_portforward_port, mysql_rds_server, mysql_rds_user, mysql_rds_password,
        account, endpoint, region, warehouse, db, role, snow_username, snow_password, dbregion, dbschema,
        site, url, tableau_username, tableau_password, token_name, token_secret, project, mysql_server, mysql_port,
        mysql_username, mysql_password) = snowflake_tableau_info
    try:
        for datasource in datasources:
            server.datasources.populate_connections(datasource)
            if len(datasource.connections) == 2:
                for connection in datasource.connections:
                    print(connection.connection_type)
                    print(connection.username)
                    # print(connection.password)


    except Exception as e:
        logger.exception(e)
        raise

def publish_datasource(snowflake_tableau_info, project_id, file_path):
    (mysql_learningDB, mysql_portforward_rds, mysql_portforward_port, mysql_rds_server, mysql_rds_user, mysql_rds_password,
        account, endpoint, region, warehouse, db, role, snow_username, snow_password, dbregion, dbschema,
        site, url, tableau_username, tableau_password, token_name, token_secret, project, mysql_server, mysql_port,
        mysql_username, mysql_password) = snowflake_tableau_info
    matches = ["DETAILS", "SUMMARY"]
    if any(x in file_path for x in matches):
        try:
            new_datasource = TSC.DatasourceItem(project_id)
            # connection_credentials= ConnectionCredentials(mysql_username, tableau_utils.decrypt_password(mysql_password), embed=True, oauth=False)
            new_datasource = server.datasources.publish(new_datasource, file_path, 'Overwrite', connection_credentials=None, as_job=True)
            logger.info(f"published datasource {new_datasource}")
            return new_datasource

        except Exception as e:
            logger.exception(e)
            raise
    else:
        try:
            new_datasource = TSC.DatasourceItem(project_id)
            connection_credentials= ConnectionCredentials(snow_username, snow_password, embed=True, oauth=False)
            new_datasource = server.datasources.publish(new_datasource, file_path, 'Overwrite', connection_credentials=connection_credentials)
            logger.info(f"published datasource {new_datasource}")
            return new_datasource

        except Exception as e:
            logger.exception(e)
            raise

def create_extract(datasource):
    try:
        job = server.datasources.create_extract(datasource)
        logger.info(f"there is a Tableau job creating extract for {datasource.name} Job ID: {job.id}")
        return job
    except Exception as e:
        logger.exception(e)
        raise

def publish_workbook(snowflake_tableau_info, project_id, file_path):
    (mysql_learningDB, mysql_portforward_rds, mysql_portforward_port, mysql_rds_server, mysql_rds_user, mysql_rds_password,
        account, endpoint, region, warehouse, db, role, snow_username, snow_password, dbregion, dbschema,
        site, url, tableau_username, tableau_password, token_name, token_secret, project, mysql_server, mysql_port,
        mysql_username, mysql_password) = snowflake_tableau_info
    matches = ["CIE Output Comparison report"]
    try:
        dirname = os.path.dirname(file_path)
        name = dirname[::-1].split('/')[0][::-1]
        file_path = dirname
        logger.info(f"file_path {file_path}")
        shutil.make_archive(file_path, 'zip', dirname)
        os.rename(f"{file_path}.zip", f"{file_path}.twbx")

        wb_item = TSC.WorkbookItem(name=name, project_id=project_id)

        if any(x in file_path for x in matches):
            new_datasource = TSC.DatasourceItem(project_id)

            conn_creds = []
            conn1 = ConnectionItem()
            conn1.server_address =  mysql_server
            conn1.server_port = str(mysql_port)
            conn1.username =  mysql_username
            conn1._connection_type = 'mysql'
            conn1.embed_password = True
            conn1.connection_credentials = ConnectionCredentials(mysql_username, tableau_utils.decrypt_password(mysql_password), embed=True, oauth=False)
            conn_creds.append(conn1)

            conn2 = ConnectionItem()
            conn2.server_address = f"{account}.{region}.{endpoint}"
            conn2.server_port = ''
            conn2.username = snow_username
            conn2._connection_type = 'snowflake'
            conn2.embed_password = True
            conn2.connection_credentials = ConnectionCredentials(snow_username, snow_password, embed=True, oauth=False)
            conn_creds.append(conn2)

            wb_item = server.workbooks.publish(wb_item, f"{file_path}.twbx", 'Overwrite', connections = conn_creds, skip_connection_check=True)
        else:
            wb_item = server.workbooks.publish(wb_item, f"{file_path}.twbx", 'Overwrite',)
        logger.info(f"published workbook {wb_item}")

    except Exception as e:
        logger.exception(e)
        raise


def replace_placeholders(files, replacement_tokens):
    for filename in files:
        basename = os.path.basename(filename)
        dirname = os.path.dirname(filename)
        name = dirname[::-1].split('/')[0][::-1]
        customer_env = f"{replacement_tokens['CUSTOMER_NAME']} {replacement_tokens['ENVIRONMENT']}"

        new_file_name = f"{output_dir}{customer_env} {name}/{customer_env} {basename}"
        if 'templates' in dirname:
            new_file_name = f"{output_dir}{customer_env} {basename.split('.')[0]}/{customer_env} {basename}"

        if os.path.exists(new_file_name) and os.path.isdir(new_file_name):
            shutil.rmtree(os.path.dirname(new_file_name))
        pathlib.Path(os.path.dirname(new_file_name)).mkdir(parents=True, exist_ok=True)

        content = None
        with open(filename) as file:
            logger.info(f"reading template {filename}")
            content = string.Template(file.read()).safe_substitute(replacement_tokens)

        assert content is not None, (f"something went wrong when processing {filename}")
        with open(new_file_name, 'w') as f:
            f.write(content)
        logger.info(f"wrote {new_file_name} successfully")

if __name__ == "__main__":
    server = None
    error = None
    try:
        parser = argparse.ArgumentParser(description=f'{app_name} arguments')
        for arg in expected_args:
            thetype = arg.get('type')
            parser.add_argument( arg.get('name'), help=arg.get('help'), required=arg.get('required'), default=arg.get('default'), type = thetype if thetype is None else locate(thetype))
        args = parser.parse_args()
        snowflake_tableau_info = tableau_utils.get_snowflake_tableau_connection_info(args.customer_name, args.environment, args.site_type, args.app_name, args.ecosystem, logger)
        assert snowflake_tableau_info is not None, f"\n\tCheck `aktanameta.CustomerTableauSite`, `aktanameta.TableauSite`, `aktanameta.TableauCustomerMap`, `aktanameta.CustomerSnowflakeConfigProperties` entries for customer: '{args.customer_name}',  environment: '{args.environment}', TableauSite type: '{args.site_type}', Snowflake appName '{args.app_name}'"

        (mysql_learningDB, mysql_portforward_rds, mysql_portforward_port, mysql_rds_server, mysql_rds_user, mysql_rds_password,
            account, endpoint, region, warehouse, db, role, snow_username, snow_password, dbregion, dbschema,
            site, url, tableau_username, tableau_password, token_name, token_secret, project, mysql_server, mysql_port,
            mysql_username, mysql_password) = snowflake_tableau_info

        output_dir = f"{output_dir}{project}_{args.environment}/"

        if os.path.exists(output_dir) and os.path.isdir(output_dir):
            shutil.rmtree(os.path.dirname(output_dir))
        pathlib.Path(os.path.dirname(output_dir)).mkdir(parents=True, exist_ok=True)

        import tableauserverclient as TSC
        if token_name is None or len(token_name) == 0:
            tableau_auth = TSC.TableauAuth(tableau_username, tableau_utils.decrypt_password(tableau_password), site)
            server = TSC.Server(url, use_server_version=True)
            server.auth.sign_in(tableau_auth)
        else:
            tableau_auth = TSC.PersonalAccessTokenAuth(token_name, token_secret, site)
            server = TSC.Server(url, use_server_version=True)
            server.auth.sign_in_with_personal_access_token(tableau_auth)

        project_name = f"{project} {args.environment}".upper()
        project_id = get_project_id(project_name)
        if project_id is None:
            project_id = create_project(project_name)
            logger.info(f"{project_name} project id {project_id}")

        sub_project_id = get_sub_project_id(project_id)


        if sub_project_id is not None:
            server.projects.delete(sub_project_id)
            logger.info(f"Deleted existing {sub_project_name} sub project id {sub_project_id}")

        sub_project_id = create_sub_project(sub_project_name, project_id)
        logger.info(f"Create {sub_project_name} sub project id {sub_project_id}")

        replacement_tokens = {
            "CUSTOMER_NAME": args.customer_name.upper(),
            "ENVIRONMENT": args.environment.upper(),
            "TABLEAU_URL": url,
            "TABLEAU_SERVER": url.replace("https://", ""),
            "TABLEAU_SITE": site,
            "SNOW_SERVER": f"{account}.{region}.{endpoint}",
            "SNOW_DBNAME": db.upper(),
            "SNOW_SCHEMA": dbschema.upper(),
            "SNOW_ROLE": role.upper(),
            "SNOW_USERNAME": snow_username.upper(),
            "SNOW_WAREHOUSE": warehouse.upper(),
            "MYSQL_PORT": mysql_port,
            "MYSQL_LEARNING_DB": mysql_learningDB,
            "MYSQL_USER_NAME": mysql_username,
            "MYSQL_SERVER": mysql_server
        }

        ds_templates = tableau_utils.get_files(templates_dir, 'tds')
        wb_templates = tableau_utils.get_files(templates_dir, 'twb')
        replace_placeholders(ds_templates, replacement_tokens)
        replace_placeholders(wb_templates, replacement_tokens)

        image_files = tableau_utils.get_files(templates_dir, 'png')
        from shutil import copyfile

        for src in image_files:
            basename = os.path.basename(src)
            dirname = os.path.dirname(src)
            subdirname=os.path.basename(os.path.dirname(dirname))
            name = dirname[::-1].split('/')[0][::-1]
            customer_env = f"{replacement_tokens['CUSTOMER_NAME']} {replacement_tokens['ENVIRONMENT']}"
            dst = f"{output_dir}{customer_env} {subdirname}/{name}/{basename}"

            if not os.path.exists(os.path.dirname(dst)):
                pathlib.Path(os.path.dirname(dst)).mkdir(parents=True, exist_ok=True)
            copyfile(src, dst)

        # publish datasources
        datasources = [publish_datasource(snowflake_tableau_info, sub_project_id, ds) for ds in tableau_utils.get_files(output_dir, 'tds')]

        # Update datasource connections
        update_datasource_connection(datasources, snowflake_tableau_info)

        # get_datasource_connection(datasources, snowflake_tableau_info)

        # publish workbooks
        [publish_workbook(snowflake_tableau_info, sub_project_id, wb) for wb in tableau_utils.get_files(output_dir, 'twb')]

        # create data extracts
        [create_extract(ds) for ds in datasources if not ds.has_extracts]

    except Exception as e:
        logger.exception(e)
        error = str(e)
    finally:
        if server is not None:
            server.auth.sign_out()
        assert error is None, (error)
