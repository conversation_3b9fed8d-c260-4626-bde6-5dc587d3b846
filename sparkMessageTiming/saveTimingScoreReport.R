##########################################################
#
#
# aktana- messageSequence estimates estimates Aktana Learning Engines.
#
# description: save messageSequence results
#
# created by : <EMAIL>
#
# created on : 2018-08-01
#
# Copyright AKTANA (c) 2016-2018.
#
#
####################################################################################################################
CHAR_COL_LENGTH <- 80

saveTimingScoreReport <- function(sc, con, con_l, scores, tteParams)
{
    library(data.table)
    library(futile.logger)
    library(sparklyr)
    library(dplyr)
  
    jdbc.url <- sprintf("****************************************", dbhost, port, dbname)
    jdbc.url.learning <- sprintf("*************************************************", dbhost, port, dbname)
  
    flog.info("entered saveTimingScoreReport function...")    

    segs <- tteParams[["segs"]]
    channelUID <- tteParams[["channelUID"]]
    channels <- tteParams[["channels"]]
    numPartitions <- tteParams[["NUM_PARTITIONS_W"]]
    
    scores <- scores %>% select(-date)
    
    #setnames(scores, "predict", "probability")
    scores <- scores %>% dplyr::rename(probability = predict)
    
    nowDate <- Sys.Date()
    nowTime <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")

    ## calculate segment scores for reporting
    scoresSeg <- data.frame(segmentType=character(), event=character(), intervalOrDoW=integer(), learningRunUID=character(), method=character(), probability=double(), segment=character())
    scoresSeg <- sdf_copy_to(sc, scoresSeg, "scoresSeg", overwrite = T)  # create an empty spark data frame
    
    #acctSeg <- accounts[, c("accountId", segs), with=F]
    acctSeg <- accounts %>% select(one_of(c("accountId", segs)))
      
    #scores.seg <- merge(scores, acctSeg, by="accountId")
    scores.seg <- scores %>% inner_join(acctSeg, by="accountId")
    
    for (seg in segs) {
      #scoresTmp <- scores.seg[, .(probability=mean(probability)), by=c(seg, "event", "intervalOrDoW", "learningRunUID", "method")]
      scoresTmp <- scores.seg %>% group_by_(seg, "event", "intervalOrDoW", "learningRunUID", "method") %>% 
                   #dplyr::mutate(probability = mean(probability))
                   dplyr::summarise(probability = mean(probability))
        
      #scoresTmp$segment <- seg 
      scoresTmp <- scoresTmp %>% dplyr::mutate(segment = seg)
      
      #setnames(scoresTmp, seg, "segmentType")
      scoresTmp <- dplyr::rename(scoresTmp, segmentType = seg)
      
      scoresTmp <- scoresTmp %>% dplyr::mutate(segmentType = substr(segmentType, 1, CHAR_COL_LENGTH)) # trim to correct length
      
      #scoresSeg <- rbind(scoresTmp, scoresSeg, fill=T)
      scoresSeg <- sdf_bind_rows(scoresTmp, scoresSeg)
    }
    
    #scoresSeg <- scoresSeg %>% dplyr::mutate(createdAt = nowTime, updatedAt = nowTime)
    
    #scoresSeg[event == "S", channelUID := "SEND"]
    scoresSeg <- scoresSeg %>% dplyr::mutate(channelUID = ifelse(event == "S", "SEND", event))
    
    #scoresSeg[event == "V", channelUID := "VISIT"]
    scoresSeg <- scoresSeg %>% dplyr::mutate(channelUID = ifelse(event == "V", "VISIT", channelUID))
    
    scoresSeg <- scoresSeg %>% select(-event) %>% filter(!is.na(segmentType))
    #scoresSeg <- scoresSeg[!is.na(segmentType)]

    if (channelUID == "SEND_CHANNEL") {  # optimize SEND channel
        if (!"VISIT" %in% channels) {
            # listen to SEND channel only, so we only output best time to send email after SEND
            # for other channels, always output best time to send email afer VISIT or EVENT
            flog.info("listen to SEND channel in reporting.")
            #scoresSeg <- scoresSeg[channelUID == "SEND"] 
            scoresSeg <- scoresSeg %>% filter(channelUID == "SEND")
        }
    }

    dbGetQuery(con_l, "TRUNCATE TABLE SegmentTimeToEngageReport")
    
    #FIELDS.seg <- list(learningRunUID="varchar(80)", segment="varchar(40)", segmentType="varchar(80)", channelUID="varchar(20)", 
    #                   probability="double", intervalOrDoW="int", method="varchar(40)", createdAt="datetime", updatedAt="datetime")
    
    flog.info("writing to table SegmentTimeToEngageReport ...")    

    dbGetQuery(con_l, "SET FOREIGN_KEY_CHECKS = 0;")

    #tryCatch(dbWriteTable(con_l, name="SegmentTimeToEngageReport", value=as.data.frame(scoresSeg), overwrite=F, append=T, row.names=FALSE, field.types=FIELDS.seg),
    #         error = function(e) {
    #           flog.error('Error in writing back to table SegmentTimeToEngageReport!', name='error')
    #           dbDisconnect(con_l)
    #           quit(save = "no", status = 67, runLast = FALSE) # user-defined error code 67 for failure of writing back to database table
    #         }
    #)    
    
    spark_write_jdbc(scoresSeg, name = "SegmentTimeToEngageReport",
                     options = list(url = jdbc.url.learning, driver='com.mysql.jdbc.Driver', user = dbuser, password = dbpassword), 
                     mode = "append")
    
    ## Now export optimal interval by finding max prob
    #setorder(scoresSeg, learningRunUID, segment, segmentType, channelUID, method, -probability)
    scoresSeg <- dplyr::arrange(scoresSeg, learningRunUID, segment, segmentType, channelUID, method, desc(probability))
    
    #scoresSeg.max <- scoresSeg[, .SD[1], by=c("learningRunUID", "segment", "segmentType", "channelUID", "method")]
    scoresSeg.max <- scoresSeg %>% group_by(learningRunUID, segment, segmentType, channelUID, method) %>% 
                                   dplyr::arrange(learningRunUID, segment, segmentType, channelUID, method, desc(probability)) %>%
                                   filter(row_number() == 1)
    
    dbGetQuery(con_l, "TRUNCATE TABLE TTEsegmentReport")
    
    FIELDS.seg.max <- list(learningRunUID="varchar(80)", segment="varchar(40)", segmentType="varchar(80)", channelUID="varchar(20)", 
                       intervalOrDoW="int", method="varchar(40)", probability="double", createdAt="datetime", updatedAt="datetime")
    
    flog.info("writing to table TTEsegmentReport ...")    

    #tryCatch(dbWriteTable(con_l, name="TTEsegmentReport", value=as.data.frame(scoresSeg.max), overwrite=F, append=T, row.names=FALSE, field.types=FIELDS.seg.max),
    #         error = function(e) {
    #           flog.error('Error in writing back to table TTEsegmentReport!', name='error')
    #           dbDisconnect(con_l)
    #           quit(save = "no", status = 67, runLast = FALSE) # user-defined error code 67 for failure of writing back to database table
    #         }
    #)    
    
    spark_write_jdbc(scoresSeg.max, name = "TTEsegmentReport",
                     options = list(url = jdbc.url.learning, driver='com.mysql.jdbc.Driver', user = dbuser, password = dbpassword), 
                     mode = "append")
    
    ########## save scores reporting results  #############
    
    #scores[event == 'S',  event := '12'] # SEND_ANY
    #scores[event == 'V', event := '3']   # VISIT
    #scores[event == 'W', event := '13']  # WEB_INTERACTION
    #scores[event == 'VS', event := '5']  # VISIT_SAMPLES
    
    scores <- scores %>% dplyr::mutate(event = ifelse(event == 'S', '12', event))
    scores <- scores %>% dplyr::mutate(event = ifelse(event == 'V', '3', event))
    scores <- scores %>% dplyr::mutate(event = ifelse(event == 'W', '13', event))
    scores <- scores %>% dplyr::mutate(event = ifelse(event == 'VS', '5', event))
    
    #scores[, event := as.integer(event)]
    scores <- scores %>% dplyr::mutate(event = as.integer(event))
    
    #setnames(scores, "event", "repActionTypeId")  # rename event to repActionTypeId
    scores <- dplyr::rename(scores, repActionTypeId = event)
    
    if (channelUID == "SEND_CHANNEL") {  # optimize SEND channel
        if (!"VISIT" %in% channels) {  
            # listen to SEND channel only, so we only output best time to send email after SEND
            # for other channels, always output best time to send email afer VISIT or EVENT
            flog.info("listen to SEND channel in reporting.")
            #scores <- scores[repActionTypeId == 12] 
            scores <- scores %>% filter(repActionTypeId == 12)
        }
    }

    scores <- scores %>% dplyr::mutate(runDate = as.Date(nowDate))
    
    dbGetQuery(con_l, "TRUNCATE TABLE AccountTimeToEngageReport")
    
    flog.info("writing to table AccountTimeToEngageReport ...")    

    #FIELDS <- list(learningRunUID="varchar(80)", accountId="int(11)", repActionTypeId="tinyint", 
    #               intervalOrDoW="int", method="varchar(40)", probability="double", runDate="date", createdAt="datetime", updatedAt="datetime")
    
    # append new scores/probs
    startTimer <- Sys.time()
    #tryCatch(dbWriteTable(con_l, name="AccountTimeToEngageReport", value=as.data.frame(scores), overwrite=F, append=T, row.names=FALSE, field.types=FIELDS),
    #         error = function(e) {
    #           flog.error('Error in writing back to LearningDB table AccountTimeToEngageReport!', name='error')
    #           dbDisconnect(con)
    #           quit(save = "no", status = 67, runLast = FALSE) # user-defined error code 67 for failure of writing back to database table
    #         }
    #)
    scores <- sdf_repartition(scores, partitions = numPartitions)
    
    spark_write_jdbc(scores, name = "AccountTimeToEngageReport",
                     options = list(url = jdbc.url.learning, driver='com.mysql.jdbc.Driver', user = dbuser, password = dbpassword), 
                     mode = "append")
    
    flog.info("Write new scores to AccountTimeToEngageReport in Learning DB: Time = %s", Sys.time()-startTimer)
    
    scores <- dplyr::arrange(scores, learningRunUID, accountId, repActionTypeId, method, desc(probability))
    
    #scores.max <- scores[, .SD[1], by=c("learningRunUID", "accountId", "repActionTypeId", "method")]
    scores.max <- scores %>% group_by(learningRunUID, accountId, repActionTypeId, method) %>% 
                             dplyr::arrange(learningRunUID, accountId, repActionTypeId, method, desc(probability)) %>%
                             filter(row_number() == 1) %>% ungroup()
    
    # convert repActionTypeId to channelUID
    #scores.max[repActionTypeId == 12, channelUID := "SEND"]
    #scores.max[repActionTypeId == 3, channelUID := "VISIT"]
    #cores.max$repActionTypeId <- NULL
    
    scores.max <- scores.max %>% dplyr::mutate(channelUID = ifelse(repActionTypeId == 12, "SEND", repActionTypeId))
    scores.max <- scores.max %>% dplyr::mutate(channelUID = ifelse(repActionTypeId == 3, "VISIT", channelUID)) %>%
                  select(-repActionTypeId)
    
    #accountIdMap <- data.table(dbGetQuery(con, "SELECT accountId, externalId FROM Account WHERE isDeleted=0;"))
    
    accountIdMap <- spark_read_jdbc(sc, "Account", options = list(
                        url = jdbc.url, user = dbuser, password = dbpassword,
                        driver='com.mysql.jdbc.Driver',
                        dbtable = "(SELECT accountId, externalId FROM Account WHERE isDeleted=0) as my_query",
                        numPartitions = as.character(numPartitions)))
    
    # get accountUID from accountId
    #scores.max <- merge(scores.max, accountIdMap, by="accountId")
    scores.max <- scores.max %>% inner_join(accountIdMap, by="accountId")
    
    #setnames(scores.max, "externalId", "accountUID")
    scores.max <- dplyr::rename(scores.max, accountUID = externalId)
    
    scores.max <- scores.max %>% select(-c(accountId, runDate))
    
    dbGetQuery(con_l, "TRUNCATE TABLE TTEhcpReport")
    
    FIELDS <- list(learningRunUID="varchar(80)", accountUID="varchar(80)", channelUID="varchar(20)",
                   intervalOrDoW="int", probability="double", createdAt="datetime", updatedAt="datetime")
    
    flog.info("writing to table TTEhcpReport ...") 
    
    # append new scores/probs
    startTimer <- Sys.time()
    #tryCatch(dbWriteTable(con_l, name="TTEhcpReport", value=as.data.frame(scores.max), overwrite=F, append=T, row.names=FALSE, field.types=FIELDS),
    #         error = function(e) {
    #           flog.error('Error in writing back to LearningDB table TTEhcpReport!', name='error')
    #           dbDisconnect(con)
    #           quit(save = "no", status = 67, runLast = FALSE) # user-defined error code 67 for failure of writing back to database table
    #         }
    #)
    spark_write_jdbc(scores.max, name = "TTEhcpReport",
                     options = list(url = jdbc.url.learning, driver='com.mysql.jdbc.Driver', user = dbuser, password = dbpassword), 
                     mode = "append")
    
    flog.info("Append new scores to TTEhcpReport: Time = %s", Sys.time()-startTimer)
    
    dbGetQuery(con_l, "SET FOREIGN_KEY_CHECKS = 1;")
}
