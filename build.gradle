import groovy.io.FileType
import groovy.sql.Sql
import com.aktana.gradle.MetadataHelper
import com.aktana.data.tableau.*
import com.aktana.data.metadata.*
import com.aktana.data.common.*
import static org.apache.commons.lang.StringEscapeUtils.escapeHtml

buildscript {
    repositories {
        maven {
            url "${artifactory_contextUrl}/aktana-snapshot"
            credentials {
                username = "${artifactory_user}"
                password = "${artifactory_password}"
            }
        }
        flatDir { dirs "$rootDir/libs" }
    }

    dependencies {
        classpath(group: 'com.aktana', name: 'aktana-gradle-data-helper', version: '1.0.0-SNAPSHOT', changing: true)
        classpath 'com.aktana:aktana-gradle-helper:1.0.101-SNAPSHOT'
        classpath 'org.codehaus.groovy.modules.http-builder:http-builder:0.7'
        classpath 'org.apache.httpcomponents:httpmime:4.3.1'
        classpath 'org.custommonkey.xmlunit:com.springsource.org.custommonkey.xmlunit:1.2.0'
        classpath 'net.saliman:gradle-properties-plugin:1.4.5'
        classpath 'mysql:mysql-connector-java:8.0.33'
        classpath(group: 'commons-lang', name: 'commons-lang', version: '2.6')
    }
}


// We need this to be able to resolve mysql-connector-java
repositories {
    maven {
        url "${artifactory_contextUrl}/aktana-snapshot"
        credentials {
            username = "${artifactory_user}"
            password = "${artifactory_password}"
        }
    }
}
configurations { driver }
dependencies { driver 'mysql:mysql-connector-java:8.0.33' }
URLClassLoader loader = GroovyObject.class.classLoader
configurations.driver.each { File file -> loader.addURL(file.toURL()) }

/* task updateDatasourceNow() {
    doLast {
        def environment = getProjectProperty('environment')
        def datasourceName = getProjectProperty('datasourceName')

        def tableauConfig = MetadataUtils.readTableauMetadata(getProjectProperty('aktmeta.url'),getProjectProperty('aktmeta.user'),getProjectProperty('aktmeta.password'), getProjectProperty('customer'), environment)
        def tb = new TableauEntryPoint(tableauConfig.url, tableauConfig.siteAdminUser, MetadataHelper.decrypt(tableauConfig.siteAdminPassword), tableauConfig.siteName)
        def id = tb.getDatasourceId(name)
        tb.updateDatasourceNow(datasourceName)
    }
}
*/


def String getProjectProperty(String propertyName)
{
  String propertyValue = hasProperty(propertyName) ? this.properties[propertyName] : "null"
  return propertyValue
}

task genEncryptedPassword() {
    doLast {
        // usage: gradle genEncryptedPassword -Ppassword=<password>
        println MetadataHelper.encrypt(getProjectProperty('password'))
    }
}

task genDecryptedPassword() {
    doLast {
        // usage: gradle genDecryptedPassword -Ppassword=<password>
        println MetadataHelper.decrypt(getProjectProperty('password'))
    }
}

task publishToTableauServer() {
    doLast {
        def environment = getProjectProperty('environment')
        def customer = getProjectProperty('customer')
        def projectName = getProjectProperty('projectName')
        // def customerSpecific = getProjectProperty('customerSpecific')
        def reportName = 'bi-reports'
        def siteName = getProjectProperty('siteName')
        def dbEnvironment = getProjectProperty('dbEnvironment')
        def templateList = getProjectProperty('templateList')

        def tdsExtension = 'tds'
        def tdsxExtension = 'tdsx'
        def twbExtension = 'twb'
        def hyperExtension = 'hyper'

        def templatePath =  "${projectDir}/bi-reports"
        def customizationPath = "${projectDir}/${customer}/customization/generated/${environment}/tableau/${projectName}/bi-reports"

        def dbEnv = MetadataUtils.readCustomerSnowflakeConfigProperties(getProjectProperty('aktmeta.url'),getProjectProperty('aktmeta.user'),getProjectProperty('aktmeta.password'), customer, environment, 'SCD')

        println "will be reading template files from ${templatePath}"

        // Check for  templates path
        def templateFolder = new File(templatePath)
        if (!templateFolder.exists()) {
            throw new Exception ("The specified folder ${reportName} does not exist at ${templateFolder.getParent()}.")
        }

        // Prepare target folder
        new File(customizationPath).mkdirs()
        def backupPath = customizationPath + "_" + new Date().format("yyyyMMDDHHmmss")
        new File(backupPath).mkdirs()
        println "Backing up latest published files at: ${backupPath}"
        copy {
            from "${customizationPath}"
            into "${backupPath}"
        }

        // Check if target tableau project exists
        def tableauConfig = MetadataUtils.readTableauMetadata(getProjectProperty('aktmeta.url'),getProjectProperty('aktmeta.user'),getProjectProperty('aktmeta.password'), customer, environment)[siteName]
        println "tableauConfig.url=${tableauConfig.url}, tableauConfig.siteName=${tableauConfig.siteName}"
        def tb = new TableauEntryPoint(tableauConfig.url, tableauConfig.siteAdminUser, MetadataHelper.decrypt(tableauConfig.siteAdminPassword), tableauConfig.siteName)
        if (!tb.findProjectByName(projectName)) {
            throw new Exception("The specified Tableau Project ${projectName} does not exist at ${tableauConfig.url}/#/site/${tableauConfig.siteName}.")
        }

        def datasourceNames = []
        def tdsxFiles = []
        def twbFiles = []
        templateFolder.eachFileRecurse (FileType.FILES) {
            // make sure we always have datasource names
            if(it.path.contains(tdsxExtension)) {
                datasourceNames << it.name.replace(".template", "").replace(".${tdsxExtension}", "")
            }
            if(it.path.contains(tdsxExtension) &&
                    ( templateList.contains("*.${tdsxExtension}") || templateList.contains(it.name.replace(".template", "")) )
            ) {
                tdsxFiles << it
            }

            if(it.path.contains(twbExtension) &&
                    ( templateList.contains("*.${twbExtension}") || templateList.contains(it.name.replace(".template", "")) )
            ) {
                twbFiles << it
            }
        }

        if (templateList.contains(tdsxExtension) && tdsxFiles.isEmpty()) {
            throw new Exception( "specified tdsx file(s) for ${reportName} cannot be found at ${templateFolder}" )
        }
        if ( templateList.contains(twbExtension) && twbFiles.isEmpty())  {
            throw new Exception( "specified twb file for ${reportName} cannot be found at ${templateFolder}" )
        }

        println "customer files will be generated under ${customizationPath}"
        tdsxFiles.each {
            def templateName = it.name.replace(".template", "")

            def datasourceName = templateName.replace(".${tdsxExtension}", "_${customer}_${dbEnvironment}")
            println "processing datasource template: ${templateName}, datasourceName: ${datasourceName}"

            def datasourceDir = new File("${customizationPath}/${datasourceName}")
            if (datasourceDir.exists()) {
                datasourceDir.deleteDir()
            }
            datasourceDir.mkdirs()

            ZipFileHelper.unzip(it.path, datasourceDir.path)
						def serverURL = ""
						if (dbEnv.region == "") {
							serverURL = dbEnv.account+'.'+dbEnv.endPoint
						} else {
							serverURL = dbEnv.account+'.'+dbEnv.region+'.'+dbEnv.endPoint
						}
            def hyperTemplateName = it.name.replace(tdsxExtension, hyperExtension)
            def tdsTemplateName = it.name.replace(tdsxExtension, tdsExtension)
            def customerTds = new File("${datasourceDir.path}/${tdsTemplateName}")
            def content = customerTds.text
                    .replaceAll('\\$\\{customer\\}', customer)
                    .replaceAll('\\$\\{site\\}', tableauConfig.siteName)
                    .replaceAll('\\$\\{dbname\\}', dbEnv.db)
                    .replaceAll('\\$\\{warehouse\\}', dbEnv.warehouse)
                    .replaceAll('\\$\\{schema\\}', dbEnv.dbSchema)
                    .replaceAll('\\$\\{server\\}', serverURL)
                    .replaceAll('\\$\\{user\\}', dbEnv.user)
                    .replaceAll('\\$\\{projectName\\}', projectName)
                    .replaceAll('\\$\\{extract}', "${datasourceName}.${hyperExtension}")
                    .replaceAll('\\$\\{role\\}', dbEnv.role)
            customerTds.text = ''
            customerTds << content
            customerTds.renameTo "${datasourceDir.path}/${datasourceName}.${tdsExtension}"
            new File("${datasourceDir.path}/Data/Extracts/${hyperTemplateName}").renameTo "${datasourceDir.path}/Data/Extracts/${datasourceName}.${hyperExtension}"
            def tdsxFilePath = "${datasourceDir.path}/${datasourceName}.${tdsxExtension}"
            ZipFileHelper.zip(datasourceDir.path, tdsxFilePath)

            def revisionNumber = tb.getLatestRevisionNumber(tb.getDatasourceId(datasourceName), "datasources")
            println "Latest published version of ${datasourceName} is ${revisionNumber}"

            def currentFile = "${backupPath}/${datasourceName}/${datasourceName}.${tdsExtension}.${revisionNumber}"
            def newFile = "${datasourceDir.path}/${datasourceName}.${tdsExtension}"
            if (revisionNumber == 0 || !DiffHelper.areFilesIdentical(currentFile, newFile)){
                println "${currentFile} and ${newFile} contents are different, will publish new file to Tableau site"
                // def datasourcePassword = MetadataHelper.decrypt(tableauConfig.datasourcePassword)
                def user = escapeHtml(dbEnv.user)
                def pass = escapeHtml(dbEnv.password)
                def datasourceId = tb.publishDatasource(tdsxFilePath, datasourceName, projectName, user, pass)
                println "Published datasource: ${datasourceName}, ${datasourceId}"

                def connections = tb.getConnectionDetails(datasourceId)
                connections.each {
                    if (it.serverAddress==getProjectProperty('aktanameta.serverAddress')) {
                        println("Updating connection to metadata rds: "+it.id)
                        println(it.serverPort)
                        tb.updateDataSourceConnectionById(datasourceId, it.id, it.serverAddress, Integer.parseInt(it.serverPort), getProjectProperty('aktmeta.user'), getProjectProperty('aktmeta.password'))
                    }
                    else {
                        println("Updating connection to cutomer rds: "+it.id)
                        println(tableauConfig.datasourcePort)
                        tb.updateDataSourceConnectionById(datasourceId, it.id, serverURL, tableauConfig.datasourcePort, user, pass)
                    }
                }

                println "Updated datasource: ${datasourceName}, ${datasourceId} connection(s)"

                revisionNumber = tb.getLatestRevisionNumber(datasourceId, "datasources")
                new File(newFile).renameTo "${newFile}.${revisionNumber}"
            } else {
                println "${currentFile} and ${newFile} contents are indentical, will not publish new file to Tableau site"
                new File(newFile).renameTo "${newFile}.${revisionNumber}"
            }
        }

        twbFiles.each {
            def replacementStrings = [:]
            datasourceNames.each {print it}
            datasourceNames.each {
                def name = "${it}_${customer}_${dbEnvironment}"
                println "Setting up datasource connection info: ${name}"

                def id = tb.getDatasourceId(name)
                replacementStrings.put("id='${it}'", "id='${id}'")
                replacementStrings.put("dbname='${it}'", "dbname='${name}'")
                replacementStrings.put("caption='${it}'", "caption='${name}'")
                replacementStrings.put("server-ds-friendly-name='${it}'", "server-ds-friendly-name='${name}'")
                replacementStrings.put("dbname=&apos;${it}&apos;", "dbname=&apos;${name}&apos;")
            }

            def workbookName = customer + " " +dbEnvironment+ " " +it.name.replace(".template", "").replace(".${twbExtension}", "").replace('_', ' ')
            def newFile = "${customizationPath}/${workbookName}.${twbExtension}"
            def twbFile = new File(newFile)
            twbFile.withWriter { writer ->
                def content = it.text.replaceAll('\\$\\{site\\}', tableauConfig.siteName).replaceAll('\\$\\{name\\}', workbookName).replaceAll('\\$\\{projectName\\}',projectName.replace(' ', '_'))
                replacementStrings.eachWithIndex {key, value, index ->
                    content = content.replaceAll(key, value)
                }
                writer.write(content)
            }

            def workbookId = tb.getWorkbookId(workbookName)
            println "${workbookId}"
            def revisionNumber = (workbookId == '') ? 0 : tb.getLatestRevisionNumber(workbookId, "workbooks")
            println "Latest published version of ${workbookId} is ${revisionNumber}"
            def currentFile = "${backupPath}/${workbookName}.${twbExtension}.${revisionNumber}"
            if (revisionNumber == 0 || !DiffHelper.areFilesIdentical(currentFile, newFile)){
                println "${currentFile} and ${newFile} contents are different, will publish new file to Tableau site"
                workbookId = tb.publishWorkbook(newFile, workbookName, projectName)
                println "Published workbook : ${workbookName}, ${workbookId}"
                sleep(5 * 1000)
                revisionNumber = tb.getLatestRevisionNumber(tb.getWorkbookId(workbookName), "workbooks")
                new File(twbFile.getPath()).renameTo "${newFile}.${revisionNumber}"
            } else {
                println "${currentFile} and ${newFile} contents are indentical, will not publish new file to Tableau site"
                new File(twbFile.getPath()).renameTo "${newFile}.${revisionNumber}"
            }
        }
    }
}
