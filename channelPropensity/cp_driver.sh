#!/bin/bash

# Wrapper/driver script for Channel-propensity jobs
# Can be used to invoke any of the Channel-propensity related jobs

# Usage: cp_driver.sh <job_name> --customer <customer-name> --env <env-name>"
#   Supported job names are: RUN_CP_PREPROC and RUN_CP_POSTPROC"
#   Sample usage: ./cp_driver.sh RUN_CP_PREPROC --customer pfizerbdpus --env dev"

# ./cp_driver.sh RUN_CP_PREPROC --customer pfizerbdpus --env dev
# ./cp_driver.sh RUN_CP_POSTPROC --customer pfizerbdpus --env dev

# RUN_CP_PREPROC - to execute the pre-processor Job for CP
# RUN_CP_POSTPROC - to execute the post-processor Job for CP

#
# All jobs take the following command line parameters:
#   --customer custName (name of the customer configured in aktana metadata)
#   --env envName (name of the environment configured in aktana metadata)
#   --region regionName (name of the region for the Job.  Note: This is not used now since metadata is used to find the region for databricks and Snowflake
#

echo "Python version : $(python3 --version)"

CP_INSTALL_DIR=`dirname $0`

JOB=$1
shift 1

APP_PARAM="--app CHANNEL_PROPENSITY"
PREPROC_SCRIPT="--scriptfile $CP_INSTALL_DIR/cp_reset_model.sql"

echo "Install python dependencies from $CP_INSTALL_DIR/requirements.txt"
pip3 install -r $CP_INSTALL_DIR/requirements.txt

case $JOB in

    RUN_CP_PREPROC)
        echo "Running CP Pre-processor job...python3 $CP_INSTALL_DIR/../common/pyUtils/run_sql_on_snowflake.py $@ $PREPROC_SCRIPT $APP_PARAM"
        python3 $CP_INSTALL_DIR/../common/pyUtils/run_sql_on_snowflake.py $@ $PREPROC_SCRIPT $APP_PARAM
	rc=$?
        ;;

    RUN_CP_POSTPROC)
        echo "Running CP Post-processor job...python3 $CP_INSTALL_DIR/cp_post_processor_dse.py $@ $APP_PARAM"
        python3 $CP_INSTALL_DIR/cp_post_processor_dse.py $@ $APP_PARAM
	rc=$?
        ;;

    *)
        echo "Usage: cp_driver.sh <job_name> --customer <customer-name> --env <env-name>"
        echo "  Supported job names are: RUN_CP_PREPROC and RUN_CP_POSTPROC"
        echo "  Sample usage: ./cp_driver.sh RUN_CP_PREPROC --customer pfizerbdpus --env dev"
	rc=1
        ;;

esac
echo "Returning from cp driver with rc=$rc"
exit $rc

