delete from "System_Config";

INSERT INTO "System_Config" ("Client","Process_Name","Feature_Period","Last_Updated","Channel_Active","Metric","Update_Type","Threshold","CurrentModel","ChallengerModel","Model_Initialisation","Status","Deployment","Last_Execution","Created_date","Data_Location") VALUES
	 ('Default','channel_propensity','202003-202102','2021-04-03','{
  "Visit_Flag" : 1,
  "Email_Flag" : 1,
  "Virtual_Visit_Flag" : 1
}','{  "Metric1": "Email_ROC"  ,  "Metric2": "Visit_ROC" ,   "Metric3": "Virtual_Visit_ROC" }','Automatic','{"Visit_ROC" :0.6 ,   "Email_ROC" : 0.7,  "Virtual_ROC" : 0.7 }'
,NULL,NULL,NULL,NULL,NULL,'04-Mar-2021','04-Mar-2021','knime://knime.workflow/../../Input/featureStore_202003-202102.table');

                                                                           
delete from "Model_Config";

insert into "Model_Config"  
values('Default','channel_propensity','{  "Metric1": "Email_ROC"  ,  "Metric2": "Visit_ROC" ,   "Metric3": "Virtual_Visit_ROC" }', 'Automatic',
       '{"Visit_ROC" :0.6 ,   "Email_ROC" : 0.7,  "Virtual_ROC" : 0.7 }',null,null,null,'<EMAIL>',null,null,'04-Mar-2021',null,null);

delete from "Models";
