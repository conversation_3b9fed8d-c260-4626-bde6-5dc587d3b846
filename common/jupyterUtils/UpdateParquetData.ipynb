{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-06-12T20:42:13.247888Z", "start_time": "2023-06-12T20:42:12.607237Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import pyspark\n", "from pyspark.sql import SparkSession\n", "import os\n", "os.environ['PYSPARK_SUBMIT_ARGS'] = \"--driver-memory 4g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.271,org.apache.hadoop:hadoop-aws:3.1.2,io.delta:delta-core_2.12:0.8.0,net.snowflake:snowflake-jdbc:3.8.0,net.snowflake:spark-snowflake_2.12:2.8.4-spark_3.0 pyspark-shell\"\n", "os.environ['SPARK_HOME'] = \"/Users/<USER>/libs/spark-3.1.2-bin-hadoop3.2\"\n", "os.environ['SPARK_LOCAL_IP'] = \"127.0.0.1\"\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-06-12T20:42:16.192825Z", "start_time": "2023-06-12T20:42:16.166193Z"}}, "outputs": [], "source": ["# Create Parameters\n", "ACCESS_ID = \"\"\n", "SECRET_KEY = \"\"\n", "SESSION_TOKEN = \"\"\n", "\n", "SPARK_APP_NAME = \"SuggCandidateUtility\"\n", "S3_PATH = \"s3a://aktana-bdp-devnovartisau/dev/data/archive_rds/repproductauthorization/updatedatyear=2023/updatedatmonth=3/updatedatday=5/\"\n", "LOCAL_CSV_PATH = \"csv\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-06-12T20:42:17.335633Z", "start_time": "2023-06-12T20:42:17.329628Z"}}, "outputs": [], "source": ["def initialize_spark():\n", "    \"\"\"\n", "    Initialize spark session\n", "    :param self:\n", "    :return:\n", "    \"\"\"\n", "    sc = pyspark.SparkContext()\n", "    sc.setSystemProperty(\"com.amazonaws.services.s3.enableV4\", \"true\")\n", "    sc.setLogLevel(\"WARN\")\n", "    hadoop_conf = sc._jsc.hadoopConfiguration()\n", "    hadoop_conf.set(\"fs.s3a.impl\", \"org.apache.hadoop.fs.s3a.S3AFileSystem\")\n", "    hadoop_conf.set(\"com.amazonaws.services.s3.enableV4\", \"true\")\n", "    hadoop_conf.set(\"fs.s3a.access.key\", ACCESS_ID)\n", "    hadoop_conf.set(\"fs.s3a.secret.key\", SECRET_KEY)\n", "    hadoop_conf.set(\"fs.s3a.session.token\", SESSION_TOKEN)\n", "    hadoop_conf.set(\"fs.s3a.connection.maximum\", \"100000\")\n", "    # hadoop_conf.set(\"fs.s3a.endpoint\", \"s3.\" + constants.aws_region + \".amazonaws.com\")\n", "    hadoop_conf.set(\"fs.s3a.endpoint\", \"s3.amazonaws.com\")\n", "    hadoop_conf.set(\"delta.logRetentionDuration\", \"36500\")\n", "    hadoop_conf.set(\"delta.deletedFileRetentionDuration\", \"365\")\n", "    hadoop_conf.set(\"fs.s3a.aws.credentials.provider\", \"org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider\")\n", "\n", "    spark = SparkSession(sc) \\\n", "        .builder \\\n", "        .appName(SPARK_APP_NAME) \\\n", "        .config(\"spark.sql.extensions\", \"io.delta.sql.DeltaSparkSessionExtension\") \\\n", "        .config(\"spark.sql.catalog.spark_catalog\", \"org.apache.spark.sql.delta.catalog.DeltaCatalog\") \\\n", "        .config(\"spark.sql.debug.maxToStringFields\", 1000) \\\n", "        .getOrCreate()\n", "\n", "    return spark"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-06-12T20:42:51.934870Z", "start_time": "2023-06-12T20:42:19.101320Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [":: loading settings :: url = jar:file:/Users/<USER>/libs/spark-3.1.2-bin-hadoop3.2/jars/ivy-2.4.0.jar!/org/apache/ivy/core/settings/ivysettings.xml\n"]}, {"name": "stderr", "output_type": "stream", "text": ["<PERSON> Default <PERSON>ache set to: /Users/<USER>/.ivy2/cache\n", "The jars for the packages stored in: /Users/<USER>/.ivy2/jars\n", "com.amazonaws#aws-java-sdk-bundle added as a dependency\n", "org.apache.hadoop#hadoop-aws added as a dependency\n", "io.delta#delta-core_2.12 added as a dependency\n", "net.snowflake#snowflake-jdbc added as a dependency\n", "net.snowflake#spark-snowflake_2.12 added as a dependency\n", ":: resolving dependencies :: org.apache.spark#spark-submit-parent-52dd576f-e66b-4ebd-a614-02b08d9a9ba7;1.0\n", "\tconfs: [default]\n", "\tfound com.amazonaws#aws-java-sdk-bundle;1.11.271 in central\n", "\tfound io.netty#netty-codec-http;4.1.17.Final in central\n", "\tfound io.netty#netty-codec;4.1.17.Final in central\n", "\tfound io.netty#netty-transport;4.1.17.Final in central\n", "\tfound io.netty#netty-buffer;4.1.17.Final in central\n", "\tfound io.netty#netty-common;4.1.17.Final in central\n", "\tfound io.netty#netty-resolver;4.1.17.Final in central\n", "\tfound io.netty#netty-handler;4.1.17.Final in central\n", "\tfound org.apache.hadoop#hadoop-aws;3.1.2 in central\n", "\tfound io.delta#delta-core_2.12;0.8.0 in central\n", "\tfound org.antlr#antlr4;4.7 in central\n", "\tfound org.antlr#antlr4-runtime;4.7 in central\n", "\tfound org.antlr#antlr-runtime;3.5.2 in central\n", "\tfound org.antlr#ST4;4.0.8 in central\n", "\tfound org.abego.treelayout#org.abego.treelayout.core;1.0.3 in central\n", "\tfound org.glassfish#javax.json;1.0.4 in central\n", "\tfound com.ibm.icu#icu4j;58.2 in central\n", "\tfound net.snowflake#snowflake-jdbc;3.8.0 in central\n", "\tfound net.snowflake#spark-snowflake_2.12;2.8.4-spark_3.0 in central\n", "\tfound net.snowflake#snowflake-ingest-sdk;0.10.1 in central\n", "\tfound net.snowflake#snowflake-jdbc;3.12.17 in central\n", ":: resolution report :: resolve 584ms :: artifacts dl 16ms\n", "\t:: modules in use:\n", "\tcom.amazonaws#aws-java-sdk-bundle;1.11.271 from central in [default]\n", "\tcom.ibm.icu#icu4j;58.2 from central in [default]\n", "\tio.delta#delta-core_2.12;0.8.0 from central in [default]\n", "\tio.netty#netty-buffer;4.1.17.Final from central in [default]\n", "\tio.netty#netty-codec;4.1.17.Final from central in [default]\n", "\tio.netty#netty-codec-http;4.1.17.Final from central in [default]\n", "\tio.netty#netty-common;4.1.17.Final from central in [default]\n", "\tio.netty#netty-handler;4.1.17.Final from central in [default]\n", "\tio.netty#netty-resolver;4.1.17.Final from central in [default]\n", "\tio.netty#netty-transport;4.1.17.Final from central in [default]\n", "\tnet.snowflake#snowflake-ingest-sdk;0.10.1 from central in [default]\n", "\tnet.snowflake#snowflake-jdbc;3.12.17 from central in [default]\n", "\tnet.snowflake#spark-snowflake_2.12;2.8.4-spark_3.0 from central in [default]\n", "\torg.abego.treelayout#org.abego.treelayout.core;1.0.3 from central in [default]\n", "\torg.antlr#ST4;4.0.8 from central in [default]\n", "\torg.antlr#antlr-runtime;3.5.2 from central in [default]\n", "\torg.antlr#antlr4;4.7 from central in [default]\n", "\torg.antlr#antlr4-runtime;4.7 from central in [default]\n", "\torg.apache.hadoop#hadoop-aws;3.1.2 from central in [default]\n", "\torg.glassfish#javax.json;1.0.4 from central in [default]\n", "\t:: evicted modules:\n", "\tnet.snowflake#snowflake-jdbc;3.8.0 by [net.snowflake#snowflake-jdbc;3.12.17] in [default]\n", "\t---------------------------------------------------------------------\n", "\t|                  |            modules            ||   artifacts   |\n", "\t|       conf       | number| search|dwnlded|evicted|| number|dwnlded|\n", "\t---------------------------------------------------------------------\n", "\t|      default     |   21  |   0   |   0   |   1   ||   20  |   0   |\n", "\t---------------------------------------------------------------------\n", ":: retrieving :: org.apache.spark#spark-submit-parent-52dd576f-e66b-4ebd-a614-02b08d9a9ba7\n", "\tconfs: [default]\n", "\t0 artifacts copied, 20 already retrieved (0kB/16ms)\n", "23/06/12 13:42:21 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable\n", "Using Spark's default log4j profile: org/apache/spark/log4j-defaults.properties\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "23/06/12 13:42:23 WARN Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.\n", "23/06/12 13:42:23 WARN Utils: Service 'SparkUI' could not bind on port 4041. Attempting port 4042.\n", "23/06/12 13:42:27 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}], "source": ["# Read parquet file from s3\n", "\n", "spark = initialize_spark()\n", "\n", "df = spark.read.parquet(S3_PATH)\n", "\n", "# Convert the spark dataframe to pandas dataframe\n", "pdf = df.to<PERSON><PERSON>()\n", "\n", "# Write Pandas dataframe to csv file\n", "#pdf.to_csv(LOCAL_CSV_PATH, index=False)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-06-12T20:43:45.234364Z", "start_time": "2023-06-12T20:43:45.223333Z"}}, "outputs": [{"data": {"text/plain": "   repProductAuthorizationId  repId  productId           createdAt  \\\n0                        113     70          1 2023-03-04 16:36:35   \n\n            updatedAt  \n0 2023-03-04 16:36:35  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>repProductAuthorizationId</th>\n      <th>repId</th>\n      <th>productId</th>\n      <th>createdAt</th>\n      <th>updatedAt</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>113</td>\n      <td>70</td>\n      <td>1</td>\n      <td>2023-03-04 16:36:35</td>\n      <td>2023-03-04 16:36:35</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["pdf.head()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"ename": "NameError", "evalue": "name 'pd' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/var/folders/1q/p4d_phrx1jz4xn05923ss7tr0000gr/T/ipykernel_6415/2783949724.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;31m# Read csv file from local\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mupdated_pdf\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_csv\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"csv\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'pd' is not defined"]}], "source": ["# Read the updated csv file from local\n", "updated_pdf = pd.read_csv(LOCAL_CSV_PATH)\n", "\n", "updated_pdf.head()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": false}, "outputs": [], "source": ["# Convert pandas dataframe to spark dataframe\n", "updated_df = spark.createDataFrame(updated_pdf, schema=df.schema)\n", "\n", "# Write spark dataframe to parquet file\n", "UPDATED_S3_PATH = \"s3a://aktana-bdp-dconovartisau/env=preuat/datatype=suggestionCandidates/rundate=2021-08-04-test/\"\n", "\n", "updated_df.write.parquet(UPDATED_S3_PATH)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}