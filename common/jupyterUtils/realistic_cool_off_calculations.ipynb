{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2024-05-15T22:31:23.261067Z", "start_time": "2024-05-15T22:31:23.257793Z"}}, "source": ["import pandas as pd\n", "from common.pyUtils.athena_reader import AthenaReader\n", "import numpy as np"], "outputs": [], "execution_count": 65}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:16:27.107362Z", "start_time": "2024-05-15T21:16:27.102720Z"}}, "cell_type": "code", "source": "params = {'rds-server': 'qagenentechcadevrds.aktana.com', 'rds-user': 'appadmin', 'rds-password': 'vuXQc0g23BNN9k7I7lmvfGEXYx4jHOQt', 'rds-enginedbname': 'genentechcaprod', 'rds-learningdbname': 'genentechcaprod_learning', 'rds-stagedbname': 'genentechcaprod_stage', 'rds-copystormdbName': 'genentechcaprod_cs', 'rds-gobblindbName': 'genentechcaprod_cs', 'rds-archivedbName': 'genentechcaprod_archive', 'rundeck-project': 'QAGENENTECH-CA-DEV', 'rundeck-server': 'https://ops-bdp-qagenentechca.aktana.com', 'rundeck-token': 'l2QL7KcHQkCcCmikqF7e4NJOq4pAB10W', 'env-sizingName': 'largex', 'env-regionName': 'usqaeks', 'env-countryCode': 'CA', 'adl-rptS3Location': 's3://aktana-bdp-qagenentechca/dev/', 'adl-adlS3Location': 's3://aktana-bdp-qagenentechca/dev/adl/', 'adl-iamRole': 'Datalake-service-role', 'adl-codeRepository': '*****************:aktana/learning.git', 'adl-mappingLocation': 's3://aktana-bdp-qagenentechca/adl/common/', 'adl-loadingLocation': 's3://aktana-bdp-qagenentechca/adl/common/', 'adl-awsRegion': 'us-east-1', 'adl-awsAccessKey': '', 'adl-awsSecretKey': '', 'adl-availabilityZone': '', 'adl-dcoS3Location': 's3://aktana-bdp-qagenentechca/dev/dco/', 'env-customer': 'qagenentechca', 'env-envName': 'dev', 'athena-username': '********************', 'athena-password': '5zpFvCRjrogeTeyW2ORYmSADK4uA/xN5cG02k82R', 'athena-region': 'us-east-1', 'athena-s3bucket': 'aktana-bdpusqaeks-glue', 'athena-stagedir': 'dbt/dev/qagenentechca', 'athena-schema': 'impact_qagenentechca_dev'}", "id": "a443156297005b37", "outputs": [], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:16:29.416617Z", "start_time": "2024-05-15T21:16:29.341935Z"}}, "cell_type": "code", "source": ["# noinspection JupyterPackage\n", "athena_reader = AthenaReader()\n", "athena_reader.connect(aws_access_key=params.get(\"athena-username\"),\n", "                      aws_secret_key=params.get(\"athena-password\"),\n", "                      session_token=\"\", aws_region=params.get(\"athena-region\"),\n", "                      athena_staging_bucket=params.get(\"athena-s3bucket\"),\n", "                      athena_staging_dir=params.get(\"athena-stagedir\"), schema=params.get(\"athena-schema\"))\n"], "id": "f6176e0eb4dacbe6", "outputs": [], "execution_count": 10}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:18:09.859322Z", "start_time": "2024-05-15T21:18:09.856040Z"}}, "cell_type": "code", "source": ["start_date = \"2023-01-01\"\n", "end_date = \"2024-01-31\"\n", "cols = \"*\"\n", "activity_date_filter = f\"activityDate >= date_parse('{start_date}', '%Y-%m-%d') and activityDate < date_parse('{end_date}', '%Y-%m-%d')\"\n", "\n", "query = f\"select {cols} from  DRL_Interaction_History_v where {activity_date_filter}\""], "id": "9079acdfc7e7133c", "outputs": [], "execution_count": 17}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:18:38.769546Z", "start_time": "2024-05-15T21:18:11.992325Z"}}, "cell_type": "code", "source": "df = athena_reader.query(query)", "id": "ad981708c2d30c4b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Executing athena query:select * from  DRL_Interaction_History_v where activityDate >= date_parse('2023-01-01', '%Y-%m-%d') and activityDate < date_parse('2024-01-31', '%Y-%m-%d')\n", "Time to complete query: 21.803586959838867s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/code/learning/learning/common/pyUtils/athena_reader.py:75: DtypeWarning: Columns (30,40) have mixed types.Specify dtype option on import or set low_memory=False.\n", "  df_data = self.download_and_load_query_results(response)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Data fetched in 26.77424383163452s\n"]}], "execution_count": 18}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:19:13.216909Z", "start_time": "2024-05-15T21:19:13.199291Z"}}, "cell_type": "code", "source": "df.head()", "id": "dddf8546c0f2b346", "outputs": [{"data": {"text/plain": ["   accountId          accountUid  productId productName          productUid  \\\n", "0     163158  0015f00000Uboa2AAB       1119     Evrysdi  a005f000005YT7kAAG   \n", "1     203083  0015f00000Ukd8oAAB       1119     Evrysdi  a005f000005YT7kAAG   \n", "2     210909  0015f00000UkGD3AAN       1119     Evrysdi  a005f000005YT7kAAG   \n", "3     298807  0015f00000UXfo5AAD       1018   Herceptin  a005f000005YT7pAAG   \n", "4     285356  0015f00000UbY38AAF       1119     Evrysdi  a005f000005YT7kAAG   \n", "\n", "   repId              repUid           currentChannel activityDate  \\\n", "0   1459  0055f000008J4wjAAC            VISIT_CHANNEL   2023-07-19   \n", "1   1531  0055f000008J4x1AAC  WEB_INTERACTIVE_CHANNEL   2023-01-23   \n", "2   1541  0055f000008J56hAAC  WEB_INTERACTIVE_CHANNEL   2023-09-19   \n", "3   2021  0055f000008J5F3AAK  WEB_INTERACTIVE_CHANNEL   2023-10-16   \n", "4   1541  0055f000008J56hAAC  WEB_INTERACTIVE_CHANNEL   2023-08-30   \n", "\n", "   interactionId  ...  reptype_akt  cur1y_interactioncount_akt  \\\n", "0         472335  ...          NaN                         NaN   \n", "1         244340  ...          NaN                         NaN   \n", "2         562940  ...          NaN                         NaN   \n", "3         630437  ...          NaN                         NaN   \n", "4         534421  ...          NaN                         NaN   \n", "\n", "   cur2y_interactioncount_akt cur6m_interactioncount_akt  mypriority_akt  \\\n", "0                         NaN                        NaN             NaN   \n", "1                         NaN                        NaN             NaN   \n", "2                         NaN                        NaN             NaN   \n", "3                         NaN                        NaN             NaN   \n", "4                         NaN                        NaN             NaN   \n", "\n", "   nba_stakeholder_akt repfieldforce_akt ismytarget_std_akt chunk_date_value  \\\n", "0                  NaN               NaN                NaN              NaN   \n", "1                  NaN               NaN                NaN              NaN   \n", "2                  NaN               NaN                NaN              NaN   \n", "3                  NaN               NaN                NaN              NaN   \n", "4                  NaN               NaN                NaN              NaN   \n", "\n", "  ismytarget_rpt  \n", "0            NaN  \n", "1            NaN  \n", "2            NaN  \n", "3            NaN  \n", "4            NaN  \n", "\n", "[5 rows x 113 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>accountUid</th>\n", "      <th>productId</th>\n", "      <th>productName</th>\n", "      <th>productUid</th>\n", "      <th>repId</th>\n", "      <th>repUid</th>\n", "      <th>currentChannel</th>\n", "      <th>activityDate</th>\n", "      <th>interactionId</th>\n", "      <th>...</th>\n", "      <th>reptype_akt</th>\n", "      <th>cur1y_interactioncount_akt</th>\n", "      <th>cur2y_interactioncount_akt</th>\n", "      <th>cur6m_interactioncount_akt</th>\n", "      <th>mypriority_akt</th>\n", "      <th>nba_stakeholder_akt</th>\n", "      <th>repfieldforce_akt</th>\n", "      <th>ismytarget_std_akt</th>\n", "      <th>chunk_date_value</th>\n", "      <th>ismytarget_rpt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>163158</td>\n", "      <td>0015f00000Uboa2AAB</td>\n", "      <td>1119</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>a005f000005YT7kAAG</td>\n", "      <td>1459</td>\n", "      <td>0055f000008J4wjAAC</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>2023-07-19</td>\n", "      <td>472335</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>203083</td>\n", "      <td>0015f00000Ukd8oAAB</td>\n", "      <td>1119</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>a005f000005YT7kAAG</td>\n", "      <td>1531</td>\n", "      <td>0055f000008J4x1AAC</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>2023-01-23</td>\n", "      <td>244340</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>210909</td>\n", "      <td>0015f00000UkGD3AAN</td>\n", "      <td>1119</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>a005f000005YT7kAAG</td>\n", "      <td>1541</td>\n", "      <td>0055f000008J56hAAC</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>2023-09-19</td>\n", "      <td>562940</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>298807</td>\n", "      <td>0015f00000UXfo5AAD</td>\n", "      <td>1018</td>\n", "      <td>Her<PERSON>ptin</td>\n", "      <td>a005f000005YT7pAAG</td>\n", "      <td>2021</td>\n", "      <td>0055f000008J5F3AAK</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>2023-10-16</td>\n", "      <td>630437</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>285356</td>\n", "      <td>0015f00000UbY38AAF</td>\n", "      <td>1119</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>a005f000005YT7kAAG</td>\n", "      <td>1541</td>\n", "      <td>0055f000008J56hAAC</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>2023-08-30</td>\n", "      <td>534421</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 113 columns</p>\n", "</div>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "execution_count": 19}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:19:17.759648Z", "start_time": "2024-05-15T21:19:17.755389Z"}}, "cell_type": "code", "source": "df.shape", "id": "4acf436a7af240bf", "outputs": [{"data": {"text/plain": ["(318604, 113)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "execution_count": 20}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:21:19.991930Z", "start_time": "2024-05-15T21:21:19.986915Z"}}, "cell_type": "code", "source": "list(df.columns)", "id": "3197594d15ad4b5e", "outputs": [{"data": {"text/plain": ["['accountId',\n", " 'accountUid',\n", " 'productId',\n", " 'productName',\n", " 'productUid',\n", " 'repId',\n", " 'repUid',\n", " 'currentChannel',\n", " 'activityDate',\n", " 'interactionId',\n", " 'cms_messageuid',\n", " 'tag',\n", " 'document_type',\n", " 'externalid',\n", " 'accounttypeid',\n", " 'facilityid',\n", " 'accountname',\n", " 'configcountrycode',\n", " 'isdeleted',\n", " 'claimsdatamonth_akt',\n", " 'cur1wsalesweeklydate_akt',\n", " 'cur6mcallcompleted_akt',\n", " 'cur6mcallcompleted_bem_akt',\n", " 'cur6mcallcompleted_cs_akt',\n", " 'cur6mcallcompleted_frm_akt',\n", " 'cur6mcallcompleted_hd_akt',\n", " 'cur6mcallcompleted_ifs_akt',\n", " 'cur6mcallcompleted_med_akt',\n", " 'cur6mcallcompleted_msl_akt',\n", " 'cur6mcallcompleted_nel_akt',\n", " 'department_std_akt',\n", " 'donotcall_akt',\n", " 'emailcaptured_std_akt',\n", " 'facilitybeds_std_akt',\n", " 'ispersonaccount_std_akt',\n", " 'kaiserflag_akt',\n", " 'levelhco_std_akt',\n", " 'pdrpoptout_std_akt',\n", " 'personalemail_std_akt',\n", " 'professionalemail_std_akt',\n", " 'profileconsent_std_akt',\n", " 'salesdatamonth_akt',\n", " 'specialties_std_akt',\n", " 'specialties2_std_akt',\n", " 'squadpriority_akt',\n", " 'title_std_akt',\n", " 'yearsincegraduation_std_akt',\n", " 'squadtarget_akt',\n", " 'accountnamelang_std_akt',\n", " 'accountnamealphabet_std_akt',\n", " 'accountname_akt',\n", " 'gnecustomemail_akt',\n", " 'claimsdatayear_akt',\n", " 'gnespecialty1_akt',\n", " 'gnespecialty2_akt',\n", " 'gnespecialty3_akt',\n", " 'salesdatayear_akt',\n", " 'cur12m_vab_nosamplesale_akt',\n", " 'crossixexpiration_akt',\n", " 'enspsamplenosale_akt',\n", " 'gneaddress_akt',\n", " 'mysqlconnectortesting_akt',\n", " 'adoptionlevel_std_akt',\n", " 'adoptionobjective_std_akt',\n", " 'advocacy_std_akt',\n", " 'awareness_std_akt',\n", " 'behavior_std_akt',\n", " 'creditstatus_akt',\n", " 'decile_std_akt',\n", " 'hcppriority_std_akt',\n", " 'hcpsegment_std_akt',\n", " 'hcptier_std_akt',\n", " 'hospitalbudget_std_akt',\n", " 'isproducttargeted_std_akt',\n", " 'journeystage_std_akt',\n", " 'potential_std_akt',\n", " 'priority_std_akt',\n", " 'proposedsegment_std_akt',\n", " 'protocolposition_std_akt',\n", " 'quintile_std_akt',\n", " 'rating_std_akt',\n", " 'readiness_std_akt',\n", " 'scaledpriorityscore_akt',\n", " 'segment_std_akt',\n", " 'sellingstage_std_akt',\n", " 'sendscompleted_dse_akt',\n", " 'sendsgoal_dse_akt',\n", " 'sendsremaining_dse_akt',\n", " 'sentiment_std_akt',\n", " 'speakerskills_std_akt',\n", " 'tier_std_akt',\n", " 'visitdetailscompleted_dse_akt',\n", " 'visitdetailsgoal_dse_akt',\n", " 'visitdetailsremaining_dse_akt',\n", " 'webedetailscompleted_dse_akt',\n", " 'webedetailsgoal_dse_akt',\n", " 'webedetailsremaining_dse_akt',\n", " 'cur3mo_natl_pen_avg_akt',\n", " 'cur3mo_aa_npc_akt',\n", " 'personadesc_akt',\n", " 'personasubsegment_akt',\n", " 'startdate',\n", " 'enddate',\n", " 'reptype_akt',\n", " 'cur1y_interactioncount_akt',\n", " 'cur2y_interactioncount_akt',\n", " 'cur6m_interactioncount_akt',\n", " 'mypriority_akt',\n", " 'nba_stakeholder_akt',\n", " 'repfieldforce_akt',\n", " 'ismytarget_std_akt',\n", " 'chunk_date_value',\n", " 'ismytarget_rpt']"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "execution_count": 22}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:32:49.778911Z", "start_time": "2024-05-15T21:32:49.775969Z"}}, "cell_type": "code", "source": "segment_csv_file_path = \"~/code/db/qa_genentechca_dev/CJ_Segment_output_1.csv\"", "id": "4992a7daea9bb886", "outputs": [], "execution_count": 25}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:33:58.779633Z", "start_time": "2024-05-15T21:33:55.867181Z"}}, "cell_type": "code", "source": "hcp_segment_df = pd.read_csv(segment_csv_file_path)", "id": "47bd95d3ed9bc7b9", "outputs": [], "execution_count": 32}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:34:11.897447Z", "start_time": "2024-05-15T21:34:11.874196Z"}}, "cell_type": "code", "source": "hcp_segment_df['isCurrent'].value_counts()", "id": "56132a625df91777", "outputs": [{"data": {"text/plain": ["0    3202441\n", "1     291131\n", "Name: isCurrent, dtype: int64"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "execution_count": 33}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:34:24.784503Z", "start_time": "2024-05-15T21:34:24.701404Z"}}, "cell_type": "code", "source": "hcp_segment_df = hcp_segment_df.loc[hcp_segment_df['isCurrent'] == 1, :]", "id": "246c1f46bf2ac4e7", "outputs": [], "execution_count": 34}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:34:25.524865Z", "start_time": "2024-05-15T21:34:25.519869Z"}}, "cell_type": "code", "source": "hcp_segment_df.shape", "id": "dbd7f81921207fb3", "outputs": [{"data": {"text/plain": ["(291131, 14)"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "execution_count": 35}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:39:21.239988Z", "start_time": "2024-05-15T21:39:21.217900Z"}}, "cell_type": "code", "source": "hcp_segment_subset_df = hcp_segment_df.loc[hcp_segment_df['segmentType'] == 'Combined', ['accountId', 'productId', 'segment']]", "id": "d34b30dcb2b947c2", "outputs": [], "execution_count": 41}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-15T21:35:00.775567Z", "start_time": "2024-05-15T21:34:53.684374Z"}}, "cell_type": "code", "source": "df.to_csv(\"~/code/db/qa_genentechca_dev/DRL_Interaction_History_v.csv\", index=False)", "id": "5a9e76e38ed99b4d", "outputs": [], "execution_count": 36}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T00:56:52.809276Z", "start_time": "2024-05-16T00:56:52.798464Z"}}, "cell_type": "code", "source": "interaction_df = df[['accountId', 'accountUid', 'productId', 'currentChannel', 'activityDate',]]", "id": "65013d1e3f6ec8a4", "outputs": [], "execution_count": 89}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T00:56:53.337265Z", "start_time": "2024-05-16T00:56:53.272178Z"}}, "cell_type": "code", "source": "interaction_df = interaction_df.merge(hcp_segment_subset_df, how='inner', on=['accountId', 'productId'])", "id": "dbc1ceb11c3a5866", "outputs": [], "execution_count": 90}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T00:56:53.756917Z", "start_time": "2024-05-16T00:56:53.749184Z"}}, "cell_type": "code", "source": "interaction_df.head()", "id": "7e63a31c3734848e", "outputs": [{"data": {"text/plain": ["   accountId          accountUid  productId           currentChannel  \\\n", "0      34964  0015f00000UcSz6AAF       1011            VISIT_CHANNEL   \n", "1      34964  0015f00000UcSz6AAF       1011             SEND_CHANNEL   \n", "2      34964  0015f00000UcSz6AAF       1011  WEB_INTERACTIVE_CHANNEL   \n", "3      34964  0015f00000UcSz6AAF       1011  WEB_INTERACTIVE_CHANNEL   \n", "4      34964  0015f00000UcSz6AAF       1011  WEB_INTERACTIVE_CHANNEL   \n", "\n", "  activityDate segment  \n", "0   2023-09-12      T5  \n", "1   2023-05-16      T5  \n", "2   2023-03-21      T5  \n", "3   2023-07-07      T5  \n", "4   2023-05-16      T5  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>accountUid</th>\n", "      <th>productId</th>\n", "      <th>currentChannel</th>\n", "      <th>activityDate</th>\n", "      <th>segment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>34964</td>\n", "      <td>0015f00000UcSz6AAF</td>\n", "      <td>1011</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>2023-09-12</td>\n", "      <td>T5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>34964</td>\n", "      <td>0015f00000UcSz6AAF</td>\n", "      <td>1011</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>2023-05-16</td>\n", "      <td>T5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>34964</td>\n", "      <td>0015f00000UcSz6AAF</td>\n", "      <td>1011</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>2023-03-21</td>\n", "      <td>T5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>34964</td>\n", "      <td>0015f00000UcSz6AAF</td>\n", "      <td>1011</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>2023-07-07</td>\n", "      <td>T5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>34964</td>\n", "      <td>0015f00000UcSz6AAF</td>\n", "      <td>1011</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>2023-05-16</td>\n", "      <td>T5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "execution_count": 91}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T00:56:54.363706Z", "start_time": "2024-05-16T00:56:54.353484Z"}}, "cell_type": "code", "source": ["# Convert activityDate to datetime\n", "interaction_df['activityDate'] = pd.to_datetime(interaction_df['activityDate'])"], "id": "7c406d43f8439219", "outputs": [], "execution_count": 92}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T00:56:55.246363Z", "start_time": "2024-05-16T00:56:55.217510Z"}}, "cell_type": "code", "source": ["# For each account find the number of days since last interaction in the every channel\n", "interaction_df = interaction_df.sort_values(by=['accountId', 'activityDate'])\n", "interaction_df['nextChannel'] = interaction_df.groupby('accountId')['currentChannel'].shift(-1)\n", "interaction_df['nextActivityDate'] = interaction_df.groupby('accountId')['activityDate'].shift(-1)\n", "interaction_df = interaction_df.dropna(subset=['nextChannel', 'nextActivityDate'])\n"], "id": "918737777652fa7c", "outputs": [], "execution_count": 93}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T00:56:55.759379Z", "start_time": "2024-05-16T00:56:55.721109Z"}}, "cell_type": "code", "source": ["# Calculate the number of days to the next interaction\n", "interaction_df['daysToNextInteraction'] = (interaction_df['nextActivityDate'] - interaction_df['activityDate']).dt.days\n", "\n", "# Optional: Filtering out zero days, if you want to ignore same-day interactions\n", "interaction_df = interaction_df[interaction_df['daysToNextInteraction'] > 0]\n", "\n", "# Define a function to calculate the desired percentiles\n", "def calculate_percentiles(group):\n", "    percentiles = [25, 30, 40, 50, 75, 90]\n", "    return pd.Series(np.percentile(group['daysToNextInteraction'], percentiles), index=[f'p{p}' for p in percentiles])\n", "\n", "# Group by 'segment', 'currentChannel' and 'nextChannel', then calculate percentiles\n", "result = interaction_df.groupby(['segment', 'currentChannel', 'nextChannel']).apply(calculate_percentiles).reset_index()"], "id": "769bdfab29b9565a", "outputs": [], "execution_count": 94}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T00:56:56.576477Z", "start_time": "2024-05-16T00:56:56.551675Z"}}, "cell_type": "code", "source": "result", "id": "1629519dbcfef241", "outputs": [{"data": {"text/plain": ["   segment           currentChannel              nextChannel    p25   p30  \\\n", "0       T1             SEND_CHANNEL             SEND_CHANNEL  16.00  18.9   \n", "1       T1             SEND_CHANNEL            VISIT_CHANNEL   8.75  13.1   \n", "2       T1             SEND_CHANNEL  WEB_INTERACTIVE_CHANNEL   9.75  12.5   \n", "3       T1            VISIT_CHANNEL             SEND_CHANNEL  10.00  11.4   \n", "4       T1            VISIT_CHANNEL            VISIT_CHANNEL   8.00   9.0   \n", "5       T1            VISIT_CHANNEL  WEB_INTERACTIVE_CHANNEL   8.00  11.0   \n", "6       T1  WEB_INTERACTIVE_CHANNEL             SEND_CHANNEL  12.00  14.0   \n", "7       T1  WEB_INTERACTIVE_CHANNEL            VISIT_CHANNEL   6.00   7.0   \n", "8       T1  WEB_INTERACTIVE_CHANNEL  WEB_INTERACTIVE_CHANNEL   7.00   8.0   \n", "9       T2             SEND_CHANNEL             SEND_CHANNEL  20.00  20.0   \n", "10      T2             SEND_CHANNEL            VISIT_CHANNEL   9.75  10.9   \n", "11      T2             SEND_CHANNEL  WEB_INTERACTIVE_CHANNEL   6.00   6.0   \n", "12      T2            VISIT_CHANNEL             SEND_CHANNEL  11.75  16.0   \n", "13      T2            VISIT_CHANNEL            VISIT_CHANNEL  12.00  14.0   \n", "14      T2            VISIT_CHANNEL  WEB_INTERACTIVE_CHANNEL   8.00   9.0   \n", "15      T2  WEB_INTERACTIVE_CHANNEL             SEND_CHANNEL  13.00  22.0   \n", "16      T2  WEB_INTERACTIVE_CHANNEL            VISIT_CHANNEL   7.00   8.0   \n", "17      T2  WEB_INTERACTIVE_CHANNEL  WEB_INTERACTIVE_CHANNEL   4.00   5.0   \n", "18      T3             SEND_CHANNEL             SEND_CHANNEL  20.00  21.0   \n", "19      T3             SEND_CHANNEL            VISIT_CHANNEL   9.00  11.6   \n", "20      T3             SEND_CHANNEL  WEB_INTERACTIVE_CHANNEL  12.00  13.6   \n", "21      T3            VISIT_CHANNEL             SEND_CHANNEL   8.00   9.0   \n", "22      T3            VISIT_CHANNEL            VISIT_CHANNEL  13.00  14.0   \n", "23      T3            VISIT_CHANNEL  WEB_INTERACTIVE_CHANNEL   8.00  10.0   \n", "24      T3  WEB_INTERACTIVE_CHANNEL             SEND_CHANNEL  12.50  23.7   \n", "25      T3  WEB_INTERACTIVE_CHANNEL            VISIT_CHANNEL   9.00  12.0   \n", "26      T3  WEB_INTERACTIVE_CHANNEL  WEB_INTERACTIVE_CHANNEL   7.00   8.0   \n", "27      T4             SEND_CHANNEL             SEND_CHANNEL  17.00  19.0   \n", "28      T4             SEND_CHANNEL            VISIT_CHANNEL  13.00  17.8   \n", "29      T4             SEND_CHANNEL  WEB_INTERACTIVE_CHANNEL  14.00  14.0   \n", "30      T4            VISIT_CHANNEL             SEND_CHANNEL  17.00  21.0   \n", "31      T4            VISIT_CHANNEL            VISIT_CHANNEL  14.00  15.0   \n", "32      T4            VISIT_CHANNEL  WEB_INTERACTIVE_CHANNEL  11.00  13.0   \n", "33      T4  WEB_INTERACTIVE_CHANNEL             SEND_CHANNEL   8.50  13.0   \n", "34      T4  WEB_INTERACTIVE_CHANNEL            VISIT_CHANNEL   7.00   9.0   \n", "35      T4  WEB_INTERACTIVE_CHANNEL  WEB_INTERACTIVE_CHANNEL   6.00   7.0   \n", "36      T5             SEND_CHANNEL             SEND_CHANNEL  23.50  28.0   \n", "37      T5             SEND_CHANNEL            VISIT_CHANNEL  14.00  19.0   \n", "38      T5             SEND_CHANNEL  WEB_INTERACTIVE_CHANNEL   9.25  14.0   \n", "39      T5            VISIT_CHANNEL             SEND_CHANNEL  21.00  25.0   \n", "40      T5            VISIT_CHANNEL            VISIT_CHANNEL  15.00  19.0   \n", "41      T5            VISIT_CHANNEL  WEB_INTERACTIVE_CHANNEL  13.00  15.0   \n", "42      T5  WEB_INTERACTIVE_CHANNEL             SEND_CHANNEL  15.00  20.0   \n", "43      T5  WEB_INTERACTIVE_CHANNEL            VISIT_CHANNEL   9.00  13.0   \n", "44      T5  WEB_INTERACTIVE_CHANNEL  WEB_INTERACTIVE_CHANNEL   7.00   8.0   \n", "\n", "     p40   p50     p75    p90  \n", "0   21.0  25.5   44.75   67.1  \n", "1   19.0  21.0   41.00   76.3  \n", "2   22.0  25.0   38.75   62.0  \n", "3   17.2  21.0   40.00   95.8  \n", "4   14.0  17.0   35.00   64.0  \n", "5   13.0  19.0   40.25   80.0  \n", "6   15.0  29.0   57.00  168.0  \n", "7   11.0  15.0   31.75   57.5  \n", "8   13.0  18.0   36.00   67.8  \n", "9   23.4  28.0   53.00   96.8  \n", "10  15.0  22.0   55.50  105.0  \n", "11   8.2  12.0   53.00  101.2  \n", "12  21.4  36.0   78.00  109.3  \n", "13  16.0  21.0   42.00   73.9  \n", "14  13.0  18.0   40.00   69.8  \n", "15  38.2  55.0   96.00  132.2  \n", "16  12.8  17.0   38.00   68.2  \n", "17   7.0  11.0   26.00   55.0  \n", "18  26.8  38.0   86.00  162.4  \n", "19  20.8  33.0   53.00  133.0  \n", "20  18.4  26.0   86.00  134.8  \n", "21  13.0  16.0   37.75   95.0  \n", "22  20.6  27.0   49.00   85.0  \n", "23  15.0  22.0   44.00   82.8  \n", "24  49.4  63.0  112.25  211.1  \n", "25  15.0  19.0   40.25   90.7  \n", "26  13.0  16.0   36.00   79.0  \n", "27  26.0  37.0   78.00  165.8  \n", "28  26.6  32.0   61.00  101.2  \n", "29  26.0  34.0   91.00  122.0  \n", "30  28.6  33.0   94.00  163.4  \n", "31  21.0  26.0   47.00   83.8  \n", "32  18.0  23.0   48.00   90.6  \n", "33  26.8  35.0   78.00  162.2  \n", "34  13.0  19.0   36.00   75.0  \n", "35  13.0  18.0   41.00   71.8  \n", "36  34.6  49.0   78.00  116.0  \n", "37  27.0  34.0   69.75  135.0  \n", "38  21.0  28.0   58.00  126.1  \n", "39  34.0  43.0   92.00  159.4  \n", "40  25.0  30.0   57.00  101.0  \n", "41  21.0  29.0   59.00  113.0  \n", "42  31.6  36.5   78.50  147.1  \n", "43  17.0  25.0   49.00   96.0  \n", "44  14.0  22.0   54.75  108.0  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>segment</th>\n", "      <th>currentChannel</th>\n", "      <th>nextChannel</th>\n", "      <th>p25</th>\n", "      <th>p30</th>\n", "      <th>p40</th>\n", "      <th>p50</th>\n", "      <th>p75</th>\n", "      <th>p90</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>T1</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>16.00</td>\n", "      <td>18.9</td>\n", "      <td>21.0</td>\n", "      <td>25.5</td>\n", "      <td>44.75</td>\n", "      <td>67.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>T1</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>8.75</td>\n", "      <td>13.1</td>\n", "      <td>19.0</td>\n", "      <td>21.0</td>\n", "      <td>41.00</td>\n", "      <td>76.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>T1</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>9.75</td>\n", "      <td>12.5</td>\n", "      <td>22.0</td>\n", "      <td>25.0</td>\n", "      <td>38.75</td>\n", "      <td>62.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>T1</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>10.00</td>\n", "      <td>11.4</td>\n", "      <td>17.2</td>\n", "      <td>21.0</td>\n", "      <td>40.00</td>\n", "      <td>95.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>T1</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>8.00</td>\n", "      <td>9.0</td>\n", "      <td>14.0</td>\n", "      <td>17.0</td>\n", "      <td>35.00</td>\n", "      <td>64.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>T1</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>8.00</td>\n", "      <td>11.0</td>\n", "      <td>13.0</td>\n", "      <td>19.0</td>\n", "      <td>40.25</td>\n", "      <td>80.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>T1</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>12.00</td>\n", "      <td>14.0</td>\n", "      <td>15.0</td>\n", "      <td>29.0</td>\n", "      <td>57.00</td>\n", "      <td>168.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>T1</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>6.00</td>\n", "      <td>7.0</td>\n", "      <td>11.0</td>\n", "      <td>15.0</td>\n", "      <td>31.75</td>\n", "      <td>57.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>T1</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>7.00</td>\n", "      <td>8.0</td>\n", "      <td>13.0</td>\n", "      <td>18.0</td>\n", "      <td>36.00</td>\n", "      <td>67.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>T2</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>20.00</td>\n", "      <td>20.0</td>\n", "      <td>23.4</td>\n", "      <td>28.0</td>\n", "      <td>53.00</td>\n", "      <td>96.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>T2</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>9.75</td>\n", "      <td>10.9</td>\n", "      <td>15.0</td>\n", "      <td>22.0</td>\n", "      <td>55.50</td>\n", "      <td>105.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>T2</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>6.00</td>\n", "      <td>6.0</td>\n", "      <td>8.2</td>\n", "      <td>12.0</td>\n", "      <td>53.00</td>\n", "      <td>101.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>T2</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>11.75</td>\n", "      <td>16.0</td>\n", "      <td>21.4</td>\n", "      <td>36.0</td>\n", "      <td>78.00</td>\n", "      <td>109.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>T2</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>12.00</td>\n", "      <td>14.0</td>\n", "      <td>16.0</td>\n", "      <td>21.0</td>\n", "      <td>42.00</td>\n", "      <td>73.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>T2</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>8.00</td>\n", "      <td>9.0</td>\n", "      <td>13.0</td>\n", "      <td>18.0</td>\n", "      <td>40.00</td>\n", "      <td>69.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>T2</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>13.00</td>\n", "      <td>22.0</td>\n", "      <td>38.2</td>\n", "      <td>55.0</td>\n", "      <td>96.00</td>\n", "      <td>132.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>T2</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>7.00</td>\n", "      <td>8.0</td>\n", "      <td>12.8</td>\n", "      <td>17.0</td>\n", "      <td>38.00</td>\n", "      <td>68.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>T2</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>4.00</td>\n", "      <td>5.0</td>\n", "      <td>7.0</td>\n", "      <td>11.0</td>\n", "      <td>26.00</td>\n", "      <td>55.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>T3</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>20.00</td>\n", "      <td>21.0</td>\n", "      <td>26.8</td>\n", "      <td>38.0</td>\n", "      <td>86.00</td>\n", "      <td>162.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>T3</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>9.00</td>\n", "      <td>11.6</td>\n", "      <td>20.8</td>\n", "      <td>33.0</td>\n", "      <td>53.00</td>\n", "      <td>133.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>T3</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>12.00</td>\n", "      <td>13.6</td>\n", "      <td>18.4</td>\n", "      <td>26.0</td>\n", "      <td>86.00</td>\n", "      <td>134.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>T3</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>8.00</td>\n", "      <td>9.0</td>\n", "      <td>13.0</td>\n", "      <td>16.0</td>\n", "      <td>37.75</td>\n", "      <td>95.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>T3</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>13.00</td>\n", "      <td>14.0</td>\n", "      <td>20.6</td>\n", "      <td>27.0</td>\n", "      <td>49.00</td>\n", "      <td>85.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>T3</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>8.00</td>\n", "      <td>10.0</td>\n", "      <td>15.0</td>\n", "      <td>22.0</td>\n", "      <td>44.00</td>\n", "      <td>82.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>T3</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>12.50</td>\n", "      <td>23.7</td>\n", "      <td>49.4</td>\n", "      <td>63.0</td>\n", "      <td>112.25</td>\n", "      <td>211.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>T3</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>9.00</td>\n", "      <td>12.0</td>\n", "      <td>15.0</td>\n", "      <td>19.0</td>\n", "      <td>40.25</td>\n", "      <td>90.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>T3</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>7.00</td>\n", "      <td>8.0</td>\n", "      <td>13.0</td>\n", "      <td>16.0</td>\n", "      <td>36.00</td>\n", "      <td>79.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>T4</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>17.00</td>\n", "      <td>19.0</td>\n", "      <td>26.0</td>\n", "      <td>37.0</td>\n", "      <td>78.00</td>\n", "      <td>165.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>T4</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>13.00</td>\n", "      <td>17.8</td>\n", "      <td>26.6</td>\n", "      <td>32.0</td>\n", "      <td>61.00</td>\n", "      <td>101.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>T4</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>14.00</td>\n", "      <td>14.0</td>\n", "      <td>26.0</td>\n", "      <td>34.0</td>\n", "      <td>91.00</td>\n", "      <td>122.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>T4</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>17.00</td>\n", "      <td>21.0</td>\n", "      <td>28.6</td>\n", "      <td>33.0</td>\n", "      <td>94.00</td>\n", "      <td>163.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>T4</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>14.00</td>\n", "      <td>15.0</td>\n", "      <td>21.0</td>\n", "      <td>26.0</td>\n", "      <td>47.00</td>\n", "      <td>83.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>T4</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>11.00</td>\n", "      <td>13.0</td>\n", "      <td>18.0</td>\n", "      <td>23.0</td>\n", "      <td>48.00</td>\n", "      <td>90.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>T4</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>8.50</td>\n", "      <td>13.0</td>\n", "      <td>26.8</td>\n", "      <td>35.0</td>\n", "      <td>78.00</td>\n", "      <td>162.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>T4</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>7.00</td>\n", "      <td>9.0</td>\n", "      <td>13.0</td>\n", "      <td>19.0</td>\n", "      <td>36.00</td>\n", "      <td>75.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>T4</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>6.00</td>\n", "      <td>7.0</td>\n", "      <td>13.0</td>\n", "      <td>18.0</td>\n", "      <td>41.00</td>\n", "      <td>71.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>T5</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>23.50</td>\n", "      <td>28.0</td>\n", "      <td>34.6</td>\n", "      <td>49.0</td>\n", "      <td>78.00</td>\n", "      <td>116.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>T5</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>14.00</td>\n", "      <td>19.0</td>\n", "      <td>27.0</td>\n", "      <td>34.0</td>\n", "      <td>69.75</td>\n", "      <td>135.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>T5</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>9.25</td>\n", "      <td>14.0</td>\n", "      <td>21.0</td>\n", "      <td>28.0</td>\n", "      <td>58.00</td>\n", "      <td>126.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>T5</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>21.00</td>\n", "      <td>25.0</td>\n", "      <td>34.0</td>\n", "      <td>43.0</td>\n", "      <td>92.00</td>\n", "      <td>159.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>T5</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>15.00</td>\n", "      <td>19.0</td>\n", "      <td>25.0</td>\n", "      <td>30.0</td>\n", "      <td>57.00</td>\n", "      <td>101.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>T5</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>13.00</td>\n", "      <td>15.0</td>\n", "      <td>21.0</td>\n", "      <td>29.0</td>\n", "      <td>59.00</td>\n", "      <td>113.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>T5</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>15.00</td>\n", "      <td>20.0</td>\n", "      <td>31.6</td>\n", "      <td>36.5</td>\n", "      <td>78.50</td>\n", "      <td>147.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>T5</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>9.00</td>\n", "      <td>13.0</td>\n", "      <td>17.0</td>\n", "      <td>25.0</td>\n", "      <td>49.00</td>\n", "      <td>96.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>T5</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>7.00</td>\n", "      <td>8.0</td>\n", "      <td>14.0</td>\n", "      <td>22.0</td>\n", "      <td>54.75</td>\n", "      <td>108.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "execution_count": 95}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T13:32:42.369870Z", "start_time": "2024-05-16T13:32:42.152801Z"}}, "cell_type": "code", "source": ["# Plot mean of p30 across segments using seaborn\n", "import seaborn as sns\n", "sns.set_theme(style=\"white\")\n", "sns.lineplot(data=result, x='segment', y='p30')"], "id": "80a899dece7b184d", "outputs": [{"data": {"text/plain": ["<AxesSubplot:xlabel='segment', ylabel='p30'>"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 112}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T00:58:53.841465Z", "start_time": "2024-05-16T00:58:53.837128Z"}}, "cell_type": "code", "source": ["# Convert p30 to int \n", "final_result = result[['segment', 'currentChannel', 'nextChannel', 'p30']]\n", "final_result['p30'] = final_result['p30'].astype(int)"], "id": "98d5ac68afa1e4e4", "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/1q/p4d_phrx1jz4xn05923ss7tr0000gr/T/ipykernel_3663/3016853768.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  final_result['p30'] = final_result['p30'].astype(int)\n"]}], "execution_count": 97}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T00:59:06.229695Z", "start_time": "2024-05-16T00:59:06.221006Z"}}, "cell_type": "code", "source": "final_result.describe()", "id": "38b79c04f69330d3", "outputs": [{"data": {"text/plain": ["             p30\n", "count  45.000000\n", "mean   13.822222\n", "std     5.394816\n", "min     5.000000\n", "25%     9.000000\n", "50%    13.000000\n", "75%    18.000000\n", "max    28.000000"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>p30</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>45.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>13.822222</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>5.394816</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>5.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>9.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>13.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>18.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>28.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "execution_count": 99}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T01:01:23.837852Z", "start_time": "2024-05-16T01:01:23.830017Z"}}, "cell_type": "code", "source": "final_result.head()", "id": "b7524a54e2549dd0", "outputs": [{"data": {"text/plain": ["  segment currentChannel              nextChannel  p30\n", "0      T1   SEND_CHANNEL             SEND_CHANNEL   18\n", "1      T1   SEND_CHANNEL            VISIT_CHANNEL   13\n", "2      T1   SEND_CHANNEL  WEB_INTERACTIVE_CHANNEL   12\n", "3      T1  VISIT_CHANNEL             SEND_CHANNEL   11\n", "4      T1  VISIT_CHANNEL            VISIT_CHANNEL    9"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>segment</th>\n", "      <th>currentChannel</th>\n", "      <th>nextChannel</th>\n", "      <th>p30</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>T1</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>T1</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>T1</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>T1</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>T1</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "execution_count": 100}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T01:01:56.542289Z", "start_time": "2024-05-16T01:01:56.535362Z"}}, "cell_type": "code", "source": "final_result.rename(columns={'segment':'hcpSegment','p30': 'days'}, inplace=True)", "id": "6921c19a44c069fb", "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/opt/anaconda3/envs/ak_drl_py39/lib/python3.9/site-packages/pandas/core/frame.py:5039: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  return super().rename(\n"]}], "execution_count": 101}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T01:02:00.653595Z", "start_time": "2024-05-16T01:02:00.646955Z"}}, "cell_type": "code", "source": "final_result.head()", "id": "a900907e4eb057fa", "outputs": [{"data": {"text/plain": ["  hcpSegment currentChannel              nextChannel  days\n", "0         T1   SEND_CHANNEL             SEND_CHANNEL    18\n", "1         T1   SEND_CHANNEL            VISIT_CHANNEL    13\n", "2         T1   SEND_CHANNEL  WEB_INTERACTIVE_CHANNEL    12\n", "3         T1  VISIT_CHANNEL             SEND_CHANNEL    11\n", "4         T1  VISIT_CHANNEL            VISIT_CHANNEL     9"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>hcpSegment</th>\n", "      <th>currentChannel</th>\n", "      <th>nextChannel</th>\n", "      <th>days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>T1</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>T1</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>T1</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>T1</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>T1</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "execution_count": 102}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T01:02:10.150484Z", "start_time": "2024-05-16T01:02:10.145489Z"}}, "cell_type": "code", "source": "final_result.to_csv(\"~/code/db/qa_genentechca_dev/realistic_cool_off_calculations.csv\", index=False)", "id": "9c2718f68f69cdc1", "outputs": [], "execution_count": 103}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-16T02:34:11.949285Z", "start_time": "2024-05-16T02:34:11.940498Z"}}, "cell_type": "code", "source": "final_result.loc[final_result['hcpSegment'] == 'T4', :]", "id": "458539ac94cdf834", "outputs": [{"data": {"text/plain": ["   hcpSegment           currentChannel              nextChannel  days\n", "27         T4             SEND_CHANNEL             SEND_CHANNEL    19\n", "28         T4             SEND_CHANNEL            VISIT_CHANNEL    17\n", "29         T4             SEND_CHANNEL  WEB_INTERACTIVE_CHANNEL    14\n", "30         T4            VISIT_CHANNEL             SEND_CHANNEL    21\n", "31         T4            VISIT_CHANNEL            VISIT_CHANNEL    15\n", "32         T4            VISIT_CHANNEL  WEB_INTERACTIVE_CHANNEL    13\n", "33         T4  WEB_INTERACTIVE_CHANNEL             SEND_CHANNEL    13\n", "34         T4  WEB_INTERACTIVE_CHANNEL            VISIT_CHANNEL     9\n", "35         T4  WEB_INTERACTIVE_CHANNEL  WEB_INTERACTIVE_CHANNEL     7"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>hcpSegment</th>\n", "      <th>currentChannel</th>\n", "      <th>nextChannel</th>\n", "      <th>days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>T4</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>T4</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>T4</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>T4</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>T4</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>T4</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>T4</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>SEND_CHANNEL</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>T4</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>VISIT_CHANNEL</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>T4</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>WEB_INTERACTIVE_CHANNEL</td>\n", "      <td>7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "execution_count": 108}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "16fa204e33216e68"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}