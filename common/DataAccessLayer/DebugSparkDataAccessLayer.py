import configparser
import uuid
from common.DataAccessLayer.DatabaseConfig import DatabaseConfig
from common.DataAccessLayer.DataAccessLayer import MessageAccessor
from common.DataAccessLayer.DataAccessLayer import DSEAccessor


import time
import datetime
import logging
import os
from pyspark.sql import SQLContext
from pyspark import SparkContext
from sqlalchemy import create_engine
import pandas as pd
from pysparkling import *
from pyspark.sql.functions import col, when

sqlContext = None
logger = None
PARTITION_COUNT = 100

account_cols = None
account_prod_cols = None

def initialize(logger_name, spark):
    """
    This function initializes the logger for the program
    :param simulation_logger_name:
    :return:
    """
    global logger
    global sqlContext

    logger = logging.getLogger(logger_name)

    sqlContext = SQLContext(spark)


class CommonDbAccessor:


    def write_pandas_dataframe(self, pandas_df, database, table_name, add_time_stamp = True, if_exists="append"):
        """

        :param pandas_df:
        :param database:
        :param table_name:
        :return:
        """
        # If add time stamp then add createdAt and updatedAt
        if add_time_stamp:
            # Add updated at and created at columns in the data frame
            ts = time.time()
            timestamp = datetime.datetime.fromtimestamp(ts).strftime('%Y-%m-%d %H:%M:%S')
            pandas_df['createdAt'] = timestamp
            pandas_df['updatedAt'] = timestamp

        # Get database config instance
        db_config = DatabaseConfig.instance()

        # Read the required MySQL configuration
        db_host = db_config.host
        db_user = db_config.user
        db_password = db_config.password
        db_name = database
        db_port = db_config.port

        # Generate Connection String
        connection_string = "mysql+pymysql://{user}:{pw}@{host}:{port}/{db}"
        connection_string = connection_string.format(user=db_user, pw=db_password, host=db_host,
                                                     port=db_port, db=db_name)
        # Create engine
        engine = create_engine(connection_string, pool_size=10, max_overflow=20)

        pandas_df.to_sql(con=engine, name=table_name, if_exists=if_exists, index=False, chunksize=10000)


class SparkLearningAccessor:
    """
    This function is responsible for spark learning database access
    """

    def get_account_table(self):
        """
        This function returns spark dataframe as all account table.
        :return:
        """
        global account_cols

        dbconfig = DatabaseConfig.instance()
        hostname = dbconfig.host
        dbname = dbconfig.dse_db_name
        jdbcPort = dbconfig.port
        username = dbconfig.user
        password = dbconfig.password


        jdbc_url = "jdbc:mysql://{0}:{1}/{2}?user={3}&password={4}".format(hostname, jdbcPort, dbname, username,
                                                                           password)
        table_name = "(SELECT * FROM {DB_NAME}.Account LIMIT 1000) as dbtable".format(DB_NAME=dbname)

        account_df = sqlContext.read.format('jdbc').options(driver='com.mysql.jdbc.Driver', url=jdbc_url, dbtable=table_name,
                                                            numPartitions=PARTITION_COUNT).load()

        account_cols = account_df.columns

        return account_df

    def get_account_product_table(self, product_uid):
        """
        This function returns the account product table subset for the specified product.
        :param product_uid:
        :return:
        """
        global account_prod_cols

        dbconfig = DatabaseConfig.instance()
        hostname = dbconfig.host
        dbname = dbconfig.dse_db_name
        jdbcPort = dbconfig.port
        username = dbconfig.user
        password = dbconfig.password
        jdbc_url = "jdbc:mysql://{0}:{1}/{2}?user={3}&password={4}".format(hostname, jdbcPort, dbname, username,
                                                                           password)
        message_accessor = MessageAccessor()
        product_id = message_accessor.get_product_id_for_uid(product_uid)
        query = "(SELECT * FROM {DB_NAME}.AccountProduct WHERE productId={PRODUCT_ID} LIMIT 1000) AS dbtable".format(DB_NAME=dbname, PRODUCT_ID=product_id)

        account_product_df = sqlContext.read.format('jdbc').options(driver='com.mysql.jdbc.Driver', url=jdbc_url,
                                                                    dbtable=query,
                                                                    numPartitions=PARTITION_COUNT).load()

        account_prod_cols = account_product_df.columns

        return account_product_df

    def get_target_column(self, product_uid, goal):
        """

        :param product_uid:
        :return:
        """
        dbconfig = DatabaseConfig.instance()

        hostname = dbconfig.host
        dbname = dbconfig.dse_db_name
        jdbcPort = dbconfig.port
        username = dbconfig.user
        password = dbconfig.password
        jdbc_url = "jdbc:mysql://{0}:{1}/{2}?user={3}&password={4}".format(hostname, jdbcPort, dbname, username,
                                                                           password)

        message_accessor = MessageAccessor()
        dse_accessor = DSEAccessor()
        product_id = message_accessor.get_product_id_for_uid(product_uid)
        event_type_id = dse_accessor.get_event_type_id_for_goal(goal)
        query = "(SELECT accountId, 1 as 'TARGET' FROM {DB_NAME}.Event WHERE productId={PRODUCT_ID} " \
                "AND eventTypeId='{EVENT_TYPE_ID}' LIMIT 1000) AS dbtable".format(DB_NAME=dbname,
                                                                                PRODUCT_ID=product_id,
                                                                                EVENT_TYPE_ID=event_type_id)

        account_target_df = sqlContext.read.format('jdbc').options(driver='com.mysql.jdbc.Driver', url=jdbc_url,
                                                                    dbtable=query,
                                                                    numPartitions=PARTITION_COUNT).load()

        return account_target_df

    def write_variable_importance(self, variable_importance_df, goal, channel, product_uid):
        """

        :param variable_importance:
        :return:
        """

        variable_importance_df = variable_importance_df.drop(columns=['relative_importance', 'percentage'])
        variable_importance_df.rename(columns={'variable': 'predictor', 'scaled_importance': 'probability'}, inplace=True)
        variable_importance_df['productUID'] = product_uid
        variable_importance_df['channelUID'] = channel
        variable_importance_df['goal'] = goal

        # Update the source of the predictor based on Account or AccountProduct table
        variable_importance_df.loc[variable_importance_df['predictor'].isin(account_cols),'source'] = "ACCOUNT"
        variable_importance_df.loc[variable_importance_df['predictor'].isin(account_prod_cols), 'source'] = "ACCOUNT_PRODUCT"

        db_config = DatabaseConfig.instance()
        learning_db_name = db_config.learning_db_name

        common_db_accessor = CommonDbAccessor()
        common_db_accessor.write_pandas_dataframe(variable_importance_df, learning_db_name,
                                                  "MessageSequencePredictorInfo")

    def write_optimal_learning_params(self, top_predictor_str, goal, channel, product_uid):
        """

        :param top_predictor_str:
        :param goal:
        :param channel:
        :param product_uid:
        :return:
        """
        data = {'paramName':['LE_MS_addPredictorsFromAccountProduct', 'LE_MS_includeVisitChannel',
                             'LE_MS_removeMessageClicks', 'LE_MS_removeMessageOpens', 'LE_MS_removeMessageSends'],
                'paramValue':[top_predictor_str, '0','0','0','0']}


        optimal_params_df = pd.DataFrame(data=data)
        optimal_params_df["channelUID"] = channel
        optimal_params_df["goal"] = goal
        optimal_params_df["productUID"] = product_uid

        db_config = DatabaseConfig.instance()
        learning_db_name = db_config.learning_db_name

        common_db_accessor = CommonDbAccessor()
        common_db_accessor.write_pandas_dataframe(optimal_params_df, learning_db_name,
                                                  "OptimalLearningParams")
