# TO-TEST

# 1. Check the dbconfig.ini file is present and has correct information.


# -TEST- SegmentAccessor Class #

# # Import SegmentAccessor class in DataAccessLayer
# from DataAccessLayer import SegmentAccessor
#
# # Create Segment Accessor Object
# segmentAccessor = SegmentAccessor()
#
# # Call the method to get Account Ids
# accountIDs = segmentAccessor.getAccountIDsInSegemnt('email_akt', 'yes')
# print(accountIDs)
#
# # Call the method to get Account Information
# accountInformation = segmentAccessor.getAccountDataTableInSegement('email_akt', 'yes')
# print(accountInformation)

from common.DataAccessLayer.DataAccessLayer import MessageAccessor
messageAccessor = MessageAccessor()
hash = messageAccessor.get_hash_for_message_sequence(['a3RA0000000e0taBB', 'a3RA00033000e0taC', 'a3RA0787600e0taBB'])
print("MessageHash : ")
print(hash)