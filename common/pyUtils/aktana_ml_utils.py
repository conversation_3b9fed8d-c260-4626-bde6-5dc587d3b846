import os
import sys
import getopt

import json
import mysql.connector
from mysql.connector import Error

class aktana_ml_utils:

    """
    PURPOSE:
        This is an utility class for ML modules at Aktana that use Databricks, Snowflake, ADL
    """

    def __init__(self):

        """
        PURPOSE:
            This does any required initialization steps.
        """

        self.cmdline_params = {}
        self.metadata_params = {}
        self.snowflake_params = {}
        self.adl_params = {}
        self.databricks_params = {}
        self.knime_params = {}
        self.env_params = {}
        self.api_secret = ""
        self.shortopts = ""
        self.longopts = ""


    # -- (> ---------------------------- SECTION=initialize ------------------------
    def initialize(self, argv, shortopts, longopts, testMode=False):

        """
        PURPOSE:
            Read appropriate metadata for the client
                * Parse command-line to read cust/env/region
                * Read Snowflake config for cust/env
                * Read Databricks config for cust/env
                * Read ADL config for cust/env
        """

        # if it is already initialized, no need to read again
        if len(self.cmdline_params) > 0:
            return

        # Get the customer, env and region from the command line.
        self.get_cmdline_params3(argv, shortopts, longopts)

        # Parameter added to enable testing of the driver program's signature without actually doing the work
        if testMode:
            print("Test mode.  Exiting...", file=sys.stderr)
            return (self.cmdline_params, None)

        # Read metadata db info from config json
        self.get_metadata_params()

        return (self.cmdline_params, self.metadata_params)

    # -- <) ---------------------------- END_SECTION=initialize --------------------

    def get_cmdline_params(self):
        return self.cmdline_params

    def get_cmdline_params3(self, argv, shortopts, longopts):

        """
        PURPOSE:
            Read the command-line arguments and store them in a dictionary.
            Command-line arguments should come in pairs, e.g.:
                "--customer abc"
        INPUTS:
            The command line arguments (sys.argv).
        RETURNS:
            Returns the dictionary.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """

        if self.cmdline_params:
            return self.cmdline_params

        self.cmdline_params = {}

        try:
            opts, args = getopt.getopt(argv[1:],shortopts,longopts)
        except getopt.GetoptError:
            print("--customer, --env and --app are required.  Optional params:" + str(longopts), file=sys.stderr)
            print("Invoke with -h to see usage and additional help", file=sys.stderr)
            sys.exit(2)

        for opt, arg in opts:
            if opt == '-h':
                #print("Usage: --customer, --env and --app are required in commandline")
                self.cmdline_params["help"] = "true"
                #sys.exit()
            elif opt in ("-c", "--customer"):
                self.cmdline_params["customer"] = arg
            elif opt in ("-e", "--env"):
                self.cmdline_params["env"] = arg
            elif opt in ("-a", "--app"):
                self.cmdline_params["app"] = arg
            elif opt in ("-r", "--region"):
                self.cmdline_params["region"] = arg
            else:
                self.cmdline_params[opt[2:]] = arg

        if "ecosystem" not in self.cmdline_params:
            self.cmdline_params["ecosystem"] = "prod"

        print ("******Cmd line params:" + str(self.cmdline_params))
        return self.cmdline_params

    def get_metadata_params(self):

        """
        PURPOSE:
            Read metadata config file based on region to read metadata db params
        INPUTS:
            The dictionary of command line params
        RETURNS:
            Returns the metadata params from the json config file as a dictionary.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """

        if self.metadata_params:
            return self.metadata_params
            
        if "customer" not in self.cmdline_params or "env" not in self.cmdline_params or "app" not in self.cmdline_params:
            print("--customer, --env and --app are required in commandline to read metadata", file=sys.stderr)
            exit(1)

        customer = self.cmdline_params["customer"]
        environment = self.cmdline_params["env"]

        try:
            region = self.cmdline_params["region"]
        except:
            region = ""
            self.cmdline_params["region"] = region
            
        metadata_host = os.environ.get("METADATA_HOST")
        if metadata_host is None:
            # OLD WAY of reading metadata conn params from a json file in local directory

            # get params to connect to metadata db in mysql
            metadataFilePath = self.get_metadata_config_filename(region)
            print ("Reading metadata db conn-params for region={} from {}".format(region, metadataFilePath), file=sys.stderr)
            with open(metadataFilePath, 'r') as f:
                self.metadata_params = json.load(f)
            
            # Set the default mysql port, if not explicitly specified
            if "port" not in self.metadata_params:
                self.metadata_params["port"] = 3306

            if self.cmdline_params["ecosystem"] == "aimqa":
                self.metadata_params["host"] = "aimqa-metadatards.aktana.com"
            elif self.cmdline_params["ecosystem"] == "saasdev":
                self.metadata_params["host"] = "saasdev-metadatards.aktana.com"
        else:
            # NEW (preferred) WAY of reading metadata conn params from env variables that would be set upfront for this job

            self.metadata_params["host"] = metadata_host
            self.metadata_params["port"] = os.environ.get("METADATA_PORT", "3306")
            self.metadata_params["database"] = "aktanameta"
            self.metadata_params["username"] = os.environ.get("METADATA_USERNAME")
            self.metadata_params["password"] = os.environ.get("METADATA_PASSWORD")

        return self.metadata_params

    def get_metadata_config_filename(self, region):
        filePath = os.path.dirname(os.path.abspath(__file__))
        defaultMetadataFile = 'customer-metadata.json'
        if region != "":
            metadataFile = filePath + "/" + region + '.' + defaultMetadataFile
            if os.path.isfile(metadataFile):
                return metadataFile
        return filePath + "/" + defaultMetadataFile

    def get_env_metadata(self):

        """
        PURPOSE:
            Read Aktana metadata for Customer Snowflake info and add it to env_params dictionary.
        INPUTS:
            The dictionary of metadata parameters and command-line parameters
        RETURNS:
            Returns the snowflake params as a dictionary.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """
            
        if self.env_params:
            return self.env_params

        self.env_params = {}

        try:
            connection = mysql.connector.connect(host=self.metadata_params["host"], database=self.metadata_params["database"], user=self.metadata_params["username"],
                                                    password=self.metadata_params["password"], port=self.metadata_params["port"])
            # local testing
            # connection = mysql.connector.connect(host='127.0.0.1',
            #                                      database=self.metadata_params["database"],
            #                                      user=self.metadata_params["username"],
            #                                         password=self.metadata_params["password"], port=33066)
        except Error as e:
            print("Could not connect to metadata:", e, file=sys.stderr)
            exit(1)

        try:
            if connection.is_connected():
                cursor = connection.cursor()
                query = "select b.rdsServer, b.rdsUserName, b.rdsUserPassword, b.enginedbName, b.learningdbName, b.stagedbName, b.copystormdbName, b.gobblindbName, b.archivedbName, b.rundeckProject, b.rundeckServer, b.rundeckToken, b.environmentSizingName, a.regionName, coalesce(a.isoCountryCode,'') as isoCountryCode from `Customer` a join `CustomerEnvironment` b on a.customerId = b.customerId where a.`customerName`='{}' and b.`envName`='{}'".format(
                    self.cmdline_params["customer"], self.cmdline_params["env"])
                cursor.execute(query)
                record = cursor.fetchall()
                if (len(record) > 0):
                    # Set connection params (if not overridden in the command-line)
                    self.env_params["rds-server"] = os.environ.get("RDS_HOST", record[0][0] ) 
                    self.env_params["rds-user"] = os.environ.get("RDS_USERNAME", record[0][1] )
                    self.env_params["rds-password"] = os.environ.get("RDS_PASSWORD", record[0][2] )
                    self.env_params["rds-enginedbname"] = record[0][3]
                    self.env_params["rds-learningdbname"] = record[0][4]
                    self.env_params["rds-stagedbname"] = record[0][5]
                    self.env_params["rds-copystormdbName"] = record[0][6]
                    self.env_params["rds-gobblindbName"] = record[0][7]
                    self.env_params["rds-archivedbName"] = record[0][8]
                    self.env_params["rundeck-project"] = record[0][9]
                    self.env_params["rundeck-server"] = record[0][10]
                    self.env_params["rundeck-token"] = record[0][11]
                    self.env_params["env-sizingName"] = record[0][12]
                    self.env_params["env-regionName"] = record[0][13]
                    self.env_params["env-countryCode"] = record[0][14]
                        
                    #print('Success reading from CustomerSnowflakeConfigProperties table, account={}, pwd=****, database={}, schema={}, warehouse={}, user={}'.format(
                    #    self.env_params["account"], self.env_params["database"], self.env_params["schema"], self.env_params["warehouse"], self.env_params["user"]
                    #))
                else:
                    print('Customer Information not found in CustomerEnvironment table for customer={}, env={}'.format(self.cmdline_params["customer"], self.cmdline_params["env"]), file=sys.stderr)
        except Error as e:
            print("Could not read env metadata:", e, file=sys.stderr)
        finally:
            # closing database connection.
            try:
                if (connection.is_connected()):
                    cursor.close()
                    connection.close()
            except NameError:
                print("")

        return self.env_params

    def get_snowflake_metadata(self):

        """
        PURPOSE:
            Read Aktana metadata for Customer Snowflake info and add it to snowflake_params dictionary.
        INPUTS:
            The dictionary of metadata parameters and command-line parameters
        RETURNS:
            Returns the snowflake params as a dictionary.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """
            
        if self.snowflake_params:
            return self.snowflake_params

        self.snowflake_params = {}

        try:
            connection = mysql.connector.connect(host=self.metadata_params["host"], database=self.metadata_params["database"], user=self.metadata_params["username"],
                                                    password=self.metadata_params["password"], port=self.metadata_params["port"])
            # local testing
            # connection = mysql.connector.connect(host='127.0.0.1',
            #                                      database=self.metadata_params["database"],
            #                                      user=self.metadata_params["username"],
            #                                      password=self.metadata_params["password"], port=33066)
        except Error as e:
            print("Could not connect to metadata:", e, file=sys.stderr)
            exit(1)

        try:
            if connection.is_connected():
                cursor = connection.cursor()
                query = "select b.account, b.warehouse, b.db, b.dbschema, b.user, b.password, b.region, b.endpoint, b.role, b.dbregion from `Customer` a join `CustomerSnowflakeConfigProperties` b on a.customerId = b.customerId where a.`customerName`='{}' and b.`envName`='{}' and b.`appName`='{}'".format(
                    self.cmdline_params["customer"], self.cmdline_params["env"], self.cmdline_params["app"])
                cursor.execute(query)
                record = cursor.fetchall()
                if (len(record) > 0):
                    # Set connection params (if not overridden in the command-line)
                    self.snowflake_params["snowflake-account"] = record[0][0]
                    self.snowflake_params["snowflake-warehouse"] = record[0][1]
                    self.snowflake_params["snowflake-database"] = record[0][2]
                    self.snowflake_params["snowflake-schema"] = record[0][3]
                    self.snowflake_params["snowflake-user"] = os.environ.get("SNOWFLAKE_USERNAME_"+self.cmdline_params["app"], record[0][4] )
                    self.snowflake_params["snowflake-password"] = os.environ.get("SNOWFLAKE_PASSWORD_"+self.cmdline_params["app"], record[0][5] )
                    self.snowflake_params["snowflake-region"] = record[0][6]
                    self.snowflake_params["snowflake-endpoint"] = record[0][7]
                    self.snowflake_params["snowflake-role"] = record[0][8]
                    self.snowflake_params["snowflake-dbregion"] = record[0][9]

                    # Overwrite snowflake param with command-line value if overridden in the command line
                    if "snowflake-account" in self.cmdline_params:
                        self.snowflake_params["snowflake-account"] = self.cmdline_params["snowflake-account"]
                    if "snowflake-warehouse" in self.cmdline_params:
                        self.snowflake_params["snowflake-warehouse"] = self.cmdline_params["snowflake-warehouse"]
                    if "snowflake-database" in self.cmdline_params:
                        self.snowflake_params["snowflake-database"] = self.cmdline_params["snowflake-database"]
                    if "snowflake-schema" in self.cmdline_params:
                        self.snowflake_params["snowflake-schema"] = self.cmdline_params["snowflake-schema"]
                    if "snowflake-user" in self.cmdline_params:
                        self.snowflake_params["snowflake-user"] = self.cmdline_params["snowflake-user"]
                    if "snowflake-password" in self.cmdline_params:
                        self.snowflake_params["snowflake-password"] = self.cmdline_params["snowflake-password"]
                    if "snowflake-region" in self.cmdline_params:
                        self.snowflake_params["snowflake-region"] = self.cmdline_params["snowflake-region"]
                    if "snowflake-endpoint" in self.cmdline_params:
                        self.snowflake_params["snowflake-endpoint"] = self.cmdline_params["snowflake-endpoint"]
                    if "snowflake-role" in self.cmdline_params:
                        self.snowflake_params["snowflake-role"] = self.cmdline_params["snowflake-role"]
                    if "snowflake-dbregion" in self.cmdline_params:
                        self.snowflake_params["snowflake-dbregion"] = self.cmdline_params["snowflake-dbregion"]
                        
                    if self.snowflake_params["snowflake-region"] != "":
                        self.snowflake_params["snowflake-account"] = self.snowflake_params["snowflake-account"] + "." + self.snowflake_params["snowflake-region"]

                    if os.environ.get("SNOWFLAKE_ACCOUNT"):
                        self.snowflake_params["snowflake-account"] = os.environ.get("SNOWFLAKE_ACCOUNT")

                    #print('Success reading from CustomerSnowflakeConfigProperties table, account={}, pwd=****, database={}, schema={}, warehouse={}, user={}'.format(
                    #    self.snowflake_params["account"], self.snowflake_params["database"], self.snowflake_params["schema"], self.snowflake_params["warehouse"], self.snowflake_params["user"]
                    #))
                else:
                    print('Customer Information not found in CustomerSnowflakeConfigProperties table for customer={}, env={}, app={}'.format(self.cmdline_params["customer"], self.cmdline_params["env"], self.cmdline_params["app"]), file=sys.stderr)
        except Error as e:
            print("Could not read Snowflake metadata:", e, file=sys.stderr)
        finally:
            # closing database connection.
            try:
                if (connection.is_connected()):
                    cursor.close()
                    connection.close()
            except NameError:
                print("")

        return self.snowflake_params

    def get_databricks_metadata(self):

        """
        PURPOSE:
            Read Aktana metadata for Customer Databricks info and add it to databricks_params dictionary.
        INPUTS:
            The dictionary of metadata parameters and command-line parameters
        RETURNS:
            Returns the databricks params as a dictionary.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """

        if self.databricks_params:
            return self.databricks_params

        self.databricks_params = {}

        try:
            connection = mysql.connector.connect(host=self.metadata_params["host"], database=self.metadata_params["database"], user=self.metadata_params["username"],
                                                    password=self.metadata_params["password"], port=self.metadata_params["port"])
        except Error as e:
            print("Could not connect to metadata:", e, file=sys.stderr)
            exit(1)

        try:
            if connection.is_connected():
                cursor = connection.cursor()
                query = "select b.endPoint, b.apiUri, b.zoneId, b.token, b.sparkVersion, b.workerNodeType, b.minWorkers, b.maxWorkers, b.timeoutSecs, b.dockerImageEnv, b.dockerImageBranch, b.dockerImageCommitId, b.dockerUsername, b.dockerPassword from `Customer` a join `CustomerDatabricksConfig` b on a.customerId = b.customerId where a.`customerName`='{}' and b.`envName`='{}' and b.`appName`='{}'".format(
                    self.cmdline_params["customer"], self.cmdline_params["env"], self.cmdline_params["app"])
                cursor.execute(query)
                record = cursor.fetchall()
                if (len(record) > 0):
                    # Set connection params (if not overridden in the command-line)
                    self.databricks_params["db-endPoint"] = record[0][0]
                    self.databricks_params["db-apiUri"] = record[0][1]
                    self.databricks_params["db-zoneId"] = record[0][2]
                    self.databricks_params["db-token"] = os.environ.get("DATABRICKS_API_TOKEN", record[0][3] )
                    self.databricks_params["db-sparkVersion"] = record[0][4]
                    self.databricks_params["db-workerNodeType"] = record[0][5]
                    self.databricks_params["db-minWorkers"] = record[0][6]
                    self.databricks_params["db-maxWorkers"] = record[0][7]
                    self.databricks_params["db-timeoutSecs"] = record[0][8]
                    self.databricks_params["db-dockerImageEnv"] = record[0][9]
                    self.databricks_params["db-dockerImageBranch"] = record[0][10]
                    self.databricks_params["db-dockerImageCommitId"] = record[0][11]
                    self.databricks_params["db-dockerUsername"] = record[0][12]
                    self.databricks_params["db-dockerPassword"] = record[0][13]

                    # Overwrite docker params with command-line value if overridden in the command line
                    if "db-dockerImageBranch" in self.cmdline_params:
                        self.databricks_params["db-dockerImageBranch"] = self.cmdline_params["db-dockerImageBranch"]
                    if "db-dockerImageCommitId" in self.cmdline_params:
                        self.databricks_params["db-dockerImageCommitId"] = self.cmdline_params["db-dockerImageCommitId"]

                    #print('Success reading from CustomerDatabricksConfig table, endPoint={}, apiUri={}, zoneId={}, token={}, sparkVersion={}, workerNodeType={}, minWorkers={}, maxWorkers={}, timeoutSecs={}'.format(
                    #    self.databricks_params["endPoint"], self.databricks_params["apiUri"], self.databricks_params["zoneId"], self.databricks_params["token"], self.databricks_params["sparkVersion"], self.databricks_params["workerNodeType"], self.databricks_params["minWorkers"], self.databricks_params["maxWorkers"], self.databricks_params["timeoutSecs"]
                    #))
                else:
                    print('Customer Information not found in CustomerDatabricksConfig table for customer={}, env={}, app={}'.format(self.cmdline_params["customer"], self.cmdline_params["env"], self.cmdline_params["app"]), file=sys.stderr)
        except Error as e:
            print("Could not read Databricks metadata:", e, file=sys.stderr)
        finally:
            # closing database connection.
            try:
                if (connection.is_connected()):
                    cursor.close()
                    connection.close()
            except NameError:
                print("")
        
        return self.databricks_params

    def get_knime_metadata(self):

        """
        PURPOSE:
            Read Aktana metadata for Customer Knime info and add it to knime_params dictionary.
        INPUTS:
            The dictionary of metadata parameters and command-line parameters
        RETURNS:
            Returns the  knime params as a dictionary.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """

        if self.knime_params:
            return self.knime_params

        self.knime_params = {}

        try:
            connection = mysql.connector.connect(host=self.metadata_params["host"], database=self.metadata_params["database"], user=self.metadata_params["username"],
                                                    password=self.metadata_params["password"], port=self.metadata_params["port"])
        except Error as e:
            print("Could not connect to metadata:", e, file=sys.stderr)
            exit(1)

        try:
            if connection.is_connected():
                cursor = connection.cursor()
                query = "select b.modelName, b.endPoint, b.apiUri, b.zoneId, b.username, b.password, b.executorRequirements, b.cpuRequirements, b.ramRequirements, b.gitRepo, b.gitPath, b.gitBranch, b.gitCommitId from `Customer` a join `CustomerKnimeConfig` b on a.customerId = b.customerId where a.`customerName`='{}' and b.`envName`='{}' and b.`appName`='{}'".format(
                    self.cmdline_params["customer"], self.cmdline_params["env"], self.cmdline_params["app"])
                cursor.execute(query)
                record = cursor.fetchall()
                if (len(record) > 0):
                    # Set connection params (if not overridden in the command-line)
                    self.knime_params["knime-modelName"] = record[0][0]
                    self.knime_params["knime-endPoint"] = record[0][1]
                    self.knime_params["knime-apiUri"] = record[0][2]
                    self.knime_params["knime-zoneId"] = record[0][3]
                    self.knime_params["knime-username"] = os.environ.get("KNIME_USERNAME", record[0][4] )
                    self.knime_params["knime-password"] = os.environ.get("KNIME_PASSWORD", record[0][5] )
                    self.knime_params["knime-executorRequirements"] = record[0][6]
                    self.knime_params["knime-cpuRequirements"] = record[0][7]
                    self.knime_params["knime-ramRequirements"] = record[0][8]
                    self.knime_params["knime-gitRepo"] = record[0][9]
                    self.knime_params["knime-gitPath"] = record[0][10]
                    self.knime_params["knime-gitBranch"] = record[0][11]
                    self.knime_params["knime-gitCommitId"] = record[0][12]

                    # Overwrite docker params with command-line value if overridden in the command line
                    if "knime-gitBranch" in self.cmdline_params:
                        self.knime_params["knime-gitBranch"] = self.cmdline_params["knime-gitBranch"]
                    if "knime-gitCommitId" in self.cmdline_params:
                        self.knime_params["knime-gitCommitId"] = self.cmdline_params["knime-gitCommitId"]

                    #print('Success reading from CustomerKnimeConfig table, endPoint={}, apiUri={}, zoneId={}, username={}, password={}, executorReq={}, cpuReq={}, ramReq={}, repo={}, path={}, branch={}, commitId={}'.format(
                    #    self.knime_params["knime-endPoint"], self.knime_params["knime-apiUri"], self.knime_params["knime-zoneId"], self.knime_params["knime-username"], self.knime_params["knime-password"], self.knime_params["knime-executorRequirements"], self.knime_params["knime-cpuRequirements"], self.knime_params["knime-ramRequirements"], self.knime_params["knime-gitRepo"], self.knime_params["knime-gitPath"], self.knime_params["knime-gitBranch"], self.knime_params["knime-gitCommitId"]
                    #))
                else:
                    print('Customer Information not found in CustomerKnimeConfig table for customer={}, env={}, app={}'.format(self.cmdline_params["customer"], self.cmdline_params["env"], self.cmdline_params["app"]), file=sys.stderr)
        except Error as e:
            print("Could not read  knime metadata:", e, file=sys.stderr)
        finally:
            # closing database connection.
            try:
                if (connection.is_connected()):
                    cursor.close()
                    connection.close()
            except NameError:
                print("")
        
        return self.knime_params

    def get_adl_metadata(self):

        """
        PURPOSE:
            Read Aktana metadata for Customer ADL Config info and add it to adl_params dictionary.
        INPUTS:
            The dictionary of metadata parameters and command-line parameters
        RETURNS:
            Returns the adl_params as a dictionary.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """

        if self.adl_params:
            return self.adl_params

        self.adl_params = {}

        try:
            connection = mysql.connector.connect(host=self.metadata_params["host"], database=self.metadata_params["database"], user=self.metadata_params["username"],
                                                    password=self.metadata_params["password"], port=self.metadata_params["port"])
        except Error as e:
            print("Could not connect to metadata:", e, file=sys.stderr)
            exit(1)

        try:
            if connection.is_connected():
                cursor = connection.cursor()
                query = "select b.rptS3Location, b.adlS3Location, b.iamRole, b.codeRepository, b.mappingLocation, b.loadingLocation, b.awsRegion, b.awsAccessKey, b.awsSecretKey, b.availabilityZone, b.dcoS3Location from `Customer` a join `CustomerADLConfig` b on a.customerId = b.customerId where a.`customerName`='{}' and b.`environment`='{}'".format(
                    self.cmdline_params["customer"], self.cmdline_params["env"])
                cursor.execute(query)
                record = cursor.fetchall()
                if (len(record) > 0):
                    # Set connection params (if not overridden in the command-line)
                    self.adl_params["adl-rptS3Location"] = record[0][0]
                    self.adl_params["adl-adlS3Location"] = record[0][1]
                    self.adl_params["adl-iamRole"] = record[0][2]
                    self.adl_params["adl-codeRepository"] = record[0][3]
                    self.adl_params["adl-mappingLocation"] = record[0][4]
                    self.adl_params["adl-loadingLocation"] = record[0][5]
                    self.adl_params["adl-awsRegion"] = record[0][6]
                    self.adl_params["adl-awsAccessKey"] = os.environ.get("ADL_AWS_ACCESS_KEY", record[0][7] )
                    self.adl_params["adl-awsSecretKey"] = os.environ.get("ADL_AWS_SECRET_KEY", record[0][8] )
                    self.adl_params["adl-availabilityZone"] = record[0][9]
                    self.adl_params["adl-dcoS3Location"] = record[0][10]

                    #print('Success reading from CustomerADLConfig table, rptS3Location={}, adlS3Location={}, iamRole={}, codeRepository={}, mappingLocation={}, loadingLocation={}, awsRegion={}, awsAccessKey={}, awsSecretKey={}, availabilityZone={}'.format(
                    #    self.adl_params["rptS3Location"], 
                    #    self.adl_params["adlS3Location"], 
                    #   self.adl_params["iamRole"], 
                    #   self.adl_params["codeRepository"], 
                    #    self.adl_params["mappingLocation"], 
                    #    self.adl_params["loadingLocation"], 
                    #    self.adl_params["awsRegion"], 
                    #    self.adl_params["awsAccessKey"], 
                    #    self.adl_params["awsSecretKey"], 
                    #    self.adl_params["availabilityZone"]
                    #))
                else:
                    print('Customer Information not found in CustomerADLConfig table for customer={}, env={}'.format(self.cmdline_params["customer"], self.cmdline_params["env"]), file=sys.stderr)
        except Error as e:
            print("Could not read ADL metadata:", e, file=sys.stderr)
        finally:
            # closing database connection.
            try:
                if (connection.is_connected()):
                    cursor.close()
                    connection.close()
            except NameError:
                print("")
        
        return self.adl_params


    def get_api_secret(self):

        """
        PURPOSE:
            Read Aktana metadata for CustomerEnviornment to retrieve secret for API.
        INPUTS:
            The dictionary of metadata parameters and command-line parameters
        RETURNS:
            Returns API secret.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """

        try:
            connection = mysql.connector.connect(host=self.metadata_params["host"], database=self.metadata_params["database"], user=self.metadata_params["username"],
                                                    password=self.metadata_params["password"], port=self.metadata_params["port"])
            # local testing
            # connection = mysql.connector.connect(host='127.0.0.1',
            #                                      database=self.metadata_params["database"],
            #                                      user=self.metadata_params["username"],
            #                                      password=self.metadata_params["password"], port=33066)
        except Error as e:
            print("Could not connect to metadata:", e, file=sys.stderr)
            exit(1)

        try:
            if connection.is_connected():
                cursor = connection.cursor()

                query = "SELECT engineEndpointSecret FROM CustomerEnvironment ce INNER JOIN Customer c ON ce.customerId = c.customerId WHERE c.customerName = \'{}\' and ce.envName = \'{}\'".format(self.cmdline_params['customer'], self.cmdline_params['env'])

                cursor.execute(query)
                record = cursor.fetchall()
                
                if (len(record) > 0):
                    self.api_secret = os.environ.get("DSE_API_SECRET", record[0][0] )
                else:
                    print('API secret Information not found in CustomerEnvironment table for customer={}, env={}'.format(self.cmdline_params["customer"], self.cmdline_params["env"]), file=sys.stderr)
        except Error as e:
            print("Could not read CustomerEnvironment metadata:", e, file=sys.stderr)
        finally:
            # closing database connection.
            try:
                if (connection.is_connected()):
                    cursor.close()
                    connection.close()
            except NameError:
                print("")
        
        return self.api_secret
# ----------------------------------------------------------------------------

    def get_params_json(self, testMode=False):

        params = {}
        cmdline_args = self.get_cmdline_params()
        if not testMode:
            params.update(self.get_env_metadata())
            params.update(self.get_snowflake_metadata())
            params.update(self.get_adl_metadata())
        params["env-customer"] = cmdline_args.get("customer")
        params["env-envName"] = cmdline_args.get("env")

        params["athena-username"] = os.environ.get("ATHENA_USERNAME", "" )
        params["athena-password"] = os.environ.get("ATHENA_PASSWORD", "" )
        params["athena-region"] = os.environ.get("AWS_REGION", params.get("adl-awsRegion"))
        params["athena-s3bucket"] = f"aktana-bdp{cmdline_args.get('region')}-glue"
        params["athena-stagedir"] = f"dbt/{cmdline_args.get('env')}/{cmdline_args.get('customer')}"
        params["athena-schema"] = f"impact_{cmdline_args.get('customer')}_{cmdline_args.get('env')}"

        return json.dumps(params)

if __name__ == '__main__':
    mlutils = aktana_ml_utils()
    cmdline_params, metadata_params = mlutils.initialize(sys.argv, "hc:e:a:r:", ["customer=","env=","app=","region=","ecosystem=","json-output="])

    if (cmdline_params.get("json-output", "") != ""):
        print(mlutils.get_params_json())
        exit(0)
        
    print ("Cmdline=",cmdline_params)
    print ("Params json=", mlutils.get_params_json())
    print ("Metadata params=",metadata_params)
    #print ("Env params=",mlutils.get_env_metadata())
    #print ("Snowflake params=",mlutils.get_snowflake_metadata())
    #print ("ADL params=", mlutils.get_adl_metadata())
    print ("Databricks params=", mlutils.get_databricks_metadata())
    print ("Knime params=", mlutils.get_knime_metadata())


