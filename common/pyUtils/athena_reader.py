import time
from typing import Dict

import boto3
import pandas as pd
from pyathena import connect

# Supports 2 ways to read from athena.
# Use connect/query to use boto3 athena-client,  use connect2/query2 to use pyathena. connect/query (using boto3) is probably faster

class AthenaReader:

    athena_client = None
    s3_client = None
    aws_access_key_id = None
    aws_secret_access_key = None
    aws_session_token = None
    aws_region = None
    athena_staging_bucket = None
    athena_staging_dir = None
    start_time = None
    schema = None
    conn = None

    #    s3://aktana-bdpusdeveks-glue/dbt/dev/sankar/
    def connect(self, aws_access_key, aws_secret_key, session_token, aws_region, athena_staging_bucket, athena_staging_dir, schema):

        self.aws_access_key_id = aws_access_key
        self.aws_secret_access_key = aws_secret_key
        # self.aws_session_token = session_token
        self.aws_region = aws_region
        self.athena_staging_bucket = athena_staging_bucket
        self.athena_staging_dir = athena_staging_dir
        self.schema = schema

        self.athena_client = boto3.client(
            "athena",
            aws_access_key_id=self.aws_access_key_id ,
            aws_secret_access_key=self.aws_secret_access_key,
            # aws_session_token=session_token,
            region_name=self.aws_region
        )

    def connect2(self, aws_access_key, aws_secret_key, session_token, aws_region, athena_staging_bucket, athena_staging_dir, schema):

        self.aws_access_key_id = aws_access_key
        self.aws_secret_access_key = aws_secret_key
        self.aws_region = aws_region
        self.athena_staging_bucket = athena_staging_bucket
        self.athena_staging_dir = athena_staging_dir
        self.schema = schema

        s3staging = "s3://"+self.athena_staging_bucket+"/"+self.athena_staging_dir

        self.conn = connect(aws_access_key_id=aws_access_key,
                    aws_secret_access_key=aws_secret_key,
                    #aws_session_token=session_token,
                    s3_staging_dir=s3staging,
                    region_name=self.aws_region,
                    schema_name=self.schema
                    )

    def query2(self, query):
        df = pd.read_sql_query(query, self.conn)
        return df

    def query(self, query):

        self.start_time = time.time()
        print ("Executing athena query:" + query)
        #"EncryptionConfiguration": {"EncryptionOption": "SSE_S3"},
        response = self.athena_client.start_query_execution(
            QueryString=query,
            QueryExecutionContext={"Database": self.schema},
            ResultConfiguration={
                "OutputLocation": "s3://"+self.athena_staging_bucket+"/"+self.athena_staging_dir
            },
        )
        df_data = self.download_and_load_query_results(response)
        #print(df_data.head())
        print(f"Data fetched in {time.time() - self.start_time}s")
        return df_data

    def download_and_load_query_results(self,
        query_response: Dict) -> pd.DataFrame:
        while True:
            try:
                # This function only loads the first 1000 rows
                self.athena_client.get_query_results(
                    QueryExecutionId=query_response["QueryExecutionId"]
                )
                break
            except Exception as err:
                if "not yet finished" in str(err):
                    time.sleep(0.001)
                else:
                    raise err
        print(f"Time to complete query: {time.time() - self.start_time}s")
        temp_file_location: str = "athena_query_results.csv"
        self.s3_client = boto3.client(
            "s3",
            aws_access_key_id=self.aws_access_key_id ,
            aws_secret_access_key=self.aws_secret_access_key,
            # aws_session_token=self.aws_session_token,
            region_name=self.aws_region
        )
        self.s3_client.download_file(
            self.athena_staging_bucket,
            f"{self.athena_staging_dir}/{query_response['QueryExecutionId']}.csv",
            temp_file_location,
        )
        return pd.read_csv(temp_file_location)
