#!/usr/bin/env python
# encoding: utf-8
import base64
import requests
import json
import re
import sys
import os
import zipfile
from pathlib import Path

from aktana_ml_utils import aktana_ml_utils

mlutils = aktana_ml_utils()
cmdline_params, metadata_params = mlutils.initialize(sys.argv, "hc:e:a:r:", ["customer=","env=","app=","region=","ecosystem=","workflow_uri=","zip_path=", "git_repo_path=", "git_workflow_path=", "skip_upload=", "task="])
knime_params = mlutils.get_knime_metadata()

def get_workflow_uri():
    workflow_uri = cmdline_params.get("workflow_uri", "")
    if workflow_uri == "":
        workflow_uri = cmdline_params["customer"] + "/" + cmdline_params["env"]  + "/" + knime_params["knime-modelName"]
    if workflow_uri.startswith("/"):
        workflow_uri = workflow_uri[1:]
    return workflow_uri

def get_parent_workflow_group_paths():
    p = Path(get_workflow_uri())
    workflow_group_uri = knime_params["knime-endPoint"] + knime_params["knime-apiUri"] + "/repository"
    workflow_group_list = []
    for part in p.parts:
        workflow_group_uri = workflow_group_uri + "/" + part
        workflow_group_list.append(workflow_group_uri)
    return workflow_group_list

def get_workflow_url():
    # Sample workflow/workflow-group create URL: http://**************:8080/knime/rest/v4/repository/Users/<USER>/newworkflow_group
    return knime_params["knime-endPoint"] + knime_params["knime-apiUri"] + "/repository/" + get_workflow_uri()

def zipdir(path, ziph):
    # ziph is zipfile handle
    for root, dirs, files in os.walk(path):
        for file in files:
            ziph.write(os.path.join(root, file))

def build_zip(output_file, input_folder, model_basename):
    print (f"Building zipfile at {output_file} using input={input_folder}/{model_basename}...")
    zipf = zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED)
    os.chdir(input_folder)
    zipdir(model_basename, zipf)
    zipf.close()

def delete_workflow():
    delete_workflow_url = get_workflow_url()

    # Requirements to call the delete workflow-group URL:
    # 1. Http DELETE call on the worflow-url 
    # 2. Content-Type header not required
    # 3. Empty body 
    #
    # Note1: Looks like the same URL can be used to deleted workflow or workflow-group
    try:
        response = requests.delete(delete_workflow_url, auth=(knime_params["knime-username"], knime_params["knime-password"]))
        print(f"Response status-code:{response.status_code}")
        print(response.text)
        return 0
    except requests.exceptions.HTTPError as e:
        print (f"Workflow(group) delete failed for url={delete_workflow_url} with HTTP error")
        print(e.response.text)
        exit(1)

def handle_create_workflow_response(json_output):
    path = json_output.get("path","")
    if (path != ""):
        try:
            print (f"Workflow(group) creation succeeded for path {path} ... created={json_output.get('createdOn','')}, owner={json_output.get('owner','')}, lastUploaded={json_output.get('lastUploadedOn','')}")
            return path
        except KeyError:
            return ""
    else:
        print (f"Creating Workflow(group) failed. Path is empty in json response")
        return ""

def create_parent_workflow_group_paths():
    # Requirements to create an empty workflow-group:
    # Http PUT call on the worflow-url with empty body and no content-type required
    parent_paths = get_parent_workflow_group_paths()
    print (f"Creating parent folders for workflow-group {parent_paths[-1]}")
    try:
        # Make empty PUT calls for each directory except the last one
        for x in parent_paths[:-1]:
            print (f"Creating folder {x} ...")
            response = requests.put(x, auth=(knime_params["knime-username"], knime_params["knime-password"]))
            #print(response.text)
            if ((response.status_code != 200 and response.status_code != 201) or handle_create_workflow_response(response.json()) == ""):
                print (f"Workflow(group) creation failed for url={x} with status_code={response.status_code}")
                print(response.text)
                exit(1)
    except requests.exceptions.HTTPError as e:
        print (f"Workflow(group) creation failed for url={parent_paths} with HTTP error")
        print(e.response.text)
        exit(1)

def handle_upload_workflow_response(json_output):
    #state = json_output.get("state","NotFound")
    #if (state == "IDLE"):
    #    print (f"Job created for workflow={json_output['workflow']} with id={json_output['id']} on executor={json_output['executorID']}")
    #    return json_output['id']
    #else:
    #    print (f"Job creation failed with state={state}")
    for x, y in json_output.items():
        print (x + ":", y)
        print("")
    return ""

def upload_workflow():
    upload_workflow_url = get_workflow_url() + ":data"
    http_headers={'Content-Type': 'application/vnd.knime.workflow-group+zip'}

    # allow for a pre-created zip-path to be specified    
    zip_path = cmdline_params.get("zip_path", "")
    if zip_path == "":
        # if zip_path is not specified, build the zip file using the git-repo and the workflow-path in the git-repo
        git_repo_path = cmdline_params.get("git_repo_path", "")
        git_workflow_path = cmdline_params.get("git_workflow_path", "")
        if git_repo_path == "" or git_workflow_path == "":
            print ("--git_repo_path and --git_workflow_path are required to upload to knime server")
            exit (1)
        p = Path(git_workflow_path)
        p2 = Path(git_repo_path)
        p2 = p2.resolve() # Convert relative path to full absolute path
        git_repo_path = str(p2)
        if git_workflow_path.startswith("/"):
            parent_path = git_repo_path + str(p.parent)
        else:
            parent_path = git_repo_path + '/' + str(p.parent)
        model_basename = str(p.name)
        zip_path = git_repo_path + '/../' + model_basename + '.zip'
        build_zip(zip_path, parent_path, model_basename)
    else:
        print(f"Using pre-created zip file={zip_path}")

    skip_upload = cmdline_params.get("skip_upload", "")
    if skip_upload == "1":
        print ("Skipping upload...")
        return 0

    # Requirements to call the upload workflow-group URL:
    # 1. Http PUT call on the worflow-url 
    # 2. Content-Type header should have 'application/vnd.knime.workflow-group+zip'
    #    Above content-type seems to work for workflow creation as well even though there is a different content-type for it
    #    But if there is an existing workflow at the URL, you cannot overwrite with a workflow-group.
    # 3. Body of the PUT should be zip file with the top-level containing just 1 directory with the contents of the workflow-group.
    #
    # Note1: This is the reason for the build_zip method to 'cd' to one directory above and to call zip on the workflow-group folder
    # Note2: The name of the zip file does not matter.  Only its contents are being sent in the body.
    # Note3: Name of the workflow-group is decided by the URI
    # Note4: You can also create an empty workflow-group by calling a "PUT" on the workflow-url with an empty body.
    # But that is not required since you can upload the entire workflow-group contents uding the deploy_workflow method below
    
    # Parent folders must be created if they don't already exist.  For example, to create /abc/xyz/model1, folders abc and xyz must be pre-created
    create_parent_workflow_group_paths()

    try:
      with open(zip_path, 'rb') as data:
        print("Headers:", http_headers)
        response = requests.put(upload_workflow_url, auth=(knime_params["knime-username"], knime_params["knime-password"]), headers=http_headers, data=data)
        if response.status_code == 200 or response.status_code == 201:
            print (f"Workflow(group) created successfully using url={upload_workflow_url} with status_code={response.status_code}:")
            handle_upload_workflow_response(response.json())
            return 0
        else:
            print (f"Workflow(group) creation response for url={upload_workflow_url} with status_code={response.status_code}:")
            print(response.text)
            exit(1)
    except requests.exceptions.HTTPError as e:
      print (f"Workflow(group) creation failed for url={upload_workflow_url} with HTTP error")
      print(e.response.text)
      exit(1)

def main():
    task = cmdline_params.get("task", "")
    if task == "upload_workflow":
        return_status = upload_workflow()
    elif task == "delete_workflow":
        return_status = delete_workflow()
    else:
        print("Usage:")
        print("To upload a workflow: --task upload_workflow --git_repo_path <path_to_git_repo> --git_workflow_path <relative_path_to_workflow_in_repo>")
        print("To delete a worklow: --task delete_workflow ")
        print("Optional parameters to upload_workflow or delete_workflow")
        print("  --workflow_uri <relative_path_to_workflow_on_server> (to override the workflow path on the server instead of the path built using metadata)")
        print("Optional parameters to upload_workflow")
        print("  --zip_path <path_to_precreated_zip_file> (this will skip building the zip file from the git repo and instead use the pre-built file)")
        print("  --skip_upload 1 (this will build the zip file but not upload it to the server - for debugging purposes)")
        exit (-1)
    exit (return_status)

if __name__ == '__main__':
    main()

