#!/usr/bin/env python

import base64
import requests
import json
import sys
import time
from aktana_ml_utils import aktana_ml_utils

mlutils = aktana_ml_utils()
cmdline_params, metadata_params = mlutils.initialize(sys.argv, "hc:e:a:r:", ["customer=","env=","app=","region=","ecosystem=","job_id=", "model_param1=", "model_param2=", "model_param3=", "task=", "workflow_uri="])
knime_params = mlutils.get_knime_metadata()
snowflake_params = mlutils.get_snowflake_metadata()
adl_params = mlutils.get_adl_metadata()

headers = {'Content-Type': 'application/json'}

###############
# Sample Job create URL: http://44.231.120.117:8080/knime/rest/v4/repository/Customer1/prod/SalesAttribution:jobs
# Sample Job status URL: http://44.231.120.117:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c
#
# A. Steps to execute a Workflow
#    1 a. Invoke create-job URL (POST to workflow_url:jobs)
#      b. Read its response to find job-id for the new job
#    2. Invoke URL to execute-job (POST to job_url with reset=true and async=true and body will include the json-input to the job)
#       a. Build URL for execute-job with reset=true&async=true
#       b. Build json-input for submit in body of POST request to execute-job 
#    3. In a loop, keep checking job status (GET on the job_url with content-type json) until state is not 'EXECUTING' (EXECUTED for success and IDLE for failure)
# 
# B. Steps to delete a job: Call DELETE on Job URL and get 204 if successful or 404 if job doesn't exist
#
# C. Steps to get job status: Call GET on Job URL and look for 'state' in the response json
#
# D. Steps to get list of jobs for a workflow: Call GET on workflow_url:jobs and get a collection of the jobs in response json
#
# ###############

################
#
# Usage: --task <taskname> is required with one of 4 possible tasknames:
# Workflow related tasks: execute_workflow or list_jobs_for_workflow
#   Use '--task execute_workflow' will create and execute a job asynchronously for workflow
#   Use '--task list_jobs_for_workflow' will list all the jobs for the workflow
#   Workflow related tasks get the workflow name using --workflow_uri parameter (if specified)
#   Otherwise, it will build the workflow path using customer/env/modelName from metadata
# Job related tasks: get_job_status or delete_job
#   Use '--task get_job_status' to retrieve the status of an existing job
#   Use '--task delete_job' to delete an existing job
#   Job related tasks require knime job_id to be specified using --job_id <jobid>

# python driver_run_knime_model.py --customer <customername> --env <envname> --app <appname> --task <task_to_perform>
# e.g. python driver_run_knime_model.py --customer msdfr --env qa --app SCD --task execute_workflow
#
# Additional command-line args:
# --workflow_uri <server_workflow_path> (to override the workflow specified in metadata for customer/env/app)
# --job_id <knime_job_id> (to override the job to execute or get status for)
#
# e.g. --workflow_uri Users/sankar/test_snowflake_conn_42 
#      --job_id 4f8a5c25-1663-40f4-b58c-82e1a4136d7c
#
################

# this url is used to list-jobs for a workflow (using HTTP GET) or to create a new job (using HTTP POST) 
def get_workflow_jobs_url():
    # Sample Job create URL: http://44.231.120.117:8080/knime/rest/v4/repository/Customer1/prod/SalesAttribution:jobs
    workflow_uri = cmdline_params.get("workflow_uri", "")
    if workflow_uri == "":
        workflow_uri = cmdline_params["customer"] + "/" + cmdline_params["env"]  + "/" + knime_params["knime-modelName"]
    if not workflow_uri.startswith("/"):
        workflow_uri = "/" + workflow_uri
    return knime_params["knime-endPoint"] + knime_params["knime-apiUri"] + "/repository" + workflow_uri + ":jobs"

# this url is used to manage the job (using HTTP GET to get the status) or to execute the job (using HTTP POST) or to delete the job (using HTTP DELETE) 
def get_job_status_url(job_id):
    # Sample Job status URL: http://44.231.120.117:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c
    override_job_id = cmdline_params.get("job_id", "")
    if override_job_id == "":
        override_job_id = job_id
    return knime_params["knime-endPoint"] + knime_params["knime-apiUri"] + "/jobs/" + override_job_id

################################
# Functions to handle creating a new job and to parse the response
################################

def handle_create_job_response(json_output):
    state = json_output.get("state","NotFound")
    if (state == "IDLE"):
        print (f"Job created for workflow={json_output['workflow']} with id={json_output['id']} on executor={json_output['executorID']}")
        return json_output['id']
    else:
        print (f"Job creation failed with state={state}")
        for x, y in json_output.items():
            print (x + ":", y)
            print("")
        return ""

def create_job():
    create_job_url = get_workflow_jobs_url()

    # Requirements to call the create job :
    # 1. Http POST call on the worflow:jobs url
    # 2. Content-Type header should be 'application/json'
    # 3. Body of the PUT should be empty
    #
    # Note1: Response will include the details of the job created including the ID and @controls section with other REST API URLs to perform on the job
    
    try:
        print(f"Calling url={create_job_url} with headers={headers}")
        response = requests.post(create_job_url, auth=(knime_params["knime-username"], knime_params["knime-password"]), headers=headers)
        if response.status_code == 200 or response.status_code == 201:
            return handle_create_job_response(response.json())
        else:
            print (f"Job creation failed for url={create_job_url} with status_code={response.status_code}")
            print(response.text)
            exit(1)
    except requests.exceptions.HTTPError as e:
        print (f"Job creation failed for url={create_job_url} with HTTP error")
        print(e.response.text)
        exit(1)
    except Exception as e:
        print (f"Job creation failed for url={create_job_url} with HTTP error")
        print(e)
        exit(1)

################################
# Functions to handle execute a new job, build its input parameters and to parse the response
################################

def build_job_execute_input_dict():

    params = {}
    params["customer"] = cmdline_params.get("customer", "")
    params["env"] = cmdline_params.get("env", "")
    params["app"] = cmdline_params.get("app", "")
    params["snowflake_account"] = snowflake_params.get("snowflake-account", "")
    params["snowflake_region"] = "" # Leave it blank since account would already have region info embedded and snowflake is deprecating this
    params["snowflake_endpoint"] = snowflake_params.get("snowflake-endpoint", "")
    params["snowflake_warehouse"] = snowflake_params.get("snowflake-warehouse", "")
    params["snowflake_db"] = snowflake_params.get("snowflake-database", "")
    params["snowflake_schema"] = snowflake_params.get("snowflake-schema", "")
    params["snowflake_role"] = snowflake_params.get("snowflake-role", "")
    params["snowflake_user"] = snowflake_params.get("snowflake-user", "")
    params["snowflake_password"] = snowflake_params.get("snowflake-password", "")
    params["adl_s3location"] = adl_params.get("adl-adlS3Location", "")
    params["aws_region"] = adl_params.get("adl-awsRegion", "")
    params["aws_access_key"] = adl_params.get("adl-awsAccessKey", "")
    params["aws_secret_key"] = adl_params.get("adl-awsSecretKey", "")
    params["model_param1"] = cmdline_params.get("model_param1", "")
    params["model_param2"] = cmdline_params.get("model_param2", "")
    params["model_param3"] = cmdline_params.get("model_param3", "")

    toplevel = {"json-input": params}

    return json.dumps(toplevel)

def handle_execute_job_response(json_output):
    state = json_output.get("state","NotFound")
    if (state == "EXECUTING" or state == "EXECUTED"):
        print (f"Job execution started for workflow={json_output['workflow']}, state={state} for name={json_output['name']} and job_id={json_output['id']} on executor={json_output['executorID']}")
        print (f"Job input={json_output['inputParameters']}")
        return 0
    else:
        print (f"Job execution failed with state={state}")
        for x, y in json_output.items():
            print (x + ":", y)
            print("")
        return -1

# Sample Job execute URL: http://44.231.120.117:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c?reset=true&async=true
def execute_job(job_id):
    job_execute_url = get_job_status_url(job_id) + "?reset=true&async=true"
    try:
        response = requests.post(job_execute_url, auth=(knime_params["knime-username"], knime_params["knime-password"]), headers=headers, data=build_job_execute_input_dict())
        if response.status_code == 200 or response.status_code == 201:
            next_step = handle_execute_job_response(response.json())
            if (next_step == 0):
                return 0
        else:
            print (f"Job execute failed for url={job_execute_url} with status_code={response.status_code}")
            print(response.text)
            exit(1)
    except requests.exceptions.HTTPError as e:
        print (f"Job creation failed for url={job_execute_url} with HTTP error")
        print(e.response.text)
        exit(1)
    except Exception as e:
        print (f"Job creation failed for url={job_execute_url} with HTTP error")
        print(e)
        exit(1)

    return 1

################################
# Functions to handle checking for the status of a job and to parse the response
################################

def handle_job_status_response(json_output):
    state = json_output.get("state","NotFound")
    if cmdline_params.get("task", "") == "get_job_status":
        show_full_job_status_output = 1 
    else:
        show_full_job_status_output = 0

    if (state == "EXECUTING" and show_full_job_status_output == 0):
        print (f"Job executing for workflow={json_output.get('workflow')}, with name={json_output.get('name')} and job_id={json_output.get('id')} on executor={json_output.get('executorID')}")
        return 1
    if ((state == "EXECUTED" or state == "EXECUTION_FINISHED") and show_full_job_status_output == 0):
        print (f"Job execution completed for workflow={json_output.get('workflow')}, with name={json_output.get('name')} and job_id={json_output.get('id')} on executor={json_output.get('executorID')}")
        print (f"Job finished at {json_output.get('finishedExecutionAt')} with output={json_output.get('outputValues')}")
        print (f"Job started at {json_output.get('startedExecutionAt')} with input={json_output.get('inputParameters')}")
        return 0
    else:
        print (f"Displaying full job status, state={state}")
        for x, y in json_output.items():
            print (x + ":", y)
            #print("")
        if show_full_job_status_output == 1:
            return 0
        else:
            return -1

def get_job_status(job_id):
    job_execute_url = get_job_status_url(job_id)
    while True:
        try:
            response = requests.get(job_execute_url, auth=(knime_params["knime-username"], knime_params["knime-password"]), headers=headers)
            if response.status_code == 200 or response.status_code == 201:
                next_step = handle_job_status_response(response.json())
                if (next_step == 0):
                    break
                if (next_step == -1):
                    exit(1)
            else:
                print (f"Job status check failed for url={job_execute_url} with status_code={response.status_code}")
                print(response.text)
                exit(1)
        except requests.exceptions.HTTPError as e:
            print (f"Job status check failed for url={job_execute_url} with HTTP error")
            print(e.response.text)
            exit(1)
        except Exception as e:
            print (f"Job status check failed for url={job_execute_url} with HTTP error")
            print(e)
            exit(1)
        print ("******* Waiting 30 secs *********")
        time.sleep(30)
    return 0

################################
# Functions to delete a job
################################

def delete_job(job_id):
    job_execute_url = get_job_status_url(job_id)
    try:
        response = requests.delete(job_execute_url, auth=(knime_params["knime-username"], knime_params["knime-password"]), headers=headers)
        if response.status_code == 200 or response.status_code == 204:
            print (f"Job deleted for url={job_execute_url} with status_code={response.status_code}")
            exit(0)
        else:
            print (f"Job delete failed for url={job_execute_url} with status_code={response.status_code}")
            print(response.text)
            exit(1)
    except requests.exceptions.HTTPError as e:
        print (f"Job delete failed for url={job_execute_url} with HTTP error")
        print(e.response.text)
        exit(1)
    except Exception as e:
        print (f"Job delete failed for url={job_execute_url} with HTTP error")
        print(e)
        exit(1)

################################
# Functions to handle listing the jobs for a workflow and to parse the response
################################

def handle_job_list_response(job_list_url, json_output):
    joblist = json_output.get("jobs")
    print (f"{len(joblist)} job(s) found for {job_list_url}")
    for x in joblist:
        print (f"  Id={x.get('id')},state={x.get('state')},created={x.get('createdAt')},started={x.get('startedExecutionAt')},finished={x.get('finishedExecutionAt')}")

def get_job_list():
    # Sample Job list URL: http://44.231.120.117:8080/knime/rest/v4/repository/Customer1/prod/SalesAttribution:jobs
    job_list_url = get_workflow_jobs_url()
    print (f"Retrieving job(s) found for {job_list_url}")
    try:
        response = requests.get(job_list_url, auth=(knime_params["knime-username"], knime_params["knime-password"]), headers=headers)
        if response.status_code == 200 or response.status_code == 201:
            handle_job_list_response(job_list_url, response.json())
        else:
            print (f"Job list call failed for url={job_list_url} with status_code={response.status_code}")
            print(response.text)
            exit(1)
    except requests.exceptions.HTTPError as e:
        print (f"Job list call failed for url={job_list_url} with HTTP error")
        print(e.response.text)
        exit(1)
    except Exception as e:
        print (f"Job list call failed for url={job_list_url} with HTTP error")
        print(e)
        exit(1)
    return (0)

################################
# Test functions to read a file for the json response to parse the response
################################

def test_handle_create_job_response():
    with open('/Users/<USER>/repos/learning/common/unitTest/data/knime/create_job_output.json') as json_file:
        data = json.load(json_file)
    next_step = handle_create_job_response(data)
    print(next_step)
    return next_step

def test_handle_execute_job_response():
    with open('/Users/<USER>/repos/learning/common/unitTest/data/knime/execute-job.json') as json_file:
        data = json.load(json_file)
    next_step = handle_execute_job_response(data)
    print(next_step)
    return next_step

def test_handle_job_status_response():
    with open('/Users/<USER>/repos/learning/common/unitTest/data/knime/job-status.json') as json_file:
        data = json.load(json_file)
    next_step = handle_job_status_response(data)
    print(next_step)
    return next_step

################################
# Main function
################################

def main():
    task = cmdline_params.get("task", "")
    return_status = 0
    if task == "execute_workflow":
        knime_job_id = create_job()
        if knime_job_id == "":
            print("knime_job_id not found for new job. Exiting...")
            return_status = -1
        else:
            return_status = execute_job(knime_job_id)
            return_status = get_job_status(knime_job_id)
    elif task == "list_jobs_for_workflow":
        return_status = get_job_list()
    elif task == "get_job_status" and cmdline_params.get("job_id", "") != "":
        return_status = get_job_status(cmdline_params.get("job_id"))
    elif task == "delete_job" and cmdline_params.get("job_id", "") != "":
        return_status = delete_job(cmdline_params.get("job_id"))
    else:
        print("Usage: --task <taskname> is required with one of 4 possible tasknames:")
        print("Workflow related tasks: execute_workflow or list_jobs_for_workflow")
        print("  Use '--task execute_workflow' will create and execute a job asynchronously for workflow")
        print("  Use '--task list_jobs_for_workflow' will list all the jobs for the workflow")
        print("  Workflow related tasks get the workflow name using --workflow_uri parameter (if specified)")
        print("  Otherwise, it will build the workflow path using customer/env/modelName from metadata")
        print("Job related tasks: get_job_status or delete_job")
        print("  Use '--task get_job_status' to retrieve the status of an existing job")
        print("  Use '--task delete_job' to delete an existing job")
        print("  Job related tasks require knime job_id to be specified using --job_id <jobid>")
        return_status = -1
    exit (return_status)

if __name__ == '__main__':
    main()
