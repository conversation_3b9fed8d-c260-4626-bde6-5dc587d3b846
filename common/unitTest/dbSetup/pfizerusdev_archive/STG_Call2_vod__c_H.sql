CREATE TABLE `STG_Call2_vod__c_H` (
  `STG_Call2_Key` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `Call2_vod__c` varchar(256) COLLATE utf8_unicode_ci NOT NULL,
  `OwnerId` varchar(256) COLLATE utf8_unicode_ci DEFAULT NULL,
  `IsDeleted` tinyint(4) DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `LastModifiedDate` datetime DEFAULT NULL,
  `Account_vod__c` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Status_vod__c` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Parent_Address_vod__c` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Call_Datetime_vod__c` datetime DEFAULT NULL,
  `Call_Date_vod__c` date DEFAULT NULL,
  `Call_Type_vod__c` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Duration_vod__c` bigint(20) DEFAULT NULL,
  `versionNumber` bigint(20) DEFAULT NULL,
  `effectiveStartDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `effectiveEndDate` datetime NOT NULL DEFAULT '2050-01-01 00:00:00',
  `arc_createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `arc_updatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`STG_Call2_Key`),
  KEY `STG_Call2_H_Call2_vod__c_IDX` (`Call2_vod__c`(255)),
  KEY `STG_Call2_H_versionNumber_IDX` (`versionNumber`),
  KEY `STG_Call2_H_Call2_vod__c__comb_IDX` (`Call2_vod__c`(255),`versionNumber`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
