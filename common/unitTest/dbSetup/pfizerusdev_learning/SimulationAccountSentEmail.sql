CREATE TABLE `SimulationAccountSentEmail` (
  `InteractionId` varchar(80) COLLATE utf8_unicode_ci NOT NULL,
  `messageUID` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `accountUID` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `productUID` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `isOpen` tinyint(1) DEFAULT NULL,
  `status` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `clickCount` bigint(20) DEFAULT NULL,
  `emailName` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `emailSubject` mediumtext COLLATE utf8_unicode_ci,
  `emailBody` mediumtext COLLATE utf8_unicode_ci,
  `emailSentDate` datetime DEFAULT NULL,
  `senderEmailID` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `accountEmailID` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `emailLastOpenedDate` datetime DEFAULT NULL,
  `lastClickDate` datetime DEFAULT NULL,
  `createdAt` datetime DEFAULT NULL,
  `updatedAt` datetime DEFAULT NULL,
  KEY `SimulationAccountSentEmail_idx_2` (`messageUID`,`accountUID`),
  KEY `SimulationAccountSentEmail_idx_1` (`accountUID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
