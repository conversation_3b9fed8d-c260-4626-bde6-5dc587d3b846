CREATE TABLE `AccountProduct` (
  `accountId` int(11) NOT NULL,
  `productId` int(11) NOT NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `priorityChantix_akt` tinyint(4) DEFAULT NULL,
  `sampleActual_akt` int(11) DEFAULT NULL,
  `sampleGuidance_akt` int(11) DEFAULT NULL,
  `sampleRemaining_akt` int(11) DEFAULT NULL,
  `chxAllBenLE35Per_akt` double DEFAULT NULL,
  `chxAllBenLE35PerMkt_akt` double DEFAULT NULL,
  `chxAllBenLE6580Per_akt` double DEFAULT NULL,
  `chxAllBenLE6580PerMkt_akt` double DEFAULT NULL,
  `chxCommLE6580Per_akt` double DEFAULT NULL,
  `chxCommLE6580PerMkt_akt` double DEFAULT NULL,
  `nrx13w_akt` double DEFAULT NULL,
  `trx13w_akt` double DEFAULT NULL,
  `top80_akt` tinyint(4) DEFAULT NULL,
  `tried13w_akt` tinyint(4) DEFAULT NULL,
  `adopter_akt` tinyint(4) DEFAULT NULL,
  `lostAdopter_akt` tinyint(4) DEFAULT NULL,
  `top10Down_akt` int(11) DEFAULT NULL,
  `top10MktShareDown_akt` int(11) DEFAULT NULL,
  `newWriter_akt` tinyint(4) DEFAULT NULL,
  `top10Up_akt` int(11) DEFAULT NULL,
  `top10MktShareUp_akt` int(11) DEFAULT NULL,
  `top10MktVolUp_akt` int(11) DEFAULT NULL,
  `topWriterNoTRx_akt` tinyint(4) DEFAULT NULL,
  `prodRank_akt` int(11) DEFAULT NULL,
  `prodMktShare_akt` double DEFAULT NULL,
  `prodChangeMktShare_akt` double DEFAULT NULL,
  `samples_akt` double DEFAULT NULL,
  `fmdpn_balance_akt` double DEFAULT NULL,
  `lyricaOD_akt` tinyint(4) DEFAULT NULL,
  `emailUnsubscribe_akt` tinyint(4) DEFAULT NULL,
  `nrx3moChange_akt` double DEFAULT NULL,
  `trx3moChange_akt` double DEFAULT NULL,
  `likelyProgressionCount_akt` int(11) DEFAULT NULL,
  `doseModificationCount_akt` int(11) DEFAULT NULL,
  `newPatientStartCount_akt` int(11) DEFAULT NULL,
  `bosNewPrescriberFlag_akt` int(11) DEFAULT NULL,
  `cmlTRXVolumePrior_akt` double DEFAULT NULL,
  `cmlTRXVolumeCurrent_akt` double DEFAULT NULL,
  `cmlTRXVolumeChange_akt` double DEFAULT NULL,
  `trx4wChange_akt` double DEFAULT NULL,
  `nrx4wChange_akt` double DEFAULT NULL,
  `visitDetailsRemaining_dse_akt` int(11) DEFAULT NULL,
  `visitDetailsGoal_dse_akt` int(11) DEFAULT NULL,
  `visitDetailsCompleted_dse_akt` int(11) DEFAULT NULL,
  `webEDetailsRemaining_dse_akt` int(11) DEFAULT NULL,
  `webEDetailsGoal_dse_akt` int(11) DEFAULT NULL,
  `webEDetailsCompleted_dse_akt` int(11) DEFAULT NULL,
  `sendsRemaining_dse_akt` int(11) DEFAULT NULL,
  `sendsGoal_dse_akt` int(11) DEFAULT NULL,
  `sendsCompleted_dse_akt` int(11) DEFAULT NULL,
  `sampleAO_akt` double DEFAULT NULL,
  `intXeljanzXRName_akt` int(11) DEFAULT NULL,
  `dblElq2mg3mTRxCurrShare_akt` double DEFAULT NULL,
  `dblElq3mNRxShareChgPct_akt` double DEFAULT NULL,
  `dblElq4wNRxShareChgPct_akt` double DEFAULT NULL,
  `dblElq4wTRxShareChgPct_akt` double DEFAULT NULL,
  `dblElq3mNRxCurrMktShare_akt` double DEFAULT NULL,
  `dblElq3mTRxCurrMktShare_akt` double DEFAULT NULL,
  `dblElq4wNRxCurrMktShare_akt` double DEFAULT NULL,
  `dblElq4wTRxCurrMktShare_akt` double DEFAULT NULL,
  `dblElq3mNRxPrevMktShare_akt` double DEFAULT NULL,
  `dblElq3mTRxPrevMktShare_akt` double DEFAULT NULL,
  `dblElq4wNRxPrevMktShare_akt` double DEFAULT NULL,
  `dblElq4wTRxPrevMktShare_akt` double DEFAULT NULL,
  `dblNOAC3mNRxShareChgPct_akt` double DEFAULT NULL,
  `dblNOAC3mNRxCurrMktShare_akt` double DEFAULT NULL,
  `dblNOAC3mNRxVolume_akt` double DEFAULT NULL,
  `dblNOAC3mTRxCurrMktShare_akt` double DEFAULT NULL,
  `dblNOAC3mNRxPrevMktShare_akt` double DEFAULT NULL,
  `dblNOAC3mTRxPrevMktShare_akt` double DEFAULT NULL,
  `dblWarfarin3mNRxShareChgPct_akt` double DEFAULT NULL,
  `dblWarfarin3mNRxCurrMktShare_akt` double DEFAULT NULL,
  `dblWarfarin3mTRxCurrMktShare_akt` double DEFAULT NULL,
  `dblWarfarin3mNRxPrevMktShare_akt` double DEFAULT NULL,
  `dblWarfarin3mTRxPrevMktShare_akt` double DEFAULT NULL,
  `dblXarelto4wNRxCurrMktShare_akt` double DEFAULT NULL,
  `visitsGoalChx_akt` int(11) DEFAULT NULL,
  `visitsRemainingChx_akt` int(11) DEFAULT NULL,
  `visitsGoalElq_akt` int(11) DEFAULT NULL,
  `visitsRemainingElq_akt` int(11) DEFAULT NULL,
  `dblXarelto3mNRxCurrMktShare_akt` double DEFAULT NULL,
  `dblElq3mTRxShareChgPct_akt` double DEFAULT NULL,
  `lyrCurr3mNRxShareChangePct_akt` double DEFAULT NULL,
  `dblLyr3mNRxCurrMktShare_akt` double DEFAULT NULL,
  `dblLyr3mNRxPrevMktShare_akt` double DEFAULT NULL,
  `lyrCurr4wNRxShareChangePct_akt` double DEFAULT NULL,
  `dblLyr4wNRxCurrMktShare_akt` double DEFAULT NULL,
  `dblLyr4wNRxPrevMktShare_akt` double DEFAULT NULL,
  `dblDuavee13wCopayDrops_akt` double DEFAULT NULL,
  `dblDuavee13wCopayRedemptions_akt` double DEFAULT NULL,
  `dblDuavee1wTRxVolume_akt` double DEFAULT NULL,
  `dblDuavee3mComDNRxVolume_akt` double DEFAULT NULL,
  `dblDuavee3mMedDNRxVolume_akt` double DEFAULT NULL,
  `dblDuavee3mNRxComBusPct_akt` double DEFAULT NULL,
  `dblDuavee3mNRxComVolume_akt` double DEFAULT NULL,
  `dblDuavee3mNRxMedDBusPct_akt` double DEFAULT NULL,
  `dblDuavee4wNRxCurrComMktShare_akt` double DEFAULT NULL,
  `dblDuavee4wNRxCurrMedDMktShare_akt` double DEFAULT NULL,
  `dblDuavee6mTRxVolume_akt` double DEFAULT NULL,
  `dblEstring13wCopayDrops_akt` double DEFAULT NULL,
  `dblEstring13wCopayRedemptions_akt` double DEFAULT NULL,
  `dblEstring3mNRxVolume_akt` double DEFAULT NULL,
  `dblEstring3mTRxComVolume_akt` double DEFAULT NULL,
  `dblEstring3mTRxVolume_akt` double DEFAULT NULL,
  `dblEstring4wComTRxShareChange_akt` double DEFAULT NULL,
  `dblEstring4wMedDTRxShareChange_akt` double DEFAULT NULL,
  `dblEstring4wTRxCurrComMktShare_akt` double DEFAULT NULL,
  `dblEstring4wTRxCurrMedDMktShare_akt` double DEFAULT NULL,
  `dblEstring4wTRxPriorComMktShare_akt` double DEFAULT NULL,
  `dblEstring4wTRxPriorMedDMktShare_akt` double DEFAULT NULL,
  `dblPO13wCopayDrops_akt` double DEFAULT NULL,
  `dblPO13wCopayRedemptions_akt` double DEFAULT NULL,
  `dblPO3mComNRxVolume_akt` double DEFAULT NULL,
  `dblPO3mMedDNRxVolume_akt` double DEFAULT NULL,
  `dblPO3mNRxComBusPct_akt` double DEFAULT NULL,
  `dblPO3mNRxComVolume_akt` double DEFAULT NULL,
  `dblPO3mNRxMedDBusPct_akt` double DEFAULT NULL,
  `dblPO3mNRxMedDVolume_akt` double DEFAULT NULL,
  `dblPO3mNRxVolume_akt` double DEFAULT NULL,
  `dblPO4wComNRxShareChange_akt` double DEFAULT NULL,
  `dblPO4wMedDNRxShareChange_akt` double DEFAULT NULL,
  `dblPO4wNRxCurrComMktShare_akt` double DEFAULT NULL,
  `dblPO4wNRxCurrMedDMktShare_akt` double DEFAULT NULL,
  `dblPO4wNRxPriorComMktShare_akt` double DEFAULT NULL,
  `dblPO4wNRxPriorMedDMktShare_akt` double DEFAULT NULL,
  `dblPVC13wCopayDrops_akt` double DEFAULT NULL,
  `dblPVC13wCopayRedemptions_akt` double DEFAULT NULL,
  `dblPVC3mNRxVolume_akt` double DEFAULT NULL,
  `dblPVC3mTRxComVolume_akt` double DEFAULT NULL,
  `dblPVC3mTRxVolume_akt` double DEFAULT NULL,
  `dblPVC4wComTRxShareChange_akt` double DEFAULT NULL,
  `dblPVC4wMedDTRxShareChange_akt` double DEFAULT NULL,
  `dblPVC4wTRxCurrComMktShare_akt` double DEFAULT NULL,
  `dblPVC4wTRxCurrMedDMktShare_akt` double DEFAULT NULL,
  `dblPVC4wTRxPriorComMktShare_akt` double DEFAULT NULL,
  `dblPVC4wTRxPriorMedDMktShare_akt` double DEFAULT NULL,
  `dblPVCEstring3mComTRxVolume_akt` double DEFAULT NULL,
  `dblPVCEstring3mMedDTRxVolume_akt` double DEFAULT NULL,
  `dblPVCEstring3mTRxComBusPct_akt` double DEFAULT NULL,
  `dblPVCEstring3mTRxMedDBusPct_akt` double DEFAULT NULL,
  `eucrisaNewWriterFlag_akt` tinyint(1) DEFAULT NULL,
  `eucrisaR13wNRx_akt` int(11) DEFAULT NULL,
  `eucrisaR13wNRxFlag_akt` tinyint(1) DEFAULT NULL,
  `LoTProxy_akt` double DEFAULT NULL,
  `productRelativeValue_akt` double DEFAULT NULL,
  `visitsGoalEuc_akt` double DEFAULT NULL,
  `visitsRemainingEuc_akt` double DEFAULT NULL,
  `dblELQ3mTRxCurrComMktShare_akt` double DEFAULT NULL,
  `dblELQ3mTRxCurrMedDMktShare_akt` double DEFAULT NULL,
  `dblLyrComAccessPct_akt` double DEFAULT NULL,
  `dblLyrMedDAccessPct_akt` double DEFAULT NULL,
  `dblLyrOverallAccessPct_akt` double DEFAULT NULL,
  `dblOAC3mTRxComBusPct_akt` double DEFAULT NULL,
  `dblOAC3mTRxMedDBusPct_akt` double DEFAULT NULL,
  `dblWar3mTRxCurrComMktShare_akt` double DEFAULT NULL,
  `dblWar3mTRxCurrMedDMktShare_akt` double DEFAULT NULL,
  `dblXar3mTRxCurrComMktShare_akt` double DEFAULT NULL,
  `dblXar3mTRxCurrMedDMktShare_akt` double DEFAULT NULL,
  `sendProbability_akt` double DEFAULT NULL,
  `visitProbability_akt` double DEFAULT NULL,
  `dblAetna12mMedDTRxMktShare_akt` double DEFAULT NULL,
  `dblELQ3mNRxCurrMedDShareChgPct_akt` double DEFAULT NULL,
  `dblSSI12mMedDTRxMktShare_akt` double DEFAULT NULL,
  `dblUHG12mMedDTRxMktShare_akt` double DEFAULT NULL,
  `newPatientCount_akt` int(11) DEFAULT NULL,
  `unshippedReferralsFlag_akt` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`accountId`,`productId`),
  UNIQUE KEY `AccountProduct_uniqueAK` (`accountId`,`productId`),
  KEY `AccountProduct_accountId` (`accountId`),
  KEY `AccountProduct_productId` (`productId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci