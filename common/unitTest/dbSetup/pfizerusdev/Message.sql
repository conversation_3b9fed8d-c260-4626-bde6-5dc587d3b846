CREATE TABLE `Message` (
  `messageId` int(11) NOT NULL AUTO_INCREMENT,
  `externalId` varchar(40) COLLATE utf8_unicode_ci NOT NULL,
  `productId` int(11) NOT NULL,
  `messageChannelId` int(11) NOT NULL,
  `messageTopicId` int(11) NOT NULL,
  `messageName` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `messageDescription` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `repActionChannelId` tinyint(4) DEFAULT NULL,
  `lastPhysicalMessageUID` varchar(40) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMAR<PERSON>Y (`messageId`),
  UNIQUE KEY `message_uniqueExternalId` (`externalId`),
  UNIQUE KEY `message_uniqueNameProductIdMessageChannelId` (`productId`,`messageName`,`messageChannelId`),
  KEY `message_productId` (`productId`),
  KEY `message_messageChannelId` (`messageChannelId`),
  KEY `message_messageTopicId` (`messageTopicId`),
  KEY `Message_lastPhysicalMessageUID_idx` (`lastPhysicalMessageUID`)
) ENGINE=InnoDB AUTO_INCREMENT=1384 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='each row defines a Message that can be sent to an Account (e.g. an iRep Email or Letter)'