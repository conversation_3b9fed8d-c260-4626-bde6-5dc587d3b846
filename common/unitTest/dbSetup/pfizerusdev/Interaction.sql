CREATE TABLE `Interaction` (
  `interactionId` int(11) NOT NULL AUTO_INCREMENT,
  `externalId` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `interactionTypeId` tinyint(4) NOT NULL,
  `repActionTypeId` tinyint(4) NOT NULL,
  `repId` int(11) NOT NULL,
  `facilityId` int(11) DEFAULT NULL,
  `startDateTime` datetime DEFAULT NULL,
  `duration` int(11) DEFAULT NULL,
  `wasCreatedFromSuggestion` tinyint(4) NOT NULL,
  `isCompleted` tinyint(4) NOT NULL,
  `isDeleted` tinyint(4) NOT NULL,
  `timeZoneId` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'IANA timeZone identifier for the Interaction',
  `startDateLocal` date DEFAULT NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`interactionId`),
  UNIQUE KEY `interaction_uniqueAK` (`externalId`),
  KEY `interaction_facilityId` (`facilityId`),
  KEY `interaction_interactionTypeId` (`interactionTypeId`),
  KEY `interaction_repActionTypeId` (`repActionTypeId`),
  KEY `interaction_repId` (`repId`),
  KEY `interaction_startDateLocal` (`startDateLocal`)
) ENGINE=InnoDB AUTO_INCREMENT=214518121 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci