CREATE TABLE `InteractionProduct` (
  `interactionId` int(11) NOT NULL,
  `productId` int(11) NOT NULL,
  `productInteractionTypeId` tinyint(4) NOT NULL,
  `repActionTypeId` tinyint(4) NOT NULL,
  `externalId` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `physicalMessageUID` varchar(40) COLLATE utf8_unicode_ci DEFAULT NULL,
  `messageTopicId` int(11) DEFAULT NULL,
  `messageId` int(11) DEFAULT NULL,
  `messageReaction` varchar(40) COLLATE utf8_unicode_ci DEFAULT NULL,
  `actionOrder` int(11) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `isCompleted` tinyint(4) NOT NULL,
  `suggestionReferenceId` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `matchedSuggestionProduct` tinyint(1) DEFAULT NULL,
  `matchedSuggestionMessage` tinyint(1) DEFAULT NULL,
  `suggestionInferredAt` datetime DEFAULT NULL,
  `isDeleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Soft delete flag.',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`interactionId`,`productId`,`productInteractionTypeId`),
  UNIQUE KEY `interactionProduct_uniqueAK1` (`interactionId`,`productId`,`productInteractionTypeId`),
  UNIQUE KEY `interactionProduct_uniqueAK2` (`interactionId`,`productId`,`repActionTypeId`),
  UNIQUE KEY `interactionProduct_uniqueExternalId` (`externalId`),
  KEY `interactionProduct_productId` (`productId`),
  KEY `interactionProduct_interactionId` (`interactionId`),
  KEY `interactionProduct_productInteractionTypeId` (`productInteractionTypeId`),
  KEY `interactionProduct_repActionTypeId` (`repActionTypeId`),
  KEY `interactionProduct_messageTopicId` (`messageTopicId`),
  KEY `interactionProduct_messageId` (`messageId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci