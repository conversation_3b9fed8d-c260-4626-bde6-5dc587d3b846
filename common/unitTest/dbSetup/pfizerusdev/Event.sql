CREATE TABLE `Event` (
  `eventId` int(11) NOT NULL AUTO_INCREMENT,
  `eventTypeId` int(11) NOT NULL,
  `accountId` int(11) NOT NULL,
  `productId` int(11) DEFAULT NULL,
  `messageTopicId` int(11) DEFAULT NULL,
  `messageId` int(11) DEFAULT NULL,
  `repId` int(11) DEFAULT NULL,
  `eventLabel` varchar(500) COLLATE utf8_unicode_ci DEFAULT NULL,
  `eventDate` date NOT NULL,
  `eventDateTimeUTC` datetime DEFAULT NULL,
  `eventValue` float DEFAULT NULL,
  `externalId` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `physicalMessageUID` varchar(40) COLLATE utf8_unicode_ci DEFAULT NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`eventId`),
  UNIQUE KEY `event_uniqueExternalId` (`externalId`),
  KEY `event_eventTypeId` (`eventTypeId`),
  KEY `event_accountId` (`accountId`),
  KEY `event_productId` (`productId`),
  KEY `event_messageTopicId` (`messageTopicId`),
  KEY `event_messageId` (`messageId`),
  KEY `event_repId` (`repId`)
) ENGINE=InnoDB AUTO_INCREMENT=4836305 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci