CREATE TABLE `StrategyTarget` (
  `strategyTargetId` int(11) NOT NULL AUTO_INCREMENT,
  `targetsPeriodId` int(11) NOT NULL,
  `targetingLevelId` int(11) NOT NULL,
  `facilityId` int(11) DEFAULT NULL,
  `accountGroupId` int(11) DEFAULT NULL,
  `accountId` int(11) DEFAULT NULL,
  `repTeamId` int(11) DEFAULT NULL,
  `repId` int(11) DEFAULT NULL,
  `productId` int(11) DEFAULT NULL,
  `messageTopicId` int(11) DEFAULT NULL,
  `messageId` int(11) DEFAULT NULL,
  `interactionTypeId` tinyint(4) NOT NULL,
  `productInteractionTypeId` tinyint(4) NOT NULL,
  `target` int(11) NOT NULL,
  `targetMin` int(11) DEFAULT NULL,
  `targetMax` int(11) DEFAULT NULL,
  `visitActionOrderMin` int(11) DEFAULT NULL,
  `visitActionOrderMax` int(11) DEFAULT NULL,
  `relativeValue` double DEFAULT NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`strategyTargetId`),
  KEY `strategyTarget_targetsPeriodId` (`targetsPeriodId`),
  KEY `strategyTarget_targetingLevelId` (`targetingLevelId`),
  KEY `strategyTarget_facilityId` (`facilityId`),
  KEY `strategyTarget_accountId` (`accountId`),
  KEY `strategyTarget_repTeamId` (`repTeamId`),
  KEY `strategyTarget_repId` (`repId`),
  KEY `strategyTarget_productId` (`productId`),
  KEY `strategyTarget_messageTopicId` (`messageTopicId`),
  KEY `strategyTarget_messageId` (`messageId`),
  KEY `strategyTarget_interactionTypeId` (`interactionTypeId`),
  KEY `strategyTarget_productInteractionTypeId` (`productInteractionTypeId`),
  KEY `StrategyTarget_accountGroupId` (`accountGroupId`)
) ENGINE=InnoDB AUTO_INCREMENT=******** DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci