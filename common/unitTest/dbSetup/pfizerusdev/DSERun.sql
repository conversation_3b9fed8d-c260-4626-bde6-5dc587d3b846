CREATE TABLE `DSERun` (
  `runId` bigint(20) NOT NULL AUTO_INCREMENT,
  `runUID` varchar(36) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `startDateTime` datetime NOT NULL,
  `startDateLocal` date DEFAULT NULL,
  `seConfigId` int(11) NOT NULL,
  `runGroupId` int(11) DEFAULT NULL,
  `persistenceVerbosity` char(1) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'T=TERSE, N=NORMAL, V=VERBOSE',
  `runSeriesName` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `repId` int(11) DEFAULT NULL COMMENT 'null when numSuggestibleReps>1',
  `numSuggestibleReps` int(11) DEFAULT NULL,
  `numSuggestibleAccounts` int(11) DEFAULT NULL,
  `numEvaluatedAccounts` int(11) DEFAULT NULL,
  `numErrorsAndWarnings` int(11) DEFAULT NULL,
  `elapsedSecsQueries` float DEFAULT NULL,
  `elapsedSecsCompute` float DEFAULT NULL,
  `elapsedSecsPersist` float DEFAULT NULL,
  `elapsedSecsTotal` float DEFAULT NULL,
  `runSummaryReport` mediumtext COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`runId`),
  UNIQUE KEY `DSERun_runUID_unique` (`runUID`),
  KEY `dserun_seConfigId` (`seConfigId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
