CREATE TABLE `Approved_Document_vod__c` (
  `Id` char(18) COLLATE utf8_unicode_ci NOT NULL,
  `OwnerId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `IsDeleted` tinyint(1) DEFAULT NULL,
  `Name` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `RecordTypeId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `LastModifiedDate` datetime DEFAULT NULL,
  `LastModifiedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `SystemModstamp` datetime DEFAULT NULL,
  `LastActivityDate` date DEFAULT NULL,
  `MayEdit` tinyint(1) DEFAULT NULL,
  `IsLocked` tinyint(1) DEFAULT NULL,
  `LastViewedDate` datetime DEFAULT NULL,
  `LastReferencedDate` datetime DEFAULT NULL,
  `ConnectionReceivedId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ConnectionSentId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Detail_Group_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Document_Description_vod__c` text COLLATE utf8_unicode_ci,
  `Document_Host_URL_vod__c` text COLLATE utf8_unicode_ci,
  `Document_ID_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Document_Last_Mod_DateTime_vod__c` datetime DEFAULT NULL,
  `Email_Allows_Documents_vod__c` tinyint(1) DEFAULT NULL,
  `Email_Domain_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Email_Fragment_HTML_vod__c` mediumtext COLLATE utf8_unicode_ci,
  `Email_From_Address_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Email_From_Name_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Email_HTML_1_vod__c` mediumtext COLLATE utf8_unicode_ci,
  `Email_HTML_2_vod__c` mediumtext COLLATE utf8_unicode_ci,
  `Email_ReplyTo_Address_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Email_ReplyTo_Name_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Email_Subject_vod__c` text COLLATE utf8_unicode_ci,
  `Email_Template_Fragment_Document_ID_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Email_Template_Fragment_HTML_vod__c` mediumtext COLLATE utf8_unicode_ci,
  `ISI_Document_ID_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Language_vod__c` text COLLATE utf8_unicode_ci,
  `Other_Document_ID_List_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `PI_Document_ID_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Piece_Document_ID_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Product_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Status_vod__c` text COLLATE utf8_unicode_ci,
  `Territory_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Vault_Instance_ID_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `PRE_Sharing_Last_Calculated_Date__c` datetime DEFAULT NULL,
  `Campaign_Source_Code__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`Id`),
  KEY `CSIX_SystemModstamp` (`SystemModstamp`),
  KEY `CSIX_4DELETECHECK` (`IsDeleted`,`SystemModstamp`),
  KEY `CSIX_OwnerId` (`OwnerId`),
  KEY `CSIX_RecordTypeId` (`RecordTypeId`),
  KEY `CSIX_CreatedById` (`CreatedById`),
  KEY `CSIX_LastModifiedById` (`LastModifiedById`),
  KEY `CSIX_ConnectionReceivedId` (`ConnectionReceivedId`),
  KEY `CSIX_ConnectionSentId` (`ConnectionSentId`),
  KEY `CSIX_Detail_Group_vod__c` (`Detail_Group_vod__c`),
  KEY `CSIX_Product_vod__c` (`Product_vod__c`),
  KEY `Id` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci