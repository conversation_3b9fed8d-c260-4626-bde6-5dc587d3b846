CREATE TABLE `Sync_Tracking_vod__c` (
  `Id` char(18) COLLATE utf8_unicode_ci NOT NULL,
  `OwnerId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `IsDeleted` tinyint(1) DEFAULT NULL,
  `Name` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `LastModifiedDate` datetime DEFAULT NULL,
  `LastModifiedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `SystemModstamp` datetime DEFAULT NULL,
  `MayEdit` tinyint(1) DEFAULT NULL,
  `IsLocked` tinyint(1) DEFAULT NULL,
  `LastViewedDate` datetime DEFAULT NULL,
  `LastReferencedDate` datetime DEFAULT NULL,
  `ConnectionReceivedId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ConnectionSentId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Download_Processed_vod__c` tinyint(1) DEFAULT NULL,
  `Mobile_ID_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Successful_Sync_vod__c` bigint(20) DEFAULT NULL,
  `Sync_Completed_Datetime_vod__c` datetime DEFAULT NULL,
  `Sync_Duration_vod__c` decimal(20,4) DEFAULT NULL,
  `Sync_Start_Datetime_vod__c` datetime DEFAULT NULL,
  `Sync_Type_vod__c` longtext COLLATE utf8_unicode_ci,
  `Upload_Processed_vod__c` tinyint(1) DEFAULT NULL,
  `VInsights_Processed_vod__c` tinyint(1) DEFAULT NULL,
  `Version_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Media_Processed_vod__c` tinyint(1) DEFAULT NULL,
  `fOwner_Profile_coe__c` longtext COLLATE utf8_unicode_ci,
  `Record_Created_DateTime__c` datetime DEFAULT NULL,
  `Discrepancy_Secs__c` decimal(20,4) DEFAULT NULL,
  `Canceled_vod__c` tinyint(1) DEFAULT NULL,
  `Sync_Discrepancy_Duration_Delta__c` decimal(20,4) DEFAULT NULL,
  `Number_of_Retries_vod__c` int(11) DEFAULT NULL,
  `Number_of_Upload_Errors_vod__c` int(11) DEFAULT NULL,
  `Number_of_Uploads_vod__c` int(11) DEFAULT NULL,
  `Number_of_VTrans_vod__c` int(11) DEFAULT NULL,
  `BackupCreatedDate` datetime DEFAULT NULL,
  `BackupModifiedDate` datetime DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
