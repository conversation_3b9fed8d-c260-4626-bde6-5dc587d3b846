CREATE TABLE `Suggestion_Feedback_vod__c_arc` (
  `Id` char(18) COLLATE utf8_unicode_ci NOT NULL,
  `IsDeleted` tinyint(1) DEFAULT NULL,
  `Name` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `RecordTypeId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `LastModifiedDate` datetime DEFAULT NULL,
  `LastModifiedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `SystemModstamp` datetime DEFAULT NULL,
  `MayEdit` tinyint(1) DEFAULT NULL,
  `IsLocked` tinyint(1) DEFAULT NULL,
  `ConnectionReceivedId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ConnectionSentId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Suggestion_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Account_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Activity_Execution_Type_vod__c` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Call2_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `DismissFeedback1_vod__c` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `DismissFeedback2_vod__c` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `DismissFeedback3_vod__c` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `DismissFeedback4_vod__c` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Mobile_ID_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Sent_Email_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`Id`),
  KEY `CSIX_SystemModstamp` (`SystemModstamp`),
  KEY `CSIX_4DELETECHECK` (`IsDeleted`,`SystemModstamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci