CREATE TABLE `Sent_Email_vod__c_arc` (
  `Id` char(18) COLLATE utf8_unicode_ci NOT NULL,
  `OwnerId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `IsDeleted` tinyint(1) DEFAULT NULL,
  `Name` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `RecordTypeId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `LastModifiedDate` datetime DEFAULT NULL,
  `LastModifiedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `SystemModstamp` datetime DEFAULT NULL,
  `LastActivityDate` date DEFAULT NULL,
  `MayEdit` tinyint(1) DEFAULT NULL,
  `IsLocked` tinyint(1) DEFAULT NULL,
  `ConnectionReceivedId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ConnectionSentId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Account_Email_vod__c` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Account_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Approved_Email_Template_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Capture_Datetime_vod__c` datetime DEFAULT NULL,
  `Detail_Group_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Email_Config_Values_vod__c` mediumtext COLLATE utf8_unicode_ci,
  `Email_Content2_vod__c` mediumtext COLLATE utf8_unicode_ci,
  `Email_Content_vod__c` mediumtext COLLATE utf8_unicode_ci,
  `Email_Fragments_vod__c` mediumtext COLLATE utf8_unicode_ci,
  `Email_Sent_Date_vod__c` datetime DEFAULT NULL,
  `Failure_Msg_vod__c` text COLLATE utf8_unicode_ci,
  `Last_Activity_Date_vod__c` datetime DEFAULT NULL,
  `Last_Device_vod__c` text COLLATE utf8_unicode_ci,
  `MC_Capture_Datetime_vod__c` datetime DEFAULT NULL,
  `Mobile_ID_vod__c` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Opened_vod__c` bigint(20) DEFAULT NULL,
  `Product_Display_vod__c` text COLLATE utf8_unicode_ci,
  `Product_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Sender_Email_vod__c` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Status_vod__c` text COLLATE utf8_unicode_ci,
  `Valid_Consent_Exists_vod__c` tinyint(1) DEFAULT NULL,
  `Approved_Document_Views_vod__c` bigint(20) DEFAULT NULL,
  `Click_Count_vod__c` bigint(20) DEFAULT NULL,
  `Last_Click_Date_vod__c` datetime DEFAULT NULL,
  `Last_Open_Date_vod__c` datetime DEFAULT NULL,
  `Open_Count_vod__c` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  KEY `CSIX_SystemModstamp` (`SystemModstamp`),
  KEY `CSIX_4DELETECHECK` (`IsDeleted`,`SystemModstamp`),
  KEY `CSIX_OwnerId` (`OwnerId`),
  KEY `CSIX_RecordTypeId` (`RecordTypeId`),
  KEY `CSIX_CreatedById` (`CreatedById`),
  KEY `CSIX_LastModifiedById` (`LastModifiedById`),
  KEY `CSIX_ConnectionReceivedId` (`ConnectionReceivedId`),
  KEY `CSIX_ConnectionSentId` (`ConnectionSentId`),
  KEY `CSIX_Account_vod__c` (`Account_vod__c`),
  KEY `CSIX_Approved_Email_Template_vod__c` (`Approved_Email_Template_vod__c`),
  KEY `CSIX_Detail_Group_vod__c` (`Detail_Group_vod__c`),
  KEY `CSIX_Product_vod__c` (`Product_vod__c`),
  KEY `Id` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci