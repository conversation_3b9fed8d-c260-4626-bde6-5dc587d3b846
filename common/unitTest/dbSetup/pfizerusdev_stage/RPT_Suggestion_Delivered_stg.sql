CREATE TABLE `RPT_Suggestion_Delivered_stg` (
  `externalId` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `suggestionLifeCycleId` bigint(20) DEFAULT NULL,
  `runId` bigint(20) DEFAULT NULL,
  `runUID` varchar(36) COLLATE utf8_unicode_ci DEFAULT NULL,
  `repTeamId` int(11) DEFAULT NULL,
  `repTeamUID` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `repTeamName` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `seConfigId` int(11) DEFAULT NULL,
  `seConfigName` varchar(60) COLLATE utf8_unicode_ci DEFAULT NULL,
  `runGroupId` int(11) DEFAULT NULL,
  `suggestedDate` date DEFAULT NULL,
  `startDateLocal` date DEFAULT NULL,
  `repId` int(11) DEFAULT NULL,
  `repUID` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `repName` varchar(150) COLLATE utf8_unicode_ci DEFAULT NULL,
  `repCreatedAt` datetime DEFAULT NULL,
  `accountId` int(11) DEFAULT NULL,
  `accountUID` varchar(40) COLLATE utf8_unicode_ci DEFAULT NULL,
  `runRepDateSuggestionId` varchar(36) COLLATE utf8_unicode_ci DEFAULT NULL,
  `accountName` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `detailRepActionTypeId` tinyint(4) DEFAULT NULL,
  `detailRepActionTypeUID` varchar(24) COLLATE utf8_unicode_ci DEFAULT NULL,
  `detailRepActionName` varchar(24) COLLATE utf8_unicode_ci DEFAULT NULL,
  `runRepDateSuggestionDetailId` varchar(36) COLLATE utf8_unicode_ci DEFAULT NULL,
  `productId` int(11) DEFAULT NULL,
  `productUID` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `productName` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `messageId` int(11) DEFAULT NULL,
  `messageUID` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `messageName` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `suggestionUID` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `suggestionReferenceId` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `lastViewedAt` datetime DEFAULT NULL,
  `viewedAt` datetime DEFAULT NULL,
  `viewedDuration` bigint(21) DEFAULT NULL,
  `actionTaken` varchar(21) COLLATE utf8_unicode_ci DEFAULT NULL,
  `actionTaken_dt` varchar(21) COLLATE utf8_unicode_ci DEFAULT NULL,
  `isSuggestionCompleted` int(11) DEFAULT NULL,
  `isSuggestionCompletedDirect` int(1) DEFAULT NULL,
  `isSuggestionCompletedInfer` int(1) DEFAULT NULL,
  `isSuggestionDismissed` int(11) DEFAULT NULL,
  `dismissReasonType` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dismissReason` text COLLATE utf8_unicode_ci,
  `dismissReason_dt` varchar(40) COLLATE utf8_unicode_ci DEFAULT NULL,
  `isSuggestionActive` tinyint(4) DEFAULT NULL,
  `facilityLatitude` double DEFAULT NULL,
  `facilityLongitude` double DEFAULT NULL,
  `facilityGeoLocationString` varchar(1000) COLLATE utf8_unicode_ci DEFAULT NULL,
  `repLatitude` double DEFAULT NULL,
  `repLongitude` double DEFAULT NULL,
  `territoryCityName` varchar(142) COLLATE utf8_unicode_ci DEFAULT NULL,
  `districtName` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `territoryId` varchar(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `territoryName` varchar(150) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regionName` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regionGroup` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dismissedAt` datetime DEFAULT NULL,
  `dismissCount` bigint(11) DEFAULT NULL,
  `lastPublishedAt` datetime DEFAULT NULL,
  `createdAt` datetime DEFAULT NULL,
  `updatedAt` datetime DEFAULT NULL,
  `reportedInteractionUID` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `inferredInteractionUID` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `interactionUID` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `completedAt` datetime DEFAULT NULL,
  `inferredAt` datetime DEFAULT NULL,
  `isFirstSuggestedDate` int(1) DEFAULT NULL,
  `isLastSuggestedDate` int(1) DEFAULT NULL,
  `suggestionDriver` varchar(7) COLLATE utf8_unicode_ci DEFAULT NULL,
  `timeoffday` date DEFAULT NULL,
  `isHighestRunidForDay` int(1) DEFAULT NULL,
  `holiday_weekend_Flag` varchar(1) COLLATE utf8_unicode_ci DEFAULT NULL,
  `crmFieldName` varchar(16) COLLATE utf8_unicode_ci DEFAULT NULL,
  `reasonText` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  `reasonRank` int(11) DEFAULT NULL,
  `runRepDateSuggestionReasonId` varchar(36) COLLATE utf8_unicode_ci DEFAULT NULL,
  `repRole` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `repBag` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dmUID` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dmName` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `interactionId` int(11) DEFAULT NULL,
  `isCompleted` tinyint(4) DEFAULT NULL,
  `startDateTime` datetime DEFAULT NULL,
  `isREMix` varchar(5) COLLATE utf8_unicode_ci DEFAULT NULL,
  `isDSESpark` int(1) DEFAULT NULL,
  `arc_createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `arc_updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`externalId`),
  UNIQUE KEY `RPT_Suggestion_Delivered_stg_UN` (`startDateLocal`,`suggestionReferenceId`,`runRepDateSuggestionDetailId`,`runRepDateSuggestionReasonId`),
  KEY `RPT_Suggestion_Delivered_stg_suggestionReferenceId_idx` (`suggestionReferenceId`),
  KEY `RPT_Suggestion_Delivered_stg_comp_idx` (`suggestionReferenceId`,`startDateLocal`),
  KEY `RPT_Suggestion_Delivered_stg_startDateLocal_idx` (`startDateLocal`),
  KEY `RPT_Suggestion_Delivered_stg_suggestedDate_idx` (`suggestedDate`),
  KEY `RPT_Suggestion_Delivered_stg_dismissReasonType_idx` (`dismissedAt`,`dismissReasonType`),
  KEY `RPT_Suggestion_Delivered_stg_repUID_idx` (`repUID`),
  KEY `RPT_Suggestion_Delivered_stg_accountUID_idx` (`accountUID`),
  KEY `RPT_Suggestion_Delivered_stg_interactionUID` (`interactionUID`),
  KEY `RPT_Suggestion_Delivered_stg_updatedAt_idx` (`arc_updatedAt`),
  KEY `runId_idx` (`runId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
