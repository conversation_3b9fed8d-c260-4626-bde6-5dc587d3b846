CREATE TABLE `Suggestion_vod__c_arc` (
  `Id` char(18) COLLATE utf8_unicode_ci NOT NULL,
  `OwnerId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `IsDeleted` tinyint(1) DEFAULT NULL,
  `Name` varchar(80) CHARACTER SET utf8 DEFAULT NULL,
  `RecordTypeId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `LastModifiedDate` datetime DEFAULT NULL,
  `LastModifiedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `SystemModstamp` datetime DEFAULT NULL,
  `MayEdit` tinyint(1) DEFAULT NULL,
  `IsLocked` tinyint(1) DEFAULT NULL,
  `ConnectionReceivedId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ConnectionSentId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Account_Priority_Score_vod__c` int(11) DEFAULT NULL,
  `Account_vod__c` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Action_Count_vod__c` bigint(20) DEFAULT NULL,
  `Actioned_vod__c` int(11) DEFAULT NULL,
  `Call_Objective_CLM_ID_vod__c` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `Call_Objective_From_Date_vod__c` date DEFAULT NULL,
  `Call_Objective_On_By_Default_vod__c` tinyint(1) DEFAULT NULL,
  `Call_Objective_Record_Type_vod__c` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `Call_Objective_To_Date_vod__c` date DEFAULT NULL,
  `Dismiss_Count_vod__c` bigint(20) DEFAULT NULL,
  `Dismissed_vod__c` int(11) DEFAULT NULL,
  `Display_Dismiss_vod__c` tinyint(1) DEFAULT NULL,
  `Display_Mark_As_Complete_vod__c` tinyint(1) DEFAULT NULL,
  `Display_Score_vod__c` tinyint(1) DEFAULT NULL,
  `Email_Template_ID_vod__c` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `Email_Template_Vault_ID_vod__c` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `Email_Template_vod__c` tinyint(1) DEFAULT NULL,
  `Expiration_Date_vod__c` date DEFAULT NULL,
  `Mark_Complete_Count_vod__c` bigint(20) DEFAULT NULL,
  `Marked_As_Complete_vod__c` int(11) DEFAULT NULL,
  `No_Homepage_vod__c` tinyint(1) DEFAULT NULL,
  `Planned_Call_Date_vod__c` date DEFAULT NULL,
  `Posted_Date_vod__c` date DEFAULT NULL,
  `Priority_vod__c` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `Reason_vod__c` text CHARACTER SET utf8,
  `Record_Type_Name_vod__c` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Suggestion_External_Id_vod__c` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Suppress_Reason_vod__c` tinyint(1) DEFAULT NULL,
  `Title_vod__c` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  PRIMARY KEY (`Id`),
  KEY `CSIX_SystemModstamp` (`SystemModstamp`),
  KEY `CSIX_4DELETECHECK` (`IsDeleted`,`SystemModstamp`),
  KEY `CSIX_Expiration_Date_vod__c` (`Expiration_Date_vod__c`),
  KEY `CSIX_Suggestion_External_Id_vod__c` (`Suggestion_External_Id_vod__c`),
  KEY `Id` (`Id`),
  KEY `OwnerId` (`OwnerId`),
  KEY `Record_Type_Name_vod__c` (`Record_Type_Name_vod__c`),
  KEY `idx_Suggestion_vod__c_arc_Call_Objective_Record_Type_vod__c` (`Call_Objective_Record_Type_vod__c`),
  KEY `idx_Call_Objective_From_Date_vod` (`Call_Objective_From_Date_vod__c`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci