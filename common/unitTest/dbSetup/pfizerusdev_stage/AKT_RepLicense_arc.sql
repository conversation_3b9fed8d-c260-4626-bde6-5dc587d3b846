CREATE TABLE `AKT_RepLicense_arc` (
  `cluster` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `districtName` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `repName` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `territoryName` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `territoryId` varchar(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `externalId` char(18) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `customerId` varchar(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regionName` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `activatedDate` datetime DEFAULT NULL,
  `isActivated` tinyint(1) DEFAULT NULL,
  `createdAt` datetime DEFAULT NULL,
  `updatedAt` datetime DEFAULT NULL,
  `repRole` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `repBag` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `seConfigId` int(11) DEFAULT NULL,
  `testRunGroupId` int(11) DEFAULT NULL,
  `RunGroupId` int(11) DEFAULT NULL,
  `dmName` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dmUID` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `startDate` datetime DEFAULT NULL,
  `endDate` datetime DEFAULT NULL,
  UNIQUE KEY `externalId` (`externalId`,`territoryId`,`cluster`,`startDate`),
  KEY `AKT_RepLicense_idx` (`externalId`),
  KEY `AKT_RepLicense_idx2` (`territoryId`),
  KEY `AKT_RepLicense_idx3` (`cluster`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
