CREATE TABLE `RecordType_vod__c_arc` (
  `Id` char(18) COLLATE utf8_unicode_ci NOT NULL,
  `Name` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `DeveloperName` varchar(80) COLLATE utf8_unicode_ci DEFAULT NULL,
  `NamespacePrefix` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `Description` text COLLATE utf8_unicode_ci,
  `BusinessProcessId` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `SobjectType` varchar(40) COLLATE utf8_unicode_ci DEFAULT NULL,
  `IsActive` tinyint(1) DEFAULT NULL,
  `IsPersonType` tinyint(1) DEFAULT NULL,
  `CreatedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `LastModifiedById` char(18) COLLATE utf8_unicode_ci DEFAULT NULL,
  `LastModifiedDate` datetime DEFAULT NULL,
  `SystemModstamp` datetime DEFAULT NULL,
  PRIMARY KEY (`Id`),
  KEY `CSIX_SystemModstamp` (`SystemModstamp`),
  KEY `CSIX_BusinessProcessId` (`BusinessProcessId`),
  KEY `CSIX_CreatedById` (`CreatedById`),
  KEY `CSIX_LastModifiedById` (`LastModifiedById`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci