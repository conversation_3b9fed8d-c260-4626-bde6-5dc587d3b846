context('testing readV3db from learningPackage')
print(Sys.time())

# writing mock data to database (func in source(sprintf('%s/common/unitTest/initializeUnitTest.r',homedir)))
requiredMockDataList <- list(pfizerusdev=c('Product','Facility','RepTeam','Message','MessageSetMessage','StrategyTarget','TargetingLevel','RepActionType','TargetsPeriod','Event','EventType','Interaction','InteractionType','ProductInteractionType','InteractionProduct','InteractionProductMessage','InteractionAccount','AccountProduct','Rep','RepTeamRep','RepAccountAssignment','Account'),pfizerprod_cs=c('Approved_Document_vod__c'))
resetMockData(homedir,dbuser,dbhost,port,dbpassword,dbname,dbname_cs,requiredMockDataList)

# loading Learning package to call function for test
library(Learning)
dictraw <- readV3db()
names(dictraw) <- paste(names(dictraw),"_new",sep="")
for(i in 1:length(dictraw))assign(names(dictraw)[i],dictraw[[i]])

# test case
test_that("test have correct length of data", {
  expect_equal(length(dictraw), 11)
  expect_equal(dim(accounts_new),c(310,46))
  expect_equal(dim(reps_new),c(193,3))
  expect_equal(dim(repAssignments_new),c(919,2))
  expect_equal(dim(facilities_new),c(1,6))
  expect_equal(dim(targets_new),c(35,8))
  expect_equal(dim(interactions_new),c(47763,13))
  expect_equal(dim(messages_new),c(446,6))
  expect_equal(dim(messageSet_new),c(10,2))
  expect_equal(dim(accountProduct_new),c(560,170))
  expect_equal(dim(events_new),c(733,9))
  expect_equal(dim(products_new),c(4,2))
})

rm(dictraw)
test_that("result is the same as saved", {
  load(sprintf("%s/common/unitTest/data/from_readV3db_accounts.RData", homedir))
  load(sprintf("%s/common/unitTest/data/from_readV3db_reps.RData", homedir))
  load(sprintf("%s/common/unitTest/data/from_readV3db_repAssignments.RData", homedir))
  load(sprintf("%s/common/unitTest/data/from_readV3db_facilities.RData", homedir))
  load(sprintf("%s/common/unitTest/data/from_readV3db_targets.RData", homedir))
  load(sprintf("%s/common/unitTest/data/from_readV3db_interactions.RData", homedir))
  load(sprintf("%s/common/unitTest/data/from_readV3db_messages.RData", homedir))
  load(sprintf("%s/common/unitTest/data/from_readV3db_messageSetMessage.RData", homedir))
  load(sprintf("%s/common/unitTest/data/from_readV3db_accountProduct.RData", homedir))
  load(sprintf("%s/common/unitTest/data/from_readV3db_events.RData", homedir))
  load(sprintf("%s/common/unitTest/data/from_readV3db_products.RData", homedir))
  expect_equal(accounts_new,accounts)
  expect_equal(reps_new,reps)
  expect_equal(repAssignments_new,repAssignments)
  expect_equal(facilities_new,facilities)
  expect_equal(targets_new,targets)
  expect_equal(interactions_new,interactions)
  expect_equal(messages_new,messages[,names(messages_new),with=F])
  expect_equal(messageSet_new,messageSetMessage)
  expect_equal(accountProduct_new,accountProduct)
  expect_equal(events_new,events)
  expect_equal(products_new,products)
})
