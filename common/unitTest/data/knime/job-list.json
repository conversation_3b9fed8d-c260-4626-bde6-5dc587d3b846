{"@controls": {"self": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42:jobs", "method": "GET"}, "knime:new-job": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42:jobs{?timeout,asyncLoading}", "isHrefTemplate": true, "method": "POST", "title": "Create a new job"}, "up": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42", "method": "GET"}}, "jobs": [{"@controls": {"self": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/4ffff172-aee6-4c45-a48d-6f5d4a6d4f01", "method": "GET"}, "collection": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42:jobs", "method": "GET"}, "edit": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/4ffff172-aee6-4c45-a48d-6f5d4a6d4f01", "method": "PUT", "encoding": "Json"}, "knime:delete": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/4ffff172-aee6-4c45-a48d-6f5d4a6d4f01", "method": "DELETE"}, "knime:workflow": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42", "method": "GET"}, "knime:upload": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/{path}:data?from-job=4ffff172-aee6-4c45-a48d-6f5d4a6d4f01", "isHrefTemplate": true, "method": "PUT"}, "knime:execute-job": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/4ffff172-aee6-4c45-a48d-6f5d4a6d4f01{?reset,async,timeout}", "isHrefTemplate": true, "output": ["application/json"], "template": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "ml_admin", "snowflake_password": "xxx", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "yyy", "aws_secret_key": "zzz"}}, "method": "POST", "encoding": "Json"}, "knime:workflow-summary": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/4ffff172-aee6-4c45-a48d-6f5d4a6d4f01/workflow-summary{?format,includeExecutionInfo}", "isHrefTemplate": true, "output": ["application/json", "application/xml"], "method": "GET"}}, "id": "4ffff172-aee6-4c45-a48d-6f5d4a6d4f01", "discard": false, "configuration": {}, "owner": "<PERSON><PERSON><PERSON><PERSON>", "state": "IDLE", "inputParameters": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "ml_admin", "snowflake_password": "xxx", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "yyy", "aws_secret_key": "zzz"}}, "notifications": {}, "isOutdated": true, "createdAt": "2021-02-11T00:02:21.535Z[Etc/UTC]", "workflow": "/Users/<USER>/test_snowflake_conn_42", "isSwapped": true, "hasReport": false, "outputValues": {"json-output": {}}, "name": "test_snowflake_conn_42 2021-02-11 00.02.21", "properties": {"com.knime.enterprise.server.executor.requirements": "", "com.knime.enterprise.server.jobpool.size": "0"}}, {"@controls": {"self": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840", "method": "GET"}, "collection": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42:jobs", "method": "GET"}, "edit": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840", "method": "PUT", "encoding": "Json"}, "knime:delete": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840", "method": "DELETE"}, "knime:workflow": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42", "method": "GET"}, "knime:upload": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/{path}:data?from-job=7851975f-3343-40e9-a970-a4a8806e6840", "isHrefTemplate": true, "method": "PUT"}, "knime:execute-job": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840{?reset,async,timeout}", "isHrefTemplate": true, "output": ["application/json"], "template": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "aaa", "snowflake_password": "bbb", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "yyy", "aws_secret_key": "zzz"}}, "method": "POST", "encoding": "Json"}, "knime:workflow-summary": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840/workflow-summary{?format,includeExecutionInfo}", "isHrefTemplate": true, "output": ["application/json", "application/xml"], "method": "GET"}}, "id": "7851975f-3343-40e9-a970-a4a8806e6840", "discard": false, "configuration": {}, "owner": "<PERSON><PERSON><PERSON><PERSON>", "state": "IDLE", "inputParameters": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "aaa", "snowflake_password": "bbb", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "yyy", "aws_secret_key": "zzz"}}, "notifications": {}, "isOutdated": false, "createdAt": "2021-02-12T22:59:15.846Z[Etc/UTC]", "workflow": "/Users/<USER>/test_snowflake_conn_42", "isSwapped": true, "hasReport": false, "outputValues": {"json-output": {}}, "name": "test_snowflake_conn_42 2021-02-12 22.59.15", "properties": {"com.knime.enterprise.server.executor.requirements": "", "com.knime.enterprise.server.jobpool.size": "0"}}, {"@controls": {"self": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/7994ac41-667b-4293-b7ef-2e7c9827a89c", "method": "GET"}, "collection": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42:jobs", "method": "GET"}, "edit": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/7994ac41-667b-4293-b7ef-2e7c9827a89c", "method": "PUT", "encoding": "Json"}, "knime:delete": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/7994ac41-667b-4293-b7ef-2e7c9827a89c", "method": "DELETE"}, "knime:workflow": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42", "method": "GET"}, "knime:upload": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/{path}:data?from-job=7994ac41-667b-4293-b7ef-2e7c9827a89c", "isHrefTemplate": true, "method": "PUT"}, "knime:execute-job": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/7994ac41-667b-4293-b7ef-2e7c9827a89c{?reset,async,timeout}", "isHrefTemplate": true, "output": ["application/json"], "template": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "aaa", "snowflake_password": "bbb", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "yyy", "aws_secret_key": "zzz"}}, "method": "POST", "encoding": "Json"}, "knime:workflow-summary": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/7994ac41-667b-4293-b7ef-2e7c9827a89c/workflow-summary{?format,includeExecutionInfo}", "isHrefTemplate": true, "output": ["application/json", "application/xml"], "method": "GET"}}, "id": "7994ac41-667b-4293-b7ef-2e7c9827a89c", "discard": false, "configuration": {}, "owner": "<PERSON><PERSON><PERSON><PERSON>", "state": "IDLE", "inputParameters": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "aaa", "snowflake_password": "bbb", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "yyy", "aws_secret_key": "zzz"}}, "notifications": {}, "isOutdated": false, "createdAt": "2021-02-13T06:45:15.013Z[Etc/UTC]", "workflow": "/Users/<USER>/test_snowflake_conn_42", "isSwapped": true, "hasReport": false, "outputValues": {"json-output": {}}, "name": "test_snowflake_conn_42 2021-02-13 06.45.15", "properties": {"com.knime.enterprise.server.executor.requirements": "", "com.knime.enterprise.server.jobpool.size": "0"}}, {"@controls": {"self": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c", "method": "GET"}, "collection": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42:jobs", "method": "GET"}, "edit": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c", "method": "PUT", "encoding": "Json"}, "knime:delete": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c", "method": "DELETE"}, "knime:workflow": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42", "method": "GET"}, "knime:upload": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/{path}:data?from-job=4f8a5c25-1663-40f4-b58c-82e1a4136d7c", "isHrefTemplate": true, "method": "PUT"}, "knime:execute-job": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c{?reset,async,timeout}", "isHrefTemplate": true, "output": ["application/json"], "template": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "ml_admin", "snowflake_password": "trsky&j0912ml", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "********************", "aws_secret_key": "yjoCsBR4jwKyoDsyWRk4I4tggdQpwHUlz82YJ2dh"}}, "method": "POST", "encoding": "Json"}, "knime:workflow-summary": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c/workflow-summary{?format,includeExecutionInfo}", "isHrefTemplate": true, "output": ["application/json", "application/xml"], "method": "GET"}}, "id": "4f8a5c25-1663-40f4-b58c-82e1a4136d7c", "discard": false, "configuration": {}, "owner": "<PERSON><PERSON><PERSON><PERSON>", "state": "EXECUTED", "inputParameters": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "ml_admin", "snowflake_password": "trsky&j0912ml", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "********************", "aws_secret_key": "yjoCsBR4jwKyoDsyWRk4I4tggdQpwHUlz82YJ2dh"}}, "notifications": {}, "isOutdated": false, "createdAt": "2021-02-11T06:04:56.465Z[Etc/UTC]", "startedExecutionAt": "2021-02-12T23:13:30.794Z[UTC]", "workflow": "/Users/<USER>/test_snowflake_conn_42", "isSwapped": true, "hasReport": false, "outputValues": {"json-output": [{"PRODUCT_COUNT": 2}]}, "finishedExecutionAt": "2021-02-12T23:13:31.361Z[Etc/UTC]", "name": "test_snowflake_conn_42 2021-02-11 06.04.56", "properties": {"com.knime.enterprise.server.executor.requirements": "", "com.knime.enterprise.server.jobpool.size": "0"}}, {"@controls": {"self": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/455711fa-12ce-40f1-b0ef-350cbe598014", "method": "GET"}, "collection": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42:jobs", "method": "GET"}, "edit": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/455711fa-12ce-40f1-b0ef-350cbe598014", "method": "PUT", "encoding": "Json"}, "knime:delete": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/455711fa-12ce-40f1-b0ef-350cbe598014", "method": "DELETE"}, "knime:workflow": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42", "method": "GET"}, "knime:upload": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/{path}:data?from-job=455711fa-12ce-40f1-b0ef-350cbe598014", "isHrefTemplate": true, "method": "PUT"}, "knime:execute-job": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/455711fa-12ce-40f1-b0ef-350cbe598014{?reset,async,timeout}", "isHrefTemplate": true, "output": ["application/json"], "template": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "aaa", "snowflake_password": "bbb", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "yyy", "aws_secret_key": "zzz"}}, "method": "POST", "encoding": "Json"}, "knime:workflow-summary": {"href": "http://44.231.120.117:8080/knime/rest/v4/jobs/455711fa-12ce-40f1-b0ef-350cbe598014/workflow-summary{?format,includeExecutionInfo}", "isHrefTemplate": true, "output": ["application/json", "application/xml"], "method": "GET"}}, "id": "455711fa-12ce-40f1-b0ef-350cbe598014", "discard": false, "configuration": {}, "owner": "sankar", "state": "IDLE", "inputParameters": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "aaa", "snowflake_password": "bbb", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "yyy", "aws_secret_key": "zzz"}}, "notifications": {}, "isOutdated": false, "createdAt": "2021-02-13T07:15:53.938Z[Etc/UTC]", "workflow": "/Users/<USER>/test_snowflake_conn_42", "isSwapped": true, "hasReport": false, "outputValues": {"json-output": {}}, "name": "test_snowflake_conn_42 2021-02-13 07.15.53", "properties": {"com.knime.enterprise.server.executor.requirements": "", "com.knime.enterprise.server.jobpool.size": "0"}}], "@namespaces": {"knime": {"name": "http://www.knime.com/server/rels#"}}}