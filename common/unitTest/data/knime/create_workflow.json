{"_class": "com.knime.enterprise.server.rest.api.v4.repository.ent.WorkflowGroup", "@controls": {"self": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF", "method": "GET"}, "up": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>", "method": "GET"}, "knime:permissions": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF:permissions", "method": "GET"}, "knime:download": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF:data{?compressionLevel}", "isHrefTemplate": true, "method": "GET", "encoding": "Raw", "title": "Download repository item"}, "knime:move": {"href": "http://**************:8080/knime/rest/v4/repository/{path}:data?from-repository=/Users/<USER>/Sales_Attr_MF{&move}", "isHrefTemplate": true, "method": "PUT"}, "edit": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF:data", "accept": ["*/*"], "method": "PUT", "encoding": "Raw", "title": "Upload new version"}, "knime:delete": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF{?deletePermanently}", "isHrefTemplate": true, "method": "DELETE"}, "knime:upload": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF/{name}:data", "isHrefTemplate": true, "accept": ["*/*"], "method": "PUT", "encoding": "Raw", "title": "Upload a new item"}, "knime:create": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF/{name}", "isHrefTemplate": true, "method": "PUT", "encoding": "None", "title": "Create a new workflow group"}}, "path": "/Users/<USER>/Sales_Attr_MF", "type": "WorkflowGroup", "children": [], "owner": "<PERSON><PERSON><PERSON><PERSON>", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "createdOn": "2021-02-14T21:35:56Z", "lastUploadedOn": "2021-02-14T21:35:56Z", "@namespaces": {"knime": {"name": "http://www.knime.com/server/rels#"}}}