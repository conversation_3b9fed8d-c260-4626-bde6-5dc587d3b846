{"_class": "com.knime.enterprise.server.rest.api.v4.repository.ent.Data", "@controls": {"self": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF2", "method": "GET"}, "up": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>", "method": "GET"}, "knime:permissions": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF2:permissions", "method": "GET"}, "knime:download": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF2:data{?compressionLevel}", "isHrefTemplate": true, "method": "GET", "encoding": "Raw", "title": "Download repository item"}, "knime:move": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/{path}:data?from-repository=/Users/<USER>/Sales_Attr_MF2{&move}", "isHrefTemplate": true, "method": "PUT"}, "edit": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF2:data", "accept": ["*/*"], "method": "PUT", "encoding": "Raw", "title": "Upload new version"}, "knime:delete": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF2{?deletePermanently}", "isHrefTemplate": true, "method": "DELETE"}, "knime:create-snapshot": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF2:snapshots{?comment}", "isHrefTemplate": true, "method": "POST", "title": "Create a snapshot of this repository item"}, "knime:snapshots": {"href": "http://44.231.120.117:8080/knime/rest/v4/repository/Users/<USER>/Sales_Attr_MF2:snapshots", "method": "GET"}}, "path": "/Users/<USER>/Sales_Attr_MF2", "type": "Data", "size": 0, "owner": "<PERSON><PERSON><PERSON><PERSON>", "@namespaces": {"knime": {"name": "http://www.knime.com/server/rels#"}}}