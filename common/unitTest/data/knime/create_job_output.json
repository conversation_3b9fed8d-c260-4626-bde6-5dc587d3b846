{"@controls": {"self": {"href": "http://**************:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840", "method": "GET"}, "collection": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42:jobs", "method": "GET"}, "edit": {"href": "http://**************:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840", "method": "PUT", "encoding": "Json"}, "knime:delete": {"href": "http://**************:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840", "method": "DELETE"}, "knime:workflow": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42", "method": "GET"}, "knime:upload": {"href": "http://**************:8080/knime/rest/v4/repository/{path}:data?from-job=7851975f-3343-40e9-a970-a4a8806e6840", "isHrefTemplate": true, "method": "PUT"}, "knime:execute-job": {"href": "http://**************:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840{?reset,async,timeout}", "isHrefTemplate": true, "output": ["application/json"], "template": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "aaa", "snowflake_password": "bbb", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "yyy", "aws_secret_key": "zzz"}}, "method": "POST", "encoding": "Json"}, "knime:swap": {"href": "http://**************:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840/swap", "isHrefTemplate": true, "method": "PUT"}, "knime:workflow-summary": {"href": "http://**************:8080/knime/rest/v4/jobs/7851975f-3343-40e9-a970-a4a8806e6840/workflow-summary{?format,includeExecutionInfo}", "isHrefTemplate": true, "output": ["application/json", "application/xml"], "method": "GET"}}, "id": "7851975f-3343-40e9-a970-a4a8806e6840", "discard": false, "configuration": {}, "executorName": "ip-172-19-60-170", "executorIPs": ["*************"], "executorID": "94e071b2-1d1d-466e-818e-22b8b46d2f30", "owner": "<PERSON><PERSON><PERSON><PERSON>", "state": "IDLE", "inputParameters": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "aaa", "snowflake_password": "bbb", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "yyy", "aws_secret_key": "zzz"}}, "notifications": {}, "isOutdated": false, "createdAt": "2021-02-12T22:59:15.846Z[Etc/UTC]", "workflow": "/Users/<USER>/test_snowflake_conn_42", "isSwapped": false, "hasReport": false, "outputValues": {"json-output": {}}, "name": "test_snowflake_conn_42 2021-02-12 22.59.15", "properties": {"com.knime.enterprise.server.executor.requirements": "", "com.knime.enterprise.server.jobpool.size": "0"}, "@namespaces": {"knime": {"name": "http://www.knime.com/server/rels#"}}}