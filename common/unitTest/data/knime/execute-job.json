{"@controls": {"self": {"href": "http://**************:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c", "method": "GET"}, "collection": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42:jobs", "method": "GET"}, "edit": {"href": "http://**************:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c", "method": "PUT", "encoding": "Json"}, "knime:delete": {"href": "http://**************:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c", "method": "DELETE"}, "knime:workflow": {"href": "http://**************:8080/knime/rest/v4/repository/Users/<USER>/test_snowflake_conn_42", "method": "GET"}, "knime:upload": {"href": "http://**************:8080/knime/rest/v4/repository/{path}:data?from-job=4f8a5c25-1663-40f4-b58c-82e1a4136d7c", "isHrefTemplate": true, "method": "PUT"}, "knime:cancel": {"href": "http://**************:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c/execution", "isHrefTemplate": true, "method": "DELETE"}, "knime:workflow-summary": {"href": "http://**************:8080/knime/rest/v4/jobs/4f8a5c25-1663-40f4-b58c-82e1a4136d7c/workflow-summary{?format,includeExecutionInfo}", "isHrefTemplate": true, "output": ["application/json", "application/xml"], "method": "GET"}}, "id": "4f8a5c25-1663-40f4-b58c-82e1a4136d7c", "discard": false, "configuration": {}, "executorName": "ip-172-19-60-170", "executorIPs": ["*************"], "executorID": "94e071b2-1d1d-466e-818e-22b8b46d2f30", "owner": "<PERSON><PERSON><PERSON><PERSON>", "state": "EXECUTING", "inputParameters": {"json-input": {"customer": "NovartisAU", "env": "preprod", "snowflake_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowflake_region": "", "snowflake_endpoint": "snowflakecomputing.com", "snowflake_warehouse": "AKTANADEV_WH_US_WEST_2", "snowflake_db": "RPT_DWPREPROD", "snowflake_schema": "DW_CENTRAL", "snowflake_role": "NONPROD_APPADMIN_ROLE", "snowflake_user": "ml_admin", "snowflake_password": "trsky&j0912ml", "adl_s3location": "s3://aktana-bdp-pfizerus/adl/", "aws_region": "us-east-1", "aws_access_key": "********************", "aws_secret_key": "yjoCsBR4jwKyoDsyWRk4I4tggdQpwHUlz82YJ2dh"}}, "notifications": {}, "isOutdated": false, "createdAt": "2021-02-11T06:04:56.465Z[Etc/UTC]", "workflow": "/Users/<USER>/test_snowflake_conn_42", "isSwapped": false, "hasReport": false, "outputValues": {"json-output": {}}, "finishedExecutionAt": "2021-02-12T23:12:54.141Z[Etc/UTC]", "name": "test_snowflake_conn_42 2021-02-11 06.04.56", "properties": {"com.knime.enterprise.server.executor.requirements": "", "com.knime.enterprise.server.jobpool.size": "0"}, "@namespaces": {"knime": {"name": "http://www.knime.com/server/rels#"}}}