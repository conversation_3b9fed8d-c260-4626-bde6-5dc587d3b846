"Id","Name","<PERSON><PERSON>perName","NamespacePrefix","Description","BusinessProcessId","SobjectType","IsActive","IsPersonType","CreatedById","CreatedDate","LastModifiedById","LastModifiedDate","SystemModstamp"
"012A0000000GbPsIAK","Email_Template_vod","Email_Template_vod",NA,"An Email Template is a Vault document type. Email Templates are root documents that are displayed to <PERSON><PERSON> when sending emails.",NA,"Approved_Document_vod__c",1,0,"00580000001luliAAA","2013-06-29 00:08:47","00580000001luliAAA","2013-06-29 00:08:47","2013-06-29 00:08:47"
"012A0000000GbPuIAK","Email_Activity_vod","Email_Activity_vod",NA,"This segregates all email activity, eg opens, clicks, bounces, unsubs, etc. This activity is all populated via API from the Email Engine. The email engine will persist CRM org_id and sent_email_id values on each activity for correlation.",NA,"Email_Activity_vod__c",1,0,"00580000001luliAAA","2013-06-29 00:08:48","00580000001luliAAA","2013-06-29 00:08:48","2013-06-29 00:08:48"
"012A00000019noOIAQ","Email_Template_vod","Email_Template_vod",NA,"Used to store email templates that are available for use on an Event.",NA,"EM_Event_Material_vod__c",1,0,"00580000001luliAAA","2016-04-09 01:32:40","00580000001luliAAA","2016-04-09 01:32:40","2016-04-09 01:32:40"
