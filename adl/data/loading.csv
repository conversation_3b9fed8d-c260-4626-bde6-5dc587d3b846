table_number|table_name|sql_query|delta_column|delta|full|filter|columns_specific_to_client|count_records_from_meta_data|which_date_column_used_for_partition|use_partition|parition_keys_columns|source_table
1|visits|" select  ip.messageReaction, ip.actionOrder,ip.quantity, ip.suggestionReferenceId, ip.matchedSuggestionProduct, ip.productInteractionTypeId,ip.matchedSuggestionMessage, ip.suggestionInferredAt, ip.physicalMessageUID,i.interactionId,i.externalId,i.timeZoneId,year(startDateTime) as interactionyear,
month(startDateTime) as interactionmonth,DAYOFMONTH(startDateTime) as interactionday,
i.startDateTime,i.startDateLocal, i.interactionTypeId,it.interactionTypeName,i.repActionTypeId,raty.repActionTypeName,i.duration, i.wasCreatedFromSuggestion, 
i.isCompleted, i.isDeleted, i.createdAt,i.updatedAt,i.repId,ia.accountId,ip.productId,i.facilityId,
ia.isDeleted as interaction_account_isdeleted,visit.Id as raw_cs_id,
${call_date},${channel},${call_date_time} ,visit.Account_vod__c as accountuid,
visit.Address_Line_1_vod__c, visit.State_vod__c,visit.City_vod__c,visit.Zip_4_vod__c, visit.Zip_vod__c, visit.Address_Line_2_vod__c,${call_notes},
visit.Call_Type_vod__c,${attendee},${product_detail},${submitted_mobile}, 
acm.channelId as reportedChannelId, acm.channelName as reportedChannelName  
from interaction i
inner join interactiontype it 
on i.interactionTypeId=it.interactionTypeId
inner join  repactiontype raty on i.repActionTypeId=raty.repActionTypeId
left join call2_vod__c visit 
on i.externalId=visit.Id
inner join interactionaccount ia 
on i.interactionId=ia.interactionId
inner join interactionproduct ip 
on i.interactionId=ip.interactionId 
left join rep r
on i.repId=r.repId
left join actionchannelmap acm
on r.repTypeId=acm.actorTypeId and i.repActionTypeId=acm.actionTypeId
where  1=1 and  (i.interactionTypeId =4 or i.interactionTypeId =12 or i.interactionTypeId =30) and i.isDeleted=0 "|updatedAt|i.updatedAt>='${partitioned_date}'|1=1|i.startDateTime >= '${startdate}' and i.startDateTime <= '${enddate}' and  ip.productId ${prodcode}|1|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${proddbname}' and table_name = 'Interaction') 
  ) xxxx"|Interaction.StartDateTime|1|interactionyear,interactionmonth|interaction,interactiontype,repactiontype,call2_vod__c,interactionaccount,interactionproduct
2|emails|"  select itp.actionOrder, itp.quantity, itp.suggestionReferenceId, 
itp.productInteractionTypeId,itp.matchedSuggestionProduct, itp.matchedSuggestionMessage, itp.suggestionInferredAt, itp.physicalMessageUID, itp.messageReaction, t7.physicalMessageDesc, t7.physicalMessageName, t7.lastPhysicalMessageUID, t7.physicalMessageId,i.interactionId,i.externalId,i.timeZoneId,year(startDateTime) as interactionyear,
month(startDateTime) as interactionmonth,DAYOFMONTH(startDateTime) as interactionday,CONCAT(cast(year(startDateTime) as STRING), '', cast(quarter(startDateTime) as STRING)) as interactionyearquarter,
i.startDateTime,i.startDateLocal, i.interactionTypeId,it.interactionTypeName,i.repActionTypeId,raty.repActionTypeName,i.duration, i.wasCreatedFromSuggestion, 
i.isCompleted, i.isDeleted, i.createdAt,i.updatedAt
,i.repId,ia.accountId,itp.productId,i.facilityId,ia.isDeleted as interaction_account_isdeleted,email.Id as raw_cs_id,${email_date},email.Capture_Datetime_vod__c,
email.Account_vod__c as accountuid,${acct_email},
${email_config}, ${email_subject},
${email_content}, ${email_parsed_content},t7.email_Message_Id, t7.email_messageChannelId, 
t7.email_messageTopicId,t7.email_messageTopicName, 
t7.email_messagename, t7.email_messagedescription,
${email_open}, ${email_open_count},${email_click_count}, ${prod_display},
email.Product_vod__c,${send_email} , email.Status_vod__c, ${email_call2},
acm.channelId as reportedChannelId, acm.channelName as reportedChannelName 
from interaction i
inner join interactiontype it 
on i.interactionTypeId=it.interactionTypeId
inner join interactionproduct itp 
on i.interactionId=itp.interactionId
inner join message m on (m.messageId = itp.messageId) 
join messagechannel mc on (m.messageChannelId = mc.messageChannelId) 
inner join  repactiontype raty on i.repActionTypeId=raty.repActionTypeId
left join sent_email_vod__c email 
on i.externalId=email.Id
left join 
( select t3.physicalMessageDesc, physicalMessageName, lastPhysicalMessageUID, physicalMessageId, t1.messageId as email_Message_Id, t1.messageChannelId as email_messageChannelId,  COALESCE(t1.messageTopicId,t2.messageTopicId) as email_messageTopicId,t2.messageTopicName as email_messageTopicName,  t1.messageName as email_messagename, t1.messageDescription as email_messagedescription from message t1 left join messagetopic t2  on t1.messageTopicId=t2.messageTopicId left join physicalmessage t3 on  (t3.messageTopicId=t2.messageTopicId and t1.messageId=t3.messageId  AND t1.productId=t3.productId  AND t1.lastPhysicalMessageUID=t3.externalId )) t7 on t7.email_Message_Id=itp.messageId 
inner join interactionaccount ia
on i.interactionId=ia.interactionId 
left join rep r
on i.repId=r.repId
left join actionchannelmap acm
on r.repTypeId=acm.actorTypeId and i.repActionTypeId=acm.actionTypeId 
where  1=1 and i.interactionTypeId =11 and i.isDeleted=0 and i.startDateTime is not null and i.repActionTypeId=12 and mc.messageChannelName = 'RTE' "|updatedAt|i.updatedAt>='${partitioned_date}'|1=1|i.startDateTime >= '${startdate}' and i.startDateTime <= '${enddate}' and  itp.productId ${prodcode}|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${proddbname}' and table_name = 'Interaction') 
  ) xxxx"|Interaction.StartDateTime|1|interactionyear,interactionmonth,interactionday|interaction,interactiontype,interactionproduct,message,repactiontype,sent_email_vod__c,messagetopic,physicalmessage,interactionaccount
3|product_old|"select distinct  ip.interactionId,ip.externalId,ip.isCompleted,year(I.startDateTime) as interactionyear,
month(I.startDateTime) as interactionmonth,DAYOFMONTH(I.startDateTime) as interactionday,I.updatedAt ,
pit.productInteractionTypeId,pit.productInteractionTypeName,ip.physicalMessageUID,
t7.messageId,t7.messageName,t7.messageDescription,t7.physicalMessageId,t7.lastPhysicalMessageUID,t7.physicalMessageName,
t7.physicalMessageDesc,t7.messageTopicId,ip.messageReaction,ip.actionOrder,ip.quantity,ip.suggestionReferenceId,ip.matchedSuggestionProduct,
ip.matchedSuggestionMessage,ip.suggestionInferredAt,ip.productId,p.productName 
from interactionproduct ip
inner join product p 
on ip.productId =p.productId 
inner join interaction I
on ip.interactionId=I.interactionId 
inner join productinteractiontype pit  
on ip.productInteractionTypeId=pit.productInteractionTypeId
left join 
(
select 
COALESCE(t1.messageId,t2.messageId) as messageId,t1.messageName ,t1.messageDescription,t2.physicalMessageId ,
t1.lastPhysicalMessageUID,COALESCE(t1.productId,t2.productId) as productId ,t2.physicalMessageName,t2.physicalMessageDesc,
COALESCE (t1.messageTopicId,t2.messageTopicId) as messageTopicId
from (
select m.messageId,m.messageName,m.messageDescription,m.lastPhysicalMessageUID,productId,
tmp.messageTopicId,tmp.messageTopicName 
from message m 
left join messagetopic tmp 
on m.messageTopicId=tmp.messageTopicId 
) t1 
left join ( 
select messageId ,physicalMessageId,pm.physicalMessageName ,pm.physicalMessageDesc,pm.productId,pm.externalId, 
tmp.messageTopicId,tmp.messageTopicName 
from physicalmessage pm  
left join messagetopic tmp 
on pm.messageTopicId=tmp.messageTopicId 
) t2 
on t1.messageId=t2.messageId and t1.productId=t2.productId and t1.lastPhysicalMessageUID=t2.externalId
) t7
on ip.messageId=t7.messageId and t7.productId=ip.productId
where 1=1 and I.startDateTime is not null "|updatedAt|I.updatedAt>='${partitioned_date}'|1=1|I.startDateTime >= '${startdate}'  and I.startDateTime <= '${enddate}' and  ip.productId ${prodcode}|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${proddbname}' and table_name = 'InteractionProduct') 
  ) xxxx"|Interaction.StartDateTime|1|interactionyear,interactionmonth,interactionday|
4|suggestions|"select  t1.suggestionReferenceId,t1.reportedInteractionUID, t1.inferredInteractionUID, t1.interactionUID, t1.interactionId,
t1.repId, t1.repUID, t1.repName,t1.detailRepActionTypeUID, t1.detailRepActionName,t1.repTeamId, t1.repTeamUID, t1.repTeamName, 
t1.detailRepActionTypeId,t1.repLatitude, t1.repLongitude,
year(t1.suggestedDate) as suggestedyear,month(t1.suggestedDate) suggestedmonth,day(t1.suggestedDate) as suggestedday,t1.suggestedDate,t1.startDateLocal, 
t1.productId, t1.productUID, t1.productName,
t1.suggestionLifeCycleId, t1.runId, t1.runUID, t1.runGroupId,t1.isHighestRunidForDay,
t1.seConfigId, t1.seConfigName,  
  t1.repCreatedAt, t1.accountId, t1.accountUID, t1.accountName,  
  t1.runRepDateSuggestionId, t1.runRepDateSuggestionDetailId, 
  t1.messageId, t1.messageUID, t1.messageName, t1.suggestionUID, 
  t1.lastViewedAt, t1.viewedAt, t1.viewedDuration, t1.actionTaken, t1.actionTaken_dt, 
t1.isSuggestionCompleted,t1.isCompleted,t1.inferredAt, t1.isFirstSuggestedDate, t1.isLastSuggestedDate, t1.startDateTime,t1.completedAt
,t1.isSuggestionCompletedDirect, t1.isSuggestionCompletedInfer, t1.isSuggestionDismissed,
t1.dismissedAt, t1.dismissCount,t1.dismissReasonType, t1.dismissReason, t1.dismissReason_dt, t1.isSuggestionActive, 
t1.facilityLatitude, t1.facilityLongitude, t1.facilityGeoLocationString,  t1.territoryCityName,
t1.districtName, t1.territoryId, t1.territoryName, t1.regionName, t1.regionGroup, t1.lastPublishedAt, t1.createdAt, t1.updatedAt, 
t1.suggestionDriver, t1.timeoffday, t1.holiday_weekend_Flag,t1.crmFieldName, 
t1.reasonText, t1.reasonRank, t1.runRepDateSuggestionReasonId, 
t1.repRole, t1.repBag, t1.dmUID, t1.dmName, 
t1.isREMix, t1.isDSESpark, acm.channelId as suggestedChannelId, acm.channelName as suggestedChannelName
FROM rpt_suggestion_delivered_stg t1
left join rep r
on r.repId = t1.repId
left join actionchannelmap acm
on acm.actorTypeId = r.repTypeId and t1.detailRepActionTypeId = acm.actionTypeId
where 1=1 and t1.suggestedDate is not null  "|suggestedDate|t1.suggestedDate>='${partitioned_date}'|1=1|t1.suggestedDate>= '${startdate}'  and t1.suggestedDate <= '${enddate}' and  t1.productId ${prodcode}|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${stgdbname}' and table_name = 'RPT_Suggestion_Delivered_stg') 
  ) xxxx"|lillyusprod_stage.RPT_Suggestion_Delivered_stg.suggestedDate|1|suggestedyear,suggestedmonth,suggestedday|rpt_suggestion_delivered_stg
5|dse_score|"select 
 year(d1.suggestedDate) as suggestedyear
,month(d1.suggestedDate) as suggestedmonth
,day(d1.suggestedDate) as suggestedday
,d1.repId, d3.accountId
,d1.suggestedDate
,d.runId,d1.runRepDateId
,d.startDateTime
,d.startDateLocal
,d.seConfigId
,d.runGroupId
,d.numSuggestibleReps
,d.numSuggestibleAccounts
,d.numEvaluatedAccounts
,d.numErrorsAndWarnings
,d3.isSuggestionCritical 
,d3.suggestionPriorityScore 
,d3.suggestionReferenceId
,d3.runRepDateSuggestionId
,d3.suggestionRank
,d3.isEligibleForLoadingIntoCRM
,d3.isEligibleForLoadingIntoMAS
,d3.actionValue
,d3.isSupersededByTrigger
,d3.primaryRepActionTypeId
,d3.suggestedRepActionTypeUID 
,d3.suggestedChannelId 
,c.channelName as suggestedChannelName
,d4.detailRepActionTypeId
,d4.detailRepActionTypeUID
,d4.productId
,d4.messageTopicId 
,acm.channelId as suggChannelId
,d3.updatedAt as runRepDateSuggestionUpdatedAt
from dserun d inner 
join dserunrepdate d1 on d1.runUID = d.runUID
 inner 
 join dserunrepdatesuggestion d3 on d1.runRepDateId=d3.runRepDateId 
 inner join rep on rep.repId = d1.repId
inner join actionchannelmap acm
on rep.repTypeId=acm.actorTypeId and d3.primaryRepActionTypeId=acm.actionTypeId
 inner 
 join dserunrepdatesuggestiondetail d4 on d3.runRepDateSuggestionId=d4.runRepDateSuggestionId 
 left join channel c on 
 d3.suggestedChannelId=c.channelId where d3.isActive = 1"|suggestedDate|t1.suggestedDate>='${partitioned_date}'|1=1|d.startDateTime>= '${startdate}'  and d.startDateTime <= '${enddate}' and  d4.productId ${prodcode}|0|"( select count(*) as TABLE_ROWS from `${proddbname}`.DSERun d
inner join `${proddbname}`.DSERunRepDate d1 
on (d1.runId is not null and d.runId=d1.runId) or (d1.runUID is not null and d.runUID=d1.runUID)
inner join  `${proddbname}`.DSERunRepDateSuggestion d3 
on d1.runRepDateId=d3.runRepDateId and d3.accountId=d2.accountId
inner join `${proddbname}`.DSERunRepDateSuggestionDetail d4 on
d3.runRepDateSuggestionId=d4.runRepDateSuggestionId,(SELECT @row_number:=0) AS t
 ) xxxx
"|lillyusprod.DSERunRepDate.suggestedDate|1|suggestedyear,suggestedmonth,suggestedday|dserun,dserunrepdate,dserunrepdatesuggestion,dserunrepdatesuggestiondetail
6|strategy_target|"select t1.strategyTargetId,t1.targetsPeriodId,t3.targetingLevelId,t1.accountGroupId,
t1.accountId as Target_Account, t1.repId as RepID_Associated, 
t1.repTeamId as RepTeamID_Associated, productId,
t1.interactionTypeId as interactionTypeId,t1.productInteractionTypeId as Target_productInteractionTypeId,
t1.target,t1.targetMin,t1.targetMax,t1.visitActionOrderMax,t1.visitActionOrderMin,t1.relativeValue,
t2.startDate,t2.endDate, 
year(t2.startDate) as start_target_year, month(t2.startDate) as start_target_month,day(t2.startDate) as start_target_day,
year(t2.endDate) as end_target_year, month(t2.endDate) as end_target_month,day(t2.endDate) as end_target_day
,t3.externalId as target_level_reach_way, t3.targetingLevelsPeriodId,t3.targetingLevelName,
t3.facilityRule,t3.accountGroupRule,t3.accountRule,t3.repTeamRule,t3.repRule,
t3.productRule,t3.messageTopicRule,t3.messageRule
from strategytarget t1 
inner join targetsperiod t2 
on t1.targetsPeriodId=t2.targetsPeriodId
inner join targetinglevel t3 
 on t1.targetingLevelId=t3.targetingLevelId
where 1=1 "|not_available|1=1|1=1|t2.startDateTime>= '${startdate}'  and t2.startDateTime <= '${enddate}' and  productId ${prodcode}|0|"  ( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema= '${proddbname}' and table_name = 'StrategyTarget') 
  ) xxxx"|lillyusprodTargetsPeriod.startDate|1|start_target_year,start_target_month,start_target_day|strategytarget,targetsperiod,targetinglevel
7|rep_account_assignment|"SELECT *
FROM  repaccountassignment "|not_available|1=1|1=1|1=1|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${proddbname}' and table_name = 'RepAccountAssignment') 
  ) xxxx"||0||repaccountassignment
8|rep_product_authorization|"select ra.* from repproductauthorization ra
where 1=1 "|not_available|1=1|1=1|1=1|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${proddbname}' and table_name = 'RepProductAuthorization') 
  ) xxxx"||0||repproductauthorization
9|account|"SELECT t1.*, t2.Gender_vod__c as hcpGender, t2.Credentials_vod__c as hcpCred, t2.Specialty_1_vod__c as hcpSpec, t2.Specialty_2_vod__c as hcpSpec2, ${tenure}, t5.latitude, t5.longitude, t5.geoLocationString, t5.timeZoneId, t6.accountTypeName, 'QA_test' as newColumnQA
from account_dse t1
left join account_cs t2 
on t1.externalId=t2.Id 
left join facility t5 
on t1.facilityId=t5.facilityId 
left join accounttype t6
on t1.accountTypeId=t6.accountTypeId "|not_available|1=1|1=1|1=1|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${proddbname}' and table_name = 'Account') 
  ) xxxx"||0||account_dse
10|rep|"SELECT 
r.repId, r.repTypeId, r.repName ,r.externalId as repUID,r.timeZoneId as rep_timeZoneId,
 r.isActivated as rep_isActivated,r.isDeleted as rep_isDeleted,r.seConfigId as rep_seConfigId,
 t3.avgLatCurrentAccounts as rep_avgLatCurrentAccounts,t3.avgLongCurrentAccounts as rep_avgLongCurrentAccounts,t3.avgMilesCurrentAccounts as avgMilesCurrentAccounts,
t3.maxMilesCurrentAccounts as rep_maxMilesCurrentAccounts,t3.numCurrAccounts as rep_numCurrAccounts, t3.numCurrAccountsWithLatLong as rep_numCurrAccountsWithLatLong,
t3.numCurrAccountsWithValidLatLong as rep_numCurrAccountsWithValidLatLong,
t4.repTypeName 
  from rep r
left join replocation t3
on r.repId=t3.repId 
left join reptype t4 
on r.repTypeId=t4.repTypeId "|not_available|1=1|1=1|1=1|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${proddbname}' and table_name = 'Rep') 
  ) xxxx"||0||rep,replocation
11|rep_team_rep|" select rr.repId,r.repTeamId,t.* from akt_replicense_arc t 
  left join repteam r
  on t.cluster=r.repTeamName
  left join rep rr 
  on t.externalId=rr.externalId  "|not_available|1=1|1=1|1=1|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${stgdbname}' and table_name = 'AKT_RepLicense_arc') 
  ) xxxx"||0||akt_replicense_arc,repteam,rep
12|approved_document|select ra.* from approved_document_vod__c ra|not_available|1=1|1=1|1=1|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${csdbname}' and table_name = 'Approved_Document_vod__c') 
  ) xxxx"||0||approved_document_vod__c
13|accountproduct|" select ra.*,year(ra.createdAt) as recordcreatedyear,
month(ra.createdAt) as recordcreatedmonth,DAYOFMONTH(ra.createdAt) as recordcreatedday from accountproduct ra"|createdAt|ra.createdAt>='${partitioned_date}'|1=1|"DATE_FORMAT(ra.createdAt, ""%Y-%m-%d"")>= '${startdate}' and DATE_FORMAT(ra.createdAt, ""%Y-%m-%d"") <= '${enddate}' and  ra.productId ${prodcode}"|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${proddbname}'  and table_name = 'AccountProduct') 
  ) xxxx"||1|recordcreatedyear,recordcreatedmonth,recordcreatedday|accountproduct
14|message_topic_email_learned|"select t.messageUID ,t.documentDescription ,
 t.emailSubject,t.emailTopicId ,t.emailTopicName 
 ,m.messageId,m.messageChannelId,
 m.messageDescription,m.messageName,m.messageTopicId,m.repActionChannelId,m.lastPhysicalMessageUID from  akt_message_topic_email_learned t 
left join message m 
on t.messageUID =m.externalId "|not_available|1=1|1=1|1=1|0|"( 
SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,DATA_LENGTH
  FROM information_schema.tables 
  WHERE (table_schema='${stgdbname}' and table_name = 'AKT_Message_Topic_Email_Learned') 
  ) xxxx"||0||
15|product|"select * from product"|not_available|1=1|1=1|1=1|0|""||0||
16|channel|"select c.*, ct.channelTypeName, ct.externalId as channelTypeUID, cc.channelCategoryName, cc.externalId as channelCategoryUID, adm.activityDeliveryModeName, adm.externalId as activityDeliveryModeUID, sdm.suggestionDeliveryModeName, sdm.externalId as suggestionDeliveryModeUID from channel c 
left join channeltype ct 
on c.channelTypeId=ct.channelTypeId 
left join channelcategory cc 
on c.channelCategoryId=cc.channelCategoryId 
left join activitydeliverymode adm
on c.activityDeliveryModeId=adm.activityDeliveryModeId 
left join suggestiondeliverymode sdm 
on c.suggestionDeliveryModeId=sdm.suggestionDeliveryModeId"|not_available|1=1|1=1|1=1|0|""||0||
17|rep_action_type|"select raty.*, ag.actionGroupName, ag.externalId as actionGroupUID from repactiontype raty 
left join actiongroup ag 
on raty.actionGroupId=ag.actionGroupId"|not_available|1=1|1=1|1=1|0|""||0||
18|emails_ref|"select er.* from emails_ref er "|not_available|1=1|1=1|1=1|0|""||0|sysmodstampyear,sysmodstampmonth,sysmodstampday|