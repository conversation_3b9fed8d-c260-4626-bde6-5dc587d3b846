ADLStandardFieldsName|SourceFieldsName|DataType|Customer|CustomerId|Standard|StandardGroup|FieldDescription|SourceTables|LevelOfGranularity|Visible|comments
recordId|systemGeneratedId||All|0|1|1|Generated ID for uniqueness of records||0|1|for V24
accountId1|allAccountId||All|0|1|1|accountId who rep interacted through one of the interaction type. It is only present if there is interactionId.|Account|0|1|
effectiveDay|allDay||All|0|1|1|day of the month when the interaction was completed or suggestion sent.|interaction suggestion|0|1|
effectiveDate|allEffectivedate||All|0|1|1|Date when the interaction was completed or suggestion was sent.|interaction suggestion|0|1|
effectiveYearMonth|allEffectiveyearmonth||All|0|1|1|yearMonth when the interaction was completed or suggestion was sent.|interaction suggestion|0|1|
effectiveMonth|allMonth||All|0|1|1|Month when the interaction was completed or suggestion was sent.|interaction suggestion|0|1|
repId|allRepId||All|0|1|1|repID column not null. COALESCE(suggestion_repIdInteraction_repid)|rep|0|1|
repAccount|allRepAccount||All|0|1|1|unique rep_account pair not null|codeGenerated|0|1|
effectiveYear|allYear||All|0|1|1|year when the interaction was completed or suggestion sent.|interaction suggestion|0|1|
callAddressLine1|callAddressLine1||All|0|1|1|visit address|visits|0|1|
callAddressLine2|callAddressLine2||All|0|1|1|visit address|visits|0|1|
callAttendeeType|callAttendeeType||All|0|1|1|visit attendee type|visits|0|1|
callDate|callCallDate||All|0|1|1|visit date|visits|0|1|
callDatetime|callCallDatetime||All|0|1|1|visit datetime|visits|0|1|
callType|callCallType||All|0|1|1|visit type|visits|0|1|
callCity|callCity||All|0|1|1|visit city|visits|0|1|
callDetailedProducts|callDetailedProducts||All|0|1|1|product which rep spoke about. can be multiple products.|visits|0|1|
callNextCallNotes|callNextCallNotes||All|0|1|1|visit notes.|visits|0|1|
callState|callState||All|0|1|1|visit state.|visits|0|1|
callSubmittedByMobile|callSubmittedByMobile||All|0|1|1|visit submitted by Mobile? copystorm field.|visits|0|1|
callZip4|callZip4||All|0|1|1|zip code of facility/account|visits|0|1|
callZip|callZip||All|0|1|1|zip code of facility/account|visits|0|1|
suggestionDismissReason|suggestionDismissReason||All|0|1|1|Reason for dismissing suggestion|RPT_Suggestion_Delivered_stg|0|1|
dseRunStartDateLocal|dseRunStartDateLocal||All|0|1|1|dse run date|dse_score|0|1|
dseRunStartDateTime|dseRunStartDateTime||All|0|1|1|dse run datetime|dse_score|0|1|
dseAllTargetsProgressText|dseAllTargetsProgressText||All|0|1|1|?|dse_score|0|1|
dseCurrentCompletionsPeriodTargetsProgressStatusId|dseCurrentCompletionsPeriodTargetsProgressStatusId||All|0|1|1|?|dse_score|0|1|
dseEvaluatedCompletionsPeriodTargetsProgressStatusId|dseEvaluatedCompletionsPeriodTargetsProgressStatusId||All|0|1|1|?|dse_score|0|1|
dseIsAccountCritical|dseIsAccountCritical||All|0|1|1|flag from dse suggesting this account is critical|dse_score|0|1|
dseIsSuggestionCritical|dseIsSuggestionCritical||All|0|1|1|flag from dse suggesting this suggestion is critical|dse_score|0|1|
dseSupersededByTrigger|dseIsSupersededByTrigger||All|0|1|1|flag from dse indicating if the suggestion superseded by trigger.|dse_score|0|1|
dseNumEvaluatedAccounts|dseNumEvaluatedAccounts||All|0|1|1|?|dse_score|0|1|
dseNumSuggestibleAccounts|dseNumSuggestibleAccounts||All|0|1|1|?|dse_score|0|1|
dseNumSuggestibleReps|dseNumSuggestibleReps||All|0|1|1|?|dse_score|0|1|
dseNumWorkDaysOverdue|dseNumWorkDaysOverdue||All|0|1|1|?|dse_score|0|1|
dseRunId|dseRunId||All|0|1|1|unique id for dse run.|dse_score|0|1|
dseRunRepDateSuggestionId|dseRunRepDateSuggestionId||All|0|1|1|unique id for dse run rep date suggestion|dse_score|0|1|
dseSuggestionPriorityScore|dseSuggestionPriorityScore||All|0|1|1|dse allocates the priority score for suggestion|dse_score|0|1|
dseTargetDrivenEvaluationInfo|dseTargetDrivenEvaluationInfo||All|0|1|1|?|dse_score|0|1|
accountEmail|emailAccountEmail||All|0|1|1|email address to which email was sent|emails|0|1|
emailCall2|emailCall2||All|0|1|1|are there any calls/visit link to this email?|emails|0|1|
emailCaptureDatetime|emailCaptureDatetime||All|0|1|1|email capture datetime|emails|0|1|
emailClickCount|emailClickCount||All|0|1|1|response by someone to whom email was sent. how many times did they click on the email?|emails|0|1|
emailConfigValues|emailEmailConfigValues||All|0|1|1|content of the email.|emails|0|1|
emailContent|emailEmailContent||All|0|1|1|content of the email.|emails|0|1|
emailSentDate|emailEmailSentDate||All|0|1|1|email sent date|emails|0|1|
emailSubject|emailEmailSubject||All|0|1|1|email subject|emails|0|1|
emailOpenCount|emailOpenCount||All|0|1|1|response by someone to whom email was sent. How many times Did they open email?|emails|0|1|
emailOpened|emailOpened||All|0|1|1|response by someone to whom email was sent. Did they open email?|emails|0|1|
emailProductDisplay|emailProductDisplay||All|0|1|1|product which rep spoke about in the email. can be multiple products.|emails|0|1|
emailProduct|emailProduct||All|0|1|1|product which rep spoke about in the email. can be multiple products.|emails|0|1|
emailSender|emailSenderEmail||All|0|1|1|rep who sent an email|emails|0|1|
emailStatus|emailStatus||All|0|1|1|status of the email. sent or delivered etc...|emails|0|1|
emailAccountUID|emailAccountuid||All|0|1|1|accountUID link to the email.|emails|0|1|
emailOpenScore|emailOpenScore||All|0|1|1|cri for emails open.|cri_scores|0|1|
interactionDowInMonth|interactionDOWInMonth||All|0|1|1|?|interaction|0|1|
interactionDayOfYear|interactionDayOfYear||All|0|1|1|?|interaction|0|1|
interactionDuration|interactionDuration||All|0|1|1|?|interaction|0|1|
interactionIsWeekend|interactionIsWeekend||All|0|1|1|?|interaction|0|1|
interactionQuarter|interactionQuarter||All|0|1|1|?|interaction|0|1|
interactionWeekDayName|interactionWeekDayName||All|0|1|1|?|interaction|0|1|
interactionWeekOfMonth|interactionWeekOfMonth||All|0|1|1|?|interaction|0|1|
interactionWeekOfYear|interactionWeekOfYear||All|0|1|1|?|interaction|0|1|
interactionYearMonth|interactionYearMonth||All|0|1|1|?|interaction|0|1|
interactionAccountId|interactionAccountId||All|0|1|1|interacted accountID|interaction|0|1|
interactionCreatedAt|interactionCreatedAt||All|0|1|1|when was the interaction created?|interaction|0|1|
interactionFacilityId|interactionFacilityId||All|0|1|1|what was the facilityID where interaction was happened?|interaction|0|1|
interactionIsCompleted|interactionIsCompleted||All|0|1|1|is interaction complete? This flag is from rep's point of view.  Did rep send an email or did rep complete the visit?|interaction|0|1|
interactionIsDeleted|interactionIsDeleted||All|0|1|1|is interaction deleted.|interaction|0|1|
interactionRepId|interactionRepId||All|0|1|1|interacted repId|interaction|0|1|
interactionRepAccount|interactionRepAccount||All|0|1|1|interacted rep and account pair|interaction|0|1|
interactionStartDateLocal|interactionStartDateLocal||All|0|1|1|interacted date|interaction|0|1|
interactionStartDateTime|interactionStartDateTime||All|0|1|1|interacted datetime|interaction|0|1|
interactionTimeZoneId|interactionTimeZoneId||All|0|1|1|interacted timezoneid|interaction|0|1|
interactionUpdatedAt|interactionUpdatedAt||All|0|1|1|interaction updatedAt|interaction|0|1|
interactionWasCreatedFromSuggestion|interactionWasCreatedFromSuggestion||All|0|1|1|was interaction created from suggestion?|interaction|0|1|
SuggestionDowInMonth|suggestionDOWInMonth||All|0|1|1|suggestion day of week in Month|suggestion|0|1|
SuggestionDayOfYear|suggestionDayOfYear||All|0|1|1|suggested day of year|suggestion|0|1|
SuggestionIsWeekend|suggestionIsWeekend||All|0|1|1|suggested day is weekend?|suggestion|0|1|
SuggestionQuarter|suggestionQuarter||All|0|1|1|suggested quarter.|suggestion|0|1|
SuggestionWeekDayName|suggestionWeekDayName||All|0|1|1|suggested weekday name.|suggestion|0|1|
SuggestionWeekOfMonth|suggestionWeekOfMonth||All|0|1|1|suggested wek of month.|suggestion|0|1|
SuggestionWeekOfYear|suggestionWeekOfYear||All|0|1|1|suggested week of year.|suggestion|0|1|
suggestionRepAccount|suggestionRepAccount||All|0|1|1|suggested rep_account pair.|suggestion|0|1|
accountId|accountId||All|0|1|1|? we have many accountID. should filter and keep only one.|?|0|1|
accountName|accountName||All|0|1|1|?|?|0|1|
avgMilesCurrentAccounts|avgMilesCurrentAccounts||All|0|1|1|?|?|0|1|
cadenceScoreStd|cadenceScoreStd||All|0|1|1|cri cadence score|cri_scores|0|1|
channelScore|channelScore||All|0|1|1|cri channel score|cri_scores|0|1|
cluster|cluster||All|0|1|1|rep team name|rep_team_rep|0|1|
createdAt|createdAt||All|0|1|1|?|?|0|1|
emailMessageId|emailMessageId||All|0|1|1|messageid for every email|emails|0|1|
emailMessageChannelId|emailMessageChannelId||All|0|1|1|?|emails|0|1|
emailMessageTopidId|emailMessageTopicId||All|0|1|1|message topic id for every email|emails|0|1|
emailMessageTopicName|emailMessageTopicName||All|0|1|1|message topic name for every email.|emails|0|1|
emailMessageDescription|emailMessagedescription||All|0|1|1|message description for every email.|emails|0|1|
emailMessageName|emailMessagename||All|0|1|1|message name for every email.|emails|0|1|
externalId|externalId||All|0|1|1|externalID which links to the copystorm.|?|0|1|
facilityId|facilityId||All|0|1|1|facilityID is hospitalID.|?|0|1|
index|index||All|0|1|1|cri index score.|cri_scores|0|1|
interactionId|interactionId||All|0|1|1|unique interactionID for every interaction.|interaction|0|1|
interactionTypeId|interactionTypeId||All|0|1|1|id for every unique type of interaction.|interaction|0|1|
interactionTypeName|interactionTypeName||All|0|1|1|name for every uinique type of interaction.|interaction|0|1|
interactionAccountIsDeleted|interactionAccountIsdeleted||All|0|1|1|is interaction account deleted.|interaction|0|1|
interactionDay|interactionday||All|0|1|1|day when the interaction was completed.|interaction|0|1|
interactionMonth|interactionmonth||All|0|1|1|month when the interaction was completed.|interaction|0|1|
interactionYear|interactionyear||All|0|1|1|year when the interaction was completed.|interaction|0|1|
isDeleted|isDeleted||All|0|1|1|?|interaction|0|1|
messageId|messageId||All|0|1|1|? have multiple mesageId in the columns.|?|0|1|
messageReaction|messageReaction||All|0|1|1|?|?|0|1|
messageTopicId|messageTopicId||All|0|1|1|?|?|0|1|
physicalMessageDesc|physicalMessageDesc||All|0|1|1|physical message desc|?|0|1|
physicalMessageUID|physicalMessageUID||All|0|1|1|physical message UID|?|0|1|
productInteractionTypeId|productInteractionTypeId||All|0|1|1|Product interaction Type|?|0|1|
productInteractionTypeName|productInteractionTypeName||All|0|1|1|Product interaction typename|?|0|1|
quantity|quantity||All|0|1|1|quantity describes about product|?|0|1|
rawCopyStormId|rawCsId||All|0|1|1|raw copy storm Id|?|0|1|
recordclass|recordclass||All|0|1|1|Record class defines every row|recordclassCsv|0|1|
recordclassId|recordclassId||All|0|1|1|record class ID unique for every record clas|recordclassCsv|0|1|
recordclassesId|recordclassesId||All|0|1|1|?? duplicates|recordclassCsv|0|1|
repActionTypeId|repActionTypeId||All|0|1|1|rep action type Id|rep_team_rep|0|1|
repActionTypeName|repActionTypeName||All|0|1|1|rep action type name|rep_team_rep|0|1|
repName|repName||All|0|1|1|interacted repname|rep_team_rep|0|1|
repTeamId|repTeamId||All|0|1|1|rep team Id|rep_team_rep|0|1|
repAvgLatCurrentAccounts|repAvgLatCurrentAccounts||All|0|1|1|rep avg lat current Accounts|rep_team_rep|0|1|
repAvgLongCurrentAccounts|repAvgLongCurrentAccounts||All|0|1|1|rep avg long current accounts|rep_team_rep|0|1|
repIsActivated|repIsActivated||All|0|1|1|is rep acctivated?|rep_team_rep|0|1|
repMaxMilesCurrentAccounts|repMaxMilesCurrentAccounts||All|0|1|1|rep max miles curr Accounts|rep_team_rep|0|1|
repNumCurrAccounts|repNumCurrAccounts||All|0|1|1|rep num current accounts|rep_team_rep|0|1|
repNumCurrAccountsWithLatLong|repNumCurrAccountsWithLatLong||All|0|1|1|rep num current accounts with valid lat long|rep_team_rep|0|1|
repNumCurrAccountsWithValidLatLong|repNumCurrAccountsWithValidLatLong||All|0|1|1|rep num current accounts with valid lat long|rep_team_rep|0|1|
repSeConfigId|repSeConfigId||All|0|1|1|rep se ConfigId|rep_team_rep|0|1|
repTimeZoneId|repTimeZoneId||All|0|1|1|rep Time zone Id|rep_team_rep|0|1|
suggestedDate|suggestedDate||All|0|1|1|Suggested Date||0|1|
suggestedDay|suggestedday||All|0|1|1|suggested Day||0|1|
suggestedMonth|suggestedmonth||All|0|1|1|suggested month||0|1|
suggestedYear|suggestedyear||All|0|1|1|suggested year||0|1|
suggestionReferenceId|suggestionReferenceId||All|0|1|1|unique suggestion id||0|1|
suggestionUID|suggestionUID||All|0|1|1|?||0|1|
suggestionYearMonth|suggestionYearMonth||All|0|1|1|suggestion year month||0|1|
suggestionAccountId|suggestionAccountId||All|0|1|1|suggestion accountId||0|1|
suggestionActionTaken|suggestionActionTaken||All|0|1|1|suggestion action taken.||0|1|
suggestionChannel|suggestionChannel||All|0|1|1|suggestion channel||0|1|
suggestionDetailRepActionName|suggestionDetailRepActionName||All|0|1|1|suggestion detail rep action name||0|1|
suggestionDetailRepActiontypeId|suggestionDetailRepActionTypeId||All|0|1|1|suggestion detail rep action Id||0|1|
suggestionEmailScore|suggestionEmailScore||All|0|1|1|email suggestion cri score||0|1|
suggestionInteractionId|suggestionInteractionId||All|0|1|1|"	
Interaction ID linking to interaction took place because of suggestion and present in interaction table"||0|1|
suggestionInteractionRaw|suggestionInteractionraw||All|0|1|1|interaction copy storm ID linking to the interaction took place because of suggestion||0|1|
suggestionMessageId|suggestionMessageId||All|0|1|1|suggestion message ID||0|1|
suggestionMessageName|suggestionMessageName||All|0|1|1|suggestion message name||0|1|
suggestionProductId|suggestionProductId||All|0|1|1|suggestion productID.||0|1|
suggestionRepID|suggestionRepId||All|0|1|1|suggested repID who needs to take action||0|1|
suggestionRepTeamId|suggestionRepTeamId||All|0|1|1|"	
suggestion rep Team ID where a repId is linked"||0|1|
suggestionVisitScore|suggestionVisitScore||All|0|1|1|suggestion visit score||0|1|
sumIndex|sumIndex||All|0|1|1|sum Index cri score||0|1|[May be diffferent name for sum cri]
targetAchievementScoreStd|targetAchievementScoreStd||All|0|1|1|target achievement score std||0|1|
tenureScoreStd|tenureScoreStd||All|0|1|1|tenure score std||0|1|
updatedAt|updatedAt||All|0|1|1|suggestion updated at||0|1|
visitScoreStd|visitScoreStd||All|0|1|1|visit cri score std||0|1|
enumTop5ZipCodeHCOSFlag_akt|enumTop5ZipCodeHCOSFlag||pfizerus|2|0|0|account||0|1|
enumPHR3BrandPriorityOrder_akt|enumPHR3BrandPriorityOrder||pfizerus|2|0|0|account||0|1|
HCPCaresetFluAllTypes_akt|hCPCaresetFluAllTypes||pfizerus|2|0|0|account||0|1|
enumParentSpecialty_akt|enumParentSpecialty||pfizerus|2|0|0|account||0|1|
sampleTargetCodeWH_akt|sampleTargetCodeWH||pfizerus|2|0|0|account||0|1|
HCPCaresetTotalPatients_akt|hCPCaresetTotalPatients||pfizerus|2|0|0|account||0|1|
channelPreferenceNeg_akt|channelPreferenceNeg||pfizerus|2|0|0|account||0|1|
WHDetailsCompleted_akt|wHDetailsCompleted||pfizerus|2|0|0|account||0|1|
driverLyricaTop3_akt|driverLyricaTop3||pfizerus|2|0|0|account||0|1|
enumLyrInsightSlideCode_akt|enumLyrInsightSlideCode||pfizerus|2|0|0|account||0|1|
driverLyricaTop2_akt|driverLyricaTop2||pfizerus|2|0|0|account||0|1|
barrierLyricaTop3_akt|barrierLyricaTop3||pfizerus|2|0|0|account||0|1|
hasSharedTargetC2C3_akt|hasSharedTargetC2C3||pfizerus|2|0|0|account||0|1|
barrierLyricaTop1_akt|barrierLyricaTop1||pfizerus|2|0|0|account||0|1|
target|target||pfizerus|2|0|0|account||0|1|
email_akt|email||pfizerus|2|0|0|account||0|1|
enumVTETargetPriority_akt|enumVTETargetPriority||pfizerus|2|0|0|account||0|1|
enumChxZDCPHotList_akt|enumChxZDCPHotList||pfizerus|2|0|0|account||0|1|
hasSharedTargetC1C3_akt|hasSharedTargetC1C3||pfizerus|2|0|0|account||0|1|
premarinOralNRxQuintile_akt|premarinOralNRxQuintile||pfizerus|2|0|0|account||0|1|
WHOptimalCall_akt|wHOptimalCall||pfizerus|2|0|0|account||0|1|
enumBrandedPreference_akt|enumBrandedPreference||pfizerus|2|0|0|account||0|1|
enumPSAIndicationFlag_akt|enumPSAIndicationFlag||pfizerus|2|0|0|account||0|1|
discussedTopic_akt|discussedTopic||pfizerus|2|0|0|account||0|1|
Interaction_targetingLevelId|interactionTargetingLevelId||pfizerus|2|0|0|account||0|1|
HCPCaresetFlueHD_akt|hCPCaresetFlueHD||pfizerus|2|0|0|account||0|1|
enumRAIndicationFlag_akt|enumRAIndicationFlag||pfizerus|2|0|0|account||0|1|
enumPHR1_PHR2BrandPriorityOrder_akt|enumPHR1PHR2BrandPriorityOrder||pfizerus|2|0|0|account||0|1|
Competitive_Trial_Risk_akt|competitiveTrialRisk||pfizerus|2|0|0|account||0|1|
enumCL1RemainingODFlag_akt|enumCL1RemainingODFlag||pfizerus|2|0|0|account||0|1|
WH_PO_TRx_QUINTILE_akt|whPoTRxQuintile||pfizerus|2|0|0|account||0|1|
Suggestion_targetingLevelId|suggestionTargetingLevelId||pfizerus|2|0|0|account||0|1|
lastSlideUsed_akt|lastSlideUsed||pfizerus|2|0|0|account||0|1|
chxEucBalanceFlag_akt|chxEucBalanceFlag||pfizerus|2|0|0|account||0|1|
LyrChannelFocus_akt|lyrChannelFocus||pfizerus|2|0|0|account||0|1|
enumDuloxetinePreference_akt|enumDuloxetinePreference||pfizerus|2|0|0|account||0|1|
eucrisaMarathonerFlag_akt|eucrisaMarathonerFlag||pfizerus|2|0|0|account||0|1|
targetsPeriodId|targetsPeriodId||pfizerus|2|0|0|account||0|1|
strategyTargetId|strategyTargetId||pfizerus|2|0|0|account||0|1|
barrierLyricaTop2_akt|barrierLyricaTop2||pfizerus|2|0|0|account||0|1|
sampleCommercialAccess_akt|sampleCommercialAccess||pfizerus|2|0|0|account||0|1|
xelSegment_akt|xelSegment||pfizerus|2|0|0|account||0|1|
whBrandPriorities_akt|whBrandPriorities||pfizerus|2|0|0|account||0|1|
enumCustNeedsAssessmentDate_akt|enumCustNeedsAssessmentDate||pfizerus|2|0|0|account||0|1|
enumLyrDosingCode_akt|enumLyrDosingCode||pfizerus|2|0|0|account||0|1|
HCPCaresetHeadroom_akt|hCPCaresetHeadroom||pfizerus|2|0|0|account||0|1|
enumSHR_DSRBrandPriorityOrder_akt|enumSHRDSRBrandPriorityOrder||pfizerus|2|0|0|account||0|1|
duaveeNRxQuintile_akt|duaveeNRxQuintile||pfizerus|2|0|0|account||0|1|
xtandiBrandPriority_akt|xtandiBrandPriority||pfizerus|2|0|0|account||0|1|
brandFlag_akt|brandFlag||pfizerus|2|0|0|account||0|1|
enumHCPAtRiskDecile_akt|enumHCPAtRiskDecile||pfizerus|2|0|0|account||0|1|
enumUCIndicationFlag_akt|enumUCIndicationFlag||pfizerus|2|0|0|account||0|1|
accessDesignation_akt|accessDesignation||pfizerus|2|0|0|account||0|1|
barrierLyrica_akt|barrierLyrica||pfizerus|2|0|0|account||0|1|
eliquisAllianceTargetFlag_akt|eliquisAllianceTargetFlag||pfizerus|2|0|0|account||0|1|
chxSampleEligibility_akt|chxSampleEligibility||pfizerus|2|0|0|account||0|1|
enumHCPCaresetHighRiskDecile_akt|enumHCPCaresetHighRiskDecile||pfizerus|2|0|0|account||0|1|
bosSegment_akt|bosSegment||pfizerus|2|0|0|account||0|1|
enumHCPCaresetFlag_akt|enumHCPCaresetFlag||pfizerus|2|0|0|account||0|1|
stateRestrictions_akt|stateRestrictions||pfizerus|2|0|0|account||0|1|
daysSinceLastPrev13Inventory_akt|daysSinceLastPrev13Inventory||pfizerus|2|0|0|account||0|1|
enumCaresetDoctorList_akt|enumCaresetDoctorList||pfizerus|2|0|0|account||0|1|
ATTRCMCalls_akt|aTTRCMCalls||pfizerus|2|0|0|account||0|1|
enumIsPersonAccount_akt|enumIsPersonAccount||pfizerus|2|0|0|account||0|1|
enumHcpSpecialty_akt|enumHcpSpecialty||pfizerus|2|0|0|account||0|1|
colScreeningRate_akt|colScreeningRate||pfizerus|2|0|0|account||0|1|
enumCL2RetailTargetFlag_akt|enumCL2RetailTargetFlag||pfizerus|2|0|0|account||0|1|
driverLyrica_akt|driverLyrica||pfizerus|2|0|0|account||0|1|
channelPreferencePos_akt|channelPreferencePos||pfizerus|2|0|0|account||0|1|
embedaBrandFlag_akt|embedaBrandFlag||pfizerus|2|0|0|account||0|1|
hasSharedTarget_akt|hasSharedTarget||pfizerus|2|0|0|account||0|1|
HCPCaresetPCV13FFSAdmin_akt|hCPCaresetPCV13FFSAdmin||pfizerus|2|0|0|account||0|1|
productPositionList_akt|productPositionList||pfizerus|2|0|0|account||0|1|
eucrisaQuintile_akt|eucrisaQuintile||pfizerus|2|0|0|account||0|1|
enumCustNeedsAssessmentFlag_akt|enumCustNeedsAssessmentFlag||pfizerus|2|0|0|account||0|1|
opioidFlag_akt|opioidFlag||pfizerus|2|0|0|account||0|1|
pvcTRxQuintile_akt|pvcTRxQuintile||pfizerus|2|0|0|account||0|1|
estringTRxQuintile_akt|estringTRxQuintile||pfizerus|2|0|0|account||0|1|
driverLyricaTop1_akt|driverLyricaTop1||pfizerus|2|0|0|account||0|1|
enumLyrInsightCode_akt|enumLyrInsightCode||pfizerus|2|0|0|account||0|1|
enumCustNeedsAssessmentRep_akt|enumCustNeedsAssessmentRep||pfizerus|2|0|0|account||0|1|
Baq_Segment_akt|baqSegment||lillyus|14|0|0|account||0|1|
Adj_Cur4w_NBRx_Jard_akt|adjCur4wNBRxJard||lillyus|14|0|0|account||0|1|
department_std_akt|departmentStd||lillyus|14|0|0|account||0|1|
Cur13w_NTS_Janu_akt|cur13wNtsJanu||lillyus|14|0|0|account||0|1|
cur4w_NTS_GLP1_FI_SOM_Chg_akt|cur4wNtsGlp1FiSomChg||lillyus|14|0|0|account||0|1|
Cur4w_Gvoke_SH_NBRx_akt|cur4wGvokeShNBRx||lillyus|14|0|0|account||0|1|
Email_Address_akt|emailAddress||lillyus|14|0|0|account||0|1|
OBU_Ascension_Opt_akt|obuAscensionOpt||lillyus|14|0|0|account||0|1|
cur13w_Baq_Comm_TRx_Share_akt|cur13wBaqCommTRxShare||lillyus|14|0|0|account||0|1|
Cur13w_BasalMkt_akt|cur13wBasalMkt||lillyus|14|0|0|account||0|1|
Cur13w_Tru_TRx_Medicare_Share_akt|cur13wTruTRxMedicareShare||lillyus|14|0|0|account||0|1|
cur13w_Tru_Comm_TRx_Share_akt|cur13wTruCommTRxShare||lillyus|14|0|0|account||0|1|
relayTarget_akt|relayTarget||lillyus|14|0|0|account||0|1|
Cur13w_NTS_Oze_akt|cur13wNtsOze||lillyus|14|0|0|account||0|1|
TotalVDCs_akt|totalVDCs||lillyus|14|0|0|account||0|1|
HCC_Decile_akt|hccDecile||lillyus|14|0|0|account||0|1|
specialties_std_akt|specialtiesStd||lillyus|14|0|0|account||0|1|
Cur6m_Patients_Metf_akt|cur6mPatientsMetf||lillyus|14|0|0|account||0|1|
Jard_Segment_akt|jardSegment||lillyus|14|0|0|account||0|1|
Ascension_Flag_akt|ascensionFlag||lillyus|14|0|0|account||0|1|
Cur13w_NTS_GLP1_akt|cur13wNtsGlp1||lillyus|14|0|0|account||0|1|
Cur13w_Tru_TRx_Comm_Share_akt|cur13wTruTRxCommShare||lillyus|14|0|0|account||0|1|
cur13w_Baq_Medicaid_TRx_Share_akt|cur13wBaqMedicaidTRxShare||lillyus|14|0|0|account||0|1|
cur13w_Tru_Medicaid_TRx_Share_akt|cur13wTruMedicaidTRxShare||lillyus|14|0|0|account||0|1|
Trulicity_Target_akt|trulicityTarget||lillyus|14|0|0|account||0|1|
Cur13w_NTS_SOM_DPP4_akt|cur13wNtsSomDpp4||lillyus|14|0|0|account||0|1|
cur6m_Jnva_NTS_akt|cur6mJnvaNts||lillyus|14|0|0|account||0|1|
enumGastricGEJDecile_akt|enumGastricGEJDecile||lillyus|14|0|0|account||0|1|
Cur13w_Baq_TRx_Medicare_Share_akt|cur13wBaqTRxMedicareShare||lillyus|14|0|0|account||0|1|
emailcaptured_std_akt|emailcapturedStd||lillyus|14|0|0|account||0|1|
Cur4w_NTS_SOM_Change_Jnva_akt|cur4wNtsSomChangeJnva||lillyus|14|0|0|account||0|1|
cur4w_Tru_Abandonment_Rx_akt|cur4wTruAbandonmentRx||lillyus|14|0|0|account||0|1|
enumCyrGastric1100_akt|enumCyrGastric1100||lillyus|14|0|0|account||0|1|
VAE_SentIn30Days_akt|vaeSentIn30Days||lillyus|14|0|0|account||0|1|
enumLarSegment_akt|enumLarSegment||lillyus|14|0|0|account||0|1|
enumPrimarySpecialty_akt|enumPrimarySpecialty||lillyus|14|0|0|account||0|1|
enumErbSegment_akt|enumErbSegment||lillyus|14|0|0|account||0|1|
cur4w_Ryb_Abandonment_Rx_akt|cur4wRybAbandonmentRx||lillyus|14|0|0|account||0|1|
enummCRCDecile_akt|enummCRCDecile||lillyus|14|0|0|account||0|1|
enumSyneosHNDecile_akt|enumSyneosHNDecile||lillyus|14|0|0|account||0|1|
LUNG_EGFR_LI_akt|lungEgfrLi||lillyus|14|0|0|account||0|1|
CSOPinnedInsightText_akt|cSOPinnedInsightText||lillyus|14|0|0|account||0|1|
enumVerSegment_akt|enumVerSegment||lillyus|14|0|0|account||0|1|
Cur12m_TRx_SH_akt|cur12mTRxSh||lillyus|14|0|0|account||0|1|
enumHibbertUniversalDMOptOut_akt|enumHibbertUniversalDMOptOut||lillyus|14|0|0|account||0|1|
ALIM_MAINT_LI_akt|alimMaintLi||lillyus|14|0|0|account||0|1|
Cur4w_BaqMarket_akt|cur4wBaqMarket||lillyus|14|0|0|account||0|1|
Cur13w_NTS_Jard_akt|cur13wNtsJard||lillyus|14|0|0|account||0|1|
Cur13w_All_Cov_Jard_akt|cur13wAllCovJard||lillyus|14|0|0|account||0|1|
ERLO_MONO_LI_akt|erloMonoLi||lillyus|14|0|0|account||0|1|
enumASCOInviteList_akt|enumASCOInviteList||lillyus|14|0|0|account||0|1|
Cur13w_NBRx_Change_Jard_akt|cur13wNBRxChangeJard||lillyus|14|0|0|account||0|1|
Cur13w_NTS_Ryb_akt|cur13wNtsRyb||lillyus|14|0|0|account||0|1|
cur4w_Ryb_NBRx_akt|cur4wRybNBRx||lillyus|14|0|0|account||0|1|
Adj_Cur13w_NTS_SOM_Change_FRXG_akt|adjCur13wNtsSomChangeFrxg||lillyus|14|0|0|account||0|1|
DOCE_MONO_LI_akt|doceMonoLi||lillyus|14|0|0|account||0|1|
Cur3m_Glu_Exp_akt|cur3mGluExp||lillyus|14|0|0|account||0|1|
Erbitux_HN_Target_Flag_akt|erbituxHnTargetFlag||lillyus|14|0|0|account||0|1|
Pathway_Indication_akt|pathwayIndication||lillyus|14|0|0|account||0|1|
enumKisqaliUser_akt|enumKisqaliUser||lillyus|14|0|0|account||0|1|
pdrpoptout_std_akt|pdrpoptoutStd||lillyus|14|0|0|account||0|1|
enumSTSDecile_akt|enumSTSDecile||lillyus|14|0|0|account||0|1|
Adj_Cur4w_NTS_DPP4_akt|adjCur4wNtsDpp4||lillyus|14|0|0|account||0|1|
DBU_Kaiser_Opt_akt|dbuKaiserOpt||lillyus|14|0|0|account||0|1|
Cur13w_NTS_DPP4_akt|cur13wNtsDpp4||lillyus|14|0|0|account||0|1|
T1_SH_PatientMix_akt|t1ShPatientMix||lillyus|14|0|0|account||0|1|
PinnedInsightText_akt|pinnedInsightText||lillyus|14|0|0|account||0|1|
enumCyrSegment_akt|enumCyrSegment||lillyus|14|0|0|account||0|1|
enumHNLADDecile_akt|enumHNLADDecile||lillyus|14|0|0|account||0|1|
Adj_Cur4w_NTS_SOM_Change_Janumet_akt|adjCur4wNtsSomChangeJanumet||lillyus|14|0|0|account||0|1|
IMS_Prescriber_ID_akt|imsPrescriberId||lillyus|14|0|0|account||0|1|
enumErbHNSegment_akt|enumErbHNSegment||lillyus|14|0|0|account||0|1|
Cur13w_TRx_BydBcs_Anth_akt|cur13wTRxBydBcsAnth||lillyus|14|0|0|account||0|1|
cur26w_JARD_MET_NTS_SOM_TOT_akt|cur26wJardMetNtsSomTot||lillyus|14|0|0|account||0|1|
Cur13w_Medicare_Cov_Jard_akt|cur13wMedicareCovJard||lillyus|14|0|0|account||0|1|
cur13w_Baq_Medicare_TRx_Share_akt|cur13wBaqMedicareTRxShare||lillyus|14|0|0|account||0|1|
Cur4wJard_NTS_akt|cur4wJardNts||lillyus|14|0|0|account||0|1|
SelperCatinib_Lung_akt|selperCatinibLung||lillyus|14|0|0|account||0|1|
enumHibbertAMAMarketingOptOut_akt|enumHibbertAMAMarketingOptOut||lillyus|14|0|0|account||0|1|
Adj_Cur4w_NTS_SOM_Change_Jnva_akt|adjCur4wNtsSomChangeJnva||lillyus|14|0|0|account||0|1|
T1D_Glu_Decile_akt|t1dGluDecile||lillyus|14|0|0|account||0|1|
Cur4w_NTS_GLP1_akt|cur4wNtsGlp1||lillyus|14|0|0|account||0|1|
Cur4w_NTS_MTI_akt|cur4wNtsMti||lillyus|14|0|0|account||0|1|
Cyramza_CRC_Target_Flag_akt|cyramzaCrcTargetFlag||lillyus|14|0|0|account||0|1|
Cur13w_TRx_Byd_Anth_akt|cur13wTRxBydAnth||lillyus|14|0|0|account||0|1|
enumSTSThoughtLeader_akt|enumSTSThoughtLeader||lillyus|14|0|0|account||0|1|
cur13w_ComboMkt_BO_NBRx_akt|cur13wComboMktBoNBRx||lillyus|14|0|0|account||0|1|
Adj_Cur4w_JarBO_Mkt_NTS_akt|adjCur4wJarBOMktNts||lillyus|14|0|0|account||0|1|
T2D_Glu_Decile_akt|t2dGluDecile||lillyus|14|0|0|account||0|1|
T2_PatientMix_akt|t2PatientMix||lillyus|14|0|0|account||0|1|
Adj_Cur4w_NTS_SOM_Change_Jard_akt|adjCur4wNtsSomChangeJard||lillyus|14|0|0|account||0|1|
Cur4w_NBRx_Jard_akt|cur4wNBRxJard||lillyus|14|0|0|account||0|1|
OptumRxTopPayer_akt|optumRxTopPayer||lillyus|14|0|0|account||0|1|
Syneos_HCC_Target_akt|syneosHccTarget||lillyus|14|0|0|account||0|1|
Adj_Cur6m_Patients_Metf_Only_akt|adjCur6mPatientsMetfOnly||lillyus|14|0|0|account||0|1|
Cur13w_NTS_Change_Jard_akt|cur13wNtsChangeJard||lillyus|14|0|0|account||0|1|
Tru_Segment_akt|truSegment||lillyus|14|0|0|account||0|1|
DBUTeamTarget_akt|dBUTeamTarget||lillyus|14|0|0|account||0|1|
Adj_Cur13w_NTS_Change_Jard_akt|adjCur13wNtsChangeJard||lillyus|14|0|0|account||0|1|
ActiveZip_akt|activeZip||lillyus|14|0|0|account||0|1|
HCP_Opt_In_akt|hcpOptIn||lillyus|14|0|0|account||0|1|
T2_Patient65Plus_akt|t2Patient65Plus||lillyus|14|0|0|account||0|1|
RAM_REVEL_LI_akt|ramRevelLi||lillyus|14|0|0|account||0|1|
Cur13w_Baq_TRx_Comm_Share_akt|cur13wBaqTRxCommShare||lillyus|14|0|0|account||0|1|
SelperCatinib_Thyroid_akt|selperCatinibThyroid||lillyus|14|0|0|account||0|1|
Erbitux_mCRC_Target_Flag_akt|erbituxMCRCTargetFlag||lillyus|14|0|0|account||0|1|
HCC_2LPlus_LI_akt|hcc2LPlusLi||lillyus|14|0|0|account||0|1|
GC_Target_akt|gcTarget||lillyus|14|0|0|account||0|1|
cur26w_GLP_MET_NTS_SOM_TOT_akt|cur26wGlpMetNtsSomTot||lillyus|14|0|0|account||0|1|
JardTargetGreaterRepVDCs_akt|jardTargetGreaterRepVDCs||lillyus|14|0|0|account||0|1|
Cur4w_GvokeNBRxSOM_akt|cur4wGvokeNBRxSOM||lillyus|14|0|0|account||0|1|
enumErbLADHNDecile_akt|enumErbLADHNDecile||lillyus|14|0|0|account||0|1|
enummNSCLCDecile_akt|enummNSCLCDecile||lillyus|14|0|0|account||0|1|
cur4w_Oze_Abandonment_Rx_akt|cur4wOzeAbandonmentRx||lillyus|14|0|0|account||0|1|
Adj_Cur4w_NTS_Ryb_akt|adjCur4wNtsRyb||lillyus|14|0|0|account||0|1|
iQCentrix_Keywords_akt|iQCentrixKeywords||lillyus|14|0|0|account||0|1|
R13w_NBRX_Change_akt|r13wNbrxChange||lillyus|14|0|0|account||0|1|
title_std_akt|titleStd||lillyus|14|0|0|account||0|1|
enumSymphonyLarSegment_akt|enumSymphonyLarSegment||lillyus|14|0|0|account||0|1|
Cur4w_JarBO_Mkt_NTS_akt|cur4wJarBOMktNts||lillyus|14|0|0|account||0|1|
DBU_AMA_Opt_Out_akt|dbuAmaOptOut||lillyus|14|0|0|account||0|1|
cur26w_TRU_MET_NTS_SOM_TOT_akt|cur26wTruMetNtsSomTot||lillyus|14|0|0|account||0|1|
Cur13w_NTS_SOM_Change_FRXG_akt|cur13wNtsSomChangeFrxg||lillyus|14|0|0|account||0|1|
Cur4w_Glucagen_SH_NBRx_akt|cur4wGlucagenShNBRx||lillyus|14|0|0|account||0|1|
Historic_HCC_Treater_akt|historicHccTreater||lillyus|14|0|0|account||0|1|
KISQ_LI_akt|kisqLi||lillyus|14|0|0|account||0|1|
enumErbmCRCDecile_akt|enumErbmCRCDecile||lillyus|14|0|0|account||0|1|
Cur6m_Patients_Metf_Only_akt|cur6mPatientsMetfOnly||lillyus|14|0|0|account||0|1|
Telesolutions_Target_akt|telesolutionsTarget||lillyus|14|0|0|account||0|1|
enumHibbertUniversalOptOut_akt|enumHibbertUniversalOptOut||lillyus|14|0|0|account||0|1|
Met_Pat_Count_A1C_7Plus_akt|metPatCountA1c7Plus||lillyus|14|0|0|account||0|1|
Cur4w_NTS_DPP4_akt|cur4wNtsDpp4||lillyus|14|0|0|account||0|1|
cur13w_Mkt_BO_NBRx_akt|cur13wMktBoNBRx||lillyus|14|0|0|account||0|1|
LUNG_LI_akt|lungLi||lillyus|14|0|0|account||0|1|
cur13w_TRU_Pat_OOP_Less25_akt|cur13wTruPatOopLess25||lillyus|14|0|0|account||0|1|
TruTargetGreaterRepVDCs_akt|truTargetGreaterRepVDCs||lillyus|14|0|0|account||0|1|
iQCentrix_Statement_akt|iQCentrixStatement||lillyus|14|0|0|account||0|1|
cur13w_GLP_NTS_SOM_MetOnly_akt|cur13wGlpNtsSomMetOnly||lillyus|14|0|0|account||0|1|
PatCnt_A1C_7_akt|patCntA1c7||lillyus|14|0|0|account||0|1|
Cur4w_NTS_Ryb_akt|cur4wNtsRyb||lillyus|14|0|0|account||0|1|
Glu_T2D_SOM_akt|gluT2dSom||lillyus|14|0|0|account||0|1|
enumErbCRCSegment_akt|enumErbCRCSegment||lillyus|14|0|0|account||0|1|
Cur13w_NTS_MTI_akt|cur13wNtsMti||lillyus|14|0|0|account||0|1|
Cur13w_NTS_SOM_Change_Jard_akt|cur13wNtsSomChangeJard||lillyus|14|0|0|account||0|1|
Cur13w_Oze_Abandonment_akt|cur13wOzeAbandonment||lillyus|14|0|0|account||0|1|
Cur13w_TRx_Tru_akt|cur13wTRxTru||lillyus|14|0|0|account||0|1|
enumCyrCRCTarget_akt|enumCyrCRCTarget||lillyus|14|0|0|account||0|1|
enumHasEmailAddress_akt|enumHasEmailAddress||lillyus|14|0|0|account||0|1|
Target_Exclusion_Flag_Jard_akt|targetExclusionFlagJard||lillyus|14|0|0|account||0|1|
SyneosTargetFlag_akt|syneosTargetFlag||lillyus|14|0|0|account||0|1|
Glu_T1D_SOM_akt|gluT1dSom||lillyus|14|0|0|account||0|1|
enumCDKDecile_akt|enumCDKDecile||lillyus|14|0|0|account||0|1|
DBU_Target_Exclusion_akt|dbuTargetExclusion||lillyus|14|0|0|account||0|1|
T1_PatientMix_akt|t1PatientMix||lillyus|14|0|0|account||0|1|
cur13w_Tru_TRx_SOM_in_GLPandBasal_akt|cur13wTruTRxSomInGLPandBasal||lillyus|14|0|0|account||0|1|
iQCentrix_Brand_Ind_akt|iQCentrixBrandInd||lillyus|14|0|0|account||0|1|
Cur13w_NTS_FI_SOM_GLP1_akt|cur13wNtsFiSomGlp1||lillyus|14|0|0|account||0|1|
ispersonaccount_std_akt|ispersonaccountStd||lillyus|14|0|0|account||0|1|
Historic_Gastric_Treater_akt|historicGastricTreater||lillyus|14|0|0|account||0|1|
Cur4w_Baq_SH_NBRx_akt|cur4wBaqShNBRx||lillyus|14|0|0|account||0|1|
Cur13w_Baq_TRx_Medicaid_Share_akt|cur13wBaqTRxMedicaidShare||lillyus|14|0|0|account||0|1|
enumHibbertKaiserFlag_akt|enumHibbertKaiserFlag||lillyus|14|0|0|account||0|1|
cur13w_Baq_AbandonmentRate_akt|cur13wBaqAbandonmentRate||lillyus|14|0|0|account||0|1|
Cur13w_Tru_Abandonment_akt|cur13wTruAbandonment||lillyus|14|0|0|account||0|1|
cur13w_JNVA_TRx_UHC_akt|cur13wJnvaTRxUhc||lillyus|14|0|0|account||0|1|
TOTAL_POD_CALLS_COMMITTED_akt|totalPodCallsCommitted||lillyus|14|0|0|account||0|1|
Alimta_Target_akt|alimtaTarget||lillyus|14|0|0|account||0|1|
Pathway_Flag_akt|pathwayFlag||lillyus|14|0|0|account||0|1|
enumHNRECMETDecile_akt|enumHNRECMETDecile||lillyus|14|0|0|account||0|1|
Cur4w_NTS_SOM_Change_Rybelsus_akt|cur4wNtsSomChangeRybelsus||lillyus|14|0|0|account||0|1|
Cur4w_Glucagon_SH_NBRx_akt|cur4wGlucagonShNBRx||lillyus|14|0|0|account||0|1|
Syneos_GC_Target_akt|syneosGcTarget||lillyus|14|0|0|account||0|1|
enumPDRPOptOut_akt|enumPDRPOptOut||lillyus|14|0|0|account||0|1|
GI_2LPlus_LI_akt|gi2LPlusLi||lillyus|14|0|0|account||0|1|
cur13w_Jard_TRx_akt|cur13wJardTRx||lillyus|14|0|0|account||0|1|
Adj_Cur13w_NTS_SOM_Change_Jard_akt|adjCur13wNtsSomChangeJard||lillyus|14|0|0|account||0|1|
enumCyrRepeater_akt|enumCyrRepeater||lillyus|14|0|0|account||0|1|
Cur13w_NTS_SGLT_akt|cur13wNtsSglt||lillyus|14|0|0|account||0|1|
Cur13w_NTS_Tru_akt|cur13wNtsTru||lillyus|14|0|0|account||0|1|
enumHibbertUniversalEMOptOut_akt|enumHibbertUniversalEMOptOut||lillyus|14|0|0|account||0|1|
Cur4w_NTS_SOM_Change_Jard_akt|cur4wNtsSomChangeJard||lillyus|14|0|0|account||0|1|
cur26w_BASAL_NTS_SOM_akt|cur26wBasalNtsSom||lillyus|14|0|0|account||0|1|
enumAlimtaSegment_akt|enumAlimtaSegment||lillyus|14|0|0|account||0|1|
Cur13w_Basal_NTS_akt|cur13wBasalNts||lillyus|14|0|0|account||0|1|
BaqTargetGreaterRepVDCs_akt|baqTargetGreaterRepVDCs||lillyus|14|0|0|account||0|1|
Jardiance_Target_akt|jardianceTarget||lillyus|14|0|0|account||0|1|
Cur4w_SH_Event_Count_akt|cur4wShEventCount||lillyus|14|0|0|account||0|1|
CDK_LI_akt|cdkLi||lillyus|14|0|0|account||0|1|
Cur4w_NBRx_Trulicity_akt|cur4wNBRxTrulicity||lillyus|14|0|0|account||0|1|
cur78w_BASAL_NTS_SOM_akt|cur78wBasalNtsSom||lillyus|14|0|0|account||0|1|
cur13w_Tru_Medicare_TRx_Share_akt|cur13wTruMedicareTRxShare||lillyus|14|0|0|account||0|1|
PlannedCallCount_akt|plannedCallCount||lillyus|14|0|0|account||0|1|
Cur13w_Comm_Cov_Jard_akt|cur13wCommCovJard||lillyus|14|0|0|account||0|1|
Cur4w_NTS_SOM_Change_Janumet_akt|cur4wNtsSomChangeJanumet||lillyus|14|0|0|account||0|1|
enumCyrLungUser_akt|enumCyrLungUser||lillyus|14|0|0|account||0|1|
Adj_Cur4wJard_NTS_akt|adjCur4wJardNts||lillyus|14|0|0|account||0|1|
PatCnt_A1C_9_akt|patCntA1c9||lillyus|14|0|0|account||0|1|
selperTarget_akt|selperTarget||lillyus|14|0|0|account||0|1|
enum1LAlimta_akt|enum1LAlimta||lillyus|14|0|0|account||0|1|
DBU_Specialty_akt|dbuSpecialty||lillyus|14|0|0|account||0|1|
Cyramza_Relay_akt|cyramzaRelay||lillyus|14|0|0|account||0|1|
cur26w_GLP_NTS_SOM_FI_akt|cur26wGlpNtsSomFi||lillyus|14|0|0|account||0|1|
AddtnlSPEC_GROUP_akt|addtnlSPECGroup||lillyus|14|0|0|account||0|1|
R4wNTS_SOM_Change_Tru_akt|r4wNTSSomChangeTru||lillyus|14|0|0|account||0|1|
R4wNTS_SOM_Change_Oze_akt|r4wNTSSomChangeOze||lillyus|14|0|0|account||0|1|
cur6m_Jard_NTS_akt|cur6mJardNts||lillyus|14|0|0|account||0|1|
Baqsimi_Target_akt|baqsimiTarget||lillyus|14|0|0|account||0|1|
GI_Indication_akt|giIndication||lillyus|14|0|0|account||0|1|
Baq_Patient_Share_akt|baqPatientShare||lillyus|14|0|0|account||0|1|
enumErbRECMETHNDecile_akt|enumErbRECMETHNDecile||lillyus|14|0|0|account||0|1|
Cur13w_NTS_SOM_SGLT_akt|cur13wNtsSomSglt||lillyus|14|0|0|account||0|1|
Factor_akt|factor||lillyus|14|0|0|account||0|1|
PatCnt_A1C_7to9_akt|patCntA1c7to9||lillyus|14|0|0|account||0|1|
CRC_LI_akt|crcLi||lillyus|14|0|0|account||0|1|
CSOTelesalesPinnedInsightText_akt|cSOTelesalesPinnedInsightText||lillyus|14|0|0|account||0|1|
DBU_HCP_responsiveness_score_akt|dbuHcpResponsivenessScore||lillyus|14|0|0|account||0|1|
btsTrumenbaDoses|btsTrumenbaDoses||pfizerus|2|0|0|account||0|1|
bts2ndDoseOpportunity|bts2ndDoseOpportunity||pfizerus|2|0|0|account||0|1|
bts1stDoseOpportunity|bts1stDoseOpportunity||pfizerus|2|0|0|account||0|1|
callAccountuid|callAccountuid||pfizerus|2|0|0|account||0|1|
isrRecFreqFlag|isrRecFreqFlag||pfizerus|2|0|0|account||0|1|
isrRepFreqFlag|isrRepFreqFlag||pfizerus|2|0|0|account||0|1|
chantixNRTTargetFlag|chantixNRTTargetFlag||pfizerus|2|0|0|account||0|1|
btsMCV4Doses|btsMCV4Doses||pfizerus|2|0|0|account||0|1|
dateSinceLastPrev13Inventory|dateSinceLastPrev13Inventory||pfizerus|2|0|0|account||0|1|
topPlansWH|topPlansWH||pfizerus|2|0|0|account||0|1|
enumHCPCaresetAgingIn|enumHCPCaresetAgingIn||pfizerus|2|0|0|account||0|1|
enumCaresetAgingInDoctorList|enumCaresetAgingInDoctorList||pfizerus|2|0|0|account||0|1|
btsBexseroDoses|btsBexseroDoses||pfizerus|2|0|0|account||0|1|
vyndamaxV122ITarget|vyndamaxV122ITarget||pfizerus|2|0|0|account||0|1|
