import pandas as pd
import pyspark
from os import path, listdir
from pyspark.sql import SparkSession
import sys
import os
import re
import findspark
import datetime
import dateutil.relativedelta

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
print ("Learning dir:",learning_dir)
sys.path.append(learning_dir + "/common/pyUtils")

import aktana_ml_utils as mlutils


def initalize_spark(cmdline_params, is_local):
    findspark.init()
    os.environ['PYSPARK_SUBMIT_ARGS'] = "--driver-memory 4g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.199," \
                                        "org.apache.hadoop:hadoop-aws:3.0.1,io.delta:delta-core_2.12:0.8.0 " \
                                        "pyspark-shell "

    sc = pyspark.SparkContext()
    sc.setSystemProperty("com.amazonaws.services.s3.enableV4", "true")
    sc.setLogLevel("WARN")
    hadoop_conf = sc._jsc.hadoopConfiguration()
    hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
    hadoop_conf.set("com.amazonaws.services.s3.enableV4", "true")
    if is_local:
        hadoop_conf.set("fs.s3a.access.key", cmdline_params['accessKey'])
        hadoop_conf.set("fs.s3a.secret.key", cmdline_params['secretKey'])
    hadoop_conf.set("fs.s3a.connection.maximum", "100000")
    # hadoop_conf.set("fs.s3a.endpoint", "s3." + constants.aws_region + ".amazonaws.com")
    hadoop_conf.set("fs.s3a.endpoint", "s3.amazonaws.com")
    hadoop_conf.set("delta.logRetentionDuration", "36500")
    hadoop_conf.set("delta.deletedFileRetentionDuration", "365")

    spark = SparkSession(sc) \
        .builder \
        .appName("adl_metrics") \
        .config("spark.sql.autoBroadcastJoinThreshold", -1) \
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
        .config("spark.driver.memory", "8g") \
        .config('spark.executor.cores', '4') \
        .config('spark.executor.memory', '8g') \
        .config("spark.python.worker.memory", '4g') \
        .config("spark.sql.debug.maxToStringFields", 1000) \
        .getOrCreate()


    return spark



def read_parquet(spark, path):
    print(f"Reading parquets from {path}...")
    df = spark.read.option("mergeSchema", "true").parquet(path)
    df.show()


def read_delta_table(spark, path):
    print(f"Reading delta table from {path}...")
    df = spark.read.format("delta").load(path)
    df.show()


def write_delta_table(spark, path):
    # create dummy spark df
    data = [(1, "A", 0.1, True),
            (2, "B", 0.2, False),
            (3, "C", 0.3, True)]

    cols = ["id", "str_field", "float_field", "bool_field"]
    df = spark.createDataFrame(data=data, schema=cols)
    df.show()

    # write to s3 path
    print(f"Writing delta to {path}...")
    df.write.format("delta").mode("overwrite").save(path)


def main():

    # get cmd params and metadata params
    ml_utils = mlutils.aktana_ml_utils()
    cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:",
                                                          ["customer=", "env=", "app=", "region=", "tunnelport=",
                                                           "local=", "mode=", "delta=", "path=", "accessKey=", "secretKey="])
    print(metadata_params)
    print(cmdline_params)

    # # get adl metadata
    # adl_params = ml_utils.get_adl_metadata()
    # print(adl_params)

    # initialize spark
    spark = initalize_spark(cmdline_params, cmdline_params.get("local", "false") == "true")
    from delta.tables import DeltaTable
    print("Spark initialization finished")

    try:
        path = cmdline_params['path']
        # if read mode
        if cmdline_params['mode'] == 'READ':
            print("READ mode...")
            if cmdline_params['delta'] == 'false':
                read_parquet(spark, path)
            else:
                read_delta_table(spark, path)

        # if write mode
        elif cmdline_params['mode'] == 'WRITE':
            print("WRITE mode...")
            write_delta_table(spark, path)

        else:
            print("Usage: mode only supports READ and WRITE, exit...")

    except Exception as e:
        print(e)

    print("Job succeeded, exit...")


if __name__ == "__main__":
    main()
