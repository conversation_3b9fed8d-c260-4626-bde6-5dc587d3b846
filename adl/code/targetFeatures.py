from awsglue.transforms import *
# from pyspark.sql.functions import *
from pyspark.sql import SQLContext

from pyspark.sql.types import *
from pyspark.sql import Row, functions as F
from pyspark.sql.window import Window
from pyspark.sql.functions import when, lit, col
import pandas as pd
from pyspark.sql.types import IntegerType
from pyspark.sql.functions import col, expr, when, broadcast, datediff, to_date, last
from utils import Utils
import logging
import pyspark
import sys
import dateutil.relativedelta
from datetime import datetime
logger = None

class TargetFeatures:

    def __init__(self, spark, target_df):

        self.target_df = target_df
        self.spark = spark

        global logger
        logger = logging.getLogger("HFS_DEV_LOGGER")


    def getTargetFeatures(self, df_final):
        # get 6 months counts
        w = Window.partitionBy('accountId', 'productId').orderBy(F.desc('interactionYearMonth'))
        drop_columns = []

        df = df_final

        for prefix in ['EmailSent', 'Visit', 'VirtualVisit']:
            df = df.withColumn('cumSum' + prefix + '6MonthCount',
                               F.sum(prefix[0].lower() + prefix[1:] + '1MonthCount').over(
                                   w.rowsBetween(Window.currentRow, Window.currentRow + 5)))
            drop_columns.append('cumSum' + prefix + '6MonthCount')

        df = df.withColumn('cumSumAll6MonthCount',
                           F.col('cumSumEmailSent6MonthCount') + F.col('cumSumVisit6MonthCount') + F.col(
                               'cumSumVirtualVisit6MonthCount'))

        df = df.withColumn('isHcpProductTargeted', (F.col('cumSumEmailSent6MonthCount') >= F.lit(2)) | (
                    F.col('cumSumVisit6MonthCount') >= F.lit(1)) | (
                                       F.col('cumSumVirtualVisit6MonthCount') >= F.lit(1)) | (

                                       F.col('cumSumAll6MonthCount') >= F.lit(3)))

        self.target_df.createOrReplaceTempView("st")
        self.target_df = self.spark.sql('''
                select targetAccount, productId
                , avg(t.target) as target, concat(left(SUBSTRING_INDEX(t.startDate,'-',2),4),right(SUBSTRING_INDEX(t.startDate,'-',2),2)) startDateMonth,concat(left(SUBSTRING_INDEX(t.endDate,'-',2),4),right(SUBSTRING_INDEX(t.endDate,'-',2),2)) endDateMonth
                from st t 
                group by
                targetAccount, productId, startDateMonth, endDateMonth''')

        df = df.drop(*drop_columns)

        df = df.alias('df1').join(self.target_df.alias('df2'),
                     (df.accountId == self.target_df.targetAccount) & (df.productId == self.target_df.productId) & (
                         df.interactionYearMonth >= self.target_df.startDateMonth) & (df.interactionYearMonth <=
                                                                        self.target_df.endDateMonth), 'left_outer').select('df1.*', 'df2.target')
        df = df.fillna(0)
        df = df.select((F.coalesce(df.target,F.lit(0)) > F.lit(0)).alias('strategyTarget'), 'accountId', 'productId',
                       'interactionYearMonth', 'isHcpProductTargeted', 'facilityId')
        df = df.withColumn('isHcpProductTargeted', df.isHcpProductTargeted | df.strategyTarget)


        df = df.drop('strategyTarget')

        df = df.select('accountId', 'productId', 'interactionYearMonth', 'isHcpProductTargeted', 'facilityId')
        cnt_df = df.filter(df.isHcpProductTargeted).groupBy('facilityId', 'productId', 'interactionYearMonth').agg(
            F.countDistinct("accountId").alias('facilityTargetAccountCount'))
        df = df.join(cnt_df, ['facilityId', 'productId', 'interactionYearMonth'], 'left_outer')
        df = df.drop('facilityId')
        df = df.fillna(0)

        return df