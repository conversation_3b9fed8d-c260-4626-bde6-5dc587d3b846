from pyspark import SparkConf
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.utils import getResolvedOptions
from utils import Utils
from adlLogger import create_logger, copy_log_file_to_s3
import os
from datetime import datetime
import json
import sys
import logging
import time
from pyspark.sql.functions import col, expr, when, broadcast, datediff, to_date, last
import pyspark.sql.functions as F
from channelFeatures import ChannelFeatures
from emailFeatures import EmailFeatures
from visitFeatures import VisitFeatures
from phoneFeatures import PhoneFeatures
from virtualVisitFeatures import VirtualVisitFeatures
from demographicFeatures import DemoFeatures
from targetFeatures import TargetFeatures
from datetime import date
import dateutil.relativedelta
from pyspark.sql.window import Window
from pyspark.sql.types import IntegerType, StringType, DoubleType
import numpy as np

def preprocessing(df):
    df = df.withColumn('yearWeek', F.concat(F.col('interactionYear'), <PERSON><PERSON>lit('_'), <PERSON>.col('interactionWeekOfYear')))
    return df

if __name__ == '__main__':
    #  Getting Glue, Spark, Sql context and setting conf for delta
    now = datetime.now()
    date_time = now.strftime("%Y-%m-%d")
    print("The script is running on ", date_time)
    # create a glue Context and connect that with spark
    conf = SparkConf(loadDefaults=True)
    conf.set("spark.delta.logStore.class", "org.apache.spark.sql.delta.storage.S3SingleDriverLogStore")
    conf.set("spark.sql.caseSensitive", "true")
    conf.set("spark.sql.execution.arrow.enabled", "true")
    conf.set("spark.executor.cores", 4)
    # conf.set("spark.driver.cores", 4)
    # conf.set("spark.sql.autoBroadcastJoinThreshold", -1)
    # conf.set("spark.driver.memory", "16g")
    # conf.set("spark.executor.memory","16g")
    # conf.set("spark.executor.memoryOverhead", "8g")
    # conf.set("spark.executor.memoryOverhead", "8g")

    # conf.set("spark.driver.memory", "16g")
    conf.set("spark.sql.shuffle.partitions", 100)
    # conf.set("spark.sql.autoBroadcastJoinThreshold", 1000000000)
    # conf.set("spark.jars.packages", "io.delta:delta-core_2.12:0.7.0")
    # conf.set("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
    # conf.set("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog")

    sc = SparkContext.getOrCreate(conf = conf)
    glueContext = GlueContext(sc)
    spark = glueContext.spark_session
    # sc.addPyFile("s3://aktana-bdp-siqa/adl/common/lib/delta-core_2.11-0.6.1.jar")
    # from delta.tables import DeltaTable
    ################################ Arguments / parameter ####################################################
    # get params from Config.json
    with open('hcpConfig.json', 'r') as f:
        config = json.load(f)

    project_dir = None
    if  len(sys.argv) > 1:
        args = getResolvedOptions(sys.argv, ['customer','environment','rptS3Location','s3CustomerAdl','mappingLocation', 'externalAdl'])
        customer = args['customer']
        s3_destination = args['s3CustomerAdl']
        source_s3_location = args['s3CustomerAdl'] + "data/"
        environment = args['environment']
        level = logging.DEBUG
        today = date.today()
        run_month = today.strftime("%Y%m") #config['run_month']
        look_back = int(config['look_back'])
        externalAdl = args['externalAdl'] == 'true'
        delta_jar_path = args['mappingLocation'] + "lib/delta-core_2.12-1.0.0.jar"
        project_dir = '/tmp/'
    else:
        customer = config["customer"]
        level = logging.DEBUG
        s3_destination = config['s3_destination']
        source_s3_location = config['source_s3_location']
        environment = config['environment']
        run_month = config['run_month']
        look_back = int(config['look_back'])
        externalAdl = config['external_adl'] == 'true'
        # hasSpark = config['hasSpark'] == 'true'
        delta_jar_path = "s3://aktana-bdp-siqa/adl/common/lib/delta-core_2.12-1.0.0.jar"

    # batch = config['batch'] == 'true'
    # archive_folder = config['archive_folder']
    # tables = config['tables']
    # bronze_tables = config['bronze_tables']

    # import delta
    sc.addPyFile(delta_jar_path)
    from delta.tables import DeltaTable

    # setup logger
    hfs_logger_name = "HFS_DEV_LOGGER"
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    script_path = os.path.realpath(__file__)
    script_dir = os.path.dirname(script_path)
    project_dir = os.path.dirname(script_dir) if not project_dir else project_dir
    local_log_path = project_dir + 'hfs_log_' + timestamp + '.txt'
    logger = create_logger(logger_name=hfs_logger_name, log_file_path=local_log_path, log_level=level)
    logger.info("Successfully set logger")

    try:
        utils = Utils(glueContext, customer)

        ############################# Initial dataloading from S3
        source_path = source_s3_location + 'silver/final_dataset/'
        adl_df = utils.dataLoadDelta(source_path)  # load from silver/final_dataset
        source_path = source_s3_location + 'bronze/product/'
        productDf = utils.dataLoadDelta(source_path)
        source_path = source_s3_location + 'silver/cri_scores/'
        cri_df = utils.dataLoadDelta(source_path)  # load from silver/final_dataset
        source_path = source_s3_location + 'bronze/strategy_target/'
        target_df = utils.dataLoadDelta(source_path)
        ############################# Preprocessing
        # add one column yearWeek
        adl_df = adl_df.withColumn('yearWeek', F.concat(F.col('interactionYear'), F.lit('_'), F.col('interactionWeekOfYear')))
        # # filter by start end date (year month)
        adlx_latest_month = adl_df.agg({"interactionYearMonth": "max"}).collect()[0][0]
        run_month = min(adlx_latest_month, run_month)
        run_month = datetime.strptime(run_month, "%Y%m") - dateutil.relativedelta.relativedelta(months=1)
        run_month = datetime.strftime(run_month, "%Y%m")
        logger.info("Latest month set as " + run_month)
        d = datetime.strptime(run_month, "%Y%m")
        start_month = d - dateutil.relativedelta.relativedelta(months=look_back)
        start_month = start_month.strftime("%Y%m")
        logger.info("Reading ADLx from " + start_month + ' to ' + run_month)
        adl_df = adl_df.withColumn("hcpStartYearMonth", F.min("interactionYearMonth").over(Window.partitionBy('accountId')))

        adl_df = adl_df.filter((adl_df.interactionYearMonth >= start_month) & (adl_df.interactionYearMonth <= run_month))
        adl_df = adl_df.filter(adl_df.accountId.isNotNull())
        cri_df = cri_df.filter((cri_df.interactionYearMonth >= start_month) & (cri_df.interactionYearMonth <= run_month))
        ############################# channel Features

        email_features_object = EmailFeatures(glueContext, customer,'SEND_CHANNEL', adl_df, run_month, look_back)
        email_features = email_features_object.getEmailFeatures()

        visit_features_object = VisitFeatures(glueContext, customer,'VISIT_CHANNEL', adl_df, run_month, look_back)
        visit_features = visit_features_object.getVisitFeatures()

        virtual_visit_features_object = VirtualVisitFeatures(glueContext, customer,'WEB_INTERACTIVE_CHANNEL', adl_df, run_month, look_back)
        virtual_visit_features = virtual_visit_features_object.getVirtualVisitFeatures()

        phone_features_object = PhoneFeatures(glueContext, customer, 'phone', adl_df, run_month,
                                                             look_back)
        phone_features = phone_features_object.getPhoneFeatures()

        channel_features = email_features.join(visit_features, ["accountId", "productId", 'interactionYearMonth'], "full")
        channel_features = channel_features.join(virtual_visit_features, ["accountId", "productId", 'interactionYearMonth'], "full")
        channel_features = channel_features.join(phone_features,
                                                 ["accountId", "productId", 'interactionYearMonth'], "full")

        ############################# Demographic Features
        demo_features_object = DemoFeatures(glueContext, customer, adl_df)
        demo_features = demo_features_object.getDemoFeatures()

        ############################# criScore Features
        cri_df = cri_df.withColumn("accountId", F.split(cri_df.interactionRepAccount, '_').getItem(1))
        criDf = cri_df.groupby("accountId", "interactionYearMonth").agg(F.max("cadenceScore").alias("criCadenceScore")
                                                                            , F.max("channelScore").alias("criChannelScore")
                                                                            , F.max("emailOpenScore").alias("criOpenScore")
                                                                            , F.max("visitScore").alias("criVisitScore")
                                                                            , F.max("tenureScore").alias("criTenureScore")
                                                                            , F.max("targetAchievementScore").alias("criTargetAchievementScore")
                                                                            , F.max("suggestionEmailScore").alias("criSuggestionEmailScore")
                                                                            , F.max("suggestionVisitScore").alias("criSuggestionVisitScore"))


        # avg_cols = F.udf(lambda array: {
        #                  for x in array:
        #                     sum(array)/len(array)
        #                 }, DoubleType())
        # criDf = criDf.withColumn("criMaxAvgIndex", avg_cols(
        #     F.array("criCadenceScore", "criChannelScore", "criOpenScore", "criSuggestionEmailScore",
        #             "criSuggestionVisitScore", "criTargetAchievementScore", "criTenureScore", "criVisitScore")))
        #
        criDf = criDf.withColumn("criMaxAvgIndex", (F.coalesce(criDf.criCadenceScore, F.lit(0))
                                 + F.coalesce(criDf.criChannelScore, F.lit(0))
                                 + F.coalesce(criDf.criOpenScore, F.lit(0))
                                 + F.coalesce(criDf.criSuggestionEmailScore, F.lit(0))
                                 + F.coalesce(criDf.criSuggestionVisitScore, F.lit(0))
                                 + F.coalesce(criDf.criTargetAchievementScore, F.lit(0))
                                 + F.coalesce(criDf.criTenureScore, F.lit(0))
                                 + F.coalesce(criDf.criVisitScore, F.lit(0)))
                                 /
                                 (F.when(criDf.criCadenceScore.isNotNull(), F.lit(1)).otherwise(F.lit(0))
                                  + F.when(criDf.criChannelScore.isNotNull(), F.lit(1)).otherwise(F.lit(0))
                                  + F.when(criDf.criOpenScore.isNotNull(), F.lit(1)).otherwise(F.lit(0))
                                  + F.when(criDf.criSuggestionEmailScore.isNotNull(), F.lit(1)).otherwise(F.lit(0))
                                  + F.when(criDf.criSuggestionVisitScore.isNotNull(), F.lit(1)).otherwise(F.lit(0))
                                  + F.when(criDf.criTargetAchievementScore.isNotNull(), F.lit(1)).otherwise(F.lit(0))
                                  + F.when(criDf.criTenureScore.isNotNull(), F.lit(1)).otherwise(F.lit(0))
                                  + F.when(criDf.criVisitScore.isNotNull(), F.lit(1)).otherwise(F.lit(0))
                                  )
                                 )

        ############################# Features Combination
        final_df = channel_features.join(demo_features, 'accountId', 'left_outer')
        final_df = final_df.join(criDf, ["accountId", 'interactionYearMonth'], "left_outer")
        ## fill NA in numeric columns with 0
        numeric_columns = [c.name for c in list(final_df.schema) if str(c.dataType) == 'LongType']
        final_df = final_df.na.fill(value=0,subset=numeric_columns)
        final_df = final_df.join(productDf.select('productId', "externalId", "productName").distinct(), "productId", "left_outer")
        final_df = final_df.withColumnRenamed("externalId", "productUid")

        ############################# Target Features
        target_features_object = TargetFeatures(spark, target_df)
        target_features = target_features_object.getTargetFeatures(final_df)

        final_df = final_df.join(target_features, ['accountId','productId', 'interactionYearMonth'], 'left_outer')
        logger.info(final_df.columns)
        ############################# write indexMonth
        idexMonthDf = spark.createDataFrame(
            [
                (index, (d - (dateutil.relativedelta.relativedelta(months=index))).strftime("%Y%m")) for index in range(12)
            ],
            ["indexMonth", "interactionYearMonth"]
        )
        # idexMonthDf = final_df.select("interactionYearMonth").distinct()
        # idexMonthDf = idexMonthDf.withColumn("indexMonth", F.row_number().over(Window.partitionBy().orderBy("interactionYearMonth")))
        final_df = final_df.join(idexMonthDf, "interactionYearMonth", "inner")
        final_df = final_df.withColumnRenamed("interactionYearMonth", "yearMonth")
        ############################# write df to s3
        s3_destination_path = s3_destination + "data/silver/hcpFeatureStore/"
        logger.info(final_df.columns)
        utils.df_topartition_delta_overwrite(final_df, ["yearMonth"], s3_destination_path)
        if externalAdl:
            cols = final_df.columns
            for ele in ['criCadenceScore','criChannelScore', 'criOpenScore', 'criVisitScore', 'criTenureScore',
                        'criTargetAchievementScorem', 'criSuggestionEmailScore', 'criSuggestionVisitScore',
                        'criMaxAvgIndex' ]:
                    if ele in cols:
                        cols.remove(ele)

            externalDf = final_df.select(cols)
            s3_destination_path = "s3://aktana-externalfiles-" + customer + "/" + environment + "/outgoing/adl/adlh/"
            utils.df_topartition_delta_overwrite(externalDf, ["yearMonth"], s3_destination_path)



    except Exception as e:
        logger.error("Error when running hcpFeatureStoreDriver: {}".format(e))


    finally:
        s3_log_path = s3_destination + 'logs/hfs_log_' + f'{customer}_{environment}_{timestamp}.txt'
        copy_log_file_to_s3(local_log_path, s3_log_path)
        




