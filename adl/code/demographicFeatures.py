
from pyspark.sql.types import *
from pyspark.sql import Row, functions as F
from pyspark.sql.window import Window
from utils import Utils
import logging
from pyspark.sql.functions import to_timestamp
from datetime import date, datetime
import dateutil.relativedelta
from pyspark.sql.types import IntegerType, StringType


class DemoFeatures:
    def __init__(self, glue_context, customer, df):
        self.glueContext = glue_context
        self.spark = self.glueContext.spark_session
        self.customer = customer
        self.utils = Utils(glue_context, customer)
        self.spark = glue_context.spark_session
        self.df = df
        global logger
        logger = logging.getLogger("HFS_DEV_LOGGER")

    def getDemoFeatures(self):
        df = self.df.select('accountId', 'hcpGender', 'hcpCred', 'hcpSpec', 'hcpSpec2', 'timeZoneId', 'facilityId',
                            'latitude', 'longitude','accountUid', 'hcpStartYearMonth')

        df = df.drop_duplicates(subset=['accountId'])
        df = self.getGeoFeatures(df)

        # get external features (as null)
        # df = df.withColumn('housholdIncome', F.lit(None).cast(StringType()))
        # df = df.withColumn('medianAge', F.lit(None).cast(StringType()))
        # df = df.withColumn('populationDensity', F.lit(None).cast(StringType()))

        df = df.withColumn("hcpStartYearMonthDateTime", to_timestamp(df.hcpStartYearMonth, "yyyyMM"))\
            .withColumn("today", F.lit(date.today()))\
            .withColumn('tenureMonths', F.round(F.months_between("today", "hcpStartYearMonthDateTime")))
        df = df.withColumn('city', F.lit(None).cast(StringType()))
        df = df.withColumn('county', F.lit(None).cast(StringType()))
        df = df.withColumn('zipCode', F.lit(None).cast(StringType()))
        df = df.withColumn('state', F.lit(None).cast(StringType()))
        # df = df.withColumnRenamed("externalId", "accountUid")

        # get peer group features
        df = self.getPeerGroupFeatures(df, 'facilityId', 'numberHcpFacility')
        df = df.drop("hcpStartYearMonthDateTime")
        # df = self.getPeerGroupFeatures(df, 'hcpSpec', 'numberHcpSpecialty')
        # df = self.getPeerGroupFeatures(df, 'city', 'numberHcpCity') (comment out for future use)

        return df

    def getGeoFeatures(self, df):
        df = df.withColumn('timeZone', F.split(df.timeZoneId, '/').getItem(1))
        cols_to_drop = ['timeZoneId']
        df = df.drop(*cols_to_drop)
        return df

    def getPeerGroupFeatures(self, df, partitionCol, colName):
        w = Window.partitionBy(F.col(partitionCol))
        df = df.withColumn(colName, F.approx_count_distinct('accountId').over(w))
        return df
