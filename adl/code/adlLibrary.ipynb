{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "class adlLibrary:\n", "    def __init__(self, CustomerFolder, Bucket):\n", "        customerFolder = CustomerFolder #\"archive1/\"\n", "        bucket = Bucket # \"aktana-bdp-siqa\"\n", "\n", "    def getCustomFields (self, customerName):\n", "    # Queries the master data dictionary for customer specific fields.\n", "    # Input parameters\n", "    # customerName: customer name string\n", "    # Returned object\n", "    # An array of fields belonging to the customer none including standard fields\n", "        df = spark.read.format(\"delta\").load(\"s3://\"+ bucket +\"/adl/\"+customerFolder+ \"config/deltaDictionary/\")\n", "        res = np.array(df.filter((df.Customer == customerName) & (df.Standard == 0) & (Visible == 1)).select(\"ADLStandardFieldsName\").toPandas()[\"ADLStandardFieldsName\"].values)\n", "        res\n", "\n", "    def getAllCustomerFields (self, customerName):\n", "    # Queries the master data dictionary fo all customer fields.\n", "\n", "    # Input parameters\n", "    # customerName: customer name string\n", "\n", "    # Returned object\n", "    # An array of fields belonging to the customer including standard fields\n", "        df = spark.read.format(\"delta\").load(\"s3://\"+ bucket +\"/adl/\"+customerFolder+ \"config/deltaDictionary/\")\n", "        res = np.array(df.filter((df.Customer == customerName) & (Visible == 1)).select(\"ADLStandardFieldsName\").toPandas()[\"ADLStandardFieldsName\"].values)\n", "        res\n", "\n", "    def getS<PERSON><PERSON>dFields (self, standardGroup = 0):\n", "    # Queries the master data dictionary for adl standard fields.\n", "    # Input parameters\n", "    # standardGroup: standard group of customer that have same columns. The default is 0 and includes all customers\n", "    # Returned object\n", "    # An array of standard fields belonging to the standard group customers. The default standarGroup 0 will return standard fields for all ADL customers.\n", "        df = spark.read.format(\"delta\").load(\"s3://\"+ bucket +\"/adl/\"+customerFolder+ \"config/deltaDictionary/\")\n", "        res = np.array(df.filter((df.StandardGroup == standardGroup) & (df.Standard == 1) & (Visible == 1)).select(\"ADLStandardFieldsName\").toPandas()[\"ADLStandardFieldsName\"].values)\n", "        res\n", "\n", "    def getStandardADL (self, customerName, standardGroup = 0):\n", "    # Queries the master data dictionary, and adl final dataset for standard adl.\n", "    # Input parameters\n", "    # customerName: customer name string\n", "    # standardGroup: standard group of customers that have the same columns. The default is 0 and includes all customers\n", "    # Returned object\n", "    # A customer feature-store spark dataframe with standard columns based on standardGroup.\n", "        df = spark.read.format(\"delta\").load(\"s3://\"+ bucket +\"/adl/\"+customerFolder+ \"config/deltaDictionary/\")\n", "        cols = np.array(df.filter((df.Customer == customerName) & (df.StandardGroup == standardGroup) & (df.Standard == 1) & (Visible == 1)).select(\"ADLStandardFieldsName\").toPandas()[\"ADLStandardFieldsName\"].values)\n", "        res = spark.read.format(\"delta\").load(\"s3://\"+ bucket +\"/adl/\"+customerFolder+ \"data/silver/final_dataset/\").select(cols)\n", "        res\n", "\n", "    def getADL (self, customerName):\n", "    # Queries the master data dictionary, and adl final dataset for customer specific adl.\n", "    # Input parameters\n", "    # customerName: customer name string\n", "    # Returned object\n", "    # A customer feature-store spark dataframe with specific and standard columns.\n", "        df = spark.read.format(\"delta\").load(\"s3://\"+ bucket +\"/adl/\"+customerFolder+ \"config/deltaDictionary/\")\n", "        cols = np.array(df.filter((df.Customer == customerName) & (Visible == 1)).select(\"ADLStandardFieldsName\").toPandas()[\"ADLStandardFieldsName\"].values)\n", "        res = spark.read.format(\"delta\").load(\"s3://\"+ bucket +\"/adl/\"+customerFolder+ \"data/silver/final_dataset/\").select(cols)\n", "        res\n", "\n", "    def getStatistics(self, customerName, LevelOfGranularity):\n", "    # Queries the master data dictionary and ADL data asset to bring static fields as well as aggregate fields at the right level of granularity. Return fields will be distinct.\n", "    # Input parameters\n", "    # customerName: customer name string\n", "    # levelOfGranularity: the level of granularity of the set of fields, that helps deduplicate the data and return the right level of granularity.\n", "    # Returned object\n", "    # A customer feature-store spark dataframe with specific fields\n", "        res = spark.read.format(\"delta\").load(\"s3://\"+ bucket +\"/adl/\"+customerFolder+ \"data/silver/cri_scores/\")\n", "        res"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}