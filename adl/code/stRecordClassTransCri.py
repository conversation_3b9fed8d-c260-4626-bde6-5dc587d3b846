from awsglue.transforms import *
# from pyspark.sql.functions import *
from pyspark.sql import SQLContext

from pyspark.sql.types import *
from pyspark.sql import Row, functions as F
from pyspark.sql.window import Window
from pyspark.sql.functions import when, lit, col
import pandas as pd
from pyspark.sql.types import IntegerType
from pyspark.sql.functions import col, expr, when, broadcast, datediff, to_date, last
from utils import Utils
import logging
import pyspark
import sys
# from pyspark.ml.feature import VectorAssembler

logger = None
# Needs to be read with a an argument from the job

class StRecordClassTransCri:
    def __init__(self, glue_context, customer, s3_destination, target, deltaTable):
        self.glueContext = glue_context
        self.customer = customer
        self.s3_destination = s3_destination
        self.utils = Utils(glue_context, customer)
        self.spark = self.glueContext.spark_session
        self.target = target
        self.DeltaTable = deltaTable

        global logger
        logger = logging.getLogger("ADL_DEV_LOGGER")
        pass

    def run(self, batch):
        try:
            target = self.target
            # TODO: this is hardcoded for the duplicates introduced by strategy target productId.
            # add productIdList, and accountIdList in V23


            if batch:
                sourcetable = "recordclasses_dse"
            # sourcetable = "recordclasses"
                df_rc = self.utils.dataLoadDelta(self.s3_destination + "data/silver/" + sourcetable)
                df_rc = df_rc.withColumn('allEffectivedate', to_date(df_rc.allEffectivedate, 'yyyy-MM-dd'))
                df_rc.createOrReplaceTempView("rc")
            else:
                df_rc = self.spark.sql("select * from rc")
                
            reps_accounts = df_rc.select("interactionRepAccount").distinct()
            logger.info("rc cached")
            # else:
            #     reps_accounts = self.spark.sql("select distinct Interaction_rep_account from rc")
            #     logger.info("reps_accounts was loaded from previous step")
            #     df_rc = df_rc.join(reps_accounts, "Interaction_rep_account").select(df_rc['*'])

            # get all the rep accounts from the dataset

            logger.info("reps_accounts count " + str(reps_accounts.count()))
            # get all months
            months = df_rc.filter(df_rc.interactionYearMonth.between(201101, 204001)).select(
                "interactionYearMonth").distinct().orderBy(["interactionYearMonth"], ascending=[1])
            master = reps_accounts.select('interactionRepAccount').distinct().crossJoin(
                months.select('interactionYearMonth')).distinct()
            # master=master.withColumn("Interaction_YearMonth", master["Interaction_YearMonth"].cast(IntegerType()))

            # ----------------------------------------------------------------------------------------------------------------------------
            # metric one
            # Email Open score

            # For each Rep_Account, calculate the total Email sent Windows F.count(recordclass)
            # For each Rep_Account, Month sum the Email Open using Windows F.sum(Email_open)

            emails = df_rc.where((col("emailStatus").isin({"Delivered_vod", "Sent_vod"})) & (
                        col("interactionTypeId") == "11")).select("recordclass", "interactionTypeId", "emailOpened",
                                                                  "interactionRepId", "interactionAccountId",
                                                                  "interactionRepAccount", "emailProduct",
                                                                  "interactionmonth", "interactionyear",
                                                                  "interactionYearMonth", "interactionStartDateLocal",
                                                                  F.count("recordclass").over(
                                                                      Window.partitionBy("interactionRepAccount",
                                                                                         "interactionYearMonth")).alias(
                                                                      "sentYearMonth"), F.sum("emailOpened").over(
                    Window.partitionBy("interactionRepAccount").orderBy('interactionYearMonth').rangeBetween(
                        Window.unboundedPreceding, 0)).alias("openTotal").cast(IntegerType()))

            # Before calculating the Email Open Score Choose last entry in the month as final value
            w1 = Window.partitionBy("interactionRepAccount", "interactionYearMonth").orderBy(
                F.desc("interactionStartDateLocal"))
            emails2 = emails.withColumn("rn", F.row_number().over(w1)).filter("rn=1").select("recordclass",
                                                                                             "interactionTypeId",
                                                                                             "emailOpened",
                                                                                             "interactionRepId",
                                                                                             "interactionAccountId",
                                                                                             "interactionRepAccount",
                                                                                             "emailProduct",
                                                                                             "interactionmonth",
                                                                                             "interactionyear",
                                                                                             "interactionYearMonth",
                                                                                             "sentYearMonth",
                                                                                             "openTotal")

            # number of emails sent by every rep_account [Total_Emails_Sent] usinf F.sum("sent_year_month")
            emails2 = emails2.select("recordclass", "interactionTypeId", "emailOpened", "interactionRepId",
                                     "interactionAccountId", "interactionRepAccount", "emailProduct",
                                     "interactionmonth", "interactionyear", "interactionYearMonth", "sentYearMonth",
                                     "openTotal", F.sum("sentYearMonth").over(
                    Window.partitionBy("interactionRepAccount").orderBy('interactionYearMonth').rangeBetween(
                        Window.unboundedPreceding, 0)).alias("sentTotal"))

            # Finally Email Open score which is Total Emails Opened/ Total Emails Sent
            emails2 = emails2.withColumn("emailOpenScore", when(col("sentTotal") >= 3,
                                                                  F.round(emails2.openTotal / emails2.sentTotal,
                                                                          2)).otherwise(lit(None)))

            # (cri.Open_Total + updatesRcDse.Open_Total / (cri.Sent_Total + updatesRcDse.Sent_Total)

            # update the probabilities down through time
            # look back the last Email_Open_Score which was not null for that rep_account
            emails2 = emails2.withColumn("emailOpenScore", F.last('emailOpenScore', True).over(
                Window.partitionBy('interactionRepAccount').orderBy('interactionYearMonth').rowsBetween(-sys.maxsize,
                                                                                                           0)))

            emails_final = emails2.select("interactionRepAccount", "interactionYearMonth",
                                          "emailOpenScore").drop_duplicates()

            master = master.join(emails_final, (master.interactionRepAccount == emails_final.interactionRepAccount) & (
                        master.interactionYearMonth == emails_final.interactionYearMonth), 'left_outer').select(
                master["*"], emails_final.emailOpenScore).dropDuplicates()
            master = master.withColumn("emailOpenScore", F.last('emailOpenScore', True).over(
                Window.partitionBy('interactionRepAccount').orderBy('interactionYearMonth').rowsBetween(-sys.maxsize,
                                                                                                           0)))

            # ----------------------------------------------------------------------------------------------------------------------------

            # Metric 2: Product Tenure
            df_rc = df_rc.withColumn('interactionStartDateLocalDt',
                                     to_date(df_rc.interactionStartDateLocal, 'yyyy-MM-dd'))
            df_rc = df_rc.orderBy(["interactionRepAccount", 'interactionStartDateLocalDt'], ascending=[1, 1])

            # Find time stamps for first and last interactions
            # min,max,previous event
            tenure = df_rc.select("recordclass", "interactionId", "interactionRepId", "interactionAccountId",
                                  "interactionRepAccount", "interactionTypeId", "repActionTypeId",
                                  "interactionStartDateTime", "interactionStartDateLocalDt", "interactionIsCompleted",
                                  "interactionYearMonth", F.min("interactionStartDateLocalDt").over(
                    Window.partitionBy("interactionRepAccount")).alias("eventFirst"),
                                  F.max("interactionStartDateLocalDt").over(
                                      Window.partitionBy("interactionRepAccount", "interactionYearMonth")).alias(
                                      "eventLast"), F.lag("interactionStartDateLocalDt").over(
                    Window.partitionBy("interactionRepAccount").orderBy("interactionStartDateLocalDt")).alias(
                    "eventPrevious"))

            # calculate tenure score by subtracting lastevent from Firstevent
            tenure = tenure.withColumn("tenureScore", datediff(tenure.eventLast, tenure.eventFirst))

            tenure2 = tenure.select("interactionRepAccount", "tenureScore", "interactionYearMonth").distinct()

            # update tenure score down the time by looking at previous null value for that rep_account
            tenure2.withColumn("tenureScore", F.last('tenureScore', True).over(
                Window.partitionBy('interactionRepAccount').orderBy('interactionYearMonth').rowsBetween(-sys.maxsize,
                                                                                                           0)))

            tenure2 = tenure2.select("interactionRepAccount", "interactionYearMonth", "tenureScore").drop_duplicates()

            # scale score
            logger.info("tenure scaling...")
            minScore, maxScore = tenure2.select(F.min(col('tenureScore')), F.max(col('tenureScore'))).first()
            if minScore is not None and maxScore is not None:
                scoreRange = maxScore - minScore
                if not scoreRange:
                    scoreRange = 1
                tenure2 = tenure2.withColumn('tenureScore', F.round((col('tenureScore') - minScore) / scoreRange, 2))


            master = master.join(tenure2, (master.interactionRepAccount == tenure2.interactionRepAccount) & (
                        master.interactionYearMonth == tenure2.interactionYearMonth), 'left_outer').select(master["*"],
                                                                                                             tenure2.tenureScore).dropDuplicates()
            master = master.withColumn("tenureScore", F.last('tenureScore', True).over(
                Window.partitionBy('interactionRepAccount').orderBy('interactionYearMonth').rowsBetween(-sys.maxsize,
                                                                                                           0)))
            logger.info("tenure scaled")
            # ----------------------------------------------------------------------------------------------------------------------------

            # metric 3 completed visits only
            # filter visit records based on interactiontype='4'
            visits = df_rc.where(col("interactionTypeId") == '4').select("interactionId", "interactionTypeId",
                                                                         "interactionRepId", "interactionAccountId",
                                                                         "interactionRepAccount",
                                                                         "interactionStartDateLocalDt",
                                                                         "interactionIsCompleted", "interactionIsDeleted",
                                                                         "interactionYearMonth")
            # get only compeletd visits
            visits = visits.where(col("interactionIsCompleted") == 1).distinct()

            # choose first in group where more than one visit to the same HCP is logged for the day
            visits = visits.select(visits["*"], F.row_number().over(
                Window.partitionBy("interactionRepAccount", "interactionStartDateLocalDt").orderBy(
                    "interactionRepAccount", "interactionStartDateLocalDt")).alias(
                "visitsSamedaySameRepAcct")).filter(col("visitsSamedaySameRepAcct") == 1)

            # find total count of all visits for that rep_account and total count of all visits per month
            visits = visits.select(visits["*"],
                                   F.count('interactionId').over(Window.partitionBy("interactionRepAccount")).alias(
                                       "visitCount"), F.count('interactionId').over(
                    Window.partitionBy("interactionRepAccount", "interactionYearMonth")).alias("repAccountCount"))

            # test
            # visits.where((col("Interaction_rep_account")=="1057_53146") & (col("Interaction_startDateLocal_dt")=="2019-02-08")).show()

            visits2 = visits.select("interactionRepAccount", "interactionYearMonth", "interactionTypeId", "visitCount",
                                    "repAccountCount").distinct()
            visits2 = visits2.withColumn("interactionYearMonth", visits2["interactionYearMonth"].cast(IntegerType()))

            # for every rep_account get the first_month and last_month interaction
            visits2 = visits2.select(visits2["*"], F.min("interactionYearMonth").over(
                Window.partitionBy("interactionRepAccount")).alias("firstMonth"), F.max("interactionYearMonth").over(
                Window.partitionBy("interactionRepAccount")).alias("lastMonth"))

            # join visits with reps table
            visits3 = master.join(visits2, (visits2.interactionRepAccount == master.interactionRepAccount) & (
                        visits2.interactionYearMonth == master.interactionYearMonth), 'left_outer').select(
                master.interactionRepAccount, master.interactionYearMonth, "interactionTypeId", visits2.visitCount,
                visits2.repAccountCount, visits2.firstMonth, visits2.lastMonth)

            # define the window for each first_month, if it's null look bacl previous month to assign it
            window = Window.partitionBy('interactionRepAccount').orderBy('interactionYearMonth')
            # define the forward-filled column
            filled_column_first = last(visits3['firstMonth'], ignorenulls=True).over(window)
            # do the fill
            visits3 = visits3.withColumn('firstMonth', filled_column_first)

            # define the for last-filled column
            filled_column_last = last(visits3['lastMonth'], ignorenulls=True).over(window)
            visits3 = visits3.withColumn('lastMonth', filled_column_last)

            # test
            # visits3.where(col("Interaction_rep_account")=='4328_47027').show()

            # check the condition with [Total visits for that rep_account for that month] and interactionmonth>first_month and Interactionmonth<=last_month then 0 else [Total visits for that rep_account for that month]
            visits3 = visits3.withColumn("repAccountCount", when(
                (col("repAccountCount").isNull()) & (visits3.interactionYearMonth >= visits3.firstMonth) & (
                            visits3.interactionYearMonth <= visits3.lastMonth), 0).otherwise(visits3.repAccountCount))

            # visits3.where(col("Interaction_rep_account")=="1013_1015").show()
            # final calculation, avergae cummulative mean for that rep_account by Interaction_year_month
            visits4 = visits3.where(col("visitCount") >= 3).withColumn('visitScore', F.round(F.avg(
                when(col("repAccountCount").isNull(), 0).otherwise(
                    (visits3.repAccountCount) + visits3.repAccountCount * 0)).over(
                Window.partitionBy("interactionRepAccount").orderBy("interactionYearMonth").rangeBetween(
                    Window.unboundedPreceding, 0)), 2)).select("interactionTypeId", "interactionRepAccount",
                                                               "interactionYearMonth", "visitScore").distinct()

            visits4 = visits4.select("interactionRepAccount", "interactionYearMonth", "visitScore").drop_duplicates()

            # scale score
            logger.info("visits scaling...")
            minScore, maxScore = visits4.select(F.min(col('visitScore')), F.max(col('visitScore'))).first()
            if minScore is not None and maxScore is not None:
                scoreRange = maxScore - minScore
                if not scoreRange:
                    scoreRange = 1
                visits4 = visits4.withColumn('visitScore', F.round((col('visitScore') - minScore) / scoreRange, 2))

            master = master.join(visits4, (master.interactionRepAccount == visits4.interactionRepAccount) & (
                        master.interactionYearMonth == visits4.interactionYearMonth), 'left_outer').select(master["*"],
                                                                                                             visits4.visitScore).dropDuplicates()
            master = master.withColumn("visitScore", F.last('visitScore', True).over(
                Window.partitionBy('interactionRepAccount').orderBy('interactionYearMonth').rowsBetween(-sys.maxsize,
                                                                                                           0)))
            logger.info("visits scaled")
            # ----------------------------------------------------------------------------------------------------------------------------

            # Metric 4: Product Rep-Account Visit Cadence
            df_rc = df_rc.withColumn('interactionStartDateLocal', to_date(df_rc.interactionStartDateLocal, 'yyyy-MM-dd'))
            df_rc = df_rc.withColumn("interactionYearMonth", df_rc["interactionYearMonth"].cast(IntegerType()))
            cadence = df_rc.where((col("interactionTypeId") == '4') & (col('interactionIsCompleted') == "1")).select(
                "interactionId", "interactionTypeId", "interactionRepId", "interactionAccountId",
                "interactionRepAccount", "interactionIsCompleted", "interactionStartDateLocal", "interactionIsDeleted",
                "interactionYearMonth")

            cadence = cadence.select(cadence["*"], F.min("interactionStartDateLocal").over(
                Window.partitionBy("interactionRepAccount")).alias("firstDate"),
                                     F.max("interactionStartDateLocal").over(
                                         Window.partitionBy("interactionRepAccount", "interactionYearMonth")).alias(
                                         "lastDate")).select(["*"]).withColumn("tenure",
                                                                                datediff("lastDate", "firstDate"))

            cadence = cadence.select(cadence["*"], F.lag("interactionStartDateLocal").over(
                Window.partitionBy("interactionRepAccount").orderBy("interactionStartDateLocal")).alias("nextDate"))
            cadence = cadence.select(cadence["*"],
                                     datediff("interactionStartDateLocal", "nextDate").alias("daysBtwVisits"))

            cadence = cadence.select(cadence["*"], F.round(F.avg("daysBtwVisits").over(
                Window.partitionBy("interactionRepAccount").orderBy("interactionYearMonth").rangeBetween(
                    Window.unboundedPreceding, 0)), 2).alias("avgDaysBtwVisits"), F.round(
                F.stddev("daysBtwVisits").over(
                    Window.partitionBy("interactionRepAccount").orderBy("interactionYearMonth").rowsBetween(
                        Window.unboundedPreceding, 0)), 2).alias("stdDaysBtwVisits"))

            columns = ['avgDaysBtwVisits', 'stdDaysBtwVisits']
            for column in columns:
                cadence = cadence.withColumn(column, F.when(F.isnan(F.col(column)), None).otherwise(F.col(column)))

            # test
            # cadence.where(col("Interaction_rep_account")=="2308_198078").show()
            # z.show(cadence.where(col("Interaction_rep_account")=="1018_5726").select("avg_days_btw_visits","std_days_btw_visits"))

            cadence2 = cadence.withColumn("cadenceScore",
                                          F.round((cadence.avgDaysBtwVisits + cadence.stdDaysBtwVisits) / 2,
                                                  2)).select("interactionTypeId", "interactionRepAccount",
                                                             "interactionYearMonth", "interactionStartDateLocal",
                                                             "cadenceScore").distinct()

            w1 = Window.partitionBy("interactionRepAccount", "interactionYearMonth").orderBy(
                "interactionStartDateLocal")
            cadence2 = cadence2.withColumn("countdays", F.max("interactionStartDateLocal").over(w1))
            w3 = Window.partitionBy("interactionRepAccount", "interactionYearMonth").orderBy(F.desc("countdays"))
            cadence3 = cadence2.withColumn("rn", F.row_number().over(w3)).filter("rn=1").select("interactionTypeId",
                                                                                                "interactionRepAccount",
                                                                                                "interactionYearMonth",
                                                                                                "cadenceScore")

            cadence3 = cadence3.select("interactionRepAccount", "interactionYearMonth",
                                       "cadenceScore").drop_duplicates()

            # scale score
            logger.info("cadence scaling...")
            minScore, maxScore = cadence3.select(F.min(col('cadenceScore')), F.max(col('cadenceScore'))).first()
            if minScore is not None and maxScore is not None:
                scoreRange = maxScore - minScore
                if not scoreRange:
                    scoreRange = 1
                cadence3 = cadence3.withColumn('cadenceScore', F.round(F.lit(1)-((col('cadenceScore') - minScore) / scoreRange), 2))

            master = master.join(cadence3, (master.interactionRepAccount == cadence3.interactionRepAccount) & (
                        master.interactionYearMonth == cadence3.interactionYearMonth), 'left_outer').select(master["*"],
                                                                                                              cadence3.cadenceScore).dropDuplicates()
            master = master.withColumn("cadenceScore", F.last('cadenceScore', True).over(
                Window.partitionBy('interactionRepAccount').orderBy('interactionYearMonth').rowsBetween(-sys.maxsize,
                                                                                                           0)))
            logger.info("cadence scaled")
            # ----------------------------------------------------------------------------------------------------------------------------

            sourcetable = "suggestions"
            df_suggestions = self.utils.dataLoadDelta(self.s3_destination + "data/bronze/" + sourcetable)
            # df_suggestions.cache()
            df_suggestions.createOrReplaceTempView("suggestions")
            logger.info("df_suggestions cached")

            spark_str = '''
            select suggestionReferenceId,detailRepActionTypeId,actionTaken,suggestedDate,repId,productId, accountId,concat(left(SUBSTRING_INDEX(suggestedDate,'-',2),4),right(SUBSTRING_INDEX(suggestedDate,'-',2),2)) suggestionYearMonth  from suggestions'''

            suggestions = self.spark.sql(spark_str)
            suggestions = suggestions.withColumn("repId", suggestions.repId.cast("String"))
            suggestions = suggestions.withColumn("accountId", suggestions.accountId.cast("String"))
            suggestions = suggestions.withColumn("suggestionRepAccount",
                                                 F.concat(F.col('repId'), F.lit('_'), F.col('accountId')))
            suggestions_visit = suggestions.where(col("detailRepActionTypeId") == '4').select("suggestionReferenceId",
                                                                                              "detailRepActionTypeId",
                                                                                              "actionTaken",
                                                                                              "suggestedDate", "repId",
                                                                                              "accountId",
                                                                                              "suggestionRepAccount",
                                                                                              "suggestionYearMonth")

            suggestions_visit = suggestions_visit.withColumn("lastFlag", when(col("suggestedDate") == (
                F.max("suggestedDate").over(
                    Window.partitionBy("suggestionRepAccount", "suggestionYearMonth", "suggestionReferenceId"))),
                                                                              1).otherwise(0))

            suggestions_visit = suggestions_visit.select(suggestions_visit["*"], F.count("suggestionReferenceId").over(
                Window.partitionBy("suggestionRepAccount", "suggestionYearMonth", "suggestionReferenceId")).alias(
                "uniqueSuggestionVisitCount")).filter("lastFlag==1")

            suggestions_visit = suggestions_visit.select(suggestions_visit["*"], F.count("suggestionReferenceId").over(
                Window.partitionBy("suggestionRepAccount", "suggestionYearMonth")).alias("totalSuggestionMo"),
                                                         when((col("actionTaken") == "Suggestions Completed"), 1).otherwise(
                                                             0).alias("suggestionComplete"))

            suggestions_visit = suggestions_visit.select(suggestions_visit["*"], F.sum("suggestionComplete").over(
                Window.partitionBy("suggestionRepAccount").orderBy("suggestionYearMonth").rangeBetween(
                    Window.unboundedPreceding, 0)).alias("completeTotal"))

            last_visit2 = suggestions_visit.select("suggestionRepAccount", "detailRepActionTypeId",
                                                   "suggestionYearMonth", "suggestedDate", "totalSuggestionMo",
                                                   "completeTotal").distinct()

            w3 = Window.partitionBy("suggestionRepAccount", "suggestionYearMonth").orderBy(F.desc("suggestedDate"))
            last_visit2 = last_visit2.withColumn("rn", F.row_number().over(w3)).filter("rn=1")

            last_visit2 = last_visit2.select(last_visit2["*"], F.sum("totalSuggestionMo").over(
                Window.partitionBy("suggestionRepAccount").rowsBetween(Window.unboundedPreceding, 0)).alias(
                "suggestionTotal"))

            last_visit2 = last_visit2.withColumn("suggestionVisitScore", when(col("suggestionTotal") >= 3, F.round(
                last_visit2.completeTotal / last_visit2.suggestionTotal, 2)).otherwise(lit(None)))
            last_visit2 = last_visit2.select("detailRepActionTypeId", "suggestionRepAccount", "suggestionYearMonth",
                                             "suggestionVisitScore").distinct()

            last_visit2 = last_visit2.select("suggestionRepAccount", "suggestionYearMonth",
                                             "suggestionVisitScore").drop_duplicates()

            master = master.join(last_visit2, (master.interactionRepAccount == last_visit2.suggestionRepAccount) & (
                        master.interactionYearMonth == last_visit2.suggestionYearMonth), 'left_outer').select(master["*"],
                                                                                                                last_visit2.suggestionVisitScore).dropDuplicates()
            master = master.withColumn("suggestionVisitScore", F.last('suggestionVisitScore', True).over(
                Window.partitionBy('interactionRepAccount').orderBy('interactionYearMonth').rowsBetween(-sys.maxsize,
                                                                                                           0)))

            # ----------------------------------------------------------------------------------------------------------------------------

            # metric 6
            suggestions_email = suggestions.where(col("detailRepActionTypeId") == '8').select("suggestionReferenceId",
                                                                                              "detailRepActionTypeId",
                                                                                              "actionTaken",
                                                                                              "suggestedDate", "repId",
                                                                                              "accountId",
                                                                                              "suggestionRepAccount",
                                                                                              "suggestionYearMonth")
            suggestions_email = suggestions_email.withColumn("lastFlag", when(col("suggestedDate") == (
                F.max("suggestedDate").over(
                    Window.partitionBy("suggestionRepAccount", "suggestionYearMonth", "suggestionReferenceId"))),
                                                                              1).otherwise(0))

            suggestions_email = suggestions_email.select(suggestions_email["*"], F.count("suggestionReferenceId").over(
                Window.partitionBy("suggestionRepAccount", "suggestionYearMonth", "suggestionReferenceId")).alias(
                "uniqueSuggestionEmailCount")).filter("lastFlag==1")

            suggestions_email = suggestions_email.select(suggestions_email["*"], F.count("suggestionReferenceId").over(
                Window.partitionBy("suggestionRepAccount", "suggestionYearMonth")).alias("totalSuggestionMo"),
                                                         when((col("actionTaken") == "Suggestions Completed"), 1).otherwise(
                                                             0).alias("suggestionComplete"))

            suggestions_email = suggestions_email.select(suggestions_email["*"], F.sum("suggestionComplete").over(
                Window.partitionBy("suggestionRepAccount").orderBy("suggestionYearMonth").rangeBetween(
                    Window.unboundedPreceding, 0)).alias("completeTotal"))

            last_email2 = suggestions_email.select("suggestionRepAccount", "detailRepActionTypeId",
                                                   "suggestionYearMonth", "suggestedDate", "totalSuggestionMo",
                                                   "completeTotal").distinct()

            w5 = Window.partitionBy("suggestionRepAccount", "suggestionYearMonth").orderBy(F.desc("suggestedDate"))
            last_email2 = last_email2.withColumn("rn", F.row_number().over(w5)).filter("rn=1")

            last_email2 = last_email2.select(last_email2["*"], F.sum("totalSuggestionMo").over(
                Window.partitionBy("suggestionRepAccount").rowsBetween(Window.unboundedPreceding, 0)).alias(
                "suggestionTotal"))

            last_email2 = last_email2.withColumn("suggestionEmailScore", when(col("suggestionTotal") >= 3, F.round(
                last_email2.completeTotal / last_email2.suggestionTotal, 2)).otherwise(lit(None)))

            last_email2 = last_email2.select("suggestionRepAccount", "suggestionYearMonth",
                                             "suggestionEmailScore").distinct()

            master = master.join(last_email2, (master.interactionRepAccount == last_email2.suggestionRepAccount) & (
                        master.interactionYearMonth == last_email2.suggestionYearMonth), 'left_outer').select(master["*"],
                                                                                                                last_email2.suggestionEmailScore).dropDuplicates()
            master = master.withColumn("suggestionEmailScore", F.last('suggestionEmailScore', True).over(
                Window.partitionBy('interactionRepAccount').orderBy('interactionYearMonth').rowsBetween(-sys.maxsize,
                                                                                                           0)))

            # ----------------------------------------------------------------------------------------------------------------------------

            # check if we have to add target

            if target == 'yes':
                sourcetable = "strategy_target"
                df_st = self.utils.dataLoadDelta(self.s3_destination + "data/bronze/" + sourcetable)
                df_st.cache()
                df_st.createOrReplaceTempView("st")
                logger.info("st cached")

                # sourcetable = "rep_account_assignment"
                # df_ra = self.utils.dataLoadDelta(self.s3_destination + "data/bronze/" + sourcetable)
                # df_ra.cache()
                # df_ra.createOrReplaceTempView("ra")
                # logger.info("ra cached")
                #
                # sourcetable = "rep_team_rep"
                # df_rt = self.utils.dataLoadDelta(self.s3_destination + "data/bronze/" + sourcetable)
                # df_rt.cache()
                # df_rt.createOrReplaceTempView("rt")
                # logger.info("df_rt cached")

                # get target and process a bit

                spark_str = """
                select targetAccount, repTeamIDAssociated, repIDAssociated,
                case when targetingLevelId=6 then 'VISIT' 
                when targetingLevelId=5 then 'SEND_ANY' end as interactionTargetingLevelId,
                case when targetingLevelId=6 then 'VISIT_DETAIL' 
                when targetingLevelId=5 then 'SEND' end as suggestionTargetingLevelId
                ,t.targetsPeriodId,avg(t.target) as target, t.startDate,t.endDate,concat(left(SUBSTRING_INDEX(t.startDate,'-',2),4),right(SUBSTRING_INDEX(t.startDate,'-',2),2)) startDateMonth,concat(left(SUBSTRING_INDEX(t.endDate,'-',2),4),right(SUBSTRING_INDEX(t.endDate,'-',2),2)) endDateMonth
                from st t 
                group by
                interactionTargetingLevelId, suggestionTargetingLevelId,repIDAssociated,repTeamIDAssociated,targetAccount,startDate, endDate,targetsPeriodId,startDateMonth, endDateMonth
                """

                df_st_2 = self.spark.sql(spark_str)
                df_st_2 = df_st_2.withColumn("repTeamIDAssociated", df_st_2.repTeamIDAssociated.cast("String"))
                df_st_2 = df_st_2.withColumn("repIDAssociated", df_st_2.repIDAssociated.cast("String"))
                df_st_2 = df_st_2.withColumn("targetAccount", df_st_2.targetAccount.cast("String"))
                # df_st_2=self.spark.sql(spark_str)
                logger.info(df_st_2.printSchema())
                df_st_2.createOrReplaceTempView("st_2")

                spark_str = """
                select * from (
                select rd.*,t1.interactionTargetingLevelId,t1.suggestionTargetingLevelId,t1.targetsPeriodId,t1.target from rc rd 
                left join st_2 t1 
                on 
                allAccountId=t1.targetAccount and ((t1.repIDAssociated is null and rd.repTeamId = t1.repTeamIDAssociated)
                OR (t1.repIDAssociated is not null and t1.repIDAssociated = rd.allRepId))
                and COALESCE(rd.interactionTypeName,rd.suggestionDetailRepActionName)=COALESCE(t1.interactionTargetingLevelId,t1.suggestionTargetingLevelId)
                and rd.allEffectivedate between t1.startDate and t1.endDate
                ) t 
                """

                df_rc_2 = self.spark.sql(spark_str)

                # df_rc_2.createOrReplaceTempView("rc_v2")
                # df_rc_2.repartition("recordclassid").write.mode('overwrite').partitionBy("recordclassid").parquet(s3_destination+customer+'/'+environment+"/recordclasses_st/")
                if batch:
                    logger.info("st recordclass size " + str(df_rc_2.count()))
                    df_rc_2.repartition(10).write.mode('overwrite').partitionBy("allEffectiveyearmonth", "recordclassId").format(
                        "delta").save(self.s3_destination + "data/silver/recordclasses_st/")
                    df_rc_2.createOrReplaceTempView("rc")
                    logger.info("done writing recordclasses_st")

                else:
                    df_rc_2.persist(pyspark.StorageLevel.MEMORY_ONLY)
                    logger.info("st recordclass size " + str(df_rc_2.count()))
                    update_path = self.s3_destination + "data/silver/rc_updates"
                    df_rc_2.write.mode("overwrite").parquet(update_path)
                    # broadcast(df_rc_2)
                    min_yearmonth = df_rc_2.agg({"allEffectiveyearmonth":"min"}).collect()[0][0]
                    self.utils.delete_sugg_recordclass_if_repeated(df_rc_2.repartition("allEffectiveyearmonth", "recordclassId"), self.s3_destination + "data/silver/recordclasses_st/",
                                                                   self.DeltaTable)
                    self.utils.upsertRecordClass(df_rc_2.repartition("allEffectiveyearmonth", "recordclassId"), self.s3_destination + "data/silver/recordclasses_st/",
                                                 self.DeltaTable, "allEffectiveyearmonth", min_yearmonth)
                    df_rc_2.createOrReplaceTempView("rc")
                    logger.info("done upserting recordclasses_st")
                    # df_rc_2 = self.utils.dataLoadDelta(self.s3_destination + "data/silver/recordclasses_st/")

                # metric 7
                df_st_3 = df_st_2.select('targetsPeriodId', 'startDateMonth', 'endDateMonth').distinct()
                df_st_3.createOrReplaceTempView("st_2")

                spark_sql = """select distinct scores.*, st_2.startDateMonth, st_2.endDateMonth
                from
                (
                select interactionRepAccount, targetsPeriodId, round(
                avg(score)
                , 2) as targetAchievementScore
                from
                (select interactionRepAccount, targetsPeriodId, interactionTypeId, targetAchievement as score
                from
                (
                select *, case when targetTotal is not null and targetTotal > 0 and targetCount is not null then targetCount / targetTotal else targetCount end as targetAchievement
                from
                (select interactionRepAccount, targetsPeriodId, interactionTypeId, sum(CASE WHEN target is not null THEN target ELSE 0 END) as targetTotal,  count(CASE WHEN interactionIsCompleted = '1' THEN 1 END) as targetCount
                from rc
                group by interactionRepAccount, targetsPeriodId, interactionTypeId
                )
                )
                where interactionTypeId in (11,4)
                )
                where score is not null and score > 0
                group by interactionRepAccount, targetsPeriodId
                ) scores
                left outer join st_2 on scores.targetsPeriodId == st_2.targetsPeriodId"""

                pt3 = self.spark.sql(spark_sql)


                master = master.join(pt3, (master.interactionRepAccount == pt3.interactionRepAccount) & (
                        master.interactionYearMonth >= pt3.startDateMonth) & (
                                             master.interactionYearMonth <= pt3.endDateMonth),
                                     'left_outer').select(master["*"],
                                                          pt3.targetAchievementScore).dropDuplicates()

                # scale score
                logger.info("finish metric 7 join, start scaling..")
                minScore, maxScore = master.select(F.min(col('targetAchievementScore')),
                                                F.max(col('targetAchievementScore'))).first()
                if minScore is not None and maxScore is not None:
                    scoreRange = maxScore - minScore
                    if not scoreRange:
                        scoreRange = 1
                    master = master.withColumn('targetAchievementScore', when(col('targetAchievementScore').isNotNull(),
                                                                          F.round((col('targetAchievementScore') - minScore) / scoreRange, 2)).otherwise(None))



                # metric 8
                spark_sql = """
                select distinct scores.*, st_2.startDateMonth, st_2.endDateMonth
                from
                (select interactionRepAccount, targetsPeriodId, round(avg(channelQuarter), 2) as channelScore
                from
                (select interactionRepAccount, targetsPeriodId, CASE WHEN count(distinct interactionTypeId) > 1 THEN min(channelTarget) / max(channelTarget) ELSE NULL END as channelQuarter
                from
                (select *, CASE WHEN target is not null and target > 0 and channelCountPeriod is not null THEN channelCountPeriod / target ELSE channelCountPeriod END as channelTarget
                from
                (select interactionRepAccount, targetsPeriodId, interactionTypeId, sum(CASE WHEN target is not null THEN target ELSE 0 END) as target, count(CASE WHEN interactionIsCompleted = '1' THEN 1 END) as channelCountPeriod
                from rc
                group by interactionRepAccount,targetsPeriodId, interactionTypeId
                )
                where interactionTypeId in (11,4)
                )
                where channelTarget is not null
                group by interactionRepAccount, targetsPeriodId
                )
                where channelQuarter is not null
                group by interactionRepAccount, targetsPeriodId
                ) scores
                left outer join st_2 on scores.targetsPeriodId == st_2.targetsPeriodId
                """

                ct3 = self.spark.sql(spark_sql)

                master = master.join(ct3, (master.interactionRepAccount == ct3.interactionRepAccount) & (
                        master.interactionYearMonth >= ct3.startDateMonth) & (
                                                      master.interactionYearMonth <= ct3.endDateMonth),
                                          'left_outer').select(master["*"],
                                                               ct3.channelScore).dropDuplicates()

                # metric 9 & 10
                criScoreCols = [c for c in master.columns if c not in ('interactionRepAccount', 'interactionYearMonth')]
                master = master.select('*', (F.lit(len(criScoreCols)) - sum(
                    [F.isnull(master[col]).cast(IntegerType()) for col in criScoreCols])).alias('nonNullCount'))
                master = master.select('*', when(master['nonNullCount']!=0, F.round(sum([F.coalesce(F.col(x), lit(0)) for x in criScoreCols]),2)).otherwise(None).alias('criScoreSum'))
                master = master.withColumn('criScoreAvg', F.round(F.col("criScoreSum") / F.col("nonNullCount"),2))
                master = master.drop('nonNullCount')


                ##############
                # master = master.withColumn("targetAchievementScore", lit(None).cast(StringType()))
                # master = master.withColumn("channelScore", lit(None).cast(StringType()))
                # master = master.withColumn("targetAchievementScore",
                #                            when(master.targetAchievementScore.isNull(), 0.0).otherwise(0.0))
                # master = master.withColumn("channelScore", when(master.channelScore.isNull(), 0.0).otherwise(0.0))
                ###############
                # master.write.mode('overwrite').partitionBy("Interaction_YearMonth").format("delta").save(
                #     self.s3_destination + "data/silver/cri_scores/")
                if batch:
                    master.write.mode('overwrite').partitionBy("interactionYearMonth").format("delta").save(
                        self.s3_destination + "data/silver/cri_scores/")
                else:
                    deltaTable = self.DeltaTable.forPath(self.spark, self.s3_destination + "data/silver/cri_scores/")
                    master = master.join(df_rc_2.select("interactionRepAccount").distinct(), "interactionRepAccount").select(master['*'])
                    master.persist(pyspark.StorageLevel.MEMORY_ONLY)
                    logger.info("cri_score size " + str(master.count()))
                    broadcast(master)
                    deltaTable.alias("cri_scores").merge(
                        master.alias("updates"),
                        " cri_scores.interactionYearMonth = updates.interactionYearMonth and cri_scores.interactionRepAccount = updates.interactionRepAccount ") \
                        .whenMatchedUpdateAll() \
                        .whenNotMatchedInsertAll() \
                        .execute()

            else:
                master = master.withColumn("targetAchievementScore", lit(None).cast(StringType()))
                master = master.withColumn("channelScore", lit(None).cast(StringType()))
                master = master.withColumn("targetAchievementScore",
                                           when(master.targetAchievementScore.isNull(), 0.0).otherwise(0.0))
                master = master.withColumn("channelScore", when(master.channelScore.isNull(), 0.0).otherwise(0.0))
                # master.write.mode('overwrite').partitionBy("Interaction_YearMonth").format("delta").save(
                #     self.s3_destination + "data/silver/cri_scores/")

                # metric 9 & 10
                criScoreCols = [c for c in master.columns if c not in ('interactionRepAccount', 'interactionYearMonth')]
                master = master.select('*', (F.lit(len(criScoreCols)) - sum(
                    [F.isnull(master[col]).cast(IntegerType()) for col in criScoreCols])).alias('nonNullCount'))
                master = master.select('*', when(master['nonNullCount'] != 0,
                                                 F.round(sum([F.coalesce(F.col(x), lit(0)) for x in criScoreCols]),
                                                         2)).otherwise(None).alias('criScoreSum'))
                master = master.withColumn('criScoreAvg', F.round(F.col("criScoreSum") / F.col("nonNullCount"), 2))
                master = master.drop('nonNullCount')

                if batch:
                    master.write.mode('overwrite').partitionBy("interactionYearMonth").format("delta").save(
                        self.s3_destination + "data/silver/cri_scores/")
                else:
                    deltaTable = self.DeltaTable.forPath(self.spark, self.s3_destination + "data/silver/cri_scores/")
                    df_rc_2 = self.spark.sql("select * from rc")
                    master = master.join(df_rc_2.select("interactionRepAccount").distinct(),
                                         "interactionRepAccount").select(master['*'])
                    master.persist(pyspark.StorageLevel.MEMORY_ONLY)
                    logger.info("cri_score size " + str(master.count()))
                    broadcast(master)
                    deltaTable.alias("cri_scores").merge(
                        master.alias("updates"),
                        " cri_scores.interactionYearMonth = updates.interactionYearMonth and cri_scores.interactionRepAccount = updates.interactionRepAccount ") \
                        .whenMatchedUpdateAll() \
                        .whenNotMatchedInsertAll() \
                        .execute()
        except Exception as e:
            logger.error("Error in stRecordClassTransCri: {}".format(e))
            raise

# ----------------------------------------------------------------------------------------------------------------------------
