
from pyspark.context import SparkContext
import string
from utils import Utils
from pyspark.sql.types import *
import logging
import pandas as pd
from pyspark.sql import Window
from pyspark.sql.functions import *
from datetime import datetime


logger = None
DSERUN_TABLES = ["sparkdserun","sparkdserunrepdate","sparkdserunrepdatesuggestion","sparkdserunrepdatesuggestiondetail"]
TABLES_WITH_SCHEMA = ['accountproduct', 'account_dse']
STRATEGY_TARGET_TABLES = ["strategytarget", "targetsperiod", "targetinglevel"]
CS_TABLES = {
    "sent_email_vod__c": ["Account_Email_vod__c","Capture_Datetime_vod__c","Click_Count_vod__c"
                            ,"Email_Config_Values_vod__c" ,"Email_Content_vod__c","Email_Sent_Date_vod__c"
                            ,"Open_Count_vod__c","Opened_vod__c","Product_Display_vod__c"
                            ,"Product_vod__c","Sender_Email_vod__c","Status_vod__c","Account_vod__c","Id","LastModifiedDate"],
    "call2_vod__c": ["Id","Call_Type_vod__c","Account_vod__c","Address_Line_1_vod__c","State_vod__c"
                    ,"City_vod__c","Zip_4_vod__c","Zip_vod__c","Address_Line_2_vod__c","Attendee_Type_vod__c"
                    ,"Call_Date_vod__c","Call_Datetime_vod__c","Detailed_Products_vod__c","Next_Call_Notes_vod__c"
                    ,"Submitted_By_Mobile_vod__c","LastModifiedDate"]
}
TABLES_WITH_OLD_DATA = {
    "interaction": "startDateLocal",
    "rpt_suggestion_delivered_stg": "suggestedDate",
    "sent_email_vod__c": "CreatedDate",
    "call2_vod__c": "CreatedDate",

}

class Data_loading:
    def __init__(self, glue_context, customer, environment, df_load_pandas,df_map_pandas, batch, DeltaTable, target):
        self.glueContext = glue_context
        self.customer = customer
        self.environment = environment
        self.utils = Utils(glue_context, customer)
        self.spark = glue_context.spark_session
        self.df_map_pandas = df_map_pandas
        self.df_load_pandas = df_load_pandas
        self.batch = batch
        self.lastDate = ""
        self.DeltaTable = DeltaTable
        self.target = target == 'yes'
        if not self.batch:
            self.condition = {
                "dse_score":"dse_score.runId = updates.runId and dse_score.suggestionReferenceId = updates.suggestionReferenceId"
                ,"strategy_target":"strategy_target.strategyTargetId = updates.strategyTargetId"
                , "rep_account_assignment": "rep_account_assignment.repId = updates.repId and rep_account_assignment.accountId = updates.accountId"
                , "rep_product_assignment": "rep_product_assignment.repId = updates.repId and rep_product_assignment.productId = updates.productId"
                , "rep_product_authorization": "rep_product_authorization.repId = updates.repId and rep_product_authorization.productId = updates.productId "
                , "account": "account.accountId = updates.accountId"
                , "approved_document": "approved_document.id = updates.id"
                , "rep_team_rep": "rep_team_rep.repTeamId = updates.repTeamId and rep_team_rep.repId = updates.repId"
                , "rep": "rep.repId = updates.repId"
                , "accountproduct": "accountproduct.productId = updates.productId and accountproduct.accountId = updates.accountId"
                , "product": "product.productId = updates.productId"
                , "channel": "channel.channelId = updates.channelId"
                , "rep_action_type": "rep_action_type.repActionTypeId = updates.repActionTypeId"
                , "emails_ref": "emails_ref.id = updates.id"
            }
        self.partitionColumns = {"interaction": ["interactionId"],
                            "interactiontype": ["interactionTypeId"], "repactiontype": ["repActionTypeId"],
                            "call2_vod__c": ["Id"], "interactionaccount": ["interactionId", "accountId"],
                            "interactionproduct": ["interactionId", "productId"], "messagetopic": ["messageTopicId"],
                            "message": ["messageId", "messageChannelId"], "messagechannel":["messageChannelId"], "sent_email_vod__c": ["Id"],
                            "product": ["productId"], "productinteractiontype": ["productInteractionTypeId"],
                            "physicalmessage": ["physicalMessageId"], "dserun": ["runId"],"sparkdserun": ["runUID"],
                            "dserunrepdate": ["runRepDateId"],
                            "sparkdserunrepdate": ["runRepDateId"],
                            "dserunrepdatesuggestion": ["runRepDateSuggestionId"],
                            "sparkdserunrepdatesuggestion": ["runRepDateSuggestionId"],
                            "dserunrepdatesuggestiondetail": ["runRepDateSuggestionDetailId"],
                            "sparkdserunrepdatesuggestiondetail": ["runRepDateSuggestionDetailId"],
                            "repaccountassignment": ["repId", "accountId"], "account_dse": ["accountId"],
                            "account_cs": ["Id"], "facility": ["facilityId"],
                            "rep": ["repId"], "replocation": ["repId"],
                            "repteam": ["repTeamId"], "akt_replicense_arc": ["cluster"],
                            "approved_document_vod__c": ["Id"], "accountproduct": ["accountId", "productId"],
                            "strategytarget": ["targetsPeriodId", "targetingLevelId", "facilityId", "accountGroupId", "accountId", "repTypeId", "productId", "messageTopicId", "messageId", "interactionTypeId", "productInteractionTypeId"], "targetsperiod": ["targetsPeriodId"],
                            "targetinglevel": ["targetingLevelId"],
                            "repproductauthorization": ["repId", "productId"],
                            "rpt_suggestion_delivered_stg": ["runRepDateSuggestionId", "externalId", "accountId", "repId", "productId"],
                            "channel":["channelId"], "channelcategory":["channelCategoryId"], "channeltype":["channelTypeId"],
                            "accounttype":["accountTypeId"], "reptype":["repTypeId"], "actionchannelmap":["actionChannelMapId"],
                            "actiongroup": ['actionGroupId'], "activitydeliverymode":['activityDeliveryModeId'],
                            "suggestiondeliverymode":["suggestionDeliveryModeId"]
                            }

        self.orderByColumns = {"interaction": ["updatedAt"],
                          "interactiontype": ["updatedAt"], "repactiontype": ["updatedAt"],
                          "call2_vod__c": ["LastModifiedDate"], "interactionaccount": ["updatedAt"],
                          "interactionproduct": ["updatedAt"], "messagetopic": ["updatedAt"],
                          "message": ["updatedAt"],"messagechannel": ["updatedAt"], "sent_email_vod__c": ["LastModifiedDate"],
                          "product": ["updatedAt"], "productinteractiontype": ["updatedAt"],
                          "physicalmessage": ["updatedAt"], "sparkdserun": ["updatedAt"],
                          "sparkdserunrepdate": ["updatedAt"], "dserunaccount": ["updatedAt"],
                          "sparkdserunrepdatesuggestion": ["updatedAt"], "sparkdserunrepdatesuggestiondetail": ["updatedAt"],
                          "repaccountassignment": ["updatedAt"], "account_dse": ["updatedAt"],
                          "account_cs": ["LastModifiedDate"], "facility": ["updatedAt"],
                          "rep": ["updatedAt"], "replocation": ["updatedAt"],
                          "repteam": ["updatedAt"], "akt_replicense_arc": ["updatedAt"],
                          "approved_document_vod__c": ["LastModifiedDate"], "accountproduct": ["updatedAt"],
                          "strategytarget": ["updatedAt"], "targetsperiod": ["updatedAt"],
                          "targetinglevel": ["updatedAt"], "repproductauthorization": ["updatedAt"],
                          "rpt_suggestion_delivered_stg": ["updatedAt"],
                          "channel": ["updatedAt"], "channelcategory": ["updatedAt"],
                          "channeltype": ["updatedAt"],
                          "accounttype": ["updatedAt"], "reptype": ["updatedAt"],
                          "actionchannelmap": ["updatedAt"],
                          "actiongroup": ["updatedAt"], "activitydeliverymode": ["updatedAt"],
                          "suggestiondeliverymode": ["updatedAt"]
        }
        global logger
        logger = logging.getLogger("ADL_DEV_LOGGER")

    def sql_to_template(self, sqlcommand, values):
        '''
        Templated sqlstring to be replaced by the values
        :param sqlcommand:
        :param values:
        :return:
        '''

        template = string.Template('%s' % (sqlcommand))
        sqlstr = template.substitute(values);
        sqlstr = sqlstr.replace('\n', ' ');
        sqlstr = sqlstr.strip()
        return sqlstr




    def get_column_mapping(self, df_map_pandas, tablename):
        '''
        Reads mapping dictionary and populates the mapping values for customer
        :param df_map_pandas: mapping dictionary from column mapping file
        :param values_database: holds the mapping values for customer
        :param customer: actual customer from config file
        :return:  mapping values for customer
        '''
        values_database = {}
        table_maskedName = {}

        df_map_table = df_map_pandas[df_map_pandas['Table_name'] == tablename]

        if df_map_table.shape[0] > 0:
            # if there are matched tables in df_map_pandas
            standard_df = df_map_table[df_map_pandas['Customer_name'] == 'ALL']
            customer_df = df_map_table[df_map_pandas['Customer_name'] == self.customer]

            for df in [standard_df, customer_df]:
                for _, row in df.iterrows():
                    if len(row['Masked_name']) != 0:
                        name_split = row['Customer_attr_watermark'].split(' ')
                        name_split.append(name_split.pop())
                        #print(row['Masked_name'], ' '.join(each for each in name_split))
                        values_database[row['Masked_name']] = ' '.join(each for each in name_split)
                        logger.info("Masked name: {}, watermark: {}".format(row['Masked_name'], values_database[row['Masked_name']]))
                        if row['Table_name'] not in table_maskedName:
                            table_maskedName[row['Table_name']] = [row['Masked_name']]
                        else:
                            table_maskedName[row['Table_name']].append(row['Masked_name'])

        return values_database, table_maskedName


    def preparesql(self, df_load_pandas, values_database, tablename):
        '''

        :param date_time:
        :param region:
        :param customer:
        :param environment:
        :param glueContext:
        :param df_load_pandas:
        :param values_database:
        :param tablename:
        :return:
        '''
        partition_columns_list = []
        for index, row in df_load_pandas.iterrows():
            # print(row['table_name'],tablename)
            if row['table_name'] == tablename:
                delta_col = row['delta_column']
                partition_columns_list = row['parition_keys_columns'].split(',')
                if len(partition_columns_list) > 0 and partition_columns_list[0] != '':
                    partition_columns_list = [self.utils.renameColumns(col) for col in partition_columns_list]
                logger.info("tablename is matched --> " + tablename)
                values_database['otherfilter'] = row['full']
                logger.info("Below is the Sql Query for table Name: "  + tablename)
                if tablename == "visits" and values_database['channel'] == "'' as channel":
                    if self.spark.sql("select * from interaction where interactionTypeId = 12 limit 5").count() > 0:
                        channel = """CASE
                                        WHEN i.interactionTypeId = 12 THEN 'WEB_INTERACTIVE_CHANNEL'
                                        ELSE 'VISIT_CHANNEL'
                                     END as channel"""
                    elif ("Remote_Meeting_vod__c" in self.spark.sql("select * from call2_vod__c limit 1").columns):
                        channel = """CASE
                                        WHEN visit.Remote_Meeting_vod__c IS NOT NULL THEN 'WEB_INTERACTIVE_CHANNEL'
                                        ELSE 'VISIT_CHANNEL'
                                     END as channel"""
                    else:
                        channel = "'VISIT_CHANNEL' as channel"
                    values_database["channel"] = channel
                sql_query = self.sql_to_template(row['sql_query'], values_database)
                logger.info(sql_query + " " + ' '.join([str(elem) for elem in partition_columns_list]))
                return sql_query, partition_columns_list



    def deDup(self, df, partitionColumns, orderByColumns):
        '''
        :param df:
        :param partitionColumns:
        :param orderByColumns:
        :return:
        '''
        w1 = Window.partitionBy(*partitionColumns).orderBy(desc(*orderByColumns))
        df = df.withColumn("filter_duplicates", row_number().over(w1)).filter("filter_duplicates=1").select(["*"])
        df = df.drop(df.filter_duplicates)
        return df


        # derive a strategy for empty tables from reporting, similar to null columns in the mapping
    def load_tables_from_s3(self,tables, source_s3_location, archive_folder):
        '''
        :param tables:
        :param source_s3_location:
        :param environment:
        :param archive_folder:
        :return:
        '''
        # if self.batch and (self.environment == "qa" or self.environment == "dev"):
        #     testDate = self.spark.read.parquet(source_s3_location + "rpt_suggestion_delivered_stg")\
        #                                     .agg({"suggestedDate": "max"}).collect()[0][0] - pd.DateOffset(weeks=1)
        #     logger.info(["Test date is ", testDate])
        tables_found = []
        tables_not_found = []
        table_columns = {}
        # get tables in source s3
        logger.info("start getting table names from archive")
        tables_in_source = self.utils.getArchiveRdsTables(source_s3_location)
        logger.info("successfully got tables")
        for table in tables:
            path = None
            sparkPath = None
            # if table == 'suggestions':
            #     table = 'rpt_suggestion_delivered_stg'
            #     path = 's3://aktana-bdp-adl/'+self.customer+'/prod/suggestions/'
            # if table in DSERUN_TABLES:
            #     sparkTable = 'spark' + table
            #     if not (table in tables_in_source or sparkTable in tables_in_source):
            #         logger.info([table, sparkTable, "Tables do not exist"])
            #         tables_not_found.append(table)
            #         tables_not_found.append(sparkTable)
            #         continue
            #     if self.batch:
            #         if table in tables_in_source:
            #             path = source_s3_location + table
            #         else:
            #             logger.info([table, "Table does not exist"])
            #             tables_not_found.append(table)
            #         if sparkTable in tables_in_source:
            #             # if no dserun*, just use 'path' to read sparkdserun*
            #             if not path:
            #                 path = source_s3_location + sparkTable
            #             else:
            #                 sparkPath = source_s3_location + sparkTable
            #         else:
            #             logger.info([sparkTable, "Table does not exist"])
            #             tables_not_found.append(sparkTable)
            #     else:
            #         if sparkTable in tables_in_source:
            #             path = source_s3_location + sparkTable
            #         else:
            #             path = source_s3_location + table
            # else:
            #     if table not in tables_in_source:
            #         logger.info([table, "Table does not exist"])
            #         tables_not_found.append(table)
            #         continue
            #     path = source_s3_location + table

            if (table not in tables_in_source) or (table in STRATEGY_TARGET_TABLES and not self.target):
                logger.info([table, "Table does not exist"])
                tables_not_found.append(table)
                continue
            path = source_s3_location + table

            logger.info(path)
            logger.info("Loading the table from Parquet")
            if table in TABLES_WITH_SCHEMA:
                df = self.utils.data_load_parquet_with_schema(self.glueContext, path)
            else:
                df = self.utils.data_load_parquet(self.glueContext, path)
            dfCol = df.columns
            if table in DSERUN_TABLES:
                if "runId" not in dfCol:
                    df = df.withColumn('runId', lit(None).cast(NullType()))
                    dfCol.append("runId")
                if "runUID" not in dfCol:
                    df = df.withColumn('runUID', lit(None).cast(NullType()))
                    dfCol.append("runUID")
                # if table == 'dserun':
                #     if 'spark' in path:
                #         df = df.withColumn('runId', lit(None).cast(NullType()))
                #         df = self.deDup(df, self.partitionColumns[sparkTable], self.orderByColumns[table])
                #     else:
                #         df = self.deDup(df, self.partitionColumns[table], self.orderByColumns[table])
            if table == "sparkdserunrepdatesuggestion":
                if 'suggestedChannelId' not in dfCol:
                    df = df.withColumn('suggestedChannelId', lit(None).cast(NullType()))
                    dfCol.append("suggestedChannelId")
            # if sparkPath:
            #     sparkDF = self.utils.data_load_parquet(self.glueContext, sparkPath)
            #     # if self.batch and (self.environment == "qa" or self.environment == "dev"):
            #     #     sparkDF = sparkDF.filter(sparkDF.updatedAt <= testDate)
            #     spCol = sparkDF.columns
            #     if "runId" not in spCol:
            #         sparkDF = sparkDF.withColumn('runId', lit(None).cast(NullType()))
            #         spCol.append("runId")
            #     if "runUID" not in spCol:
            #         sparkDF = sparkDF.withColumn('runUID', lit(None).cast(NullType()))
            #         spCol.append("runUID")
            #     if table == 'dserun':
            #         sparkDF = sparkDF.withColumn('runId', lit(None).cast(NullType()))
            #         logger.info('replace runId with null...')
            #         sparkDF = self.deDup(sparkDF, self.partitionColumns[sparkTable], self.orderByColumns[table])
            #     if set(spCol) != set(dfCol):
            #         common_cols = list(set(spCol).intersection(set(dfCol)))
            #         df = df.select(common_cols)
            #         sparkDF = sparkDF.select(common_cols)
            #     df = df.union(sparkDF.select(df.columns))

            logger.info(["@@@@@@ columns @@@@@@@@@", df.columns])
            # if df.rdd.isEmpty():
            #     logger.info([table, "Table does not exist"])
            #     tables_not_found.append(table)
            # else:

            # filter old data
            if table in TABLES_WITH_OLD_DATA:
                date_col = TABLES_WITH_OLD_DATA[table]
                date_value = datetime.today() - pd.tseries.offsets.DateOffset(years=2)
                date_str = datetime.strftime(date_value, "%Y-%m-%d")
                logger.info("Filter " + table + " from " + date_str)
                df = df.filter(col(date_col) >= lit(date_str))

            if not self.batch:
                if table in ["sent_email_vod__c", "call2_vod__c", "approved_document_vod__c"]:
                    df = df.filter(df.LastModifiedDate > self.lastDate)

                elif table not in ["interactiontype", "repactiontype", "message", "messagetopic", "messagechannel",
                                    "interactionaccount", "interactionproduct", "account_cs",
                                   "productinteractiontype", "actionchannelmap", "channel", "channelcategory", "channeltype", "rep",
                                   "replocation", "reptype", "targetsperiod", "targetinglevel", "accounttype"
                                   "actiongroup", "activitydeliverymode", "suggestiondeliverymode"
                                   ]:
                    df = df.filter(df.updatedAt > self.lastDate)

            # if (self.batch and (self.environment == "qa" or self.environment == "dev" )):
            #     if table == "call2_vod__c":
            #         df = df.filter(df.LastModifiedDate <= testDate)
            #     elif table == "approved_document_vod__c":
            #         df = df.filter(df.Document_Last_Mod_DateTime_vod__c <= testDate)
            #     elif table == "account_cs":
            #         #df = df.filter(df.ATL_Last_Update_Date_Time_vod__c <= testDate) #does not exist for pfizerus
            #         df = df.filter(df.LastActivityDate <= testDate)
            #     elif table == "sent_email_vod__c":
            #         df = df.filter(df.LastModifiedDate <= testDate)
            #     else:
            #         df = df.filter(df.updatedAt <= testDate)
            if len(self.partitionColumns[table]) > 0:
                df = self.deDup(df, self.partitionColumns[table], self.orderByColumns[table])
            #df = df.repartition(40)

            # special processing on sent_email_vod_c
            if table == 'sent_email_vod__c':
                # create view for email_ref with Id and Email_Content
                ref_df = df.select("Id", "Email_Content_vod__c", "LastModifiedDate", "sysmodstampyear", "sysmodstampmonth", "sysmodstampday")
                ref_df.createOrReplaceTempView("emails_ref")
                logger.info("emails_ref view has been created")
                tables_found.append("emails_ref")
                # truncate Email_Content col in Sent_Email table
                df = df.withColumn("Email_Content_vod__c", lit(""))


            if table in DSERUN_TABLES:
                df.createOrReplaceTempView(table[5:])
            else:
                df.createOrReplaceTempView(table)
            logger.info([table, " view has been created"])
            if table in CS_TABLES and self.batch:
                df.cache()
                logger.info(f"Table {table} has been cached with {df.count()} rows...")
            table_columns[table] = df.columns
            logger.info("{} columns saved...".format(table))
            tables_found.append(table)
        return tables_found, tables_not_found, table_columns

    def upsert(self, tablename, result_sql, s3_destination):
        try:
            if tablename == "emails":
                deltaTable = self.DeltaTable.forPath(self.spark, s3_destination + "data/bronze/" + tablename)
                updates = self.spark.sql(result_sql)
                # normalize column names
                updates = self.utils.normalizeColumnNames(updates)
                # broadcast(updates)
                deltaTable.alias("emails").merge(
                    updates.alias("updates"),
                    "emails.interactionId = updates.interactionId and emails.repActionTypeId = updates.repActionTypeId " +
                    "and emails.accountId = updates.accountId and emails.productId = updates.productId") \
                    .whenMatchedUpdateAll() \
                    .whenNotMatchedInsertAll() \
                    .execute()
            elif tablename == "visits":
                deltaTable = self.DeltaTable.forPath(self.spark, s3_destination + "data/bronze/" + tablename)
                updates = self.spark.sql(result_sql)
                # normalize column names
                updates = self.utils.normalizeColumnNames(updates)
                # broadcast(updates)
                deltaTable.alias("visits").merge(
                    updates.alias("updates"),
                    "visits.interactionId = updates.interactionId and visits.repActionTypeId = updates.repActionTypeId " +
                    "and visits.accountId = updates.accountId and visits.productId = updates.productId") \
                    .whenMatchedUpdateAll() \
                    .whenNotMatchedInsertAll() \
                    .execute()
            elif tablename == "suggestions":
                deltaTable = self.DeltaTable.forPath(self.spark, s3_destination + "data/bronze/" + tablename)
                updates = self.spark.sql(result_sql)
                # normalize column names
                updates = self.utils.normalizeColumnNames(updates)
                # broadcast(updates)
                deltaTable.alias("suggestions").merge(
                    updates.alias("updates"),
                    "suggestions.suggestionReferenceId = updates.suggestionReferenceId "+
                    "and suggestions.startDateLocal = updates.startDateLocal and suggestions.runRepDateSuggestionReasonId = updates.runRepDateSuggestionReasonId " +
                     "and (suggestions.runId = updates.runId or suggestions.runUID = updates.runUID) "
                    "and suggestions.runRepDateSuggestionDetailId = updates.runRepDateSuggestionDetailId ") \
                    .whenMatchedUpdateAll() \
                    .whenNotMatchedInsertAll() \
                    .execute()
            elif tablename == "strategy_target":
                if self.target:
                    deltaTable = self.DeltaTable.forPath(self.spark, s3_destination + "data/bronze/" + tablename)
                    updates = self.spark.sql(result_sql)
                    # normalize column names
                    updates = self.utils.normalizeColumnNames(updates)
                    # broadcast(updates)
                    deltaTable.alias(tablename).merge(
                        updates.alias("updates"),
                        self.condition[tablename]) \
                        .whenMatchedUpdateAll() \
                        .whenNotMatchedInsertAll() \
                        .execute()
            else:
                deltaTable = self.DeltaTable.forPath(self.spark, s3_destination + "data/bronze/" + tablename)
                updates = self.spark.sql(result_sql)
                if tablename == 'dse_score':
                    updates = self.deDup(updates, ["runId", "suggestionReferenceId"], ["runRepDateSuggestionUpdatedAt"])
                # normalize column names
                updates = self.utils.normalizeColumnNames(updates)
                # broadcast(updates)
                deltaTable.alias(tablename).merge(
                    updates.alias("updates"),
                    self.condition[tablename]) \
                    .whenMatchedUpdateAll() \
                    .whenNotMatchedInsertAll() \
                    .execute()
        except Exception as e:
            logger.error("Error in upsert: {}".format(e))

    def nullDeTyped(self, df):
        # get dataframe schema
        my_schema = list(df.schema)

        null_cols = []
        # iterate over schema list to filter for NullType columns
        for st in my_schema:
            if str(st.dataType) == 'NullType':
                null_cols.append(st)
        # cast null type columns to string (or whatever you'd like)
        for ncol in null_cols:
            mycolname = str(ncol.name)
            df = df \
                .withColumn(mycolname, df[mycolname].cast('string'))
        return df

    def run (self, source_s3_location, s3_destination,archive_folder, bronze_tables, tables):
        if self.batch:
            self.lastDate = "batch"
        else:
            # self.lastDate = self.spark.read.parquet(s3_destination + "data/silver/final_dataset")\
            #                                 .agg({"All_effectivedate": "max"}).collect()[0][0]
            self.lastDate = self.spark.read.format("delta").load(s3_destination + "data/bronze/suggestions") \
                                                .agg({"updatedAt": "max"}).collect()[0][0]
        logger.info("The data loading has been started from S3 at " + str(self.lastDate))
        tables_found, tables_not_found, table_columns = self.load_tables_from_s3(tables, source_s3_location, archive_folder)
        if "strategytarget" in tables_not_found:
            self.target = False
        logger.info([tables_found, "These are the tables in memory."])
        logger.info([tables_not_found, "These are the tables not found in memory"])

        # validating the sql queries from data loading file and prep data to write them in bronze area.
        # values_database = {}
        for tablename in bronze_tables:
            logger.info("handling table: " + tablename)
            if (self.batch and tablename == "strategy_target" and "strategytarget" in tables_not_found):

                field = [StructField("strategyTargetId", IntegerType(), True),
                         StructField("targetsPeriodId", IntegerType(), True),
                         StructField("targetingLevelId", IntegerType(), True),
                         StructField("facilityId", StringType(), True),
                         StructField("accountGroupId", StringType(), True),
                         StructField("targetAccount", IntegerType(), True),
                         StructField("repTeamIdAssociated", IntegerType(), True),
                         StructField("repIdAssociated", IntegerType(), True),
                         StructField("productId", IntegerType(), True),
                         StructField("messageTopicId", StringType(), True),
                         StructField("messageId", StringType(), True),
                         StructField("interactionTypeId", IntegerType(), True),
                         StructField("targetProductInteractionTypeId", IntegerType(), True),
                         StructField("target", IntegerType(), True),
                         StructField("targetMin", IntegerType(), True),
                         StructField("targetMax", IntegerType(), True),
                         StructField("visitActionOrderMin", IntegerType(), True),
                         StructField("relativeValue", IntegerType(), True),
                         StructField("createdAt", TimestampType(), True),
                         StructField("updatedAt", TimestampType(), True),
                         StructField("startDate", TimestampType(), True),
                         StructField("endDate", TimestampType(), True)]
                schema = StructType(field)
                df = self.spark.createDataFrame(self.spark.sparkContext.emptyRDD(), schema)
                # normalize column names
                df = self.utils.normalizeColumnNames(df)
                logger.info(df.count())
                df.repartition(1).write.format("delta").save(s3_destination + "data/bronze/strategy_target/")
                logger.info("empty strategy target table was created")
            elif not self.batch:
                logger.info("Starting versioning " + tablename)
                values_database, table_maskedName = self.get_column_mapping(self.df_map_pandas, tablename)
                if tablename in table_maskedName:
                    values_database = self.utils.validateColumns(self.df_load_pandas, values_database, table_maskedName, table_columns, tablename)
                result_sql, partition_columns_list = self.preparesql(self.df_load_pandas, values_database, tablename)
                self.upsert(tablename,result_sql, s3_destination)
            else:
                values_database, table_maskedName = self.get_column_mapping(self.df_map_pandas, tablename)
                if tablename in table_maskedName:
                    values_database = self.utils.validateColumns(self.df_load_pandas, values_database, table_maskedName,
                                                             table_columns, tablename)
                result_sql, partition_columns_list = self.preparesql(self.df_load_pandas, values_database, tablename)
                if partition_columns_list[0] !="":
                    df = self.spark.sql(result_sql)
                    if tablename == 'dse_score':
                        df = self.deDup(df, ["runId", "suggestionReferenceId"], ["runRepDateSuggestionUpdatedAt"])
                    # normalize column names
                    df = self.nullDeTyped(df)
                    df = self.utils.normalizeColumnNames(df)
                    self.utils.df_topartition_delta(df, partition_columns_list,
                                           s3_destination + "data/bronze/" + tablename)
                    df.unpersist()
                else:
                    df = self.spark.sql(result_sql)
                    # normalize column names
                    df = self.nullDeTyped(df)
                    df = self.utils.normalizeColumnNames(df)
                    self.utils.df_to_delta(df, s3_destination + "data/bronze/" + tablename)
                    df.unpersist()
            self.spark.catalog.dropGlobalTempView(tablename)
        #     # print(spark.sql(result_sql))
        if "productinteractiontype" in tables_found:
            tables_found.remove('productinteractiontype')
        for table in tables_found:
            self.spark.catalog.dropGlobalTempView(table)
            logger.info(table + " Dropped from memory")
        # self.spark.sparkContext._jvm.System.gc()