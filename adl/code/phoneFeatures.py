from awsglue.transforms import *
from pyspark.sql import SQLContext
from pyspark.sql.types import *
from pyspark.sql import Row, functions as F
from pyspark.sql.window import Window
from pyspark.sql.functions import when, lit, col
import pandas as pd
from pyspark.sql.types import IntegerType
from pyspark.sql.functions import col, expr, when, broadcast, datediff, to_date, last
from utils import Utils
import logging
import pyspark
import sys
from channelFeatures import ChannelFeatures



class PhoneFeatures(ChannelFeatures):
    def __init__(self, glue_context, customer, channel, df, current_month, look_back):
        self.channel = channel
        self.recordclass = ['Visit only with Interaction completed',
                     'Visit suggestion without Interaction',
                     'Visit only with Interaction not_completed',
                     'Visit and suggestion with Interaction completed']
        ChannelFeatures.__init__(self, glue_context, customer, channel, df, self.recordclass, current_month, look_back)

    def getPhoneFeatures(self):
        return super().getChannelFeatures("phone")