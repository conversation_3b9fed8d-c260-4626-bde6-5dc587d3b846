{"customer": "genente<PERSON>", "s3_destination": "s3://aktana-bdp-siqa/adl/archive4hkm/", "s3_load_csv": "s3://aktana-bdp-adlcommon/RB-learning-27.3.0/common/data/loading.csv", "s3_map_csv": "s3://aktana-bdp-adlcommon/RB-learning-27.3.0/common/data/mapping.csv", "s3_dict_csv": "s3://aktana-bdp-adlcommon/RB-learning-27.3.0/common/data/dictionary.csv", "source_s3_location": "s3://aktana-bdp-genentechus/prod/data/archive_rds/", "environment": "dev", "archive_folder": "archive_rds", "strategy_target": "yes", "batch": "true", "external_adl": "true", "tables": ["sent_email_vod__c", "sparkdserun", "sparkdserunrepdate", "sparkdserunrepdatesuggestion", "sparkdserunrepdatesuggestiondetail", "interaction", "interactiontype", "repactiontype", "call2_vod__c", "interactionaccount", "interactionproduct", "messagetopic", "message", "messagechannel", "productinteractiontype", "physicalmessage", "repaccountassignment", "account_dse", "account_cs", "facility", "rep", "replocation", "repteam", "akt_replicense_arc", "approved_document_vod__c", "rpt_suggestion_delivered_stg", "strategytarget", "targetsperiod", "targetinglevel", "repproductauthorization", "product", "channel", "channelcategory", "channeltype", "accounttype", "reptype", "actionchannelmap", "actiongroup", "activitydeliverymode", "suggestiondeliverymode"], "bronze_tables": ["emails", "visits", "suggestions", "dse_score", "rep_account_assignment", "rep_product_authorization", "account", "rep", "rep_team_rep", "approved_document", "strategy_target", "product", "channel", "rep_action_type"]}