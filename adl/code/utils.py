import os.path
import string
import logging
import boto3
import re
from datetime import datetime, timedelta
import pyspark.sql.functions as F

logger = None
class Utils:
    def __init__(self, glue_context, customer):
        self.glueContext = glue_context
        self.customer = customer
        self.spark = self.glueContext.spark_session if hasattr(self.glueContext, 'spark_session') else None


        global logger
        logger = logging.getLogger("ADL_DEV_LOGGER")
        pass

    def df_to_partitions(self,df, partition_columns_list, s3_destination):
        '''
        :param df:
        :param partition_columns_list:
        :param s3_destination:
        :return:
        '''
        try:
            df.repartition(*partition_columns_list).write.mode('overwrite').partitionBy(*partition_columns_list).parquet(
            s3_destination)
        except Exception as e:
            logger.error("Error in df_to_partition: {}".format(e))
            raise
        return True

    def df_to_partitions_append(self,df, partition_columns_list, s3_destination):
        '''

        :param df:
        :param partition_columns_list:
        :param s3_destination:
        :return:
        '''
        try:
            df.repartition(*partition_columns_list).write.mode('append').partitionBy(*partition_columns_list).parquet(
            s3_destination)
        except Exception as e:
            logger.error("Error in df_to_partition_append: {}".format(e))
            raise
        return True

    # storedfwithunpartition
    def df_to_unpartition(self,df, s3_destination):
        '''

        :param df:
        :param s3_destination:
        :return:
        '''
        try:
            df.write.mode('overwrite').parquet(s3_destination)
        except Exception as e:
            logger.error("Error in df_to_unpartition: {}".format(e))
            raise
        return True

    def data_load_parquet(self,glueContext, s3_load_parquet):
        '''

        :param glueContext:
        :param s3_load_parquet:
        :return:
        '''
        # read the dataloading sript and convert it to dynamic data frame. It's a pipe delimited csv file with partitions
        try:
            # dynamic_frame0 = glueContext.create_dynamic_frame_from_options('s3', connection_options={'paths': [s3_load_parquet],
            #                                                                                          'groupFiles': 'inPartition',
            #                                                                                      "partitionKeys": [
            #                                                                                          'InteractionYear']},format="parquet",
            #                                                            transformation_ctx="dynamic_frame0")
            # #convert dynamic dataframe to pandas dataframe for faster operation
            # df = dynamic_frame0.toDF().distinct()

            # add suffix to filter out year=1000 dummy data
            filtered_path = os.path.join(s3_load_parquet, '*year=20*')
            # add basePath option
            df = self.spark.read.option("mergeSchema", "true").option("basePath", s3_load_parquet) \
                    .parquet(filtered_path)
        except Exception as e:
            logger.error("Error in data_load_parquet: {}".format(e))
            raise
        return df

    def data_load_parquet_with_schema(self,glueContext, s3_load_parquet):
        '''

        :param glueContext:
        :param s3_load_parquet:
        :param schema:
        :return:
        '''
        try:
            schema = self.getArchiveTableSchema(s3_load_parquet)
            # add suffix to filter out year=1000 dummy data
            filtered_path = os.path.join(s3_load_parquet, '*year=20*')
            # add basePath option
            df = self.spark.read.schema(schema).option("basePath", s3_load_parquet).parquet(
                filtered_path)
        except Exception as e:
            logger.error("Error in data_load_parquet: {}".format(e))
            raise
        return df

    # load the csv scrip by sending glue context and path to csv file[Pipe delimited, with headers]. returns pandas dataframe
    def data_load_csv(self, glueContext, s3_load_csv):
        '''
        :param glueContext:
        :param s3_load_csv:
        :return:
        '''
        # read the dataloading sript and convert it to dynamic data frame. It's a pipe delimited csv file with partitions
        try:
            dynamic_frame0 = glueContext.create_dynamic_frame_from_options('s3', connection_options={'paths': [s3_load_csv], },
                                                                       format_options={"withHeader": True,
                                                                                       "separator": "|"}, format="csv",
                                                                       transformation_ctx="dynamic_frame0")
        # convert dynamic dataframe to pandas dataframe for faster operation
            df = dynamic_frame0.toDF().toPandas()
        except Exception as e:
            logger.error("Error in data_load_csv: {}".format(e))
            raise
        return df

    def df_topartition_delta(self,df, partition, location):
        '''

        :param df:
        :param partition:
        :param location:
        :return:
        '''
        try:
            df.repartition(10, partition).write.format("delta").partitionBy(partition).save(location)
        except Exception as e:
            logger.error("Error in df_topartition_delta: {}".format(e))
            raise

    def df_topartition_delta_overwrite(self,df, partition, location):
        '''

        :param df:
        :param partition:
        :param location:
        :return:
        '''
        try:
            df.repartition(40, partition).write.mode('overwrite').format("delta").partitionBy(partition).save(location)
        except Exception as e:
            logger.error("Error in df_topartition_delta_overwrite: {}".format(e))
            raise

    def df_to_delta(self, df, location):
        '''

        :param df:
        :param location:
        :return:
        '''
        try:
            df.write.format("delta").mode("overwrite").save(location)
        except Exception as e:
            logger.error("Error in df_to_delta: {}".format(e))
            raise


    def dataLoadDelta(self, s3_table_location):
        '''

        :param s3_table_location:
        :return:
        '''
        df = None
        try:
            df = self.spark.read.format("delta").load(s3_table_location)
        except Exception as e:
            logger.error("Error in dataLoadDelta: {}".format(e))
            raise
        return df

    def sql_to_template(self,sqlcommand, values):
        template = string.Template('% s' % (sqlcommand))
        sqlstr = template.substitute(values)
        sqlstr = sqlstr.replace('\n', ' ');
        sqlstr = sqlstr.strip()
        return sqlstr

    def delete_sugg_recordclass_if_repeated(self, updates, s3_recordclasses_destination, DeltaTable):
        logger.info("start deleting suggestion record class if repeated...")
        deltaTable = DeltaTable.forPath(self.spark, s3_recordclasses_destination)
        deltaTable.alias("df").merge(
            updates.alias("updates"),
            "df.recordclassId % 5 = 0 and df.suggestionReferenceId = updates.suggestionReferenceId and (df.recordclassId = updates.recordclassId+1 or df.recordclassId = updates.recordclassId+2)") \
            .whenMatchedDelete() \
            .execute()

    def upsertRecordClass(self, updates, s3_recordclasses_destination, DeltaTable, yearmonth_col, min_yearmonth):
        yearmonth_filter = f"(recordClasses.{yearmonth_col} >= {min_yearmonth})"
        deltaTable = DeltaTable.forPath(self.spark, s3_recordclasses_destination)
        # upsert interaction record classes
        logger.info("start upserting interaction record class...")
        updates_int = updates.filter("interactionId is not null")
        deltaTable.alias("recordClasses").merge(
            updates_int.alias("updates"),
             yearmonth_filter + " and (recordClasses.recordclassId % 5 != 0 and recordClasses.interactionId = updates.interactionId)") \
            .whenMatchedUpdateAll() \
            .whenNotMatchedInsertAll() \
            .execute()
        # reload delta table
        deltaTable = DeltaTable.forPath(self.spark, s3_recordclasses_destination)
        # upsert suggestion record classes
        logger.info("start upserting suggestion record class...")
        updates_sugg = updates.filter("interactionId is null")
        deltaTable.alias("recordClasses").merge(
            updates_sugg.alias("updates"),
            yearmonth_filter + " and (recordClasses.recordclassId % 5 = 0 and recordClasses.suggestionReferenceId = updates.suggestionReferenceId )") \
            .whenMatchedUpdateAll() \
            .whenNotMatchedInsertAll() \
            .execute()

        # set={"isCompleted": "updates.isCompleted"
        #     , "isDeleted": "updates.isDeleted"
        #     , "updatedAt": "updates.updatedAt"}) \


    ##########################################################################

    def getMaxPartitionData_Interaction(glue_client, glueContext, databasename, sourcetable):
        ### using boto client to read partitions
        response = glue_client.get_partitions(DatabaseName=databasename, TableName=sourcetable)
        partitions = [int(p['Values'][0]) for p in response['Partitions']]
        set_partitions = list(set(partitions))
        set_partitions.sort()
        set_partitions = set_partitions[0]
        logger.info(set_partitions)
        loaddate = str(set_partitions)
        push_down_predicate = "(interactionyear = " + loaddate + ")"
        # source_DF = glueContext.create_dynamic_frame.from_catalog(database = databasename, table_name = sourcetable, transformation_ctx = "main_query", push_down_predicate = push_down_predicate)
        source_DF = glueContext.create_dynamic_frame.from_catalog(database=databasename, table_name=sourcetable,
                                                                  transformation_ctx="main_query")
        responseGetTable = glue_client.get_table(DatabaseName=databasename, Name=sourcetable)
        logger.info('\n after getting responseGetTable')
        responseTable = responseGetTable['Table']
        logger.info('\n after getting responseTable')
        responseStorage = responseTable['StorageDescriptor']
        logger.info('\n after getting responseStorage')
        responseColumns = responseStorage['Columns']
        logger.info('\n after getting responseColumns')
        responseMapping = []
        logger.info('\n building responseMapping')
        return source_DF

    def getMaxPartitionData_Suggestions(glue_client, glueContext, databasename, sourcetable):
        ### using boto client to read partitions
        response = glue_client.get_partitions(DatabaseName=databasename, TableName=sourcetable)
        partitions = [int(p['Values'][0]) for p in response['Partitions']]
        set_partitions = list(set(partitions))
        set_partitions.sort()
        set_partitions = set_partitions[0]
        logger.info(set_partitions)
        loaddate = str(set_partitions)
        push_down_predicate = "(suggestedyear = " + loaddate + ")"
        # source_DF = glueContext.create_dynamic_frame.from_catalog(database = databasename, table_name = sourcetable, transformation_ctx = "main_query", push_down_predicate = push_down_predicate)
        source_DF = glueContext.create_dynamic_frame.from_catalog(database=databasename, table_name=sourcetable,
                                                                  transformation_ctx="main_query")
        responseGetTable = glue_client.get_table(DatabaseName=databasename, Name=sourcetable)
        logger.info('\n after getting responseGetTable')
        responseTable = responseGetTable['Table']
        logger.info('\n after getting responseTable')
        responseStorage = responseTable['StorageDescriptor']
        logger.info('\n after getting responseStorage')
        responseColumns = responseStorage['Columns']
        logger.info('\n after getting responseColumns')
        responseMapping = []
        logger.info('\n building responseMapping')
        return source_DF

    def getMaxPartitionData_target(glue_client, glueContext, databasename, sourcetable):
        ### using boto client to read partitions
        response = glue_client.get_partitions(DatabaseName=databasename, TableName=sourcetable)
        partitions = [int(p['Values'][0]) for p in response['Partitions']]
        set_partitions = list(set(partitions))
        set_partitions.sort()
        set_partitions = set_partitions[0]
        logger.info(set_partitions)
        loaddate = str(set_partitions)
        push_down_predicate = "(start_target_year = " + loaddate + ")"
        # source_DF = glueContext.create_dynamic_frame.from_catalog(database = databasename, table_name = sourcetable, transformation_ctx = "main_query", push_down_predicate = push_down_predicate)
        source_DF = glueContext.create_dynamic_frame.from_catalog(database=databasename, table_name=sourcetable,
                                                                  transformation_ctx="main_query")
        responseGetTable = glue_client.get_table(DatabaseName=databasename, Name=sourcetable)
        logger.info('\n after getting responseGetTable')
        responseTable = responseGetTable['Table']
        logger.info('\n after getting responseTable')
        responseStorage = responseTable['StorageDescriptor']
        logger.info('\n after getting responseStorage')
        responseColumns = responseStorage['Columns']
        logger.info('\n after getting responseColumns')
        responseMapping = []
        logger.info('\n building responseMapping')
        return source_DF

    def getUnPartitionData(glue_client, glueContext, databasename, sourcetable):
        source_DF = glueContext.create_dynamic_frame.from_catalog(database=databasename, table_name=sourcetable,
                                                                  transformation_ctx="main_query")
        responseGetTable = glue_client.get_table(DatabaseName=databasename, Name=sourcetable)
        logger.info('\n after getting responseGetTable')
        responseTable = responseGetTable['Table']
        logger.info('\n after getting responseTable')
        responseStorage = responseTable['StorageDescriptor']
        logger.info('\n after getting responseStorage')
        responseColumns = responseStorage['Columns']
        logger.info('\n after getting responseColumns')
        responseMapping = []
        logger.info('\n building responseMapping')
        return source_DF

    def getArchiveRdsTables(self, source_s3_location):
        # drop 's3://'
        path = source_s3_location[5:]
        path_list = path.split('/')
        # get bucket name and prefix
        bucket_name = path_list[0]
        prefix_name = '/'.join(path_list[1:])
        logger.info([bucket_name,prefix_name])
        # connect s3 bucket
        s3_client = boto3.client('s3')
        objects_list = s3_client.list_objects(Bucket=bucket_name, Prefix=prefix_name, Delimiter='/')
        result = []
        # get all sub-dir in source archive_rds dir
        if "CommonPrefixes" in objects_list:
            for object in objects_list.get('CommonPrefixes'):
                table_full_path = object.get('Prefix')
                table = table_full_path.split('/')[-2]
                result.append(table)

            return set(result)
        else: return set()

    def normalizeColumnNames(self, df):
        cols = sorted(df.columns)
        logger.info("-----------------column name was------------------------")
        logger.info(",".join(cols))
        logger.info("--------------------------------------------------------")
        newCols = set()
        for col in cols:
            newColName = self.renameColumns(col)
            # drop non-standard col if duplicate
            if newColName in newCols:
                df = df.drop(col)
                continue
            newCols.add(newColName)
            df = df.withColumnRenamed(col, newColName)
        logger.info("-----------------now column is--------------------------")
        logger.info(",".join(newCols))
        logger.info("--------------------------------------------------------")
        return df

    def renameColumns(self, col):
        newCol = re.sub('_vod__c', '', col)
        newCol = re.sub('__c', '', newCol)
        newCol = re.sub('_akt', '', newCol)
        newCol = ''.join(
            a[0] + a[1:].lower() if a.isupper() else a[0].upper() + a[1:] for a in re.split('([^a-zA-Z0-9])', newCol) if
            a.isalnum())
        newColName = newCol[0].lower() + newCol[1:]
        return newColName

    def renameFinalDataset(self, df, df_dict_pandas):
        cols = df.columns
        for col in cols:
            matchedRows = df_dict_pandas[(df_dict_pandas['SourceFieldsName'] == col) & df_dict_pandas['Customer'].isin(['All', self.customer])]
            if matchedRows.shape[0] == 1:
                newCol = matchedRows.iloc[0]['ADLStandardFieldsName']
                df = df.withColumnRenamed(col, newCol)
            elif matchedRows.shape[0] == 0:
                logger.info("Column {} not found in Master Data Dictionary...".format(col))
            else:
                logger.info("Column {} has multiple matches in Master Data Dictionary...".format(col))

        return df


    def validateColumns(self, df_load_pandas, values_database, table_maskedName, table_columns, table):
        # get column set
        source_tables = df_load_pandas[df_load_pandas['table_name'] == table]['source_table'].values[0]
        source_tables = source_tables.split(',')

        # if there are source tables
        if len(source_tables) >= 0 and source_tables[0] != '':
            column_set = set().union(*[table_columns[t] for t in source_tables])
            # validate columns
            for mask_name in table_maskedName[table]:
                logger.info("Handling {} in {}...".format(mask_name, table))
                watermark = values_database[mask_name]
                if len(watermark.split()) == 1:
                    # single word watermark --> standard column name, check if exists
                    if watermark not in column_set:
                        logger.warning("{} could not be found in source table! Replace with empty string...".format(mask_name))
                        values_database[mask_name] = "'' as {}".format(watermark)
                    else:
                        values_database[mask_name] = "{} as {}".format(watermark, watermark)

        return values_database

    def countDistinctValuesByGroup(self, df, groupColumns, countkey, newColumnName):
        return df.groupBy(groupColumns).agg(F.countDistinct(countkey).alias(newColumnName))

    def countValuesByGroup(self, df, groupColumns, sumKey, newColumnName):
        return df.groupBy(groupColumns).agg(F.count(sumKey).alias(newColumnName))

    def getPreMonthString(self, current_month):
        current_month_object = datetime.strptime(current_month, "%Y%m")
        time_delta = timedelta(days=1)
        pre_month_object = current_month_object - time_delta
        pre_month = datetime.strftime(pre_month_object, "%Y%m")
        return pre_month

    def getMasterDf(self, df, current_month, look_back):
        account_product_df = df.select('accountId', 'productId').distinct()
        int_year_month_list = [(current_month,)]
        each_month = current_month
        for _ in range(look_back):
            pre_month = self.getPreMonthString(each_month)
            each_month = pre_month
            int_year_month_list.append((each_month,))
        int_year_month_df = self.spark.createDataFrame(int_year_month_list).toDF('interactionYearMonth')

        # join account_product df with possible month df
        master_df = account_product_df.crossJoin(int_year_month_df)
        return master_df

    def getArchiveTableSchema(self, path):
        '''
        This function gets latest schema of archive table
        :param path: s3 path to archive table
        :return: latest schema of archive table
        '''
        # remove s3://
        bucket, prefix = path[5:].split(sep='/', maxsplit=1)
        # add '/' in prefix
        prefix += '/'

        s3_client = boto3.client("s3")
        for _ in range(3):
            objs = s3_client.list_objects_v2(Bucket=bucket,Prefix=prefix, Delimiter='/')
            prefix = sorted([prefix_obj['Prefix'] for prefix_obj in objs["CommonPrefixes"]],
                            key=lambda x: int(x.split('=')[-1].replace('/', '')))[-1]

        return self.spark.read.parquet("s3://" + bucket + '/' + prefix).schema

    def path_exists(self, path):
        # spark is a SparkSession
        sc = self.spark.sparkContext
        fs = sc._jvm.org.apache.hadoop.fs.FileSystem.get(
            sc._jvm.java.net.URI.create(path),
            sc._jsc.hadoopConfiguration(),
        )
        return fs.exists(sc._jvm.org.apache.hadoop.fs.Path(path))