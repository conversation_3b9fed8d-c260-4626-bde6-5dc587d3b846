from awsglue.transforms import *
# from pyspark.sql.functions import *
from pyspark.sql import SQLContext

from pyspark.sql.types import *
from pyspark.sql import Row, functions as F
from pyspark.sql.window import Window
from pyspark.sql.functions import when, lit, col
import pandas as pd
from pyspark.sql.types import IntegerType
from pyspark.sql.functions import col, expr, when, broadcast, datediff, to_date, last
from utils import Utils
import logging
import pyspark
import sys
from channelFeatures import ChannelFeatures



class EmailFeatures(ChannelFeatures):
    def __init__(self, glue_context, customer, channel, df, current_month, look_back):
        self.channel = channel
        self.recordclass = ['Email only with Interaction completed',
                     'EMail suggestion without Interactiond',
                     'Email and suggestion with Interaction completed']
        ChannelFeatures.__init__(self, glue_context, customer, channel, df, self.recordclass, current_month, look_back)

    def getEmailFeatures(self):
        common_df = super().getChannelFeatures("emailSent")

        email_df = self.getOpenClickFeatures()

        final_df = common_df.join(email_df, ['accountId', 'productId', 'interactionYearMonth'], 'full')

        final_df = self.getEmailRatesFeatures(final_df)
        return final_df


    def getOpenClickFeatures(self):
        '''
        This function gets OpenClick features with columns [accountId, productId, interactionYearMonth, emailOpen1MonthCount, emailClick1MonthCount]
        :return:
        '''
        # open features
        openDf = self.allProdDf.where(col('emailOpenCount') > F.lit(0))
        openDfMonthly = super().getMonthlyFeatures(openDf, "emailOpen")
        openDfFinal = openDfMonthly.join(super().getMeanGapFeatures(openDf, 'EmailOpen'),
                                         ['accountId', 'productId', 'interactionYearMonth'], 'full')

        clickDf = self.allProdDf.where(col('emailClickCount') > F.lit(0))
        clickDfMonthly = super().getMonthlyFeatures(clickDf, "emailClick")
        clickDfFinal = clickDfMonthly.join(super().getMeanGapFeatures(clickDf, 'EmailClick'),
                                           ['accountId', 'productId', 'interactionYearMonth'], 'full')

        return openDfFinal.join(clickDfFinal, ['accountId', 'productId', 'interactionYearMonth'], 'full')

    def getEmailRatesFeatures(self, df):

        ##### add back all the possible months
        df = df.join(self.masterDf, ['accountId', 'productId', 'interactionYearMonth'], 'right_outer')

        # fill NA
        df = df.fillna(0)

        # get email open click rate for current month
        df = df.withColumn('emailOpenRate', F.when(df.emailSent1MonthCount > F.lit(0),
                                                   df.emailOpen1MonthCount / df.emailSent1MonthCount).otherwise(
            F.lit(0)))
        df = df.withColumn('emailClickRate', F.when(df.emailSent1MonthCount > F.lit(0),
                                                    df.emailClick1MonthCount / df.emailSent1MonthCount).otherwise(
            F.lit(0)))
        df = df.withColumn('emailOpenClickRate', F.when(df.emailSent1MonthCount > F.lit(0),
                                                        (
                                                                    df.emailOpen1MonthCount + df.emailClick1MonthCount) / df.emailSent1MonthCount).otherwise(
            F.lit(0)))

        # get rolling period features
        w = Window.partitionBy('accountId', 'productId').orderBy(F.desc('interactionYearMonth'))
        drop_columns = []
        for i in [3,6,12]:
            for prefix in ['Sent', 'Open', 'Click']:
                df = df.withColumn('cumSumEmail' + prefix + str(i) + 'MonthCount', F.sum('email'+prefix + '1MonthCount').over(w.rowsBetween(Window.currentRow, Window.currentRow + i-1)))
                drop_columns.append('cumSumEmail' + prefix + str(i) + 'MonthCount')
        for i in [3,6,12]:
            for prefix in ['Open', 'Click']:
                df = df.withColumn('email' + prefix + 'RateOver' + str(i) + 'Months',
                                   F.when(F.col('cumSumEmailSent' + str(i) + 'MonthCount') > F.lit(0),
                                          F.col('cumSumEmail' + prefix + str(i) + 'MonthCount') / F.col(
                                              'cumSumEmailSent' + str(i) + 'MonthCount')).otherwise(
                                       F.lit(0)))
            df = df.withColumn('emailOpenClickRateOver' + str(i) + 'Months',
                               F.when(F.col('cumSumEmailSent' + str(i) + 'MonthCount') > F.lit(0),
                                      (F.col('cumSumEmailOpen' + str(i) + 'MonthCount') + F.col(
                                          'cumSumEmailClick' + str(i) + 'MonthCount')) / F.col(
                                          'cumSumEmailSent' + str(i) + 'MonthCount')).otherwise(
                                   F.lit(0)))

        df = df.drop(*drop_columns)
        return df

        # # combine open click features / add combined features
        # combined_df = open_ints_df.join(click_ints_df, ['accountId', 'productId', 'interactionYearMonth'], 'full')
        # combined_df = combined_df.na.fill(value=0, subset=['numberEmailOpen','numberEmailClick'])
        # combined_df = combined_df.withColumn('numberEmailOpenClick', F.col('numberEmailOpen') + F.col('numberEmailClick'))
        #
        # # cumulative features
        # combined_df = combined_df.withColumn(open_feature_name + 'Total', F.sum(open_feature_name).over(
        #     Window.partitionBy('accountId', 'productId') \
        #     .orderBy('interactionYearMonth').rangeBetween(Window.unboundedPreceding, 0)))
        #
        # combined_df = combined_df.withColumn(click_feature_name + 'Total', F.sum(click_feature_name).over(
        #     Window.partitionBy('accountId', 'productId') \
        #         .orderBy('interactionYearMonth').rangeBetween(Window.unboundedPreceding, 0)))
        #
        # combined_df = combined_df.withColumn('numberEmailOpenClickTotal', F.sum('numberEmailOpenClick').over(
        #     Window.partitionBy('accountId', 'productId') \
        #         .orderBy('interactionYearMonth').rangeBetween(Window.unboundedPreceding, 0)))

