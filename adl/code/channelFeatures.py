from awsglue.transforms import *
# from pyspark.sql.functions import *
from pyspark.sql import SQLContext

from pyspark.sql.types import *
from pyspark.sql import Row, functions as F
from pyspark.sql.window import Window
from pyspark.sql.functions import when, lit, col
import pandas as pd
from pyspark.sql.types import IntegerType
from pyspark.sql.functions import col, expr, when, broadcast, datediff, to_date, last
from utils import Utils
import logging
import pyspark
import sys
import dateutil.relativedelta
from datetime import datetime
import numpy as np

logger = None

class ChannelFeatures:

    def __init__(self, glue_context, customer, channel, df, recordclass, current_month, look_back):
        self.glueContext = glue_context
        self.customer = customer
        self.utils = Utils(glue_context, customer)
        self.spark = glue_context.spark_session
        self.channel = channel
        self.recordclass = recordclass
        self.channel_df = df.where((col('recordclass').isin(self.recordclass)) & (col('interactionIsCompleted')==F.lit(1)) & (col('channel') == channel))
        self.dfAll = self.channel_df.withColumn('productId', F.lit(-1))
        self.channel_df = self.channel_df.withColumn('productId', F.explode(F.split('productIdList', ','))).select('accountId',
                                                                                                      'interactionId',
                                                                                                      'interactionYearMonth',
                                                                                                      'productId',
                                                                                                      'interactionWeekOfYear',
                                                                                                      "interactionDayOfYear",
                                                                                                      "emailOpenCount",
                                                                                                      "emailClickCount",
                                                                                                      "effectiveDate")
        self.allProdDf = self.channel_df.union(self.dfAll.select(self.channel_df.columns))
        self.current_month = current_month
        self.look_back = look_back

        d = datetime.strptime(self.current_month, "%Y%m")
        start_month = d - dateutil.relativedelta.relativedelta(months=look_back)
        self.start_month = start_month.strftime("%Y%m")

        start_month_one_year = d - dateutil.relativedelta.relativedelta(months=11)
        self.start_month_one_year = start_month_one_year.strftime("%Y%m")
        self.allProdDfOneYear = self.allProdDf.filter(
            (self.allProdDf.interactionYearMonth >= self.start_month_one_year) & (
                    self.allProdDf.interactionYearMonth <= self.current_month))

        self.masterDf = self.utils.getMasterDf(self.allProdDfOneYear, self.current_month, self.look_back)

        global logger
        logger = logging.getLogger("HFS_DEV_LOGGER")

    def getChannelFeatures(self, colNamePrefix):
        # monthly features
        monthlyDf = self.getMonthlyFeatures(self.allProdDf, colNamePrefix)

        # monthly_df = self.getMonthlyInts(self.channel_df)
        # d = datetime.strptime(self.start_month, "%Y%m")
        # cumStartDate = d + dateutil.relativedelta.relativedelta(months=3)
        # monthly_df = monthly_df.filter(monthly_df.interactionYearMonth >= cumStartDate.strftime("%Y%m"))
        # # final features
        # final_df = self.getTotalInts(monthly_df)
        gapDf = self.getMeanGapFeatures(self.allProdDf, colNamePrefix[0].upper()+colNamePrefix[1:])

        return monthlyDf.join(gapDf, ['accountId', 'productId','interactionYearMonth'], 'full')

    def getMonthlyFeatures(self, df, colNamePrefix):
        '''
        SI-4586
        This method calculates the monthly counts for any channel & event (interaction, interactionWeek, interactionday)
        :return: accountId, productId, interactionYearMonth, channel1MonthCount, channel1MonthWeekCount, channel1MonthDayCount
        '''

        dfMonthlyInterations = self.utils.countValuesByGroup(df, [
            'accountId', 'productId', 'interactionYearMonth'], "interactionId", colNamePrefix + "1MonthCount")
        dfMonthlyInterationsPerWeek = self.utils.countDistinctValuesByGroup(df, [
            'accountId', 'productId', 'interactionYearMonth'], "interactionWeekOfYear",
                                                                            colNamePrefix + "1MonthWeekCount")
        dfMonthlyInterationsPerDay = self.utils.countDistinctValuesByGroup(df, [
            'accountId', 'productId', 'interactionYearMonth'], "interactionDayOfYear", colNamePrefix + "1MonthDayCount")

        return dfMonthlyInterations.join(dfMonthlyInterationsPerWeek,
                                         ['accountId', 'productId', 'interactionYearMonth'], "full"
                                         ).join(dfMonthlyInterationsPerDay,
                                                ['accountId', 'productId', 'interactionYearMonth'], "full")


    def getMeanGapFeatures(self, df, colNamePrefix):
        '''
        SI-4601
        This method calculate the mean gap features for any channel
        :param df:
        :param colNamePrefix:
        :return:
        '''

        w = Window.partitionBy('accountId','productId').orderBy('effectiveDate')
        df = df.withColumn('lastEffectiveDate', F.lag('effectiveDate', 1).over(w))
        # df = df.withColumn('gap', F.datediff(F.to_date('effectiveDate', 'yyyy-MM-dd'),F.to_date('lastEffectiveDate', 'yyyy-MM-dd')))

        # ML-44 only count business days in gap
        workdaysUDF = F.udf(lambda date1, date2: int(np.busday_count(date1, date2)) if (date1 is not None and date2 is not None) else None, IntegerType())
        df = df.withColumn('gap', workdaysUDF(F.to_date('lastEffectiveDate', 'yyyy-MM-dd'), F.to_date('effectiveDate', 'yyyy-MM-dd')))

        df = df.groupBy('accountId', 'productId', 'interactionYearMonth').agg(F.sum('gap').alias('gapSum1Month'), F.count('effectiveDate').alias('intSum1Month'))
        df = df.withColumn('mean'+ colNamePrefix +'GapsOver1Month', F.col('gapSum1Month')/F.col('intSum1Month'))
        drop_cols = ['gapSum1Month', 'intSum1Month', 'lastEffectiveDate']

        df = df.join(self.masterDf, ['accountId', 'productId', 'interactionYearMonth'], 'right_outer')
        df = df.fillna(0)

        w = Window.partitionBy('accountId','productId').orderBy('interactionYearMonth')
        for i in [3,6]:
            df = df.withColumn('gapSum'+str(i)+'Month', F.sum('gapSum1Month').over(w.rowsBetween(Window.currentRow - i + 1, Window.currentRow)))
            df = df.withColumn('intSum'+str(i)+'Month', F.sum('intSum1Month').over(w.rowsBetween(Window.currentRow - i + 1, Window.currentRow)))
            df = df.withColumn('mean'+colNamePrefix+'GapsOver' + str(i) + 'Month', F.col('gapSum'+str(i)+'Month') / F.col('intSum'+str(i)+'Month'))
            drop_cols.append('gapSum' + str(i) + 'Month')
            drop_cols.append('intSum' + str(i) + 'Month')


        df = df.drop(*drop_cols)
        df = df.fillna(0)

        return df


    # def getTotalInts(self, df):
    #     # get features for cumulative sum
    #     feature_prefix = 'number' + self.channel.capitalize()
    #     current_month_feature_name = feature_prefix + 'Month'
    #     df_total = df.withColumn(feature_prefix + 'Total', F.sum(current_month_feature_name).over(
    #         Window.partitionBy('accountId', 'productId') \
    #         .orderBy('interactionYearMonth').rangeBetween(Window.unboundedPreceding, 0)))
    #     return df_total
    #
    # def getMonthlyInts(self, df):
    #     # df_monthly = df.select('accountId').distinct()
    #
    #     # current month feature
    #     feature_prefix = 'number' + self.channel.capitalize()
    #     current_month_feature_name = feature_prefix + 'Month'
    #     df_total = self.getNumberOfIntsInSpan(df, self.start_month, self.current_month, current_month_feature_name)
    #
    #     # get master df with all accountId-productId VS interactionYearMonth combinations
    #     account_product_df = df_total.select('accountId','productId').distinct()
    #     int_year_month_list = [(self.current_month,)]
    #     tmp_month = self.current_month
    #     for i in range(self.look_back):
    #         pre_month = self.utils.getPreMonthString(tmp_month)
    #         tmp_month = pre_month
    #         int_year_month_list.append((tmp_month,))
    #     int_year_month_df = self.spark.createDataFrame(int_year_month_list).toDF('interactionYearMonth')
    #     master_df = account_product_df.crossJoin(int_year_month_df)
    #     df_total = df_total.join(master_df, ['accountId','productId','interactionYearMonth'], 'right_outer')
    #
    #     # get features for previous months
    #     pre_feature_name = feature_prefix + 'PreviousMonth'
    #     pre_two_feature_name = feature_prefix + 'PreviousTwoMonth'
    #     pre_three_feature_name = feature_prefix + 'PreviousThreeMonth'
    #     df_total = df_total.withColumn(pre_feature_name, F.lag(current_month_feature_name, 1).over(Window.partitionBy('accountId', 'productId')\
    #                                                                                                       .orderBy('interactionYearMonth')))
    #     df_total = df_total.withColumn(pre_two_feature_name, F.lag(current_month_feature_name, 2).over(
    #         Window.partitionBy('accountId', 'productId') \
    #         .orderBy('interactionYearMonth')))
    #     df_total = df_total.withColumn(pre_three_feature_name, F.lag(current_month_feature_name, 3).over(
    #         Window.partitionBy('accountId', 'productId') \
    #         .orderBy('interactionYearMonth')))
    #
    #     return df_total
    #
    # def getNumberOfIntsInSpan(self, df, start_month, end_month, feature_name):
    #     df_span = df.filter(df.interactionYearMonth.between(start_month,end_month))
    #     df_span = df_span.select('accountId','interactionId', 'productIdList','interactionYearMonth')
    #
    #     # get df for all
    #     df_total = self.utils.countDistinctValuesByGroup(df_span.select('accountId','interactionId', 'interactionYearMonth'), [F.col('accountId'),F.col('interactionYearMonth')], 'interactionId', feature_name)
    #     df_total = df_total.withColumn('productId', F.lit(-1))
    #     df_total = df_total.select("accountId","productId",'interactionYearMonth', feature_name)
    #     logger.info("columns for all products:")
    #     logger.info(df_total.columns)
    #
    #     # get df for products
    #     df_split = df_span.withColumn('productId',F.explode(F.split('productIdList',','))).select('accountId', 'interactionId','productId', 'interactionYearMonth')
    #     df_by_product = self.utils.countDistinctValuesByGroup(df_split, [F.col('accountId'), F.col('productId'),F.col('interactionYearMonth')], 'interactionId', feature_name)
    #     logger.info("columns by product:")
    #     logger.info(df_by_product.columns)
    #
    #     return df_total.union(df_by_product)