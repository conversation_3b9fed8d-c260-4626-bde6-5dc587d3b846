<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" autoUpload="Always" serverName="<EMAIL>:22 key" remoteFilesAllowedToDisappearOnAutoupload="false">
    <serverData>
      <paths name="adl">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/scripts/adl" local="$PROJECT_DIR$" web="/" />
            <mapping local="$PROJECT_DIR$/../../../../../glue/PyGlue.zip" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:22 key">
        <serverdata>
          <mappings>
            <mapping deploy="/tmp/pycharm_project_350" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
    </serverData>
    <option name="myAutoUpload" value="ALWAYS" />
  </component>
</project>