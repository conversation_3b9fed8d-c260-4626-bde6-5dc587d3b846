<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="code.adlDriver.rawDataProcess" />
          <option value="code.adlDriver.s3Source" />
          <option value="code.adlDriver.s3Destination" />
          <option value="code.adlDriver.adlTransformations" />
          <option value="code.adlDriver.recordClass" />
          <option value="code.adlDriver.s3Sources" />
          <option value="code.adlDriver.models" />
          <option value="code.adlDriver.model" />
          <option value="code.adlDriver.modeltype" />
          <option value="code.adlDriver.params" />
          <option value="code.adlDriver.utils" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>