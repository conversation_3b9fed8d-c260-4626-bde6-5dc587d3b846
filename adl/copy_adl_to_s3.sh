#!/bin/bash

TARGET_DIR=$1
if [[ -z $TARGET_DIR ]] ; then
    echo "TARGET_DIR not provided, using BRANCH..."
    TARGET_DIR=$BRANCH
fi

ADL_DIR="learning/adl"
TARGET_PATH=s3://aktana-bdp-adlcommon/$TARGET_DIR/common

echo "Deploying ADL from source=$ADL_DIR to target=$TARGET_PATH using code from branch=$BRANCH"

aws s3 cp $ADL_DIR/code/ $TARGET_PATH/code/ --recursive
aws s3 cp $ADL_DIR/data/ $TARGET_PATH/data/ --recursive
aws s3 cp $ADL_DIR/lib/ $TARGET_PATH/lib/ --recursive

