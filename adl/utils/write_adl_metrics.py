import pandas as pd
import pyspark
from os import path, listdir
from pyspark.sql import SparkSession
import sys
import os
import re
import findspark
import datetime
import dateutil.relativedelta

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
print ("ADL dir:",learning_dir)
sys.path.append(learning_dir + "/../common/pyUtils")

import aktana_ml_utils as mlutils


def initalize_spark(adl_params):
    findspark.init()
    os.environ['PYSPARK_SUBMIT_ARGS'] = "--driver-memory 4g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.199," \
                                        "org.apache.hadoop:hadoop-aws:3.3.4,io.delta:delta-core_2.12:2.2.0 " \
                                        "pyspark-shell "

    sc = pyspark.SparkContext()
    sc.setSystemProperty("com.amazonaws.services.s3.enableV4", "true")
    sc.setLogLevel("WARN")
    hadoop_conf = sc._jsc.hadoopConfiguration()
    hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
    hadoop_conf.set("com.amazonaws.services.s3.enableV4", "true")

    access_key = os.getenv('AWS_ACCESS_KEY_ID')
    secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
    session_token = os.getenv('AWS_SESSION_TOKEN')

    #print ("access_key:" + access_key)
    #print ("secret_key:" + secret_key)
    #print ("session_token:" + session_token)
    if (access_key is None or access_key == ""):
        # Env vars not set, using access from CustomerADLConfig
        if (adl_params['adl-awsAccessKey'] != ""):
            print ("Env vars not set, using access from CustomerADLConfig")
            hadoop_conf.set("fs.s3a.access.key", adl_params['adl-awsAccessKey'])
            hadoop_conf.set("fs.s3a.secret.key", adl_params['adl-awsSecretKey'])
        else:
            print ("Env vars not set, and ADL metadata does not have access-key. Will rely on role for the job")
    else:
        print ("Env vars are set, using access/secret-key from ENVIRONMENT")
        hadoop_conf.set("fs.s3a.access.key", access_key)
        hadoop_conf.set("fs.s3a.secret.key", secret_key)
        if (session_token is not None and session_token != ""):
            print ("Session token present in ENVIRONMENT")
            hadoop_conf.set("fs.s3a.aws.credentials.provider", "org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider")
            hadoop_conf.set("fs.s3a.session.token", session_token)

    hadoop_conf.set("fs.s3a.connection.maximum", "100000")
    # hadoop_conf.set("fs.s3a.endpoint", "s3." + constants.aws_region + ".amazonaws.com")
    hadoop_conf.set("fs.s3a.endpoint", "s3.amazonaws.com")
    hadoop_conf.set("delta.logRetentionDuration", "36500")
    hadoop_conf.set("delta.deletedFileRetentionDuration", "365")

    spark = SparkSession(sc) \
        .builder \
        .appName("adl_metrics") \
        .config("spark.sql.autoBroadcastJoinThreshold", -1) \
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
        .config("spark.driver.memory", "8g") \
        .config('spark.executor.cores', '4') \
        .config('spark.executor.memory', '8g') \
        .config("spark.python.worker.memory", '4g') \
        .config("spark.sql.debug.maxToStringFields", 1000) \
        .getOrCreate()


    return spark



def read_adl_from_s3(adl_params, spark):
    adl_path = adl_params["adl-adlS3Location"] + 'data/silver/final_dataset/'
    adl_path = re.sub("s3:", "s3a:", adl_path)
    print(f"Reading ADL from {adl_path}...")
    adl = spark.read.format("delta").load(adl_path)
    adl.createOrReplaceTempView("adl")
    return


def write_metrics_to_s3(adl_params, spark):
    # get target location by adding metrics/ suffix to adlS3Location
    target_path = adl_params["adl-adlS3Location"] + 'metrics/'

    # sql template for channel, product and recordclass
    sql = """select {GROUP_LEVEL}, count(1), count(DISTINCT accountId), count(DISTINCT repId), count(DISTINCT interactionId),
     count(DISTINCT suggestionReferenceId), count(distinct interactionYearMonth), min(interactionYearMonth), 
     max(interactionYearMonth) 
     from adl group by {GROUP_LEVEL}
     order by {GROUP_LEVEL}"""

    # get metrics by channel, product and recordclass, and write to adl/metrics/ folder
    for group_level in ['channel', 'productIdList', 'recordclassId']:
        sql_by_group = sql.format(GROUP_LEVEL=group_level)
        df = spark.sql(sql_by_group).toPandas()
        path = target_path + 'metrics_by_' + group_level + '.csv'
        df.to_csv(path, index=False)
        #print(df)
        print(f"File written in {path}")

    # get start and end month for general metrics, data range [current_month - 5, current_month]
    today = datetime.datetime.today()
    end_month_str = datetime.datetime.strftime(today, "%Y%m")
    start_month = today + dateutil.relativedelta.relativedelta(months=-5)
    start_month_str = datetime.datetime.strftime(start_month, "%Y%m")

    # sql for general metrics
    sql_general = f'''SELECT channel, reportedChannelName, productIdList, interactionYearMonth, recordclass, 
    count(1), count(distinct accountId), count(distinct interactionId), count(distinct suggestionReferenceId) 
    from adl where interactionYearMonth <= '{end_month_str}' and interactionYearMonth >= '{start_month_str}' 
    group by channel, reportedChannelName, productIdList, interactionYearMonth, recordclass 
    order by channel, interactionYearMonth'''

    df_general = spark.sql(sql_general).toPandas()
    path = target_path + 'metrics_general.csv'
    df_general.to_csv(path, index=False)
    print(f"Writing general stats from {start_month_str} to {end_month_str} to {path}")
    #print(df_general)
    print(f"File written in {path}")
    return

def main():

    # get cmd params and metadata params
    ml_utils = mlutils.aktana_ml_utils()
    cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:",
                                                          ["customer=", "env=", "app=", "region=", "tunnelport=",
                                                           "view=", "db=", "filename=", "localfile="])
    #print(metadata_params)
    print(cmdline_params)
    print(f"Reading metadata from {metadata_params['host']}")

    # get adl metadata
    adl_params = ml_utils.get_adl_metadata()
    print(f"Reading ADL from {adl_params['adl-adlS3Location']}")
    #print(adl_params)

    # initialize spark
    spark = initalize_spark(adl_params)
    from delta.tables import DeltaTable
    print("Spark initialization finished")

    # read final dataset from s3 and create temp view
    read_adl_from_s3(adl_params, spark)

    # set pandas options to display all rows & columns
    pd.set_option('display.max_rows', None)
    pd.set_option('display.max_columns', None)

    # write metrics csv files to metrics/ folder
    write_metrics_to_s3(adl_params, spark)
    print("Successfully writing metrics files")


if __name__ == "__main__":
    main()
