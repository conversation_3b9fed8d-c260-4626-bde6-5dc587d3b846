import pyspark
from pyspark.sql import SparkSession
import sys
import os
import findspark
import boto3
from pyspark.sql.types import IntegerType, StringType, DoubleType, FloatType, LongType, BooleanType, TimestampType, DecimalType
import pandas as pd
import pyspark.sql.functions as F

TYPE_MAPPING = {
    "double": DoubleType(),
    "integer": IntegerType(),
    "float": FloatType(),
    "string": StringType(),
    "long": LongType(),
    "bool": BooleanType(),
    "timestamp": TimestampType()
}

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
print ("Learning dir:",learning_dir)
sys.path.append(learning_dir + "/common/pyUtils")

import aktana_ml_utils as mlutils


def initalize_spark(adl_params):
    findspark.init()
    os.environ['PYSPARK_SUBMIT_ARGS'] = "--driver-memory 12g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.199," \
                                        "org.apache.hadoop:hadoop-aws:3.0.1,io.delta:delta-core_2.12:0.8.0 " \
                                        "pyspark-shell "

    sc = pyspark.SparkContext()
    sc.setSystemProperty("com.amazonaws.services.s3.enableV4", "true")
    sc.setLogLevel("WARN")
    hadoop_conf = sc._jsc.hadoopConfiguration()
    hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
    hadoop_conf.set("com.amazonaws.services.s3.enableV4", "true")
    hadoop_conf.set("fs.s3a.access.key", adl_params['adl-awsAccessKey'])
    hadoop_conf.set("fs.s3a.secret.key", adl_params['adl-awsSecretKey'])
    hadoop_conf.set("fs.s3a.connection.maximum", "100000")
    # hadoop_conf.set("fs.s3a.endpoint", "s3." + constants.aws_region + ".amazonaws.com")
    hadoop_conf.set("fs.s3a.endpoint", "s3.amazonaws.com")
    hadoop_conf.set("delta.logRetentionDuration", "36500")
    hadoop_conf.set("delta.deletedFileRetentionDuration", "365")

    spark = SparkSession(sc) \
        .builder \
        .appName("adl_metrics") \
        .config("spark.sql.autoBroadcastJoinThreshold", -1) \
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
        .config("spark.driver.memory", "12g") \
        .config("spark.driver.maxResultSize", "4g") \
        .config('spark.executor.cores', '4') \
        .config('spark.executor.memory', '12g') \
        .config("spark.python.worker.memory", '12g') \
        .config("spark.yarn.executor.memoryOverhead", '1200') \
        .config("spark.sql.debug.maxToStringFields", 1000) \
        .getOrCreate()

    return spark


def rewrite_tables(spark, adl_params, table_path, csv_path):
    csv_pdf = pd.read_csv(csv_path, delimiter='|')
    region = adl_params['adl-awsRegion']
    access_key = adl_params['adl-awsAccessKey']
    secret_key = adl_params['adl-awsSecretKey']
    s3_client = boto3.client('s3', region_name=region, aws_access_key_id=access_key, aws_secret_access_key=secret_key)

    column_types = []
    for _, row in csv_pdf.iterrows():
        column_types.append([row['column'], row['new_type']])

    # drop 's3://'
    table_path = table_path[5:]
    path_list = table_path.split('/')
    # get bucket name and prefix
    bucket = path_list[0]
    table_prefix = '/'.join(path_list[1:])

    process_table(bucket, table_prefix, column_types, s3_client, spark)


def process_table(bucket, table_prefix, column_types, s3_client, spark):
    print(f"Processing table {table_prefix}...")
    df_all = None
    year_key, month_key, day_key = '', '', ''
    # loop through each year
    for year_prefix in s3_client.list_objects_v2(Bucket=bucket, Prefix=table_prefix, Delimiter='/').get('CommonPrefixes'):
        year_prefix = year_prefix['Prefix']
        # 'adl/sysmodyear=2021/' -> ['sysmodyear', '2021']
        year_key, year_value = year_prefix[:-1].split('/')[-1].split('=')
        # loop through each month
        for month_prefix in s3_client.list_objects_v2(Bucket=bucket, Prefix=year_prefix, Delimiter='/').get('CommonPrefixes'):
            month_prefix = month_prefix['Prefix']
            month_key, month_value = month_prefix[:-1].split('/')[-1].split('=')
            # loop through each day
            df_all = None
            for day_prefix in s3_client.list_objects_v2(Bucket=bucket, Prefix=month_prefix, Delimiter='/').get('CommonPrefixes'):
                day_prefix = day_prefix['Prefix']
                day_key, day_value = day_prefix[:-1].split('/')[-1].split('=')
                # loop through each parquet
                for parquet_path in s3_client.list_objects_v2(Bucket=bucket, Prefix=day_prefix).get('Contents'):
                    parquet_path = parquet_path['Key']
                    if parquet_path.endswith('.parquet'):
                        parquet_full_path = 's3a://' + bucket + '/' + parquet_path
                        df = spark.read.parquet(parquet_full_path)
                        # cast column types
                        for column, new_type in column_types:
                            if "decimal" not in new_type:
                                df = df.withColumn(column, F.col(column).cast(TYPE_MAPPING[new_type]))
                            else:
                                # get precision and scale from type, e.g. decimal(11,4) -> 11, 4
                                precision, scale = map(int, new_type[8:-1].split(","))
                                df = df.withColumn(column, F.col(column).cast(DecimalType(precision, scale)))
                        # add partition columns
                        df = df.withColumn(year_key, F.lit(int(year_value)))
                        df = df.withColumn(month_key, F.lit(int(month_value)))
                        df = df.withColumn(day_key, F.lit(int(day_value)))
                        if not df_all:
                            df_all = df
                        else:
                            new_cols_1 = list(set(df.columns) - set(df_all.columns))
                            new_cols_2 = list(set(df_all.columns) - set(df.columns))
                            for c in new_cols_1:
                                df_all = df_all.withColumn(c, F.lit(None))
                            for c in new_cols_2:
                                df = df.withColumn(c, F.lit(None))
                            df_all = df_all.unionByName(df)

            if df_all:
                table_full_path = 's3a://' + bucket + '/' + table_prefix[:-1] + '_new/'
                print(f"Rewrite parquets in {table_full_path}...{year_value}-{month_value}")
                df_all.write.mode("append").partitionBy(year_key, month_key, day_key).parquet(table_full_path)


def main():

    # get cmd params and metadata params
    ml_utils = mlutils.aktana_ml_utils()
    cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:",
                                                          ["customer=", "env=", "app=", "region=", "tunnelport=",
                                                           "table-path=", "csv-path="])
    print(metadata_params)
    print(cmdline_params)

    # get adl metadata
    adl_params = ml_utils.get_adl_metadata()
    print(adl_params)

    # initialize spark
    spark = initalize_spark(adl_params)
    print("Spark initialization finished")

    # read csv file
    table_path = cmdline_params.get('table-path', None)
    csv_path = cmdline_params.get('csv-path', None)
    if not table_path or not csv_path:
        print("table path and csv path are mandatory")
        exit(1)
    rewrite_tables(spark, adl_params, table_path, csv_path)
    print("Finish overwriting...")


if __name__ == "__main__":
    main()
