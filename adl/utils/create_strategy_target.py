import pandas as pd
import json

result="""
{
  "strategyTargetId": -99999,
  "targetsPeriodId": -99999,
  "targetingLevelId": -99999,
  "facilityId": -99999,
  "accountGroupId": -99999,
  "accountId": -99999,
  "repTeamId": -99999,
  "repId": -99999,
  "repTypeId": -99999,
  "productId": -99999,
  "messageTopicId": -99999,
  "messageId": -99999,
  "interactionTypeId": -99999,
  "productInteractionTypeId": -99999,
  "target": -99999,
  "targetMin": -99999,
  "targetMax": -99999,
  "visitActionOrderMin": -99999,
  "visitActionOrderMax": -99999,
  "relativeValue": -99999.9999,
  "createdAt": 4.5365284490539983778509312e+25,
  "updatedAt": 4.5365284490539983778509312e+25,
  "is_dummy_record": 1
}
"""

parsed = json.loads(result)
final_df = pd.DataFrame.from_dict([parsed], orient="columns")
final_df['updatedAt'] = pd.to_datetime('01/01/2023', format='%d/%m/%Y')
final_df['createdAt'] = pd.to_datetime('01/01/2023', format='%d/%m/%Y')
final_df.to_parquet('sample_strategy_target.parquet', compression='gzip') 

# This script creates a single row record for approved_document table with timestamp from 2021 (to avoid the following spark error when using year = 1000 with Spark 3):
#   Caused by: org.apache.spark.SparkException: Job aborted due to stage failure: Task 0 in stage 329.0 failed 4 times, most recent failure: Lost task 0.3 in stage 329.0 (TID 5862) (************** executor 12): org.apache.spark.SparkUpgradeException: 
#   You may get a different result due to the upgrading of Spark 3.0: reading dates before 1582-10-15 or timestamps before 1900-01-01T00:00:00Z from Parquet INT96 files can be ambiguous, as the files may be written by Spark 2.x or legacy versions of Hive, which uses a legacy hybrid calendar that is different from Spark 3.0+'s Proleptic Gregorian calendar. 
#   See more details in SPARK-31404. 
#   You can set spark.sql.legacy.parquet.int96RebaseModeInRead to 'LEGACY' to rebase the datetime values w.r.t. the calendar difference during reading. Or set spark.sql.legacy.parquet.int96RebaseModeInRead to 'CORRECTED' to read the datetime values as it is.

# Copy this file as the single dummy row for approved-document to the archive_rds folders to run ADL.
#  aws s3 cp sample_strategy_target.parquet s3://aktana-bdp-genentechca/prod/data/archive_rds/strategytarget/updatedatyear=2023/updatedatmonth=1/updatedatday=1/part-00000-7ffb4bc9-980c-4e42-9929-584ea15cccbf-c000.gz.parquet
#  aws s3 cp sample_approved_doc.parquet s3://aktana-bdp-biogeneu2/prod/data/archive_rds/approved_document_vod__c/sysmodstampyear=2023/sysmodstampmonth=1/sysmodstampday=1/part-00000-7ffb4bc9-980c-4e42-9929-584ea15cccbf-c000.gz.parquet

#aws s3 mv s3://aktana-bdp-genentechca/prod/data/archive_rds/strategytarget/updatedatyear=1900/ s3://aktana-bdp-genentechca/prod/data/archive_rds/strategytarget_1900_record/updatedatyear=1900/ --recursive

