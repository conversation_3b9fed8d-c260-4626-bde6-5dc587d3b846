
#
# This utility will print the list of columns whose datatype are different from 2 parquet schema definitions
#
# python3 ~/repos/learning/adl/utils/parquet_col_diff.py \
#  --file1-path s3://aktana-bdp-pfizerde/prod/data/archive_rds/account_cs/sysmodstampyear=2023/sysmodstampmonth=4/sysmodstampday=4/part-00000-8895e1c8-6c1b-4ec6-a8dd-ec2a6037f9a1-c000.gz.parquet \
#  --file2-path s3://aktana-bdp-pfizerde/prod/data/archive_rds/account_cs/sysmodstampyear=2021/sysmodstampmonth=5/sysmodstampday=13/part-00004-9a82ffa4-1957-484f-9ede-0b9278cd7e70.c000.gz.parquet \
#  --sparktype-path pfizerde_account_cs.csv

# python3 parquet_col_diff_v2.py --file1-path s3://aktana-bdp-pfizereu/prod/data/archive_rds/account_cs/sysmodstampyear=2023/sysmodstampmonth=2/sysmodstampday=2/ --file2-path s3://aktana-bdp-pfizereu/prod/data/archive_rds/account_cs/sysmodstampyear=2023/sysmodstampmonth=3/sysmodstampday=19/ --sparktype-path s3://aktana-bdp-pfizereu/prod/adl/veevatypes/account_cs_changes.csv --local true

# New way to find the schema differences of parquet files
#   1. Invoke the column diff generator with the path to a recent date (with new schema) and an older date (with old schema) using command such as:
#      Note: file1-path is the file with current datatypes and file2-path is the file with old datatypes
#       python3 parquet_col_diff_v2.py --file1-path s3://aktana-bdp-novartisau/prod/data/archive_rds/sent_email_vod__c/sysmodstampyear=2023/sysmodstampmonth=1/sysmodstampday=1/ --file2-path s3://aktana-bdp-novartisau/prod/data/archive_rds/sent_email_vod__c/sysmodstampyear=2023/sysmodstampmonth=3/sysmodstampday=19/ --sparktype-path output.csv --local true
#   2. Copy the generated csv file to s3 using:
#        aws s3 cp account_cs_changes.csv s3://aktana-bdp-pfizereu/prod/adl/veevatypes/account_cs_changes.csv
#   3. Run overwrite_archive_rds utility using the DCO rundeck job using:
#       Options: 
#           SelectedModule: dco 
#           ENGINE_CMDLINE_PARAMS: --table-path s3://aktana-bdp-pfizerde/prod/data/archive_rds/account_cs/ --csv-path s3://aktana-bdp-pfizereu/prod/adl/veevatypes/account_cs_changes.csv 
#           GIT_BRANCH_NAME_OVD: develop 
#           SelectedDriver: 2.Optimizer 
#           clusterseq: 6 
#           DriverOverride: spark_utils/overwrite_archive_rds

# Old way to find the schema of parquet files
#    When ADL fails with "Failed to merge schema" error, do the following to find the col-type differences between the new and old files and then overwrite the old files to the new schema using:
#    1. Download a recent parquet file (with new schema) and an older parquet file (with old schema) from archive_rds using commands such as:
#        aws s3 ls s3://aktana-bdp-novartisau/prod/data/archive_rds/sent_email_vod__c/sysmodstampyear=2023/sysmodstampmonth=1/sysmodstampday=1/
#        aws s3 cp s3://aktana-bdp-novartisau/prod/data/archive_rds/sent_email_vod__c/sysmodstampyear=2023/sysmodstampmonth=1/sysmodstampday=1/part-00000-0b1b74c1-5710-4a4d-a9f8-d5c7a597c27a-c000.gz.parquet old.parquet
#        aws s3 ls s3://aktana-bdp-novartisau/prod/data/archive_rds/sent_email_vod__c/sysmodstampyear=2023/sysmodstampmonth=3/sysmodstampday=19/
#        aws s3 cp s3://aktana-bdp-novartisau/prod/data/archive_rds/sent_email_vod__c/sysmodstampyear=2023/sysmodstampmonth=3/sysmodstampday=19/part-00000-040c950d-ef08-40b1-8544-4f2636b44a50-c000.gz.parquet new.parquet
#    2. Find the schema of the 2 parquet files
#        parquet-tools schema old.parquet > old.schema.txt
#        parquet-tools schema new.parquet > new.schema.txt
#    3. Invoke this utility to find the datatype differences between the 2 schema files using:
#        python3 parquet_col_diff.py new.schema.txt old.schema.txt > account_cs_changes.csv
#    4. Copy the generated csv file to s3 using:
#        aws s3 cp account_cs_changes.csv s3://aktana-bdp-pfizereu/prod/adl/veevatypes/account_cs_changes.csv
#    5. Run overwrite_archive_rds utility using the DCO rundeck job using:
#       Options: 
#           SelectedModule: dco 
#           ENGINE_CMDLINE_PARAMS: --table-path s3://aktana-bdp-pfizerde/prod/data/archive_rds/account_cs/ --csv-path s3://aktana-bdp-pfizereu/prod/adl/veevatypes/account_cs_changes.csv 
#           GIT_BRANCH_NAME_OVD: develop 
#           SelectedDriver: 2.Optimizer 
#           clusterseq: 6 
#           DriverOverride: spark_utils/overwrite_archive_rds


import pyspark
from pyspark.sql import SparkSession
import sys
import os
import findspark
import boto3
from pyspark.sql.types import IntegerType, StringType, DoubleType, FloatType, LongType, BooleanType, TimestampType, DecimalType, StructType, StructField
import pandas as pd
import pyspark.sql.functions as F
import getopt
import json
from os import path, listdir
import requests
from pyspark.sql import SQLContext

class ArchiveDiffFinder():

    def __init__(self):
        """

        """

        # 0. Read and set arguments to the program
        """
        Arguments to the engine from Rundeck
        [scenario_uid, run_date, credentials]
        """
        self.cmdline_params = {}
        self.params = {}
        self.__read_cmd_line_parameters()
        self.spark = None

    def get_aws_region(self):
        r = requests.get("http://169.254.169.254/latest/meta-data/placement/region")
        return r.text

    def get_cmdline_args(self, argv):

        """
        PURPOSE:
            Read the command-line arguments and store them in a dictionary.
            Command-line arguments should come in pairs, e.g.:
                "--customer abc"
        INPUTS:
            The command line arguments (sys.argv).
        RETURNS:
            Returns the dictionary.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """

        if self.cmdline_params:
            return self.cmdline_params

        self.cmdline_params = {}

        shortopts = "hc:e:s:r:f:"
        longopts = ["customer=","env=","param1=","param2=","param3=", "file1-path=", "file2-path=", "sparktype-path=", "local="]

        try:
            opts, args = getopt.getopt(argv[1:],shortopts,longopts)
        except getopt.GetoptError:
            print("Supported args are:" + str(longopts), file=sys.stderr)
            sys.exit(2)

        for opt, arg in opts:
            if opt == '-h':
                print("Usage: --customer, --env, --file1-path, file2-path, --sparktype-path are required.  --param1, --param2 or --param3 are optional.")
                sys.exit()
            elif opt in ("-c", "--customer"):
                self.cmdline_params["customer"] = arg
            elif opt in ("--use-debug"):
                self.cmdline_params["use-debug"] = arg
            elif opt in ("-e", "--env"):
                self.cmdline_params["env"] = arg
            elif opt in ("-f", "--conn-param-file"):
                self.cmdline_params["conn-param-file"] = arg
            else:
                self.cmdline_params[opt[2:]] = arg

        print ("******Cmd line params:" + str(self.cmdline_params))
        return self.cmdline_params

    def __read_cmd_line_parameters(self):
        """

        @return:
        """
        self.get_cmdline_args(sys.argv)

    def get_caller_identity(self):

        client = boto3.client("sts")
        response = client.get_caller_identity()
        print("Current caller identity:")
        print(response)

    def get_role_credentials(self):

        client = boto3.client("sts")
        response = client.get_caller_identity()
        print("Current caller identity:")
        print(response)

        #response = client.assume_role(RoleArn=response['Arn'], RoleSessionName="Archive-overwrite")
        #response = client.assume_role(RoleArn="arn:aws:iam::332437952577:role/emr-ec2-useks-us-east-1", RoleSessionName="Archive-overwrite")
        response = client.get_session_token()

        print("Credentials for role:")
        print(response)
        credentials = response["Credentials"]
        return credentials

    def initalize_spark(self):
        findspark.init()
        os.environ['PYSPARK_SUBMIT_ARGS'] = "--driver-memory 4g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.199," \
                                            "org.apache.hadoop:hadoop-aws:3.0.1,io.delta:delta-core_2.12:0.8.0 " \
                                            "pyspark-shell "

        access_key = os.environ['AWS_ACCESS_KEY_ID']
        secret_key = os.environ['AWS_SECRET_ACCESS_KEY']

        self.get_caller_identity()

        sc = pyspark.SparkContext()
        sc.setSystemProperty("com.amazonaws.services.s3.enableV4", "true")
        sc.setLogLevel("WARN")
        hadoop_conf = sc._jsc.hadoopConfiguration()
        hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
        hadoop_conf.set("com.amazonaws.services.s3.enableV4", "true")

        if self.cmdline_params.get("local", "false") == "true":
            print(f"Running in local-mode. Using aws-access and aws-secret keys")
            hadoop_conf.set("fs.s3a.access.key", access_key)
            hadoop_conf.set("fs.s3a.secret.key", secret_key)
        if self.cmdline_params.get("local", "false") == "option1":
            credentials = self.get_role_credentials()
            print(f"Running in server-mode. Using aws-access and aws-secret keys from assumed role")
            hadoop_conf.set("fs.s3a.aws.credentials.provider", "org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider")
            hadoop_conf.set("fs.s3a.access.key", credentials["AccessKeyId"])
            hadoop_conf.set("fs.s3a.secret.key", credentials["SecretAccessKey"])
            hadoop_conf.set("fs.s3a.session.token", credentials["SessionToken"])
        if self.cmdline_params.get("local", "false") == "option2":
            print(f"Running in server-mode. Enabling server side encryption with key")
            #https://docs.cloudera.com/HDPDocuments/HDP2/HDP-2.6.3/bk_cloud-data-access/content/SSE-KMS.html
            hadoop_conf.set("fs.s3a.enableServerSideEncryption", "true")
            hadoop_conf.set("fs.s3a.server-side-encryption-algorithm", "SSE-KMS")
            hadoop_conf.set("fs.s3a.server-side-encryption.key", "arn:aws:kms:us-east-1:518905070720:key/8d49fe5f-8763-413e-b405-c96cc4d5d9e5")
        if self.cmdline_params.get("local", "false") == "option3":
            print(f"Running in server-mode. Enabling server side encryption without key")
            #https://docs.cloudera.com/HDPDocuments/HDP2/HDP-2.6.3/bk_cloud-data-access/content/SSE-KMS.html
            hadoop_conf.set("fs.s3a.enableServerSideEncryption", "true")
            hadoop_conf.set("fs.s3a.server-side-encryption-algorithm", "SSE-KMS")
        else:
            print(f"Running in server-mode. Skipping aws-access and aws-secret keys")

        hadoop_conf.set("fs.s3a.enableServerSideEncryption", "true")
        hadoop_conf.set("fs.s3a.connection.maximum", "100000")
        # hadoop_conf.set("fs.s3a.endpoint", "s3." + constants.aws_region + ".amazonaws.com")
        hadoop_conf.set("fs.s3a.endpoint", "s3.amazonaws.com")
        hadoop_conf.set("delta.logRetentionDuration", "36500")
        hadoop_conf.set("delta.deletedFileRetentionDuration", "365")

        self.spark = SparkSession(sc) \
            .builder \
            .appName("adl_metrics") \
            .config("spark.sql.autoBroadcastJoinThreshold", -1) \
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
            .config("spark.driver.memory", "4g") \
            .config("spark.driver.maxResultSize", "4g") \
            .config('spark.executor.cores', '1') \
            .config('spark.executor.memory', '4g') \
            .config("spark.python.worker.memory", '4g') \
            .config("spark.yarn.executor.memoryOverhead", '1200') \
            .config("spark.sql.debug.maxToStringFields", 1000) \
            .getOrCreate()

        sqlContext = SQLContext(sparkContext=sc, sparkSession=self.spark)
        sqlContext.setConf("spark.sql.parquet.compression.codec","gzip")


    def init_s3(self):
        region = self.cmdline_params.get("param3", "")
        #if (region == ""):
        #    region = self.get_aws_region()
        print("Using aws-region:" + region)

        if self.cmdline_params.get("local", "false") == "true":
            print(f"Running in local-mode. Using aws-access and aws-secret keys")
            access_key = os.environ['AWS_ACCESS_KEY_ID']
            secret_key = os.environ['AWS_SECRET_ACCESS_KEY']
            #s3_client = boto3.client('s3', region_name=region, aws_access_key_id=access_key, aws_secret_access_key=secret_key)
            s3_client = boto3.client('s3', aws_access_key_id=access_key, aws_secret_access_key=secret_key)
        else:
            print(f"Running in server-mode. Skipping aws-access and aws-secret keys")
            #s3_client = boto3.client('s3', region_name=region)
            s3_client = boto3.client('s3')

        return s3_client

    def read_file(self, s3_client, table_path):

        # drop 's3://'
        table_path = table_path[5:]
        path_list = table_path.split('/')
        # get bucket name and prefix
        bucket = path_list[0]
        table_prefix = '/'.join(path_list[1:])
        print(f"Reading from bucket={bucket} and prefix={table_prefix}")
        for parquet_path_obj in s3_client.list_objects_v2(Bucket=bucket, Prefix=table_prefix).get('Contents'):
            parquet_path = parquet_path_obj['Key']
            print(f"      Reading parquet file ...{parquet_path}")
            if parquet_path.endswith('.parquet'):
                parquet_full_path = 's3a://' + bucket + '/' + parquet_path
                df = self.spark.read.parquet(parquet_full_path)
                break
        return df

    def read_schema(self, df):
        cols = {}
        for field in df.schema.fields:
            #print(field.name +" , "+str(field.dataType))
            cols[field.name] = field.dataType
        
        return cols

    def build_diff(self, collist1, collist2, csv_path):

        output_schema=StructType([ StructField("col",StringType(),True), StructField("new_type",StringType(),True) ])
        diffs = {}

        print(f"Unmatched columns are:")
        print("column|new_type|old_type")
        for key in collist1.keys():
            type1 = collist1[key]
            if key in collist2:
                type2 = collist2[key]
                if (type1 != type2):
                    #print(f"Column type mismatch for {key}, type1:{type1}, type2:{type2}")
                    diffs[key] = str(type1)
                    print(f"{key}|{type1}|{type2}")
        collist = [(k, v) for k, v in diffs.items()]
        
        print(f"Writing differences to {csv_path}")
        df = pd.DataFrame(collist, columns =['column', 'new_type'])
        df.to_csv(csv_path, header=True, index=False, sep='|')
        
        #Use spark to write csv path
        #output_df = self.spark.createDataFrame(data=collist, schema = output_schema)
        #output_df.printSchema()
        #output_df.show(50)
        #output_df.repartition(1).write.options(header='True', delimiter='|').csv(csv_path)


    def start(self):

        # initialize spark
        self.initalize_spark()
        print("Spark initialization finished")

        # read csv file
        file1_path = self.cmdline_params.get('file1-path', None)
        file2_path = self.cmdline_params.get('file2-path', None)
        csv_path = self.cmdline_params.get('sparktype-path', None)
        if not file1_path or not file2_path:
            print("file1_path and file2_path are mandatory")
            exit(1)

        s3 = self.init_s3()
        df1 = self.read_file(s3,file1_path)
        df2 = self.read_file(s3,file2_path)
        print("Schema for df1:")
        df1.printSchema()
        print("Schema for df2:")
        df2.printSchema()
        column_types1 = self.read_schema(df1)
        column_types2 = self.read_schema(df2)
        self.build_diff(column_types1, column_types2, csv_path)


if __name__ == "__main__" :

     diffFinder_utility = ArchiveDiffFinder()
     run_uid = diffFinder_utility.start()
