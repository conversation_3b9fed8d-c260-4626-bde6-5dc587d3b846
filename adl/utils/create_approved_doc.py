import pandas as pd
import json

result="""
{
  "Id": "DEFAULT",
  "OwnerId": "DEFAULT",
  "IsDeleted": "false",
  "Name": "DEFAULT",
  "CurrencyIsoCode": "DEFAULT",
  "RecordTypeId": "DEFAULT",
  "CreatedDate": 4.5365284490539983778509312e+25,
  "CreatedById": "DEFAULT",
  "LastModifiedDate": 4.5365284490539983778509312e+25,
  "LastModifiedById": "DEFAULT",
  "SystemModstamp": 4.5365284490539983778509312e+25,
  "LastActivityDate": "1000-01-06",
  "MayEdit": "false",
  "IsLocked": "false",
  "LastViewedDate": 4.5365284490539983778509312e+25,
  "LastReferencedDate": 4.5365284490539983778509312e+25,
  "Detail_Group_vod__c": "DEFAULT",
  "Document_Description_vod__c": "DEFAULT",
  "Document_Host_URL_vod__c": "DEFAULT",
  "Document_ID_vod__c": "DEFAULT",
  "Document_Last_Mod_DateTime_vod__c": 4.5365284490539983778509312e+25,
  "Email_Allows_Documents_vod__c": "false",
  "Email_Domain_vod__c": "DEFAULT",
  "Email_Fragment_HTML_vod__c": "DEFAULT",
  "Email_From_Address_vod__c": "DEFAULT",
  "Email_From_Name_vod__c": "DEFAULT",
  "Email_HTML_1_vod__c": "DEFAULT",
  "Email_HTML_2_vod__c": "DEFAULT",
  "Email_ReplyTo_Address_vod__c": "DEFAULT",
  "Email_ReplyTo_Name_vod__c": "DEFAULT",
  "Email_Subject_vod__c": "DEFAULT",
  "Email_Template_Fragment_Document_ID_vod__c": "DEFAULT",
  "Email_Template_Fragment_HTML_vod__c": "DEFAULT",
  "ISI_Document_ID_vod__c": "DEFAULT",
  "Language_vod__c": "DEFAULT",
  "Other_Document_ID_List_vod__c": "DEFAULT",
  "PI_Document_ID_vod__c": "DEFAULT",
  "Piece_Document_ID_vod__c": "DEFAULT",
  "Product_vod__c": "DEFAULT",
  "Status_vod__c": "DEFAULT",
  "Territory_vod__c": "DEFAULT",
  "Vault_Instance_ID_vod__c": "DEFAULT",
  "Allow_Any_Product_Fragment_vod__c": "false",
  "Allowed_Document_IDs_vod__c": "DEFAULT",
  "Engage_Document_Id_vod__c": "DEFAULT",
  "Vault_Document_ID_vod__c": "DEFAULT",
  "Key_Message_vod__c": "DEFAULT",
  "Events_Management_Subtype_vod__c": "DEFAULT",
  "Survey_vod__c": "DEFAULT",
  "Content_Type_vod__c": "DEFAULT",
  "Bcc_vod__c": "DEFAULT",
  "BIIB_Country__c": "DEFAULT",
  "BIIB_Is_same_as_user_country__c": "false",
  "WeChat_Template_ID_vod__c": "DEFAULT",
  "BIIB_Function__c": "DEFAULT",
  "BIIB_Hospital_Sales_Line__c": "false",
  "BIIB_Market_Access__c": "false",
  "BIIB_Medical__c": "false",
  "BIIB_Sales__c": "false",
  "BIIB_Service__c": "false",
  "BIIB_Approved_Email_Categories__c": "DEFAULT",
  "BIIB_Transaction__c": "DEFAULT",
  "BIIB_AD_Country_Code__c": "DEFAULT",
  "BIIB_Approved_Document_Function__c": "DEFAULT",
  "BIIB_Nurse__c": "false",
  "Publish_Method_vod__c": "DEFAULT",
  "BackupCreatedDate": 4.5365284490539983778509312e+25,
  "BackupModifiedDate": 4.5365284490539983778509312e+25,
  "is_dummy_record": 1
}
"""

parsed = json.loads(result)
final_df = pd.DataFrame.from_dict([parsed], orient="columns")
final_df['CreatedDate'] = pd.to_datetime('01/01/2023', format='%d/%m/%Y')
final_df['LastModifiedDate'] = pd.to_datetime('01/01/2023', format='%d/%m/%Y')
final_df['SystemModstamp'] = pd.to_datetime('01/01/2023', format='%d/%m/%Y')

final_df.info()

final_df.to_parquet('sample_approved_doc.parquet', compression='gzip') 

# This script creates a single row record for approved_document table with timestamp from 2021 (to avoid the following spark error when using year = 1000 with Spark 3):
#   Caused by: org.apache.spark.SparkException: Job aborted due to stage failure: Task 0 in stage 329.0 failed 4 times, most recent failure: Lost task 0.3 in stage 329.0 (TID 5862) (************** executor 12): org.apache.spark.SparkUpgradeException: 
#   You may get a different result due to the upgrading of Spark 3.0: reading dates before 1582-10-15 or timestamps before 1900-01-01T00:00:00Z from Parquet INT96 files can be ambiguous, as the files may be written by Spark 2.x or legacy versions of Hive, which uses a legacy hybrid calendar that is different from Spark 3.0+'s Proleptic Gregorian calendar. 
#   See more details in SPARK-31404. 
#   You can set spark.sql.legacy.parquet.int96RebaseModeInRead to 'LEGACY' to rebase the datetime values w.r.t. the calendar difference during reading. Or set spark.sql.legacy.parquet.int96RebaseModeInRead to 'CORRECTED' to read the datetime values as it is.

# Copy this file as the single dummy row for approved-document to the archive_rds folders to run ADL.
#  aws s3 cp sample_approved_doc.parquet s3://aktana-bdp-biogenap/prod/data/archive_rds/approved_document_vod__c/sysmodstampyear=2023/sysmodstampmonth=1/sysmodstampday=1/part-00000-7ffb4bc9-980c-4e42-9929-584ea15cccbf-c000.gz.parquet
#  aws s3 cp sample_approved_doc.parquet s3://aktana-bdp-biogeneu2/prod/data/archive_rds/approved_document_vod__c/sysmodstampyear=2023/sysmodstampmonth=1/sysmodstampday=1/part-00000-7ffb4bc9-980c-4e42-9929-584ea15cccbf-c000.gz.parquet