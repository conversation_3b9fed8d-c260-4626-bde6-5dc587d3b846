import time
import os
import sys
import json
import mysql.connector
from mysql.connector import Error
from datetime import datetime
from boto3.session import Session
import datetime
import statsd

startTime = datetime.datetime.now()
customer = sys.argv[1]
environment = sys.argv[2]
adlFlag = sys.argv[4]
ecoSystem = "prod"
if len(sys.argv) > 6:
    ecoSystem = sys.argv[6]
region = None
if len(sys.argv) > 7:
    region = sys.argv[7]

job_name = adlFlag + '_' + customer + '_' + environment
ACCESS_KEY = ''
SECRET_KEY = ''
awsRegion = ''
job_state = ''
adlS3Location = ''
JOB_FAIL = 0
ADL_COUNT = 0
EXEC_TIME = 0

if __name__ == "__main__":

    # get CustomerADLConfig data
    file_path = sys.argv[3] + '/learning/adl'
    os.chdir(file_path)
    config = None
    if "METADATA_HOST" in os.environ:
        config = {"host": os.getenv("METADATA_HOST", ""),
                  "database": "aktanameta",
                  "username": os.getenv("METADATA_USERNAME", "appadmin"),
                  "password": os.getenv("METADATA_PASSWORD", "")}
    else:
        with open('customer-metadata.json', 'r') as f:
            config = json.load(f)


    # try:
    #     connection = mysql.connector.connect(host=config["host"], database=config["database"], user=config["username"],
    #                                          password=config["password"])
    #     if connection.is_connected():
    #         cursor = connection.cursor()
    #         query = "select b.`adlS3Location`, b.`awsRegion`, b.`awsAccessKey`, b.`awsSecretKey`, b.`rptS3Location`, b.`environment`, b.`iamRole`, b.`mappingLocation` from `Customer` a join `CustomerADLConfig` b on a.customerId = b.customerId where a.`customerName`='{}' and b.`environment`='{}'".format(
    #             customer, environment)
    #         cursor.execute(query)
    #         record = cursor.fetchall()
    #         if (len(record) == 1):
    #             adlS3Location = record[0][0]
    #             awsRegion = record[0][1]
    #             ACCESS_KEY = record[0][2]
    #             SECRET_KEY = record[0][3]
    #             print('Success reading from CustomerADLConfig table')
    #         else:
    #             print('Customer Information not found in CustomerADLConfig table')
    #             exit(1)
    # except Error as e:
    #     print("Error", e)
    #     # exit(1)
    # finally:
    #     # closing database connection.
    #     if (connection.is_connected()):
    #         cursor.close()
    #         connection.close()

    try:

        # if ecoSystem == "aimqa":
        #     config["host"] = "aimqa-metadatards.aktana.com"
        # elif ecoSystem == "saasdev":
        #     config["host"] = "saasdev-metadatards.aktana.com"

        connection = mysql.connector.connect(host=config["host"], database=config["database"], user=config["username"],
                                             password=config["password"])
        if connection.is_connected():
            cursor = connection.cursor()
            query = "select b.`adlS3Location`, b.`awsRegion`, b.`awsAccessKey`, b.`awsSecretKey`, b.`rptS3Location`, b.`environment`, b.`iamRole`, b.`mappingLocation`, b.`disableADL` from `Customer` a join `CustomerADLConfig` b on a.customerId = b.customerId where a.`customerName`='{}' and b.`environment`='{}'".format(
                customer, environment)
            cursor.execute(query)
            record = cursor.fetchall()
            if (len(record) == 1):
                adlS3Location = record[0][0]
                awsRegion = record[0][1]
                ACCESS_KEY = record[0][2]
                SECRET_KEY = record[0][3]
                disableADL = record[0][8]
                print('Success reading from CustomerADLConfig table')

                if (disableADL == 1):
                    print('ADL execution has been disabled for this customer/env in metadata.  Exiting...')
                    exit(0)
            else:
                print('Customer Information not found in CustomerADLConfig table')
                JOB_FAIL = 1
    except Error as e:
        print("Error", e)
        JOB_FAIL = 1
    finally:
        # closing database connection.
        if (connection.is_connected()):
            cursor.close()
            connection.close()


    # Run the job and catch status
    try:
        session = Session(region_name=awsRegion)
        client = session.client('glue')
        response = client.start_job_run(JobName=job_name)
        if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
            job_run_id = response['JobRunId']
            status = client.get_job_run(JobName=job_name, RunId=job_run_id)
            if status:
                state = status['JobRun']['JobRunState']
                while state != 'SUCCEEDED':
                    status = client.get_job_run(JobName=job_name, RunId=job_run_id)
                    state = status['JobRun']['JobRunState']
                    time.sleep(15)
                    if state in ['SUCCEEDED', 'STOPPED', 'FAILED', 'TIMEOUT']:
                        job_state = state
                        break

            print("Job State: " + job_state)
            EXEC_TIME = client.get_job_run(JobName=job_name, RunId=job_run_id)['JobRun']['ExecutionTime']

            # Get logs from aws
            try:
                # cloudwatch = session.client('logs')
                # response = cloudwatch.get_log_events(logGroupName='/aws-glue/jobs/output', logStreamName=job_run_id)
                # fail = True
                # for x in response["events"]:
                #     if "ADL_DEV_LOGGER" in x["message"]:
                #         print(
                #             datetime.utcfromtimestamp(x["timestamp"] / 1000).strftime('%Y-%m-%d %H:%M:%S') + ":" + x[
                #                 "message"])
                #     if  "ADL_DEV_LOGGER ERROR" in x["message"]:
                #         job_state = "FAILED"
                #         exit(1)
                #     if "final dataset table written" in x["message"]:
                #         fail = False

                # print("Job 2nd State: " + job_state)
                # job_state = client.get_job_run(JobName=job_name, RunId=job_run_id)
                # print("Job 3nd State: " + job_state)
                # if fail: exit(1)

                #////////////////////////
                adlS3Location = adlS3Location.replace("s3://", "")
                adlS3Location = adlS3Location.split('/', 1)
                adlS3Location[1] = adlS3Location[1] + "logs/"
                get_last_modified = lambda obj: int(obj['LastModified'].strftime('%s'))
                s3 = session.client('s3')
                objs = s3.list_objects_v2(Bucket=adlS3Location[0], Prefix=adlS3Location[1])['Contents']
                last_added = [obj['Key'] for obj in sorted(objs, key=get_last_modified, reverse=True)][0]
                tmpTime = last_added.split("/")
                ts = tmpTime[-1].replace(".txt", "")
                if adlFlag == 'ADL':
                    prefix = f'adl_log_{customer}_{environment}_'
                else:
                    prefix = f'hfs_log_{customer}_{environment}_'
                ts = ts.replace(prefix, "")
                if datetime.datetime.strptime(ts, '%Y%m%d%H%M%S') > startTime:
                    data = s3.get_object(Bucket=adlS3Location[0], Key=last_added)
                    contents = data['Body'].read()
                    log_res = contents.decode("utf-8")
                    # print("///////////////////////////////// Content ///////////////////////////////////////" + contents)
                    print("///////////////////////////////// log_res ///////////////////////////////////////" + log_res)
                    print(last_added)
                    if (("ADL_DEV_LOGGER ERROR" in log_res) or ("HFS_DEV_LOGGER ERROR" in log_res)):
                        JOB_FAIL = 1
                    if (adlFlag == 'ADL') and ("final dataset table written" not in log_res) and ("record class updates count is 0, skip processing..." not in log_res):
                        JOB_FAIL = 1
                    if not JOB_FAIL and "final dataset table written" in log_res:
                        # get ADL_COUNT from log
                        ADL_COUNT = int(log_res.split("final recordclasses size ")[1].split()[0])
                else:
                    print("Log was not printed into S3")
                    JOB_FAIL = 1
            except Exception as e:
                print(e)
                JOB_FAIL = 1

            if job_state != 'SUCCEEDED':
                JOB_FAIL = 1

        else:
            print(response)
            JOB_FAIL = 1
    except Exception as e:
        print(e)
        JOB_FAIL = 1
    finally:
        # write observability metrics
        if not region:
            print("Region not defined, exit without writing metrics...")
            exit(JOB_FAIL)

        # USDEVEKS => usdeveksstatsd.aktana.com
        # useks => useksstatsd.aktana.com
        # us => usstatsd.aktana.com
        # euqa => eustatsd.aktana.com
        statsd_server = region + "statsd.aktana.com"
        if "eks" not in region:
            statsd_server = region[:2] + "statsd.aktana.com"
        statsd_port = '9125'

        # metric prefix
        if adlFlag == "ADL":
            metric_prefix = 'type.{TYPE}.cmpny.' + customer + '.regn.' + region + '.cntry.none' + '.env.' + environment + \
                        '.apptype.ADL' + '.cmpnt.' + adlFlag + '.metric'
        else:
            metric_prefix = 'type.{TYPE}.cmpny.' + customer + '.regn.' + region + '.cntry.none' + '.env.' + environment + \
                            '.apptype.FS' + '.cmpnt.' + adlFlag + '.metric'

        # gauge metrics
        statsd_client = statsd.StatsClient(statsd_server, statsd_port,
                                           prefix=metric_prefix.format(TYPE="g"))
        statsd_client.gauge("job_status", JOB_FAIL)
        print(f"Job status: {JOB_FAIL}. 0: SUCCESS, 1: FAIL")
        statsd_client.gauge("exec_time", EXEC_TIME)
        print(f"Execution time: {EXEC_TIME}")
        if adlFlag == "ADL":
            statsd_client.gauge("adl_cnt", ADL_COUNT)
            print(f"{ADL_COUNT} of rows are written to final_dataset")

        # counter metrics
        statsd_client = statsd.StatsClient(statsd_server, statsd_port,
                                           prefix=metric_prefix.format(TYPE="c"))
        if JOB_FAIL:
            statsd_client.incr("fail_cnt", 1)
            print("Failure count increased by 1")
        else:
            statsd_client.incr("success_cnt", 1)
            print("Success count increased by 1")

        exit(JOB_FAIL)








