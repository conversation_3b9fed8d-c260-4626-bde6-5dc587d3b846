#!/usr/bin/env python
# encoding: utf-8
import os
import sys
import requests
import json
import boto3
import mysql.connector
from mysql.connector import Error
from boto3.session import Session

customer = sys.argv[1]
environment = sys.argv[2]
adlFlag = sys.argv[4]
jobName = adlFlag + '_' + customer + '_' + environment
cloningPath = sys.argv[3] + '/learning/adl/'
branch = sys.argv[5]
ecoSystem = "prod"
if len(sys.argv) > 6:
    ecoSystem = sys.argv[6]
    
ACCESS_KEY = ''
SECRET_KEY = ''
awsRegion = ''
s3Destination = ''
rptS3Location = ''
environmentdb = ''
iamRole = ''
mappingLocation = ''
externalAdl = ''
role = 'Aktana-AWSGlueServiceRole-Global'  # Read from CustomerADLConfig table in future
description = 'Deployment of a glue job for ADL'
maxConcurrentRuns = 1
numberOfWorkers = 10
WorkerType = 'G.2X'


def copyADLCodeToS3(source, destination, sub_folder):
    s3Client = boto3.client('s3')
    source = source + sub_folder
    arr = os.listdir(source)
    destination = destination.replace('s3://', '')
    s3Destination = destination.split('/', 1)
    print(f"copy from {source}...")
    for f in arr:
        try:
            response = s3Client.upload_file(source + f, s3Destination[0], s3Destination[1] + sub_folder + f)

        except requests.exceptions.HTTPError as e:
            print(e.response.text)
            exit(1)


def create_job(session, command, defaultArguments, executionProperty):
    try:
        client = session.client('glue')
        response = client.create_job(
            Name=jobName,
            Description=description,
            LogUri='string',
            Role=iamRole,
            ExecutionProperty=executionProperty,
            Command=command,
            DefaultArguments=defaultArguments,
            GlueVersion='3.0',
            MaxRetries=0,
            Timeout=420,
            # MaxCapacity=10.0,
            NumberOfWorkers= numberOfWorkers,
            WorkerType= WorkerType
        )

        if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
            print(jobName + " Job Created Successfully")

        else:
            print(response)
            exit(1)

    except Exception as e:
        print("Job Creation Failed")
        print(e)
        exit(1)


def update_job(session, command, defaultArguments, executionProperty):
    try:
        client = session.client('glue')
        response = client.update_job(
            JobName=jobName,
            JobUpdate={
                "Description": description,
                "LogUri": 'string',
                "Role": iamRole,
                "ExecutionProperty": executionProperty,
                "Command": command,
                "DefaultArguments": defaultArguments,
                "GlueVersion": '3.0',
                "MaxRetries": 0,
                "Timeout": 420,
                #"MaxCapacity": 10.0,
                "NumberOfWorkers": numberOfWorkers,
                "WorkerType":WorkerType
            }
        )

        if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
            print(jobName + " Job Updated Successfully")

        else:
            print(response)
            exit(1)

    except Exception as e:
        print("Job Updation Failed")
        print(e)
        exit(1)


if __name__ == "__main__":
    # get CustomerADLConfig data
    file_path = sys.argv[3] + '/learning/adl'
    os.chdir(file_path)
    config = None
    if "METADATA_HOST" in os.environ:
        config = {"host": os.getenv("METADATA_HOST", ""),
                  "database": "aktanameta",
                  "username": os.getenv("METADATA_USERNAME", "appadmin"),
                  "password": os.getenv("METADATA_PASSWORD", "")}
    else:
        with open('customer-metadata.json', 'r') as f:
            config = json.load(f)

    try:
        # if ecoSystem == "aimqa":
        #     config["host"] = "aimqa-metadatards.aktana.com"
        # elif ecoSystem == "saasdev":
        #     config["host"] = "saasdev-metadatards.aktana.com"

        connection = mysql.connector.connect(host=config["host"], database=config["database"], user=config["username"],
                                             password=config["password"])
        if connection.is_connected():
            cursor = connection.cursor()
            query = "select b.`adlS3Location`, b.`awsRegion`, b.`awsAccessKey`, b.`awsSecretKey`, b.`rptS3Location`, b.`environment`, b.`iamRole`, b.`mappingLocation`, b.`externalAdl`, b.`numberOfWorkers`, b.`workerType` from `Customer` a join `CustomerADLConfig` b on a.customerId = b.customerId where a.`customerName`='{}' and b.`environment`='{}'".format(
                customer, environment)
            cursor.execute(query)
            record = cursor.fetchall()
            if (len(record) == 1):
                s3Destination = record[0][0]
                awsRegion = record[0][1]
                ACCESS_KEY = record[0][2]
                SECRET_KEY = record[0][3]
                rptS3Location = record[0][4]
                environmentdb = record[0][5]
                iamRole = record[0][6]
                mappingLocation = record[0][7]
                externalAdl = record[0][8]
                if record[0][9]:
                    numberOfWorkers = int(record[0][9])
                if record[0][10]:
                    WorkerType = record[0][10]
            else:
                print('Customer Information not found in CustomerADLConfig table')
                exit(1)
    except Error as e:
        print("Error", e)
        exit(1)
    finally:
        # closing database connection.
        if (connection.is_connected()):
            cursor.close()
            connection.close()

    s3CustomerAdl = s3Destination

    # Copy code to appropriate S3 bucket
    s3Destination = s3Destination.replace('s3://', '')
    mappingLocation = "s3://aktana-bdp-adlcommon/" + branch + '/common/'
    # mappingFolder = mappingLocation.split('/common/')[0].split('/')[-1]


    # print("mappingFolder: " + mappingFolder)
    # if environment == 'prod' and mappingFolder != branchName:
    #     print("Versions not match!")
    #     exit(1)

    # if environment != 'prod':
    #     copyADLCodeToS3(cloningPath, mappingLocation, 'code/')
    #     copyADLCodeToS3(cloningPath, mappingLocation, 'data/')
    #     copyADLCodeToS3(cloningPath, mappingLocation, 'lib/')

    # Set extra job parameters to appropriate relative paths
    if adlFlag == "ADL":
        ScriptLocation = mappingLocation + 'code/adlDriver.py'
    else:
        ScriptLocation = mappingLocation + 'code/hcpFeatureStoreDriver.py'
    command = {
        'Name': 'glueetl',
        'ScriptLocation': ScriptLocation,
        'PythonVersion': '3'
    }

    defaultArguments = {
        '--customer': customer,
        '--environment': environment,
        '--accesKey': ACCESS_KEY,
        '--secreteKey': SECRET_KEY,
        '--rptS3Location': rptS3Location,
        '--s3CustomerAdl': s3CustomerAdl,
        '--mappingLocation': mappingLocation,
        '--externalAdl': externalAdl,
        '--conf': 'spark.sql.legacy.parquet.int96RebaseModeInRead=CORRECTED --conf spark.sql.legacy.parquet.int96RebaseModeInWrite=CORRECTED --conf spark.sql.legacy.parquet.datetimeRebaseModeInRead=CORRECTED --conf spark.sql.legacy.parquet.datetimeRebaseModeInWrite=CORRECTED',
        '--extra-jars': mappingLocation + 'lib/delta-core_2.12-1.0.0.jar',
        '--extra-py-files': mappingLocation + 'code/dataLoading.py'
                            + ',' + mappingLocation + 'code/utils.py'
                            + ',' + mappingLocation + 'code/dseRecordClassTrans.py'
                            + ',' + mappingLocation + 'code/finalDataTrans.py'
                            + ',' + mappingLocation + 'code/initRecordClass.py'
                            + ',' + mappingLocation + 'code/stRecordClassTransCri.py'
                            + ',' + mappingLocation + 'code/suggestionsProductTrans.py'
                            + ',' + mappingLocation + 'code/adlLogger.py'
                            + ',' + mappingLocation + 'code/deDuplicate.py'
                            + ',' + mappingLocation + 'code/emailFeatures.py'
                            + ',' + mappingLocation + 'code/hcpFeatureStoreDriver.py'
                            + ',' + mappingLocation + 'code/virtualVisitFeatures.py'
                            + ',' + mappingLocation + 'code/visitFeatures.py'
                            + ',' + mappingLocation + 'code/phoneFeatures.py'
                            + ',' + mappingLocation + 'code/channelFeatures.py'
                            + ',' + mappingLocation + 'code/demographicFeatures.py'
                            + ',' + mappingLocation + 'code/targetFeatures.py',
        '--extra-files': mappingLocation + 'code/config.json'
                        + ',' + mappingLocation + 'code/hcpConfig.json',
        '--TempDir': s3CustomerAdl + 'logs/',
        '--job-bookmark-option': 'job-bookmark-disable'
    }

    executionProperty = {
        'MaxConcurrentRuns': maxConcurrentRuns
    }

    # Create / Update the Glue job
    try:
        session = Session(region_name=awsRegion)
        client = session.client('glue')
        response = client.get_job(JobName=jobName)
        if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
            print("Job already exists, now updating the job")
            update_job(session, command, defaultArguments, executionProperty)
    except Exception as e:
        if type(e).__name__ == "EntityNotFoundException":
            print("Creating new job: " + jobName)
            create_job(session, command, defaultArguments, executionProperty)
        else:
            print(e)
            exit(1)
