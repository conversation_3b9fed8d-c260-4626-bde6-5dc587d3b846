"""
Module s3_manager
"""

import boto3
import botocore.exceptions
import logging
import time
import os
from adl.automation.utils.config_loader import load_config

logger = logging.getLogger(__name__)
customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

ACCESS_KEY = str(environment.get("adl")["access_key"])
SECRET_KEY = str(environment.get("adl")["secret_key"])
AWS_REGION = str(environment.get("adl")["region"])


class AWSManager:
    """Request Manager"""

    __instance = None

    def __init__(self, service=None, region=None, access_key=None, secret=None):
        """S3 client initial instantiation"""
        self.region = region
        self.access_key = access_key
        self.secret = secret
        self.client = boto3.client(service, region_name=region, aws_access_key_id=access_key,
                                   aws_secret_access_key=secret)

    @staticmethod
    def get_instance(service='s3'):
        """This method get a instance of the S3Manager class.
        Returns:
            S3Manager -- return a instance of S3Manager class.
        """
        if AWSManager.__instance is None:
            AWSManager.__instance = AWSManager(service, AWS_REGION, ACCESS_KEY, SECRET_KEY)
        return AWSManager.__instance

    def s3_key_exist(self, bucket_name, key):
        """This method verifies if an specific key exist
        """
        response = self.client.list_objects_v2(Bucket=bucket_name, Prefix=key)
        if response:
            for obj in response['Contents']:
                if key == obj['Key']:
                    return True
        return False

    def s3_get_sub_folders(self, bucket_name, prefix):
        response = self.client.list_objects(Bucket=bucket_name, Prefix=prefix, Delimiter='/')
        try:
            sub_folders = response.get('CommonPrefixes')
            return sub_folders
        except botocore.exceptions.DataNotFoundError as ex:
            logger.error(f'Unable to get CommonPrefixes {ex}')

    def s3_folder_exists(self, bucket_name, folder_key):
        response = self.client.list_objects(Bucket=bucket_name, Prefix=folder_key)
        try:
            result = response.get('Contents')
            return result
        except botocore.exceptions.ParamValidationError as ex:
            logger.error(f'folder does not exist {ex}')

    def s3_folder_contains_files(self, bucket_name, folder_key, extension):
        response = self.client.list_objects(Bucket=bucket_name, Prefix=folder_key)
        try:
            count = 0
            contents = response['Contents']
            for item in contents:
                key = item.get("Key")
                if key.endswith(f".{extension}"):
                    count = count + 1
            return count > 0
        except botocore.exceptions.ParamValidationError as ex:
            logger.error(f'folder does not exist {ex}')

    def glue_get_job_last_execution_status(self, job_name):
        job_runs = self.client.get_job_runs(JobName=job_name)
        last_status = job_runs.get('JobRuns')[0].get('JobRunState')

        return last_status

    def glue_run_job(self, job_name, wait_until_finished=True, check_interval=15):
        time_elapsed = 0
        job = self.client.start_job_run(JobName=job_name)
        if wait_until_finished:
            status = self.glue_get_job_last_execution_status(job_name)
            while status == 'RUNNING':
                status = self.glue_get_job_last_execution_status(job_name)
                logger.info(f'{job_name} still Running after {time_elapsed / 60} minutes')
                time.sleep(check_interval)
                time_elapsed += 15

        return job

    def s3_folder_file_exists(self, bucket_name, folder_key, name_file):
        response = self.client.list_objects(Bucket=bucket_name, Prefix=folder_key)
        count = 0
        try:
            contents = response.get('Contents')
            for item in contents:
                if item.get("Key").endswith(f"{name_file}"):
                    count = count + 1

            if count > 0:
                return True
            else:
                return False
        except botocore.exceptions.ParamValidationError as ex:
            logger.error(f'file does not exist {ex}')

    def s3_get_file_key_last_modified(self, bucket_name, prefix, contain_text=None):
        def get_last_modified(obj): return int(obj['LastModified'].strftime('%s'))
        result = self.client.list_objects_v2(Bucket=bucket_name, Prefix=prefix, Delimiter='/')
        contents = result.get('Contents')
        if contain_text is None:
            last_added_key = [obj['Key'] for obj in sorted(contents, key=get_last_modified, reverse=True)][0]
        else:
            contents_ordered = [obj['Key'] for obj in sorted(contents, key=get_last_modified, reverse=True)]
            last_added_key = list(filter(lambda x: contain_text in x, contents_ordered))[0]
        return last_added_key

    def s3_read_file(self, bucket_name, name_file):
        data = self.client.get_object(Bucket=bucket_name, Key=name_file)
        contents = data['Body'].read()
        return str(contents.decode("utf-8"))

    def get_txt_file_content(self, bucket_name, prefix_name):
        list_obj = self.client.list_objects(Bucket=bucket_name, Prefix=prefix_name)
        latest_obj = max(list_obj['Contents'], key=lambda x: x['LastModified'])
        key_string = latest_obj['Key']
        txt_object = self.client.get_object(Bucket=bucket_name, Key=key_string)
        body = txt_object.get('Body').iter_lines()
        list = []
        for line in body:
            line = line.decode('utf-8')
            list.append(line)
        return list

    def obtain_last_backup(self, bucket_name, prefix_table, object_key):
        objects = self.client.list_objects(Bucket=bucket_name, Prefix=prefix_table)
        latest_backup = max(objects['Contents'], key=lambda x: x['LastModified'])
        if len(latest_backup) > 0:
            latest_object_key = latest_backup['Key']
            latest_object_key = latest_object_key.split('/', 5)
            latest_backup = latest_object_key[3] + object_key
            logger.debug(f"The s3 key latest backup is: {latest_backup}")
        else:
            logger.error(f"A folder with the {prefix_table} wasn't find")
        return latest_backup

    def get_folder_content(self, bucket_name, folder_prefix, object_key=""):
        objects = self.client.list_objects_v2(Bucket=bucket_name, Prefix=folder_prefix, Delimiter=object_key)
        content = objects.get('Contents')

        return content
