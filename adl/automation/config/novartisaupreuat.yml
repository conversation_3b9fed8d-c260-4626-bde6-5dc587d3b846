environment_info:
  customer_name: dconovartisau
  environment: preuat
adl:
  region: eu-central-1
  access_key: ********************
  secret_key: hQcWLr1CYvT7fdjMLTlyAz2pe3Roll9wwqWAaWl3
  bucket: aktana-bdp-dconovartisau
  rpt_bucket: aktana-bdp-novartisau
  bucket_common: aktana-bdp-adlcommon
  base_adl_url: s3a://aktana-bdp-dconovartisau/preuat/adl/data/
  base_rpt_url: s3a://aktana-bdp-novartisau/prod/data/archive_rds/
  base_config_url: s3a://aktana-bdp-dconovartisau/preuat/adl/config/
  base_adl_key: preuat/adl2/data/{}/
  base_data_key: s3a://aktana-bdp-adlcommon/{}/common/data/
  base_adl_logs_key: preuat/adl2/logs/
  base_adl_rds_key: prod/data/archive_rds/
  base_common_key: "{}/common/"

channel_propensity:
  bucket: aktana-bdp-dconovartisau
  base_cp_model_key: preuat/dco/data/dco_read_write/raw_files/channel_propensity/model/
  learning_docker_job_id: dconovartisau-prod-Run_Learning_Docker_Job

aws:
  glue_job_name: ADL_dconovartisau_preuat
ssh:
  host: bdpeuqa001.aktana.com
  port: 22
db:
  remote_address: dconovartisaurds.aktana.com
  remote_port: 3306
  user_name: automationqa
  local_port: 33068
  password: ********************************
  db_name: novartisaupreuat
  stage_db_name: novartisaupreuat_stage
metadata:
  db_name: aktanameta
  user_name: appadmin
  local_port: 33068
  password: ******************************
rundeck:
  api_token: 'RWSya04GqJu1JWDVvQsds9LMNQCHIkHN'
  base_url: https://ops-bdp-dconovartisau.aktana.com
  deploy_job_id: dconovartisau-preuat-Deploy_Learning_ADL
  run_job_id: dconovartisau-preuat-Run_Learning_ADL
  run_all_model_job_id: dconovartisau-preuat-Run_Learning-
  run_copy_s3_job_id: 54ab0bb2-6ea1-4f65-915f-e442c00caf14
  api_version: 23
  anchor_time:
    nightly_score: 98
    nightly_score_default: 60
    accuracy: 14
    old_nightly_score: 29
    old_accuracy: 0
    parallel: 63
    parallel_accuracy: 9
  rem_time:
    nightly_score: 71
  tte_time:
    manual_score: 178
    manual_build: 0
    nightly_score: 0
    nightly_build: 0
    build: 301
    re_build: 301
    predictor_optimize: 311
    reporting_only: 311
    event_type: 0
  mso_time:
snowFlake:
  sfOptions_spark_snowflake: {"sfURL": "aktana.eu-central-1.snowflakecomputing.com",
            "sfUser": "dconovartisau_ML_admin",
            "sfPassword": "********************************",
            "sfDatabase": "dconovartisau_dw_preuat",
            "sfSchema": "CHANNEL_PROPENSITY",
            "sfWarehouse": "EUQA_WH_001",
            "sfRole": "AKT_DCONOVARTISAU_US_NONPROD_ML_ADMIN_ROLE",
            "sfAccount": "aktana.eu-central-1",
            "parallelism": "64"}
learning_api:
  secret: "AktanaSecret427518!"
  username: "automation"
  password: "RPjBHkJVqaieWLA"
  learning_url_base: ""
