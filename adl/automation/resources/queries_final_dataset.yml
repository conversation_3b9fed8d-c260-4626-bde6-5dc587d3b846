query:
  tc_11836: "with result as(SELECT a.accountId as account1,a.latitude as latitude1,b.accountId as account2,b.latitude \
  as latitude2 FROM parquet.`{}` a inner JOIN parquet.`{}` b on a.accountId=b.accountId) \
  SELECT * FROM result WHERE latitude1!=latitude2"
  tc_11837: "with result as(SELECT a.accountId as account1,a.longitude as longitude1,b.accountId as  \
            account2,b.longitude as longitude2 FROM parquet.`{}` a inner JOIN parquet.`{}` b on  \
            a.accountId=b.accountId) SELECT * FROM result WHERE longitude1!=longitude2"
  tc_11838: "with result as(SELECT a.accountId as account1,a.geoLocationString as geoLocationString1,b.accountId as  \
            account2,b.geoLocationString as geoLocationString2 FROM parquet.`{}` a inner JOIN parquet.`{}`  \
            b on a.accountId=b.accountId) SELECT * FROM result WHERE geoLocationString1!=geoLocationString2"
  tc_11839: "with result as(SELECT a.accountId as account1,a.timeZoneId as timeZoneId1,b.accountId as  \
            account2,b.timeZoneId as timeZoneId2 FROM parquet.`{}` a inner JOIN parquet.`{}`  \
            b on a.accountId=b.accountId)SELECT * FROM result WHERE timeZoneId1!=timeZoneId2"
