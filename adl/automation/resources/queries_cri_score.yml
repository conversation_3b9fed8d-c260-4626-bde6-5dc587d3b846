query:
  result1_tc_12087: "with result1 as (SELECT first(interactionRepAccount) as interactionRepAccount,  \
  MAX(suggestionEmailScore) as suggestionEmailScore, interactionYearMonth  FROM parquet.`{}`  \
  where interactionRepAccount LIKE '%10794%' and interactionYearMonth=202010 group by  \
  interactionYearMonth limit 15)select a.accountId, a.productId, a.criSuggestionEmailScore,  \
  a.yearMonth, b.suggestionEmailScore, b.interactionYearMonth, b.interactionRepAccount  \
  FROM parquet.`{}` a  join result1 b on a.yearMonth=b.interactionYearMonth WHERE a.accountId=10794"
  result2_tc_12087: "with result2 as (with result1 as (SELECT first(interactionRepAccount) as interactionRepAccount, \
  MAX(suggestionEmailScore) as suggestionEmailScore, interactionYearMonth,  \
  SUBSTRING(first(interactionRepAccount), 6, 10) as account FROM parquet.`{}` group by  \
  interactionYearMonth limit 15)SELECT a.accountId, a.productId, a.criSuggestionEmailScore,  \
  a.yearMonth, b.interactionRepAccount, b.suggestionEmailScore, b.interactionYearMonth, b.account  \
  FROM parquet.`{}` a  join result1 b on a.yearMonth=b.interactionYearMonth  \
  WHERE a.accountId=b.account)SELECT * FROM result2  \
  WHERE criSuggestionEmailScore!=suggestionEmailScore"

  result1_tc_12088: "with result1 as (SELECT first(interactionRepAccount) as interactionRepAccount,  \
  MAX(suggestionVisitScore) as suggestionVisitScore, interactionYearMonth  FROM parquet.`{}`  \
  where interactionRepAccount LIKE '%10134%' and interactionYearMonth=202010 group by  \
  interactionYearMonth limit 15)SELECT a.accountId, a.productId, a.criSuggestionVisitScore,  \
  a.yearMonth, b.suggestionVisitScore, b.interactionYearMonth, b.interactionRepAccount  \
  FROM parquet.`{}` a  join result1 b on a.yearMonth=b.interactionYearMonth WHERE a.accountId=10134"
  result2_tc_12088: "with result3 as (with result2 as (with result1 as(SELECT interactionRepAccount,  \
  suggestionVisitScore, interactionYearMonth, SUBSTRING(interactionRepAccount, 6, 10) as account  \
  FROM parquet.`{}`)SELECT MAX(suggestionVisitScore) as suggestionVisitScore, interactionYearMonth,  \
  account FROM result1 group by interactionYearMonth, account)select a.accountId, a.productId,  \
  a.criSuggestionVisitScore, a.yearMonth, b.suggestionVisitScore, b.interactionYearMonth, b.account  \
  FROM parquet.`{}` a  join result2 b on a.yearMonth=b.interactionYearMonth WHERE  \
  a.accountId=b.account) SELECT * FROM result3 where criSuggestionVisitScore!=suggestionVisitScore"

  result1_tc_12089: "with result1 as (SELECT first(interactionRepAccount) as interactionRepAccount,  \
  MAX(targetAchievementScore) as targetAchievementScore, interactionYearMonth  FROM parquet.`{}`  \
  where interactionRepAccount LIKE '%********%' and interactionYearMonth=202009 group by  \
  interactionYearMonth LIMIT 15)select a.accountId, a.productId, a.criTargetAchievementScore,  \
  a.yearMonth, b.targetAchievementScore, b.interactionYearMonth, b.interactionRepAccount  \
  FROM parquet.`{}` a  join result1 b on a.yearMonth=b.interactionYearMonth WHERE a.accountId=********"
  result2_tc_12089: "with result2 as (with result1 as (SELECT first(interactionRepAccount) as interactionRepAccount,  \
  MAX(targetAchievementScore) as targetAchievementScore, interactionYearMonth,  \
  SUBSTRING(first(interactionRepAccount), 6, 10) as account FROM parquet.`{}` group by  \
  interactionYearMonth limit 15)select a.accountId, a.productId, a.criTargetAchievementScore,  \
  a.yearMonth, b.interactionRepAccount, b.targetAchievementScore, b.interactionYearMonth, b.account  \
  FROM parquet.`{}` a  join result1 b on a.yearMonth=b.interactionYearMonth WHERE  \
  a.accountId=b.account) SELECT * FROM result2 where criTargetAchievementScore!=targetAchievementScore"

  result1_tc_12090: "with result1 as (SELECT first(interactionRepAccount) as interactionRepAccount, MAX(tenureScore) as \
  tenureScore, interactionYearMonth  FROM parquet.`{}` WHERE interactionRepAccount LIKE '%10134%'  \
  and interactionYearMonth=202010 group by interactionYearMonth limit 15) SELECT a.accountId,  \
  a.productId, a.criTenureScore, a.yearMonth, b.tenureScore, b.interactionYearMonth,  \
  b.interactionRepAccount FROM parquet.`{}` a  join result1 b on a.yearMonth=b.interactionYearMonth  \
  WHERE a.accountId=10134"
  result2_tc_12090: "with result3 as (with result2 as (with result1 as (SELECT interactionRepAccount, tenureScore, \
  interactionYearMonth, SUBSTRING(interactionRepAccount, 6, 10) as account FROM parquet.`{}`) \
  select MAX(tenureScore) as tenureScore, interactionYearMonth, account FROM result1 group by  \
  interactionYearMonth,account) SELECT a.accountId, a.productId, a.criTenureScore, a.yearMonth,  \
  b.tenureScore, b.interactionYearMonth, b.account FROM parquet.`{}` a  join result2 b on  \
  a.yearMonth=b.interactionYearMonth WHERE a.accountId=b.account)SELECT * FROM result3  \
  WHERE criTenureScore!=tenureScore"