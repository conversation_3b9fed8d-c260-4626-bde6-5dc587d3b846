
query_tc_12061: "with result as(SELECT a.accountId as account1, a.yearMonth as yearMonth,b.accountId as account2,
  b.interactionYear<PERSON>onth as interactionYearMonth FROM parquet.`{}` a inner join parquet.`{}` b on 
  a.accountId=b.accountId and a.yearMonth=b.interactionYearMonth) SELECT * FROM result 
  WHERE yearMonth!=interactionYearMonth"

query_tc_12064: "with result as(SELECT a.accountId as account1,a.accountUid  as accountUid ,b.accountId  
              as account2,b.externalId  as externalId  FROM parquet.`{}` a inner join parquet.`{}` b on 
              a.accountId=b.accountId) SELECT * FROM result WHERE accountUid!=externalId"

query_tc_12067: "with result as(SELECT a.accountId as account1,a.facilityId  as facilityId1 ,b.accountId as account2,\
              b.facilityId  as facilityId  FROM parquet.`{}` a inner join parquet.`{}` b on a.accountId=b.accountId)  \
              SELECT * FROM result WHERE facilityId1!=facilityId "

query_tc_12069: "with result as(SELECT a.accountId as account1,a.hcpGender as gender1,b.accountId as  \
                   account2,b.hcpGender as gender2 FROM parquet.`{}` a inner join parquet.`{}` b on  \
                   a.accountId=b.accountId) SELECT * FROM result WHERE gender1!=gender2"

query_tc_12070: "with result as(SELECT a.accountId as account1,a.hcpSpec as hcpSpec1,b.accountId as  \
                   account2,b.hcpSpec as hcpSpec2 FROM parquet.`{}` a inner join parquet.`{}` b on  \
                   a.accountId=b.accountId) SELECT * FROM result WHERE hcpSpec1!=hcpSpec2"

query_tc_12071: "with result as(SELECT a.accountId as account1,a.hcpSpec2 as hcpSpec1,b.accountId as  \
                   account2,b.hcpSpec2 as hcpSpec2 FROM parquet.`{}` a inner join parquet.`{}` b on  \
                   a.accountId=b.accountId) SELECT * FROM result WHERE hcpSpec1!=hcpSpec2"