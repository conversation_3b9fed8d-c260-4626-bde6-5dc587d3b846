from abc import ABCMeta, abstractmethod


class IBuilder(metaclass=ABCMeta):
    """The Builder Interface"""

    @staticmethod
    @abstractmethod
    def build_project_name(param):
        """Build CP params"""

    @staticmethod
    @abstractmethod
    def build_repo_branch(param):
        """Build CP params"""

    @staticmethod
    @abstractmethod
    def build_script_to_execute(param):
        """Build CP params"""

    @staticmethod
    @abstractmethod
    def build_script_parameters(param):
        """Build CP params"""

    @staticmethod
    @abstractmethod
    def build_environment_variables(param):
        """Build CP params"""

    @staticmethod
    @abstractmethod
    def build_ecosystem(param):
        """Build CP params"""

    @staticmethod
    @abstractmethod
    def get_result():
        """return result"""


class Builder(IBuilder):
    """Concrete builder"""

    def __init__(self):
        self.rundeck_params = CPRundeckParams()

    def build_project_name(self, project_name):
        self.rundeck_params.project_name = project_name
        return self

    def build_repo_branch(self, branch):
        self.rundeck_params.branch = branch
        return self

    def build_script_to_execute(self, script_to_execute):
        self.rundeck_params.script_to_execute = script_to_execute
        return self

    def build_script_parameters(self, script_parameters):
        self.rundeck_params.script_parameters = script_parameters
        return self

    def build_environment_variables(self, environment_variables):
        self.rundeck_params.environment_variables = environment_variables
        return self

    def build_ecosystem(self, ecosystem):
        self.rundeck_params.ecosystem = ecosystem
        return self

    def get_result(self):
        return self.rundeck_params


class CPRundeckParams:
    def __init__(self):
        self.project_name = 'learning'
        self.branch = 'develop'
        self.script_to_execute = ''
        self.script_parameters = ''
        self.environment_variables = ''
        self.ecosystem = ''

    def __str__(self):
        return f"BRANCH: {self.branch}, SCRIPT: {self.script_to_execute}, ENV_VARS: {self.environment_variables}, " \
               f"SCRIPT_PARAMS: {self.script_parameters}, PROJECT_NAME: {self.project_name}, ECOSYSTEM: {self.ecosystem}"