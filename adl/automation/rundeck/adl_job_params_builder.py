from abc import ABCMeta, abstractmethod


class IBuilder(metaclass=ABCMeta):
    """The Builder Interface"""

    @staticmethod
    @abstractmethod
    def build_feature_store(param):
        """Build ADL params"""

    @staticmethod
    @abstractmethod
    def build_adl_branch(param):
        """Build ADL params"""

    @staticmethod
    @abstractmethod
    def get_result():
        """return result"""


class Builder(IBuilder):
    """Concrete builder"""

    def __init__(self):
        self.rundeck_params = RundeckParams()

    def build_adl_branch(self, branch):
        self.rundeck_params.branch = branch
        return self

    def build_feature_store(self, feature_store):
        self.rundeck_params.feature_store = feature_store
        return self

    def get_result(self):
        return self.rundeck_params


class RundeckParams:
    def __init__(self):
        self.branch = 'develop'
        self.feature_store = ''

    def __str__(self):
        return f'BRANCH: {self.branch}, ' \
               f'FEATURE_STORE: {self.feature_store}'