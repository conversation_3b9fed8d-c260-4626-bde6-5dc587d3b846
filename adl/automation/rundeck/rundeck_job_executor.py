import os
import logging
from adl.automation.utils.config_loader import load_config
from adl.automation.rundeck.rundeck_manager import RundeckManager
logger = logging.getLogger(__name__)


class RundeckJobExecutor:
    def __init__(self):
        customer = os.environ.get("ENV_NAME")
        self.environment = load_config(f'adl/automation/config/{customer}').data
        self.rundeck = RundeckManager(self.environment.get("rundeck")["base_url"],
                                      self.environment.get("rundeck")["api_token"],
                                      self.environment.get("rundeck")["api_version"])

    def run_job(self, builder, job_id, model="ADL"):
        """This method is in charge to execute a Rundeck job according to the parameters specified in builder object
        """
        options = self.__get_job_options(builder, model)
        print("options: ", options)
        self.rundeck.run_job(job_id, options)

    def get_status_last_execution(self, job_id):
        """This method is in charge to retrieve the status of last job executed
        """
        status = self.rundeck.get_last_execution_status(job_id)

        return status

    def validate_output_contains_log_entry(self, job_id, log_entry):
        response = False
        entries = self.rundeck.get_output_from_last_job(job_id)
        for entry in entries:
            if log_entry in entry.get("log"):
                response = True
                break

        return response

    def validate_output_has_errors(self, job_id):
        """
        This method will verify if output contains ERRORS
        Args:
            job_id: rundeck job id

        Returns:
            True if there are errors in the output
        """
        response = False
        entries = self.rundeck.get_output_from_last_job(job_id)

        for entry in entries:
            if entry.get('log').contains('ERROR'):
                response = True
                logger.info(entry.get('log'))
                break

        return response

    def __get_job_options(self, builder, model):
        """This method build the parameters for Rundeck in JSON format"""
        if model == "ADL":
            options = {'BRANCH': builder.branch,
                       'FEATURE_STORE': builder.feature_store}
        elif model == "COPY_CODE":
            options = {'RELEASE_VERSION': builder.branch}
        elif model == "CP":
            options = {'BRANCH': builder.branch,
                       'SCRIPT': builder.script_to_execute,
                       'ENV_VARS': builder.environment_variables,
                       'SCRIPT_PARAMS': builder.script_parameters,
                       'PROJECT_NAME': builder.project_name,
                       'ECOSYSTEM': builder.ecosystem}
        else:
            options = {'SelectedModule': builder.selected_module,
                       'TERMINATE_CLUSTER': builder.terminate_cluster,
                       'TASKGROUP1': builder.taskgroup1,
                       'GIT_BRANCH_NAME_OVD': builder.branch,
                       'PARARUN_MODEL': builder.model,
                       'Selected_Driver': builder.selected_driver,
                       'TASKGROUP5': builder.task5,
                       'MODEL_TYPE': builder.model_type,
                       'RUN_UID': builder.run_uid,
                       'CONFIG_UID': builder.config_uid,
                       'BUILD_UID': builder.build_uid}
        return options

    def get_execution_info(self, job_id, info):
        return self.rundeck.get_execution_info(job_id, info)

    def get_execution_time(self, job_id):
        """
        this method will get the time that job takes to be executed
        """
        execution = self.rundeck.get_execution_state_for_last_job_execution(job_id)
        start_time = execution.get('startTime')
        end_time = execution.get('endTime')
        from datetime import datetime
        datetime_obj_1 = datetime.strptime(start_time, "%Y-%m-%dT%H:%M:%SZ")
        datetime_obj_2 = datetime.strptime(end_time, "%Y-%m-%dT%H:%M:%SZ")
        execution_time = datetime_obj_2 - datetime_obj_1
        return execution_time

