"""Module rundeck_manager
"""
import logging
import time

from pyrundeck import Rundeck

logger = logging.getLogger(__name__)


class RundeckManager:
    """Request Manager"""

    __instance = None

    def __init__(self, base_url=None, token=None, api_version=None):
        """Request Manager initial instantiation"""
        self.base_url = base_url
        self.token = token
        self.api_version = api_version
        self.rundeck = Rundeck(self.base_url, self.token, self.api_version)

    @staticmethod
    def get_instance(base_url=None, token=None):
        """This method get a instance of the RundeckManager class.

        Returns:
            RundeckManager -- return a instance of RundeckManager class.
        """
        if RundeckManager.__instance is None:
            RundeckManager.__instance = RundeckManager(base_url, token)
        return RundeckManager.__instance

    def get_executions_list_by_status(self, job_id, status='running'):
        """Sends request to verify if job builds are running, failed or succeeded
        """
        running_jobs = self.rundeck.get_executions_for_job(job_id=job_id, status=status)
        return running_jobs

    def run_job(self, job_id, options=None, wait_until_finished=True, check_interval=15):
        """Sends request to execute a job
        """
        time_elapsed = 0
        self.rundeck.run_job(job_id, options=options)
        running_jobs = self.rundeck.get_executions_for_job(job_id=job_id, status='running')
        if wait_until_finished:
            job_name = running_jobs.get('executions')[0].get('job').get('name')
            while running_jobs.get('executions'):
                running_jobs = self.get_executions_list_by_status(job_id)
                logger.info(f'{job_name} still Running after {time_elapsed / 60} minutes')
                time.sleep(check_interval)
                time_elapsed += 15

    def get_last_execution_status(self, job_id):
        """Sends request to get the last execution status
        """
        return self.get_last_execution(job_id).get('status')

    def get_last_execution(self, job_id):
        """Sends request to get the last execution
        """
        return self.rundeck.get_executions_for_job(job_id=job_id).get('executions')[0]

    def get_execution_id(self, job_id):
        """Sends request to get last execution id
        """
        return self.get_last_execution(job_id).get('id')

    def get_job(self, job_name):
        """ Sends request to get job by name
        """
        return self.rundeck.get_job(job_name)

    def list_jobs(self, project):
        """
        list all the jobs inside the project
        :param project:
        :return:
        """
        return self.rundeck.list_jobs(project)

    def get_output_from_last_job(self, job_id):
        """
        get all etries from latest execution
        Args:
            job_id: rundeck job id

        Returns:
        a list of log entries
        """
        execution_id = self.get_execution_id(job_id)
        output = self.rundeck.execution_output_by_id(execution_id)

        return output.get('entries')

    def get_execution_info(self, job_id, info):
        """Sends request to get the info of the job
        """
        return self.rundeck.get_executions_for_job(job_id, None).get('executions')[0].get(info)

    def get_execution_state_for_last_job_execution(self, job_id):
        """
        get execution Status for a job execution
        """
        execution_id = self.rundeck.get_executions_for_job(job_id=job_id).get('executions')[0].get('id')
        return self.rundeck.execution_state(execution_id)
