from abc import ABCMeta, abstractmethod


class IBuilder(metaclass=ABCMeta):
    """The Builder Interface"""

    @staticmethod
    @abstractmethod
    def build_selected_module(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def build_selected_driver(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def build_taskgroup1(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def build_terminate_cluster(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def build_model(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def build_branch(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def build_task5(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def build_model_type(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def run_uid(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def config_uid(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def build_uid(param):
        """Build All model params"""

    @staticmethod
    @abstractmethod
    def get_result():
        """return result"""


class AllModuleBuilder(IBuilder):
    """Concrete builder"""

    def __init__(self):
        self.rundeck_params = RundeckParams()

    def build_selected_module(self, selected_module):
        self.rundeck_params.selected_module = selected_module
        return self

    def build_selected_driver(self, selected_driver):
        self.rundeck_params.selected_driver = selected_driver
        return self

    def build_taskgroup1(self, taskgroup1):
        self.rundeck_params.taskgroup1 = taskgroup1
        return self

    def build_terminate_cluster(self, terminate_cluster):
        self.rundeck_params.terminate_cluster = terminate_cluster
        return self

    def build_model(self, model):
        self.rundeck_params.model = model
        return self

    def build_branch(self, branch):
        self.rundeck_params.branch = branch
        return self

    def build_task5(self, task5):
        self.rundeck_params.task5 = task5
        return self

    def build_model_type(self, model_type):
        self.rundeck_params.model_type = model_type
        return self

    def run_uid(self, run_uid):
        self.rundeck_params.run_uid = run_uid
        return self

    def config_uid(self, config_uid):
        self.rundeck_params.config_uid = config_uid
        return self

    def build_uid(self, build_uid):
        self.rundeck_params.build_uid = build_uid
        return self

    def get_result(self):
        return self.rundeck_params


class RundeckParams:
    def __init__(self):
        self.selected_module = ''
        self.selected_driver = ''
        self.taskgroup1 = 'performLearningrunFlow'
        self.terminate_cluster = 'true'
        self.model = ''
        self.branch = ''
        self.task5 = ''
        self.model_type = ''
        self.run_uid = ''
        self.config_uid = ''
        self.build_uid = ''

    def __str__(self):
        return f'SelectedModule: {self.selected_module}' \
               f'TERMINATE_CLUSTER: {self.terminate_cluster}' \
               f'TASKGROUP1: {self.taskgroup1}' \
               f'GIT_BRANCH_NAME_OVD: {self.branch}' \
               f'PARARUN_MODEL: {self.model}' \
               f'Selected_Driver: {self.selected_driver}' \
               f'TASKGROUP5: {self.task5}' \
               f'MODEL_TYPE: {self.model_type}' \
               f'RUN_UID: {self.run_uid}' \
               f'CONFIG_UID: {self.config_uid}' \
               f'BUILD_UID: {self.build_uid}'
