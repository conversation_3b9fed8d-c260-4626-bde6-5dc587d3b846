from abc import ABCMeta, abstractmethod


class IBuilder(metaclass=ABCMeta):
    """The Builder Interface"""

    @staticmethod
    @abstractmethod
    def build_branch(param):
        """Build CP params"""

    @staticmethod
    @abstractmethod
    def build_task(param):
        """Build CP params"""

    @staticmethod
    @abstractmethod
    def build_workflow_uri(param):
        """Build CP params"""

    @staticmethod
    @abstractmethod
    def build_tasks(param):
        """Build CP params"""

    @staticmethod
    @abstractmethod
    def get_result():
        """Build CP params"""


class Builder(IBuilder):
    """Concrete builder"""

    def __init__(self):
        self.rundeck_params = RundeckParams()

    def build_branch(self, branch):
        self.rundeck_params.branch = branch
        return self

    def build_task(self, task):
        self.rundeck_params.task = task
        return self

    def build_workflow_uri(self, workflow_uri):
        self.rundeck_params.workflow_uri = workflow_uri
        return self

    def build_tasks(self, tasks):
        self.rundeck_params.tasks = tasks
        return self

    def get_result(self):
        return self.rundeck_params


class RundeckParams:
    def __init__(self):
        self.branch = 'develop'
        self.task = ''
        self.workflow_uri = ''
        self.tasks = ''

    def __str__(self):
        return f'BRANCH: {self.branch}, ' \
               f'Task: {self.task}, ' \
               f'workflow_uri: {self.workflow_uri}, ' \
               f'TASKS: {self.tasks}'