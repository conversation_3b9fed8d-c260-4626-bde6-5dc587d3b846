"""
Constants to be used in Automation Tests
"""
import os

CHANNEL_PROPENSITY_TABLES = ['Evaluation_Table', 'Model_Threshold', 'Models', 'MF_QA_logs', 'Process_Definition', 'System_Config', 'xAI_Factors', 'Calibrated_Scores', 'MF_QA_config', 'Model_Config']
"""
SCD Constants
"""
SCD_TABLES = ['SCD_IN_PARAM', 'SCD_IN_PRODUCT_CONFIG', 'SCD_IN_PRODUCT_METRIC_SUMMARY', 'SCD_IN_REPORTING_LEVEL', 'SCD_IN_SALES_FACT_SUMMARY', 'SCD_IN_TARGET_VIEW_MAPPING', 'SCD_IN_USE_CASE']
VIEW_SCD_IN_USEABLE_SALES_DIM_COLUMNS = ['SCD_IN_PRODUCT_CONFIG_KEY', 'PRODUCT_NAME', 'DIM_METRIC_KEY', 'METRIC_NAME', 'DIM_FREQUENCY_KEY', 'FREQUENCY_NAME', 'SCD_IN_REPORTING_LEVEL_KEY', 'REPORTING_LEVEL_NAME', 'DIM_MARKETBASKET_KEY', 'MARKETBASKET_NAME', 'MIN_PERIOD_NUMBER', 'MAX_PERIOD_NUMBER', 'IS_USEABLE', 'REASON_TEXT']
VW_HCP_COUNT_PER_THRESHOLD_RPT = ['HCP_COUNT', 'PRODUCT_ID', 'PRODUCT_NAME', 'USE_CASE_NAME', 'PERCENTAGE', 'SCD_TRIGGER_DATE']
ANCHOR_JOB_ID = "1"
REM_JOB_ID = "2"
MSO_JOB_ID = "3"
TTE_JOB_ID = "4"

