BASE_S3_URL = "s3://aktana-bdp-siqa"
ADL_TABLES_FOLDER = "/adl/archive1/data/"
BRONZE_S3A_PATH = "s3a://aktana-bdp-siqa/adl/archive1/data/bronze/"
RPT_S3_PATH = "s3a://aktana-bdp-adltest/archive1/data/archive_rds/"
SUCCEEDED_STATUS = "succeeded"
PRIVATE_KEY_FILE = 'adl/automation/config/profiles/automationqa.pem'
AUTOMATION_USER = 'automationqa'
BRONZE_TABLES = ['account', 'accountproduct', 'approved_document', 'channel', 'dse_score', 'dse_score_final', 'emails',
                 'product', 'rep_account_assignment', 'rep_action_type', 'rep_product_authorization', 'rep_team_rep',
                 'rep', 'strategy_target', 'suggestion_final', 'suggestions', 'visits']
SILVER_TABLE = ['cri_scores', 'final_dataset', 'recordclassCsv', 'recordclasses_dse', 'recordclasses_st', 'recordclasses']

EMAILS_COLUMNS = {'emailMessageId', 'emailMessagename', 'emailMessagedescription', 'physicalMessageUID',
                     'lastPhysicalMessageUID', 'physicalMessageName', 'physicalMessageDesc', 'emailMessageTopicId',
                     'messageReaction', 'actionOrder', 'quantity', 'suggestionReferenceId', 'matchedSuggestionProduct',
                     'matchedSuggestionMessage', 'suggestionInferredAt', 'productId', 'physicalMessageUID'}
RDS_TABLES = ['interaction', 'interactiontype', 'repactiontype', 'call2_vod__c', 'interactionaccount',
              'interactionproduct', 'messagetopic', 'message', 'sent_email_vod__c', 'product',
              'productinteractiontype', 'message', 'messagetopic', 'physicalmessage', 'dserun',
              'dserunrepdate', 'dserunaccount', 'dserunrepdatesuggestion', 'dserunrepdatesuggestiondetail',
              'repaccountassignment', 'account_dse', 'account_cs', 'facility', 'rep', 'replocation',
              'repteam', 'akt_replicense_arc', 'approved_document_vod__c', 'accountproduct', 'strategytarget',
              'targetsperiod', 'targetinglevel', 'repproductauthorization']
text_validate = ['Successfully set logger', 'The data mapping is versioned in Delta at {}config/',
                 'The data loading is versioned in Delta at {}config/',
                 'The data loading has been started from S3']

MAPPING_TABLES = ['mapping', 'loading', 'dictionary']

DSE_CHANNEL_NAMES = ["VISIT_CHANNEL", "SEND_CHANNEL", "WEB_INTERACTIVE_CHANNEL"]

body_tte = {
              "learningConfigUID": "TTE_CONFIG",
              "learningConfigName": "TTE Config",
              "learningConfigDescription": "TTE config test validation",
              "modelType": "TTE",
              "productUID": "AKT_ALL_PRODUCTS",
              "channelUID": "SEND_CHANNEL",
              "learningVersionUID": ""
            }
params_tte = [
              {
                "paramName": "LE_MT_channels",
                "paramIndex": 0,
                "paramValue": "VISIT;SEND"
              },
              {
                "paramName": "LE_MT_lookBack",
                "paramIndex": 0,
                "paramValue": "365"
              },
              {
                "paramName": "LE_MT_predictAhead",
                "paramIndex": 0,
                "paramValue": "30"
              },
              {
                "paramName": "LE_MT_addPredictorsFromAccountProduct",
                "paramIndex": 0,
                "paramValue": "A_A_meetingName01_akt;QA_A_meetingName_akt;"
              },
              {
                "paramName": "LE_MT_target",
                "paramIndex": 0,
                "paramValue": "Open"
              },
              {
                "paramName": "LE_MT_AnalysisUseML",
                "paramIndex": 0,
                "paramValue": "RF"
              },
              {
                "paramName": "LE_MT_GBMLearnRate",
                "paramIndex": 0,
                "paramValue": "0.05"
              },
              {
                "paramName": "LE_MT_RFtreeDepth",
                "paramIndex": 0,
                "paramValue": "10"
              },
              {
                "paramName": "LE_MT_RFtreeNo",
                "paramIndex": 0,
                "paramValue": "100"
              },
              {
                "paramName": "LE_MT_GBMntrees",
                "paramIndex": 0,
                "paramValue": "200"
              },
              {
                "paramName": "LE_MT_segments",
                "paramIndex": 0,
                "paramValue": "QA_A_meetingName01_akt;enumConsent_akt;QA_A_meetingName_akt;"
              },
              {
                "paramName": "LE_MT_reportOnly",
                "paramIndex": 0,
                "paramValue": "false"
              }
            ]

body_rem = {
              "learningConfigUID": "AKT_REM_V0",
              "learningConfigName": "REM Config",
              "learningConfigDescription": "REM config test validation",
              "modelType": "REM",
              "productUID": "AKT_ALL_PRODUCTS",
              "channelUID": "SEND_CHANNEL",
              "learningVersionUID": ""
            }

params_rem = [
              {
                "paramName": "LE_RE_channels",
                "paramIndex": "0",
                "paramValue": "3;6"
              },
              {
                "paramName": "LE_RE_EngageWindow",
                "paramIndex": "0",
                "paramValue": "7"
              },
              {
                "paramName": "LE_RE_epsilon",
                "paramIndex": "0",
                "paramValue": ".0001"
              },
              {
                "paramName": "LE_RE_includeReactions",
                "paramIndex": "0",
                "paramValue": "Complete;Execution;Dismiss;Ignore"
              },
              {
                "paramName": "LE_RE_lookForward",
                "paramIndex": "0",
                "paramValue": "6"
              },
              {
                "paramName": "LE_RE_numberCores",
                "paramIndex": "0",
                "paramValue": "2"
              },
              {
                "paramName": "LE_RE_startHorizon",
                "paramIndex": "0",
                "paramValue": "2019-01-29"
              },
              {
                "paramName": "LE_RE_sugStartDate",
                "paramIndex": "0",
                "paramValue": "365"
              },
              {
                "paramName": "LE_RE_today",
                "paramIndex": "0",
                "paramValue": "2019-12-31"
              },
              {
                "paramName": "LE_RE_useForProbability",
                "paramIndex": "0",
                "paramValue": "B"
              }
            ]

body_anchor = {
                  "learningConfigUID": "AKT_ANCHOR_V0",
                  "learningConfigName": "ANCHOR Config",
                  "learningConfigDescription": "ANCHOR config test validation",
                  "modelType": "ANCHOR",
                  "productUID": "AKT_ALL_PRODUCTS",
                  "channelUID": "SEND_CHANNEL",
                  "learningVersionUID": ""
                }

params_anchor = [
                  {
                    "paramName": "LE_AN_numberCores",
                    "paramIndex": "0",
                    "paramValue": "4"
                  },
                  {
                    "paramName": "LE_AN_predictAhead",
                    "paramIndex": "0",
                    "paramValue": "5"
                  },
                  {
                    "paramName": "LE_AN_withinDayClusterRatio",
                    "paramIndex": "0",
                    "paramValue": "3"
                  },
                  {
                    "paramName": "LE_AN_historyWindow",
                    "paramIndex": "0",
                    "paramValue": "365"
                  },
                  {
                    "paramName": "LE_AN_predictMode",
                    "paramIndex": "0",
                    "paramValue": "Facility"
                  },
                  {
                    "paramName": "LE_AN_maxFarPercentile",
                    "paramIndex": "0",
                    "paramValue": ".75"
                  },
                  {
                    "paramName": "LE_AN_minIntsRequire",
                    "paramIndex": "0",
                    "paramValue": "40"
                  },
                  {
                    "paramName": "LE_AN_fixRandomSeed",
                    "paramIndex": "0",
                    "paramValue": "TRUE"
                  }
                ]
