""" Utils to manage files"""
import configparser
import json
import logging

import pandas as pd
from pyspark.sql.functions import trim, col

logger = logging.getLogger(__name__)


class FileUtils:
    """Class defined to manage files (Read)"""

    @staticmethod
    def read_json(template='DSEConfig.json', base_path='resources/dse_templates/'):
        """Look if a template can be loaded, if not, return empty dictionary
        :param template: key word for the template to use
        :type template: string
        :param base_path: path of the resources folder
        :type base_path: string
        """
        path = f'{base_path}{template}'
        try:
            return json.load(open(path))
        except FileNotFoundError:
            return {}

    @staticmethod
    def read_conf_file(path=None):
        """
        Return values on conf file using a temp node as workaround due
        our conf files doesn't have a section defined
        :param path: pathname of file
        :type path: string
        :param section: section to retrieve
        :type section: string
        """
        config = configparser.ConfigParser(interpolation=None, strict=False)
        with open(path) as lines:
            config.read_string(f'[temp]\n{lines.read()}')
        return config['temp']

    @staticmethod
    def read_file(path=None):
        """
        Return values on conf file using a temp node as workaround due
        our conf files doesn't have a section defined
        :param path: pathname of file
        :type path: string
        """
        with open(path) as file:
            content = file.readlines()
        return content

    @staticmethod
    def write_lines_in_file(path=None, modified_config=[""]):  # pylint: disable=W0102
        """
        write values in file according modified config list
        :param path: pathname of file
        :type path: string
        :param modified_config: list of items on list
        :type modified_config: string
        """

        with open(path, 'w') as updated_conf:
            for line in modified_config:
                updated_conf.write(line)

    @staticmethod
    def get_csv_data(filepath='resources/bronze_tables.csv'):
        """
        get csv data from specific file
        :param filepath: path of the file.
        :return:
        """
        try:
            dataframe = pd.read_csv(filepath_or_buffer=filepath,
                                    usecols=['bucket', 'table_name'])
            tuples = [tuple(x) for x in dataframe.to_numpy()]
            return tuples

        except Exception as error:
            logger.error(error)

    @staticmethod
    def is_equal_both_dataframes(df1, df2):
        """
        method to know if two dataframes are equal
        :param df1: dataframe 1.
        :param df2: dataframe 2.
        :return: true or false
        """
        df1 = FileUtils.trim_dataframe(df1)
        df2 = FileUtils.trim_dataframe(df2)
        result = df1.subtract(df2)
        print(result.count())
        return result.rdd.isEmpty()

    @staticmethod
    def trim_dataframe(df1):
        """
        method to delete blank spaces of a dataframe
        :param df1: dataframe 1.
        :return df1: data frame
        """
        columns1 = [c[0] for c in df1.dtypes if c[1].startswith('string')]
        for f in columns1:
            df1 = df1.withColumn(f, trim(col(f)))
        return df1
