from datetime import datetime as dt
from dateutil.relativedelta import relativedelta as rd


class DateUtils:
    """Class defined to manage date
    """

    @staticmethod
    def generate_index_month():
        """
        Generate index month
        return: tuple list for twelve months
        """
        now = dt.now()
        subtrack_date = lambda x: now - rd(months=x)
        new_date = lambda x, f: int(f"{f(x).year}0{f(x).month}" if f(x).month <= 9 else f"{f(x).year}{f(x).month}")
        month_list = [(i, new_date(i, subtrack_date)) for i in range(0, 12)]
        return month_list
