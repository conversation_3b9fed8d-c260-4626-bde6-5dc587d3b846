import os
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.api_request import RequestApi
customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
learning_url_base = str(environment.get("learning_api")["learning_url_base"])


class LeaningApiRequest:
    @staticmethod
    def create_draft_models(session, model_type):
        model_type = model_type.upper()
        url_draft = f"{learning_url_base}LearningConfig/modelType/{model_type}/defaultDraft"
        request_api = RequestApi.get_instance(session)
        response = request_api.post(url_draft)
        return response

    @staticmethod
    def create_draft(session, body):
        url_draft = f"{learning_url_base}LearningConfig/draft"
        request_api = RequestApi.get_instance(session)
        response = request_api.post(url_draft, json=body)
        return response

    @staticmethod
    def create_params(session, learning_config_uid, body_params):
        url_draft = f"{learning_url_base}LearningConfig/{learning_config_uid}/params"
        request_api = RequestApi.get_instance(session)
        response = request_api.post(url_draft, json=body_params)
        return response

    @staticmethod
    def build_models(session, learning_config_uid):
        request_api = RequestApi.get_instance(session)
        url_build = f"{learning_url_base}LearningConfig/{learning_config_uid}/build"
        response = request_api.put(url_build)
        return response

    @staticmethod
    def deploy_models(session, learning_config_uid):
        request_api = RequestApi.get_instance(session)
        LeaningApiRequest.build_models(session, learning_config_uid)
        url_deploy = f"{learning_url_base}LearningConfig/{learning_config_uid}/deploy"
        response = request_api.put(url_deploy)
        return response

    @staticmethod
    def publish_models(session, learning_config_uid):
        request_api = RequestApi.get_instance(session)
        LeaningApiRequest.deploy_models(session, learning_config_uid)
        url_deploy = f"{learning_url_base}LearningConfig/{learning_config_uid}/publish"
        response = request_api.put(url_deploy)
        return response

    @staticmethod
    def delete_models(session, learning_config_uid):
        request_api = RequestApi.get_instance(session)
        url_deploy = f"{learning_url_base}LearningConfig/LearningConfig/{learning_config_uid}"
        response = request_api.delete(url_deploy)
        return response
