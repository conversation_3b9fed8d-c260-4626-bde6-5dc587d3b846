"""Module to load configuration file"""
import yaml


class ConfigLoader:
    """Class to handle the Read of Environment Variables
    """

    def __init__(self):
        """Initialize
        """
        self.data = None

    def load(self, path):
        """
        Load the file specified
        :param path:
        :return:
        """
        with open(path, "r") as files:
            self.data = yaml.safe_load(files)


_INSTANCE = None


def load_config(filepath='config/config'):
    """singleton to read configurations
    """
    global _INSTANCE

    _INSTANCE = ConfigLoader()
    _INSTANCE.load(f'{filepath}.yml')

    return _INSTANCE