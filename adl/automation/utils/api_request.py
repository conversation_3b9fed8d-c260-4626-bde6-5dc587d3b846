from common.pyUtils.logger import get_module_logger
logger = get_module_logger(__name__)


class RequestApi:
    """Request Api"""
    __instance = None

    def __init__(self, api_service):
        self.api_service = api_service

    @staticmethod
    def get_instance(api_service) -> 'RequestApi':
        """This method gets an instance of the RequestApi class.
        Returns:
            RequestApi -- an instance of RequestApi class.
        """
        if RequestApi.__instance is None:
            RequestApi.__instance = RequestApi(api_service)
        return RequestApi.__instance

    def post(self, url, headers=None, params=None, data=None, json=None):
        response = self.api_service.post(url, headers=headers, params=params, data=data, json=json)
        return response

    def get(self, url, headers=None, params=None, data=None, json=None):
        response = self.api_service.get(url, headers=headers, params=params, data=data, json=json)
        return response

    def put(self, url, headers=None, params=None, data=None, json=None):
        response = self.api_service.put(url, headers=headers, params=params, data=data, json=json)
        return response

    def delete(self, url, headers=None, params=None, data=None, json=None):
        response = self.api_service.delete(url, headers=headers, params=params, data=data, json=json)
        return response
