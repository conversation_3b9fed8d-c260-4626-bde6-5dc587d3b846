""" Utils to executes job to rundeck"""
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
import logging
from adl.automation.utils.constans import ExecutionInfo

logger = logging.getLogger(__name__)


class JobUtils:
    """Class defined to run jobs """

    @staticmethod
    def run_job_to_obtain_time(builder, run_job_id, model):
        rundeck = RundeckJobExecutor()
        rundeck.run_job(builder, run_job_id, model)
        run_status = rundeck.get_status_last_execution(run_job_id)
        run_duration = rundeck.get_execution_info(run_job_id, ExecutionInfo.DURATION.value)
        return run_duration, run_status
