"""SSH Tunnel Connector"""
import logging
import sshtunnel

logger = logging.getLogger(__name__)


class SSHTunnel:
    """SSH Tunnel Class
    """

    def __init__(self, ssh_host=None, user_name=None, remote_bind_address=None, remote_port=None,
                 local_port=None, p_key=None):
        """Initialize
        """
        self.tunnel = sshtunnel.SSHTunnelForwarder(
            ssh_address_or_host=ssh_host,
            ssh_username=user_name,
            ssh_pkey=p_key,
            remote_bind_address=(remote_bind_address, remote_port),
            local_bind_address=("127.0.0.1", local_port)
        )
        sshtunnel.SSH_TIMEOUT = 18000.0
        sshtunnel.TUNNEL_TIMEOUT = 18000.0

    def start(self):
        """Start Connection"""
        try:
            self.tunnel.start()
            logger.info("Tunnel Connection Started")

        except Exception as exception:
            logger.error(exception)

    def stop(self):
        """Stop Connection"""
        try:
            self.tunnel.stop()
            logger.info("Tunnel Connection Finished")

        except Exception as exception:
            logger.error(exception)