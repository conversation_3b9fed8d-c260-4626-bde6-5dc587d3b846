import pandas as pd
import paramiko
import sqlalchemy
import os
import boto3
import logging
import s3fs
from deltalake import DeltaTable
from adl.automation.utils.ssh_tunnel import SSHTunnel
from adl.automation.utils.config_loader import load_config
from adl.automation.utils import Constants
from common.pyUtils.logger import get_module_logger

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
customer_name = str(environment.get("environment_info")["customer_name"])
environment_name = str(environment.get("environment_info")["environment"])
ACCESS_KEY = str(environment.get("adl")["access_key"])
SECRET_KEY = str(environment.get("adl")["secret_key"])
adl_base_path = str(environment.get("adl")["base_adl_url"])

logger = get_module_logger(__name__)


class DataReader:
    def __init__(self):
        pass

    @staticmethod
    def read_parquet_file_as_pandas_df(path):
        try:
            df = pd.read_parquet(path, engine='pyarrow',
                                 storage_options={
                                     "key": ACCESS_KEY,
                                     "secret": SECRET_KEY
                                 })
            return df
        except Exception as e:
            print("Failed trying to read the file: " + str(e))

    @staticmethod
    def read_delta_table(path):
        try:
            fs = s3fs.S3FileSystem(key=ACCESS_KEY, secret=SECRET_KEY)
            df = DeltaTable(path, file_system=fs).to_table().to_pandas()

            return df
        except Exception as e:
            logger.info("Failed trying to read delta table from s3 " + str(e))

    @staticmethod
    def execute_query(query, metadata=False):
        # logger.debug("Query to be executed: ", query)
        print("query to be executed {}".format(query))
        env_configuration = load_config(f'adl/automation/config/{customer}').data
        if metadata:
            remote_bind_address = "metadatards.aktana.com"
        else:
            remote_bind_address = env_configuration.get("db")["remote_address"]
        automation_key = paramiko.RSAKey.from_private_key_file(Constants.PRIVATE_KEY_FILE)
        tunnel = SSHTunnel(ssh_host=env_configuration.get("ssh")["host"],
                           user_name=Constants.AUTOMATION_USER,
                           remote_bind_address=remote_bind_address,
                           remote_port=env_configuration.get("db")["remote_port"],
                           local_port=env_configuration.get("db")["local_port"],
                           p_key=automation_key)
        tunnel.start()
        # logger.info("Was SSH connection opened?: ", tunnel.tunnel.tunnel_is_up)
        print("Was SSH connection opened?: {}".format(tunnel.tunnel.tunnel_is_up))
        if tunnel.tunnel.tunnel_is_up:
            if metadata:
                conn_string = DataReader.__get_connection_url(env_configuration, "metadata")
            else:
                conn_string = DataReader.__get_connection_url(env_configuration, "db")
            print("connection string: ", conn_string)
            try:
                engine = sqlalchemy.create_engine(conn_string)
                # logger.info("Connection to db established")
                print("Connection to db established")
                df = pd.read_sql_query(sql=query, con=engine)
                engine.dispose()

                return df
            except Exception as exception:
                # logger.error('There was a problem trying to execute query: ', exception)
                print("There was a problem trying to execute query: {}".format(exception))
                exit(1)
            finally:
                if tunnel.tunnel.tunnel_is_up:
                    tunnel.stop()

    @staticmethod
    def read_data_from_snowflake(spark, query):
        sf_options_spark_snowflake = environment.get("snowFlake")["sfOptions_spark_snowflake"]
        try:
            df = spark.read.format("net.snowflake.spark.snowflake") \
                .options(**sf_options_spark_snowflake) \
                .option("query", query).load()
            return df
        except Exception as e:
            print("The following error occurred when executing the query ", e)
            return spark.sparkContext.emptyRDD()

    @staticmethod
    def get_latest_cp_run_uid(spark):
        query = "SELECT \"Run_Uid\" FROM \"Model_Config\" ORDER BY \"Last_Execution\" desc LIMIT 1"
        df = DataReader.read_data_from_snowflake(spark, query)

        return df.first().Run_Uid

    @staticmethod
    def __get_connection_url(env_configuration, db_type):
        db_user = env_configuration.get(db_type)["user_name"]
        db_passwd = env_configuration.get(db_type)["password"]
        port = env_configuration.get(db_type)["local_port"]
        db_name = env_configuration.get(db_type)["db_name"]

        return "mysql+pymysql://{USER}:{PASSWORD}@127.0.0.1:{PORT}/{DB}".format(USER=db_user,
                                                                                PASSWORD=db_passwd,
                                                                                PORT=port,
                                                                                DB=db_name)

    @staticmethod
    def get_unique_columns_values_from_parquet_as_list(path, column_name):
        df = DataReader.read_parquet_file_as_pandas_df(path)
        df_values = df[column_name].drop_duplicates().to_list()

        return df_values

    @staticmethod
    def get_metadata_property_value(property_name):
        query = f"SELECT rptS3Location, adlS3Location, mappingLocation, loadingLocation, externalAdl, disableADL \
               FROM aktanameta.CustomerADLConfig \
               WHERE customerId = (SELECT customerId FROM Customer \
               WHERE customerName = '{customer_name}' AND environment = '{environment_name}')"
        df = DataReader.execute_query(query, metadata=True)
        property_value = df.iloc[0][property_name]
        return property_value

    @staticmethod
    def update_metadata_property_value(property_name, property_value):
        query_to_update = f"UPDATE CustomerADLConfig SET {property_name} = '{property_value}' \
            WHERE customerId = (SELECT customerId FROM Customer \
            WHERE customerName = '{customer_name}' AND environment = '{environment_name}');"
        DataReader.execute_query(query_to_update, metadata=True)

    @staticmethod
    def read_adl_parquet_file(spark, folder, table):
        df = spark.read.parquet(f'{adl_base_path}{folder}/{table}')

        return df
