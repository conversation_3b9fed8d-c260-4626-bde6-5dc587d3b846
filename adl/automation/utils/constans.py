"""Module for api constants"""
from enum import Enum


class RundeckServer(Enum):
    """Enum Created to manage Rundeck constants"""
    BASE_URL = 'https://ops-bdp-pfizerbdpus.aktana.com/'
    TOKEN = 'IgLjhtmt4aTHn6L6os6wEyDiGBp3kzNr'


class S3Constants(Enum):
    """Enum Created to manage AWS S3 constants"""
    REGION = "us-west-2"
    ACCESS_KEY = "********************"
    SECRET = "yjoCsBR4jwKyoDsyWRk4I4tggdQpwHUlz82YJ2dh"

class CPColumns(Enum):
    Calibrated_Scores = ['Model_Name', 'Process_Name', 'Date', 'Channel', 'Run_Uid', 'accountId', 'Calibrated_Probability', 'Segment', 'Timestamp', 'accountUid']
    MF_QA_config = ['Test_Phase', 'Test_ID', 'Test_Description', 'Test_Priority', 'Test_Action']

class SCDtableColumns(Enum):
    SCD_IN_PARAM = ['PARAM_DESC', 'UPDATED_TS', 'PARAM_VALUE', 'PARAM_TYPE', 'CREATED_TS', 'PARAM_NAME', 'IS_DELETED', 'SCD_IN_PARAM_KEY']


class Models(Enum):
    ANCHOR = "anchor"
    REM = "rem"
    MSO = "mso"
    TTE = "tte"


class ExecutionInfo(Enum):
    ID = "id"
    URL = "url"
    PERMALINK = "permalink"
    USER = "user"
    PROJECT = "project"
    EXECUTIONTYPE = "executionType"
    EXECUTIONSTATE = "executionState"
    STARTIME = "startTime"
    START = "start"
    ENDTIME = "endTime"
    END = "end"
    DURATION = "duration"
    JOB = "job"
    DESCRIPTION = "description"
    ARGSTRING = "argstring"
