import pytest
import os

from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader
from adl.automation.s3.s3_manager import AWSManager
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
from adl.automation.rundeck.cp_driver_params_builder import Builder

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
columns = ['recommended_channel_ai_std_akt', 'chnlPropLevel_ai_std_akt']
tables = ['Calibrated_Scores', 'Evaluation_Table', 'MF_QA_logs', 'Models', 'xAI_Factors']
bucket = str(environment.get("channel_propensity")["bucket"])
base_cp_model_key = str(environment.get("channel_propensity")["base_cp_model_key"])
cp_job_id = str(environment.get("channel_propensity")["learning_docker_job_id"])
run_uid = "TBD"


# TC-13845 : Validate that all the probabilities populate across all the accounts and channels on 'Calibrated_Scores'
@pytest.mark.tc13845
@pytest.mark.channel_propensity
def test_calibrate_scores_probabilities(spark):
    query = f"SELECT DISTINCT Calibrated_Probability FROM Calibrated_Scores \
    WHERE Run_Uid ='{run_uid}' ORDER BY Calibrated_Probability DESC;"
    df = DataReader.read_data_from_snowflake(spark, query)

    assert not df.rdd.isEmpty()


# TC-13847 : Validate the Model output written on S3 is in PMML format
@pytest.mark.tc13847
@pytest.mark.channel_propensity
def test_model_output():
    client = AWSManager.get_instance('s3')
    has_data = client.s3_folder_contains_files(bucket, base_cp_model_key, "pmml")

    assert has_data


# TC-13857 : Validate the #of accounts, channels between ADL FS Vs Snowflake output
@pytest.mark.tc13857
@pytest.mark.channel_propensity
def test_accounts_between_s3_and_fs(spark):
    df = spark.read.format('delta').load("")
    df.createTempView("hcpFeatureStore")
    result = spark.sql("SELECT DISTINCT(accountId) as total FROM hcpFeatureStore")

    query = f"SELECT DISTINCT accountId FROM Calibrated_Scores WHERE Run_Uid='{run_uid}' ORDER BY accountId desc;"
    sf_df = DataReader.read_data_from_snowflake(spark, query)

    assert result.first().total == sf_df.count()


# TC-13844 : Validate the 2nd job execution time do not vary much compare to 1st job execution timing
@pytest.mark.tc13844
@pytest.mark.channel_propensity
def test_cp_job_execution_timing(spark):
    rundeck = RundeckJobExecutor()
    previous_time = rundeck.get_execution_time(cp_job_id)
    builder = Builder() \
        .build_script_to_execute('learning/modelFactory/channel_propensity/cp_driver.sh') \
        .build_script_parameters('RUN_CP_ENGINE --customer $CUSTOMER --env $ENVIRONMENT --ecosystem $ECOSYSTEM') \
        .build_environment_variables('-e CUSTOMER=$CUSTOMER -e REGION=$REGION -e ENVIRONMENT=$ENVIRONMENT -e SCHEMA_PREFIX=impact') \
        .build_ecosystem('saasdev') \
        .get_result()

    rundeck.run_job(builder, cp_job_id, "CP")

    assert rundeck.get_status_last_execution(cp_job_id) == "succeeded"
    actual_time = rundeck.get_execution_time(cp_job_id)

    assert actual_time <= previous_time


# TC-13843 : Validate that CP 2nd/incremental job runs successful and populate output into 'Calibrated_Scores'
@pytest.mark.tc13843
@pytest.mark.channel_propensity
@pytest.mark.parametrize("table_name", tables)
def test_cp_incremental_output(spark, table_name):
    run_uid_cp = DataReader.get_latest_cp_run_uid(spark)
    query = f"SELECT * FROM \"{table_name}\" where \"Run_Uid\"='{run_uid_cp}';"
    df = DataReader.read_data_from_snowflake(spark, query)

    assert df.rdd.isEmpty()