import pytest
import os

from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
from adl.automation.rundeck.cp_driver_params_builder import Builder


customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
columns = ['recommended_channel_ai_std_akt', 'chnlPropLevel_ai_std_akt']
cp_job_id = str(environment.get("channel_propensity")["learning_docker_job_id"])

# TC-13854 : Verify all information in calibrates_scores from snowflake are in AccountProductLabelValue table fro
@pytest.mark.tc13854
@pytest.mark.channel_propensity
def test_calibrate_scores(spark):
    query = "SELECT DISTINCT(accountUID) FROM CHANNEL_PROPENSITY_OUTPUT_V3"
    df = DataReader.read_data_from_snowflake(spark, query)

    rds_query = "SELECT distinct(lv.accountId) FROM AccountProductLabelValue lv join LabelType lt \
    on lv.labelTypeId = lt.labelTypeId WHERE lt.labelCategory = 'AKTANA-AI-Channel-Propensity';"
    pd_df = DataReader.execute_query(rds_query)

    assert df.count() == len(pd_df)


# TC-13848 : Verify the recommended_channel_ai_std_akt and chnlPropLevel_ai_std_akt columns are populated in RDS
@pytest.mark.parametrize("column_name", columns)
@pytest.mark.tc13854
@pytest.mark.channel_propensity
def test_columns_values_rds(spark, column_name):
    rds_query = f"SELECT distinct({column_name}) FROM AccountProductLabelValue al join LabelType lt \
    on al.labelTypeId = lt.labelTypeId where lt.labelCategory = 'AKTANA-AI-Channel-Propensity';"
    pd_df = DataReader.execute_query(rds_query)

    values = list(pd_df[column_name])

    assert 'null' not in values


# TC-13849 : Verify the recommended_channel_ai_std_akt and chnlPropLevel_ai_std_akt columns in RDS matches in Snowflake
@pytest.mark.parametrize("column_name", columns)
@pytest.mark.tc13854
@pytest.mark.channel_propensity
def test_column_values_between_rds_snowflake(spark, column_name):
    query = f"SELECT DISTINCT({column_name}) FROM CHANNEL_PROPENSITY_OUTPUT_V3"
    df = DataReader.read_data_from_snowflake(spark, query)
    df = df.toPandas()

    rds_query = f"SELECT distinct({column_name}) FROM AccountProductLabelValue al join LabelType lt \
    on al.labelTypeId = lt.labelTypeId where lt.labelCategory = 'AKTANA-AI-Channel-Propensity';"
    pd_df = DataReader.execute_query(rds_query)

    result = df.compare(pd_df)

    assert result.empty


# TC-12605 : Validate the CP Post Processor Run deck runs fine and populates the output into DSE table
@pytest.mark.tc12605
@pytest.mark.channel_propensity
def test_cp_post_processor_job(spark):
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_script_to_execute('python3 learning/channelPropensity/cp_post_processor_dse.py') \
        .build_ecosystem('saasdev') \
        .get_result()

    rundeck.run_job(builder, cp_job_id, "CP")
    assert rundeck.get_status_last_execution(cp_job_id) == "succeeded"