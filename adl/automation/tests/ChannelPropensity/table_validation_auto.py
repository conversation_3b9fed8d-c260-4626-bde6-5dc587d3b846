import pytest
import os

from adl.automation.utils.constans import CPColumns
from adl.automation.utils import Constants
from adl.automation.rundeck.adl_job_params_builder import Builder
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader
from adl.automation import constants


bronze_folder_names = Constants.BRONZE_TABLES
silver_folder_names = Constants.SILVER_TABLE

customer = os.environ.get("ENV_NAME")
adl_branch = os.environ.get("ADL_BRANCH")
feature = os.environ.get("FEATURE_STORE")
environment = load_config(f'adl/automation/config/{customer}').data

ACCESS_KEY = str(environment.get("adl")["access_key"])
SECRET_KEY = str(environment.get("adl")["secret_key"])
AWS_REGION = str(environment.get("adl")["region"])
BUCKET = str(environment.get("adl")["bucket"])
base_key = str(environment.get("adl")["base_adl_key"])
deploy_job_id = str(environment.get("rundeck")["deploy_job_id"])
run_job_id = str(environment.get("rundeck")["run_job_id"])
testdata = ['Calibrated_Scores',
            'MF_QA_config']


# TC-12524:Validate the Calibrated_Scores table columns with right data types created and populates the data
@pytest.mark.tc12524
@pytest.mark.adl_job
def test_validate_tables_exists(spark):
    actual_t = constants.CHANNEL_PROPENSITY_TABLES
    query = "SELECT TABLE_NAME FROM information_schema.tables WHERE TABLE_SCHEMA = 'CHANNEL_PROPENSITY'"
    df = DataReader.read_data_from_snowflake(spark, query)
    df.show(truncate=False)

    expected_tables = df.select("TABLE_NAME").rdd.flatMap(lambda x: x).collect()

    result = all(item in expected_tables for item in actual_t )

    assert result

# TC-12454:Validate the Model_Config table columns with right data types created and populates the data
@pytest.mark.tc12454
@pytest.mark.adl_job
@pytest.mark.parametrize("table", testdata)
def test_validate_columns_for_tables(spark, table):
    query = "SELECT COLUMN_NAME, DATA_TYPE FROM information_schema.columns WHERE TABLE_SCHEMA = 'CHANNEL_PROPENSITY' AND TABLE_NAME = '{}'".format(table)
    df = DataReader.read_data_from_snowflake(spark, query)
    df.show(truncate=False)

    actual_columns = df.select("COLUMN_NAME").rdd.flatMap(lambda x: x).collect()

    expected = CPColumns[f'{table}'].value

    print(expected)
    print(actual_columns)

    result = all(item in actual_columns for item in expected )

    assert result