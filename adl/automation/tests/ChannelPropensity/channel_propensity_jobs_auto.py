import pytest
import os
import delayed_assert

from adl.automation.s3.s3_manager import <PERSON><PERSON><PERSON>anager
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
from adl.automation.utils import Constants
from adl.automation.rundeck.channel_propensity_params_builder import Builder
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader


bronze_folder_names = Constants.BRONZE_TABLES
silver_folder_names = Constants.SILVER_TABLE

customer = os.environ.get("ENV_NAME")
adl_branch = os.environ.get("ADL_BRANCH")
feature = os.environ.get("FEATURE_STORE")
environment = load_config(f'adl/automation/config/{customer}').data

ACCESS_KEY = str(environment.get("adl")["access_key"])
SECRET_KEY = str(environment.get("adl")["secret_key"])
AWS_REGION = str(environment.get("adl")["region"])
BUCKET = str(environment.get("adl")["bucket"])
base_key = str(environment.get("adl")["base_adl_key"])
channel_propensity_job_id = str(environment.get("rundeck")["deploy_job_id"])
run_job_id = str(environment.get("rundeck")["run_job_id"])


# TC-12585:Validate the Knime Run deck on CHANNEL_PROPENSITY runs successful, with all Task options
# TC-12604:Validate that after run 'execute_workflow' job, model output populates into CP snowflake tables
@pytest.mark.tc12585
def test_channel_propensity_rundeck_job(spark):
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_branch(adl_branch) \
        .build_task('execute_workflow') \
        .build_workflow_uri('/Examples/REST/Predict%20Results%20Using%20REST%20API') \
        .build_tasks('2.Knime-workflow') \
        .get_result()
    rundeck.run_job(builder, channel_propensity_job_id)
    status = rundeck.get_status_last_execution(channel_propensity_job_id)

    assert status == Constants.SUCCEEDED_STATUS

    query_1 = "SELECT Run_Uid, count(1), count(distinct accountId), count(distinct Timestamp), max(Timestamp) \
    FROM Calibrated_Scores GROUP BY Run_Uid ORDER BY max(Timestamp) desc;"
    df = DataReader.read_data_from_snowflake(spark, query_1)
    delayed_assert.expect(df.count() > 0)

    query_2 = "select Run_Uid, count(1), count(distinct accountId), count(distinct Factor_Name), \
    count(distinct Timestamp), max(Timestamp) from xAI_Factors group by Run_Uid order by max(Timestamp) desc;"
    df = DataReader.read_data_from_snowflake(spark, query_2)
    delayed_assert.expect(df.count() > 0)

    query_3 = "select Run_Uid, count(1), count(distinct Model_Name), count(distinct Feature_Period), \
    count(distinct Channel), count(distinct Timestamp), max(Timestamp) \
    from Models group by Run_Uid order by max(Timestamp) desc;"
    df = DataReader.read_data_from_snowflake(spark, query_3)
    delayed_assert.expect(df.count() > 0)

    query_4 = "select Run_Uid, count(1), count(distinct Model_Initialisation), count(distinct Status), \
    count(distinct Feature_Period), count(distinct Date), count(distinct Last_Execution), max(Last_Execution) \
    from Model_Config group by Run_Uid order by max(Last_Execution) desc;"
    df = DataReader.read_data_from_snowflake(spark, query_4)
    delayed_assert.expect(df.count() > 0)

    query_5 = "select Run_Uid, count(1), count(distinct Update_Date), count(distinct Test_Phase), \
    count(distinct Test_ID), count(distinct Test_Result), count(distinct Update_Date), max(Update_Date) \
    from MF_QA_logs group by Run_Uid order by max(Update_Date) desc;  select Run_Uid, count(1), \
    count(distinct Model_Name), count(distinct Channel), count(distinct Timestamp), max(Timestamp) \
    from Evaluation_Table group by Run_Uid order by max(Timestamp) desc;"
    df = DataReader.read_data_from_snowflake(spark, query_5)
    delayed_assert.expect(df.count() > 0)

    delayed_assert.assert_expectations()
