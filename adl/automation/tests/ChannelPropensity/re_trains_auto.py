import pytest
import os
from datetime import datetime, timedelta
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
from adl.automation.rundeck.cp_driver_params_builder import Builder

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
bucket = str(environment.get("channel_propensity")["bucket"])
base_cp_model_key = str(environment.get("channel_propensity")["base_cp_model_key"])
cp_job_id = str(environment.get("channel_propensity")["learning_docker_job_id"])


# TC-13846 : db migration MF_QA_config Test_ID= '0' update and DATABASECHANGELOG entry
@pytest.mark.tc13846
@pytest.mark.channel_propensity
def test_mf_qa_config_test_id(spark):
    query = "SELECT * FROM \"DEVNOVARTISBR_DW_PROD\".\"CHANNEL_PROPENSITY\".\"MF_QA_config\" WHERE \"Test_ID\" = 0"
    df = DataReader.read_data_from_snowflake(spark, query)

    assert not df.rdd.isEmpty()


# TC-13858 : Validate that model re-trains, when model does not exists
@pytest.mark.tc13846
@pytest.mark.channel_propensity
def test_mf_qa_config_test_id(spark):
    delete_query = "DELETE FROM \"DEVNOVARTISBR_DW_PROD\".\"CHANNEL_PROPENSITY\".\"Model_Config\""
    DataReader.read_data_from_snowflake(spark, delete_query)
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_script_to_execute('learning/modelFactory/channel_propensity/cp_driver.sh') \
        .build_script_parameters('RUN_CP_ENGINE --customer $CUSTOMER --env $ENVIRONMENT --ecosystem $ECOSYSTEM') \
        .build_environment_variables(
        '-e CUSTOMER=$CUSTOMER -e REGION=$REGION -e ENVIRONMENT=$ENVIRONMENT -e SCHEMA_PREFIX=impact') \
        .build_ecosystem('saasdev') \
        .get_result()

    rundeck.run_job(builder, cp_job_id, "CP")

    assert rundeck.get_status_last_execution(cp_job_id) == "succeeded"

    query = "DELETE FROM \"DEVNOVARTISBR_DW_PROD\".\"CHANNEL_PROPENSITY\".\"Model_Config\""
    df = DataReader.read_data_from_snowflake(spark, query)

    assert not df.rdd.isEmpty()


# TC-13859 : Validate that model re-trains, when any new channel added or removed
@pytest.mark.tc13846
@pytest.mark.channel_propensity
def test_mf_qa_config_test_id(spark):
    query = "SELECT * FROM \"DEVNOVARTISBR_DW_PROD\".\"CHANNEL_PROPENSITY\".\"Models\""
    previos_df = DataReader.read_data_from_snowflake(spark, query)
    update_query = "UPDATE \"DEVNOVARTISBR_DW_PROD\".\"CHANNEL_PROPENSITY\".\"Model_Config\" \
    SET \"Model_Package_Combination\" = '{\'Train_send_Flag\': 0, \'Train_visit_Flag\': 1, \'Train_virtual_visit_Flag\': 0, \'Train_phone_Flag\': 0}' \
    WHERE \"Run_Uid\" = '129233f9-59d4-4bd5-9f6d-659be319c20c';"
    DataReader.read_data_from_snowflake(spark, update_query)
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_script_to_execute('learning/modelFactory/channel_propensity/cp_driver.sh') \
        .build_script_parameters('RUN_CP_ENGINE --customer $CUSTOMER --env $ENVIRONMENT --ecosystem $ECOSYSTEM') \
        .build_environment_variables(
        '-e CUSTOMER=$CUSTOMER -e REGION=$REGION -e ENVIRONMENT=$ENVIRONMENT -e SCHEMA_PREFIX=impact') \
        .build_ecosystem('saasdev') \
        .get_result()

    rundeck.run_job(builder, cp_job_id, "CP")
    assert rundeck.get_status_last_execution(cp_job_id) == "succeeded"

    actual_df = DataReader.read_data_from_snowflake(spark, query)
    assert previos_df.count() < actual_df.count()

    cs_query = "SELECT * FROM \"Calibrated_Scores\" WHERE \"Run_Uid\" = '1b2b3af6-717d-4032-93a6-4971e9c78461'"
    df = DataReader.read_data_from_snowflake(spark, cs_query)

    assert not df.rdd.isEmpty()


# TC-13860 : Validate that model re-trains, If an existing model is older than a quarter (i.e. 90 days)
@pytest.mark.tc13860
@pytest.mark.channel_propensity
def test_re_trains_older_than_a_quarter(spark):
    cp_run_uid = DataReader.get_latest_cp_run_uid(spark)
    query = "SELECT * FROM \"Model_Config\" WHERE \"Deployment\" = 'Current_Model';"
    previous_df = DataReader.read_data_from_snowflake(spark, query)
    date = previous_df.first().Date
    date_time_object = datetime.strptime(date, '%Y-%m-%d')
    d = date_time_object - timedelta(days=91)
    d = d.date()
    update_query = f"UPDATE \"Model_Config\" SET \"Date\" = '{d}' WHERE \"Deployment\" = 'Current_Model';"
    DataReader.read_data_from_snowflake(spark, update_query)
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_script_to_execute('learning/modelFactory/channel_propensity/cp_driver.sh') \
        .build_script_parameters('RUN_CP_ENGINE --customer $CUSTOMER --env $ENVIRONMENT --ecosystem $ECOSYSTEM') \
        .build_environment_variables(
        '-e CUSTOMER=$CUSTOMER -e REGION=$REGION -e ENVIRONMENT=$ENVIRONMENT -e SCHEMA_PREFIX=impact') \
        .build_ecosystem('saasdev') \
        .get_result()

    rundeck.run_job(builder, cp_job_id, "CP")
    assert rundeck.get_status_last_execution(cp_job_id) == "succeeded"

    actual_df = DataReader.read_data_from_snowflake(spark, query)
    assert previous_df.count() < actual_df.count()


# TC-13861 : Validate that model re-trains, when AUC is less than threshold for the current data
@pytest.mark.tc13861
@pytest.mark.channel_propensity
def test_re_trains_auc_is_less_than_threshold(spark):
    cp_run_uid = DataReader.get_latest_cp_run_uid(spark)
    query = "SELECT * FROM \"System_Config\""
    config_df = DataReader.read_data_from_snowflake(spark, query)
    threshold = config_df.first().Threshold
    update_query = f"UPDATE \"Evaluation_Table\" SET AUC = {threshold} WHERE \"Run_Uid\" = '{cp_run_uid}'"
    DataReader.read_data_from_snowflake(spark, update_query)
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_script_to_execute('learning/modelFactory/channel_propensity/cp_driver.sh') \
        .build_script_parameters('RUN_CP_ENGINE --customer $CUSTOMER --env $ENVIRONMENT --ecosystem $ECOSYSTEM') \
        .build_environment_variables(
        '-e CUSTOMER=$CUSTOMER -e REGION=$REGION -e ENVIRONMENT=$ENVIRONMENT -e SCHEMA_PREFIX=impact') \
        .build_ecosystem('saasdev') \
        .get_result()

    rundeck.run_job(builder, cp_job_id, "CP")
    assert rundeck.get_status_last_execution(cp_job_id) == "succeeded"

    current_run_uid = DataReader.get_latest_cp_run_uid(spark)
    assert cp_run_uid != current_run_uid, "no new model was created"

    df = DataReader.read_data_from_snowflake(spark, f"SELECT * FROM \"Calibrated_Scores\" WHERE \"Run_Uid\" = '{current_run_uid}'")
    assert df.rdd.isEmpty()