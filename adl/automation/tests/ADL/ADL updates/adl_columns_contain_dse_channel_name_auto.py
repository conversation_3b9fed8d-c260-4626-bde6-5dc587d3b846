import pytest
import os
import logging
from pandasql import sqldf
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader
from adl.automation.utils import Constants

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
base_adl_url = str(environment.get("adl")["base_adl_url"])
table_names = [('suggestions_final', "channel"), ('suggestions', "suggestedChannelName"), ('visits', "channel")]
logger = logging.getLogger(__name__)


# TC-13084 : Validate that bronze tables are using standard DSE channel names
@pytest.mark.parametrize("table, column", table_names)
@pytest.mark.tc13084
def test_tc_validate_updated_table(spark, table, column):
    expected_values = Constants.DSE_CHANNEL_NAMES
    table_path = f"{base_adl_url}bronze/{table}/"
    logger.info(table_path)
    df = DataReader.read_parquet_file_as_pandas_df(table_path)
    query = f"SELECT DISTINCT({column}) FROM df"
    logger.info(query)
    result = sqldf(query)
    actual_values = list(result[f"{column}"])
    logger.info(f"Expected Values: {expected_values}")
    logger.info(f"Actual Values: {actual_values}")

    assert all(item in expected_values for item in actual_values),\
        "some channels are not present into table"
