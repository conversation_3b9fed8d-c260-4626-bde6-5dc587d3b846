import pytest
import os

from adl.automation.s3.s3_manager import AWSManager
from common.pyUtils.logger import get_module_logger
from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
base_adl_url = str(environment.get("adl")["base_adl_url"])
logger = get_module_logger(__name__)
BUCKET = str(environment.get("adl")["bucket"])


# TC-10479 : Validate that suggestions_final table was updated and new rows were added
# TC-10480 : Validate that recordclasses table was updated and new rows were added
@pytest.mark.tc10479
@pytest.mark.tc10480
@pytest.mark.parametrize("table", [("bronze", "suggestions_final"),
                                   ("silver", "recordclasses")])
def test_validate(spark, table):
    s3_client = AWSManager.get_instance("s3")

    current_table = f"{base_adl_url}{table[0]}/{table[1]}"
    base_adl_key_table = str(environment.get("adl")["base_adl_key"]).replace("{}/", f"{table[0]}_")
    backup_key = s3_client.obtain_last_backup(BUCKET, base_adl_key_table, f"/{table[1]}")
    backup_table = f"{base_adl_url}{backup_key}"

    query = "SELECT COUNT(*) current_count FROM parquet.`{}`".format(current_table)
    df = spark.sql(query)
    current_count = df.first().current_count
    logger.debug(f"{table[0]} count: {current_count}")

    query = "SELECT COUNT(*) backup_count FROM parquet.`{}`".format(backup_table)
    df = spark.sql(query)
    backup_count = df.first().backup_count
    logger.debug(f"{backup_key} count: {backup_count}")

    assert current_count >= backup_count
