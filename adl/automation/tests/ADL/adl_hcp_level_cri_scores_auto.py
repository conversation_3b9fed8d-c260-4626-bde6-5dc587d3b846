import pytest

adl_siqa_url = "s3a://aktana-bdp-siqa/adl/archive8/data/"
hcp_feature_store_table = f"{adl_siqa_url}silver/hcpFeatureStore"
final_dataset_table = f"{adl_siqa_url}silver/final_dataset"
account_table = f"{adl_siqa_url}/bronze/account"
criscores_table = f"{adl_siqa_url}/silver/cri_scores"

# TC-12078: Validate that new numberHcpFacility column was added
# and it correspond to # of accounts associated with the same facilityId
@pytest.mark.tc12078
def test_validate_that_new_numberhcpfacility_column_was_added(spark):

    query = 'WITH result2 AS (WITH result1 AS ( SELECT t1.accountId, t1.facilityId, t2.max_interactionStartDateLocal \
          FROM parquet.`{final_data_set}` t1 INNER JOIN( SELECT accountId, MAX(interactionStartDateLocal) AS \
          max_interactionStartDateLocal FROM parquet.`{final_data_set}` GROUP BY accountId) t2 ON \
          t1.accountId=t2.accountId WHERE t1.facilityId=(SELECT FIRST(facilityId) FROM parquet.`{final_data_set}` \
          LIMIT 1) AND interactionStartDateLocal>"2019-01-01" ) SELECT COUNT(DISTINCT(accountId)) AS QA_count, \
          facilityId AS fd_facility FROM result1 GROUP BY facilityId) SELECT a.QA_count AS c1, b.numberHcpFacility AS c2\
          FROM result2 a JOIN parquet.`{}` b ON a.fd_facility=b.facilityId \
          '.format(hcp_feature_store_table, final_data_set=final_dataset_table)
    df = spark.sql(query)
    df.show()
    assert df.first().c1 == df.first().c2


# TC-12083: Validate that criCadenceScore column was added to hcpFeatureStore table
#           and it correspond to the max aggregation accross reps for the month
# TC-12084: Validate that criChannelScore column was added to hcpFeatureStore table
#           and it correspond to the max aggregation accross reps for the month
# TC-12086: Validate that criOpenScore column was added to hcpFeatureStore table
#           and it correspond to the max aggregation accross reps for the month
@pytest.mark.tc12083
@pytest.mark.tc12084
@pytest.mark.tc12086
@pytest.mark.parametrize("columns", [("cadenceScore", "criCadenceScore"), ("channelScore", "criChannelScore"),
                                     ("emailOpenScore", "criOpenScore")])
def test_validte_that_columns_were_added_to_hcpfeaturestore_table(spark, columns):
    accountId = spark.sql(f"SELECT first(accountId) a FROM parquet.`{hcp_feature_store_table}` LIMIT 1").first().a
    print(accountId)

    query1 = 'WITH result1 AS ( SELECT FIRST(interactionRepAccount), MAX({}) AS column, \
            interactionYearMonth FROM parquet.`{}` WHERE interactionRepAccount LIKE "%{accountId}%" AND \
            interactionYearMonth=202010 GROUP BY interactionYearMonth LIMIT 15 ) SELECT a.accountId, a.productId, \
            a.{},a.yearMonth,b.column,b.interactionYearMonth FROM parquet.`{hcp_feature_store_table}` a JOIN result1 b \
            ON a.yearMonth=b.interactionYearMonth WHERE a.accountId={accountId} \
            '.format(columns[0], criscores_table, columns[1],
                     accountId=accountId, hcp_feature_store_table=hcp_feature_store_table)
    df1 = spark.sql(query1)
    assert df1.rdd.isEmpty()

    query2 = 'WITH result2 AS ( WITH result1 AS ( select first(interactionRepAccount) AS interactionRepAccount,	\
             MAX({}) AS column, interactionYearMonth, SUBSTRING(first(interactionRepAccount), 6, 10) \
             AS account FROM parquet.`{}` GROUP BY interactionYearMonth LIMIT 15) SELECT a.accountId, a.productId, \
             a.{}, a.yearMonth, b.interactionRepAccount, b.column, b.interactionYearMonth, b.account \
             FROM parquet.`{}` a JOIN result1 b ON a.yearMonth=b.interactionYearMonth WHERE a.accountId=b.account) \
             SELECT COUNT(*) AS c from result2 WHERE {}!=column \
             '.format(columns[0], criscores_table, columns[1], hcp_feature_store_table, columns[1])
    df2 = spark.sql(query2)
    assert df2.first().c == 0


# TC-12078: Validate that criMaxAvgIndex column was added to hcpFeatureStore table
#           and it correspond to the average cri index for all scores and not sum
@pytest.mark.tc12085
def test_validate_that_crimaxavgindex_column_was_added_to_hcpfeaturestore_table(spark):

    query = 'WITH result2 AS (WITH result1 aASs ( SELECT accountId, productId, yearMonth, criCadenceScore, \
            criChannelScore,criOpenScore, criSuggestionEmailScore, criSuggestionVisitScore, criTargetAchievementScore, \
            criTenureScore, criVisitScore, criMaxAvgIndex, (COALESCE(criCadenceScore, 0)+COALESCE(criChannelScore, 0)+\
            COALESCE(criOpenScore, 0)+COALESCE(criSuggestionEmailScore, 0)+COALESCE(criSuggestionVisitScore, 0)+\
            COALESCE(criTargetAchievementScore, 0)+COALESCE(criTenureScore, 0)+COALESCE(criVisitScore, 0)) AS SUM, \
            CASE WHEN criCadenceScore IS NOT NULL THEN 1 ELSE 0 END + CASE WHEN criChannelScore IS NOT NULL THEN 1 \
            ELSE 0 END + CASE WHEN criOpenScore IS NOT NULL THEN 1 ELSE 0 END + CASE WHEN criSuggestionEmailScore \
            IS NOT NULL THEN 1 ELSE 0 END + CASE WHEN criSuggestionVisitScore IS NOT NULL THEN 1 ELSE 0 END + CASE \
            WHEN criTargetAchievementScore IS NOT NULL THEN 1 ELSE 0 END + CASE WHEN criTenureScore IS NOT NULL THEN \
            1 ELSE 0 END + CASE WHEN criVisitScore IS NOT NULL THEN 1 ELSE 0 END AS countNotNulls FROM parquet.`{}` \
            LIMIT 20) SELECT *,SUM/countNotNulls AS calculatedAverage FROM result1) SELECT COUNT(*) c FROM result2 \
            WHERE criMaxAvgIndex!=calculatedAverage'.format(hcp_feature_store_table)
    df = spark.sql(query)
    df.show()
    assert df.first() == 0
