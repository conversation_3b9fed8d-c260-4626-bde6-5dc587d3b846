import pytest
import os

from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_rpt_url = str(environment.get("adl")["base_rpt_url"])
base_adl_url = str(environment.get("adl")["base_adl_url"])

rpt_suggestions = f"{base_rpt_url}rpt_suggestion_delivered_stg/"
adl_suggestions = f"{base_adl_url}bronze/suggestions"


# TC-10221:Validate that suggestion count is equal in bronze folder and RPT folder
@pytest.mark.tc10221
def test_tc_10221(spark):

    query_adl = "SELECT count(distinct(suggestionReferenceId)) as suggestions_count FROM parquet.`{}`"
    df_adl = spark.sql(query_adl.format(adl_suggestions))
    df_adl = df_adl.first().suggestions_count

    query_rpt = "SELECT count(distinct(suggestionReferenceId)) as suggestion_count FROM parquet.`{}` "
    df_rpt = spark.sql(query_rpt.format(rpt_suggestions))
    df_rpt = df_rpt.first().suggestion_count

    assert df_adl == df_rpt
