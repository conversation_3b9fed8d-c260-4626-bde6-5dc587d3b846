import pytest
import os
import yaml


from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

with open("adl/automation/resources/queries_hcp_feature.yml", "r") as file:
    queries = yaml.safe_load(file)

base_adl_url = str(environment.get("adl")["base_adl_url"])

hcpFeatureStore = f"{base_adl_url}silver/hcpFeatureStore"
final_dataset = f"{base_adl_url}silver/final_dataset"
account = f"{base_adl_url}bronze/account"


# TC-12061:Validate that new yearMonth column was added, and it corresponds to interactionYearMonth from final_da
# TC-12064:Validate that new accountUid column was added, and it corresponds to externalId from bronze/account ta
# TC-12067:Validate that new facilityId column was added, and it corresponds to facilityId from bronze/account t
# TC-12069:Validate that new hcpGender column was added, and it corresponds to gender from bronze/account table
# TC-12070:Validate that new hcpSpec column was added, and it corresponds to Spec from bronze/account table
# TC-12071:Validate that new hcpSpec2 column was added, and it corresponds to Spec2 from bronze/account table
@pytest.mark.parametrize("select_table, query", [(final_dataset, queries["query_tc_12061"]),
                                                 (account, queries["query_tc_12064"]),
                                                 (account, queries["query_tc_12067"]),
                                                 (account, queries["query_tc_12069"]),
                                                 (account, queries["query_tc_12070"]),
                                                 (account, queries["query_tc_12071"])])
@pytest.mark.tc12061
@pytest.mark.tc12064
@pytest.mark.tc12067
@pytest.mark.tc12069
@pytest.mark.tc12070
@pytest.mark.tc12071
def test_validate_new_column_was_added(spark, select_table, query):
    df = spark.sql(query.format(hcpFeatureStore, select_table))
    assert df.rdd.isEmpty()
