import pytest
import os

from adl.automation.s3.s3_manager import <PERSON><PERSON><PERSON>anager
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
from adl.automation.utils import Constants
from adl.automation.rundeck.adl_job_params_builder import Builder
from adl.automation.utils.config_loader import load_config


bronze_folder_names = Constants.BRONZE_TABLES
silver_folder_names = Constants.SILVER_TABLE

customer = os.environ.get("ENV_NAME")
adl_branch = os.environ.get("ADL_BRANCH")
feature = os.environ.get("FEATURE_STORE")
environment = load_config(f'adl/automation/config/{customer}').data

ACCESS_KEY = str(environment.get("adl")["access_key"])
SECRET_KEY = str(environment.get("adl")["secret_key"])
AWS_REGION = str(environment.get("adl")["region"])
BUCKET = str(environment.get("adl")["bucket"])
deploy_job_id = str(environment.get("rundeck")["deploy_job_id"])
glue_job_name = str(environment.get("aws")["glue_job_name"])


# TC-9603:Validate that Glue job runs fine for NovartisUS
@pytest.mark.tc9603
@pytest.mark.adl_job
def test_run_glue_job():
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_adl_branch(adl_branch)\
        .build_feature_store(feature) \
        .get_result()
    rundeck.run_job(builder, deploy_job_id)
    deploy_status = rundeck.get_status_last_execution(deploy_job_id)

    assert deploy_status == Constants.SUCCEEDED_STATUS

    client = AWSManager.get_instance(service='glue')
    client.glue_run_job(glue_job_name)
    status = client.glue_get_job_last_execution_status(glue_job_name)

    assert status == Constants.SUCCEEDED_STATUS.upper()