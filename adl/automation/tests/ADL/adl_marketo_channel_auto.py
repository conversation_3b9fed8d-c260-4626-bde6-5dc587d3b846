import pytest
import os
from adl.automation.utils.config_loader import load_config


customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
base_adl_url = str(environment.get("adl")["base_adl_url"])


# TC-12840:Validate that ‘recordclasses’ silver table has the newly added Marketo channel suggestion data
# TC-12841:Validate that ‘final_dataset’ silver table has the newly added Marketo channel suggestion data
@pytest.mark.tc12840
@pytest.mark.tc12841
@pytest.mark.parametrize("table", ["recordclasses", "final_dataset"])
def test_validate_that_recordclasses_silver_table_has_the_newly_added_marketo_channel(spark, table):
    test_table = f"{base_adl_url}silver/{table}"

    query = "SELECT COUNT(suggestionDetailRepActionName) AS quantity FROM parquet.`{}` \
            WHERE suggestionDetailRepActionName='MARKETO'".format(test_table)
    df = spark.sql(query)

    assert df.first().quantity > 0
