import pytest
import os
from adl.automation.utils.config_loader import load_config
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

adl_job_id = str(environment.get("rundeck")["run_job_id"])


# TC-13550 : ADL shouldn't read 1000-01-01 data when loading archive tables
@pytest.mark.tc13550
def test_dummy_data_is_not_present():
    rundeck = RundeckJobExecutor()
    response = rundeck.validate_output_contains_log_entry(adl_job_id, '1000-01-01')

    assert not response
