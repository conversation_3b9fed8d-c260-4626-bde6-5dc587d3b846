import pytest
import os

from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])
queries = load_config('adl/automation/resources/queries_cri_score').data

cri_scores = f"{base_adl_url}silver/cri_scores"
hcpFeatureStore = f"{base_adl_url}silver/hcpFeatureStore"
product = f"{base_adl_url}bronze/product"

result1_tc_12087 = str(queries.get("query")["result1_tc_12087"])
result2_tc_12087 = str(queries.get("query")["result2_tc_12087"])
result1_tc_12088 = str(queries.get("query")["result1_tc_12088"])
result2_tc_12088 = str(queries.get("query")["result2_tc_12088"])
result1_tc_12089 = str(queries.get("query")["result1_tc_12089"])
result2_tc_12089 = str(queries.get("query")["result2_tc_12089"])
result1_tc_12090 = str(queries.get("query")["result1_tc_12090"])
result2_tc_12090 = str(queries.get("query")["result2_tc_12090"])


# TC-12087:Validate that criSuggestionEmailScore column was added to hcpFeatureStore table, and it corresponds to
# TC-12088:Validate that criSuggestionVisitScore column was added to hcpFeatureStore table, and it corresponds to
# TC-12089:Validate that criTargetAchievementScore column was added to hcpFeatureStore table, and it corresponds
# TC-12090:Validate that criTenureScore column was added to hcpFeatureStore table, and it corresponds to the max
@pytest.mark.tc12087
@pytest.mark.tc12088
@pytest.mark.tc12089
@pytest.mark.tc12090
@pytest.mark.parametrize("result1, result2", [(result1_tc_12087, result2_tc_12087),
                                              (result1_tc_12088, result2_tc_12088),
                                              (result1_tc_12089, result2_tc_12089),
                                              (result1_tc_12090, result2_tc_12090)])
def test_validate_column_added_hcpfeaturestore(spark, result1, result2):
    df_result1 = spark.sql(result1.format(cri_scores, hcpFeatureStore))
    df_result2 = spark.sql(result2.format(cri_scores, hcpFeatureStore))
    assert df_result1.rdd.isEmpty()
    assert df_result2.rdd.isEmpty()


# TC-12080:Validate that new productUid column was added, and it corresponds to productUid from bronze/product
@pytest.mark.tc12080
def test_tc_12080(spark):
    query = "with result1 as (SELECT a.productName as hcp_productName,a.productId as hcp_productId,  \
            a.productUid as hcp_productUid,b.productName as prod_productName,b.productId as prod_productId,  \
            b.externalId as prod_externalId FROM parquet.`{}` a inner join parquet.`{}` b on  \
            a.productName=b.productName limit 15)SELECT * FROM result1 WHERE hcp_productUid!=prod_externalId"
    df = spark.sql(query.format(hcpFeatureStore, product))
    assert df.rdd.isEmpty()
