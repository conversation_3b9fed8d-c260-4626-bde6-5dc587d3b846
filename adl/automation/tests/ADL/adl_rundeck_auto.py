import pytest
import os

from adl.automation.s3.s3_manager import <PERSON><PERSON><PERSON>anager
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
from adl.automation.utils import Constants
from adl.automation.rundeck.adl_job_params_builder import Builder
from adl.automation.utils.config_loader import load_config


bronze_folder_names = Constants.BRONZE_TABLES
silver_folder_names = Constants.SILVER_TABLE

customer = os.environ.get("ENV_NAME")
adl_branch = os.environ.get("ADL_BRANCH")
feature = os.environ.get("FEATURE_STORE")
environment = load_config(f'adl/automation/config/{customer}').data

ACCESS_KEY = str(environment.get("adl")["access_key"])
SECRET_KEY = str(environment.get("adl")["secret_key"])
AWS_REGION = str(environment.get("adl")["region"])
BUCKET = str(environment.get("adl")["bucket"])
base_key = str(environment.get("adl")["base_adl_key"])
deploy_job_id = str(environment.get("rundeck")["deploy_job_id"])
run_job_id = str(environment.get("rundeck")["run_job_id"])


# TC-9599:Validate that Rundeck job runs fine for PfizerUS
# TC-9666:Validate that job runs fine for pfizerUS for the first time(Batch)
@pytest.mark.tc9599
@pytest.mark.adl_job
def test_deploy_and_run_adl_job():
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_adl_branch(adl_branch)\
        .build_feature_store(feature) \
        .get_result()
    rundeck.run_job(builder, deploy_job_id)
    deploy_status = rundeck.get_status_last_execution(deploy_job_id)

    assert deploy_status == Constants.SUCCEEDED_STATUS

    rundeck.run_job(builder, run_job_id)
    run_status = rundeck.get_status_last_execution(run_job_id)

    assert run_status == Constants.SUCCEEDED_STATUS

    assert not rundeck.validate_output_has_errors(run_job_id)


# TC-9667:Validate that job runs fine for pfizerUS for the second time (Incremental)
@pytest.mark.tc9667
@pytest.mark.adl_job
def test_deploy_and_run_adl_job_incremental():
    s3_client = AWSManager.get_instance('s3', AWS_REGION, ACCESS_KEY, SECRET_KEY)
    if s3_client.s3_folder_exists(BUCKET, base_key.format("bronze")):
        rundeck = RundeckJobExecutor()
        builder = Builder() \
            .build_adl_branch(adl_branch)\
            .build_feature_store(feature) \
            .get_result()
        rundeck.run_job(builder, deploy_job_id)
        deploy_status = rundeck.get_status_last_execution(deploy_job_id)

        assert deploy_status == Constants.SUCCEEDED_STATUS

        rundeck.run_job(builder, run_job_id)
        run_status = rundeck.get_status_last_execution(run_job_id)

        assert run_status == Constants.SUCCEEDED_STATUS

        assert not rundeck.validate_output_has_errors(run_job_id)


# TC-10213:Validate that correct files were created under Bronze folder
# TC-9591:Validate that data was generated for PfizerUS
@pytest.mark.tc10213
@pytest.mark.adl_job
@pytest.mark.parametrize("table_name", bronze_folder_names)
def test_folder_validation(table_name):
    s3_client = AWSManager.get_instance("s3")
    response = s3_client.s3_folder_exists(BUCKET, base_key.format("bronze") + table_name)
    assert response

    files_exist = s3_client.s3_folder_contains_files(BUCKET,  base_key.format("bronze") + table_name, "parquet")

    assert files_exist


# TC-10215:Validate that correct files were created under Silver folder
@pytest.mark.tc10215
@pytest.mark.parametrize("table_name", silver_folder_names)
def test_tc_10215(table_name):
    s3_client = AWSManager.get_instance("s3")
    response = s3_client.s3_folder_exists(BUCKET, base_key.format("silver") + table_name)

    assert response
