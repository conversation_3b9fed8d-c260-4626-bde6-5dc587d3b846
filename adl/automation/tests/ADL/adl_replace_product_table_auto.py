import pytest
import os
from adl.automation.s3.s3_manager import AWSManager
from common.pyUtils.logger import get_module_logger
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.Constants import EMAILS_COLUMNS


customer = os.environ.get("ENV_NAME")
adl_branch = os.environ.get("ADL_BRANCH")
environment = load_config(f'adl/automation/config/{customer}').data
BUCKET = str(environment.get("adl")["bucket"])
base_adl_key = str(environment.get("adl")["base_adl_key"]).replace("{}", "bronze")
base_adl_url = str(environment.get("adl")["base_adl_url"])
logger = get_module_logger(__name__)


# TC-10406:Validate that product_final table is not present anymore
@pytest.mark.tc10406
def test_validate_that_product_final_table_is_not_present_anymore(spark):
    s3_client = AWSManager.get_instance("s3")
    response = s3_client.s3_folder_exists(BUCKET, f"{base_adl_key}product_final")
    assert response is None


# TC-10407:Validate that new columns were added to Emails table
@pytest.mark.tc10407
def test_validate_that_new_columns_were_added_to_emails_table(spark):
    s3_client = AWSManager.get_instance("s3")
    response = s3_client.s3_folder_exists(BUCKET, f"{base_adl_key}emails")
    assert response is not None

    expected_columns = EMAILS_COLUMNS
    email_table = f"{base_adl_url}bronze/emails"
    email_df = spark.read.parquet(email_table)
    actual_columns = set(email_df.columns)

    assert expected_columns.issubset(actual_columns)
