import pytest
import os

from common.pyUtils.logger import get_module_logger

from adl.automation.utils.data_reader import DataReader
from adl.automation.utils import Constants
from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])
base_rpt_url = str(environment.get("adl")["base_rpt_url"])

bronze_folder_names = Constants.BRONZE_TABLES
silver_folder_names = Constants.SILVER_TABLE

logger = get_module_logger(__name__)


# TC-11112:Validate calculation of CRI metric number 2 "Relationship Tenure"
@pytest.mark.tc11112
def validate_calculation_of_cri_metric_number_two_relationship_tenure(spark):
    recordclasses_dse_table = f"{base_adl_url}silver/recordclasses_dse/"
    cri_scores_table = f"{base_adl_url}silver/cri_scores/"

    df = spark.sql("\
        SELECT DISTINCT(interactionRepId), interactionAccountId \
        FROM parquet.`{}` \
        LIMIT 1".format(recordclasses_dse_table))

    interactionRepId = df.first().interactionRepId
    interactionAccountId = df.first().interactionAccountId

    logger.debug(f"interactionRepId: {interactionRepId}, interactionAccountId: {interactionAccountId}")

    query = 'with result2 as' \
            '	( with result1 as (' \
            '		select Interaction_repId as repId,Interaction_accountId as accountId,interactionyear, ' \
            '               interactionmonth, Interaction_startDateLocal,' \
            '			SUBSTRING(Interaction_startDateLocal, 6, 2) as mes1 ' \
            '		FROM parquet.`{}` ' \
            '		where Interaction_repId={} and Interaction_accountId={} order by Interaction_startDateLocal desc' \
            '		)' \
            '	select  MAX(Interaction_startDateLocal) as Interaction_startDate,' \
            '		CONCAT(first(repId),"_",first(accountId)) as rep_account,interactionyear,first(interactionmonth),' \
            '		CONCAT(interactionyear,mes1) as year_month,' \
            '		(select MIN(Interaction_startDateLocal) from result1) as mindate ' \
            '	from result1 group by interactionyear,mes1  order by Interaction_startDate desc' \
            '	)' \
            'select a.*,DATEDIFF(a.Interaction_startDate, mindate) as Difference, ' \
            '       b.tenure_score as tenure_score_from_cri ' \
            'from result2 a ' \
            'join parquet.`{}` b on a.rep_account=b.Interaction_rep_account and a.year_month=b.Interaction_YearMonth ' \
            'order by a.Interaction_startDate desc'.format(recordclasses_dse_table, interactionRepId,
                                                           interactionAccountId, cri_scores_table)

    rpt_df = spark.sql(query)
    assert rpt_df.filter("Difference != tenureScore_from_cri").rdd.isEmpty()


# TC-11114:Validate calculation of CRI metric number 4 "Cadence of Visits"
@pytest.mark.tc11114
def validate_calculation_of_cri_metric_number_four_cadence_of_visits(spark):
    recordclasses_dse_table = f"{base_adl_url}silver/recordclasses_dse/"

    df = spark.sql("\
    SELECT DISTINCT(interactionRepId), interactionAccountId \
    FROM parquet.`{}` \
    WHERE interactionTypeId=4 and interactionIsCompleted=1 \
    LIMIT 1".format(recordclasses_dse_table))

    interactionRepId = df.first().interactionRepId
    interactionAccountId = df.first().interactionAccountId
    interactionTypeId = df.first().interactionTypeId
    interactionIsCompleted = df.first().interactionIsCompleted

    logger.debug(f"interactionRepId: {interactionRepId}, interactionAccountId: {interactionAccountId}, "
                 f"interactionTypeId: {interactionTypeId}, interactionIsCompleted: {interactionIsCompleted}")

    query = 'with result7 as (' \
            '	with result6 as ( ' \
            '		with result5 as ( ' \
            '			with result4 as ( ' \
            '				with result3 as ( ' \
            '					with result2 as ( ' \
            '						with result1 as(' \
            '							select interactionIsCompleted,interactionTypeName,interactionRepAccount,' \
            '                                   interactionYear,interactionMonth,interactionStartDateLocal,' \
            '								    SUBSTRING(interactionStartDateLocal, 6, 2) as mes1 ' \
            '							FROM parquet.`{}` ' \
            '							where interactionRepId={} and interactionAccountId={} and ' \
            '                                   interactionTypeId={} and interactionIsCompleted={} ' \
            '							order by interactionStartDateLocal desc' \
            '						)' \
            '						select *,(select MAX(interactionStartDateLocal) from result1) as maxdate, ' \
            '                               CONCAT(interactionyear,mes1) as year_month ' \
            '						from result1 order by interactionStartDateLocal asc' \
            '				)' \
            '					select * from result2 where interactionStartDateLocal >= add_months(maxdate,-150)' \
            '				)' \
            '				select *,(select MIN(interactionStartDateLocal) from result3) as mindate,' \
            '					ROW_NUMBER() OVER (ORDER BY year_month asc) as rownumber ' \
            '				from result3 order by interactionStartDateLocal desc' \
            '			)' \
            '			select a1.*, a2.interactionStartDateLocal as previousInteraction ' \
            '			from result4 a1 left join result4 a2 on a1.rownumber=a2.rownumber+1' \
            '		)' \
            '		select *, DATEDIFF(interactionStartDateLocal, mindate) as tenure, ' \
            '                   DATEDIFF(interactionStartDateLocal, previousInteraction) as days_btw_visits ' \
            '		from result5' \
            '	)' \
            '	select *, ROUND(tenure/rownumber,2) as average_visit,' \
            '             ROUND(STDDEV(days_btw_visits) OVER (ORDER BY year_month asc),2) as STDEV ' \
            '	from result6' \
            ')' \
            'select *,ROUND((average_visit+STDEV)/2,2) as cri_calculated ' \
            'from result7'.format(recordclasses_dse_table, interactionRepId, interactionAccountId,
                                  interactionTypeId, interactionIsCompleted)
    rpt_df = spark.sql(query)
    assert rpt_df.filter("cri_calculated != visitScore").rdd.isEmpty()


# TC-11116:Validate calculation of CRI metric number 6 "Suggestion Email Completion Probability"
@pytest.mark.tc11116
def validate_calculation_of_cri_metric_number_six_suggestion_email_completion_probability(spark):
    suggestions_table = f"{base_adl_url}bronze/suggestions/"

    df = spark.sql("\
    SELECT DISTINCT(interactionRepId), interactionAccountId \
    FROM parquet.`{}` \
    WHERE interactionTypeId=4 and interactionIsCompleted=1 \
    LIMIT 1".format(suggestions_table))

    repId = df.first().repId
    accountId = df.first().accountId
    detailRepActionTypeId = df.first().detailRepActionTypeId

    logger.debug(f"repId: {repId}, accountId: {accountId}, "
                 f"detailRepActionTypeId: {detailRepActionTypeId}")

    query = 'with result6 as( ' \
        '	with result5 as( ' \
        '		with result4 as( ' \
        '			with result3 as( ' \
        '				with result2 as( ' \
        '					with result1 as(' \
        '						select suggestionReferenceId,detailRepActionTypeId as RepActionTypeId,' \
        '							actionTaken,isSuggestionCompleted,suggestedDate,repId,accountId,	' \
        '						    CONCAT(repId,"_",accountId) as rep_account, ' \
        '							concat(left(SUBSTRING_INDEX(suggestedDate,"-",2),4), ' \
        '                           right(SUBSTRING_INDEX(suggestedDate,"-",2),2)) as suggestion_YearMonth ' \
        '						FROM parquet.`{}` ' \
        '                       where repId={} and accountId={} and detailRepActionTypeId={} ' \
        '					)' \
        '					select *,count(suggestionReferenceId) over(partition by suggestionReferenceId,' \
        '						rep_account,suggestion_YearMonth) as unique_suggestion_visit_count,' \
        '						MAX(suggestedDate) over(partition by rep_account,suggestion_YearMonth) ' \
        '                       as maxsuggestedDatepermonth ' \
        '					from result1' \
        '				)' \
        '				select *,IF(suggestedDate=maxsuggestedDatepermonth,1,0) as lastFlag ' \
        '				from result2 order by suggestedDate desc' \
        '			)' \
        '			select *,count(repId) over(partition by suggestion_YearMonth, ' \
        '                   lastFlag=1 order by suggestedDate desc) as total_suggestion_mo ' \
        '			from result3 order by suggestedDate desc' \
        '		)' \
        '		select rep_account,first(RepActionTypeId),suggestion_YearMonth,max(suggestedDate) as suggestedDate,' \
        '			first(total_suggestion_mo) as total_suggestion_mo,' \
        '           sum(isSuggestionCompleted) as isSuggestionCompleted ' \
        '		from result4 group by rep_account,suggestion_YearMonth ' \
        '	)' \
        '	select * from result5 order by suggestion_YearMonth asc' \
        ')' \
        'select *,Sum(total_suggestion_mo) Over ( Order by suggestedDate asc ) as suggestion_total,' \
        '	Sum(isSuggestionCompleted) Over ( Order by suggestedDate asc ) as complete_total ' \
        'from result6'.format(suggestions_table, repId, accountId, detailRepActionTypeId)
    rpt_df = spark.sql(query)
    assert rpt_df.filter("complete_total != suggestionVisitScore").rdd.isEmpty()


# TC-11118:Validate calculation of CRI metric number 8 "Channel Utilization"
@pytest.mark.tc11118
def validate_calculation_of_cri_metric_number_eight_channel_utilization(spark):
    recordclasses_dse_table = f"{base_adl_url}silver/recordclasses_dse/"

    query = 'select interactionRepAccount, targetsPeriodId, round(avg(channelQuarter), 2) as channelScore' \
            'from(select interactionRepAccount, targetsPeriodId, ' \
            '		CASE WHEN count(distinct interactionTypeId) > 1 ' \
            '               THEN min(channelTarget) / max(channelTarget) ELSE NULL END as channelQuarter' \
            '		from(' \
            '			select *, CASE WHEN target is not null and target > 0 and channelCountPeriod is not null ' \
            '							THEN channelCountPeriod / target ELSE channelCountPeriod END as channelTarget' \
            '			from(' \
            '				select interactionRepAccount, targetsPeriodId, interactionTypeId, ' \
            '					sum(CASE WHEN target is not null THEN target ELSE 0 END) as target, ' \
            '					count(CASE WHEN interactionIsCompleted = "1" THEN 1 END) as channelCountPeriod' \
            '				from {}' \
            '				group by interactionRepAccount,targetsPeriodId, interactionTypeId' \
            '			)' \
            '		where interactionTypeId in (11,4)' \
            '		)' \
            '	where channelTarget is not null' \
            '	group by interactionRepAccount, targetsPeriodId' \
            '	)' \
            'where channelQuarter is not null' \
            'group by interactionRepAccount, targetsPeriodId'.format(recordclasses_dse_table)

    rpt_df = spark.sql(query)
    assert rpt_df.filter("channel_utilization_calculated != channelScore").rdd.isEmpty()


# TC-11512:Validate calculation of CRI metric number 10 "criScoreAvg"
@pytest.mark.tc11512
def validate_calculation_of_cri_metric_number_ten_cri_score_avg(spark):
    cri_scores_table = f"{base_adl_url}silver/cri_scores/"

    query = 'with result1 as(' \
            '	SELECT interactionRepAccount,interactionYearMonth,' \
            '	  8-((CASE WHEN first(emailOpenScore) IS NULL THEN 1 ELSE 0 END)' \
            '	  + (CASE WHEN first(tenureScore) IS NULL THEN 1 ELSE 0 END)' \
            '	  + (CASE WHEN first(visitScore) IS NULL THEN 1 ELSE 0 END)' \
            '	  + (CASE WHEN first(cadenceScore) IS NULL THEN 1 ELSE 0 END)' \
            '	  + (CASE WHEN first(suggestionVisitScore) IS NULL THEN 1 ELSE 0 END)' \
            '	  + (CASE WHEN first(suggestionEmailScore) IS NULL THEN 1 ELSE 0 END)' \
            '	  + (CASE WHEN first(targetAchievementScore) IS NULL THEN 1 ELSE 0 END)' \
            '	  + (CASE WHEN first(channelScore) IS NULL THEN 1 ELSE 0 END)) AS sum_of_not_nulls,' \
            '	  Round(' \
            '			first(coalesce(emailOpenScore,0.00)' \
            '			+coalesce(tenureScore,0.00)' \
            '			+coalesce(visitScore,0.00)' \
            '			+coalesce(cadenceScore,0.00)' \
            '			+coalesce(suggestionVisitScore,0.00)' \
            '			+coalesce(suggestionEmailScore,0.00)' \
            '			+coalesce(targetAchievementScore,0.00)' \
            '			+coalesce(channelScore,0.00)),2) as calculated_sum,' \
            '	  first(criScoreSum) as criScoreSum,' \
            '	  first(criScoreAvg) as criScoreAvg' \
            '	FROM parquet.`{}` group by interactionRepAccount,interactionYearMonth limit 10' \
            ') select *,ROUND((calculated_sum/sum_of_not_nulls),2) as calculated_avg ' \
            'from result1'.format(cri_scores_table)

    rpt_df = spark.sql(query)
    assert rpt_df.filter("criScoreAvg != channelScore").rdd.isEmpty()

    query = 'with result2 as(' \
            'with result1 as(' \
            'SELECT interactionRepAccount,interactionYearMonth,' \
            '  8-((CASE WHEN first(emailOpenScore) IS NULL THEN 1 ELSE 0 END)' \
            '  + (CASE WHEN first(tenureScore) IS NULL THEN 1 ELSE 0 END)' \
            '  + (CASE WHEN first(visitScore) IS NULL THEN 1 ELSE 0 END)' \
            '  + (CASE WHEN first(cadenceScore) IS NULL THEN 1 ELSE 0 END)' \
            '  + (CASE WHEN first(suggestionVisitScore) IS NULL THEN 1 ELSE 0 END)' \
            '  + (CASE WHEN first(suggestionEmailScore) IS NULL THEN 1 ELSE 0 END)' \
            '  + (CASE WHEN first(targetAchievementScore) IS NULL THEN 1 ELSE 0 END)' \
            '  + (CASE WHEN first(channelScore) IS NULL THEN 1 ELSE 0 END)) AS sum_of_not_nulls,' \
            '  Round(' \
            'first(coalesce(emailOpenScore,0.00)' \
            '+coalesce(tenureScore,0.00)' \
            '+coalesce(visitScore,0.00)' \
            '+coalesce(cadenceScore,0.00)' \
            '+coalesce(suggestionVisitScore,0.00)' \
            '+coalesce(suggestionEmailScore,0.00)' \
            '+coalesce(targetAchievementScore,0.00)' \
            '+coalesce(channelScore,0.00)),2) as calculated_sum,' \
            '  first(criScoreSum) as criScoreSum,' \
            '  first(criScoreAvg) as criScoreAvg' \
            'FROM parquet.`{}` group by interactionRepAccount,interactionYearMonth limit 10' \
            ') select *,ROUND((calculated_sum/sum_of_not_nulls),2) as calculated_avg from result1' \
            ') select * from result2 where criScoreAvg != calculated_avg'.format(cri_scores_table)

    rpt_df = spark.sql(query)
    assert rpt_df.rdd.isEmpty()


# TC-12092:Validate that criCadenceScore value was reversed and it is correct
@pytest.mark.tc12092
def validate_that_cri_cadence_score_value_was_reversed_and_it_is_correct(spark):
    recordclasses_dse_table = f"{base_adl_url}silver/recordclasses_dse/"
    cri_scores_table = f"{base_adl_url}silver/cri_score/"

    df = spark.sql("\
    SELECT DISTINCT(interactionRepId), interactionAccountId \
    FROM parquet.`{}` \
    WHERE interactionTypeId=4 and interactionIsCompleted=1 \
    LIMIT 1".format(recordclasses_dse_table))

    interactionRepId = df.first().interactionRepId
    interactionAccountId = df.first().interactionAccountId
    interactionTypeId = df.first().interactionTypeId
    interactionIsCompleted = df.first().interactionIsCompleted

    df_cri_scores = spark.sql("\
        SELECT DISTINCT(interactionRepId), tenure_score \
        FROM parquet.`{}` where interactionRepId={}".format(cri_scores_table, interactionRepId))

    tenure_score = df_cri_scores.first().tenure_score

    logger.debug(f"interactionRepId: {interactionRepId}, interactionAccountId: {interactionAccountId}, "
                 f"interactionTypeId: {interactionTypeId}, interactionIsCompleted: {interactionIsCompleted}")

    query = 'with result7 as (' \
            '	with result6 as ( ' \
            '		with result5 as ( ' \
            '			with result4 as ( ' \
            '				with result3 as ( ' \
            '					with result2 as ( ' \
            '						with result1 as(' \
            '							select interactionIsCompleted,interactionTypeName,interactionRepAccount,' \
            '                                   interactionYear,interactionMonth,interactionStartDateLocal,' \
            '								    SUBSTRING(interactionStartDateLocal, 6, 2) as mes1 ' \
            '							FROM parquet.`{}` ' \
            '							where interactionRepId={} and interactionAccountId={} and ' \
            '                                   interactionTypeId={} and interactionIsCompleted={} ' \
            '							order by interactionStartDateLocal desc' \
            '						)' \
            '						select *,(select MAX(interactionStartDateLocal) from result1) as maxdate, ' \
            '                               CONCAT(interactionyear,mes1) as year_month ' \
            '						from result1 order by interactionStartDateLocal asc' \
            '				)' \
            '					select * from result2 where interactionStartDateLocal >= add_months(maxdate,-150)' \
            '				)' \
            '				select *,(select MIN(interactionStartDateLocal) from result3) as mindate,' \
            '					ROW_NUMBER() OVER (ORDER BY year_month asc) as rownumber ' \
            '				from result3 order by interactionStartDateLocal desc' \
            '			)' \
            '			select a1.*, a2.interactionStartDateLocal as previousInteraction ' \
            '			from result4 a1 left join result4 a2 on a1.rownumber=a2.rownumber+1' \
            '		)' \
            '		select *, DATEDIFF(interactionStartDateLocal, mindate) as tenure, ' \
            '                   DATEDIFF(interactionStartDateLocal, previousInteraction) as days_btw_visits ' \
            '		from result5' \
            '	)' \
            '	select *, ROUND(tenure/rownumber,2) as average_visit,' \
            '             ROUND(STDDEV(days_btw_visits) OVER (ORDER BY year_month asc),2) as STDEV ' \
            '	from result6' \
            ')' \
            'select *,ROUND((average_visit+STDEV)/2,2) as cri_calculated ' \
            'from result7'.format(recordclasses_dse_table, interactionRepId, interactionAccountId,
                                  interactionTypeId, interactionIsCompleted)
    rpt_df = spark.sql(query)

    new_score = rpt_df.selectExpr("criCandenceScore", "1 - criCandenceScore as new_score")

    assert new_score == tenure_score

