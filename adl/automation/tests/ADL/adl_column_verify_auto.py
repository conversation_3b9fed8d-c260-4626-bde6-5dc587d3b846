import pytest
import os
import logging

from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
queries = load_config('adl/automation/resources/queries_final_dataset').data
base_adl_url = str(environment.get("adl")["base_adl_url"])
logger = logging.getLogger(__name__)


tc_11836 = str(queries.get("query")["tc_11836"])
tc_11837 = str(queries.get("query")["tc_11837"])
tc_11838 = str(queries.get("query")["tc_11838"])
tc_11839 = str(queries.get("query")["tc_11839"])

account = f"{base_adl_url}bronze/account/"
final_dataset = f"{base_adl_url}silver/final_dataset"
visits = f"{base_adl_url}bronze/visits"
recordclasses = f"{base_adl_url}silver/recordclasses"

table_columns_names = [(recordclasses, 'latitude1', 'latitude2'), (final_dataset,  'suggestionChannel', 'channel')]

column_name = [('latitude', 'longitude', 'geoLocationString', 'timeZoneId')]

query_with_columns = [(tc_11836, 'latitude1', 'latitude2'), (tc_11837, 'longitude1', 'longitude2'),
                      (tc_11838, 'geoLocationString1', 'geoLocationString2'), (tc_11839, 'timeZoneId1', 'timeZoneId2')]


# TC-11828:Validate that “latitude“ column was added to account table
# TC-11829:Validate that “longitude“ column was added to account table
# TC-11830:Validate that “geoLocationString“ column was added to account table
# TC-11831:Validate that “timeZoneId“ column was added to account table
@pytest.mark.parametrize("column", column_name)
@pytest.mark.tc11828
@pytest.mark.tc11829
@pytest.mark.tc11830
@pytest.mark.tc11831
def test_validate_column_name(spark, column):
    query = "SELECT * FROM parquet.`{}`"
    df = spark.sql(query.format(account))
    columns = df.columns
    assert column in columns


# TC-11836:Validate that “latitude“ column was added to final_dataset table
# TC-11837:Validate that “longitude“ column was added to final_dataset table
# TC-11838:Validate that “geoLocationString“ column was added to final_dataset table
# TC-11839:Validate that “timeZoneId“ column was added to final_dataset table
@pytest.mark.parametrize("query, column1, column2", query_with_columns)
@pytest.mark.tc11836
@pytest.mark.tc11837
@pytest.mark.tc11838
@pytest.mark.tc11839
def test__validate_columns_name(spark, query, column1, column2):
    df = spark.sql(query.format(final_dataset, account))
    columns = df.columns
    assert column1 in columns
    assert column2 in columns
    assert df.rdd.isEmpty()


# TC-10408:Validate that new columns were added to Visits table
@pytest.mark.tc10408
def test_tc_10408(spark):
    df = spark.sql("select * FROM parquet.`{}`".format(visits))
    df = df.columns
    assert 'messageReaction', 'actionOrder' in df and ('quantity', 'suggestionReferenceId') in df
    assert 'matchedSuggestionProduct', 'matchedSuggestionMessage' in df and ('suggestionInferredAt', 'productId') in df
    assert 'physicalMessageUID' in df


# TC-10409:Validate that new columns were added to recordclasses table
# TC-13085:Validate that final_dataset has the suggestionChannel and channel columns
@pytest.mark.parametrize("table, column1, column2", table_columns_names)
@pytest.mark.tc10409
@pytest.mark.tc13085
def test_validate_columns(spark, table, column1, column2):
    logger.info(f"validate that columns: {column1} and {column2} are present into {table} table")
    df = spark.read.format('delta').load(table)

    assert column1, column2 in df.columns
