import pytest
import os
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.date_utils import DateUtils


customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])
hcp_feature_store_table = f"{base_adl_url}silver/hcpFeatureStore"
final_dataset_table = f"{base_adl_url}silver/final_dataset"
account_table = f"{base_adl_url}/bronze/account"
month_list = DateUtils.generate_index_month()


# TC-12061:Validate that new yearMonth column was added and it correspond to interactionYearMonth from final_dataset
@pytest.mark.tc12061
def test_validate_that_new_yearmonth_column_was_added_and_it_correspond_to_interactionyearmonth(spark):
    query = 'WITH result AS ( SELECT a.accountId AS account1, a.yearMonth AS yearMonth, b.accountId AS account2, \
            b.interactionYearMonth AS interactionYearMonth FROM parquet.`{}` a INNER JOIN parquet.`{}` b \
            ON a.accountId=b.accountId AND a.yearMonth=b.interactionYearMonth ) SELECT COUNT(*) AS c FROM result \
            WHERE yearMonth!=interactionYearMonth'.format(hcp_feature_store_table, final_dataset_table)
    df = spark.sql(query)
    assert df.first().c == 0


# TC-12064:Validate that new accountUid column was added and it correspond to externalId from bronze/account table
# TC-12067:Validate that new facilityId column was added and  it correspond to facilityId from bronze/account table
@pytest.mark.tc12064
@pytest.mark.tc12067
@pytest.mark.parametrize("data", [("accountUid", "externalId"), ("facilityId", "facilityId")])
def test_validate_that_new_column_was_added_and_it_correspond_to_bronze_account_table(spark, data):
    query = 'WITH result AS ( SELECT a.accountId AS account1, a.{} AS data1, b.accountId AS account2, \
            b.{} AS data2 FROM parquet.`{}` a INNER JOIN parquet.`{}` b ON a.accountId=b.accountId) \
            SELECT COUNT(*) AS c FROM result WHERE data1 != data2 \
            '.format(data[0], data[1], hcp_feature_store_table, account_table)
    df = spark.sql(query)
    assert df.first().c == 0


# TC-12072:Validate that new indexMonth column was added and number is correct
@pytest.mark.tc12072
@pytest.mark.parametrize("month", month_list)
def test_validate_that_new_indexmonth_column_was_added_and_number_is_correct(spark, month):
    query = 'SELECT COUNT(*) AS c FROM parquet.`{}` \
            WHERE indexMonth!={} AND yearMonth={}'.format(hcp_feature_store_table, month[0], month[1])
    df = spark.sql(query)
    assert df.first().c == 0


# TC-12073:Validate that new hcpStartYearMonth column was added and it correspond to first account interaction
@pytest.mark.tc12073
def test_validate_that_new_hcpstartyearmonth_column_was_added_and_it_correspond_to_first_account_interaction(spark):
    query = 'WITH result4 AS ( WITH result3 AS ( WITH result2 AS ( WITH result1 AS ( SELECT accountId, \
            First_Value(interactionStartDateLocal) OVER (PARTITION BY accountId ORDER BY interactionStartDateLocal) AS \
            interactionStartDateLocal FROM parquet.`{}` WHERE interactionStartDateLocal IS NOT null ORDER BY \
            interactionStartDateLocal asc ) SELECT accountId, First_Value(interactionStartDateLocal) AS \
            interactionStartDateLocal FROM result1 GROUP BY accountId ) SELECT *, CONCAT( SUBSTRING( \
            interactionStartDateLocal, 1, 4), SUBSTRING(interactionStartDateLocal, 6, 2)) AS firstYearMonth FROM \
            result2 ) SELECT a.accountId, a.hcpStartYearMonth, b.firstYearMonth  FROM parquet.`{}` a INNER JOIN \
            result3 b ON a.accountId=b.accountId ) SELECT COUNT(*) AS c FROM result4 WHERE \
            hcpStartYearMonth!=firstYearMonth'.format(final_dataset_table, hcp_feature_store_table)
    df = spark.sql(query)
    assert df.first().c == 0


# TC-12076:Validate that new timeZone column was added and it correspond to timezone from bronze/account table
@pytest.mark.tc12076
def test_validate_that_new_timezone_column_was_added_and_it_correspond_to_timezone_from_bronze_account_table(spark):
    query = 'WITH RESULT AS ( SELECT a.accountId AS account1, a.timeZone AS timeZone1, b.accountId AS account2, \
            b.timeZoneId AS timeZoneId2 FROM parquet.`{}` a INNER JOIN parquet.`{}` b ON a.accountId=b.accountId) \
            SELECT COUNT(*) AS c FROM result WHERE timeZone1!=SUBSTRING(timeZoneId2, 11, 6) \
            '.format(hcp_feature_store_table, account_table)
    df = spark.sql(query)
    assert df.first().c == 0
