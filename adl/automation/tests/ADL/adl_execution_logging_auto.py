import pytest
import os
import logging
from adl.automation.s3.s3_manager import <PERSON><PERSON><PERSON>anager
from adl.automation.utils.config_loader import load_config
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
from adl.automation.rundeck.adl_job_params_builder import Builder
from adl.automation.utils import Constants

customer = os.environ.get("ENV_NAME")
adl_branch = os.environ.get("ADL_BRANCH")
feature = os.environ.get("FEATURE_STORE")
environment = load_config(f'adl/automation/config/{customer}').data
base_adl_logs_key = str(environment.get("adl")["base_adl_logs_key"])
base_rpt_key = str(environment.get("adl")["base_rpt_url"]).replace("s3a", "s3")
base_config_key = str(environment.get("adl")["base_config_url"]).replace("s3a", "s3")
BUCKET = str(environment.get("adl")["bucket"])
RPT_BUCKET = str(environment.get("adl")["rpt_bucket"])
RPT_TABLES = Constants.RPT_TABLES
PATTERN_FILE = 'adl_log_'
BASE_KEY = str(environment.get("adl")["base_adl_key"])
deploy_job_id = str(environment.get("rundeck")["deploy_job_id"])
run_job_id = str(environment.get("rundeck")["run_job_id"])
BASE = str(environment.get("adl")["base_adl_url"])
prefix_name = BASE_KEY.replace("data/{}/", "logs/adl_log_")
logger = logging.getLogger(__name__)


# TC-10197 : Validate correct tables are being displayed in logs
@pytest.mark.tc10197
def test_validate_correct_tables_are_being_displayed_in_logs(spark):
    s3_client = AWSManager.get_instance("s3")
    last_file = s3_client.s3_get_file_key_last_modified(BUCKET, base_adl_logs_key, PATTERN_FILE)
    content_file = s3_client.s3_read_file(BUCKET, last_file)

    missing_tables = []
    for table in RPT_TABLES:
        url = f"{base_rpt_key}{table}"
        logger.info(f"table url validated in the logs: {url}")
        table_created_msg = f"['{table}', ' view has been created']"
        table_saved_msg = f"{table} columns saved..."
        if not (url in content_file and table_created_msg in content_file and table_saved_msg in content_file):
            missing_tables.append(table)

    error_msg = f"There are {len(missing_tables)} Tables, that were not displayed in logs: {missing_tables}"

    assert len(missing_tables) == 0, error_msg


# TC-10196 : Validate that correct path for delta and mapping files is displayed
@pytest.mark.tc10196
def test_tc_10196(spark):
    is_present = True
    s3_client = AWSManager.get_instance()
    last_file = s3_client.s3_get_file_key_last_modified(BUCKET, base_adl_logs_key, PATTERN_FILE)
    print(last_file)
    content_file = s3_client.s3_read_file(BUCKET, "preuat/adl2/logs/adl_log_20221213004536.txt")
    mapping_tables = Constants.MAPPING_TABLES
    for table in mapping_tables:
        log_entry = f"The data {table} is versioned in Delta at {base_config_key}"
        if log_entry not in content_file:
            is_present = False
            break

    assert is_present


# TC-10195 : Validate that log files are NOT being overwritten if logs folder is not empty
@pytest.mark.tc10195
@pytest.mark.adl_job
def test_tc_10195():
    s3_client = AWSManager.get_instance("s3")
    base_key = BASE_KEY.replace("data/{}/", "logs/")
    response = s3_client.s3_folder_exists(BUCKET, f"{base_key}")
    assert response is not None

    count_logs_backup = s3_client.get_folder_content(BUCKET, base_key)

    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_adl_branch(adl_branch)\
        .build_feature_store(feature) \
        .get_result()
    rundeck.run_job(builder, deploy_job_id)

    deploy_status = rundeck.get_status_last_execution(deploy_job_id)
    assert deploy_status == Constants.SUCCEEDED_STATUS
    rundeck.run_job(builder, run_job_id)
    run_status = rundeck.get_status_last_execution(run_job_id)
    assert run_status == Constants.SUCCEEDED_STATUS
    assert not rundeck.validate_output_has_errors(run_job_id)

    count_logs_new = s3_client.get_folder_content(BUCKET, base_key)
    assert len(count_logs_backup) < len(count_logs_new), "The logs files are being overwritten "

    txt_list = s3_client.get_txt_file_content(BUCKET, prefix_name)
    assert len(txt_list) > 0, "The last log file is empty"
