import pytest
import os

from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_rpt_url = str(environment.get("adl")["base_rpt_url"])
base_adl_url = str(environment.get("adl")["base_adl_url"])

adl_dse_score = f"{base_adl_url}bronze/dse_score"
rds_dserun = f"{base_rpt_url}dserun"
rds_dserunrepdate = f"{base_rpt_url}dserunrepdate"
rds_dserunrepdatesuggestion = f"{base_rpt_url}dserunrepdatesuggestion"
rds_dserunrepdatesuggestiondetail = f"{base_rpt_url}dserunrepdatesuggestiondetail"


# TC-10223:Validate that Dse_score is created by the four raw tables, check count of runId , runrepdateid and r
@pytest.mark.tc10223
def test_tc_10223(spark):

    query_adl = "SELECT count(distinct(suggestionReferenceId)) as Dse_score FROM parquet.`{}`"
    df_adl = spark.sql(query_adl.format(adl_dse_score))
    df_adl = df_adl.first().Dse_score

    query = "SELECT count(distinct(suggestionReferenceId)) as Rds_count FROM ( SELECT d1.repId,d3.accountId,d.runId,d1.runRepDateId,d.runGroupId, \
            d.seConfigId,d4.productId,d3.suggestionReferenceId,d4.messageTopicId FROM parquet.`{}` \
             d INNER JOIN parquet.`{}` d1 ON (d1.runId is not null and d.runId=d1.runId) inner join parquet.`{}` \
             d3 ON d1.runRepDateId=d3.runRepDateId INNER JOIN parquet.`{}` d4 ON  \
            d3.runRepDateSuggestionId=d4.runRepDateSuggestionId GROUP BY d1.repId,d3.accountId,d.runId, \
            d1.runRepDateId,d.runGroupId,d.seConfigId,d4.productId,d3.suggestionReferenceId,d4.messageTopicId )"
    df_rds = spark.sql(query.format(rds_dserun, rds_dserunrepdate,
                                    rds_dserunrepdatesuggestion, rds_dserunrepdatesuggestiondetail))
    df_rds = df_rds.first().Rds_count

    assert df_adl == df_rds
