import pytest
import os

from pyspark.sql.functions import regexp_replace, when
from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
adl_branch = os.environ.get("ADL_BRANCH")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])
base_data_key = str(environment.get("adl")["base_data_key"])


# TC-9485:Validate that correct data is displayed for each mapping version
@pytest.mark.tc9485
def test_tc_9485(spark):
    adl_table = f"{base_adl_url.replace('data', 'config')}deltaMapping/"
    df = spark.read.format("delta").load(adl_table)
    df = df.withColumn('Customer_attr_watermark',
                       regexp_replace('Customer_attr_watermark', "'' as ", ""))

    test_table = f"{base_data_key.format(adl_branch)}mapping.csv"
    mapping_df = spark.read.csv(test_table, header=True, sep="|")
    mapping_df = mapping_df.withColumn('Customer_attr_watermark',
                                       regexp_replace('Customer_attr_watermark', "'' as ", ""))

    result_df = df.subtract(mapping_df)
    print(result_df.show())
    assert result_df.rdd.isEmpty()
