import pytest
import os

from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])

suggestions = f"{base_adl_url}bronze/suggestions"
suggestions_final = f"{base_adl_url}bronze/suggestions_final"


# TC-12838:Validate that ‘suggestions’ bronze table has the newly added Marketo channel suggestion data
# TC-12839:Validate that ‘suggestions_final’ bronze table has the newly added Marketo channel suggestion data
@pytest.mark.parametrize("select_table", [suggestions, suggestions_final])
@pytest.mark.tc12838
@pytest.mark.tc12839
def test_tc_12838(spark, select_table):
    df = spark.sql("SELECT DISTINCT(interactionId) as interactionId FROM parquet.`{}` LIMIT 1".format(select_table))
    interaction_id = df.first().interactionId
    df = spark.sql("SELECT * FROM parquet.`{}` WHERE detailRepActionName='MARKETO' "
                   "AND interactionId={}".format(select_table, interaction_id))
    assert not df.rdd.isEmpty(), "The dataframe is empty"
