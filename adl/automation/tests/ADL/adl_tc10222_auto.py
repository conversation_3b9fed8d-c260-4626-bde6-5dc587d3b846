import pytest
import os

from common.pyUtils.logger import get_module_logger

from adl.automation.utils import Constants
from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

adl_branch = os.environ.get("ADL_BRANCH")

base_adl_url = str(environment.get("adl")["base_adl_url"])
base_rpt_url = str(environment.get("adl")["base_rpt_url"])

logger = get_module_logger(__name__)


# TC-10222:Validate that final suggestion count is equal in bronze folder and RPT folder
@pytest.mark.tc10222
def test_validate_that_final_suggestion_count_is_equal_in_bronze_folder_and_rpt_folder(spark):
    suggestion_final_table = f"{base_adl_url}bronze/suggestions_final/"
    query = 'SELECT count(distinct(interactionId)) as countInteraction FROM parquet.`{}`'.format(suggestion_final_table)
    df = spark.sql(query)
    count_suggestions1 = df.first().countInteraction
    logger.debug(f"Count interactions of suggestion_final table: {count_suggestions1}")

    interaction_table = f"{base_rpt_url}interaction"
    query = "SELECT COUNT(DISTINCT(interactionId)) AS countInteraction FROM parquet.`{}` WHERE interactionId in ( \
            SELECT interactionId FROM parquet.`{}`)".format(interaction_table, suggestion_final_table)

    rpt_df = spark.sql(query)
    count_suggestions2 = rpt_df.first().countInteraction
    logger.debug(f"Count interactions of interaction table: {count_suggestions2}")

    assert count_suggestions1 == count_suggestions2


# TC-10218:Validate that emails count is equal in bronze folder and RPT folder
@pytest.mark.tc10218
def test_validate_that_emails_count_is_equal_in_bronze_folder_and_rpt_folder(spark):
    interaction_table = f"{base_rpt_url}interaction"
    interactiontype_table = f"{base_rpt_url}interactiontype"
    interactionproduct_table = f"{base_rpt_url}interactionproduct"
    message_table = f"{base_rpt_url}message"
    repactiontype_table = f"{base_rpt_url}repactiontype"
    sent_email_vod__c_table = f"{base_rpt_url}sent_email_vod__c"
    messagetopic_table = f"{base_rpt_url}messagetopic"
    interactionaccount_table = f"{base_rpt_url}interactionaccount"

    query = "SELECT COUNT(*) AS c FROM parquet.`{}` i INNER JOIN parquet.`{}` it ON \
            i.interactionTypeId=it.interactionTypeId INNER JOIN parquet.`{}` itp ON i.interactionId=itp.interactionId \
            INNER JOIN parquet.`{}` m ON (m.messageId = itp.messageId) INNER JOIN parquet.`{}` raty \
            ON i.repActionTypeId=raty.repActionTypeId LEFT JOIN parquet.`{}` email ON i.externalId=email.Id \
            LEFT JOIN ( SELECT t1.messageId AS email_Message_Id, messageChannelId AS email_messageChannelId, \
            COALESCE(t1.messageTopicId,t2.messageTopicId) AS email_messageTopicId, t2.messageTopicName AS \
            email_messageTopicName, t1.messageName AS email_messagename, t1.messageDescription AS \
            email_messagedescription FROM parquet.`{}` t1 LEFT JOIN parquet.`{}` t2  ON \
            t1.messageTopicId=t2.messageTopicId ) t7 ON t7.email_Message_Id=itp.messageId INNER JOIN parquet.`{}` ia \
            ON i.interactionId=ia.interactionId WHERE i.interactionTypeId =11 AND i.isDeleted=0 AND i.startDateTime \
            IS NOT null AND i.repActionTypeId=12 AND m.messageChannelId = 1".format(interaction_table,
            interactiontype_table, interactionproduct_table, message_table, repactiontype_table,sent_email_vod__c_table,
            message_table, messagetopic_table, interactionaccount_table)

    rpt_df = spark.sql(query)
    email_count_rpt = rpt_df.first().c
    print(f"Emails count from rpt: {email_count_rpt}")

    emails_table = f"{base_adl_url}bronze/emails/"
    query = 'SELECT COUNT(*) AS c FROM parquet.`{}`'.format(emails_table)
    bronze_df = spark.sql(query)
    email_count_bronze = bronze_df.first().c
    print(f"Emails count from bronze: {email_count_bronze}")

    assert email_count_bronze == email_count_rpt
