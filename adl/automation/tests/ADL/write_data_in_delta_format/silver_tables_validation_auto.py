import pytest
import os

from adl.automation.utils.data_reader import DataReader
from adl.automation.utils import Constants
from adl.automation.utils.config_loader import load_config


customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])
base_rpt_url = str(environment.get("adl")["base_rpt_url"])

bronze_folder_names = Constants.BRONZE_TABLES
silver_folder_names = Constants.SILVER_TABLE

# TC-10278:Validate that recordclasses(interactionId) count is equal in silver folder and RPT folder
@pytest.mark.tc10278
def test_record_classes_interaction_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.select('interactionId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses/"
    rpt_table = f"{base_rpt_url}interaction/"

    rpt_df = spark.sql("SELECT count(distinct(interactionId)) as count_interactions FROM parquet.`{}` \
    where interactionId in (select interactionId FROM parquet.`{}`)".format(rpt_table, adl_table))

    assert rpt_df.first().count_interactions == df.count()


# TC-10279:Validate that recordclasses(Interaction_accountId) count is equal in silver folder and RPT folder
@pytest.mark.tc10279
def test_record_classes_interaction_account_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.select('interactionAccountId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses/"
    rpt_table = f"{base_rpt_url}interactionaccount/"

    rpt_df = spark.sql("select count(distinct(accountId)) count_account_id FROM parquet.`{}` \
    where accountId in (select interactionAccountId FROM parquet.`{}`)".format(rpt_table, adl_table))

    assert rpt_df.first().count_account_id == df.count()


# TC-10280:Validate that recordclasses(messageId) count is equal in silver folder and RPT folder
@pytest.mark.tc10280
def test_record_classes_message_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.select('messageId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses/"
    rpt_table = f"{base_rpt_url}message/"

    rpt_df = spark.sql("select count(distinct(messageId)) as message_count FROM parquet.`{}` \
    where messageId in (select messageId FROM parquet.`{}`)".format(rpt_table, adl_table))

    assert rpt_df.first().message_count == df.count()


# TC-10281:Validate that recordclasses(suggestion_repId) count is equal in silver folder and RPT folder
@pytest.mark.tc10281
def test_record_classes_suggestion_rep_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.select('suggestion_repId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses/"
    rpt_table = f"{base_rpt_url}rep/"

    rpt_df = spark.sql("select count(distinct(repId)) as suggestion_rep_count FROM parquet.`{}` \
    where repId in (select suggestion_repId FROM parquet.`{}`)".format(rpt_table, adl_table))

    assert rpt_df.first().suggestion_rep_count == df.count()


# TC-10284:Validate that emails in recordclasses_dse are also in RPT folder interaction
@pytest.mark.tc10284
def test_record_classes_dse_email_verification(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses_dse")
    df = df.select('interactionId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses_dse/"
    rpt_table = f"{base_rpt_url}interaction/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where InteractionId in (select distinct(interactionId) FROM parquet.`{}`)".format(rpt_table, adl_table))

    assert rpt_df.first().interaction_count == df.count()


# TC-10285:Validate that sent emails in recordclasses_dse are also in RPT folder interaction
@pytest.mark.tc10285
def test_record_classes_dse_sent_emails_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses_dse")
    df = df.filter("recordclass='Email only with Interaction completed'")
    df = df.select('interactionId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses_dse/"
    rpt_table = f"{base_rpt_url}interaction/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as sent_emails_count FROM parquet.`{}` \
    where recordclass='Email only with Interaction completed' and interactionId in \
    (select distinct(interactionId) FROM parquet.`{}` where isCompleted=1)".format(adl_table, rpt_table))

    assert rpt_df.first().sent_emails_count == df.count()


# TC-10286:Validate that interactionsaccounts in recordclasses_dse are also in RPT folder interactionaccount
@pytest.mark.tc10286
def test_record_classes_dse_interaction_account_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses_dse")
    df = df.select('interactionAccountId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses_dse/"
    rpt_table = f"{base_rpt_url}interactionaccount//"

    rpt_df = spark.sql("select count(distinct(interactionaccount)) as interaction_account_count FROM parquet.`{}` \
    where interactionId in (select distinct(interactionaccount) FROM parquet.`{}`)".format(adl_table, rpt_table))

    assert rpt_df.first().interaction_account_count == df.count()


# TC-10287:Validate that messages in recordclasses_dse are also in RPT folder message
@pytest.mark.tc10287
def test_record_classes_dse_messages_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses_dse")
    df = df.select('messageId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses_dse/"
    rpt_table = f"{base_rpt_url}interactionaccount//"

    rpt_df = spark.sql("select count(distinct(messageId)) as message_count FROM parquet.`{}` \
    where messageId in (select distinct(messageId) FROM parquet.`{}`)".format(adl_table, rpt_table))

    assert rpt_df.first().message_count == df.count()


# TC-10288:Validate that dserunId in recordclasses_dse are also in RPT dserun
@pytest.mark.tc10289
def test_record_classes_dse_dserunid_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses_dse")
    df = df.select('dseRunId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses_dse/"
    rpt_table = f"{base_rpt_url}interactionaccount//"

    rpt_df = spark.sql("select count(distinct(dseRunId)) as dse_run_count FROM parquet.`{}` \
    where dseRunId in (select distinct(runId) FROM parquet.`{}`)".format(adl_table, rpt_table))

    assert rpt_df.first().dse_run_count == df.count()


# TC-10288:Validate that dserunId in recordclasses_dse are also in RPT dserun
@pytest.mark.tc10289
def test_record_classes_dse_dserunid_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses_dse")
    df = df.select('dseRunId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses_dse/"
    rpt_table = f"{base_rpt_url}interactionaccount//"

    rpt_df = spark.sql("select count(distinct(dseRunId)) as dse_run_count FROM parquet.`{}` \
    where dseRunId in (select distinct(runId) FROM parquet.`{}`)".format(adl_table, rpt_table))

    assert rpt_df.first().dse_run_count == df.count()


# TC-10290:Validate that repTeam in recordclasses_dse are also in RPT repteam
@pytest.mark.tc10290
def test_record_classes_dse_repteam_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses_dse")
    df = df.select('suggestionRepTeamId').distinct()

    adl_table = f"{base_adl_url}silver/recordclasses_dse/"
    rpt_table = f"{base_rpt_url}interactionaccount//"

    rpt_df = spark.sql("select count(distinct(suggestionRepTeamId)) as rep_team_count FROM parquet.`{}` \
    where suggestionRepTeamId in (select distinct(repTeamId) FROM parquet.`{}`)".format(adl_table, rpt_table))

    assert rpt_df.first().rep_team_count == df.count()


# TC-10291:Validate that repTeam in silver/recordclasses_dse are also in bronze/rep_team_rep
@pytest.mark.tc10291
def test_record_classes_dse_repteam_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses_dse")
    df = df.select('suggestionRepTeamId').distinct()

    silver_table = f"{base_adl_url}silver/recordclasses_dse/"
    bronze_table = f"{base_adl_url}bronze/rep_team_rep/"

    rpt_df = spark.sql("select count(distinct(suggestionRepTeamId)) as rep_team_count FROM parquet.`{}` \
    where suggestionRepTeamId in (select distinct(repTeamId) FROM parquet.`{}`)".format(silver_table, bronze_table))

    assert rpt_df.first().rep_team_count == df.count()


# TC-10291:Validate that repTeam in silver/recordclasses_dse are also in bronze/rep_team_rep
@pytest.mark.tc10291
def test_record_classes_dse_repteam_count(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses_dse")
    df = df.select('suggestionRepTeamId').distinct()

    silver_table = f"{base_adl_url}silver/recordclasses_dse/"
    bronze_table = f"{base_adl_url}bronze/rep_team_rep/"

    rpt_df = spark.sql("select count(distinct(suggestionRepTeamId)) as rep_team_count FROM parquet.`{}` \
    where suggestionRepTeamId in (select distinct(repTeamId) FROM parquet.`{}`)".format(silver_table, bronze_table))

    assert rpt_df.first().rep_team_count == df.count()


# TC-10292:Validate that DSE_runId in silver/recordclasses_dse are also in bronze/dse_score
@pytest.mark.tc10292
def test_record_classes_dse_are_in_bronze(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses_dse")
    df = df.select('dseRunId').distinct()

    silver_table = f"{base_adl_url}silver/recordclasses_dse/"
    bronze_table = f"{base_adl_url}bronze/dse_score/"

    rpt_df = spark.sql("select count(distinct(dseRunId)) as run_id_count FROM parquet.`{}` \
    where dseRunId in (select distinct(runId) FROM parquet.`{}`)".format(silver_table, bronze_table))

    assert rpt_df.first().run_id_count == df.count()


# TC-10293:Validate “Email only with Interaction completed“ recordclass
@pytest.mark.tc10293
def test_record_classes_interaction_completed(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.filter("recordclass = 'Email only with Interaction completed'").select('interactionId').distinct()

    emails_table = f"{base_adl_url}bronze/emails/"
    suggestion_table = f"{base_adl_url}bronze/suggestion/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where interactionId not in (select interactionId FROM parquet.`{}`) \
    and isCompleted=1 and interactionId is not null".format(emails_table, suggestion_table))

    assert rpt_df.first().interaction_count == df.count()


# TC-10294:Validate “Email only with Interaction not_completed“ recordclass
@pytest.mark.tc10294
def test_record_classes_interaction_not_completed(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.filter("recordclass = 'Email only with Interaction not_completed'").select('interactionId').distinct()

    emails_table = f"{base_adl_url}bronze/emails/"
    suggestion_table = f"{base_adl_url}bronze/suggestion/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where interactionId not in (select interactionId FROM parquet.`{}`) \
    and isCompleted=0 and interactionId is not null".format(emails_table, suggestion_table))

    assert rpt_df.first().interaction_count == df.count()


# TC-10295:Validate “Email and suggestion with Interaction completed“ recordclass
@pytest.mark.tc10295
def test_record_classes_suggestion_and_interaction_completed(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.filter("recordclass = 'Email and suggestion with Interaction completed'").select('interactionId').distinct()

    emails_table = f"{base_adl_url}bronze/emails/"
    suggestion_table = f"{base_adl_url}bronze/suggestion/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where interactionId not in (select interactionId FROM parquet.`{}`) \
    and isCompleted=1 and interactionId is not null".format(emails_table, suggestion_table))

    assert rpt_df.first().interaction_count == df.count()


# TC-10296:Validate “Email and suggestion with Interaction not_completed“ recordclass
@pytest.mark.tc10296
def test_record_classes_suggestion_and_interaction_not_completed(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.filter("recordclass = 'Email and suggestion with Interaction not_completed'").select('interactionId').distinct()

    emails_table = f"{base_adl_url}bronze/emails/"
    suggestion_table = f"{base_adl_url}bronze/suggestion/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where interactionId not in (select interactionId FROM parquet.`{}`) \
    and isCompleted=0 and interactionId is not null".format(emails_table, suggestion_table))

    assert rpt_df.first().interaction_count == df.count()


# TC-10297:Validate “Email suggestion without Interaction“ recordclass
@pytest.mark.tc10297
def test_record_classes_without_interaction(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.filter("recordclass = 'Email suggestion without Interaction'").select('interactionId').distinct()

    emails_table = f"{base_adl_url}bronze/emails/"
    suggestion_table = f"{base_adl_url}bronze/suggestion/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where interactionId not in (select interactionId FROM parquet.`{}`) \
    and isCompleted=0 and interactionId is not null".format(emails_table, suggestion_table))

    assert rpt_df.first().interaction_count == df.count()


# TC-10298:Validate “Visit only with Interaction completed“ recordclass
@pytest.mark.tc10298
def test_record_classes_visit_only_interaction_completed(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.filter("recordclass = 'Visit only with Interaction completed'").select('interactionId').distinct()

    visits_table = f"{base_adl_url}bronze/visits/"
    suggestion_table = f"{base_adl_url}bronze/suggestion/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where interactionId not in (select interactionId FROM parquet.`{}`) \
    and isCompleted=1 and interactionId is not null".format(visits_table, suggestion_table))

    assert rpt_df.first().interaction_count == df.count()


# TC-10299:Validate “Visit only with Interaction not_completed“ recordclass
@pytest.mark.tc10299
def test_record_classes_visit_only_interaction__not_completed(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.filter("recordclass = 'Visit only with Interaction not_completed'").select('interactionId').distinct()

    visits_table = f"{base_adl_url}bronze/visits/"
    suggestion_table = f"{base_adl_url}bronze/suggestion/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where interactionId not in (select interactionId FROM parquet.`{}`) \
    and isCompleted=0 and interactionId is not null".format(visits_table, suggestion_table))

    assert rpt_df.first().interaction_count == df.count()


# TC-10300:Validate “Visit and suggestion with Interaction completed“ recordclass
@pytest.mark.tc10300
def test_record_classes_visit_and_suggestion_interaction_completed(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.filter("recordclass = 'Visit and suggestion with Interaction completed'").select('interactionId').distinct()

    visits_table = f"{base_adl_url}bronze/visits/"
    suggestion_table = f"{base_adl_url}bronze/suggestion/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where interactionId in (select interactionId FROM parquet.`{}`) \
    and isCompleted=1 and interactionId is not null".format(visits_table, suggestion_table))

    assert rpt_df.first().interaction_count == df.count()


# TC-10301:Validate “Visit and suggestion with Interaction not_completed“ recordclass
@pytest.mark.tc10301
def test_record_classes_visit_and_suggestion_interaction_not_completed(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.filter("recordclass = 'Visit and suggestion with Interaction not_completed'").select('interactionId').distinct()

    visits_table = f"{base_adl_url}bronze/visits/"
    suggestion_table = f"{base_adl_url}bronze/suggestion/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where interactionId in (select interactionId FROM parquet.`{}`) \
    and isCompleted=0 and interactionId is not null".format(visits_table, suggestion_table))

    assert rpt_df.first().interaction_count == df.count()


# TC-10302:Validate “Visit suggestion without Interaction“ recordclass
@pytest.mark.tc10302
def test_record_classes_visit_suggestion_without_interaction(spark):
    df = DataReader.read_adl_parquet_file(spark, "silver", "recordclasses")
    df = df.filter("recordclass = 'Visit suggestion without Interaction'").select('interactionId').distinct()

    visits_table = f"{base_adl_url}bronze/visits/"
    suggestion_table = f"{base_adl_url}bronze/suggestion/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as interaction_count FROM parquet.`{}` \
    where interactionId in (select interactionId FROM parquet.`{}`) \
    and isCompleted=0 and interactionId is not null".format(visits_table, suggestion_table))

    assert rpt_df.first().interaction_count == df.count()
