import pytest
import os

from adl.automation.utils.data_reader import DataReader
from adl.automation.utils import Constants
from adl.automation.utils.config_loader import load_config


customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])
base_rpt_url = str(environment.get("adl")["base_rpt_url"])

bronze_folder_names = Constants.BRONZE_TABLES
silver_folder_names = Constants.SILVER_TABLE


# TC-10214:Validate that user can read and get data from files under Bronze folder
@pytest.mark.tc10214
@pytest.mark.parametrize("table_name", bronze_folder_names)
def test_bronze_tables_validation(spark, table_name):
    df = DataReader.read_adl_parquet_file(spark, "bronze", table_name)

    assert df.count() > 0


# TC-10216:Validate that user can read and get data from files under Silver folder
@pytest.mark.tc10216
@pytest.mark.parametrize("table_name", silver_folder_names)
def test_silver_tables_validation(spark, table_name):
    df = DataReader.read_adl_parquet_file(spark, "silver", table_name)

    assert df.count() > 0

# TC-10217:Validate that visits count is equal in bronze folder and RPT folder
@pytest.mark.tc10217
def test_visit_count_validation(spark):
    df = DataReader.read_adl_parquet_file(spark, "bronze", "visits")
    df = df.select('interactionId').distinct()

    adl_table = f"{base_adl_url}bronze/visits/"
    rpt_table = f"{base_rpt_url}interaction/"

    rpt_df = spark.sql("select count(distinct(interactionId)) as count_interactions \
    FROM parquet.`{}` where interactionId in (select interactionId FROM parquet.`{}`)".format(rpt_table, adl_table))

    assert rpt_df.first().count_interactions == df.count()