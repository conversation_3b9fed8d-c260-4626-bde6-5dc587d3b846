import pytest
import os
from adl.automation.s3.s3_manager import AWSManager
from common.pyUtils.logger import get_module_logger
from adl.automation.utils.config_loader import load_config


customer = os.environ.get("ENV_NAME")
adl_branch = os.environ.get("ADL_BRANCH")
environment = load_config(f'adl/automation/config/{customer}').data

BUCKET_COMMON = str(environment.get("adl")["bucket_common"])
base_common_key = str(environment.get("adl")["base_common_key"]).replace("{}", adl_branch)
base_data_key = str(environment.get("adl")["base_data_key"]).replace("{}", adl_branch)

master_csv_path = os.path.join(os.path.abspath(os.curdir), "adl/data/dictionary.csv")
csv_path = base_data_key + "dictionary.csv"
logger = get_module_logger(__name__)


# TC-11154:Validate that master data dictionary was created
@pytest.mark.tc11154
def test_validate_that_master_data_dictionary_was_created(spark):
    s3_client = AWSManager.get_instance("s3")
    response = s3_client.s3_folder_file_exists(BUCKET_COMMON, base_common_key, "dictionary.csv")
    assert response


# TC-11156:Validate that "common" columns are renamed based on master data dictionary
@pytest.mark.tc11156
def test_validate_that_common_columns_are_renamed_based_on_master_data_dictionary(spark):
    df_csv = spark.read.options(sep='|', header='true').csv(csv_path)
    df_csv = df_csv.filter("Customer == 'All'").select("ADLStandardFieldsName")

    df_master = spark.read.options(sep='|', header='true').csv(master_csv_path)
    df_master = df_master.filter("Customer == 'All'").select("ADLStandardFieldsName")

    result = df_csv.subtract(df_master)
    assert result.rdd.isEmpty()
