import pytest
import os


from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])

recordclasses = f"{base_adl_url}silver/recordclasses/"
cri_scores = f"{base_adl_url}silver/cri_score/"
suggestions = f"{base_adl_url}bronze/suggestions/"


# TC-11111:Validate calculation of CRI metric number 1 "Email Open Probability"
@pytest.mark.tc11111
def test_tc_11111(spark):
    id = spark.sql("select first(InteractionRepId) as InteractionRepId, first(InteractionAccountId) as "
                   "InteractionAccountId FROM parquet.`{}`".format(recordclasses))
    query = "with result2 as ( with result1 as (select first(interactionRepId) as repId,first(interactionAccountId) " \
            "as accountId,interactionYear,interactionMonth,SUM(emailOpened) as opentotal,COUNT(emailOpened) as " \
            "senttotal, first(interactionStartDateLocal) as Interaction_startDate, " \
            "first(SUBSTRING(interactionStartDateLocal, 6, 2)) as mes1 FROM parquet.`{}` where interactionRepId={} " \
            "and interactionAccountId={} and emailOpened is not null group by interactionYear,interactionMonth order " \
            "by Interaction_startDate asc) select Interaction_startDate,CONCAT(repId,'_',accountId) as rep_account," \
            "CONCAT(interactionYear,mes1) as year_month,Sum(opentotal) Over ( Order by Interaction_startDate ) as " \
            "sumopentotal,Sum(senttotal) Over ( Order by Interaction_startDate ) as sumsenttotal from result1) " \
            "select a.*,ROUND(a.sumopentotal/a.sumsenttotal,2) as email_open_score_QA_calculated,b.emailOpenScore " \
            "as Email_open_score_from_cri_table from result2 a join parquet.`{}` " \
            "b on a.rep_account=b.interactionRepAccount and a.year_month=b.interactionYearMonth " \
            "order by a.Interaction_startDate asc"
    df = spark.sql(query.format(recordclasses, id.select('InteractionRepId'),
                                id.select('InteractionAccountId'), cri_scores))
    assert df.select('email_open_score_calculated') == df.select('Email_open_score_from_cri')

# TC-11113:Validate calculation of CRI metric number 3 "Average Number of Completed Quarterly Visits"
@pytest.mark.tc11113
def test_tc_11113(spark):
    id = spark.sql("select first(InteractionRepId) as InteractionRepId, first(InteractionAccountId) "
                   "as InteractionAccountId, first(interactionTypeId) as interactionTypeId FROM "
                   "parquet.`{}`".format(recordclasses))
    query = "with result3 as( with result2 as( with result1 as (select interactionTypeName,interactionAccountId," \
            "interactionYear,interactionMonth,interactionStartDateLocal,SUBSTRING(interactionStartDateLocal, 6, 2) " \
            "as mes1 FROM parquet.`{}` where interactionRepId={} and interactionAccountId={} and " \
            "interactionTypeId={} order by interactionStartDateLocal asc) select first(interactionTypeName) " \
            "as interactionType, first(interactionAccountId) as rep_account,interactionYear,mes1, " \
            "CONCAT(interactionYear,mes1) as year_month,COUNT(interactionTypeName) as visitspermonth from result1 " \
            "group by interactionYear,mes1 order by year_month) select *,Sum(visitspermonth) Over " \
            "( Order by year_month ) as visitstotal,COUNT(visitspermonth) Over ( Order by year_month ) " \
            "as countvisits from result2) select a.*,ROUND(a.visitstotal/a.countvisits,2) as visit_score_calculated," \
            "b.visitScore as visit_score_from_cri from result3 a join parquet.`{}` b on " \
            "a.rep_account=b.interactionRepAccount and a.year_month=b.interactionYearMonth order by a.year_month asc"
    df = spark.sql(query.format(recordclasses, id.select('InteractionRepId'), id.select('InteractionAccountId'),
                                id.select('interactionTypeId'), cri_scores))
    assert df.select('visit_score_calculated') == df.select('visit_score_from_cri')

# TC-11115:Validate calculation of CRI metric number 5 "Suggestion Visit Completion Probability"
@pytest.mark.tc11115
def test_tc_11115(spark):
    id = spark.sql("select first(repId) as repId, first(accountId) as accountId, first(detailRepActionTypeId) as "
                   "detailRepActionTypeId FROM parquet.`{}`".format(suggestions))
    query = "with result6 as( with result5 as( with result4 as( with result3 as( with result2 as( with result1 as(" \
            "select suggestionReferenceId,detailRepActionTypeId as RepActionTypeId,actionTaken,isSuggestionCompleted," \
            "suggestedDate,repId,accountId,CONCAT(repId,'_',accountId) as rep_account, " \
            "concat(left(SUBSTRING_INDEX(suggestedDate,'-',2),4), right(SUBSTRING_INDEX(suggestedDate,'-',2),2)) as " \
            "suggestion_YearMonth FROM parquet.`{}` where repId={} and accountId={} and detailRepActionTypeId={} ) " \
            "select *,count(suggestionReferenceId) over(partition by " \
            "suggestionReferenceId,rep_account,suggestion_YearMonth) as unique_suggestion_visit_count," \
            "MAX(suggestedDate) over(partition by rep_account,suggestion_YearMonth) as maxsuggestedDatepermonth from " \
            "result1) select *,IF(suggestedDate=maxsuggestedDatepermonth,1,0) as lastFlag from result2 order by " \
            "suggestedDate desc)select *,count(repId) over(partition by suggestion_YearMonth,lastFlag=1 order by " \
            "suggestedDate desc) as total_suggestion_mo from result3 order by suggestedDate desc) " \
            "select rep_account,first(RepActionTypeId),suggestion_YearMonth,max(suggestedDate) as suggestedDate," \
            "first(total_suggestion_mo) as total_suggestion_mo,sum(isSuggestionCompleted) as isSuggestionCompleted  " \
            "from result4 group by rep_account,suggestion_YearMonth ) select * from result5 order by " \
            "suggestion_YearMonth asc) select *,Sum(total_suggestion_mo) Over ( Order by suggestedDate asc ) " \
            "as suggestion_total,Sum(isSuggestionCompleted) Over ( Order by suggestedDate asc ) " \
            "as complete_total from result6"
    df = spark.sql(query.format(suggestions, id.select('repId'), id.select('accountId'),
                                id.select('detailRepActionTypeId')))
    assert df.select('complete_total') == df.select('suggestion_total')

# TC-11117:Validate calculation of CRI metric number 7 "Target Team Achievement"
# @pytest.mark.tc11117
# def test_tc_11117(spark):
#     IN HOLD, THE QUERY HAD TO BE REFACTORED
#     query = "select interactionRepAccount, targetsPeriodId, round(avg(score), 2) as targetAchievementScore from " \
#             "(select interactionRepAccount, targetsPeriodId, interactionTypeId, targetAchievement as score from ( " \
#             "select *, case when targetTotal is not null and targetTotal > 0 and targetCount is not null then targetCount / " \
#             "targetTotal else targetCount end as targetAchievement from " \
#             "(select interactionRepAccount, targetsPeriodId, interactionTypeId, sum(CASE WHEN target is not null THEN target ELSE 0 END)" \
#             " as targetTotal, count(CASE WHEN interactionIsCompleted = '1' THEN 1 END) as targetCount from rc group by " \
#             "interactionRepAccount, targetsPeriodId, interactionTypeId )) where interactionTypeId in (11,4)) " \
#             "where score is not null and score > 0 group by interactionRepAccount, targetsPeriodId"
#     df = spark.sql(query)
#     assert df.select('target_achievement_calculated')

# TC-11511:Validate calculation of CRI metric number 9 "criScoreSum"
@pytest.mark.tc11511
def test_tc_11511(spark):
    query = "SELECT interactionRepAccount,interactionYearMonth, 8-((CASE WHEN first(emailOpenScore) IS NULL THEN 1 " \
            "ELSE 0 END) + (CASE WHEN first(tenureScore) IS NULL THEN 1 ELSE 0 END) + (CASE WHEN first(visitScore) " \
            "IS NULL THEN 1 ELSE 0 END) + (CASE WHEN first(cadenceScore) IS NULL THEN 1 ELSE 0 END)  + " \
            "(CASE WHEN first(suggestionVisitScore) IS NULL THEN 1 ELSE 0 END) + (CASE WHEN " \
            "first(suggestionEmailScore) IS NULL THEN 1 ELSE 0 END)  + (CASE WHEN first(targetAchievementScore) " \
            "IS NULL THEN 1 ELSE 0 END) + (CASE WHEN first(channelScore) IS NULL THEN 1 ELSE 0 END)) " \
            "AS sum_of_not_nulls, Round(first(coalesce(emailOpenScore,0.00)+coalesce(tenureScore,0.00)+" \
            "coalesce(visitScore,0.00)+coalesce(cadenceScore,0.00)+coalesce(suggestionVisitScore,0.00)+" \
            "coalesce(suggestionEmailScore,0.00)+coalesce(targetAchievementScore,0.00)+" \
            "coalesce(channelScore,0.00)),2) as calculated_sum, first(criScoreSum) as criScoreSumFROM parquet.`{}` " \
            "group by interactionRepAccount,interactionYearMonth limit 10"
    df = spark.sql(query.format(cri_scores))
    assert df.select('calculated_sum') == df.select('criScoreSum')
    query = "with result1 as (SELECT interactionRepAccount,interactionYearMonth, 8-((CASE WHEN first(emailOpenScore) " \
            "IS NULL THEN 1 ELSE 0 END) + (CASE WHEN first(tenureScore) IS NULL THEN 1 ELSE 0 END)  + " \
            "(CASE WHEN first(visitScore) IS NULL THEN 1 ELSE 0 END) + (CASE WHEN first(cadenceScore) " \
            "IS NULL THEN 1 ELSE 0 END)  + (CASE WHEN first(suggestionVisitScore) IS NULL THEN 1 ELSE 0 END) + " \
            "(CASE WHEN first(suggestionEmailScore) IS NULL THEN 1 ELSE 0 END)  + " \
            "(CASE WHEN first(targetAchievementScore) IS NULL THEN 1 ELSE 0 END) + (CASE WHEN first(channelScore) " \
            "IS NULL THEN 1 ELSE 0 END)) AS sum_of_not_nulls,  Round(first(coalesce(emailOpenScore,0.00) + " \
            "coalesce(tenureScore,0.00)+coalesce(visitScore,0.00)+coalesce(cadenceScore,0.00)+coalesce" \
            "(suggestionVisitScore,0.00)+coalesce(suggestionEmailScore,0.00)+coalesce(targetAchievementScore,0.00)+" \
            "coalesce(channelScore,0.00)),2) as calculated_sum,  first(criScoreSum) as criScoreSum " \
            "FROM parquet.`{}` group by interactionRepAccount, interactionYearMonth limit 10) select * from result1" \
            " where calculated_sum != criScoreSum"
    df = spark.sql(query.format(cri_scores))
    assert df.rdd.isEmpty()

# TC-11513:Validate that CRI metrics 1 to 8, values are between 0-1
@pytest.mark.tc11513
def test_tc_11513(spark):
    query = "select * FROM parquet.`{}`  where suggestionVisitScore<0 or suggestionVisitScore>1 or visitScore<0 or " \
            "visitScore>1 or suggestionEmailScore<0 or suggestionEmailScore>1 or emailOpenScore<0 or " \
            "emailOpenScore>1 or tenureScore<0 or tenureScore>1 or interactionRepAccount<0 or " \
            "interactionRepAccount>1 or targetAchievementScore<0 or targetAchievementScore>1 or channelScore<0 or " \
            "channelScore>1 or cadenceScore<0 or cadenceScore>1"
    df = spark.sql(query.format(cri_scores))
    assert df.rdd.isEmpty()
