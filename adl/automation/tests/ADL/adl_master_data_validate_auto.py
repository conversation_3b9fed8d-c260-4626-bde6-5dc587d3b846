import pytest
import os

from adl.automation.utils.file_utils import FileUtils
from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])

dictionary = f"{base_adl_url}dictionary.csv"
final_dataset = f"{base_adl_url}silver/final_dataset/"
path_dictionary = os.path.realpath('adl/data/dictionary.csv')


# TC-11155:Validate that master data dictionary was populated correctly according selected customer
@pytest.mark.tc11155
def test_tc_11155(spark):
    df = spark.read.csv(dictionary, header=True, sep="|")
    df = df.select("Customer")

    dictionary_df = spark.read.csv(path_dictionary, header=True, sep="|")
    dictionary_df = dictionary_df.select("Customer")

    assert FileUtils.is_equal_both_dataframes(df, dictionary_df)


# TC-11142:Validate that "customer specific" columns are renamed based on master data dictionary
@pytest.mark.parametrize("select_customer", ["pfizerus", "All", "lillyus"])
@pytest.mark.tc11142
def test_tc_11142(spark, select_customer):
    df = spark.read.parquet(final_dataset)
    df_columns = df.columns
    df_columns.sort()

    dictionary_df = spark.read.csv(path_dictionary, header=True, sep="|")
    dictionary_df = dictionary_df.filter(dictionary_df.Customer == select_customer)
    dictionary_df = dictionary_df.select('ADLStandardFieldsName')

    dictionary_df = dictionary_df.collect()
    for i, row in enumerate(dictionary_df):
        dictionary_df[i] = str(row).replace("Row(ADLStandardFieldsName='", "")
    for i, row in enumerate(dictionary_df):
        dictionary_df[i] = str(row).replace("')", "")
    dictionary_df.sort()

    assert df_columns == dictionary_df
