import pytest
import os
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader


customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])

# TC-11824:Validate that “tenure“ column was added to account table
# TC-11825:Validate that “hcpGender“ column was added to account table
# TC-11826:Validate that “hcpCred“ column was added to account table
# TC-11827:Validate that “hcpSpec“ column was added to account table
@pytest.mark.parametrize("new_column", ["tenure", "hcpGender", "hcpCred", "hcpSpec"])
@pytest.mark.tc11824
@pytest.mark.tc11825
@pytest.mark.tc11826
@pytest.mark.tc11827
def test_validate_that_new_columns_were_added_to_account_table(spark, new_column):
    df = DataReader.read_adl_parquet_file(spark, "bronze", "account")
    assert new_column in df.columns


# TC-11832:Validate that “tenure“ column was added to final_dataset table
# TC-11833:Validate that “hcpGender“ column was added to final_dataset table
# TC-11834:Validate that “hcpCred“ column was added to final_dataset table
# TC-11835:Validate that “hcpSpec“ column was added to final_dataset table
@pytest.mark.parametrize("new_column", ["tenure", "hcpGender", "hcpCred", "hcpSpec"])
@pytest.mark.tc11832
@pytest.mark.tc11833
@pytest.mark.tc11834
@pytest.mark.tc11835
def test_validate_that_new_columns_were_added_to_dataset_table(spark, new_column):
    final_dataset_table = f"{base_adl_url}silver/final_dataset"
    account_table = f"{base_adl_url}bronze/account"

    query = "WITH RESULT AS( SELECT a.accountId AS account1,a.{} AS valueA,b.accountId AS account2,b.{} AS valueB \
            FROM parquet.`{}` a INNER JOIN parquet.`{}` b ON a.accountId=b.accountId ) \
            SELECT * FROM RESULT WHERE valueA!=valueB".format(new_column, new_column, final_dataset_table, account_table)

    df = spark.sql(query)

    assert df.rdd.isEmpty()
