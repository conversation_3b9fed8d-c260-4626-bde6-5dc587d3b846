import pytest
import os
from common.pyUtils.logger import get_module_logger
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.file_utils import FileUtils

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])
adl_branch = os.environ.get("ADL_BRANCH")
base_adl_config = str(environment.get("adl")["base_data_key"]).replace("{}", adl_branch)

logger = get_module_logger(__name__)


# TC-9487 : Validate that correct data is displayed for each loading version
@pytest.mark.tc9487
def test_validate_that_correct_data_is_displayed_for_each_loading_version(spark):
    url = base_adl_url.replace('data', 'config')
    df = spark.read.format("delta").load(f"{url}deltaLoading/")
    df = df.orderBy(df.table_number.asc())
    df = df.select("table_name", "sql_query")
    df.show()

    path = f"{base_adl_config}loading.csv"
    df_csv = spark.read.options(sep='|', header='true', multiLine=True).csv(path)
    df_csv = df_csv.orderBy(df_csv.table_number.asc())
    df_csv = df_csv.select("table_name", "sql_query")
    df_csv.show()

    assert FileUtils.is_equal_both_dataframes(df, df_csv)
