import pytest
import os
import re


from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])

account = f"{base_adl_url}bronze/account"
rep = f"{base_adl_url}bronze/rep/"
visits = f"{base_adl_url}bronze/visits/"
rep_account_assignment = f"{base_adl_url}bronze/rep_account_assignment/"
emails = f"{base_adl_url}bronze/emails/"
suggestions = f"{base_adl_url}bronze/suggestions/"
camelcase_pattern = r'^[a-z0-9]+([A-Z][a-z0-9]*)*$'


# TC-10937:Validate that columns in account table were renamed
# TC-10938:Validate that columns in rep table were renamed
# TC-10939:Validate that columns in visits table were renamed
# TC-10940:Validate that columns in rep_account_assignment table were renamed
# TC-10941:Validate that columns in emails table were renamed
# TC-10942:Validate that columns in suggestions table were renamed
@pytest.mark.parametrize("select_table", [account, rep, visits, rep_account_assignment, emails, suggestions])
@pytest.mark.tc10937
@pytest.mark.tc10938
@pytest.mark.tc10939
@pytest.mark.tc10940
@pytest.mark.tc10941
@pytest.mark.tc10942
def test_validate_rename_columns_structure(spark, select_table):
    df = spark.sql("select * from parquet.`{}` limit 1".format(select_table))
    for col in df.columns:
        assert "_akt" not in col, f"The column {col} contain '_akt' in the mane"
        assert "_vod__c" not in col, f"The column {col} contain '_vod__c' in the mane"
        assert "_" not in col, f"The column {col} contain '_' in the mane"
        assert re.match(camelcase_pattern, col), f"The column {col} " \
                                                 f"not respect does not conform to camelcase naming pattern"
