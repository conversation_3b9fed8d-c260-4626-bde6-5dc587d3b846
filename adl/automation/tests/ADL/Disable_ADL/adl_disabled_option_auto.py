import pytest
import os
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
from adl.automation.rundeck.adl_job_params_builder import Builder
from adl.automation.utils import Constants

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
adl_branch = os.environ.get("ADL_BRANCH")
feature = os.environ.get("FEATURE_STORE")
queries = load_config('adl/automation/resources/queries_final_dataset').data
adl_job_id = str(environment.get("rundeck")["run_job_id"])


# TC-13679 : validate that a new disableADL column added with default=0(enabled), into CustomerADLConfig metadata
# TC-13681 : validate that run ADL job runs fine and populate output into s3, when enable back adl(disableADL=0)
@pytest.mark.tc13679
def test_disabled_adl_false(spark):
    property_value = DataReader.get_metadata_property_value("disableADL")
    assert property_value == 0
    rundeck = RundeckJobExecutor()
    status = rundeck.get_status_last_execution(adl_job_id)

    assert status == Constants.SUCCEEDED_STATUS


# TC-13680 : Validate that run ADL job skip to run adl, when Disable ADL(disableADL=1) in metadata
@pytest.mark.tc13680
def test_disabled_adl_true(spark):
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_adl_branch(adl_branch) \
        .build_feature_store(feature) \
        .get_result()
    DataReader.update_metadata_property_value("disableADL", 1)
    property_value = DataReader.get_metadata_property_value("disableADL")
    assert property_value == 1

    rundeck.run_job(builder, adl_job_id)
    status = rundeck.get_status_last_execution(adl_job_id)

    assert status == Constants.SUCCEEDED_STATUS

    expected_log_entry = "ADL execution has been disabled for this customer/env in metadata."
    entry_is_present = rundeck.validate_output_contains_log_entry(adl_job_id, expected_log_entry)

    assert entry_is_present

    DataReader.update_metadata_property_value("disableADL", 0)


