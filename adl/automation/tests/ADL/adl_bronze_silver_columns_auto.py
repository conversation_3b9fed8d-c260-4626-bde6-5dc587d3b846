import pytest
import os
import re
from adl.automation.utils.config_loader import load_config

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])


# TC-10943:Validate that columns in dse_score table were renamed
# TC-10944:Validate that columns in rep_product_authorization table were renamed
# TC-10945:Validate that columns in rep_team_rep table were renamed
# TC-10946:Validate that columns in approved_document table were renamed
# TC-10947:Validate that columns in accountproduct table were renamed
# TC-10948:Validate that columns in strategy_target table were renamed
@pytest.mark.tc10943
@pytest.mark.tc10944
@pytest.mark.tc10945
@pytest.mark.tc10946
@pytest.mark.tc10947
@pytest.mark.tc10948
@pytest.mark.parametrize("table", ["dse_score", "rep_product_authorization", "rep_team_rep", "approved_document",
                                   "accountproduct", "strategy_target"])
def test_validate_that_columns_were_renamed(spark, table):
    dse_score_table = f"{base_adl_url}/bronze/{table}"

    df = spark.read.parquet(dse_score_table)
    actual_columns = df.columns
    pattern = re.compile("^[a-z][a-z0-9]+([A-Z][A-z0-9]*[a-z0-9]*)*")
    bad_columns = list(filter(lambda x: not bool(pattern.match(x)), actual_columns))
    assert len(bad_columns) == 0, bad_columns
