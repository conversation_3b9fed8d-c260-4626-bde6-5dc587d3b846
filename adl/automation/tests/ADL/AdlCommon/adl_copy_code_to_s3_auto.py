import pytest
import os
import logging
import delayed_assert as assertion
from adl.automation.s3.s3_manager import AWSManager
from adl.automation.utils import Constants
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
from adl.automation.rundeck.adl_job_params_builder import Builder
from adl.automation.utils.config_loader import load_config

logger = logging.getLogger(__name__)


customer = os.environ.get("ENV_NAME")
branch = os.environ.get("ADL_BRANCH")
environment = load_config(f'adl/automation/config/{customer}').data
common_bucket = str(environment.get("adl")["bucket_common"])
adl_bucket = str(environment.get("adl")["bucket"])
base_adl_key = str(environment.get("adl")["base_adl_key"]).replace("data/{}/", "")
base_key = str(environment.get("adl")["base_common_key"])
copy_code_job_id = str(environment.get("rundeck")["run_copy_s3_job_id"])


# TC-12590: Validate the COPY_CODE_TO_S3 run deck job creates selected RB folder on aktana-bdp-adlcommon S3 bucket
@pytest.mark.tc12590
def test_copy_code_s3_job():
    client = AWSManager.get_instance()
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_adl_branch(branch)\
        .get_result()
    rundeck.run_job(builder, copy_code_job_id, "COPY_CODE")
    status = rundeck.get_status_last_execution(copy_code_job_id)

    assert status == Constants.SUCCEEDED_STATUS

    common_prefix = base_key.format(branch)
    logger.info(f"ADL common Prefix: {common_prefix}")
    folder_exists = client.s3_folder_exists(common_bucket, common_prefix)
    code_files = client.s3_folder_contains_files(common_bucket, f"{common_prefix}code", "py")
    data_files = client.s3_folder_contains_files(common_bucket, f"{common_prefix}data", "csv")
    lib_files = client.s3_folder_contains_files(common_bucket, f"{common_prefix}lib", "jar")

    assertion.expect(folder_exists, "common folder doesn't exist")
    assertion.expect(code_files, "code files were not created")
    assertion.expect(data_files, "data files were not created")
    assertion.expect(lib_files, "lib files were not created")

    assertion.assert_expectations()


# TC-9424 : Validate all sub-folders were created on testing bucket
@pytest.mark.tc9424
def test_sub_folder_were_created():
    client = AWSManager.get_instance('s3')
    logger.info("adl folder validation")
    adl_folder_exists = client.s3_folder_exists(adl_bucket, base_adl_key)
    assertion.expect(adl_folder_exists, "common folder doesn't exist")
    logger.info("config folder validation")
    adl_folder_exists = client.s3_folder_exists(adl_bucket, f"{base_adl_key}config/")
    assertion.expect(adl_folder_exists, "common folder doesn't exist")
    logger.info("data folder validation")
    adl_folder_exists = client.s3_folder_exists(adl_bucket, f"{base_adl_key}data/")
    assertion.expect(adl_folder_exists, "common folder doesn't exist")
    logger.info("logs folder validation")
    adl_folder_exists = client.s3_folder_exists(adl_bucket, f"{base_adl_key}logs/")
    assertion.expect(adl_folder_exists, "common folder doesn't exist")

    assertion.assert_expectations()
