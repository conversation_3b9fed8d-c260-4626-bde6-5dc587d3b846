import pytest
import os
import datetime
from adl.automation.utils import Constants
from adl.automation.rundeck.all_model_params_builder import AllModuleBuilder
from adl.automation.utils.config_loader import load_config
from adl.automation.constants import ANCHOR_JOB_ID
from adl.automation.utils.learning_api_utils import LeaningApiRequest
from adl.automation.utils.constans import Models
from adl.automation.utils.job_utils import JobUtils

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
run_job_id = str(environment.get("rundeck")["run_all_model_job_id"] + ANCHOR_JOB_ID)
run_default_job = str(environment.get("rundeck")["run_all_model_job_id"])

nightly_score = float(environment.get("rundeck")["anchor_time"]["nightly_score"])
nightly_score_default = float(environment.get("rundeck")["anchor_time"]["nightly_score_default"])
accuracy = float(environment.get("rundeck")["anchor_time"]["accuracy"])
old_nightly_score = float(environment.get("rundeck")["anchor_time"]["old_nightly_score"])
old_accuracy = float(environment.get("rundeck")["anchor_time"]["old_accuracy"])
parallel = float(environment.get("rundeck")["anchor_time"]["parallel"])
parallel_accuracy = float(environment.get("rundeck")["anchor_time"]["parallel_accuracy"])


# TC-9326 : Validate performance of ANCHOR Nightly Score (Regular) job
# TC-9328 : Validate performance of ANCHOR Accuracy job
# TC-9329 : Validate performance of OLD ANCHOR Nightly Score job
@pytest.mark.parametrize("module, driver, time, task5_cluster", [("anchor", "anchorDriver", nightly_score, ""),
                                                                 ("anchorAccuracy", "AnchorAccuracyReportDriver",
                                                                  accuracy, "terminateCluster"),
                                                                 ("anchorOld", "anchorOldDriver", old_nightly_score, "")])
@pytest.mark.tc9326
@pytest.mark.tc9328
@pytest.mark.tc9329
def test_performance_anchor_job(get_token, module, driver, time, task5_cluster):
    response = LeaningApiRequest.create_draft_models(get_token, Models.ANCHOR.value)
    assert response.status_code == 200
    learning_config_uid = response.json()[0].get("learningConfigUID")
    response = LeaningApiRequest.deploy_models(get_token, learning_config_uid)
    assert response.status_code == 200
    builder = AllModuleBuilder() \
        .build_selected_module(module) \
        .build_selected_driver(driver) \
        .build_task5(task5_cluster) \
        .get_result()
    run_duration, run_status = JobUtils.run_job_to_obtain_time(builder, run_job_id, Models.ANCHOR.value)
    max_duration = datetime.timedelta(minutes=time)
    assert run_status == Constants.SUCCEEDED_STATUS
    assert run_duration <= max_duration


# TC-9331 : Validate performance of PARALLEL ANCHOR job
# TC-9332 : Validate performance of PARALLEL ANCHOR Accuracy job
@pytest.mark.parametrize("module, driver, time, task5_cluster", [("anchor", "anchorDriver", parallel, ""),
                                                                 ("anchorAccuracy", "AnchorAccuracyReportDriver",
                                                                  parallel_accuracy, "terminateCluster")])
@pytest.mark.tc9331
@pytest.mark.tc9332
def test_performance_anchor_parallel_job(get_token, module, driver, time, task5_cluster):
    response = LeaningApiRequest.create_draft_models(get_token, Models.ANCHOR.value)
    learning_config_uid = response.json()[0].get("learningConfigUID")
    assert response.status_code == 200
    response = LeaningApiRequest.deploy_models(get_token, learning_config_uid)
    assert response.status_code == 200
    builder = AllModuleBuilder() \
        .build_selected_module(module) \
        .build_selected_driver(driver) \
        .build_model("anchor") \
        .build_task5(task5_cluster) \
        .get_result()
    run_duration, run_status = JobUtils.run_job_to_obtain_time(builder, run_job_id, Models.ANCHOR.value)
    max_duration = datetime.timedelta(minutes=time)
    assert run_status == Constants.SUCCEEDED_STATUS
    assert run_duration <= max_duration


# TC-13284 : Verify the Anchor job runs successfully and there is no performance degradations
@pytest.mark.parametrize("module, driver, time, task5_cluster", [("anchor", "anchorDriver", nightly_score, ""),
                                                                 ("anchorAccuracy", "AnchorAccuracyReportDriver",
                                                                  accuracy, "terminateCluster")])
@pytest.mark.tc13284
def test_tc_13284(get_token, module, driver, time, task5_cluster):
    response = LeaningApiRequest.create_draft_models(get_token, Models.ANCHOR.value)
    learning_config_uid = response.json()[0].get("learningConfigUID")
    assert response.status_code == 200
    response = LeaningApiRequest.deploy_models(get_token, learning_config_uid)
    assert response.status_code == 200
    builder = AllModuleBuilder() \
        .build_selected_module(module) \
        .build_selected_driver(driver) \
        .build_task5(task5_cluster) \
        .get_result()
    run_duration, run_status = JobUtils.run_job_to_obtain_time(builder, run_job_id, Models.ANCHOR.value)
    max_duration = datetime.timedelta(minutes=time)
    assert run_status == Constants.SUCCEEDED_STATUS
    assert run_duration <= max_duration


# TC-9327 : Validate performance of ANCHOR Nightly Score (Default Model) job
@pytest.mark.tc9327
def test_tc_9327(get_token):
    response = LeaningApiRequest.create_draft_models(get_token, Models.ANCHOR.value)
    learning_config_uid = response.json()[0].get("learningConfigUID")
    assert response.status_code == 200
    response = LeaningApiRequest.build_models(get_token, learning_config_uid)
    assert response.status_code == 200
    response = LeaningApiRequest.deploy_models(get_token, learning_config_uid)
    assert response.status_code == 200
    builder = AllModuleBuilder() \
        .build_model_type(Models.ANCHOR.value.upper()) \
        .get_result()
    run_duration, run_status = JobUtils.run_job_to_obtain_time(builder, run_job_id, Models.ANCHOR.value)
    max_duration = datetime.timedelta(minutes=nightly_score_default)
    assert run_status == Constants.SUCCEEDED_STATUS
    assert run_duration <= max_duration
