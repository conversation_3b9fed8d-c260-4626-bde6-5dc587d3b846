import pytest
import os
import datetime
from adl.automation.utils import Constants
from adl.automation.rundeck.all_model_params_builder import AllModuleBuilder
from adl.automation.utils.config_loader import load_config
from adl.automation.constants import TTE_JOB_ID
from adl.automation.utils.learning_api_utils import LeaningApiRequest
from adl.automation.utils.constans import Models
from adl.automation.utils.job_utils import JobUtils
from adl.automation.utils.Constants import body_tte, params_tte

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
run_job_id = str(environment.get("rundeck")["run_all_model_job_id"] + TTE_JOB_ID)

manual_score = float(environment.get("rundeck")["tte_time"]["manual_score"])
manual_build = float(environment.get("rundeck")["tte_time"]["manual_build"])
nightly_score = float(environment.get("rundeck")["tte_time"]["nightly_score"])
nightly_build = float(environment.get("rundeck")["tte_time"]["nightly_build"])
build = float(environment.get("rundeck")["tte_time"]["build"])
re_build = float(environment.get("rundeck")["tte_time"]["re_build"])
predictor_optimize = float(environment.get("rundeck")["tte_time"]["predictor_optimize"])
reporting_only = float(environment.get("rundeck")["tte_time"]["reporting_only"])
event_type = float(environment.get("rundeck")["tte_time"]["event_type"])


# TC-9321 : Validate performance of TTE Manual Scoring job
@pytest.mark.tc9321
def test_tc_9321(get_token):
    response = LeaningApiRequest.create_draft(get_token, body_tte)
    assert response.status_code == 200
    learning_config_uid = response.json().get("learningConfigUID")
    response = LeaningApiRequest.create_params(get_token, learning_config_uid, params_tte)
    assert response.status_code == 200
    response = LeaningApiRequest.publish_models(get_token, learning_config_uid)
    build_uid = response.json().get("currentLearningBuildUID")
    run_uid = response.json().get("latestRunUID")
    assert response.status_code == 200
    builder = AllModuleBuilder() \
        .build_selected_module("sparkMessageTiming") \
        .build_selected_driver("messageTimingScoreDriver") \
        .build_run_uid(run_uid) \
        .build_config_uid("TTE_Config") \
        .build_build_uid(build_uid) \
        .get_result()
    run_duration, run_status = JobUtils.run_job_to_obtain_time(builder, run_job_id, Models.TTE.value)
    max_duration = datetime.timedelta(minutes=manual_score)
    assert run_status == Constants.SUCCEEDED_STATUS
    assert run_duration <= max_duration


# TC-9320 : Validate performance of TTE Nighly Scoring job
# TC-9316 : Validate performance of TTE Predictor Optimizer job
@pytest.mark.parametrize("module, driver, time", [("sparkMessageTiming", "messageTimingScoreDriver", nightly_score),
                                                  ("sparkTTEPredictorOptimizer", "TTEPredictorOptimizer",predictor_optimize)])
@pytest.mark.tc9316
@pytest.mark.tc9320
def test_tc_9320(get_token, module, driver, time):
    response = LeaningApiRequest.create_draft(get_token, body_tte)
    assert response.status_code == 200
    learning_config_uid = response.json().get("learningConfigUID")
    response = LeaningApiRequest.create_params(get_token, learning_config_uid, params_tte)
    assert response.status_code == 200
    response = LeaningApiRequest.publish_models(get_token, learning_config_uid)
    assert response.status_code == 200
    builder = AllModuleBuilder() \
        .build_selected_module(module) \
        .build_selected_driver(driver) \
        .get_result()
    run_duration, run_status = JobUtils.run_job_to_obtain_time(builder, run_job_id, Models.TTE.value)
    max_duration = datetime.timedelta(minutes=manual_score)
    assert run_status == Constants.SUCCEEDED_STATUS
    assert run_duration <= max_duration


# TC-9319 : Validate performance of TTE Manual Build job
@pytest.mark.tc9319
def test_tc_9319(get_token):
    response = LeaningApiRequest.create_draft(get_token, body_tte)
    assert response.status_code == 200
    learning_config_uid = response.json().get("learningConfigUID")
    response = LeaningApiRequest.create_params(get_token, learning_config_uid, params_tte)
    assert response.status_code == 200
    response = LeaningApiRequest.publish_models(get_token, learning_config_uid)
    build_uid = response.json().get("currentLearningBuildUID")
    assert response.status_code == 200
    builder = AllModuleBuilder() \
        .build_selected_module("sparkMessageTiming") \
        .build_selected_driver("messageTimingDriver") \
        .build_config_uid("TTE_Config") \
        .build_build_uid(build_uid) \
        .get_result()
    run_duration, run_status = JobUtils.run_job_to_obtain_time(builder, run_job_id, Models.TTE.value)
    max_duration = datetime.timedelta(minutes=manual_score)
    assert run_status == Constants.SUCCEEDED_STATUS
    assert run_duration <= max_duration

