import pytest
import os
import datetime
from adl.automation.utils import Constants
from adl.automation.rundeck.all_model_params_builder import AllModuleBuilder
from adl.automation.utils.config_loader import load_config
from adl.automation.constants import REM_JOB_ID
from adl.automation.utils.learning_api_utils import LeaningApiRequest
from adl.automation.utils.constans import Models
from adl.automation.utils.job_utils import JobUtils
from adl.automation.utils.Constants import body_rem, params_rem

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data
run_job_id = str(environment.get("rundeck")["run_all_model_job_id"] + REM_JOB_ID)
run_default_job = str(environment.get("rundeck")["run_all_model_job_id"])
nightly_score = float(environment.get("rundeck")["rem_time"]["nightly_score"])


# TC-6506 : Validate that REM job runs about 55 mins with old parameter formats
@pytest.mark.tc6506
def test_tc_6506(get_token):
    response = LeaningApiRequest.create_draft(get_token, body_rem)
    learning_config_uid = response.json().get("learningConfigUID")
    assert response.status_code == 200
    response = LeaningApiRequest.create_params(get_token, learning_config_uid, params_rem)
    assert response.status_code == 200
    response = LeaningApiRequest.deploy_models(get_token, learning_config_uid)
    assert response.status_code == 200
    builder = AllModuleBuilder() \
        .build_selected_module("sparkEngagement") \
        .build_selected_driver("engagementDriver") \
        .get_result()
    run_duration, run_status = JobUtils.run_job_to_obtain_time(builder, run_job_id, Models.REM.value)
    max_duration = datetime.timedelta(minutes=nightly_score)
    assert run_status == Constants.SUCCEEDED_STATUS
    assert run_duration <= max_duration


# TC-6490 : Performance: Validate that REM job runs about 1 hour 11 mins
@pytest.mark.tc6490
def test_tc_6490(get_token):
    response = LeaningApiRequest.create_draft_models(get_token, Models.ANCHOR.value)
    learning_config_uid = response.json()[0].get("learningConfigUID")
    assert response.status_code == 200
    response = LeaningApiRequest.deploy_models(get_token, learning_config_uid)
    assert response.status_code == 200
    builder = AllModuleBuilder() \
        .build_selected_module("sparkEngagement") \
        .build_selected_driver("engagementDriver") \
        .get_result()
    run_duration, run_status = JobUtils.run_job_to_obtain_time(builder, run_job_id, Models.REM.value)
    max_duration = datetime.timedelta(minutes=nightly_score)
    assert run_status == Constants.SUCCEEDED_STATUS
    assert run_duration <= max_duration
