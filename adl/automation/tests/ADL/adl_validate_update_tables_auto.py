import pytest
import os
import delayed_assert as assertion
from common.pyUtils.logger import get_module_logger
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader
from adl.automation.rundeck.rundeck_job_executor import RundeckJobExecutor
from adl.automation.rundeck.adl_job_params_builder import Builder
from adl.automation.utils import Constants

customer = os.environ.get("ENV_NAME")
adl_branch = os.environ.get("ADL_BRANCH")
feature = os.environ.get("FEATURE_STORE")
environment = load_config(f'adl/automation/config/{customer}').data
deploy_job_id = str(environment.get("rundeck")["deploy_job_id"])
run_job_id = str(environment.get("rundeck")["run_job_id"])
base_adl_url = str(environment.get("adl")["base_adl_url"])
bucket = str(environment.get("adl")["bucket"])

logger = get_module_logger(__name__)


# TC-10485 : Validate that dse_Score_Final table was updated and new rows were added
# TC-10486 : Validate that recordclasses_dse table was updated and new rows were added
# TC-10493 : Validate that final_dataset table was updated and new rows were added
# TC-10366 : Validate that new data is loaded in tables inside bronze folder
# TC-10479 : Validate that suggestions_final table was updated and new rows were added
# TC-10480 : Validate that recordclasses table was updated and new rows were added
@pytest.mark.tc10485
@pytest.mark.tc10486
@pytest.mark.tc10493
def test_tc_validate_updated_table(spark):
    logger.info("Reading baseline values for adl tables")
    emails_previous = DataReader.read_delta_table(f"{base_adl_url}bronze/emails")
    suggestion_final_previous = DataReader.read_delta_table(f"{base_adl_url}bronze/suggestion_final")
    dse_score_final_previous = DataReader.read_delta_table(f"{base_adl_url}bronze/dse_score_final")
    recordclasses_previous = DataReader.read_delta_table(f"{base_adl_url}silver/recordclasses")
    record_classes_dse_previous = DataReader.read_delta_table(f"{base_adl_url}silver/recordclasses_dse")
    final_dataset_previous = DataReader.read_delta_table(f"{base_adl_url}silver/final_dataset")

    logger.info("Running ADL incremental job")
    rundeck = RundeckJobExecutor()
    builder = Builder() \
        .build_adl_branch(adl_branch) \
        .build_feature_store(feature) \
        .get_result()
    rundeck.run_job(builder, deploy_job_id)
    deploy_status = rundeck.get_status_last_execution(deploy_job_id)

    assert deploy_status == Constants.SUCCEEDED_STATUS

    rundeck.run_job(builder, run_job_id)
    run_status = rundeck.get_status_last_execution(run_job_id)

    assert run_status == Constants.SUCCEEDED_STATUS

    logger.info("Reading final values from ADL tables to compare")
    emails = DataReader.read_delta_table(f"{base_adl_url}bronze/emails")
    suggestion_final = DataReader.read_delta_table(f"{base_adl_url}bronze/suggestion_final")
    dse_score_final = DataReader.read_delta_table(f"{base_adl_url}bronze/dse_score_final")
    recordclasses = DataReader.read_delta_table(f"{base_adl_url}silver/recordclasses")
    record_classes_dse = DataReader.read_delta_table(f"{base_adl_url}silver/recordclasses_dse")
    final_dataset = DataReader.read_delta_table(f"{base_adl_url}silver/final_dataset")

    logger.info("Comparing previous and actual values")
    assertion.expect(len(emails) >= len(emails_previous))
    assertion.expect(len(suggestion_final) >= len(suggestion_final_previous))
    assertion.expect(len(dse_score_final) >= len(dse_score_final_previous))
    assertion.expect(len(recordclasses) >= len(recordclasses_previous))
    assertion.expect(len(record_classes_dse) >= len(record_classes_dse_previous))
    assertion.expect(len(final_dataset) >= len(final_dataset_previous))

    assertion.assert_expectations()

