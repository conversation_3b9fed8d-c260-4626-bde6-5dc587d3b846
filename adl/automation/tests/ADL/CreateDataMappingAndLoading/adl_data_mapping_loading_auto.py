import pytest
import os

from adl.automation.utils.config_loader import load_config
from adl.automation.s3.s3_manager import AWSManager


customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

base_adl_url = str(environment.get("adl")["base_adl_url"])
base_rpt_url = str(environment.get("adl")["base_rpt_url"])
bucket_name = str(environment.get("adl")["bucket"])
base_config_path = str(environment.get("adl")["base_config_url"])
base_adl_key = str(environment.get("adl")["base_adl_key"]).replace("data", "config")


delta_tables = ['deltaLoading', 'deltaMapping']


# TC-9488 : Validate that deltaLoading and deltaMapping folders were created inside config folder
@pytest.mark.tc9488
@pytest.mark.parametrize("table_name", delta_tables)
def test_delta_tables_are_present(table_name):
    s3 = AWSManager.get_instance('s3')
    is_present = s3.s3_folder_exists(bucket_name, base_adl_key.format(table_name))

    assert is_present


# TC-9482 : Validate that user can see the delta versions history for loading file
# TC-9483 : Validate that user can see the delta versions history for mapping file
@pytest.mark.tc9482
@pytest.mark.parametrize("table_name", delta_tables)
def test_delta_versions_history(spark, table_name):
    from delta.tables import DeltaTable
    delta_table = DeltaTable.forPath(spark, base_config_path + table_name)
    fullHistoryDF = delta_table.history()

    assert not fullHistoryDF.count() == 0


# TC-9484 : Validate that user can read any delta version of mapping file
# TC-9486 : Validate that user can read any delta version of loading file
@pytest.mark.tc9484
@pytest.mark.parametrize("table_name", delta_tables)
def test_delta_read_table(spark, table_name):
    df = spark.read.format('delta').option('versionAsOf', 0).load(base_config_path + table_name)

    assert not df.rdd.isEmpty()