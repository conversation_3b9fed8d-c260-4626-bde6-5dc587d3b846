import pytest
import os

from adl.automation.utils.constans import SCDtableColumns
from adl.automation.utils import Constants
from adl.automation.rundeck.adl_job_params_builder import Builder
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader
from adl.automation import constants


bronze_folder_names = Constants.BRONZE_TABLES
silver_folder_names = Constants.SILVER_TABLE

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

testdata = ['SCD_IN_PARAM']

"""
tests covered in file
- TC-9577:Validate the * SCD_IN_PARAM* table data
- TC-9576:Validate the * SCD_IN_PRODUCT_CONFIG* table data
- TC-9578:Validate the * SCD_IN_PRODUCT_METRIC_SUMMARY* table populated data
- TC-9579:Validate the * SCD_IN_REPORTING_LEVEL* table populated data
- TC-9580:Validate the * SCD_IN_SALES_FACT_SUMMARY* table populated data
- TC-9581:Validate the * SCD_IN_TARGET_VIEW_MAPPING* table populated data
- TC-9582:Validate the * SCD_IN_USE_CASE* table populated data
"""


# TC-9577:Validate the * SCD_IN_PARAM* table data
@pytest.mark.tc9577
@pytest.mark.scd
def test_validate_scd_tables_exists(spark):
    actual_tables = constants.SCD_TABLES
    query = "SELECT TABLE_NAME FROM information_schema.tables WHERE TABLE_SCHEMA = 'SCD'"
    df = DataReader.read_data_from_snowflake(spark, query)
    df.show(truncate=False)

    expected_tables = df.select("TABLE_NAME").rdd.flatMap(lambda x: x).collect()

    result = all(item in expected_tables for item in actual_tables)

    assert result


# TC-9576:Validate the * SCD_IN_PRODUCT_CONFIG* table data
@pytest.mark.tc9576
@pytest.mark.scd
@pytest.mark.parametrize("table", testdata)
def test_validate_columns_for_tables(spark, table):
    query = "SELECT COLUMN_NAME, DATA_TYPE FROM information_schema.columns WHERE TABLE_SCHEMA = 'SCD' AND TABLE_NAME = '{}'".format(table)
    df = DataReader.read_data_from_snowflake(spark, query)
    df.show(truncate=False)

    actual_columns = df.select("COLUMN_NAME").rdd.flatMap(lambda x: x).collect()

    expected = SCDtableColumns[f'{table}'].value

    print(expected)
    print(actual_columns)

    result = all(item in actual_columns for item in expected )

    assert result