import pytest
import os

from adl.automation.utils.constans import SCDtableColumns
from adl.automation.utils import Constants
from adl.automation.rundeck.adl_job_params_builder import Builder
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader
from adl.automation import constants


bronze_folder_names = Constants.BRONZE_TABLES
silver_folder_names = Constants.SILVER_TABLE

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

testdata = ['SCD_IN_PARAM']

"""
tests covered in file
- TC-11077:Validate the Snowflake vw_SCD_IN_USEABLE_SALES_DIM View created with below columns
- TC-11079:Validate to see what reporting-level, frequency, metrics, market-baskets and period-range are useable for a product
- TC-11080:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates PRODUCT_CONFIG_KEY and PRODUCT_NAME Column values matching with SCD_IN_PRODUCT_CONFIG table
- TC-11087:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates DIM_METRIC_KEY and DIM_METRIC_NAME Column values matching with DIM_METRIC and SCD_IN_PRODUCT_METRIC_SUMMARY tables
- TC-11088:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates DIM_FREQUENCY_KEY and DIM_FREQUENCY_NAME Column values matching with SCD_IN_PRODUCT_METRIC_SUMMARY AND DIM_FREQUENCY tables
- TC-11089:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates REPORTING_LEVEL_KEY and REPORTING_LEVEL_NAME Column values matching with SCD_IN_PRODUCT_METRIC_SUMMARY AND SCD_IN_REPORTING_LEVEL tables
- TC-11090:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates DIM_MARKETBASKET_KEY and MARKETBASKET_NAME Column values matching with SCD_IN_PRODUCT_METRIC_SUMMARY AND DIM_MARKETBASKET tables
- TC-11091:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates MIN_PERIOD_NUMBER and MAX_PERIOD_NUMBER Column values calculated correctly, based on DIM_PERIOD, SCD_IN_PRODUCT_METRIC_SUMMARY and DIM_FREQUENCY tables
- TC-11103:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates IS_USEABLE and REASON_TEXT Column values matching with SCD_IN_PRODUCT_METRIC_SUMMARY table
"""


# TC-11077:Validate the Snowflake vw_SCD_IN_USEABLE_SALES_DIM View created with below columns
@pytest.mark.tc11077
@pytest.mark.scd
def test_validate_scd_view_useable_columns(spark):
    expected_columns = constants.VIEW_SCD_IN_USEABLE_SALES_DIM_COLUMNS
    query = "SELECT * FROM SCD.VW_SCD_IN_USEABLE_SALES_DIM;"

    df = DataReader.read_data_from_snowflake(spark, query)

    assert list(df.columns) == expected_columns

# TC-11079:Validate to see what reporting-level, frequency, metrics, market-baskets and period-range are useable for a product
@pytest.mark.tc11077
@pytest.mark.scd
def test_validate_scd_view_useable_columns(spark):
    query = "SELECT * FROM SCD.VW_SCD_IN_USEABLE_SALES_DIM"
    df = DataReader.read_data_from_snowflake(spark, query)

    products = df.select("SCD_IN_PRODUCT_CONFIG_KEY").distinct().rdd.flatMap(lambda x: x).collect()

    for product in products:
        query_2 = "SELECT * FROM SCD.vw_SCD_IN_USEABLE_SALES_DIM WHERE SCD_IN_PRODUCT_CONFIG_KEY = '{}';".format(product)
        df = DataReader.read_data_from_snowflake(spark, query_2)

        df = df.select("METRIC_NAME", "FREQUENCY_NAME", "REPORTING_LEVEL_NAME")

        pd_df = df.toPandas()

        assert not pd_df.isnull().values.any()


# TC-11080:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates PRODUCT_CONFIG_KEY and PRODUCT_NAME Column values matching with SCD_IN_PRODUCT_CONFIG table
@pytest.mark.tc11080
@pytest.mark.scd
def test_validate_scd_view_useable_columns(spark):
    query = "select distinct (SCD_IN_PRODUCT_CONFIG_KEY) from SCD.vw_SCD_IN_USEABLE_SALES_DIM;"
    query_2 = "select distinct (PRODUCT_NAME) from SCD.vw_SCD_IN_USEABLE_SALES_DIM;"

    df = DataReader.read_data_from_snowflake(spark, query)
    df_2 = DataReader.read_data_from_snowflake(spark, query_2)

    assert df.count() == df_2.count()

    query = "select distinct (SCD_IN_PRODUCT_CONFIG_KEY) from SCD.vw_SCD_IN_USEABLE_SALES_DIM \
    where SCD_IN_PRODUCT_CONFIG_KEY in (SELECT SCD_IN_PRODUCT_CONFIG_KEY FROM SCD.SCD_IN_PRODUCT_CONFIG);"
    query_2 = "select distinct (SCD_IN_PRODUCT_CONFIG_KEY) from SCD.vw_SCD_IN_USEABLE_SALES_DIM \
    where PRODUCT_NAME in (SELECT PRODUCT_NAME FROM SCD.SCD_IN_PRODUCT_CONFIG);"

    df = DataReader.read_data_from_snowflake(spark, query)
    df_2 = DataReader.read_data_from_snowflake(spark, query_2)

    assert df.count() == df_2.count()


# TC-11087:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates DIM_METRIC_KEY and DIM_METRIC_NAME Column values matching with DIM_METRIC and SCD_IN_PRODUCT_METRIC_SUMMARY tables
@pytest.mark.tc11080
@pytest.mark.scd
def test_validate_scd_view_useable_columns(spark):
    query = "select * from SCD.vw_SCD_IN_USEABLE_SALES_DIM; "
    query_2 = "select * from SCD.vw_SCD_IN_USEABLE_SALES_DIM A \
    JOIN EUQA_DEV.DW_CENTRAL_VIEW.VW_DIM_METRIC B ON (A.DIM_METRIC_KEY=B.DIM_METRIC_KEY and A.METRIC_NAME=B.METRIC_NAME) \
    WHERE A.DIM_METRIC_KEY IN (SELECT distinct(DIM_METRIC_KEY) from SCD.SCD_IN_PRODUCT_METRIC_SUMMARY);"

    df = DataReader.read_data_from_snowflake(spark, query)
    df_2 = DataReader.read_data_from_snowflake(spark, query_2)

    assert df.count() == df_2.count()

# TC-11088:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates DIM_FREQUENCY_KEY and DIM_FREQUENCY_NAME Column values matching with SCD_IN_PRODUCT_METRIC_SUMMARY AND DIM_FREQUENCY tables
@pytest.mark.tc11080
@pytest.mark.scd
def test_validate_scd_view_useable_columns(spark):
    query = "select * from SCD.vw_SCD_IN_USEABLE_SALES_DIM; "
    query_2 = "select * from SCD.vw_SCD_IN_USEABLE_SALES_DIM A \
    JOIN EUQA_DEV.DW_CENTRAL.DIM_FREQUENCY B ON (A.DIM_FREQUENCY_KEY=B.DIM_FREQUENCY_KEY and A.FREQUENCY_NAME=B.FREQUENCY_NAME) \
    WHERE A.DIM_FREQUENCY_KEY IN (SELECT distinct(DIM_FREQUENCY_KEY) from SCD.SCD_IN_PRODUCT_METRIC_SUMMARY);"

    df = DataReader.read_data_from_snowflake(spark, query)
    df_2 = DataReader.read_data_from_snowflake(spark, query_2)

    assert df.count() == df_2.count()


# TC-11089:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates REPORTING_LEVEL_KEY and REPORTING_LEVEL_NAME Column values matching with SCD_IN_PRODUCT_METRIC_SUMMARY AND SCD_IN_REPORTING_LEVEL tables
@pytest.mark.tc11080
@pytest.mark.scd
def test_validate_scd_view_useable_columns(spark):
    query = "select * from SCD.vw_SCD_IN_USEABLE_SALES_DIM;"
    query_2 = "sselect COUNT(*) from SCD.vw_SCD_IN_USEABLE_SALES_DIM A \
    JOIN SCD.SCD_IN_REPORTING_LEVEL B ON (A.SCD_IN_REPORTING_LEVEL_KEY=B.SCD_IN_REPORTING_LEVEL_KEY \
    and A.REPORTING_LEVEL_NAME=B.REPORTING_LEVEL_NAME) WHERE A.SCD_IN_REPORTING_LEVEL_KEY IN \
    (select distinct(SCD_IN_REPORTING_LEVEL_KEY) from SCD.SCD_IN_PRODUCT_METRIC_SUMMARY);"

    df = DataReader.read_data_from_snowflake(spark, query)
    df_2 = DataReader.read_data_from_snowflake(spark, query_2)

    assert df.count() == df_2.count()


# TC-11090:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates DIM_MARKETBASKET_KEY and MARKETBASKET_NAME Column values matching with SCD_IN_PRODUCT_METRIC_SUMMARY AND DIM_MARKETBASKET tables
@pytest.mark.tc11080
@pytest.mark.scd
def test_validate_scd_view_useable_columns(spark):
    query = "select * from RPT_DWPREPROD_QA.SCD.vw_SCD_IN_USEABLE_SALES_DIM WHERE DIM_MARKETBASKET_KEY is not NULL;"
    query_2 = "select * from SCD.vw_SCD_IN_USEABLE_SALES_DIM A \
    JOIN DW_CENTRAL.DIM_MARKETBASKET B ON (A.DIM_MARKETBASKET_KEY=B.DIM_MARKETBASKET_KEY and \
    A.MARKETBASKET_NAME=B.MARKETBASKET_NAME) WHERE A.DIM_FREQUENCY_KEY IN \
    (select distinct(DIM_FREQUENCY_KEY) from SCD.SCD_IN_PRODUCT_METRIC_SUMMARY);"

    df = DataReader.read_data_from_snowflake(spark, query)
    df_2 = DataReader.read_data_from_snowflake(spark, query_2)

    assert df.count() == df_2.count()


# TC-11091:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates MIN_PERIOD_NUMBER and MAX_PERIOD_NUMBER \
# Column values calculated correctly, based on DIM_PERIOD, SCD_IN_PRODUCT_METRIC_SUMMARY and DIM_FREQUENCY tables
@pytest.mark.tc11080
@pytest.mark.scd
def test_validate_scd_view_useable_columns(spark):
    query = "select DIM_FREQUENCY_KEY, min (PERIOD_NUMBER), max (PERIOD_NUMBER) from DW_CENTRAL.DIM_PERIOD group by DIM_FREQUENCY_KEY;"
    query_2 = "select DIM_FREQUENCY_KEY, MIN_PERIOD_NUMBER, MAX_PERIOD_NUMBER from SCD.VW_SCD_IN_USEABLE_SALES_DIM;"

    df = DataReader.read_data_from_snowflake(spark, query)
    df_2 = DataReader.read_data_from_snowflake(spark, query_2)

    result = df.subtract(df_2)

    assert result.rdd.isEmpty()


# TC-11103:Validate the vw_SCD_IN_USEABLE_SALES_DIM View populates IS_USEABLE and REASON_TEXT Column values matching with SCD_IN_PRODUCT_METRIC_SUMMARY table
@pytest.mark.tc11103
@pytest.mark.scd
def test_validate_reason_text_column(spark):
    query = "select * from SCD.vw_SCD_IN_USEABLE_SALES_DIM WHERE REASON_TEXT IS NOT NULL; "
    query_2 = "select * from SCD.vw_SCD_IN_USEABLE_SALES_DIM A JOIN \
    SCD.SCD_IN_PRODUCT_METRIC_SUMMARY B USING (SCD_IN_PRODUCT_CONFIG_KEY) \
    WHERE A.IS_USEABLE=B.IS_USEABLE AND A.REASON_TEXT IS NOT NULL AND A.REASON_TEXT=B.REASON_TEXT;"

    df = DataReader.read_data_from_snowflake(spark, query)
    df_2 = DataReader.read_data_from_snowflake(spark, query_2)


    assert df.count() == df_2.count()