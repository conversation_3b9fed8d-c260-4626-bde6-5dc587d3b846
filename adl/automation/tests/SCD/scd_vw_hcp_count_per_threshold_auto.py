import pytest
import os

from adl.automation.utils.constans import SCDtableColumns
from adl.automation.utils import Constants
from adl.automation.rundeck.adl_job_params_builder import Builder
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.data_reader import DataReader
from adl.automation import constants


bronze_folder_names = Constants.BRONZE_TABLES
silver_folder_names = Constants.SILVER_TABLE

customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

testdata = ['SCD_IN_PARAM']

"""
tests covered in file
- TC-12479:Validate the Snowflake VW_HCP_COUNT_PER_THRESHOLD_RPT View was created
- TC-12480:Validate the Snowflake VW_HCP_COUNT_PER_THRESHOLD_RPT View created with below columns
"""


# TC-12479:Validate the Snowflake VW_HCP_COUNT_PER_THRESHOLD_RPT View was created
@pytest.mark.tc12479
@pytest.mark.scd
def test_validate_scd_view_hcp_count(spark):
    views_df = DataReader.read_data_from_snowflake(spark, "SELECT * FROM information_schema.views WHERE TABLE_SCHEMA = 'SCD'")
    scd_views = views_df.select("TABLE_NAME").rdd.flatMap(lambda x: x).collect()

    assert "VW_HCP_COUNT_PER_THRESHOLD_RPT" in scd_views

    expected_columns = constants.VW_HCP_COUNT_PER_THRESHOLD_RPT
    query = "SELECT * FROM SCD.VW_HCP_COUNT_PER_THRESHOLD_RPT;"

    df = DataReader.read_data_from_snowflake(spark, query)

    print(df.columns)

    assert list(df.columns) == expected_columns