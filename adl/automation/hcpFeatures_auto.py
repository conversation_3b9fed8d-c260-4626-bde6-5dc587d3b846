import os, sys
import pytest
from pytest import approx

import pandas
import s3fs
import pyarrow.dataset as ds
import pyarrow.parquet as pq
from deltalake import DeltaTable
import os
from pandasql import sqldf


# script_path = os.path.realpath(__file__)
# script_dir = os.path.dirname(script_path)
# learning_dir = os.path.dirname(script_dir)
# sys.path.append(learning_dir)


pytestSc = pytest.mark.usefixtures("spark_context")
pytestSpark = pytest.mark.usefixtures("spark")
pytestAdl = pytest.mark.usefixtures("getAdl")
hcpStore = pytest.mark.usefixtures("getHcpStore")


# @pytest.fixture()
# def name(pytestconfig):
#     return pytestconfig.getoption("name")

# def test_print_name(name):
#     print(f"\ncommand line param (name): {name}")
#
#
# def test_print_name_2(pytestconfig):
#     print(f"test_print_name_2(name): {pytestconfig.getoption('name')}")


# get uniquie product names
#prod_list = list(set(input_table_1['basketId']))
#print(prod_list)

#prod_list = ['ELIQUIS', 'LYRICA']

#df = DeltaTable("aktana-bdp-siqa/adl/archive1Rs/data/silver/hcpFeatureStore", file_system=fs).to_table(filter=ds.field('productName').isin(prod_list)).to_pandas()
#df = DeltaTable("aktana-bdp-siqa/adl/archive1Rs/data/silver/hcpFeatureStore", file_system=fs).to_table(filter=ds.field('productName').isin(prod_list), columns=['productName', 'facilityId', 'indexMonth', 'numberHcpFacility', 'accountId', 'hcpStartYearMonth', 'EmailSent1MonthCount', 'Visit1MonthCount', 'tenureMonths', 'hcpGender', 'hcpCred', 'hcpSpec', 'zipCode', 'city', 'state', 'timeZone']).to_pandas()



# output
def test_sizeOdAdlMock(getHcpStore):
    df = getHcpStore
    cnt = sqldf("select count(accountId) from df")
    cnt = cnt.iloc[0,0]
    assert cnt == 260292

@pytest.mark.get_hcp_fs
def test_sizeOdAdl(getHcpStore):
    df = getHcpStore
    cnt = sqldf("select count(accountId) from df")
    cnt = cnt.iloc[0,0]
    assert cnt == 281616