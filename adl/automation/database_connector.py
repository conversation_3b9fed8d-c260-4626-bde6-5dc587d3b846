"""Database Connector
"""
import logging

import pandas as pd
import sqlalchemy

logger = logging.getLogger(__name__)


class Database:
    """Database Class connector
        """

    def __init__(self, database=None, rds=None, connector='mysql+pymysql', user=None, password=None,
                 warehouse=None, role='sysadmin'):

        self.user = user
        self.password = password
        self.engine = None

        self.bind_address = '127.0.0.1'
        self.local_port = '3306'
        self.conn_string = f'{connector}://{self.user}:{self.password}@{self.bind_address}:{self.local_port}/'
        self.conn_string = self.conn_string if not database else f'{self.conn_string}{database}?charset=utf8'

        self._open_connection()

    def _open_connection(self):
        try:
            self.engine = sqlalchemy.create_engine(self.conn_string)
            logger.info(f"Database Connection Successfully Opened : {self.conn_string}")

        except Exception as exception:
            logger.error(exception)

    def execute_query(self, query):
        """ Execute a query
        :param query:
        :return:
        """
        try:
            query = query.replace('%', '%%')  # Fix pymysql format error unsupported format character
            dataframe = pd.read_sql_query(sql=query, con=self.engine)
            self.engine.dispose()
            logger.info(f"Query Successfully Executed : {query}")
            return dataframe

        except Exception as exception:
            logger.error(exception)
            self.engine.dispose()
