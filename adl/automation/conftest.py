import json
import urllib
import pyspark
import pytest
import logging
import findspark
import os
import sys
import requests
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)
from pyspark import HiveContext
from pyspark import SparkConf
from pyspark import SparkContext
from pyspark.streaming import StreamingContext
from pyspark.sql import SparkSession
import s3fs
from deltalake import DeltaTable
from adl.automation.utils.ssh_tunnel import SSHTunnel
from adl.automation.database_connector import Database
from adl.automation.utils.config_loader import load_config
from adl.automation.utils.api_request import RequestApi

rptS3Location = ""
s3Destination = ""
awsRegion = ""
customer = os.environ.get("ENV_NAME")
environment = load_config(f'adl/automation/config/{customer}').data

ACCESS_KEY = str(environment.get("adl")["access_key"])
SECRET_KEY = str(environment.get("adl")["secret_key"])
SECRET_API = str(environment.get("learning_api")["secret"])
USER_API = str(environment.get("learning_api")["username"])
PASSWORD_API = str(environment.get("learning_api")["password"])
learning_url_base = str(environment.get("learning_api")["learning_url_base"])


@pytest.fixture(scope='session')
def spark(request):
    global ACCESS_KEY, SECRET_KEY
    os.environ[
        'PYSPARK_SUBMIT_ARGS'] = "--driver-memory 4g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.271," \
                                 "org.apache.hadoop:hadoop-aws:3.1.2,io.delta:delta-core_2.12:0.8.0," \
                                 "net.snowflake:snowflake-jdbc:3.8.0," \
                                 "net.snowflake:spark-snowflake_2.12:2.8.4-spark_3.0 pyspark-shell "
    findspark.init()
    sc = pyspark.SparkContext()
    sc.setSystemProperty("com.amazonaws.services.s3.enableV4", "true")
    hadoop_conf = sc._jsc.hadoopConfiguration()
    hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
    hadoop_conf.set("com.amazonaws.services.s3.enableV4", "true")
    hadoop_conf.set("fs.s3a.access.key", ACCESS_KEY)
    hadoop_conf.set("fs.s3a.secret.key", SECRET_KEY)
    hadoop_conf.set("fs.s3a.connection.maximum", "100000")
    hadoop_conf.set("fs.s3a.endpoint", "s3.amazonaws.com")
    hadoop_conf.set("delta.logRetentionDuration", "36500")
    hadoop_conf.set("delta.deletedFileRetentionDuration", "365")

    spark = SparkSession(sc) \
        .builder \
        .appName("automation app") \
        .config("spark.sql.autoBroadcastJoinThreshold", -1) \
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
        .config("spark.driver.memory", "8g") \
        .config('spark.executor.cores', '4') \
        .config('spark.executor.memory', '8g') \
        .config("spark.python.worker.memory", '4g') \
        .config("spark.sql.debug.maxToStringFields", 1000) \
        .getOrCreate()

    return spark


@pytest.fixture(scope="session")
def hive_context(spark_context):
    return HiveContext(spark_context)


@pytest.fixture(scope="session")
def streaming_context(spark_context):
    return StreamingContext(spark_context, 1)


@pytest.fixture(scope="session")
def getAdl(spark):
    spark.read.csv("data/adlEx.csv", header=True)


@pytest.fixture(scope="session")
def getAdlData(request):
    # getS3meta(request)
    global s3Destination, awsRegion, ACCESS_KEY, SECRET_KEY
    fs = s3fs.S3FileSystem(key=ACCESS_KEY, secret=SECRET_KEY)

    return fs


@pytest.fixture(scope="session")
def getRptData(request):
    # getS3meta(request)
    global rptS3Location, awsRegion, ACCESS_KEY, SECRET_KEY
    fs = s3fs.S3FileSystem(key=ACCESS_KEY, secret=SECRET_KEY)

    df = DeltaTable(rptS3Location + "data/bronze/account",
                    file_system=fs).to_table().to_pandas()
    return df


@pytest.fixture(scope="session")
def getHcpStore(request):
    # getS3meta(request)
    # os.environ['AWS_DEFAULT_REGION'] = "us-west-2"
    global s3Destination, awsRegion, ACCESS_KEY, SECRET_KEY
    fs = s3fs.S3FileSystem(key=ACCESS_KEY, secret=SECRET_KEY)
    # df = DeltaTable("s3://aktana-bdp-siqa/adl/archive8/data/silver/hcpFeatureStore",
    #                 file_system=fs).to_table().to_pandas()
    df = DeltaTable(s3Destination + "data/silver/hcpFeatureStore",
                    file_system=fs).to_table().to_pandas()
    return df


def pytest_addoption(parser):
    parser.addoption("--customer", action="store", default="novartisau")
    parser.addoption("--env", action="store", default="ds")


@pytest.fixture(scope="session")
def get_token():
    session = requests.Session()
    url = f"{learning_url_base}Token"
    data = {
               "secret": SECRET_API,
               "username": USER_API,
               "password": PASSWORD_API
            }
    request_api = RequestApi.get_instance(session)
    response = request_api.post(url, json=data)
    assert response.status_code == 200
    api_service = session.headers.update({'Authorization': f'Bearer {response.text}'})
    return api_service
