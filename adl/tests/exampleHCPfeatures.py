class ExampleHCPfeatures:

    def sizeOfAdlMock(df):
        return df.count()

    def interactionWeekDayxNameRatio(df):
        df1 = df.filter(df.interactionIsWeekend == "1").count()
        df0 = df.filter(df.interactionIsWeekend == "0").count()
        return round(df1 / df0)

    def emailOpenAvg(_df):
        df = _df.select("emailOpenCount").na.drop()
        df = df.withColumn("emailOpenCount", df.emailOpenCount.cast('int'))
        res = df.groupBy().avg('emailOpenCount').collect()[0]
        df.show()
        return res