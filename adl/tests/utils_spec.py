import os,sys
import pytest
from pyspark import SQLContext
from pyspark.sql import SparkSession
import pyspark.sql.functions as F

script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)

from adl.code.utils import Utils
# from awsglue.context import GlueContext

# pytestSc = pytest.mark.usefixtures("spark_context")
# pytestSpark = pytest.mark.usefixtures("spark")

# def do_word_counts(lines):
#     counts = (lines.flatMap(lambda x: x.split())
#                   .map(lambda x: (x, 1))
#                   .reduceByKey(lambda x, y: x+y)
#              )
#     results = {word: count for word, count in counts.collect()}
#     return results
#
# def test_do_word_counts(spark_context):
#     test_input = [
#         ' hello spark ',
#         ' hello again spark spark'
#     ]
#
#     input_rdd = spark_context.parallelize(test_input, 1)
#     results = do_word_counts(input_rdd)
#
#     expected_results = {'hello':2, 'spark':3, 'again':1}
#     assert results == expected_results

def test_normalize(spark):
    # glueContext = GlueContext(spark_context)
    # sqlContext = SQLContext(spark_context)
    # utils = Utils(spark_context, "pfizerus")
    p = Utils(spark, "pfizerus")
    df = spark.createDataFrame(
    [
        (1, 'x', 2),
        (2, 'y', 1),
    ],
    ['some_column','Another_column', 'lAST_cOLUMN']
    )
    resultedDF = p.normalizeColumnNames(df)
    resColumns = resultedDF.columns
    assert resColumns == ['someColumn','anotherColumn', 'lASTCOLUMN'] #lastCOLUMN

def test_renameColumns(spark):
    res = Utils.renameColumns(Utils,'some_column')
    assert res == 'someColumn'
    # , 'Another_column', 'lAST_cOLUMN'
    res = Utils.renameColumns(Utils, 'Another_column')
    assert res == 'anotherColumn'