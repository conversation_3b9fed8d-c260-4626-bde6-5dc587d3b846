import os, sys
import pytest
from pytest import approx
# from exampleHCPfeatures import ExampleHCPfeatures

script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)

from exampleHCPfeatures import ExampleHCPfeatures

pytestSc = pytest.mark.usefixtures("spark_context")
pytestSpark = pytest.mark.usefixtures("spark")
pytestAdl = pytest.mark.usefixtures("getAdl")

def test_sizeOdAdlMock(spark):
    df = spark.read.csv("adl/tests/data/adlEx.csv",header=True)
    assert ExampleHCPfeatures.sizeOfAdlMock(df) == 10000

def test_emailOpenAvg(spark):
    df = spark.read.csv("adl/tests/data/adlEx.csv",header=True)
    res = ExampleHCPfeatures.emailOpenAvg(df)
    assert res['avg(emailOpenCount)'] == None

def test_interactionWeekDayxNameRatio(spark):
    df = spark.read.csv("adl/tests/data/adlEx.csv",header=True)
    res = ExampleHCPfeatures.interactionWeekDayxNameRatio(df)
    assert res == approx(0.000000000001)
