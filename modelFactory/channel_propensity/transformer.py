from datetime import datetime

from abstract_model_factory.abstract_transformer import AbstractTransformer
from channel_propensity.constants import Constants
from channel_propensity.data import Data
import pandas as pd
import numpy as np
from scipy import stats

from channel_propensity.qa_data_handler import QADataHandler


class Transformer(AbstractTransformer):

    def create_flow_variables(self):
        data = Data.get_instance()
        hcp_fs_pdf = data.get_dataframe("hcp_fs_pdf")

        # create flow variable named random with 0 <= value <= 100
        hcp_fs_pdf['yearMonth'] = hcp_fs_pdf['yearMonth'].astype(int)
        min_yearMonth = min(hcp_fs_pdf['yearMonth'])
        max_yearMonth = max(hcp_fs_pdf['yearMonth'])
        feature_period = f"{min_yearMonth}-{max_yearMonth}"

        data.set_param('Feature_Period', feature_period)

        feature_period_start = str(min_yearMonth)
        feature_period_end = str(max_yearMonth)
        feature_period_array = [feature_period_start, feature_period_end]

        if (int(feature_period_end[:4]) - int(feature_period_start[:4])) > 0:
            feature_period_duration = ((int(feature_period_end) - int(feature_period_start)) - 90)
        else:
            feature_period_duration = ((int(feature_period_end) - int(feature_period_start)) - 2)

        data.set_param('Feature_Period_Duration', feature_period_duration)

        data.set_param('totalBusinessDays', feature_period_duration * 21)

        data.set_param('totalBusinessWeeks', round(feature_period_duration * 4.5))

        data.set_param('totalBusinessMonths', feature_period_duration)

        if len(feature_period_array) == 2:
            if int(feature_period_array[0]) < int(feature_period_array[1]):
                feature_period_start = int(feature_period_array[0])
                feature_period_end = int(feature_period_array[1])

        if str(feature_period_end)[-1] == '1' and str(feature_period_end)[-2] == '0':
            feature_period_end_target = int(str(int(str(feature_period_end)[0:4]) - 1) + '10')
        elif str(feature_period_end)[-1] == '2' and str(feature_period_end)[-2] == '0':
            feature_period_end_target = int(str(int(str(feature_period_end)[0:4]) - 1) + '11')
        elif str(feature_period_end)[-1] == '3' and str(feature_period_end)[-2] == '0':
            feature_period_end_target = int(str(int(str(feature_period_end)[0:4]) - 1) + '12')
        else:
            feature_period_end_target = int(
                str(feature_period_end)[0:4] + '0' + str(int(str(feature_period_end)[-2:]) - 3))

        data.set_param('featurePeriodStart', feature_period_start)

        data.set_param('featurePeriodEnd', feature_period_end)

        data.set_param('featurePeriodEndTarget', feature_period_end_target)

    def create_email_features(self):
        data = Data.get_instance()
        hcp_fs_pdf = data.get_dataframe('hcp_fs_pdf')

        featurePeriod = data.get_param('Feature_Period')

        print(featurePeriod)

        featurePeriodDuration = data.get_param('Feature_Period_Duration')

        totalBusinessDays = featurePeriodDuration * 21

        totalBusinessWeeks = round(featurePeriodDuration * 4.5)

        totalBusinessMonths = featurePeriodDuration

        featurePeriodStart = data.get_param('featurePeriodStart')

        featurePeriodEnd = data.get_param('featurePeriodEnd')

        featurePeriodEndTarget = data.get_param('featurePeriodEndTarget')


        def filterDataBasedOnFeaturePeriod(rawFeatureStore, featurePeriodStart, featurePeriodEnd):
            '''
            Filter raw feature store to pass on based on the feature and target period defined.
            '''
            print(rawFeatureStore.columns)
            return rawFeatureStore[(rawFeatureStore['yearMonth'] >= featurePeriodStart) & (
                        rawFeatureStore['yearMonth'] <= featurePeriodEnd)]

        def numberEmailSent(rawFeatureStore):
            '''
            No of email sent in a given input data.
            '''
            return pd.merge(rawFeatureStore,
                            (rawFeatureStore.groupby("accountId")['emailSent1MonthCount'].agg(numberEmailSent='sum')),
                            how="left", on=["accountId"])

        def numberEmailOpen(rawFeatureStore):
            '''
            No of email opened in a given input data.
            '''
            return pd.merge(rawFeatureStore,
                            (rawFeatureStore.groupby("accountId")['emailOpen1MonthCount'].agg(numberEmailOpen='sum')),
                            how="left", on=["accountId"])

        def numberEmailClick(rawFeatureStore):
            '''
            No of email clicked in a given input data.
            '''
            return pd.merge(rawFeatureStore,
                            (rawFeatureStore.groupby("accountId")['emailClick1MonthCount'].agg(numberEmailClick='sum')),
                            how="left", on=["accountId"])

        def numberEmailOpenClick(rawFeatureStore):
            '''
            No of email opened and clicked in a given input data.
            '''
            rawFeatureStore['numberEmailOpenClick'] = rawFeatureStore['numberEmailOpen'] + rawFeatureStore[
                'numberEmailClick']

            return rawFeatureStore

        def percentEmailOpen(rawFeatureStore):
            '''
            Percentage no of email opened in a given input data.
            '''
            rawFeatureStore['percentEmailOpen'] = np.where(rawFeatureStore['numberEmailOpenClick'] > 0, (
                        rawFeatureStore['numberEmailOpen'] / rawFeatureStore['numberEmailOpenClick']).round(4), 0)

            return rawFeatureStore

        def percentEmailClick(rawFeatureStore):
            '''
            Percentage no of email clicked in a given input data.
            '''
            rawFeatureStore['percentEmailClick'] = np.where(rawFeatureStore['numberEmailOpenClick'] > 0, (
                        rawFeatureStore['numberEmailClick'] / rawFeatureStore['numberEmailOpenClick']).round(4), 0)

            return rawFeatureStore

        def percentEmailOpenClick(rawFeatureStore):
            '''
            Percentage no of email clicked in a given input data.
            '''
            rawFeatureStore['percentEmailOpenClick'] = np.where(rawFeatureStore['numberEmailSent'] > 0, (
                        rawFeatureStore['numberEmailOpenClick'] / rawFeatureStore['numberEmailSent']).round(4), 0)

            return rawFeatureStore

        def noSentEmailWeek(rawFeatureStore):
            '''
            Weekly no of in a given input data.
            '''
            rawFeatureStore['noSentEmailWeek'] = np.where(rawFeatureStore['emailSent1MonthWeekCount'] > 0, (
                        rawFeatureStore['numberEmailSent'] / rawFeatureStore['emailSent1MonthWeekCount']).round(4), 0)

            return rawFeatureStore

        def meanEmailOpenWeek(rawFeatureStore):
            '''
            Weekly no of eamil opened in a given input data.
            '''
            rawFeatureStore['meanEmailOpenWeek'] = np.where(rawFeatureStore['emailOpen1MonthWeekCount'] > 0, (
                        rawFeatureStore['numberEmailOpen'] / rawFeatureStore['emailOpen1MonthWeekCount']).round(4), 0)

            return rawFeatureStore

        def meanEmailClickWeek(rawFeatureStore):
            '''
            Weekly no of email clicked in a given input data.
            '''
            rawFeatureStore['meanEmailClickWeek'] = np.where(rawFeatureStore['emailClick1MonthWeekCount'] > 0, (
                        rawFeatureStore['numberEmailClick'] / rawFeatureStore['emailClick1MonthWeekCount']).round(4), 0)

            return rawFeatureStore

        def meanEmailOpenClickWeek(rawFeatureStore):
            '''
            Weekly no of email open clicked in a given input data.
            '''
            rawFeatureStore['meanEmailOpenClickWeek'] = np.where(rawFeatureStore['emailOpen1MonthWeekCount'] > 0, (
                        rawFeatureStore['numberEmailOpen'] / rawFeatureStore['emailOpen1MonthWeekCount']).round(4),
                                                                 0) + np.where(
                rawFeatureStore['emailClick1MonthWeekCount'] > 0,
                (rawFeatureStore['numberEmailClick'] / rawFeatureStore['emailClick1MonthWeekCount']).round(4), 0)

            return rawFeatureStore

        def meanTotalEmailOpenClickWeek(rawFeatureStore):
            '''
           Avg Weekly no of email clicked in a given input data.
            '''
            rawFeatureStore['meanTotalEmailOpenClickWeek'] = np.where(rawFeatureStore['noSentEmailWeek'] > 0, (
                        rawFeatureStore['numberEmailOpenClick'] / rawFeatureStore['noSentEmailWeek']).round(4), 0)

            return rawFeatureStore

        def numberEmailMonth(rawFeatureStore):
            '''
            Monthly avg no of email sent in a given data.
            '''
            rawFeatureStore['numberEmailMonth'] = np.where(featurePeriodDuration > 0,
                                                           rawFeatureStore['numberEmailSent'] / featurePeriodDuration,
                                                           0).round(4)

            return rawFeatureStore

        def meanEmailOpenMonth(rawFeatureStore):
            '''
            Monthly avg no of email open in a given data.
            '''
            rawFeatureStore['meanEmailOpenMonth'] = np.where(featurePeriodDuration > 0,
                                                             rawFeatureStore['numberEmailOpen'] / featurePeriodDuration,
                                                             0).round(4)

            return rawFeatureStore

        def meanEmailClickMonth(rawFeatureStore):
            '''
            Monthly avg no of email clicked in a given data.
            '''
            rawFeatureStore['meanEmailClickMonth'] = np.where(featurePeriodDuration > 0, rawFeatureStore[
                'numberEmailClick'] / featurePeriodDuration, 0).round(4)

            return rawFeatureStore

        def meanEmailOpenClickMonth(rawFeatureStore):
            '''
            Monthly avg no of email open clicked in a given data.
            '''
            rawFeatureStore['meanEmailOpenClickMonth'] = np.where(featurePeriodDuration > 0, (
                        rawFeatureStore['numberEmailOpen'] + rawFeatureStore[
                    'numberEmailClick']) / featurePeriodDuration, 0).round(4)

            return rawFeatureStore

        def meanTotalEmailOpenClickMonth(rawFeatureStore):
            '''
            Avg no of opened+clicked mails per month per total sent mails per month.
            '''
            rawFeatureStore['meanTotalEmailOpenClickMonth'] = np.where(
                (featurePeriodDuration > 0 and featurePeriodDuration > 0),
                ((rawFeatureStore['numberEmailOpen'] + rawFeatureStore['numberEmailClick']) / featurePeriodDuration) /
                rawFeatureStore['numberEmailSent'], 0).round(4)

            return rawFeatureStore

        def emailOpenRate(rawFeatureStore):
            '''
            No of email opened per mail sent.
            '''
            rawFeatureStore['emailOpenRate'] = np.where(rawFeatureStore['numberEmailSent'] > 0,
                                                        rawFeatureStore['numberEmailOpen'] / rawFeatureStore[
                                                            'numberEmailSent'], 0).round(4)

            return rawFeatureStore

        def emailClickRate(rawFeatureStore):
            '''
            No of email clicked per mail opened.
            '''
            rawFeatureStore['emailClickRate'] = np.where(rawFeatureStore['numberEmailOpen'] > 0,
                                                         rawFeatureStore['numberEmailClick'] / rawFeatureStore[
                                                             'numberEmailOpen'], 0).round(4)

            return rawFeatureStore

        def emailOpenClickRate(rawFeatureStore):
            '''
            No of email opened per mail sent.+ No of email clicked per mail opened.
            '''
            rawFeatureStore['emailOpenClickRate'] = (
                        rawFeatureStore['emailOpenRate'] + rawFeatureStore['emailClickRate']).round(4)

            return rawFeatureStore

        def emailTotalOpenMonth(rawFeatureStore):
            '''
            No of email opened in rencet month.
            '''

            return pd.merge(rawFeatureStore, (
                rawFeatureStore[rawFeatureStore['yearMonth'] == featurePeriodEnd].groupby("accountId")[
                    'emailOpen1MonthCount'].agg(emailTotalOpenMonth='sum')), how="left", on=["accountId"])

        def percentEmailTotalOpenMonth(rawFeatureStore):
            '''
            Email open percentage in rencet month.
            '''
            rawFeatureStore['percentEmailTotalOpenMonth'] = np.where(rawFeatureStore['numberEmailOpen'] > 0,
                                                                     rawFeatureStore['emailTotalOpenMonth'] /
                                                                     rawFeatureStore['numberEmailOpen'], 0)

            return rawFeatureStore

        def emailOpenRateMonth(rawFeatureStore, featurePeriodEnd):
            '''
            No of email opened per sent mail in rencet month.
            '''

            openRecent_sentRecent = pd.merge(
                rawFeatureStore[rawFeatureStore['yearMonth'] == featurePeriodEnd].groupby("accountId")[
                    'emailOpen1MonthCount'].agg(emailOpenRecentMonth='sum'),
                rawFeatureStore[rawFeatureStore['yearMonth'] == featurePeriodEnd].groupby("accountId")[
                    'emailSent1MonthCount'].agg(emailSentRecentMonth='sum'), how="left", on=["accountId"])

            rawFeatureStore['emailOpenRateMonth'] = np.where(((openRecent_sentRecent['emailSentRecentMonth'] > 0)),
                                                             openRecent_sentRecent['emailOpenRecentMonth'] /
                                                             openRecent_sentRecent['emailSentRecentMonth'], 0)

            return rawFeatureStore

        def emailClickRateMonth(rawFeatureStore, featurePeriodEnd):
            '''
            No of email clicked per sent mail in rencet month.
            '''

            clickRecent_openRecent = pd.merge(
                rawFeatureStore[rawFeatureStore['yearMonth'] == featurePeriodEnd].groupby("accountId")[
                    'emailClick1MonthCount'].agg(emailClickRecentMonth='sum'),
                rawFeatureStore[rawFeatureStore['yearMonth'] == featurePeriodEnd].groupby("accountId")[
                    'emailOpen1MonthCount'].agg(emailOpenRecentMonth='sum'), how="left", on=["accountId"])

            clickRecent_openRecent['emailClickRateMonth'] = np.where(
                ((clickRecent_openRecent['emailOpenRecentMonth'] > 0)), (
                            clickRecent_openRecent['emailClickRecentMonth'] / clickRecent_openRecent[
                        'emailOpenRecentMonth']).round(4), 0)
            res = rawFeatureStore.join(clickRecent_openRecent['emailClickRateMonth'], on='accountId')
            return res

        def __get_previous_month(yearMonthInt, monthsDelta=1):

            # Get Previous month based is string of format 'YYYYMM'
            yearMonthStr = str(yearMonthInt)
            yearMonthDt = datetime.strptime(yearMonthStr, '%Y%m')
            previousMonthDt = yearMonthDt - pd.DateOffset(months=monthsDelta)

            # Convert previous month to string of format 'YYYYMM'
            previousMonthStr = previousMonthDt.strftime('%Y%m')
            previousMonthInt = int(previousMonthStr)

            return previousMonthInt

        def emailTotalOpenPreviousMonth(rawFeatureStore):
            '''
            No of email opened in previous month.
            '''

            month_target = __get_previous_month(featurePeriodEnd)

            return pd.merge(rawFeatureStore, (
                rawFeatureStore[rawFeatureStore['yearMonth'] == month_target].groupby("accountId")[
                    'emailOpen1MonthCount'].agg(emailTotalOpenPreviousMonth='sum')), how="left", on=["accountId"])

        def percentEmailTotalOpenPreviousMonth(rawFeatureStore):
            '''
            Email open percentage in previous month.
            '''

            rawFeatureStore['percentEmailTotalOpenPreviousMonth'] = np.where(rawFeatureStore['numberEmailOpen'] > 0,
                                                                             rawFeatureStore[
                                                                                 'emailTotalOpenPreviousMonth'] /
                                                                             rawFeatureStore['numberEmailOpen'], 0)

            return rawFeatureStore

        def emailOpenRatePreviousMonth(rawFeatureStore, featurePeriodEnd):
            '''
            No of email opened per sent mail in previous month.
            '''

            month_target = __get_previous_month(featurePeriodEnd)

            openPrev_sentPrev = pd.merge(
                rawFeatureStore[rawFeatureStore['yearMonth'] == month_target].groupby("accountId")[
                    'emailOpen1MonthCount'].agg(emailOpenPrevMonth='sum'),
                rawFeatureStore[rawFeatureStore['yearMonth'] == month_target].groupby("accountId")[
                    'emailSent1MonthCount'].agg(emailSentPrevMonth='sum'), how="left", on=["accountId"])

            openPrev_sentPrev['emailOpenRatePreviousMonth'] = np.where(((openPrev_sentPrev['emailSentPrevMonth'] > 0)),
                                                                       openPrev_sentPrev['emailOpenPrevMonth'] /
                                                                       openPrev_sentPrev['emailSentPrevMonth'], 0)

            res = rawFeatureStore.join(openPrev_sentPrev['emailOpenRatePreviousMonth'], on='accountId')
            return res

        def emailClickRatePreviousMonth(rawFeatureStore, featurePeriodEnd):
            '''
            No of email clicked per sent mail in previous month.
            '''

            month_target = __get_previous_month(featurePeriodEnd)

            clickPrev_openPrev = pd.merge(
                rawFeatureStore[rawFeatureStore['yearMonth'] == month_target].groupby("accountId")[
                    'emailClick1MonthCount'].agg(emailClickPrevMonth='sum'),
                rawFeatureStore[rawFeatureStore['yearMonth'] == month_target].groupby("accountId")[
                    'emailOpen1MonthCount'].agg(emailOpenPrevMonth='sum'), how="left", on=["accountId"])

            clickPrev_openPrev['emailClickRatePreviousMonth'] = np.where(
                ((clickPrev_openPrev['emailOpenPrevMonth'] > 0)),
                clickPrev_openPrev['emailClickPrevMonth'] / clickPrev_openPrev['emailOpenPrevMonth'], 0)

            res = rawFeatureStore.join(clickPrev_openPrev['emailClickRatePreviousMonth'], on='accountId')
            return res

        def emailTotalOpenPreviousTwoMonth(rawFeatureStore, featurePeriodEnd):
            '''
            No of email opened in previous to previous month.
            '''

            month_target = __get_previous_month(featurePeriodEnd, 2)

            return pd.merge(rawFeatureStore, (
                rawFeatureStore[rawFeatureStore['yearMonth'] == month_target].groupby("accountId")[
                    'emailOpen1MonthCount'].agg(emailTotalOpenPreviousTwoMonth='sum')), how="left", on=["accountId"])

        def percentEmailTotalOpenPreviousTwoMonth(rawFeatureStore):
            '''
            Email open percentage in previous to previous month.
            '''

            rawFeatureStore['percentEmailTotalOpenPreviousTwoMonth'] = np.where(rawFeatureStore['numberEmailOpen'] > 0,
                                                                                rawFeatureStore[
                                                                                    'emailTotalOpenPreviousTwoMonth'] /
                                                                                rawFeatureStore['numberEmailOpen'], 0)

            return rawFeatureStore

        def emailOpenRatePreviousTwoMonth(rawFeatureStore, featurePeriodEnd):
            '''
            No of email opened per sent mail in previous to previous month.
            '''

            month_target = __get_previous_month(featurePeriodEnd, 2)

            openPrev_sentPrev = pd.merge(
                rawFeatureStore[rawFeatureStore['yearMonth'] == month_target].groupby("accountId")[
                    'emailOpen1MonthCount'].agg(emailOpenPrevMonth='sum'),
                rawFeatureStore[rawFeatureStore['yearMonth'] == month_target].groupby("accountId")[
                    'emailSent1MonthCount'].agg(emailSentPrevMonth='sum'), how="left", on=["accountId"])

            openPrev_sentPrev['emailOpenRatePreviousTwoMonth'] = np.where(
                ((openPrev_sentPrev['emailSentPrevMonth'] > 0)),
                openPrev_sentPrev['emailOpenPrevMonth'] / openPrev_sentPrev['emailSentPrevMonth'], 0)

            res = rawFeatureStore.join(openPrev_sentPrev['emailOpenRatePreviousTwoMonth'], on='accountId')
            return res

        def emailClickRatePreviousTwoMonth(rawFeatureStore, featurePeriodEnd):
            '''
            No of email clicked per sent mail in previous to previous month.
            '''

            month_target = __get_previous_month(featurePeriodEnd, 2)

            clickPrev_openPrev = pd.merge(
                rawFeatureStore[rawFeatureStore['yearMonth'] == month_target].groupby("accountId")[
                    'emailClick1MonthCount'].agg(emailClickPrevMonth='sum'),
                rawFeatureStore[rawFeatureStore['yearMonth'] == month_target].groupby("accountId")[
                    'emailOpen1MonthCount'].agg(emailOpenPrevMonth='sum'), how="left", on=["accountId"])

            clickPrev_openPrev['emailClickRatePreviousTwoMonth'] = np.where(
                ((clickPrev_openPrev['emailOpenPrevMonth'] > 0)),
                clickPrev_openPrev['emailClickPrevMonth'] / clickPrev_openPrev['emailOpenPrevMonth'], 0)

            res = rawFeatureStore.join(clickPrev_openPrev['emailClickRatePreviousTwoMonth'], on='accountId')
            return res

        def numberEmailSentDay(rawFeatureStore):
            '''
            No of days the email has sent in a given input data.
            '''
            return pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['emailSent1MonthDayCount'].agg(numberEmailSentDay='sum')),
                            how="left", on=["accountId"])

        def percentNumberEmailSentDay(rawFeatureStore, totalBusinessDays):
            '''
            No of days the email has sent in a given input data.
            '''

            rawFeatureStore['percentNumberEmailSentDay'] = np.where(totalBusinessDays > 0, (
                        rawFeatureStore['numberEmailSentDay'] / totalBusinessDays).round(4), 0)

            return rawFeatureStore

        def numberEmailSentYearWeek(rawFeatureStore):
            '''
            No of unique weeks email has sent in a given input data.
            '''
            return pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['emailSent1MonthWeekCount'].agg(numberEmailSentYearWeek='sum')),
                            how="left", on=["accountId"])

        def numberEmailSentWrtYearWeek(rawFeatureStore, totalBusinessWeeks):
            '''
            No of unique weeks email has sent with respect to toal business weeks in a given input data.
            '''
            rawFeatureStore['numberEmailSentWrtYearWeek'] = np.where(totalBusinessWeeks > 0, (
                        rawFeatureStore['numberEmailSentYearWeek'] / totalBusinessWeeks).round(4), 0)

            return rawFeatureStore

        def numberEmailSentMonth(rawFeatureStore):
            '''
            No of unique months email has sent in a given input data.
            '''
            return pd.merge(rawFeatureStore, (
                rawFeatureStore[rawFeatureStore['emailSent1MonthCount'] > 0].groupby("accountId")['yearMonth'].agg(
                    numberEmailSentMonth='nunique')), how="left", on=["accountId"])

        def percentEmailSentMonth(rawFeatureStore, totalBusinessMonths):
            '''
            No of unique months email has sent with respect to toal business months in a given input data.
            '''
            rawFeatureStore['percentEmailSentMonth'] = np.where(totalBusinessMonths > 0, (
                        rawFeatureStore['numberEmailSentMonth'] / totalBusinessMonths).round(4), 0)

            return rawFeatureStore

        def emailHighFacilityFlag(rawFeatureStore, highEmailThreshold=98):
            '''
            Is hcp belongs to the facility having total no of email open clicked greater than higher threshold.
            '''

            threshold = np.percentile(rawFeatureStore['numberEmailOpenClick'], highEmailThreshold)

            facilityLevelEmailData = rawFeatureStore.groupby('facilityId')['numberEmailOpenClick'].agg(
                facilityLevelEmails='sum')

            rawFeatureStore['emailHighFacilityFlag'] = np.where((pd.merge(rawFeatureStore, facilityLevelEmailData,
                                                                          how="left", on=["facilityId"])[
                'facilityLevelEmails']) >= threshold, 1, 0)

            return rawFeatureStore

        def emailLowFacilityFlag(rawFeatureStore, lowEmailThreshold=0):
            '''
            Is hcp belongs to the facility having total no of email open clicked smaller than lower threshold.
            '''

            facilityLevelEmailData = rawFeatureStore.groupby('facilityId')['numberEmailOpenClick'].agg(
                facilityLevelEmails='sum')

            rawFeatureStore['emailLowFacilityFlag'] = np.where((pd.merge(rawFeatureStore, facilityLevelEmailData,
                                                                         how="left", on=["facilityId"])[
                'facilityLevelEmails']) <= lowEmailThreshold, 1, 0)

            return rawFeatureStore

        def emailHighHcpFlag(rawFeatureStore, highEmailThreshold=98):
            '''
            Is hcp having total no of email open clicked greater than higher threshold.
            '''

            threshold = np.percentile(rawFeatureStore['numberEmailOpenClick'], highEmailThreshold)

            rawFeatureStore['emailHighHcpFlag'] = np.where(rawFeatureStore['numberEmailOpenClick'] >= threshold, 1, 0)

            return rawFeatureStore

        def emailLowHcpFlag(rawFeatureStore, lowEmailThreshold=0):
            '''
            Is hcp having total no of email open clicked greater than lower threshold.
            '''

            rawFeatureStore['emailLowHcpFlag'] = np.where(rawFeatureStore['numberEmailOpenClick'] <= lowEmailThreshold,
                                                          1, 0)

            return rawFeatureStore

        def numberHcpInFacility(rawFeatureStore, lowEmailThreshold=0):
            '''
            Number of HCP's in the facility.
            '''
            rawFeatureStore['numberHcpInFacility'] = rawFeatureStore['numberHcpFacility']

            return rawFeatureStore

        def stdEmailOpenClickDay(rawFeatureStore):
            '''
            Daily email open+click std deviation.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['emailOpen1MonthDayCount'].agg(np.std,
                                                                                                          ddof=1).to_frame(
                                'stdEmailOpenClickDay').reset_index(), how="left", on=["accountId"])

        def stdEmailOpenClickWeek(rawFeatureStore):
            '''
            Weekly email open+click std deviation.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['emailOpen1MonthWeekCount'].agg(np.std,
                                                                                                           ddof=1).to_frame(
                                'stdEmailOpenClickWeek').reset_index(), how="left", on=["accountId"])

        def stdEmailOpenClickMonth(rawFeatureStore):
            '''
            Monthly email open+click std deviation.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['emailOpen1MonthCount'].agg(np.std,
                                                                                                       ddof=1).to_frame(
                                'stdEmailOpenClickMonth').reset_index(), how="left", on=["accountId"])

        def meanEmailOpenGapDayOver1Month(rawFeatureStore):
            '''
            Daily email gap over recent month.
            '''
            rawFeatureStore['meanEmailOpenGapDayOver1Month'] = rawFeatureStore['meanEmailOpenGapsOver1Month'].round(4)

            return rawFeatureStore

        def meanEmailOpenGapDayOver3Month(rawFeatureStore):
            '''
            Daily email gap over recent 3 months.
            '''
            rawFeatureStore['meanEmailOpenGapDayOver3Month'] = rawFeatureStore['meanEmailOpenGapsOver3Month'].round(4)

            return rawFeatureStore

        def meanEmailOpenGapDayOver6Month(rawFeatureStore):
            '''
            Daily email open gap over recent 6 months.
            '''
            rawFeatureStore['meanEmailOpenGapDayOver6Month'] = rawFeatureStore['meanEmailOpenGapsOver6Month'].round(4)

            return rawFeatureStore

        def yearlyAvgMeanEmailOpenGapDayOver1Month(rawFeatureStore):
            '''
            Yearly avg mean  email open gap over recent month.
            '''
            res = rawFeatureStore.groupby('accountId')['meanEmailOpenGapDayOver1Month'].agg(sumMeanEmails='sum')
            res['yearlyAvgMeanEmailOpenGapDayOver1Month'] = res['sumMeanEmails'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanEmailOpenGapDayOver1Month'], on='accountId')

        def yearlyAvgMeanEmailOpenGapDayOver3Month(rawFeatureStore):
            '''
            Yearly avg mean email open gap over recent 3 months.
            '''
            res = rawFeatureStore.groupby('accountId')['meanEmailOpenGapDayOver3Month'].agg(sumMeanEmails='sum')
            res['yearlyAvgMeanEmailOpenGapDayOver3Month'] = res['sumMeanEmails'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanEmailOpenGapDayOver3Month'], on='accountId')

        def yearlyAvgMeanEmailOpenGapDayOver6Month(rawFeatureStore):
            '''
            Yearly avg mean email open gap over recent 6 months.
            '''
            res = rawFeatureStore.groupby('accountId')['meanEmailOpenGapDayOver6Month'].agg(sumMeanEmails='sum')
            res['yearlyAvgMeanEmailOpenGapDayOver6Month'] = res['sumMeanEmails'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanEmailOpenGapDayOver6Month'], on='accountId')

        def emailOpenGapWeeks(rawFeatureStore):
            '''
            Email Open gap in weeks.
            '''

            rawFeatureStore['emailOpenGapWeeks'] = np.where(rawFeatureStore['emailOpen1MonthWeekCount'] > 0,
                                                            rawFeatureStore['meanEmailOpenGapsOver1Month'] /
                                                            rawFeatureStore['emailOpen1MonthWeekCount'], 0)

            return rawFeatureStore

        def meanEmailSentGapMonth(rawFeatureStore):
            '''
            Email sent gap over month.
            '''

            rawFeatureStore['meanEmailSentGapMonth'] = np.where(rawFeatureStore['meanEmailSentGapsOver1Month'] > 0,
                                                                rawFeatureStore['meanEmailSentGapsOver1Month'], 0)

            return rawFeatureStore

        copy_hcp_fs_pdf = hcp_fs_pdf

        email_fs_pdf = copy_hcp_fs_pdf.pipe(filterDataBasedOnFeaturePeriod, featurePeriodStart, featurePeriodEndTarget) \
            .pipe(numberEmailSent) \
            .pipe(numberEmailOpen) \
            .pipe(numberEmailClick) \
            .pipe(numberEmailOpenClick) \
            .pipe(percentEmailOpen) \
            .pipe(percentEmailClick) \
            .pipe(percentEmailOpenClick) \
            .pipe(noSentEmailWeek) \
            .pipe(meanEmailOpenWeek) \
            .pipe(meanEmailOpenClickWeek) \
            .pipe(meanEmailClickWeek) \
            .pipe(meanTotalEmailOpenClickWeek) \
            .pipe(numberEmailMonth) \
            .pipe(meanEmailOpenMonth) \
            .pipe(meanEmailClickMonth) \
            .pipe(meanEmailOpenClickMonth) \
            .pipe(meanTotalEmailOpenClickMonth) \
            .pipe(emailOpenRate) \
            .pipe(emailClickRate) \
            .pipe(emailOpenClickRate) \
            .pipe(emailTotalOpenMonth) \
            .pipe(percentEmailTotalOpenMonth) \
            .pipe(emailClickRateMonth, featurePeriodEnd) \
            .pipe(emailTotalOpenPreviousMonth) \
            .pipe(percentEmailTotalOpenPreviousMonth) \
            .pipe(emailOpenRatePreviousMonth, featurePeriodEnd) \
            .pipe(emailClickRatePreviousMonth, featurePeriodEnd) \
            .pipe(emailTotalOpenPreviousTwoMonth, featurePeriodEnd) \
            .pipe(percentEmailTotalOpenPreviousTwoMonth) \
            .pipe(emailOpenRatePreviousTwoMonth, featurePeriodEnd) \
            .pipe(emailClickRatePreviousTwoMonth, featurePeriodEnd) \
            .pipe(numberEmailSentDay) \
            .pipe(percentNumberEmailSentDay, totalBusinessDays) \
            .pipe(numberEmailSentYearWeek) \
            .pipe(numberEmailSentWrtYearWeek, totalBusinessWeeks) \
            .pipe(numberEmailSentMonth) \
            .pipe(percentEmailSentMonth, totalBusinessMonths) \
            .pipe(emailHighFacilityFlag) \
            .pipe(emailLowFacilityFlag) \
            .pipe(emailHighHcpFlag) \
            .pipe(emailLowHcpFlag) \
            .pipe(numberHcpInFacility) \
            .pipe(stdEmailOpenClickDay) \
            .pipe(stdEmailOpenClickWeek) \
            .pipe(stdEmailOpenClickMonth) \
            .pipe(meanEmailOpenGapDayOver1Month) \
            .pipe(meanEmailOpenGapDayOver3Month) \
            .pipe(meanEmailOpenGapDayOver6Month) \
            .pipe(yearlyAvgMeanEmailOpenGapDayOver1Month) \
            .pipe(yearlyAvgMeanEmailOpenGapDayOver3Month) \
            .pipe(yearlyAvgMeanEmailOpenGapDayOver6Month) \
            .pipe(emailOpenGapWeeks) \
            .pipe(meanEmailSentGapMonth)

        colsToDrop = hcp_fs_pdf.columns.to_list()
        if 'accountId' in colsToDrop:
            colsToDrop.remove('accountId')
        if 'accountUid' in colsToDrop:
            colsToDrop.remove('accountUid')

        idx = email_fs_pdf.groupby(['accountId'])['indexMonth'].transform(max) == email_fs_pdf['indexMonth']

        # Copy input to output
        email_fs_pdf = email_fs_pdf[idx].drop(columns=colsToDrop)

        email_fs_pdf.drop_duplicates(subset=['accountId'], keep='first', inplace=True)

        data.set_dataframe('email_fs_pdf', email_fs_pdf)

    def create_visit_features(self):

        data = Data.get_instance()
        hcp_fs_pdf = data.get_dataframe('hcp_fs_pdf')

        featurePeriod = data.get_param('Feature_Period')

        print(featurePeriod)

        featurePeriodDuration = data.get_param('Feature_Period_Duration')

        totalBusinessDays = featurePeriodDuration * 21

        totalBusinessWeeks = round(featurePeriodDuration * 4.5)

        totalBusinessMonths = featurePeriodDuration

        featurePeriodStart = data.get_param('featurePeriodStart')

        featurePeriodEnd = data.get_param('featurePeriodEnd')

        featurePeriodEndTarget = data.get_param('featurePeriodEndTarget')

        def filterDataBasedOnFeaturePeriod(rawFeatureStore, featurePeriodStart, featurePeriodEnd):
            '''
            Filter raw feature store to pass on based on the feature and target period defined.
            '''
            print(rawFeatureStore.columns)
            return rawFeatureStore[(rawFeatureStore['yearMonth'] >= featurePeriodStart) & (
                        rawFeatureStore['yearMonth'] <= featurePeriodEnd)]

        def numberTotalVisits(rawFeatureStore):
            '''
            Total no of completed visits in a given feature period.
            '''
            return pd.merge(rawFeatureStore,
                            (rawFeatureStore.groupby("accountId")['visit1MonthCount'].agg(numberTotalVisits='sum')),
                            how="left", on=["accountId"])

        def meanVisitsDay(rawFeatureStore):
            '''
            Daily avg no of completed visits in a given feature period.
            '''
            rawFeatureStore['meanVisitsDay'] = (pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['visit1MonthDayCount'].agg(visitTotalDays='sum')), how="left",
                                                         on=["accountId"])).apply(
                lambda row: row['numberTotalVisits'] / row['visitTotalDays'] if row['visitTotalDays'] > 0 else 0,
                axis=1).round(4)

            return rawFeatureStore

        def meanVisitsWeek(rawFeatureStore):
            '''
            Weekly avg no of visits in a given feature period.
            '''
            rawFeatureStore['meanVisitsWeek'] = (pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['visit1MonthWeekCount'].agg(visitTotalWeeks='sum')), how="left",
                                                          on=["accountId"])).apply(
                lambda row: row['numberTotalVisits'] / row['visitTotalWeeks'] if row['visitTotalWeeks'] > 0 else 0,
                axis=1).round(4)

            return rawFeatureStore

        def meanVisitsMonth(rawFeatureStore):
            '''
            Monthly avg no of completed visits in a given feature period.
            '''
            rawFeatureStore['meanVisitsMonth'] = np.where(featurePeriodDuration > 0,
                                                          rawFeatureStore['numberTotalVisits'] / featurePeriodDuration,
                                                          0).round(4)

            return rawFeatureStore

        def numberVisitsDay(rawFeatureStore):
            '''
            Days spent to complete the visits in a given feature period.
            '''

            return pd.merge(rawFeatureStore,
                            (rawFeatureStore.groupby("accountId")['visit1MonthDayCount'].agg(numberVisitsDay='sum')),
                            how="left", on=["accountId"])

        def percentNumberVisitsDay(rawFeatureStore):
            '''
            Percentage no of days spent to complete the visits in a given feature period.
            '''

            rawFeatureStore['percentNumberVisitsDay'] = rawFeatureStore.apply(
                lambda row: row['numberVisitsDay'] / totalBusinessDays if totalBusinessDays > 0 else 0, axis=1).round(4)

            return rawFeatureStore

        def visitHighHcpFlag(rawFeatureStore, highVisitsThreshold=98):
            '''
            High no of Visits Flag stating that the total no of visits are high or not based on threshold.
            '''

            threshold = np.percentile(rawFeatureStore['numberTotalVisits'], highVisitsThreshold)

            rawFeatureStore['visitHighHcpFlag'] = np.where(rawFeatureStore['numberTotalVisits'] >= threshold, 1, 0)

            return rawFeatureStore

        def visitLowHcpFlag(rawFeatureStore, lowVisitsThreshold=2):
            '''
            Low no of Visits Flag stating that the total no of visits are low or not based on threshold.
            '''
            threshold = np.percentile(rawFeatureStore['numberTotalVisits'], lowVisitsThreshold)

            rawFeatureStore['visitLowHcpFlag'] = np.where(rawFeatureStore['numberTotalVisits'] <= threshold, 1, 0)

            return rawFeatureStore

        ## Note : visit1MonthWeekCount to be added in Feature Store

        def tenureWeek(rawFeatureStore):
            '''
            Total no of unique weeks spents for visits in a given feature period.
            '''
            return pd.merge(rawFeatureStore,
                            (rawFeatureStore.groupby("accountId")['visit1MonthWeekCount'].agg(tenureWeek='sum')),
                            how="left", on=["accountId"])

        ## Note : visit1MonthWeekCount to be added in Feature Store

        def percentTenureWeek(rawFeatureStore):
            '''
            Percentage no of unique weeks spents for visits in a given feature period.
            '''
            rawFeatureStore['percentTenureWeek'] = rawFeatureStore.apply(
                lambda row: row['tenureWeek'] / totalBusinessWeeks if totalBusinessWeeks > 0 else 0, axis=1).round(4)

            return rawFeatureStore

        def tenureYearMonth(rawFeatureStore):
            '''
            no of Year_Month spent in completed the visits in a given feature period.
            '''

            return pd.merge(rawFeatureStore, (
                rawFeatureStore[rawFeatureStore['visit1MonthCount'] > 0].groupby("accountId")['yearMonth'].agg(
                    tenureYearMonth='nunique')), how="left", on=["accountId"])

        def percentTenureYearMonth(rawFeatureStore):
            '''
            Percentage no of Year_Month spent in completed the visits in a given feature period.
            '''

            rawFeatureStore['percentTenureYearMonth'] = rawFeatureStore.apply(
                lambda row: row['tenureYearMonth'] / featurePeriodDuration if featurePeriodDuration > 0 else 0,
                axis=1).round(4)

            return rawFeatureStore

        # Note : meanVisitGapsOver1Month yet to be added in Feature Store

        def meanGapYearMonth(rawFeatureStore):
            '''
            No of completed visits in a given feature period.
            '''
            return pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['meanVisitGapsOver1Month'].agg(meanGapYearMonth='sum')),
                            how="left", on=["accountId"])

        def facilityVisitHighFlag(rawFeatureStore, highVisitThreshold=98):
            '''
            Is hcp belongs to the facility having total no of visits greater than higher threshold.
            '''

            threshold = np.percentile(rawFeatureStore['numberTotalVisits'], highVisitThreshold)

            facilityLevelVisitData = rawFeatureStore.groupby('facilityId')['visit1MonthCount'].agg(
                facilityLevelVisits='sum')

            rawFeatureStore['facilityVisitHighFlag'] = np.where((pd.merge(rawFeatureStore, facilityLevelVisitData,
                                                                          how="left", on=["facilityId"])[
                'facilityLevelVisits']) >= threshold, 1, 0)

            return rawFeatureStore

        def facilityVisitLowFlag(rawFeatureStore, lowVisitThreshold=2):
            '''
            Is hcp belongs to the facility having total no of visits greater than higher threshold.
            '''

            threshold = np.percentile(rawFeatureStore['numberTotalVisits'], lowVisitThreshold)

            facilityLevelVisitData = rawFeatureStore.groupby('facilityId')['visit1MonthCount'].agg(
                facilityLevelVisits='sum')

            rawFeatureStore['facilityVisitLowFlag'] = np.where((pd.merge(rawFeatureStore, facilityLevelVisitData,
                                                                         how="left", on=["facilityId"])[
                'facilityLevelVisits']) <= threshold, 1, 0)

            return rawFeatureStore

        def stdVisitsDay(rawFeatureStore):
            '''
            Daily visit std dev.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['visit1MonthDayCount'].agg(np.std,
                                                                                                      ddof=1).to_frame(
                                'stdVisitsDay').reset_index(), how="left", on=["accountId"])

        def stdVisitsWeek(rawFeatureStore):
            '''
            Weekly visit std dev.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['visit1MonthWeekCount'].agg(np.std,
                                                                                                       ddof=1).to_frame(
                                'stdVisitsWeek').reset_index(), how="left", on=["accountId"])

        def stdVisitsMonth(rawFeatureStore):
            '''
            Monthly visit std dev.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['visit1MonthCount'].agg(np.std,
                                                                                                   ddof=1).to_frame(
                                'stdVisitsMonth').reset_index(), how="left", on=["accountId"])

        def meanVisitGapOver1Month(rawFeatureStore):
            '''
            Daily visit gap over recent month.
            '''
            rawFeatureStore['meanVisitGapOver1Month'] = rawFeatureStore['meanVisitGapsOver1Month'].round(4)

            return rawFeatureStore

        def meanVisitGapOver3Month(rawFeatureStore):
            '''
            Daily visit gap over recent 3 months.
            '''
            rawFeatureStore['meanVisitGapOver3Month'] = rawFeatureStore['meanVisitGapsOver3Month'].round(4)

            return rawFeatureStore

        def meanVisitGapOver6Month(rawFeatureStore):
            '''
            Daily visit gap over recent 6 months.
            '''
            rawFeatureStore['meanVisitGapOver6Month'] = rawFeatureStore['meanVisitGapsOver6Month'].round(4)

            return rawFeatureStore

        def yearlyAvgMeanVisitGapOver1Month(rawFeatureStore):
            '''
            Yearly avg mean visit gap over recent month.
            '''
            res = rawFeatureStore.groupby('accountId')['meanVisitGapsOver1Month'].agg(sumMeanVisits='sum')
            res['yearlyAvgMeanVisitGapOver1Month'] = res['sumMeanVisits'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanVisitGapOver1Month'], on='accountId')

        def yearlyAvgMeanVisitGapOver3Month(rawFeatureStore):
            '''
            Yearly avg mean visit gap over recent 3 months.
            '''
            res = rawFeatureStore.groupby('accountId')['meanVisitGapsOver3Month'].agg(sumMeanVisits='sum')
            res['yearlyAvgMeanVisitGapOver3Month'] = res['sumMeanVisits'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanVisitGapOver3Month'], on='accountId')

        def yearlyAvgMeanVisitGapOver6Month(rawFeatureStore):
            '''
            Yearly avg mean visit gap over recent 6 months.
            '''
            res = rawFeatureStore.groupby('accountId')['meanVisitGapsOver6Month'].agg(sumMeanVisits='sum')
            res['yearlyAvgMeanVisitGapOver6Month'] = res['sumMeanVisits'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanVisitGapOver6Month'], on='accountId')

        def meanGapYearWeek(rawFeatureStore):
            '''
            Visits gap in weeks.
            '''

            rawFeatureStore['meanVisitGapsOver1Month'] = np.where(rawFeatureStore['visit1MonthWeekCount'] > 0,
                                                                  rawFeatureStore['meanVisitGapsOver1Month'] /
                                                                  rawFeatureStore['visit1MonthWeekCount'], 0)

            return rawFeatureStore

        def monthlySlopeInterceptErrorVisits1Month(rawFeatureStore, featurePeriodDuration):
            '''
            Slope intercept and error for monthly visits.
            '''

            res1 = rawFeatureStore.groupby(['accountId', 'yearMonth'])['visit1MonthCount'].agg(Sum='sum')

            res2 = (res1.groupby('accountId').agg({'Sum': lambda x: list(x)}))

            res2['activeMonths'] = res2.apply(lambda x: ([1 if visits > 0 else 0 for visits in x['Sum']]), axis=1)

            # indexes = list(range(1,len(x['activeMonths'])+1))

            aa = res2.apply(lambda x: stats.linregress(list(range(1, len(x['activeMonths']) + 1)), x['activeMonths']),
                            axis=1)

            c = pd.DataFrame(aa).rename(columns={0: 'results'})

            finalRes = pd.DataFrame([*c.results], c.index)

            return pd.merge(rawFeatureStore, finalRes[['slope', 'intercept', 'stderr']], on="accountId",
                            how="left").rename(
                columns={'slope': 'monthlySlopeVisits1Month', 'intercept': 'monthlyInterceptVisits1Month',
                         'stderr': 'monthlyErrorVisits1Month'})

        def monthlySlopeInterceptErrorVisits3Month(rawFeatureStore, featurePeriodDuration):
            '''
            Slope intercept and error for 3 Monthly visits.
            '''
            res1 = rawFeatureStore.groupby(['accountId', 'yearMonth'])['visit1MonthCount'].agg(Sum='sum')

            res2 = (res1.groupby('accountId').agg({'Sum': lambda x: list(x)}))

            res2['sum3Months'] = res2.apply(lambda x: np.convolve(x['Sum'], np.ones(3, dtype=int), mode='valid'),
                                            axis=1)

            res2['activeMonths'] = res2.apply(lambda x: ([1 if visits > 0 else 0 for visits in x['sum3Months']]),
                                              axis=1)

            # indexes = list(range(1,featurePeriodDuration-1))

            aa = res2.apply(lambda x: stats.linregress(list(range(1, len(x['activeMonths']) + 1)), x['activeMonths']),
                            axis=1)

            c = pd.DataFrame(aa).rename(columns={0: 'results'})

            finalRes = pd.DataFrame([*c.results], c.index)

            return pd.merge(rawFeatureStore, finalRes[['slope', 'intercept', 'stderr']], on="accountId",
                            how="left").rename(
                columns={'slope': 'monthlySlopeVisits3Month', 'intercept': 'monthlyInterceptVisits3Month',
                         'stderr': 'monthlyErrorVisits3Month'})

        copy_hcp_fs_pdf = hcp_fs_pdf
        visit_fs_pdf = copy_hcp_fs_pdf.pipe(filterDataBasedOnFeaturePeriod, featurePeriodStart, featurePeriodEndTarget) \
            .pipe(numberTotalVisits) \
            .pipe(meanVisitsDay) \
            .pipe(meanVisitsWeek) \
            .pipe(meanVisitsMonth) \
            .pipe(numberVisitsDay) \
            .pipe(percentNumberVisitsDay) \
            .pipe(visitHighHcpFlag) \
            .pipe(visitLowHcpFlag) \
            .pipe(tenureWeek) \
            .pipe(percentTenureWeek) \
            .pipe(tenureYearMonth) \
            .pipe(percentTenureYearMonth) \
            .pipe(meanGapYearMonth) \
            .pipe(facilityVisitHighFlag) \
            .pipe(facilityVisitLowFlag) \
            .pipe(stdVisitsDay) \
            .pipe(stdVisitsWeek) \
            .pipe(stdVisitsMonth) \
            .pipe(meanVisitGapOver1Month) \
            .pipe(meanVisitGapOver3Month) \
            .pipe(meanVisitGapOver6Month) \
            .pipe(yearlyAvgMeanVisitGapOver1Month) \
            .pipe(yearlyAvgMeanVisitGapOver3Month) \
            .pipe(yearlyAvgMeanVisitGapOver6Month) \
            .pipe(meanGapYearWeek) \
            .pipe(monthlySlopeInterceptErrorVisits1Month, featurePeriodDuration) \
            .pipe(monthlySlopeInterceptErrorVisits3Month, featurePeriodDuration)

        colsToDrop = hcp_fs_pdf.columns.to_list()
        if 'accountId' in colsToDrop:
            colsToDrop.remove('accountId')
        if 'accountUid' in colsToDrop:
            colsToDrop.remove('accountUid')

        idx = visit_fs_pdf.groupby(['accountId'])['indexMonth'].transform(max) == visit_fs_pdf['indexMonth']

        # Copy input to output
        visit_fs_pdf = visit_fs_pdf[idx].drop(columns=colsToDrop)

        visit_fs_pdf.drop_duplicates(subset=['accountId'], keep='first', inplace=True)

        data.set_dataframe('visit_fs_pdf', visit_fs_pdf)

    def create_virtual_visit_features(self):

        data = Data.get_instance()
        hcp_fs_pdf = data.get_dataframe('hcp_fs_pdf')

        featurePeriod = data.get_param('Feature_Period')

        print(featurePeriod)

        featurePeriodDuration = data.get_param('Feature_Period_Duration')

        totalBusinessDays = featurePeriodDuration * 21

        totalBusinessWeeks = round(featurePeriodDuration * 4.5)

        totalBusinessMonths = featurePeriodDuration

        featurePeriodStart = data.get_param('featurePeriodStart')

        featurePeriodEnd = data.get_param('featurePeriodEnd')

        featurePeriodEndTarget = data.get_param('featurePeriodEndTarget')

        def filterDataBasedOnFeaturePeriod(rawFeatureStore, featurePeriodStart, featurePeriodEnd):
            '''
            Filter raw feature store to pass on based on the feature and target period defined.
            '''
            print(rawFeatureStore.columns)
            return rawFeatureStore[(rawFeatureStore['yearMonth'] >= featurePeriodStart) & (
                        rawFeatureStore['yearMonth'] <= featurePeriodEnd)]

        def numberTotalVirtualVisits(rawFeatureStore):
            '''
            Total no of virtual visits in a given feature period.
            '''
            return pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['virtualVisit1MonthCount'].agg(numberTotalVirtualVisits='sum')),
                            how="left", on=["accountId"])

        def meanVirtualVisitsDay(rawFeatureStore):
            '''
            Daily avg no of virtual visits in a given feature period.
            '''
            rawFeatureStore['meanVirtualVisitsDay'] = (pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['virtualVisit1MonthDayCount'].agg(virtualVisitTotalDays='sum')),
                                                                how="left", on=["accountId"])).apply(
                lambda row: row['numberTotalVirtualVisits'] / row['virtualVisitTotalDays'] if row[
                                                                                                  'virtualVisitTotalDays'] > 0 else 0,
                axis=1).round(4)

            return rawFeatureStore

        def meanVirtualVisitsWeek(rawFeatureStore):
            '''
            Weekly avg no of virtual visits in a given feature period.
            '''
            rawFeatureStore['meanVirtualVisitsWeek'] = (pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['virtualVisit1MonthWeekCount'].agg(virtualVisitTotalWeeks='sum')),
                                                                 how="left", on=["accountId"])).apply(
                lambda row: row['numberTotalVirtualVisits'] / row['virtualVisitTotalWeeks'] if row[
                                                                                                   'virtualVisitTotalWeeks'] > 0 else 0,
                axis=1).round(4)

            return rawFeatureStore

        def meanVirtualVisitsMonth(rawFeatureStore):
            '''
            Monthly avg no of virtual visits in a given feature period.
            '''
            rawFeatureStore['meanVirtualVisitsMonth'] = np.where(featurePeriodDuration > 0, rawFeatureStore[
                'numberTotalVirtualVisits'] / featurePeriodDuration, 0).round(4)

            return rawFeatureStore

        def numberVirtualVisitsDay(rawFeatureStore):
            '''
            Days spent on the virtual visits in a given feature period.
            '''

            return pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['virtualVisit1MonthDayCount'].agg(numberVirtualVisitsDay='sum')),
                            how="left", on=["accountId"])

        def percentNumberVirtualVisitsDay(rawFeatureStore):
            '''
            Percentage no of days spent to complete the virtual visits in a given feature period.
            '''

            rawFeatureStore['percentNumberVirtualVisitsDay'] = rawFeatureStore.apply(
                lambda row: row['numberVirtualVisitsDay'] / totalBusinessDays if totalBusinessDays > 0 else 0,
                axis=1).round(4)

            return rawFeatureStore

        def virtualVisitHighHcpFlag(rawFeatureStore, highVisitsThreshold=98):
            '''
            High no of virtual visits Flag stating that the total no of virtual visits are high or not based on threshold.
            '''
            high_threshold = np.percentile(rawFeatureStore['numberTotalVirtualVisits'], highVisitsThreshold)

            rawFeatureStore['virtualVisitHighHcpFlag'] = np.where(
                rawFeatureStore['numberTotalVirtualVisits'] >= high_threshold, 1, 0)

            return rawFeatureStore

        def virtualVisitLowHcpFlag(rawFeatureStore, lowVirtualVisitsThreshold=2):
            '''
            Low no of Visits Flag stating that the total no of virtual visits are low or not based on threshold.
            '''

            low_threshold = np.percentile(rawFeatureStore['numberTotalVirtualVisits'], lowVirtualVisitsThreshold)

            rawFeatureStore['virtualVisitLowHcpFlag'] = np.where(
                rawFeatureStore['numberTotalVirtualVisits'] <= low_threshold, 1, 0)

            return rawFeatureStore

        ## Note : numberofWeeksWithActiveInteractionsForVisit to be added in Feature Store

        def tenureWeekVirtualVisits(rawFeatureStore):
            '''
            Total no of unique weeks spents for virtual visits in a given feature period.
            '''
            return pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['virtualVisit1MonthWeekCount'].agg(tenureWeekVirtualVisits='sum')),
                            how="left", on=["accountId"])

        ## Note : numberofWeeksWithActiveInteractionsForVisit to be added in Feature Store

        def percentTenureWeekVirtualVisits(rawFeatureStore):
            '''
            Percentage no of unique weeks spents for virtual visits in a given feature period.
            '''
            rawFeatureStore['percentTenureWeekVirtualVisits'] = np.where(totalBusinessWeeks > 0, (
                        rawFeatureStore['tenureWeekVirtualVisits'] / totalBusinessWeeks).round(4), 0)

            return rawFeatureStore

        def tenureYearMonthVirtualVisit(rawFeatureStore):
            '''
            no of Year_Month spent in completed the virtual visits in a given feature period.
            '''

            return pd.merge(rawFeatureStore, (
                rawFeatureStore[rawFeatureStore['virtualVisit1MonthCount'] > 0].groupby("accountId")['yearMonth'].agg(
                    tenureYearMonthVirtualVisit='nunique')), how="left", on=["accountId"])

        def percentTenureYearMonthVirtualVisit(rawFeatureStore):
            '''
            Percentage no of Year_Month spent in completed the virtual visits in a given feature period.
            '''

            rawFeatureStore['percentTenureYearMonthVirtualVisit'] = rawFeatureStore.apply(lambda row: row[
                                                                                                          'tenureYearMonthVirtualVisit'] / featurePeriodDuration if featurePeriodDuration > 0 else 0,
                                                                                          axis=1).round(4)

            return rawFeatureStore

        def facilityVirtualVisitHighFlag(rawFeatureStore, highVirtualVisitThreshold=98):
            '''
            Is hcp belongs to the facility having total no of virtual visits greater than higher threshold.
            '''

            threshold = np.percentile(rawFeatureStore['numberTotalVirtualVisits'], highVirtualVisitThreshold)

            facilityLevelVirtualVisitData = rawFeatureStore.groupby('facilityId')['virtualVisit1MonthCount'].agg(
                facilityLevelVirtualVisits='sum')

            rawFeatureStore['facilityVirtualVisitHighFlag'] = np.where((pd.merge(rawFeatureStore,
                                                                                 facilityLevelVirtualVisitData,
                                                                                 how="left", on=["facilityId"])[
                'facilityLevelVirtualVisits']) >= threshold, 1, 0)

            return rawFeatureStore

        def facilityVirtualVisitLowFlag(rawFeatureStore, lowVisitThreshold=2):
            '''
            Is hcp belongs to the facility having total no of virtual visits less than lower threshold.
            '''

            threshold = np.percentile(rawFeatureStore['numberTotalVirtualVisits'], lowVisitThreshold)

            facilityLevelVirtualVisitData = rawFeatureStore.groupby('facilityId')['virtualVisit1MonthCount'].agg(
                facilityLevelVirtualVisits='sum')

            rawFeatureStore['facilityVirtualVisitLowFlag'] = np.where((pd.merge(rawFeatureStore,
                                                                                facilityLevelVirtualVisitData,
                                                                                how="left", on=["facilityId"])[
                'facilityLevelVirtualVisits']) <= threshold, 1, 0)

            return rawFeatureStore

        def stdVirtualVisitsDay(rawFeatureStore):
            '''
            Daily Virtual visit std dev.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['virtualVisit1MonthDayCount'].agg(np.std,
                                                                                                             ddof=1).to_frame(
                                'stdVirtualVisitsDay').reset_index(), how="left", on=["accountId"])

        def stdVirtualVisitsWeek(rawFeatureStore):
            '''
            Weekly Virtual visit std dev.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['virtualVisit1MonthWeekCount'].agg(np.std,
                                                                                                              ddof=1).to_frame(
                                'stdVirtualVisitsWeek').reset_index(), how="left", on=["accountId"])

        def stdVirtualVisitsMonth(rawFeatureStore):
            '''
            Monthly Virtual visit std dev.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['virtualVisit1MonthCount'].agg(np.std,
                                                                                                          ddof=1).to_frame(
                                'stdVirtualVisitsMonth').reset_index(), how="left", on=["accountId"])

        def meanVirtualVisitGapOver1Month(rawFeatureStore):
            '''
            Daily Virtual visit gap over recent month.
            '''
            rawFeatureStore['meanVirtualVisitGapOver1Month'] = rawFeatureStore['meanVirtualVisitGapsOver1Month'].round(
                4)

            return rawFeatureStore

        def meanVirtualVisitGapOver3Month(rawFeatureStore):
            '''
            Daily Virtual visit gap over recent 3 months.
            '''
            rawFeatureStore['meanVirtualVisitGapOver3Month'] = rawFeatureStore['meanVirtualVisitGapsOver3Month'].round(
                4)

            return rawFeatureStore

        def meanVirtualVisitGapOver6Month(rawFeatureStore):
            '''
            Daily Virtual visit gap over recent 6 months.
            '''
            rawFeatureStore['meanVirtualVisitGapOver6Month'] = rawFeatureStore['meanVirtualVisitGapsOver6Month'].round(
                4)

            return rawFeatureStore

        def yearlyAvgMeanVirtualVisitGapOver1Month(rawFeatureStore):
            '''
            Yearly avg mean Virtual visit gap over recent month.
            '''
            res = rawFeatureStore.groupby('accountId')['meanVirtualVisitGapsOver1Month'].agg(sumMeanVisits='sum')
            res['yearlyAvgMeanVirtualVisitGapOver1Month'] = res['sumMeanVisits'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanVirtualVisitGapOver1Month'], on='accountId')

        def yearlyAvgMeanVirtualVisitGapOver3Month(rawFeatureStore):
            '''
            Yearly avg mean Virtual visit gap over recent 3 months.
            '''
            res = rawFeatureStore.groupby('accountId')['meanVirtualVisitGapsOver3Month'].agg(sumMeanVisits='sum')
            res['yearlyAvgMeanVirtualVisitGapOver3Month'] = res['sumMeanVisits'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanVirtualVisitGapOver3Month'], on='accountId')

        def yearlyAvgMeanVirtualVisitGapOver6Month(rawFeatureStore):
            '''
            Yearly avg mean Virtual visit gap over recent 6 months.
            '''
            res = rawFeatureStore.groupby('accountId')['meanVirtualVisitGapsOver6Month'].agg(sumMeanVisits='sum')
            res['yearlyAvgMeanVirtualVisitGapOver6Month'] = res['sumMeanVisits'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanVirtualVisitGapOver6Month'], on='accountId')

        def meanVirtualVisitGapYearWeek(rawFeatureStore):
            '''
            Virtual Visits gap in weeks.
            '''

            rawFeatureStore['meanVirtualVisitGapYearWeek'] = np.where(
                rawFeatureStore['virtualVisit1MonthWeekCount'] > 0,
                rawFeatureStore['meanVirtualVisitGapsOver1Month'] / rawFeatureStore['virtualVisit1MonthWeekCount'], 0)

            return rawFeatureStore

        copy_hcp_fs_pdf = hcp_fs_pdf
        virtual_visit_fs_pdf = copy_hcp_fs_pdf.pipe(filterDataBasedOnFeaturePeriod, featurePeriodStart, featurePeriodEndTarget) \
            .pipe(numberTotalVirtualVisits) \
            .pipe(meanVirtualVisitsDay) \
            .pipe(meanVirtualVisitsWeek) \
            .pipe(meanVirtualVisitsMonth) \
            .pipe(numberVirtualVisitsDay) \
            .pipe(percentNumberVirtualVisitsDay) \
            .pipe(virtualVisitHighHcpFlag) \
            .pipe(virtualVisitLowHcpFlag) \
            .pipe(tenureWeekVirtualVisits) \
            .pipe(percentTenureWeekVirtualVisits) \
            .pipe(tenureYearMonthVirtualVisit) \
            .pipe(percentTenureYearMonthVirtualVisit) \
            .pipe(facilityVirtualVisitHighFlag) \
            .pipe(facilityVirtualVisitLowFlag) \
            .pipe(stdVirtualVisitsDay) \
            .pipe(stdVirtualVisitsWeek) \
            .pipe(stdVirtualVisitsMonth) \
            .pipe(meanVirtualVisitGapOver1Month) \
            .pipe(meanVirtualVisitGapOver3Month) \
            .pipe(meanVirtualVisitGapOver6Month) \
            .pipe(yearlyAvgMeanVirtualVisitGapOver1Month) \
            .pipe(yearlyAvgMeanVirtualVisitGapOver3Month) \
            .pipe(yearlyAvgMeanVirtualVisitGapOver6Month) \
            .pipe(meanVirtualVisitGapYearWeek)

        colsToDrop = hcp_fs_pdf.columns.to_list()
        if 'accountId' in colsToDrop:
            colsToDrop.remove('accountId')
        if 'accountUid' in colsToDrop:
            colsToDrop.remove('accountUid')

        idx = virtual_visit_fs_pdf.groupby(['accountId'])['indexMonth'].transform(max) == virtual_visit_fs_pdf['indexMonth']

        # Copy input to output
        virtual_visit_fs_pdf = virtual_visit_fs_pdf[idx].drop(columns=colsToDrop)

        virtual_visit_fs_pdf.drop_duplicates(subset=['accountId'], keep='first', inplace=True)

        data.set_dataframe("virtual_visit_fs_pdf", virtual_visit_fs_pdf)

    def create_phone_features(self):

        data = Data.get_instance()
        hcp_fs_pdf = data.get_dataframe('hcp_fs_pdf')

        featurePeriod = data.get_param('Feature_Period')

        print(featurePeriod)

        featurePeriodDuration = data.get_param('Feature_Period_Duration')

        totalBusinessDays = featurePeriodDuration * 21

        totalBusinessWeeks = round(featurePeriodDuration * 4.5)

        totalBusinessMonths = featurePeriodDuration

        featurePeriodStart = data.get_param('featurePeriodStart')

        featurePeriodEnd = data.get_param('featurePeriodEnd')

        featurePeriodEndTarget = data.get_param('featurePeriodEndTarget')

        def filterDataBasedOnFeaturePeriod(rawFeatureStore, featurePeriodStart, featurePeriodEnd):
            '''
            Filter raw feature store to pass on based on the feature and target period defined.
            '''
            print(rawFeatureStore.columns)
            return rawFeatureStore[(rawFeatureStore['yearMonth'] >= featurePeriodStart) & (
                        rawFeatureStore['yearMonth'] <= featurePeriodEnd)]

        def numberTotalPhones(rawFeatureStore):
            '''
            Total no of phones in a given feature period.
            '''
            return pd.merge(rawFeatureStore,
                            (rawFeatureStore.groupby("accountId")['phone1MonthCount'].agg(numberTotalPhones='sum')),
                            how="left", on=["accountId"])

        def meanPhonesDay(rawFeatureStore):
            '''
            Daily avg no of phones in a given feature period.
            '''
            rawFeatureStore['meanPhonesDay'] = (pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['phone1MonthDayCount'].agg(phoneTotalDays='sum')), how="left",
                                                         on=["accountId"])).apply(
                lambda row: row['numberTotalPhones'] / row['phoneTotalDays'] if row['phoneTotalDays'] > 0 else 0,
                axis=1).round(4)

            return rawFeatureStore

        def meanPhonesWeek(rawFeatureStore):
            '''
            Weekly avg no of phones in a given feature period.
            '''
            rawFeatureStore['meanPhonesWeek'] = (pd.merge(rawFeatureStore, (
                rawFeatureStore.groupby("accountId")['phone1MonthWeekCount'].agg(phoneTotalWeeks='sum')), how="left",
                                                          on=["accountId"])).apply(
                lambda row: row['numberTotalPhones'] / row['phoneTotalWeeks'] if row['phoneTotalWeeks'] > 0 else 0,
                axis=1).round(4)

            return rawFeatureStore

        def meanPhonesMonth(rawFeatureStore):
            '''
            Monthly avg no of phones in a given feature period.
            '''
            rawFeatureStore['meanPhonesMonth'] = np.where(featurePeriodDuration > 0,
                                                          rawFeatureStore['numberTotalPhones'] / featurePeriodDuration,
                                                          0).round(4)

            return rawFeatureStore

        def numberPhonesDay(rawFeatureStore):
            '''
            Days spent on the phones in a given feature period.
            '''

            return pd.merge(rawFeatureStore,
                            (rawFeatureStore.groupby("accountId")['phone1MonthDayCount'].agg(numberPhonesDay='sum')),
                            how="left", on=["accountId"])

        def percentNumberPhonesDay(rawFeatureStore):
            '''
            Percentage no of days spent to complete the phones in a given feature period.
            '''

            rawFeatureStore['percentNumberPhonesDay'] = rawFeatureStore.apply(
                lambda row: row['numberPhonesDay'] / totalBusinessDays if totalBusinessDays > 0 else 0, axis=1).round(4)

            return rawFeatureStore

        def phoneHighHcpFlag(rawFeatureStore, highVisitsThreshold=98):
            '''
            High no of phones Flag stating that the total no of phones are high or not based on threshold.
            '''
            high_threshold = np.percentile(rawFeatureStore['numberTotalPhones'], highVisitsThreshold)

            rawFeatureStore['phoneHighHcpFlag'] = np.where(rawFeatureStore['numberTotalPhones'] >= high_threshold, 1, 0)

            return rawFeatureStore

        def phoneLowHcpFlag(rawFeatureStore, lowPhonesThreshold=2):
            '''
            Low no of Visits Flag stating that the total no of phones are low or not based on threshold.
            '''

            low_threshold = np.percentile(rawFeatureStore['numberTotalPhones'], lowPhonesThreshold)

            rawFeatureStore['phoneLowHcpFlag'] = np.where(rawFeatureStore['numberTotalPhones'] <= low_threshold, 1, 0)

            return rawFeatureStore

        def tenureWeekPhones(rawFeatureStore):
            '''
            Total no of unique weeks spents for phones in a given feature period.
            '''
            return pd.merge(rawFeatureStore,
                            (rawFeatureStore.groupby("accountId")['phone1MonthWeekCount'].agg(tenureWeekPhones='sum')),
                            how="left", on=["accountId"])

        def percentTenureWeekPhones(rawFeatureStore):
            '''
            Percentage no of unique weeks spents for phones in a given feature period.
            '''
            rawFeatureStore['percentTenureWeekPhones'] = np.where(totalBusinessWeeks > 0, (
                        rawFeatureStore['tenureWeekPhones'] / totalBusinessWeeks).round(4), 0)

            return rawFeatureStore

        def tenureYearMonthPhone(rawFeatureStore):
            '''
            no of Year_Month spent in completed the phones in a given feature period.
            '''

            return pd.merge(rawFeatureStore, (
                rawFeatureStore[rawFeatureStore['phone1MonthCount'] > 0].groupby("accountId")['yearMonth'].agg(
                    tenureYearMonthPhone='nunique')), how="left", on=["accountId"])

        def percentTenureYearMonthPhone(rawFeatureStore):
            '''
            Percentage no of Year_Month spent in completed the phones in a given feature period.
            '''

            rawFeatureStore['percentTenureYearMonthPhone'] = rawFeatureStore.apply(
                lambda row: row['tenureYearMonthPhone'] / featurePeriodDuration if featurePeriodDuration > 0 else 0,
                axis=1).round(4)

            return rawFeatureStore

        def facilityPhoneHighFlag(rawFeatureStore, highPhoneThreshold=98):
            '''
            Is hcp belongs to the facility having total no of phones greater than higher threshold.
            '''

            threshold = np.percentile(rawFeatureStore['numberTotalPhones'], highPhoneThreshold)

            facilityLevelPhoneData = rawFeatureStore.groupby('facilityId')['phone1MonthCount'].agg(
                facilityLevelPhones='sum')

            rawFeatureStore['facilityPhoneHighFlag'] = np.where((pd.merge(rawFeatureStore, facilityLevelPhoneData,
                                                                          how="left", on=["facilityId"])[
                'facilityLevelPhones']) >= threshold, 1, 0)

            return rawFeatureStore

        def facilityPhoneLowFlag(rawFeatureStore, lowVisitThreshold=2):
            '''
            Is hcp belongs to the facility having total no of phones less than lower threshold.
            '''

            threshold = np.percentile(rawFeatureStore['numberTotalPhones'], lowVisitThreshold)

            facilityLevelPhoneData = rawFeatureStore.groupby('facilityId')['phone1MonthCount'].agg(
                facilityLevelPhones='sum')

            rawFeatureStore['facilityPhoneLowFlag'] = np.where((pd.merge(rawFeatureStore, facilityLevelPhoneData,
                                                                         how="left", on=["facilityId"])[
                'facilityLevelPhones']) <= threshold, 1, 0)

            return rawFeatureStore

        def stdPhonesDay(rawFeatureStore):
            '''
            Daily Phone std dev.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['phone1MonthDayCount'].agg(np.std,
                                                                                                      ddof=1).to_frame(
                                'stdPhonesDay').reset_index(), how="left", on=["accountId"])

        def stdPhonesWeek(rawFeatureStore):
            '''
            Weekly Phone std dev.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['phone1MonthWeekCount'].agg(np.std,
                                                                                                       ddof=1).to_frame(
                                'stdPhonesWeek').reset_index(), how="left", on=["accountId"])

        def stdPhonesMonth(rawFeatureStore):
            '''
            Monthly Phone std dev.
            '''

            return pd.merge(rawFeatureStore,
                            rawFeatureStore.fillna(0).groupby('accountId')['phone1MonthCount'].agg(np.std,
                                                                                                   ddof=1).to_frame(
                                'stdPhonesMonth').reset_index(), how="left", on=["accountId"])

        def meanPhoneGapOver1Month(rawFeatureStore):
            '''
            Daily Phone gap over recent month.
            '''
            rawFeatureStore['meanPhoneGapOver1Month'] = rawFeatureStore['meanPhoneGapsOver1Month'].round(4)

            return rawFeatureStore

        def meanPhoneGapOver3Month(rawFeatureStore):
            '''
            Daily Phone gap over recent 3 months.
            '''
            rawFeatureStore['meanPhoneGapOver3Month'] = rawFeatureStore['meanPhoneGapsOver3Month'].round(4)

            return rawFeatureStore

        def meanPhoneGapOver6Month(rawFeatureStore):
            '''
            Daily Phone gap over recent 6 months.
            '''
            rawFeatureStore['meanPhoneGapOver6Month'] = rawFeatureStore['meanPhoneGapsOver6Month'].round(4)

            return rawFeatureStore

        def yearlyAvgMeanPhoneGapOver1Month(rawFeatureStore):
            '''
            Yearly avg mean Phone gap over recent month.
            '''
            res = rawFeatureStore.groupby('accountId')['meanPhoneGapsOver1Month'].agg(sumMeanVisits='sum')
            res['yearlyAvgMeanPhoneGapOver1Month'] = res['sumMeanVisits'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanPhoneGapOver1Month'], on='accountId')

        def yearlyAvgMeanPhoneGapOver3Month(rawFeatureStore):
            '''
            Yearly avg mean Phone gap over recent 3 months.
            '''
            res = rawFeatureStore.groupby('accountId')['meanPhoneGapsOver3Month'].agg(sumMeanVisits='sum')
            res['yearlyAvgMeanPhoneGapOver3Month'] = res['sumMeanVisits'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanPhoneGapOver3Month'], on='accountId')

        def yearlyAvgMeanPhoneGapOver6Month(rawFeatureStore):
            '''
            Yearly avg mean Phone gap over recent 6 months.
            '''
            res = rawFeatureStore.groupby('accountId')['meanPhoneGapsOver6Month'].agg(sumMeanVisits='sum')
            res['yearlyAvgMeanPhoneGapOver6Month'] = res['sumMeanVisits'] / featurePeriodDuration

            return rawFeatureStore.join(res['yearlyAvgMeanPhoneGapOver6Month'], on='accountId')

        def meanPhoneGapYearWeek(rawFeatureStore):
            '''
            phones gap in weeks.
            '''

            rawFeatureStore['meanPhoneGapYearWeek'] = np.where(rawFeatureStore['phone1MonthWeekCount'] > 0,
                                                               rawFeatureStore['meanPhoneGapsOver1Month'] /
                                                               rawFeatureStore['phone1MonthWeekCount'], 0)

            return rawFeatureStore

        copy_hcp_fs_pdf = hcp_fs_pdf
        phone_fs_pdf = copy_hcp_fs_pdf.pipe(filterDataBasedOnFeaturePeriod, featurePeriodStart, featurePeriodEndTarget) \
            .pipe(numberTotalPhones) \
            .pipe(meanPhonesDay) \
            .pipe(meanPhonesWeek) \
            .pipe(meanPhonesMonth) \
            .pipe(numberPhonesDay) \
            .pipe(percentNumberPhonesDay) \
            .pipe(phoneHighHcpFlag) \
            .pipe(phoneLowHcpFlag) \
            .pipe(tenureWeekPhones) \
            .pipe(percentTenureWeekPhones) \
            .pipe(tenureYearMonthPhone) \
            .pipe(percentTenureYearMonthPhone) \
            .pipe(facilityPhoneHighFlag) \
            .pipe(facilityPhoneLowFlag) \
            .pipe(stdPhonesDay) \
            .pipe(stdPhonesWeek) \
            .pipe(stdPhonesMonth) \
            .pipe(meanPhoneGapOver1Month) \
            .pipe(meanPhoneGapOver3Month) \
            .pipe(meanPhoneGapOver6Month) \
            .pipe(yearlyAvgMeanPhoneGapOver1Month) \
            .pipe(yearlyAvgMeanPhoneGapOver3Month) \
            .pipe(yearlyAvgMeanPhoneGapOver6Month) \
            .pipe(meanPhoneGapYearWeek)

        colsToDrop = hcp_fs_pdf.columns.to_list()
        if 'accountId' in colsToDrop:
            colsToDrop.remove('accountId')
        if 'accountUid' in colsToDrop:
            colsToDrop.remove('accountUid')

        idx = phone_fs_pdf.groupby(['accountId'])['indexMonth'].transform(max) == phone_fs_pdf['indexMonth']

        # Copy input to output
        phone_fs_pdf = phone_fs_pdf[idx].drop(columns=colsToDrop)

        phone_fs_pdf.drop_duplicates(subset=['accountId'], keep='first', inplace=True)

        data.set_dataframe('phone_fs_pdf', phone_fs_pdf)

    def create_cri_based_features(self):

        data = Data.get_instance()
        hcp_fs_pdf = data.get_dataframe('hcp_fs_pdf')

        featurePeriod = data.get_param('Feature_Period')

        print(featurePeriod)

        featurePeriodDuration = data.get_param('Feature_Period_Duration')

        totalBusinessDays = featurePeriodDuration * 21

        totalBusinessWeeks = round(featurePeriodDuration * 4.5)

        totalBusinessMonths = featurePeriodDuration

        featurePeriodStart = data.get_param('featurePeriodStart')

        featurePeriodEnd = data.get_param('featurePeriodEnd')

        featurePeriodEndTarget = data.get_param('featurePeriodEndTarget')

        def filterDataBasedOnFeaturePeriod(rawFeatureStore, featurePeriodStart, featurePeriodEnd):
            '''
            Filter raw feature store to pass on based on the feature and target period defined.
            '''
            return rawFeatureStore[(rawFeatureStore['yearMonth'] >= featurePeriodStart) & (
                        rawFeatureStore['yearMonth'] <= featurePeriodEnd)]

        def criMaxCadenceScore(rawFeatureStore):
            '''
            CRI max cadence score score in a given input data.
            '''
            if ('criCadenceScore' in rawFeatureStore.columns):
                rawFeatureStore['criMaxCadenceScore'] = rawFeatureStore['criCadenceScore'].round(4)
            else:
                rawFeatureStore['criMaxCadenceScore'] = np.nan

            return rawFeatureStore

        def criMaxEmailOpenScore(rawFeatureStore):
            '''
            CRI max Email Open score score in a given input data.
            '''
            if ('criOpenScore' in rawFeatureStore.columns):
                rawFeatureStore['criMaxEmailOpenScore'] = rawFeatureStore['criOpenScore'].round(4)
            else:
                rawFeatureStore['criMaxEmailOpenScore'] = np.nan

            return rawFeatureStore

        def criMaxVisitScore(rawFeatureStore):
            '''
            CRI max visit score no of in a given input data.
            '''
            if ('criVisitScore' in rawFeatureStore.columns):
                rawFeatureStore['criMaxVisitScore'] = rawFeatureStore['criVisitScore'].round(4)
            else:
                rawFeatureStore['criMaxVisitScore'] = np.nan

            return rawFeatureStore

        def criMaxSumIndex(rawFeatureStore):
            '''
            CRI max sum index score no of in a given input data.
            '''
            if ('criMaxAvgIndex' in rawFeatureStore.columns):
                rawFeatureStore['criMaxSumIndex'] = rawFeatureStore['criMaxAvgIndex'].round(4)
            else:
                rawFeatureStore['criMaxSumIndex'] = np.nan

            return rawFeatureStore

        def criMaxTenureScore(rawFeatureStore):
            '''
            CRI max tenure score no of in a given input data.
            '''
            if ('criTenureScore' in rawFeatureStore.columns):
                rawFeatureStore['criMaxTenureScore'] = rawFeatureStore['criTenureScore'].round(4)
            else:
                rawFeatureStore['criMaxTenureScore'] = np.nan

            return rawFeatureStore

        copy_hcp_fs_pdf = hcp_fs_pdf
        cri_fs_pdf = copy_hcp_fs_pdf.pipe(filterDataBasedOnFeaturePeriod, featurePeriodStart, featurePeriodEndTarget) \
            .pipe(criMaxCadenceScore) \
            .pipe(criMaxEmailOpenScore) \
            .pipe(criMaxVisitScore) \
            .pipe(criMaxSumIndex) \
            .pipe(criMaxTenureScore)

        colsToDrop = hcp_fs_pdf.columns.to_list()
        if 'accountId' in colsToDrop:
            colsToDrop.remove('accountId')
        if 'accountUid' in colsToDrop:
            colsToDrop.remove('accountUid')

        idx = cri_fs_pdf.groupby(['accountId'])['indexMonth'].transform(max) == cri_fs_pdf['indexMonth']

        # Copy input to output
        cri_fs_pdf = cri_fs_pdf[idx].drop(columns=colsToDrop)

        cri_fs_pdf.drop_duplicates(subset=['accountId'], keep='first', inplace=True)

        data.set_dataframe("cri_based_fs_pdf", cri_fs_pdf)

    def create_target(self):

        data = Data.get_instance()
        hcp_fs_pdf = data.get_dataframe('hcp_fs_pdf')

        featurePeriod = data.get_param('Feature_Period')

        # print(featurePeriod)

        featurePeriodDuration = data.get_param('Feature_Period_Duration')

        featurePeriodArray = featurePeriod.split('-')

        if len(featurePeriodArray) == 2:
            if int(featurePeriodArray[0]) < int(featurePeriodArray[1]):
                featurePeriodStart = int(featurePeriodArray[0])
                featurePeriodEnd = int(featurePeriodArray[1])

        if len(featurePeriodArray) == 2:
            if int(featurePeriodArray[0]) < int(featurePeriodArray[1]):
                targetPeriodEnd = int(featurePeriodArray[1])

        if str(targetPeriodEnd)[-2:] == '05':
            month_target_3 = int(str(int(str(targetPeriodEnd)[0:4])) + '03')
            month_target_4 = int(str(int(str(targetPeriodEnd)[0:4])) + '02')
            month_target_5 = int(str(int(str(targetPeriodEnd)[0:4])) + '01')
            month_target_6 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '12')

        elif str(targetPeriodEnd)[-2:] == '04':
            month_target_3 = int(str(int(str(targetPeriodEnd)[0:4])) + '02')
            month_target_4 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '01')
            month_target_5 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '12')
            month_target_6 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '11')

        elif str(targetPeriodEnd)[-2:] == '03':
            month_target_3 = int(str(int(str(targetPeriodEnd)[0:4])) + '01')
            month_target_4 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '12')
            month_target_5 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '11')
            month_target_6 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '10')

        elif str(targetPeriodEnd)[-2:] == '02':
            month_target_3 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '12')
            month_target_4 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '11')
            month_target_5 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '10')
            month_target_6 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '09')
        elif str(targetPeriodEnd)[-2:] == '01':
            month_target_3 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '11')
            month_target_4 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '10')
            month_target_5 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '09')
            month_target_6 = int(str(int(str(targetPeriodEnd)[0:4]) - 1) + '08')
        else:
            month_target_3 = int(str(targetPeriodEnd)[0:4] + '0' + str(int(str(targetPeriodEnd)[-2:]) - 2))
            month_target_4 = int(str(targetPeriodEnd)[0:4] + '0' + str(int(str(targetPeriodEnd)[-2:]) - 3))
            month_target_5 = int(str(targetPeriodEnd)[0:4] + '0' + str(int(str(targetPeriodEnd)[-2:]) - 4))
            month_target_6 = int(str(targetPeriodEnd)[0:4] + '0' + str(int(str(targetPeriodEnd)[-2:]) - 5))

        # print(month_target_3,month_target_4,month_target_5,month_target_6)

        def createTargetIndicators(rawFeatureStore, targetPeriodEnd, month_target_3, month_target_4, month_target_5,
                                   month_target_6):

            if (data.get_param('send') == 1):
                # Create Email Target Indicators
                rawFeatureStore = pd.merge(pd.merge(
                    pd.merge(
                        pd.merge(rawFeatureStore, rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_3) & (
                                    rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                            'emailOpen1MonthCount'].agg(y_email_3='sum')
                                 , on="accountId", how="left"),
                        rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_4) & (
                                    rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                            'emailOpen1MonthCount'].agg(y_email_4='sum')
                        , on="accountId", how="left"),
                    rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_5) & (
                                rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                        'emailOpen1MonthCount'].agg(y_email_5='sum')
                    , on="accountId", how="left"),
                    rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_6) & (
                                rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                        'emailOpen1MonthCount'].agg(y_email_6='sum')
                    , on="accountId", how="left")

            if (data.get_param('visit') == 1):
                # Create Visit target Indicators
                rawFeatureStore = pd.merge(pd.merge(
                    pd.merge(
                        pd.merge(rawFeatureStore, rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_3) & (
                                    rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                            'visit1MonthCount'].agg(y_visit_3='sum')
                                 , on="accountId", how="left"),
                        rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_4) & (
                                    rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                            'visit1MonthCount'].agg(y_visit_4='sum')
                        , on="accountId", how="left"),
                    rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_5) & (
                                rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                        'visit1MonthCount'].agg(y_visit_5='sum')
                    , on="accountId", how="left"),
                    rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_6) & (
                                rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                        'visit1MonthCount'].agg(y_visit_6='sum')
                    , on="accountId", how="left")

            if (data.get_param('virtual_visit') == 1):
                # Create Virtual Visit target Indicators
                rawFeatureStore = pd.merge(pd.merge(
                    pd.merge(
                        pd.merge(rawFeatureStore, rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_3) & (
                                    rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                            'virtualVisit1MonthCount'].agg(y_virtual_visit_3='sum')
                                 , on="accountId", how="left"),
                        rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_4) & (
                                    rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                            'virtualVisit1MonthCount'].agg(y_virtual_visit_4='sum')
                        , on="accountId", how="left"),
                    rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_5) & (
                                rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                        'virtualVisit1MonthCount'].agg(y_virtual_visit_5='sum')
                    , on="accountId", how="left"),
                    rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_6) & (
                                rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                        'virtualVisit1MonthCount'].agg(y_virtual_visit_6='sum')
                    , on="accountId", how="left")

            if (data.get_param('phone') == 1):
                # Create Phone target Indicators
                rawFeatureStore = pd.merge(pd.merge(
                    pd.merge(
                        pd.merge(rawFeatureStore, rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_3) & (
                                    rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                            'phone1MonthCount'].agg(y_phone_3='sum')
                                 , on="accountId", how="left"),
                        rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_4) & (
                                    rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                            'phone1MonthCount'].agg(y_phone_4='sum')
                        , on="accountId", how="left"),
                    rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_5) & (
                                rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                        'phone1MonthCount'].agg(y_phone_5='sum')
                    , on="accountId", how="left"),
                    rawFeatureStore[(rawFeatureStore['yearMonth'] >= month_target_6) & (
                                rawFeatureStore['yearMonth'] <= targetPeriodEnd)].groupby('accountId')[
                        'phone1MonthCount'].agg(y_phone_6='sum')
                    , on="accountId", how="left")
            return rawFeatureStore

        def filterDataBasedOnFeaturePeriod(rawFeatureStore, targetPeriodStart, targetPeriodEnd):
            '''
            Filter raw feature store to pass on based on the feature and target period defined.
            '''
            print(rawFeatureStore.columns)
            return rawFeatureStore[
                (rawFeatureStore['yearMonth'] >= targetPeriodStart) & (rawFeatureStore['yearMonth'] <= targetPeriodEnd)]

        colsToDropForTarget = ["productId", "emailSent1MonthCount", "emailSent1MonthWeekCount",
                               "emailSent1MonthDayCount", "meanEmailSentGapsOver1Month", "meanEmailSentGapsOver3Month",
                               "meanEmailSentGapsOver6Month", "emailOpen1MonthWeekCount", "emailOpen1MonthDayCount",
                               "meanEmailOpenGapsOver1Month", "meanEmailOpenGapsOver3Month",
                               "meanEmailOpenGapsOver6Month", "emailClick1MonthWeekCount", "emailClick1MonthDayCount",
                               "meanEmailClickGapsOver1Month", "meanEmailClickGapsOver3Month",
                               "meanEmailClickGapsOver6Month", "emailOpenRate", "emailClickRate", "emailOpenClickRate",
                               "emailOpenRateOver3Months", "emailClickRateOver3Months", "emailOpenClickRateOver3Months",
                               "emailOpenRateOver6Months", "emailClickRateOver6Months", "emailOpenClickRateOver6Months",
                               "emailOpenRateOver12Months", "emailClickRateOver12Months",
                               "emailOpenClickRateOver12Months", "visit1MonthWeekCount", "visit1MonthDayCount",
                               "meanVisitGapsOver1Month", "meanVisitGapsOver3Month", "meanVisitGapsOver6Month",
                               "virtualVisit1MonthWeekCount", "virtualVisit1MonthDayCount",
                               "meanVirtualVisitGapsOver1Month", "meanVirtualVisitGapsOver3Month",
                               "meanVirtualVisitGapsOver6Month", "hcpGender", "hcpCred", "hcpSpec",
                               "hcpSpec2", "facilityId", "latitude", "longitude", "accountUid", "hcpStartYearMonth",
                               "timeZone", "today", "tenureMonths", "city", "county", "zipCode", "state",
                               "numberHcpFacility", "criCadenceScore", "criChannelScore", "criOpenScore",
                               "criVisitScore", "criTenureScore", "criTargetAchievementScore",
                               "criSuggestionEmailScore", "criSuggestionVisitScore", "criMaxAvgIndex",
                               "productUid", "productName", "isHcpProductTargeted", "facilityTargetAccountCount",
                               "indexMonth"]
        hcp_fs_target_pdf = hcp_fs_pdf.drop(columns=colsToDropForTarget)

        colsToDrop = hcp_fs_target_pdf.columns.to_list()
        if 'accountId' in colsToDrop:
            colsToDrop.remove('accountId')

        target_pdf = hcp_fs_target_pdf.pipe(filterDataBasedOnFeaturePeriod, month_target_6, targetPeriodEnd) \
            .pipe(createTargetIndicators, targetPeriodEnd, month_target_3, month_target_4, month_target_5,
                  month_target_6)

        target_pdf = target_pdf.fillna(0).drop(columns=colsToDrop)

        exclude_non_dup_cols = ["y_email_3","y_email_4","y_email_5","y_email_6","y_visit_3","y_visit_4",
                        "y_visit_5","y_visit_6","y_virtual_visit_3","y_virtual_visit_4",
                        "y_virtual_visit_5", "y_virtual_visit_6"]

        non_dup_cols = [col for col in target_pdf.columns.to_list() if col not in exclude_non_dup_cols]

        target_pdf.drop_duplicates(subset=non_dup_cols, keep='first', inplace=True)

        # -- PART-2 --

        df = target_pdf

        hcp_count = df.shape[0]

        y_email_3mnths_count = df[df.y_email_3 > 0].shape[0]
        y_email_4mnths_count = df[df.y_email_4 > 0].shape[0]
        y_email_5mnths_count = df[df.y_email_5 > 0].shape[0]
        y_email_6mnths_count = df[df.y_email_6 > 0].shape[0]

        y_visit_3mnths_count = df[df.y_visit_3 > 0].shape[0]
        y_visit_4mnths_count = df[df.y_visit_4 > 0].shape[0]
        y_visit_5mnths_count = df[df.y_visit_5 > 0].shape[0]
        y_visit_6mnths_count = df[df.y_visit_6 > 0].shape[0]

        y_phone_3mnths_count = df[df.y_phone_3 > 0].shape[0]
        y_phone_4mnths_count = df[df.y_phone_4 > 0].shape[0]
        y_phone_5mnths_count = df[df.y_phone_5 > 0].shape[0]
        y_phone_6mnths_count = df[df.y_phone_6 > 0].shape[0]

        y_virtual_visit_3mnths_count = df[df.y_virtual_visit_3 > 0].shape[0]
        y_virtual_visit_4mnths_count = df[df.y_virtual_visit_4 > 0].shape[0]
        y_virtual_visit_5mnths_count = df[df.y_virtual_visit_5 > 0].shape[0]
        y_virtual_visit_6mnths_count = df[df.y_virtual_visit_6 > 0].shape[0]

        if (data.get_param('transform_send') == 1):
            if (y_email_3mnths_count > data.get_param('Transform_Q_1_Threshold')):
                df['Y_send'] = np.where(df['y_email_3'] > 0, 1, 0)
                data.set_param('Y_send_duration', "y_email_3")
            elif (y_email_3mnths_count < data.get_param('Transform_Q_1_Threshold') and y_email_4mnths_count >=
                  data.get_param('Transform_Q_1_Threshold')):
                df['Y_send'] = np.where(df['y_email_4'] > 0, 1, 0)
                data.set_param('Y_send_duration', "y_email_4")
            elif (y_email_4mnths_count < data.get_param('Transform_Q_1_Threshold') and y_email_5mnths_count >=
                  data.get_param('Transform_Q_1_Threshold')):
                df['Y_send'] = np.where(df['y_email_5'] > 0, 1, 0)
                data.set_param('Y_send_duration', "y_email_5")
            elif (y_email_5mnths_count < data.get_param('Transform_Q_1_Threshold') and y_email_6mnths_count >=
                  data.get_param('Transform_Q_1_Threshold')):
                df['Y_send'] = np.where(df['y_email_6'] > 0, 1, 0)
                data.set_param('Y_send_duration', "y_email_6")
            else:
                df['Y_send'] = np.where(df['y_email_3'] > 0, 1, 0)
                data.set_param('transform_send', 0)
                data.set_param('Y_send_duration', "y_email_3")
        else:
            df['Y_send'] = 0
            data.set_param('Y_send_duration', "y_email_0")

        if (data.get_param('transform_visit') == 1):
            if (y_visit_3mnths_count > data.get_param('Transform_Q_1_Threshold')):
                df['Y_visit'] = np.where(df['y_visit_3'] > 0, 1, 0)
                data.set_param('Y_visit_duration', "y_visit_3")
            elif (y_visit_3mnths_count < data.get_param('Transform_Q_1_Threshold') and y_visit_4mnths_count >=
                  data.get_param('Transform_Q_1_Threshold')):
                df['Y_visit'] = np.where(df['y_visit_4'] > 0, 1, 0)
                data.set_param('Y_visit_duration', "y_visit_4")
            elif (y_visit_4mnths_count < data.get_param('Transform_Q_1_Threshold') and y_visit_5mnths_count >=
                  data.get_param('Transform_Q_1_Threshold')):
                df['Y_visit'] = np.where(df['y_visit_5'] > 0, 1, 0)
                data.set_param('Y_visit_duration', "y_visit_5")
            elif (y_visit_5mnths_count < data.get_param('Transform_Q_1_Threshold') and y_visit_6mnths_count >=
                  data.get_param('Transform_Q_1_Threshold')):
                df['Y_visit'] = np.where(df['y_visit_6'] > 0, 1, 0)
                data.set_param('Y_visit_duration', "y_visit_6")
            else:
                df['Y_visit'] = np.where(df['y_visit_3'] > 0, 1, 0)
                data.set_param('transform_visit', 0)
                data.set_param('Y_visit_duration', "y_visit_3")
        else:
            df['Y_visit'] = 0
            data.set_param('Y_visit_duration', "y_visit_0")

        if (data.get_param('transform_virtual_visit') == 1):
            if (y_virtual_visit_3mnths_count > data.get_param('Transform_Q_1_Threshold')):
                df['Y_virtual_visit'] = np.where(df['y_virtual_visit_3'] > 0, 1, 0)
                data.set_param('Y_virtual_visit_duration', "y_virtual_visit_3")
            elif (y_virtual_visit_3mnths_count < data.get_param(
                         'Transform_Q_1_Threshold') and y_virtual_visit_4mnths_count >= data.get_param('Transform_Q_1_Threshold')):
                df['Y_virtual_visit'] = np.where(df['y_virtual_visit_4'] > 0, 1, 0)
                data.set_param('Y_virtual_visit_duration', "y_virtual_visit_4")
            elif (y_virtual_visit_4mnths_count < data.get_param(
                               'Transform_Q_1_Threshold') and y_virtual_visit_5mnths_count >= data.get_param(
                                'Transform_Q_1_Threshold')):
                df['Y_virtual_visit'] = np.where(df['y_virtual_visit_5'] > 0, 1, 0)
                data.set_param('Y_virtual_visit_duration', "y_virtual_visit_5")
            elif (y_virtual_visit_5mnths_count < data.get_param(
                                'Transform_Q_1_Threshold') and y_virtual_visit_6mnths_count >= data.get_param(
                                'Transform_Q_1_Threshold')):
                df['Y_virtual_visit'] = np.where(df['y_virtual_visit_6'] > 0, 1, 0)
                data.set_param('Y_virtual_visit_duration', "y_virtual_visit_6")
            else:
                df['Y_virtual_visit'] = np.where(df['y_virtual_visit_3'] > 0, 1, 0)
                data.set_param('transform_virtual_visit', 0)
                data.set_param('Y_virtual_visit_duration', "y_virtual_visit_3")
        else:
            df['Y_virtual_visit'] = 0
            data.set_param('Y_virtual_visit_duration', "y_virtual_visit_0")

        if (data.get_param('transform_phone') == 1):
            if (y_phone_3mnths_count > data.get_param('Transform_Q_1_Threshold')):
                df['Y_phone'] = np.where(df['y_phone_3'] > 0, 1, 0)
                data.set_param('Y_phone_duration', "y_phone_3")
            elif (y_phone_3mnths_count < data.get_param('Transform_Q_1_Threshold') and y_phone_4mnths_count >=
                  data.get_param('Transform_Q_1_Threshold')):
                df['Y_phone'] = np.where(df['y_phone_4'] > 0, 1, 0)
                data.set_param('Y_phone_duration', "y_phone_4")
            elif (y_phone_4mnths_count < data.get_param('Transform_Q_1_Threshold') and y_phone_5mnths_count >=
                  data.get_param('Transform_Q_1_Threshold')):
                df['Y_phone'] = np.where(df['y_phone_5'] > 0, 1, 0)
                data.set_param('Y_phone_duration', "y_phone_5")
            elif (y_phone_5mnths_count < data.get_param('Transform_Q_1_Threshold') and y_phone_6mnths_count >=
                  data.get_param('Transform_Q_1_Threshold')):
                df['Y_phone'] = np.where(df['y_phone_6'] > 0, 1, 0)
                data.set_param('Y_phone_duration', "y_phone_6")
            else:
                df['Y_phone'] = np.where(df['y_phone_3'] > 0, 1, 0)
                data.set_param('transform_phone',  0)
                data.set_param('Y_phone_duration', "y_phone_3")
        else:
            df['Y_phone'] = 0
            data.set_param('Y_phone_duration', "y_phone_0")

        cols = ['accountId', 'Y_visit', 'Y_phone', 'Y_virtual_visit', 'Y_send']

        target_pdf = df[cols]

        target_pdf['Y_all'] = target_pdf[['Y_visit', 'Y_phone', 'Y_virtual_visit', 'Y_send']].max(axis=1)

        data.set_param('Y_send', target_pdf['Y_send'].sum())
        data.set_param('Y_visit', target_pdf['Y_visit'].sum())
        data.set_param('Y_phone', target_pdf['Y_phone'].sum())
        data.set_param('Y_virtual_visit', sum(target_pdf['Y_virtual_visit'] == 1))

        data.set_dataframe("target_pdf", target_pdf)

    def create_features(self):
        self.create_flow_variables()
        self.create_email_features()
        self.create_visit_features()
        self.create_virtual_visit_features()
        self.create_phone_features()
        self.create_cri_based_features()

    def feature_reduction(self):
        data = Data.get_instance()

        visit_fs_pdf = data.get_dataframe('visit_fs_pdf')
        virtual_visit_fs_pdf = data.get_dataframe('virtual_visit_fs_pdf')
        phone_fs_pdf = data.get_dataframe('phone_fs_pdf')
        email_fs_pdf = data.get_dataframe('email_fs_pdf')

        full_fs_pdf = pd.merge(visit_fs_pdf, email_fs_pdf, on=['accountId', 'accountUid'], how='inner')
        full_fs_pdf = pd.merge(full_fs_pdf, phone_fs_pdf, on=['accountId', 'accountUid'], how='inner')
        full_fs_pdf = pd.merge(full_fs_pdf, virtual_visit_fs_pdf, on=['accountId', 'accountUid'], how='inner')

        target_pdf = data.get_dataframe('target_pdf')
        full_fs_pdf = pd.merge(full_fs_pdf, target_pdf, on=['accountId'], how='inner')

        data.set_dataframe("full_fs_pdf", full_fs_pdf)
        full_fs_pdf = data.get_dataframe("full_fs_pdf")

        reduced_fs_pdf = full_fs_pdf.copy()

        # Drop Missing Value
        reduced_fs_pdf = reduced_fs_pdf.dropna(axis=1, how='all')

        # Low Variance Filter
        cols_to_remove =  target_pdf.columns
        cols_to_remove = cols_to_remove.insert(0,'accountUid')
        cols = [col for col in reduced_fs_pdf.columns if col not in cols_to_remove]
        variance = reduced_fs_pdf.var()
        variance_threshold = 0.0
        low_var_cols = [col for col in cols if variance[col] <= variance_threshold]
        reduced_fs_pdf.drop(columns=low_var_cols, inplace=True)
        data.set_param("Low_Var_Feature_reduction", len(low_var_cols))

        # High correlation Filter
        cor_matrix = reduced_fs_pdf.drop(columns=cols_to_remove).corr().abs()
        upper_tri = cor_matrix.where(np.triu(np.ones(cor_matrix.shape), k=1).astype(bool))
        corr_threshold = 0.8
        high_corr_cols = [column for column in upper_tri.columns if any(upper_tri[column] > corr_threshold)]
        reduced_fs_pdf.drop(columns=high_corr_cols, inplace=True)
        data.set_param("Corr_Filter_Feature_reduction", len(high_corr_cols))

        data.set_dataframe("reduced_fs_pdf", reduced_fs_pdf)

    def qa_module(self):
        data = Data.get_instance()
        mf_qa_config_pdf = data.get_dataframe("mf_qa_config_pdf")
        phase = "Transform"

        # TEST-1: Positive class sufficiency for Send
        test_1_dict = QADataHandler.get_dict_with_test_info(phase, 1)
        test_1_dict['Test_Value'] = str(data.get_param('Y_send'))
        if data.get_param('Y_send') < data.get_param('Transform_Q_1_Threshold'):
            test_1_dict['Test_Result'] = "Fail"
        QADataHandler.add_row_qa_logs(test_1_dict)

        # TEST-2: Positive class sufficiency for Visit
        test_2_dict = QADataHandler.get_dict_with_test_info(phase, 2)
        test_2_dict['Test_Value'] = str(data.get_param('Y_visit'))
        if data.get_param('Y_visit') < data.get_param('Transform_Q_1_Threshold'):
            test_2_dict['Test_Result'] = "Fail"
        QADataHandler.add_row_qa_logs(test_2_dict)

        # TEST-3: Positive class sufficiency for Virtual Visit
        #TODO: This Could be a metric
        test_3_dict = QADataHandler.get_dict_with_test_info(phase, 3)
        test_3_dict['Test_Value'] = str(data.get_param('Y_virtual_visit'))
        if data.get_param('Y_virtual_visit') < data.get_param('Transform_Q_1_Threshold'):
            test_3_dict['Test_Result'] = "Fail"
        QADataHandler.add_row_qa_logs(test_3_dict)

        # TEST-4: Positive class sufficiency for Phone
        test_4_dict = QADataHandler.get_dict_with_test_info(phase, 4)
        test_4_dict['Test_Value'] = str(data.get_param('Y_phone'))
        if data.get_param('Y_phone') < data.get_param('Transform_Q_1_Threshold'):
            test_4_dict['Test_Result'] = "Fail"
        QADataHandler.add_row_qa_logs(test_4_dict)

        # TEST-5: Target Period for send Channel
        test_5_dict = QADataHandler.get_dict_with_test_info(phase, 5)
        test_5_dict['Test_Value'] = data.get_param('Y_send_duration')
        QADataHandler.add_row_qa_logs(test_5_dict)

        # TEST-6: Target Period for visit Channel
        test_6_dict = QADataHandler.get_dict_with_test_info(phase, 6)
        test_6_dict['Test_Value'] = data.get_param('Y_visit_duration')
        QADataHandler.add_row_qa_logs(test_6_dict)

        # TEST-7: Target Period for vitual visit Channel
        test_7_dict = QADataHandler.get_dict_with_test_info(phase, 7)
        test_7_dict['Test_Value'] = data.get_param('Y_virtual_visit_duration')
        QADataHandler.add_row_qa_logs(test_7_dict)

        # TEST-8: Target Period for phone Channel
        test_8_dict = QADataHandler.get_dict_with_test_info(phase, 8)
        test_8_dict['Test_Value'] = data.get_param('Y_phone_duration')
        QADataHandler.add_row_qa_logs(test_8_dict)

        # TEST-9: Feature Reduction in low variance Filter
        test_9_dict = QADataHandler.get_dict_with_test_info(phase, 9)
        test_9_dict['Test_Value'] = data.get_param('Low_Var_Feature_reduction')
        QADataHandler.add_row_qa_logs(test_9_dict)

        # TEST-10: Feature reduction in high corr filter
        test_10_dict = QADataHandler.get_dict_with_test_info(phase, 10)
        test_10_dict['Test_Value'] = data.get_param('Corr_Filter_Feature_reduction')
        QADataHandler.add_row_qa_logs(test_10_dict)

    def execute(self):
        self.create_features()
        self.create_target()
        self.feature_reduction()

