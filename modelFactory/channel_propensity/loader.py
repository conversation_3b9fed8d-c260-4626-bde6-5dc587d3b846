from datetime import datetime

import findspark
import pandas as pd
import pyspark
from pyspark.sql import SparkSession

from abstract_model_factory.abstract_loader import AbstractLoader
from channel_propensity.data import Data
from channel_propensity.data_access_layer import DataAccessLayer
from qa_data_handler import QADataHandler
import constants
from channel_propensity.constants import Constants
import os



class Loader(AbstractLoader):

    def __get_previous_runs_qa_log(self):

        query = "SELECT TOP 1 * FROM CHANNEL_PROPENSITY.\"MF_QA_logs\"\
                 WHERE \"Test_Phase\" = 'Load' AND \"Test_ID\" = 1\
                 ORDER BY \"Update_Date\" desc"
        last_mf_qa_log_pdf = DataAccessLayer.read_snow_flake_pandas(query)
        return last_mf_qa_log_pdf

    def qa_module(self):
        data = Data.get_instance()
        hcp_fs_pdf = data.get_dataframe("hcp_fs_pdf")

        unique_hcp_count = hcp_fs_pdf.accountId.nunique()

        # TEST-1: Check HCP Count Threshold
        hcp_count_threshold = data.get_param("Load_Q_1_Threshold")
        test_1_dict = QADataHandler.get_dict_with_test_info(phase='Load', test_id=1)
        test_1_dict['Test_Value'] = str(unique_hcp_count)

        if unique_hcp_count < hcp_count_threshold:
            test_1_dict['Test_Result'] = "Fail"

        QADataHandler.add_row_qa_logs(test_1_dict)

        # TEST-2: Check HCP Count w.r.t last run's HCP count
        deviation_threshold = data.get_param('Load_Q_2_Threshold')/100
        test_2_dict = QADataHandler.get_dict_with_test_info('Load', 2)

        last_mf_qa_log_pdf = self.__get_previous_runs_qa_log()
        if not last_mf_qa_log_pdf.empty:
            last_hcp_count = int(last_mf_qa_log_pdf['Test_Value'])
            deviation = abs(unique_hcp_count - last_hcp_count)/100

            test_2_dict['Test_Value'] = str(deviation)

            if deviation > deviation_threshold:
                test_2_dict['Test_Result'] = "Fail"
        else:
            test_2_dict['Test_Value'] = 'No Previous Run'

        QADataHandler.add_row_qa_logs(test_2_dict)

        print(data.get_dataframe("mf_qa_logs_pdf"))


    def __get_spark(self):

        findspark.init()
        os.environ['PYSPARK_SUBMIT_ARGS'] = "--driver-memory 12g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.199," \
                                            "org.apache.hadoop:hadoop-aws:3.3.1,io.delta:delta-core_2.12:1.0.0 " \
                                            "pyspark-shell "

        sc = pyspark.SparkContext()
        sc.setSystemProperty("com.amazonaws.services.s3.enableV4", "true")
        sc.setLogLevel("WARN")
        hadoop_conf = sc._jsc.hadoopConfiguration()
        hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
        hadoop_conf.set("com.amazonaws.services.s3.enableV4", "true")
        if Constants.LOCAL_MODE:
            hadoop_conf.set("fs.s3a.access.key", Constants.AWS_ACCESS_KEY_ID)
            hadoop_conf.set("fs.s3a.secret.key", Constants.AWS_SECRET_ACCESS_KEY)
            hadoop_conf.set("fs.s3a.session.token", Constants.AWS_SESSION_TOKEN)
        hadoop_conf.set("fs.s3a.connection.maximum", "100000")
        # hadoop_conf.set("fs.s3a.endpoint", "s3." + constants.aws_region + ".amazonaws.com")
        hadoop_conf.set("fs.s3a.endpoint", "s3.amazonaws.com")
        hadoop_conf.set("delta.logRetentionDuration", "36500")
        hadoop_conf.set("delta.deletedFileRetentionDuration", "365")

        spark = SparkSession(sc) \
            .builder \
            .appName(Constants.SPARK_APP_NAME) \
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
            .config("spark.sql.debug.maxToStringFields", 1000) \
            .getOrCreate()

        print("Spark Initialized.")
        return spark

    def read_feature_store_adl(self):

        data = Data.get_instance()

        if Constants.TEST_MODE:
            hcp_fs_pdf = pd.read_csv("/Users/<USER>/code/learning_develop/learning/modelFactory/channel_propensity/tests/data/hcp_fs_pdf.csv")
            # Select random 200000 rows from dataframe
            hcp_fs_pdf = hcp_fs_pdf.sample(n=100000, random_state=7)
            hcp_fs_pdf['yearMonth'] = hcp_fs_pdf['yearMonth']
            data.set_dataframe("hcp_fs_pdf", hcp_fs_pdf)
            return

        spark = self.__get_spark()
        hcp_feature_store_path = Constants.ADL_S3_PATH + "data/silver/hcpFeatureStore"
        print("Reading HCP Feature Store from Path: " + hcp_feature_store_path)
        hcp_fs_pdf = spark.read.format("delta").load(hcp_feature_store_path).toPandas()

        print("HCP Feature Store-")
        print(hcp_fs_pdf)

        data.set_dataframe("hcp_fs_pdf", hcp_fs_pdf)

    def execute(self):
        self.read_feature_store_adl()