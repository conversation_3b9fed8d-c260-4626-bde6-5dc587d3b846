import pandas as pd
import unittest

from channel_propensity import constants
from channel_propensity.data import Data
from channel_propensity.initializer import Initializer
from channel_propensity.learner import <PERSON><PERSON>
from channel_propensity.predictor import Predictor
import pickle


class MyTestCase(unittest.TestCase):

    @classmethod
    def setUpClass(cls) -> None:
        # reduced_fs_pdf = pd.read_csv("tests/data/reduced_fs_pdf.csv")
        # full_fs_pdf = pd.read_csv("tests/data/hcp_fs_pdf.csv")
        # data = Data.get_instance()
        # mf_qa_config_pdf = pd.read_csv("tests/data/mf_qa_config_pdf.csv")
        # data.set_dataframe("mf_qa_config_pdf", mf_qa_config_pdf)
        # data.set_dataframe("reduced_fs_pdf", reduced_fs_pdf)
        # data.set_dataframe("full_fs_pdf", full_fs_pdf)
        #
        # for channel in ["send", "visit", "all"]:
        #     data.set_param(f"transform_{channel}", 1)
        #
        # constants.RUN_UID = "test"
        # initializer = Initializer(constants.INITIALIZER_MODULE)
        # initializer.initialize_qa_log()
        # learner = Learner("Learner")
        # learner.execute()
        #
        # pickle.dump(data, open("tests/data/data.pkl", "wb"))

        data = pickle.load(open("tests/data/data.pkl", "rb"))
        full_fs_pdf = pd.read_csv("tests/data/kn_full_fs_pdf.csv")

        data.set_param('Low_segment_1', 33)
        data.set_param('High_segment_1', 66)

        data.set_dataframe("full_fs_pdf", full_fs_pdf)
        Data.set_instance(data)

    def test_execute(self):
        predictor = Predictor("Predictor")
        predictor.execute()


if __name__ == '__main__':
    unittest.main()
