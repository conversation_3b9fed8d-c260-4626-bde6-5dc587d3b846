import unittest

import pandas as pd

import pandas as pd

from channel_propensity.data import Data
from channel_propensity.initializer import Initializer
from channel_propensity.loader import Loader
from channel_propensity.transformer import Transformer


class MyTestCase(unittest.TestCase):

    @classmethod
    def setUpClass(cls) -> None:
        hcp_fs_pdf = pd.read_csv("tests/data/hcp_fs_pdf.csv")
        data = Data.get_instance()
        data.set_dataframe("hcp_fs_pdf", hcp_fs_pdf)

    def test_something(self):
        self.assertEqual(True, True)  # add assertion here

    def __is_identical_dataframe(self, python_pdf, knime_pdf):
        return python_pdf.shape == knime_pdf.shape

    def __test_channel_features(self, channel:str):
        data = Data.get_instance()
        transformer = Transformer()
        transformer.create_flow_variables()
        create_feature_method = transformer.__getattribute__(f'create_{channel}_features')
        create_feature_method()

        python_channel_fs_pdf = data.get_dataframe(f"{channel}_fs_pdf")
        knime_channel_fs_pdf = pd.read_csv(f"tests/data/kn_{channel}_fs_pdf.csv")

        is_passed = self.__is_identical_dataframe(python_channel_fs_pdf, knime_channel_fs_pdf)

        return is_passed

    def test_create_email_features(self):
        is_passed = self.__test_channel_features("email")
        self.assertEqual(is_passed, True)

    def test_create_visit_features(self):
        is_passed = self.__test_channel_features("visit")
        self.assertEqual(is_passed, True)

    def test_create_virtual_visit_features(self):
        is_passed = self.__test_channel_features("virtual_visit")
        self.assertEqual(is_passed, True)

    def test_create_phone_features(self):
        is_passed = self.__test_channel_features("phone")
        self.assertEqual(is_passed, True)

    def test_create_cri_based_features(self):
        is_passed = self.__test_channel_features("cri_based")
        self.assertEqual(is_passed, True)

    def test_create_targets(self):
        data = Data.get_instance()
        transformer = Transformer()
        transformer.create_flow_variables()
        transformer.create_target()

        target_pdf = data.get_dataframe("target_pdf")
        knime_target_pdf = pd.read_csv("tests/data/kn_target_pdf.csv")

        is_passed = self.__is_identical_dataframe(target_pdf, knime_target_pdf)

        return is_passed
        self.assertEqual(is_passed, True)


if __name__ == '__main__':
    unittest.main()
