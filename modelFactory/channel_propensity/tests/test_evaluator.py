import pandas as pd
import unittest

from channel_propensity.data import Data
from channel_propensity.evaluator import Evaluator
from channel_propensity import constants
from channel_propensity.initializer import Initializer


class MyTestCase(unittest.TestCase):
    @classmethod
    def setUpClass(cls) -> None:
        result_pdf = pd.read_csv("tests/data/kn_results_pdf.csv")
        mf_qa_config_pdf = pd.read_csv("tests/data/mf_qa_config_pdf.csv")
        data = Data.get_instance()
        data.set_dataframe("result_pdf", result_pdf)
        data.set_dataframe("mf_qa_config_pdf", mf_qa_config_pdf)
        data.set_param('KS_OP_1', 75.0)

    # def test_evaluate_send(self):
    #     data = Data.get_instance()
    #     data.set_param("transform_send", 1)
    #     evaluator = Evaluator()
    #     print(evaluator.evaluate_channel("send"))
    #     print(data.get_dataframe("evaluate_results_pdf"))
    #     #data.set_dataframe("evaluate_results_pdf", evaluator.evaluate_results_pdf)

    # def test_evaluate_qa(self):
    #     evaluator = Evaluator()
    #     data = Data.get_instance()
    #     evaluator.evaluate_results_pdf = data.get_dataframe("evaluate_results_pdf")
    #     print(evaluator.evaluate_results_pdf)
    #     evaluator.qa_evaluate_process()
    #     print(data.get_dataframe("qa_results_pdf"))

    def test_execute(self):
        data = Data.get_instance()
        data.set_param('Threshold', 0.7)
        constants.RUN_UID = "test"
        initializer = Initializer(constants.INITIALIZER_MODULE)
        initializer.initialize_qa_log()
        for channel in ["send", "email", "visit", "virtual_visit", "phone"]:
            data.set_param(f"transform_{channel}", 1)

        evaluator = Evaluator("Evaluator")
        evaluator.execute()
        print(data.get_dataframe("evaluate_results_pdf"))

        evaluator.qa_module()

if __name__ == '__main__':
    unittest.main()
