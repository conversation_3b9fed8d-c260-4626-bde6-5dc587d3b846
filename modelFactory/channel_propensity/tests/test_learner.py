import pandas as pd
import unittest

from channel_propensity import constants
from channel_propensity.data import Data
from channel_propensity.initializer import Initializer
from channel_propensity.learner import Learner


class MyTestCase(unittest.TestCase):

    @classmethod
    def setUpClass(cls) -> None:
        reduced_fs_pdf = pd.read_csv("tests/data/reduced_fs_pdf.csv")
        data = Data.get_instance()
        mf_qa_config_pdf = pd.read_csv("tests/data/mf_qa_config_pdf.csv")
        data.set_dataframe("mf_qa_config_pdf", mf_qa_config_pdf)
        data.set_dataframe("reduced_fs_pdf", reduced_fs_pdf)
        for channel in ["send", "visit", "all"]:
            data.set_param(f"transform_{channel}", 1)

    def test_execute(self):
        constants.RUN_UID = "test"
        initializer = Initializer(constants.INITIALIZER_MODULE)
        initializer.initialize_qa_log()
        learner = Learner("Learner")
        learner.execute()
        learner.qa_module()


if __name__ == '__main__':
    unittest.main()
