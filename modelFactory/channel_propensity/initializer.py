import ast
import uuid

import datetime
import pandas as pd

from abstract_model_factory.abstract_initializer import AbstractInitializer
import sys
import getopt

from channel_propensity.data import Data
from channel_propensity.constants import Constants
from os import path
import json
import os
from channel_propensity.data_access_layer import DataAccessLayer
from aktana_ml_utils import aktana_ml_utils

class Initializer(AbstractInitializer):

    def get_cmdline_args(self, argv):

        """
        PURPOSE:
            Read the command-line arguments and store them in a dictionary.
            Command-line arguments should come in pairs, e.g.:
                "--customer abc"
        INPUTS:
            The command line arguments (sys.argv).
        RETURNS:
            Returns the dictionary.
        DESIRABLE ENHANCEMENTS:
            Improve error detection and handling.
        """

        data = Data.get_instance()

        print("####### Command Line Parameters:", sys.argv)


        shortopts = "hc:e:a:r:ec:o:l:d"
        longopts = ["customer=", "env=", "app=", "region=", "ecosystem=", "json-output=", "local=", "use-debug="]

        try:
            opts, args = getopt.getopt(argv[1:], shortopts, longopts)
        except getopt.GetoptError:
            print("Supported args are:" + str(longopts), file=sys.stderr)
            sys.exit(2)

        for opt, arg in opts:
            if opt == '-h':
                print(
                    "Usage: --customer, --env, --scenario are required. --conn-param-file, --rundate, --param1, --param2 or --param3 are optional.")
                sys.exit(2)
            elif opt in ("-c", "--customer"):
                data.set_param("customer", arg)
            elif opt in ("--use-debug"):
                data.set_param("use-debug", arg)
            elif opt in ("-e", "--env"):
                data.set_param("env", arg)
            elif opt in ("-f", "--conn-param-file"):
                data.set_param("conn-param-file", arg)
            elif opt in ("-d", "--use-debug"):
                data.set_param("use-debug", arg)
            elif opt in ("-l", "--local"):
                data.set_param("local", arg)
            else:
                data.set_param(opt[2:], arg)

            # If local is true set local constants
            if data.get_param("local", "") != "":
                Constants.LOCAL_MODE = True

            # If use-debug is true set debug constants
            if data.get_param("use-debug", "") != "":
                Constants.TEST_MODE = True

        print("****** Command Line Parameters:" + str(data.params))

    def read_param_file(self):
        """

        @return:
        """
        data = Data.get_instance()

        # print command line arguments
        print("****** Command Line Parameters:",  sys.argv)

        short_opt = "hc:e:a:r:ec:o:l:d"
        long_opt = ["customer=", "env=", "app=", "region=", "ecosystem=", "json-output=", "local=", "use-debug="]
        cmd_argv = sys.argv


        ak_ml_utils = aktana_ml_utils()
        ak_ml_utils.initialize(cmd_argv, short_opt, long_opt)
        params = ak_ml_utils.get_params_json()
        params = json.loads(params)
        #print("Params:" + str(params))

        Constants.ACCESS_ID = params['adl-awsAccessKey']
        Constants.ACCESS_KEY = params['adl-awsAccessKey']
        Constants.SECRET_KEY = params['adl-awsSecretKey']

        Constants.DCO_S3_PATH = params['adl-dcoS3Location']
        Constants.ADL_S3_PATH = params['adl-adlS3Location']

        Constants.sw_account = params['snowflake-account']
        Constants.sw_user = params['snowflake-user']
        Constants.sw_password = params['snowflake-password']
        Constants.sw_database = params['snowflake-database']
        Constants.sw_schema = params['snowflake-schema']
        Constants.sw_role = params['snowflake-role']
        Constants.sw_warehouse = params['snowflake-warehouse']
        Constants.ADL_S3_PATH = Constants.ADL_S3_PATH.replace('s3:', 's3a:')

        Constants.S3_BASE_PATH = Constants.ADL_S3_PATH.split('adl/')[0]
        Constants.TARGET_S3_PATH = Constants.S3_BASE_PATH + 'channel_propensity/'

    def read_qa_config(self):
        query = 'select * from "MF_QA_config"'
        qa_config_pdf = DataAccessLayer.read_snow_flake_pandas(query)
        print("MF QA Config-", qa_config_pdf)
        Data.get_instance().set_dataframe('mf_qa_config_pdf', qa_config_pdf)

    def read_model_threshold(self):
        query = 'select * from "Model_Threshold"'
        model_threshold_pdf = DataAccessLayer.read_snow_flake_pandas(query)
        print("Model Thresholds", model_threshold_pdf)
        data = Data.get_instance()

        # Iterate through the model thresholds and set the parameters
        for index, row in model_threshold_pdf.iterrows():
            data.set_param(f"{row['Threshold_ID']}", row['Threshold_Value_Number'])

    def read_system_config(self):
        query = 'select * from "System_Config" where "Process_Name" = \'channel_propensity\';'
        system_config_pdf = DataAccessLayer.read_snow_flake_pandas(query)
        print("System Config", system_config_pdf)
        data = Data.get_instance()

        # Iterate through the dataframe columns and set values in data params
        for col in system_config_pdf.columns:
            data.set_param(col, system_config_pdf[col].values[0])


        # If Client_Name is default set customer value from command line params
        if data.get_param("Client", "Default") == "Default":
            data.set_param("Client_Name", data.get_param("customer"))

        channel_active_dict = ast.literal_eval(data.get_param('Channel_Active'))
        for channel, value in channel_active_dict.items():
            data.set_param(channel, value)
            data.set_param(f'transform_{channel}', value)

        # Set all channel to 1
        data.set_param('transform_all', 1)

        data.set_param('deployment_option', 1) # 0 - Data, 1- Data+Model

        data.set_param('s3_model_store_path', Constants.DCO_S3_PATH + "data/dco_read_write/raw_files/channel_propensity/model/")
        model_name = data.get_param("Client_Name") + "_" + datetime.date.today().strftime("%Y-%m-%d")
        data.set_param('Model_Name', model_name)

    def initialize_qa_log(self):
        data = Data.get_instance()
        mf_qa_config_pdf = data.get_dataframe('mf_qa_config_pdf')
        mf_qa_logs_pdf = pd.DataFrame(columns=mf_qa_config_pdf.columns)
        mf_qa_logs_pdf["Test_Value"] = None
        mf_qa_logs_pdf["Test_Result"] = None
        mf_qa_logs_pdf["Test_Action"] = None
        mf_qa_logs_pdf["Client_Name"] = None
        mf_qa_logs_pdf["Process_Name"] = None
        mf_qa_logs_pdf["Model_Name"] = None
        mf_qa_logs_pdf["Update_Date"] = None
        mf_qa_logs_pdf["Run_Uid"] = None

        data.set_dataframe("mf_qa_logs_pdf", mf_qa_logs_pdf)

    def qa_module(self):
        pass
        # data = Data.get_instance()
        # qa_config_pdf = data.get_dataframe("mf_qa_config_pdf")
        # test_qa_config = qa_config_pdf.loc[qa_config_pdf['"Test_Phase"'] == 'Init', :]
        # All the Tests are not substantial hence passing them now

    def execute(self):
        self.get_cmdline_args(sys.argv)
        self.read_param_file()
        self.read_system_config()
        self.read_model_threshold()

        Constants.RUN_UID = str(uuid.uuid4())

        self.read_qa_config()
        self.initialize_qa_log()

        print("Initialized channel propensity with Run Uid- ", Constants.RUN_UID)
