

class Constants:
    ADL_S3_PATH = None
    DCO_S3_PATH = None

    SPARK_APP_NAME = "channel_propensity"

    SNOWFLAKE_SOURCE_NAME = "net.snowflake.spark.snowflake"
    sfOptions_spark_snowFlake = None

    sw_account = 'aktana.ap-northeast-1.aws'
    sw_user = 'novartisau_ml_admin'
    sw_password = 'woE28cF8QUjaIbnI1arDCy5MxkStA9PP'
    sw_database = 'NOVARTISAU_DW_DEV'
    sw_schema = 'DCO'
    sw_warehouse = 'DEMO_WH'
    sw_role = 'NOVARTISAU_NONPROD_APPADMIN_ROLE'

    ADL_S3_PATH = None
    DCO_S3_PATH = None

    # Module Names for the different stages of the pipeline
    INITIALIZER_MODULE = "Init"
    LOADER_MODULE = "Load"
    TRANSFORMER_MODULE = "Transform"
    LEARNER_MODULE = "Learn"
    PREDICTOR_MODULE = "Score"
    EVALUATOR_MODULE = "Evaluate"
    DEPLOYER_MODULE = "Deploy"

    CHANNELS = ['send', 'visit', 'virtual_visit', 'phone']
    CHANNELS_ALL = CHANNELS + ['all']

    TEST_MODE = False
    LOCAL_MODE = False
    RUN_UID = "test-run-local"

    AWS_ACCESS_KEY_ID = ""
    AWS_SECRET_ACCESS_KEY = ""
    AWS_SESSION_TOKEN = ""


    @classmethod
    def get_snowflake_conn_params(cls):
        global sfOptions_spark_snowFlake

        if sfOptions_spark_snowFlake is None:
            sfOptions_spark_snowFlake = {
                "sfURL": cls.sw_account + ".snowflakecomputing.com",
                "sfUser": cls.sw_user,
                "sfPassword": cls.sw_password,
                "sfDatabase": cls.sw_database,
                "sfSchema": cls.sw_schema,
                "sfWarehouse": cls.sw_warehouse,
                "sfRole": cls.sw_role,
                "parallelism": "64",
            }
            print("Global sfOptions_spark_snowFlake has been initialized")
        return sfOptions_spark_snowFlake