from datetime import datetime, date

import pandas as pd

from abstract_model_factory.abstract_deployer import AbstractDeployer
from channel_propensity.constants import Constants
from channel_propensity.data import Data
from channel_propensity.data_access_layer import DataAccessLayer

from channel_propensity.qa_data_handler import QADataHandler
import boto3
import tempfile

class Deployer(AbstractDeployer):

    def qa_module(self):
        test_phase = "Deploy"
        data = Data.get_instance()
        deployment_status = data.get_param('deployment_status', "Nothing Deployed - Rollback")

        # Test 1: Info log deployment mode
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=1,
                                             test_value=deployment_status)

    @staticmethod
    def write_calibrated_scores_to_snowflake():
        data = Data.get_instance()
        calibrated_scores_pdf = data.get_dataframe('calibrated_scores_pdf')

        # Format calibrated scores to flat structure
        formatted_cali_scores_pdf = None

        for channel in Constants.CHANNELS:
            probability_pdf = calibrated_scores_pdf[['accountId', f'{channel}_prob_cali']].melt(id_vars='accountId',
                                                                             value_vars=[f'{channel}_prob_cali'],
                                                                             var_name='Channel',
                                                                             value_name='Calibrated_Probability')

            segment_pdf = calibrated_scores_pdf[['accountId', f'{channel}_prob_cali_segment']].melt(id_vars='accountId',
                                                                                 value_vars=[f'{channel}_prob_cali_segment'],
                                                                                 var_name='Channel',
                                                                                 value_name='Segment')

            channel_cali_scores_pdf = probability_pdf.merge(segment_pdf.drop(columns='Channel'), on='accountId')

            if formatted_cali_scores_pdf is not None:
                #formatted_cali_scores_pdf.concat(channel_cali_scores_pdf)
                formatted_cali_scores_pdf = pd.concat([formatted_cali_scores_pdf, channel_cali_scores_pdf])
            else:
                formatted_cali_scores_pdf = channel_cali_scores_pdf

        # Add accountUid column
        full_fs_pdf = data.get_dataframe("full_fs_pdf")
        formatted_cali_scores_pdf = formatted_cali_scores_pdf.merge(full_fs_pdf[['accountId', 'accountUid']],
                                                                    on='accountId', how='inner')

        formatted_cali_scores_pdf = DataAccessLayer.add_metadata_to_pdf(formatted_cali_scores_pdf)

        DataAccessLayer.write_snow_flake_pandas(formatted_cali_scores_pdf, 'Calibrated_Scores')

        print("Calibrated scores written to snowflake")
        return formatted_cali_scores_pdf

    def write_calibrated_scores_to_s3(self):
        data = Data.get_instance()
        cali_scores_pdf = data.get_dataframe('calibrated_scores_pdf')

        # Add accountUid column
        full_fs_pdf = data.get_dataframe("full_fs_pdf")
        cali_scores_pdf = cali_scores_pdf.merge(full_fs_pdf[['accountId', 'accountUid']],
                                                                    on='accountId', how='inner')

        cali_scores_pdf = DataAccessLayer.add_metadata_to_pdf(cali_scores_pdf)

        run_date = cali_scores_pdf['Date'].iloc[0]
        run_month = run_date.strftime("%Y-%m") + "-01"

        predictions_s3_dir = Constants.TARGET_S3_PATH + f"runmonth={run_month}/"
        target_name = "channel_propensity.parquet"
        self.write_predictions_to_s3(cali_scores_pdf, predictions_s3_dir, target_name)

        print("Calibrated scores written to snowflake")
        return cali_scores_pdf

    def get_s3_client(self):
        ACCESS_KEY = Constants.AWS_ACCESS_KEY_ID
        SECRET_KEY = Constants.AWS_SECRET_ACCESS_KEY
        SESSION_TOKEN = Constants.AWS_SESSION_TOKEN

        if ACCESS_KEY != "":
            s3 = boto3.client('s3',
                            aws_access_key_id=ACCESS_KEY,
                            aws_secret_access_key=SECRET_KEY,
                            aws_session_token=SESSION_TOKEN)

        else:
            s3 = boto3.client('s3')

        return s3

    def write_predictions_to_s3(self, predictions, s3_path, target_name):
        s3_bucket = s3_path.split('/')[2]
        predictions_path = '/'.join(s3_path.split('/')[3:]) + target_name

        s3 = self.get_s3_client()

        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/{target_name}"
            predictions.to_parquet(temp_path, index=False)
            s3.upload_file(temp_path, s3_bucket, predictions_path)

        print("Uploaded predictions to : " + s3_path)

    # def write_calibrated_scores_to_s3(self, scored_pdf):

    #     scored_pdf['ChannelName'] = scored_pdf['Channel'].str.split('_prob_cali', n=1).str[0]
    #     scored_pdf['ChannelName'] = scored_pdf['ChannelName'].str.upper()
    #     scored_pdf['ChannelName'] = scored_pdf['ChannelName'] + "_CHANNEL"
    #     run_date = scored_pdf['Date'].iloc[0]
    #     run_month = run_date.strftime("%Y-%m") + "-01"

    #     predictions_s3_dir = Constants.TARGET_S3_PATH + f"runmonth={run_month}/"
    #     target_name = "channel_propensity.parquet"
    #     self.write_predictions_to_s3(scored_pdf, predictions_s3_dir, target_name)

    #     return scored_pdf

    @staticmethod
    def write_xai_to_snowflake():
        data = Data.get_instance()
        xai_pdf = data.get_dataframe('xai_result_pdf')

        if xai_pdf is None:
            print("No XAI results to write to snowflake")
            return

        xai_pdf = DataAccessLayer.add_metadata_to_pdf(xai_pdf)

        # Add accountUid column
        full_fs_pdf = data.get_dataframe("full_fs_pdf")
        xai_pdf = xai_pdf.merge(full_fs_pdf[['accountId', 'accountUid']],
                                on='accountId', how='inner')

        DataAccessLayer.write_snow_flake_pandas(xai_pdf, 'xAI_Factors')

        print("XAI written to snowflake")

    @staticmethod
    def write_models_to_s3():

        data = Data.get_instance()

        for channel in Constants.CHANNELS + ['all']:
            cp_channel_model = data.get_model(f"{channel}_model")

            if cp_channel_model:
                target_name = f'Y_{channel}'
                s3_path = data.get_param('s3_model_store_path') + f"cp_{channel}_{datetime.utcnow().strftime('%Y%m%d-%H%M%S')}_model.pmml"
                DataAccessLayer.write_model_pmml_to_s3(model=cp_channel_model, s3_path=s3_path, target_name=target_name)
                DataAccessLayer.write_object_pkl_to_s3(object=cp_channel_model, s3_path=s3_path.replace(".pmml", ".pkl"))

                data.set_param(f'deployed_{channel}_model_path', s3_path)

                print("Uploaded model to : " + data.get_param(f'deployed_{channel}_model_path'))
            else:
                print(f"Model for {channel} channel not found, nothing to upload to S3")

    @staticmethod
    def write_models_pdf_to_snowflake():
        data = Data.get_instance()
        models_pdf = pd.DataFrame(columns=['Channel', 'Deployed_model_path'])
        for channel in Constants.CHANNELS + ['all']:
            model_path = data.get_param(f'deployed_{channel}_model_path')
            if model_path:
                model_path = model_path.replace("s3:/", "")
                models_pdf.loc[len(models_pdf)] = {'Channel': channel, 'Deployed_model_path': model_path}

        # Check if model path is empty
        if models_pdf.empty:
            print("No models to write to snowflake")
        else:
            models_pdf['Feature_Period'] = data.get_param('Feature_Period')
            models_pdf = DataAccessLayer.add_metadata_to_pdf(models_pdf)
            DataAccessLayer.write_snow_flake_pandas(models_pdf, 'Models')
            print("Models written to snowflake")

    @staticmethod
    def write_evaluation_results_to_snowflake():
        data = Data.get_instance()
        evaluation_results_pdf = data.get_dataframe('evaluate_results_pdf')

        evaluation_results_pdf = DataAccessLayer.add_metadata_to_pdf(evaluation_results_pdf)

        DataAccessLayer.write_snow_flake_pandas(evaluation_results_pdf, 'Evaluation_Table')

        print("Evaluation results written to snowflake")


    @staticmethod
    def write_model_config_to_snowflake(Status='Completed', Deployment='Current_Model'):

        data = Data.get_instance()

        if Status == 'Completed' and Deployment == 'Current_Model':
            # Update older current_model row to NA
            query = "update \"Model_Config\" set \"Deployment\" = 'NA' where \"Deployment\" = 'Current_Model'"
            DataAccessLayer.execute_snowflake_update_query(query)

        model_package_combination = {f'Train_{channel}_Flag':data.get_param(f'transform_{channel}', 0) for channel in Constants.CHANNELS}

        model_config_data = {'Feature_Period': data.get_param('Feature_Period'),
                             'Update_Type': data.get_param('Update_Type', 'Automatic'),
                             'Client_Name': data.get_param('Client_Name'),
                             'ChallengerModel': data.get_param('ChallengerModel','NA'),
                             'Model_Initialisation': data.get_param('Model_Initialisation', 'missing'),
                             'Model_Package_Combination': str(model_package_combination),
                             'Status': Status,
                             'Deployment': Deployment}

        model_config_pdf = pd.DataFrame(model_config_data, index=[0])
        model_config_pdf = DataAccessLayer.add_metadata_to_pdf(model_config_pdf)
        model_config_pdf.rename(columns={'Timestamp': 'Last_Execution'}, inplace=True)

        DataAccessLayer.write_snow_flake_pandas(model_config_pdf, 'Model_Config')

    def execute(self):
        data = Data.get_instance()
        deployment_option = data.get_param('deployment_option') # 0 - Data, 1- Data+Model
        print("Deploying data")
        self.write_calibrated_scores_to_snowflake()
        self.write_calibrated_scores_to_s3()
        self.write_evaluation_results_to_snowflake()
        self.write_xai_to_snowflake()
        data.set_param('deployment_status', "Only Data Deployed")

        if deployment_option == 1:
            print("Deploying learned models")
            self.write_models_to_s3()
            self.write_models_pdf_to_snowflake()
            self.write_model_config_to_snowflake()
            data.set_param('deployment_status', "Models & Data Deployed")

        print("Deployment completed for run_uid: " + Constants.RUN_UID)