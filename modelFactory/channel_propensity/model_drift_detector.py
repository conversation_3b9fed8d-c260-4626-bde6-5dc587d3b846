from datetime import date, timed<PERSON>ta

from abstract_model_factory.abstract_model_drift_detector import AbstractModelDriftDetector
from channel_propensity.constants import Constants
from channel_propensity.data import Data
from channel_propensity.data_access_layer import DataAccessLayer
from channel_propensity.evaluator import Evaluator
from channel_propensity.predictor import Predictor


class ModelDriftDetector(AbstractModelDriftDetector):
    def is_model_retraining_required(self) -> bool:

        print("Checking if model retraining is required")

        is_model_training_required = True
        data = Data.get_instance()
        data.set_param('is_model_training_required', 1)

        # Read Model_Config table from snowflake
        query = 'select * from "Model_Config" where "Deployment" = \'Current_Model\';'
        model_config_pdf = DataAccessLayer.read_snow_flake_pandas(query)

        # Check if earlier model exists
        if model_config_pdf.empty:
            print("Retraining Required: No earlier model exists")
            return is_model_training_required

        # Check if model is older than a quarter
        model_date = model_config_pdf['Date'].values[0]
        if model_date < date.today() - timedelta(days=90):
            print("Retraining Required: Model is older than a quarter")
            return is_model_training_required

        # Check if new channels are added or removed from configuration
        data = Data.get_instance()
        model_package_combination = model_config_pdf['Model_Package_Combination'].values[0]

        # a. Convert model_package_combination dictionary string to dictionary
        model_package_combination = eval(model_package_combination)

        # b. Check if new channels are added or removed from configuration
        for channel in Constants.CHANNELS:
            if f'Train_{channel}_Flag' not in model_package_combination:
                print(f"Retraining Required: New channel {channel} added to configuration")
                return is_model_training_required
            elif model_package_combination[f'Train_{channel}_Flag'] != data.get_param(f'transform_{channel}', 0):
                print(f"Retraining Required: Channel {channel} configuration changed")
                return is_model_training_required

        # Check AUC of current model
        # Get current model name
        model_name = model_config_pdf['Model_Name'].values[0]
        run_uid = model_config_pdf['Run_Uid'].values[0]

        # Get Models table from snowflake
        query = f'select * from "Models" where "Model_Name" = \'{model_name}\' and "Run_Uid" = \'{run_uid}\';'
        models_pdf = DataAccessLayer.read_snow_flake_pandas(query)

        if models_pdf.empty:
            print("Retraining Required: No model found in Models table")
            return is_model_training_required

        full_fs_pdf = data.get_dataframe('full_fs_pdf')
        model_dict = {}

        # Get S3 path of current models
        for channel in Constants.CHANNELS_ALL:
            if data.get_param(f'transform_{channel}', 0) == 0:
                print("Skipping retraining AUC test for channel: " + channel + " as it is not enabled")
                continue
            model_path = models_pdf[models_pdf['Channel'] == channel]['Deployed_model_path'].values[0]
            model_path = "s3a:/" + model_path
            # Read model from S3
            print(f"Reading model from {model_path}")
            #model = DataAccessLayer.read_model_from_s3(model_path)
            model = DataAccessLayer.read_model_pkl_from_s3(model_path.replace(".pmml", ".pkl"))

            # Model is None if model is not found in S3
            if not model:
                print(f"Retraining Required: No model found for channel {channel}")
                return is_model_training_required

            # Set channel model in data
            model_dict[f'{channel}_model'] = model

            # Use predictor to get predictions
            predicted_pdf = Predictor.predict_using_xgb_for_channel(channel, model, full_fs_pdf)

            # Get AUC
            target = "Y_" + channel
            prob = channel + "_prob"
            _, _, auc = Evaluator.calculate_fmeasure_threshold_auc(predicted_pdf.dropna(subset=[target, prob]),
                                                                   target,
                                                                   prob)

           # Check if AUC is less than threshold
            threshold = float(data.get_param('Threshold'))
            if auc < threshold:
                print(f"Retraining Required: AUC of {channel} is less than threshold, AUC: {auc}, Threshold: {threshold}")
                return is_model_training_required

        # If none of the above conditions are met, then no need to retrain model
        is_model_training_required = False
        data.set_param('is_model_retraining_required', 0)

        data.set_param('deployment_option', 0)  # 0 - Data, 1- Data+Model
        for channel_model, model in model_dict.items():
            data.set_model(channel_model, model)
        print("Model Retraining Not Required")
        return is_model_training_required
