import numpy as np
import pandas as pd
import shap as shap

from abstract_model_factory.abstract_predictor import AbstractPredictor
from channel_propensity.constants import Constants
from channel_propensity.data import Data
from scipy.optimize import minimize
from lightgbm import LGBMClassifier

from channel_propensity.qa_data_handler import QADataHandler


class Predictor(AbstractPredictor):

    def qa_module(self):
        """
        QA module for predictor
            Test_Phase	Test_ID	Test_Description	    Test_Priority	Test_Action
            Score	    1	Send Low anchor point	            info	This is for info only.
            Score	    2	Visit Low anchor point	            info	This is for info only.
            Score	    3	Virtual Visit Low anchor point	    info	This is for info only.
            Score	    4	Phone Low anchor point	            info	This is for info only.
            Score	    5	Send High anchor point	            info	This is for info only.
            Score	    6	Visit High anchor point	            info	This is for info only.
            Score	    7	Virtual Visit High anchor point	    info	This is for info only.
            Score	    8	Phone High anchor point	            info	This is for info only.
            Score	    0	Time of execution in mili seconds	info	This is for info only.

        :return:
        """
        test_phase = "Score"
        data = Data.get_instance()

        # Test 1: Send Low anchor point
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=1,
                                             test_value=data.get_param('send_low_anchor_point'))

        # Test 2: Visit Low anchor point
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=2,
                                             test_value=data.get_param('visit_low_anchor_point'))

        # Test 3: Virtual Visit Low anchor point
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=3,
                                             test_value=data.get_param('virtual_visit_low_anchor_point'))

        # Test 4: Phone Low anchor point
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=4,
                                             test_value=data.get_param('phone_low_anchor_point'))

        # Test 5: Send High anchor point
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=5,
                                             test_value=data.get_param('send_high_anchor_point'))

        # Test 6: Visit High anchor point
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=6,
                                             test_value=data.get_param('visit_high_anchor_point'))

        # Test 7: Virtual Visit High anchor point
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=7,
                                             test_value=data.get_param('virtual_visit_high_anchor_point'))

        # Test 8: Phone High anchor point
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=8,
                                             test_value=data.get_param('phone_high_anchor_point'))

    @staticmethod
    def get_percentile(channel_metadata, input_data, percentile_dataframe):
        for index, row in channel_metadata.iterrows():
            percentile_dataframe[row['Probablity_Column']] = np.percentile(input_data[row['Probablity_Column']],
                                                                           q=np.linspace(0, 100, 10001),
                                                                           interpolation='lower').round(5)
        return percentile_dataframe

    @staticmethod
    def get_anchor_points(channel_metadata, percentile_dataframe, low=0.001, high=0.75):
        low_dict = {}
        high_dict = {}

        for index, row in channel_metadata.iterrows():
            low_dict[row['Channel'] + '_low'] = \
            percentile_dataframe[percentile_dataframe[row['Probablity_Column']] >= low]['percentile'].min()
            high_dict[row['Channel'] + '_high'] = \
            percentile_dataframe[percentile_dataframe[row['Probablity_Column']] >= high]['percentile'].min()

        percentile_low = max([0 if x != x else x for x in low_dict.values()])
        print('percentile_low', percentile_low)

        percentile_high = max([0 if x != x else x for x in high_dict.values()])
        print('percentile_high ', percentile_high)

        low_df = percentile_dataframe[percentile_dataframe['percentile'] == percentile_low]
        high_df = percentile_dataframe[percentile_dataframe['percentile'] == percentile_high]

        low = {}
        high = {}

        for index, row in channel_metadata.iterrows():
            low[row['Channel'] + '_low'] = round(low_df[row['Probablity_Column']].tolist()[0], 3)
            high[row['Channel'] + '_high'] = round(high_df[row['Probablity_Column']].tolist()[0], 3)

        return low, high

    def add_xai_for_channel(self, channel):
        data = Data.get_instance()

        if data.get_model(f"{channel}_model"):
            print(f"Adding XAI for {channel} channel")
            full_fs_pdf = data.get_dataframe("full_fs_pdf")
            calibrated_scores_pdf = data.get_dataframe("calibrated_scores_pdf")

            xai_pdf = full_fs_pdf.merge(calibrated_scores_pdf[['accountId', f'{channel}_prob_cali_segment']], on=['accountId'])
            xai_pdf[f'Y_{channel}_high'] = xai_pdf[f'{channel}_prob_cali_segment'].apply(lambda x: 1 if x == 'High' else 0)
            cols = ['Y_visit', 'Y_phone', 'Y_virtual_visit', 'Y_send', 'Y_all', 'visit_prob', 'phone_prob',
                    'send_prob', 'virtual_visit_prob', 'all_prob']

            xai_pdf.drop(cols, axis=1, inplace=True)
            # Only use numeric columns
            xai_numeric_pdf = xai_pdf._get_numeric_data()
            xai_train_input_pdf = xai_numeric_pdf.drop(['accountId', f'Y_{channel}_high'], axis=1)

            # Use first column as value column
            # value_column = data[data.columns[0]]
            # Use second column as target column
            target_col_pdf = xai_numeric_pdf[f'Y_{channel}_high']

            ## LightGBM - parameters defining and model fitting
            params = {'max_depth': 4,
                      'objective': 'binary',
                      'learning_rate': 0.01,
                      'max_bin': 500,
                      'subsample': 0.9,
                      'subsample_freq': 1,
                      'colsample_bytree': 0.8,
                      'metric': 'f1',
                      'scale_pos_weight': 1.4}

            lgbm_model = LGBMClassifier(objective=params['objective'], verbose=1,
                                        max_depth=params['max_depth'],
                                        #learing_rate=params['learning_rate'],
                                        max_bin=params['max_bin'],
                                        subsample=params['subsample'],
                                        subsample_freq=params['subsample_freq'],
                                        colsample_bytree=params['colsample_bytree'],
                                        n_estimators=1000,
                                        scale_pos_weight=params['scale_pos_weight'])
            lgbm_model.fit(xai_train_input_pdf, target_col_pdf)
            # plot_importance(lgbm_model,max_num_features=12,figsize=(12,6)).get_figure().savefig(buffer, format='svg')

            print("Completed lightgbm model fitting")
            shap_values = shap.TreeExplainer(lgbm_model).shap_values(xai_train_input_pdf)

            no_col_names_df = pd.DataFrame(shap_values[0])
            no_col_names_df.columns = xai_train_input_pdf.columns
            df = no_col_names_df.rank(axis=1, ascending=False, method='first').astype(int).copy()
            xai_numeric_pdf = xai_numeric_pdf.reset_index()
            df['accountId'] = xai_numeric_pdf['accountId']
            xai_result_pdf = df[df.columns.values].melt(id_vars=['accountId'], var_name='Factor_Name',
                                                      value_name='Factor_Rank')
            xai_result_pdf['Display_Name'] = xai_result_pdf['Factor_Name']
            xai_result_pdf = xai_result_pdf[xai_result_pdf['Factor_Rank'] < 4]
            xai_result_pdf['Channel'] = channel
            return xai_result_pdf
        else:
            print(f"Channel model not found for channel:{channel}. Unable to add xai.")
            return None

    @staticmethod
    def predict_using_xgb_for_channel(channel, channel_model, full_fs_pdf):

        if channel_model:
            feature_cols = channel_model.get_booster().feature_names

            full_fs_pdf[f'{channel}_prob'] = \
                channel_model.predict_proba(full_fs_pdf[feature_cols])[:, 1]
        else:
            print("Channel model not found for channel: ", channel)
            full_fs_pdf[f'{channel}_prob'] = 0.0

        return full_fs_pdf

    @staticmethod
    def predict_using_pmml_for_channel(channel, pmml_model, full_fs_pdf):

        if pmml_model:

            feature_cols = list(pmml_model.field_mapping.keys())[:-1]
            full_fs_pdf[f'{channel}_prob'] = \
                pmml_model.predict_proba(full_fs_pdf[feature_cols].fillna(value=0), )[:, 1]
        else:
            print("Channel model not found for channel: ", channel)
            full_fs_pdf[f'{channel}_prob'] = 0.0

        return full_fs_pdf

    @staticmethod
    def sigmoid_transform(input_array, low, low_target, high, high_target):
        # objective function
        def objective(parameters):
            A = parameters[0]
            B = parameters[1]
            C = parameters[2]
            D = parameters[3]
            return (A * (1 / (1 + np.exp(-(B * low + C)))) + D - low_target) ** 2 + (
                        A * (1 / (1 + np.exp(-(B * high + C)))) + D - high_target) ** 2

        # transformed low prob >= 0
        def constraint1(parameters):
            A = parameters[0]
            B = parameters[1]
            C = parameters[2]
            D = parameters[3]
            return A * (1 / (1 + np.exp(-(B * low + C)))) + D

        # transformed low prob <= 1
        def constraint2(parameters):
            A = parameters[0]
            B = parameters[1]
            C = parameters[2]
            D = parameters[3]
            return -A * (1 / (1 + np.exp(-(B * low + C)))) - D + 1

        # transformed high prob >= 0
        def constraint3(parameters):
            A = parameters[0]
            B = parameters[1]
            C = parameters[2]
            D = parameters[3]
            return A * (1 / (1 + np.exp(-(B * high + C)))) + D

        # transformed high prob <= 1
        def constraint4(parameters):
            A = parameters[0]
            B = parameters[1]
            C = parameters[2]
            D = parameters[3]
            return -A * (1 / (1 + np.exp(-(B * high + C)))) - D + 1

        # prob = 0 -> 0
        def constraint5(parameters):
            A = parameters[0]
            B = parameters[1]
            C = parameters[2]
            D = parameters[3]
            return A * (1 / (1 + np.exp(-(B * 0.0 + C)))) + D

        # prob = 1 -> 1
        def constraint6(parameters):
            A = parameters[0]
            B = parameters[1]
            C = parameters[2]
            D = parameters[3]
            return A * (1 / (1 + np.exp(-(B * 1.0 + C)))) + D - 1

        # inital guess
        parameter_init = np.ones(4)
        con1 = {'type': 'ineq', 'fun': constraint1}
        con2 = {'type': 'ineq', 'fun': constraint2}
        con3 = {'type': 'ineq', 'fun': constraint3}
        con4 = {'type': 'ineq', 'fun': constraint4}
        con5 = {'type': 'eq', 'fun': constraint5}
        con6 = {'type': 'eq', 'fun': constraint6}
        cons = ([con1, con2, con3, con4, con5, con6])
        solution = minimize(objective, parameter_init, method='SLSQP', jac='2-point', constraints=cons,
                            options={'disp': True, 'maxiter': 1000})
        print('Parameter A, B, C, D: ', solution.x)
        A = solution.x[0]
        B = solution.x[1]
        C = solution.x[2]
        D = solution.x[3]
        return A * (1 / (1 + np.exp(-(B * input_array + C)))) + D

    def cross_calibration(self, channel_prob_pdf, channels, target_channel, extracted_low_target,
                          extracted_high_target, low_dict, high_dict):
        for channel in channels:
            if channel == target_channel:
                print('channel **:', channel)
                channel_prob_pdf[f'{channel}_prob_cali'] = channel_prob_pdf[f'{channel}_prob']
            else:
                low = low_dict.get(channel + '_low')
                high = high_dict.get(channel + '_high')
                print(f'channel ##: {channel} low: {low} high : {high} extracted_low_target : {extracted_low_target} '
                      f'extracted_high_target: {extracted_high_target}')

                channel_prob_pdf[f'{channel}_prob_cali'] = self.sigmoid_transform(channel_prob_pdf[f'{channel}_prob'],
                                                                                  low=low,
                                                                                  low_target=extracted_low_target,
                                                                                  high=high,
                                                                                  high_target=extracted_high_target)
        return channel_prob_pdf

    def execute(self):

        data = Data.get_instance()
        full_fs_pdf = data.get_dataframe("full_fs_pdf")

        for channel in Constants.CHANNELS_ALL:
            print("Predicting for channel: " + channel)
            channel_model = data.get_model(f'{channel}_model')
            full_fs_pdf = self.predict_using_xgb_for_channel(channel, channel_model, full_fs_pdf)
            # if data.get_param('is_model_retraining_required', 1) == 1:
            #     full_fs_pdf = self.predict_using_xgb_for_channel(channel, channel_model, full_fs_pdf)
            # else:
            #     full_fs_pdf = self.predict_using_pmml_for_channel(channel, channel_model, full_fs_pdf)

        for channel in Constants.CHANNELS:
            print("Cross calibrating for channel: " + channel)

            full_fs_pdf[f'{channel}_prob_raw'] = full_fs_pdf[f'{channel}_prob']
            full_fs_pdf[f'{channel}_prob'] = full_fs_pdf[f'{channel}_prob'] * 0.85 / full_fs_pdf['all_prob']
            full_fs_pdf[f'{channel}_prob'] = np.where(full_fs_pdf[f'{channel}_prob'] > 0.9, 0.9,
                                                         full_fs_pdf[f'{channel}_prob'])

        percentile_pdf = pd.DataFrame({'percentile': np.linspace(0, 100, 10001)})

        channel_col_values = Constants.CHANNELS
        probablity_col_values = [f'{channel}_prob' for channel in Constants.CHANNELS]
        channel_data_dict = {'Channel': channel_col_values, 'Probablity_Column': probablity_col_values}
        channel_pdf = pd.DataFrame(channel_data_dict)

        percentile1_pdf = self.get_percentile(channel_pdf.copy(), full_fs_pdf.copy(), percentile_pdf)

        #TO-ASK: Why is hardcoding? We can parameterize
        low_anchor1, high_anchor1 = self.get_anchor_points(channel_pdf.copy(), percentile1_pdf, low=0.001, high=0.65)

        anchor_pt_df = pd.concat([pd.DataFrame([low_anchor1]), pd.DataFrame([high_anchor1])], axis=1, sort=False)

        anchor_pt_df[anchor_pt_df < 0.1] = 0.1
        anchor_pt_df[anchor_pt_df > 0.85] = 0.85

        # Cross calibrate channels
        low_dict = {}
        high_dict = {}
        for key, value in anchor_pt_df.to_dict('records')[0].items():
            if '_low' in key.lower():
                low_dict[key] = value
            elif '_high' in key.lower():
                high_dict[key] = value

        print('low_dict: ', low_dict)
        print('high_dict: ', high_dict)

        # Sorting low anchor dict
        low_dict_sorted = sorted(low_dict.items(), key=lambda x: x[1])

        # Selecting the thresholds
        extracted_target_channel = (low_dict_sorted[-1][0].replace('_low', ''))
        extracted_low_target = low_dict_sorted[-1][1]
        extracted_high_target = high_dict.get(extracted_target_channel + '_high')

        print('extracted_channel : ', extracted_target_channel)
        print('extracted_low_target : ', extracted_low_target)
        print('extracted_high_target : ', extracted_high_target)

        calib_cols = ['accountId', 'visit_prob', 'send_prob', 'phone_prob', 'virtual_visit_prob', 'all_prob']
        df_calib = full_fs_pdf[calib_cols]

        for channel in Constants.CHANNELS:
            if channel == extracted_target_channel:
                print('channel **:', channel)
                df_calib.copy()[channel.lower() + '_prob_cali'] = df_calib.copy()[channel.lower() + '_prob']
            else:
                low = low_dict.get(channel + '_low')
                high = high_dict.get(channel + '_high')

                print(f'channel ##: {channel} low: {low} high : {high} '
                      f'extracted_low_target : {extracted_low_target} '
                      f'extracted_high_target: {extracted_high_target}')

                df_calib.copy()[channel.lower() + '_prob_cali'] = self.sigmoid_transform(
                    input_array=df_calib.copy()[channel.lower() + '_prob'],
                    low=low,
                    low_target=extracted_low_target,
                    high=high,
                    high_target=extracted_high_target)

        df_calib_op = self.cross_calibration(channel_prob_pdf=df_calib, channels=Constants.CHANNELS,
                                             target_channel=extracted_target_channel,
                                             extracted_high_target=extracted_high_target,
                                             extracted_low_target=extracted_low_target, low_dict=low_dict,
                                             high_dict=high_dict).clip(lower=0)

        # Set the probability to 0 if the model is not available
        for channel in Constants.CHANNELS:
            print(channel)
            if data.get_model(f"{channel}_model") is None:
                print(f"Model for {channel} is not available, unable to run predict.")
                df_calib_op[f'{channel}_prob_cali'] = 0.0

        low_seg_quantile_threshold = data.get_param('Low_segment_1') / 100
        high_seg_quantile_threshold = data.get_param('High_segment_1') / 100

        for channel in Constants.CHANNELS:
            channel_low_seg_prob_threshold = float(df_calib_op[f'{channel}_prob_cali'].quantile([low_seg_quantile_threshold]))
            channel_high_seg_prob_threshold = float(df_calib_op[f'{channel}_prob_cali'].quantile([high_seg_quantile_threshold]))

            data.set_param(f'{channel}_low_anchor_point', channel_low_seg_prob_threshold)
            data.set_param(f'{channel}_high_anchor_point', channel_high_seg_prob_threshold)

            df_calib_op[f'{channel}_prob_cali_segment'] = 'Low'
            df_calib_op[f'{channel}_prob_cali_segment'] = np.where((df_calib_op[f'{channel}_prob_cali'] < channel_low_seg_prob_threshold) | (df_calib_op[f'{channel}_prob_cali'] == 0.0), 'Low', df_calib_op[f'{channel}_prob_cali_segment'])
            df_calib_op[f'{channel}_prob_cali_segment'] = np.where((df_calib_op[f'{channel}_prob_cali'] < channel_high_seg_prob_threshold) & (df_calib_op[f'{channel}_prob_cali'] >= channel_low_seg_prob_threshold), 'Medium', df_calib_op[f'{channel}_prob_cali_segment'])
            df_calib_op[f'{channel}_prob_cali_segment'] = np.where((df_calib_op[f'{channel}_prob_cali'] > channel_high_seg_prob_threshold), 'High', df_calib_op[f'{channel}_prob_cali_segment'])

        data.set_dataframe("full_fs_pdf", full_fs_pdf)
        data.set_dataframe("calibrated_scores_pdf", df_calib_op)

        # Generate XAI
        xai_result_pdf = None
        for channel in Constants.CHANNELS:
            xai_channel_pdf = self.add_xai_for_channel(channel)
            if xai_channel_pdf is not None:
                if xai_result_pdf is None:
                    xai_result_pdf = xai_channel_pdf
                else:
                    xai_result_pdf = xai_result_pdf.append(xai_channel_pdf)

        if xai_result_pdf is not None:
            data.set_dataframe("xai_result_pdf", xai_result_pdf)



