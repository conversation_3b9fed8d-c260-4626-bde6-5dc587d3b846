import numpy as np
import pandas as pd
from scipy.stats import stats

from abstract_model_factory.abstract_learner import Abstract<PERSON><PERSON><PERSON>
from channel_propensity.constants import Constants
from channel_propensity.data import Data
from sklearn.model_selection import train_test_split, StratifiedKFold, RandomizedSearchCV
from xgboost import XGBClassifier

from channel_propensity.qa_data_handler import QADataHandler


class Learner(AbstractLearner):

    @staticmethod
    def prepare_data(data_df, target_col, prob_col):
        bin_label = ['Bin 01', 'Bin 02', 'Bin 03', 'Bin 04', 'Bin 05', 'Bin 06', 'Bin 07', 'Bin 08', 'Bin 09']
        model_output_df = data_df[[target_col, prob_col]].copy()
        model_output_df['bin'] = pd.qcut(model_output_df[prob_col].rank(method='first'), labels=bin_label, q=9)
        #    model_output_df['bin'] = pd.qcut(model_output_df[prob_col], q=9)
        # model_output_df['bin'] = quantile(model_output_df[prob_col], c(0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9, 1), type=1)
        mean_df = model_output_df.groupby(['bin'], as_index=False)[prob_col].mean()
        std = model_output_df.groupby(['bin'])[prob_col].std()
        mean_df['predicted_prob_std'] = std.values
        mean_df.rename(columns={prob_col: 'predicted_prob_mean'}, inplace=True)
        mean_df['predicted_prob_lower'] = mean_df['predicted_prob_mean'] - mean_df['predicted_prob_std']
        mean_df['predicted_prob_upper'] = mean_df['predicted_prob_mean'] + mean_df['predicted_prob_std']
        mean_2_df = model_output_df.groupby(['bin'], as_index=False)[target_col].mean()
        mean_df['actual_prob_mean'] = mean_2_df[target_col].values
        std = model_output_df.groupby(['bin'])[target_col].std()
        mean_df['actual_prob_std'] = std.values
        mean_df['actual_prob_lower'] = mean_df['actual_prob_mean'] - mean_df['actual_prob_std']
        mean_df['actual_prob_upper'] = mean_df['actual_prob_mean'] + mean_df['actual_prob_std']
        mean_df['diff'] = mean_df['actual_prob_mean'] - mean_df['predicted_prob_mean']
        mean_df['pct_diff'] = mean_df['diff'] / mean_df['predicted_prob_mean']
        labels = []
        labels_actual = []
        labels_predicted = []
        for row in mean_df.iterrows():
            labels.append(
                row[1].bin + ' Predicted Mean: ' + str(round(row[1].predicted_prob_mean, 4)) + ', Actual Mean: ' + str(
                    round(row[1].actual_prob_mean, 4)))
            labels_actual.append('Actual Mean: ' + str(round(row[1].actual_prob_mean, 4)))
            labels_predicted.append('Predicted Mean: ' + str(round(row[1].predicted_prob_mean, 4)))
        mean_df['label'] = labels
        mean_df['label_actual'] = labels_actual
        mean_df['label_predicted'] = labels_predicted
        mean_df['label_pos_x'] = -0.05
        mean_df['label_pos_y'] = [1.4 - 0.05 * n for n in range(mean_df.shape[0])]
        return mean_df

    def execute(self):
        print("Executing Learner")

        data = Data.get_instance()
        reduced_fs_pdf = data.get_dataframe("reduced_fs_pdf")
        data.set_param('transform_all', 0)

        for channel in Constants.CHANNELS_ALL:
            print(f" == Processing learn model for {channel} channel")
            target = f'Y_{channel}'
            is_active = data.get_param(f'transform_{channel}', 0)
            prob = f'{channel}_prob'

            print(f"Target: {target} | Prob: {prob} | Channel: {channel} | Active: {is_active}")

            folds = 4
            param_comb = 20 #TODO: Move both of them to constants

            col_removal_list = ['accountId', 'accountUid', 'Y_visit', 'Y_phone', 'Y_virtual_visit', 'Y_send', 'Y_all']
            col_removal_list = [col for col in col_removal_list if col in reduced_fs_pdf.columns]

            if is_active:

                pos_n = reduced_fs_pdf[reduced_fs_pdf[target] == 1][target].count()
                neg_n = reduced_fs_pdf[reduced_fs_pdf[target] == 0][target].count()
                print(f"Negative Target Count: {neg_n} | Positive Target Count: {pos_n}")
                pos_weight = (neg_n / (pos_n + 0.0000001))

                print(f"Learning Rate: 1 | Pos Weight: {pos_weight}")

                X_train, X_test, Y_train, Y_test = train_test_split(reduced_fs_pdf.drop(col_removal_list, axis=1),
                                                                    reduced_fs_pdf[target].astype(int),
                                                                    stratify=reduced_fs_pdf[target],
                                                                    test_size=0.2,
                                                                    random_state=111)
                params = {
                    'eta': [2, 4, 6, 8, 10, 12],
                    'gamma': [0, 0.1],
                    'reg_alpha': [0, 0.1],
                    'scale_pos_weight': [pos_weight, 1],
                    'max_depth': [3, 5, 8],
                    'subsample': [0.8, 1.0]
                }

                xgb = XGBClassifier(learning_rate=0.02, n_estimators=600, objective='binary:logistic')

                skf = StratifiedKFold(n_splits=folds, shuffle=True, random_state=1001)
                random_search = RandomizedSearchCV(xgb, param_distributions=params, n_iter=param_comb,
                                                   scoring='roc_auc', n_jobs=4, cv=skf.split(X_train, Y_train),
                                                   random_state=1001)

                # Here we go
                random_search.fit(X_train, Y_train)
                print('\n All results:')
                print(random_search.cv_results_)
                print('\n Best estimator:')
                print(random_search.best_estimator_)
                print('\n Best AUC score for %d-fold search with %d parameter combinations:' % (folds, param_comb))
                print(random_search.best_score_)
                print('\n Best hyperparameters:')
                print(random_search.best_params_)

                X = reduced_fs_pdf.drop(
                    columns=['accountUid', 'Y_visit', 'Y_virtual_visit', 'Y_send', 'Y_phone', 'accountId', 'Y_all'])
                y = reduced_fs_pdf[target].astype('int')

                # Final model creation
                #########################################################################

                xgb_clf_model = XGBClassifier(max_depth=random_search.best_params_['max_depth'],
                                              gamma=random_search.best_params_['gamma'],
                                              reg_alpha=random_search.best_params_['reg_alpha'],
                                              subsample=random_search.best_params_['subsample'],
                                              eta=random_search.best_params_['eta'],
                                              scale_pos_weight=random_search.best_params_['scale_pos_weight'],
                                              learning_rate=0.02,
                                              n_estimators=600,
                                              objective='binary:logistic')

                xgb_clf_model.fit(X, y)
                y_pred = xgb_clf_model.predict(X)
                y_prob = xgb_clf_model.predict_proba(X)

                feature_imp = (xgb_clf_model.get_booster().get_score(importance_type="gain"))
                hyper_params = random_search.best_params_
                model_performance = random_search.best_score_

                data.set_param(f"{channel}_hyper_params", hyper_params)

                data.set_param(f"{channel}_feature_importance", feature_imp)

                data.set_param(f"{channel}_model_performance", model_performance)

                data.set_model(f"{channel}_model", xgb_clf_model)

                df = pd.DataFrame({'y': y})
                df['prob'] = pd.DataFrame(y_prob)[1]
                input_df = self.prepare_data(df, target_col='y', prob_col='prob')

                slope, intercept, r_value, _, std_err = stats.linregress(list(input_df['predicted_prob_mean']),
                                                                         list(input_df['actual_prob_mean']))
                r_square = (round(np.square(r_value), 3))

                data.set_param(f"{channel}_calibration_r2", r_square)

                # set transform all to 1 if any channel is active
                data.set_param('transform_all', 1)
            else:
                print(f"Skipping {channel} as it is not active.")

    def qa_module(self):
        """
        This method is used to execute the QA module of the learner

        Test_Phase	Test_ID	Test_Description	                            Test_Priority	Test_Action
            Learn	1	Optimal Hyper Parameters for Send Channel	        info	This is for info only.
            Learn	2	Optimal Hyper Parameters for Visit Channel	        info	This is for info only.
            Learn	3	Optimal Hyper Parameters for Virtual Visit Channel	info	This is for info only.
            Learn	4	Optimal Hyper Parameters for Phone Channel	        info	This is for info only.
            Learn	5	Model performance - Send Channel	                info	This is for info only.
            Learn	6	Model performance - Visit Channel	                info	This is for info only.
            Learn	7	Model performance - Virtual Visit Channel	        info	This is for info only.
            Learn	8	Model performance - Phone Channel	                info	This is for info only.
            Learn	9	Model Type for Send Channel	                        info	This is for info only.
            Learn	10	Model Type for Visit Channel	                    info	This is for info only.
            Learn	11	Model Type for Virtual Visit Channel	            info	This is for info only.
            Learn	12	Model Type for Phone Channel	                    info	This is for info only.
            Learn	13	Feature Importance - Send Channel	                info	This is for info only.
            Learn	14	Feature Importance - Visit Channel	                info	This is for info only.
            Learn	15	Feature Importance - Virtual Visit Channel	        info	This is for info only.
            Learn	16	Feature Importance - Phone Channel	                info	This is for info only.
            Learn	17	Internal Calibration R2 - Send Channel	            Medium	Raise A Medium priority ticket.
            Learn	18	Internal Calibration R2 - Visit Channel	            Medium	Raise A Medium priority ticket.
            Learn	19	Internal Calibration R2 - Virtual Visit Channel	    Medium	Raise A Medium priority ticket.
            Learn	20	Internal Calibration R2 - Phone Channel	            Medium	Raise A Medium priority ticket.
        :return:
        """
        print("Executing QA on Learner")
        test_phase = "Learn"
        data = Data.get_instance()

        # Test 1
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=1,
                                             test_value=data.get_param("send_hyper_params"))

        # Test 2
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=2,
                                             test_value=data.get_param("visit_hyper_params"))

        # Test 3
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=3,
                                             test_value=data.get_param("virtual_visit_hyper_params"))

        # Test 4
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=4,
                                             test_value=data.get_param("phone_hyper_params"))

        # Test 5
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=5,
                                             test_value=data.get_param("send_model_performance"))

        # Test 6
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=6,
                                             test_value=data.get_param("visit_model_performance"))

        # Test 7
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=7,
                                             test_value=data.get_param("virtual_visit_model_performance"))

        # Test 8
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=8,
                                             test_value=data.get_param("phone_model_performance"))

        # Test 9
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=9,
                                             test_value=data.get_param("send_model_type"))

        # Test 10
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=10,
                                             test_value=data.get_param("visit_model_type"))

        # Test 11
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=11,
                                             test_value=data.get_param("virtual_visit_model_type"))

        # Test 12
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=12,
                                             test_value=data.get_param("phone_model_type"))

        # Test 13
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=13,
                                             test_value=data.get_param("send_feature_importance"))

        # Test 14
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=14,
                                             test_value=data.get_param("visit_feature_importance"))

        # Test 15
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=15,
                                             test_value=data.get_param("virtual_visit_feature_importance"))

        # Test 16
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=16,
                                             test_value=data.get_param("phone_feature_importance"))

        # Test 17
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=17,
                                             test_value=data.get_param("send_calibration_r2"))

        # Test 18
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=18,
                                             test_value=data.get_param("visit_calibration_r2"))

        # Test 19
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=19,
                                             test_value=data.get_param("virtual_visit_calibration_r2"))

        # Test 20
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=20,
                                             test_value=data.get_param("phone_calibration_r2"))