from datetime import datetime
from datetime import date

import numpy as np
import pandas as pd
from sklearn.metrics import f1_score, precision_recall_curve, roc_auc_score


from abstract_model_factory.abstract_evaluator import AbstractEvaluator
from channel_propensity.constants import Constants
from channel_propensity.data import Data
from channel_propensity.data_access_layer import DataAccessLayer
from channel_propensity.qa_data_handler import QADataHandler


class Evaluator(AbstractEvaluator):

    def __init__(self, name):
        super().__init__(name)
        evaluate_results_pdf = pd.DataFrame(columns=["max_KS", "OP_KS", "Cutoff_Threshold", "F-Measure", "AUC",
                                                          "Channel"])
        data = Data.get_instance()
        data.set_dataframe("evaluate_results_pdf", evaluate_results_pdf)

    def calculate_ks_measures(self, df=None, target=None, prob=None, point=None):
        df['target0'] = 1 - df[target]
        df['bucket'] = pd.qcut(df[prob].rank(method='first'), 20)
        grouped = df.groupby('bucket', as_index=False)
        kstable = pd.DataFrame()
        kstable['min_prob'] = grouped.min()[prob]
        kstable['max_prob'] = grouped.max()[prob]
        kstable['events'] = grouped.sum()[target]
        kstable['nonevents'] = grouped.sum()['target0']
        kstable = kstable.sort_values(by="min_prob", ascending=False).reset_index(drop=True)
        kstable['event_rate'] = (kstable.events / df[target].sum()).apply('{0:.2%}'.format)
        kstable['nonevent_rate'] = (kstable.nonevents / df['target0'].sum()).apply('{0:.2%}'.format)
        kstable['cum_eventrate'] = (kstable.events / df[target].sum()).cumsum()
        kstable['cum_noneventrate'] = (kstable.nonevents / df['target0'].sum()).cumsum()
        kstable['KS'] = abs(np.round(kstable['cum_eventrate'] - kstable['cum_noneventrate'], 3) * 100)

        # Formating
        kstable['cum_eventrate'] = kstable['cum_eventrate'].apply('{0:.2%}'.format)
        kstable['cum_noneventrate'] = kstable['cum_noneventrate'].apply('{0:.2%}'.format)
        kstable.index = reversed(range(1, 21))
        kstable.index.rename('Half_Decile', inplace=True)

        max_ks = round(max(kstable['KS']), 3)
        index = int(point * 0.2)
        ks_at_given_point = round(kstable.at[index, 'KS'], 3)

        return (max_ks, ks_at_given_point)

    @staticmethod
    def calculate_fmeasure_threshold_auc(data, target, prob):

        # Drop na values in column target and prob
        data = data.dropna(subset=[target, prob])

        # Calculate optimal threshold for max f-measure
        precision, recall, thresholds = precision_recall_curve(data[target], data[prob])
        fmeasure = 2 * recall * precision / (recall + precision)
        optimal_threshold = thresholds[np.argmax(fmeasure)]
        max_fmeasure = np.max(fmeasure)

        # Calculate AUC
        try:
            auc_score = roc_auc_score(data[target], data[prob])
        except ValueError:
            print("AUC cannot be calculated for this model")
            auc_score = -999999.0

        return max_fmeasure, optimal_threshold, auc_score
    def evaluate_channel(self, channel):
        print("Evaluating channel: " + channel)
        data = Data.get_instance()
        is_channel_transformed = data.get_param(f'transform_{channel}', 0)
        evaluate_results_pdf = data.get_dataframe("evaluate_results_pdf")

        if is_channel_transformed == 0:

            default_threshold = -999999.0
            default_fmeasure = -999999.0
            default_auc = -999999.0
            default_max_ks = -999999.0
            default_max_ks_operting_point = -999999.0

            evaluate_results_pdf.loc[len(evaluate_results_pdf)] = {"max_KS": default_max_ks,
                                                                   "OP_KS": default_max_ks_operting_point,
                                                                   "Cutoff_Threshold": default_threshold,
                                                                   "F-Measure": default_fmeasure,
                                                                   "AUC": default_auc, "Channel": channel}

            return

        target = "Y_" + channel
        prob_raw = channel + "_prob_raw"
        point = data.get_param('KS_OP_1')
        result_pdf = data.get_dataframe("full_fs_pdf")

        # Drop na values in column Y_channel
        channel_result_pdf = result_pdf.dropna(subset=[target, prob_raw])

        max_ks, ks_at_given_point = self.calculate_ks_measures(df=channel_result_pdf, target=target,
                                                              prob=prob_raw, point=point)

        max_fmeasure, optimal_threshold, auc_score = self.calculate_fmeasure_threshold_auc(data=channel_result_pdf,
                                                                                           target=target,
                                                                                           prob=prob_raw)


        evaluate_results_pdf.loc[len(evaluate_results_pdf)] = {"max_KS": max_ks, "OP_KS": ks_at_given_point,
                                          "Cutoff_Threshold": optimal_threshold, "F-Measure": max_fmeasure,
                                          "AUC": auc_score, "Channel": channel}

    def evaluate_send(self):
        self.evaluate_channel("send")

    def evaluate_visit(self):
        self.evaluate_channel("visit")

    def evaluate_virtual_visit(self):
        self.evaluate_channel("virtual_visit")

    def evaluate_phone(self):
        self.evaluate_channel("phone")

    def qa_module(self):
        """
                Test_Phase	Test_ID	Test_Description	Test_Priority	Test_Action
                Evaluate	1	Send channel AUC performance	High	Raise A High priority ticket.
                Evaluate	2	Visit channel AUC performance	High	Raise A High priority ticket.
                Evaluate	3	Virtual Visit channel AUC performance	High	Raise A High priority ticket.
                Evaluate	4	Phone channel AUC performance	High	Raise A High priority ticket.
                Evaluate	5	Send Max KS Perf.	info	This is for info only.
                Evaluate	6	Visit Max KS Perf.	info	This is for info only.
                Evaluate	7	Virtual Visit Max KS Perf.	info	This is for info only.
                Evaluate	8	Phone Max KS Perf.	info	This is for info only.
                Evaluate	9	Send Max F1 Perf.	info	This is for info only.
                Evaluate	10	Visit Max F1 Perf.	info	This is for info only.
                Evaluate	11	Virtual Visit Max F1 Perf.	info	This is for info only.
                Evaluate	12	Phone Max F1 Perf.	info	This is for info only.
     [NOT USED] Evaluate	13	Time of execution in mili seconds	info	This is for info only.
        :return:
        """
        test_phase = "Evaluate"

        data = Data.get_instance()
        threshold = float(data.get_param('Threshold'))
        evaluate_results_pdf = data.get_dataframe("evaluate_results_pdf")

        # TEST-1: Send channel AUC performance
        send_auc = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'send', 'AUC'].iloc[0]
        test_1_dict = QADataHandler.get_dict_with_test_info(phase=test_phase, test_id=1, test_value=send_auc)
        if send_auc < threshold:
            test_1_dict['Test_Result'] = 'Fail'
        QADataHandler.add_row_qa_logs(test_1_dict)

        # TEST-2: Visit channel AUC performance
        visit_auc = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'visit', 'AUC'].iloc[0]
        test_2_dict = QADataHandler.get_dict_with_test_info(phase=test_phase, test_id=2, test_value=visit_auc)
        if visit_auc < threshold:
            test_2_dict['Test_Result'] = 'Fail'
        QADataHandler.add_row_qa_logs(test_2_dict)

        # TEST-3: Virtual Visit channel AUC performance
        virtual_visit_auc = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'virtual_visit', 'AUC'].iloc[0]
        test_3_dict = QADataHandler.get_dict_with_test_info(phase=test_phase, test_id=3, test_value=virtual_visit_auc)
        if virtual_visit_auc < threshold:
            test_3_dict['Test_Result'] = 'Fail'
        QADataHandler.add_row_qa_logs(test_3_dict)

        # TEST-4: Phone channel AUC performance
        phone_auc = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'phone', 'AUC'].iloc[0]
        test_4_dict = QADataHandler.get_dict_with_test_info(phase=test_phase, test_id=4, test_value=phone_auc)
        if phone_auc < threshold:
            test_4_dict['Test_Result'] = 'Fail'
        QADataHandler.add_row_qa_logs(test_4_dict)

        # TEST-5: Send Max KS Perf.
        send_max_ks = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'send', 'max_KS'].iloc[0]
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=5, test_value=send_max_ks)

        # TEST-6: Visit Max KS Perf.
        visit_max_ks = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'visit', 'max_KS'].iloc[0]
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=6, test_value=visit_max_ks)

        # TEST-7: Virtual Visit Max KS Perf.
        virtual_visit_max_ks = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'virtual_visit', 'max_KS'].iloc[0]
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=7, test_value=virtual_visit_max_ks)

        # TEST-8: Phone Max KS Perf.
        phone_max_ks = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'phone', 'max_KS'].iloc[0]
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=8, test_value=phone_max_ks)

        # TEST-9: Send Max F1 Perf.
        send_max_f1 = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'send', 'F-Measure'].iloc[0]
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=9, test_value=send_max_f1)

        # TEST-10: Visit Max F1 Perf.
        visit_max_f1 = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'visit', 'F-Measure'].iloc[0]
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=10, test_value=visit_max_f1)

        # TEST-11: Virtual Visit Max F1 Perf.
        virtual_visit_max_f1 = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'virtual_visit', 'F-Measure'].iloc[0]
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=11, test_value=virtual_visit_max_f1)

        # TEST-12: Phone Max F1 Perf.
        phone_max_f1 = evaluate_results_pdf.loc[evaluate_results_pdf['Channel'] == 'phone', 'F-Measure'].iloc[0]
        QADataHandler.append_info_in_qa_logs(test_phase=test_phase, test_id=12, test_value=phone_max_f1)


    def execute(self):
        self.evaluate_visit()
        self.evaluate_virtual_visit()
        self.evaluate_send()
        self.evaluate_phone()
