import os
import sys
import traceback

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
model_factory_dir = os.path.dirname(script_dir)
learning_dir = os.path.dirname(model_factory_dir)
sys.path.append(learning_dir)
print ("Adding folder to python path:"+learning_dir)

import common.pyUtils.aktana_ml_utils as mlutils

import common.pyUtils.snowflake_python_base as sfpb
import common.pyUtils.run_sql_on_snowflake as sfsql
import pandas as pd
import numpy as np
import time
import requests
import json
import statsd

CHUNK_SIZE = 3000  # number of rows to write

influenceCols = ['influenceUID', 'influenceValue', 'isVeto', 'accountUid', 'segment', 'repUid',
                            'suggestionCandidateUid',
                            'type', 'products', 'channel', 'startDate', 'endDate', 'configId', 'factorUid', 'useCaseTagId', 'actionTypeId', 'actorTypeId']

def fetch_api_token(TOKEN_URL, secret_info):
    """
    PURPOSE:
        Fetch a api token
    INPUTS:
        TOKEN_URL - the endpoit to get token
        secret_info - API secret_info
    RETURNS:
        Returns API token.
    """
    response = requests.post(TOKEN_URL, json=secret_info)
    return response.text


def get_standard_header(api_token):
    """
    PURPOSE:
        Makes a standard header with baearer auth and app.json type
    INPUTS:
        api_token - the token for API
    RETURNS:
        Returns header using api token.
    """
    return {"Content-Type": "application/json", 'Authorization': 'Bearer {0}'.format(api_token)}


def make_get_call(url, api_token, headers=None):
    """
    PURPOSE:
        Makes a get call with a url and optional custom headers
    INPUTS:
        url - the endpoint
        api_token - the token for API
        headers - standard header
    RETURNS:
        Returns results
    """
    if headers is None:  headers = get_standard_header(api_token)
    response = requests.get(url, headers=headers)
    return response.text


def make_post_call(url, api_token, json_body, headers=None):
    """
    PURPOSE:
        Makes a post call with a url, json body and optional custom headers
    INPUTS:
        url - the endpoint
        api_token - the token for API
        headers - standard header
    RETURNS:
        Returns results
    """
    if headers is None:  headers = get_standard_header(api_token)
    response = requests.post(url, headers=headers, data=json_body)
    return response.text


def make_delete_call(url, api_token, headers=None):
    '''
    Purpose:
        Makes a delete call with a url suffix, json body and optional custom headers
    Inputs:
        url - the endpoint for delete
        api_token - the token for API
        headers - standard header
    Returns:
        API call results
    '''
    if headers is None:  headers = get_standard_header(api_token)
    response = requests.delete(url, headers=headers)
    return response.text

def get_dw_central_prefix(snowflake_params):
    regionDB = snowflake_params.get("snowflake-dbregion", "")
    prefix = f"{regionDB}.DW_CENTRAL_VIEW.VW_"
    if (regionDB == ""):
        print ("No region DB specified in metadata.  Continuing as single-tenant...")
        prefix="DW_CENTRAL."
    print (f"Will read DW_CENTRAL schema with prefix:{prefix}")
    return prefix

def get_cp_output(snowflake_params):
    """
    PURPOSE:
        retrieve post process results for APLV
    INPUTS:
        snowflake parameters to connect
    RETURNS:
        Returns data of label type and value
    """
    spb = sfpb.snowflake_python_base()
    conn = spb.create_connection(snowflake_params)
    sfcur = conn.cursor()

    DW_CENTRAL_PREFIX = get_dw_central_prefix(snowflake_params)

    query = f"""  
        select * from "CHANNEL_PROPENSITY"."CHANNEL_PROPENSITY_OUTPUT_V3" cs
        """

    sfcur.execute(query)
    data = sfcur.fetchall()
    col_names = [i[0] for i in sfcur.description]

    df = pd.DataFrame(data, columns=col_names)
    return (df)


def process_cp_v2_output(result_df):
    """
    PURPOSE:
        process snowflake dataframe into format that can be inserted into DSE
    INPUTS:
        dataframe from snowflake
    RETURNS:
        dataframe to be inserted
    """

    #############

    # add label and description
    result_df["label"] = result_df["labelTypeUID"]
    result_df["description"] = result_df["labelTypeUID"]

    # add accountProductLabelUID
    result_df['accountProductLabelUID'] = result_df['labelTypeUID'] + '~' + \
                                          result_df['accountUID'] + '~~' + result_df['label']

    # order by accountUID, labelTypeUID
    result_df["labelTypeUID"] = pd.Categorical(result_df["labelTypeUID"],
                                               categories=["EmailPropensityScore",
                                                           "VisitPropensityScore",
                                                           "VirtualVisitPropensityScore",
                                                           "SEND_CHANNEL",
                                                           "VISIT_CHANNEL",
                                                           "WEB_INTERACTIVE_CHANNEL",
                                                           "AIRecommendedChannel"],
                                               ordered=True)
    #############

    result_df = result_df.sort_values(["accountUID", "labelTypeUID"])

    #return result_df[result_df.labelTypeUID != 'AIRecommendedChannel'].drop(columns=['recommended_channel_ai_std_akt', 'recommended_channel_value', 'recommended_channel_segment'])
    return result_df[result_df.labelTypeUID != 'AIRecommendedChannel'].drop(columns=['recommended_channel_value', 'recommended_channel_segment'])

def write_to_APLV(url, api_token, chunk_size, df):
    """
    PURPOSE:
        write data into DSE table AccountProductLabelValue
    INPUTS:
        url - the endpoint
        api_token - the token for API
        headers - standard header
    RETURNS:

    """
    batch = df.shape[0] // chunk_size + 1
    print('there are {} batches to write ...'.format(batch))

    # break data into chunks to call DSE API
    # since there is batch size limitation on DSE API
    for chunk in np.array_split(df, batch):
        data = chunk.to_csv(sep='|', index=False)
        resp = make_post_call(url, api_token, data)
        print(resp)

    return (0)

def write_influences_json(df, s3path, s3file):

    try:

        nested_cols = ['influence', 'account', 'rep', 'suggestion', 'channelDetails']
        unnested_cols = ['products', 'channel', 'startDate', 'endDate', 'configId']
        nexted = {
            'influence': ['influenceUID', 'influenceValue', 'isVeto'],
            'account': ['accountUid', 'segment'],
            'rep': ['repUid'],
            'suggestion': ['suggestionCandidateUid', 'type', 'factorUid', 'useCaseTagId'],
            'channelDetails': ['actionTypeId', 'actorTypeId']
        }

        db_cols = set(df.columns)
        extra_cols = db_cols - set(influenceCols)
        if extra_cols:
            print ("Unrecognized/extra columns in input dataframe:" + str(extra_cols))
            print ("Accepted columns are:" + str(influenceCols))

        final_cols = list(set(unnested_cols) & db_cols)
        for groupName in nested_cols:
            nested_cols = set(nexted[groupName])
            result_cols = list(nested_cols & db_cols)
            if result_cols:
                dftemp = df
                if 'repUid' in result_cols:
                    dftemp = df.rename(columns={"repUid": "uid"})
                    result_cols.remove('repUid')
                    result_cols.append('uid')
                if 'accountUid' in result_cols:
                    dftemp = df.rename(columns={"accountUid": "uid"})
                    result_cols.remove('accountUid')
                    result_cols.append('uid')
                df[groupName]  = dftemp[result_cols].to_dict('records')
                final_cols.append(groupName)
        df = df[final_cols]
        print(df.info())
        print (df.head(10))

        #Save output to parquet
        s3path = s3path.replace("s3a://", "s3://", 1) # pandas to_parquet expect s3 url and doesn't support s3a protocol
        print("Writing influence file to " + s3path + s3file + ".parquet")
        df.to_parquet(s3path + s3file + '.parquet', compression='gzip')

    except Exception as e:
        print("Creating influences file failed")
        traceback.print_exc()
        print(e)

def write_dco_influence(ml_utils, customer, env, df):

    adl_params = ml_utils.get_adl_metadata()
    dcoLocation = adl_params.get("adl-dcoS3Location", "")
    if (dcoLocation == ""):
        print("DCO S3 Location not defined in CustomerADLConfig.  Skipping CP output to DCO")
        return 0
    #dcoLocation = adl_params.get("adl-dcoS3Location", "s3://aktana-bdp-" + customer + "/" + env + "/dco/")

    df = df[["accountUID", "labelTypeUID", "value"]]
    df.insert(0,column='influenceUID',value='ChannelPropensity')
    df.rename({'accountUID': 'accountUid', 'value': 'influenceValue', 'labelTypeUID' : 'channel'}, axis=1, inplace=True)

    # remove AI recommended channel and other values except the scores
    df = df[df['channel'].str.contains('Score')]

    # Rename channels to standard names used in DSE
    df.loc[df["channel"] == "EmailPropensityScore", "channel"] = "SEND_CHANNEL"
    df.loc[df["channel"] == "VisitPropensityScore", "channel"] = "VISIT_CHANNEL"
    df.loc[df["channel"] == "VirtualVisitPropensityScore", "channel"] = "WEB_INTERACTIVE_CHANNEL"

    df = df.reset_index()
    df = df.drop(columns=['index'], axis=1)
    s3path = dcoLocation + "data/dco_read_write/bronze/EXTERNAL_INFLUENCES/"
    write_influences_json(df, s3path, "AKTANA_AI_CHANNEL_PROPENSITY/AKTANA_AI_CHANNEL_PROPENSITY")

    return 0

def main():
    start_time = time.time()
    rc = 0
    try:
        ml_utils = mlutils.aktana_ml_utils()

        cmdline_params, metadata_params = ml_utils.initialize(sys.argv, "hc:e:r:", ["customer=","env=","app=","region=","ecosystem="])
        print(metadata_params)
        print(cmdline_params)

        region = cmdline_params['region']
        env = cmdline_params['env']
        customer = cmdline_params['customer']

        snowflake_params = ml_utils.get_snowflake_metadata()
        print(snowflake_params)

        # chunk_size = get_chunk_size(snowflake_params)
        # print('chunk size = {}'.format(chunk_size))

        # Construct base dse api url
        base_url = 'https://{}dse{}.aktana.com/{}/api/v3.0/'.format(region, env, customer)
        print('base_url is:{}'.format(base_url))

        TOKEN_URL = base_url + 'Token/internal'
        APLV_URL = base_url + 'AccountProductLabelValue/batch/insert'

        # Get API secret by quering metadata
        secret = ml_utils.get_api_secret()

        secret_info = {"secret": secret}
        print('secret_info is:{}'.format(secret_info))

        # Fetch API token
        print('Fetching api token from={}'.format(TOKEN_URL))
        api_token = fetch_api_token(TOKEN_URL, secret_info)
        print('length of api_token={}'.format(len(api_token)))
        #print('api_token={}'.format(api_token))

        # Get SCD output data for APLV
        df = get_cp_output(snowflake_params)
        print('number of rows from snowflake = {}'.format(df.shape[0]))
        print(df.head(20))
        print (df['labelTypeUID'].unique())

        # Process CP output for APLV
        result_df = process_cp_v2_output(df)
        print('number of rows after post-processing = {}'.format(result_df.shape[0]))
        print(result_df.head(20))
        print (result_df['labelTypeUID'].unique())


        # delete same scd label data first
        print('build url for delete')

        # Delete API requires labelTypeName, not labelTypeUID. So use the name with spaces
        for l in ["Email Propensity Score", "Visit Propensity Score","Virtual Visit Propensity Score", "AIRecommendedChannel"]:
            print("deleting " + l + '...')
            url_del = base_url + 'AccountProductLabelValue/byLabelType/' + l
            print(url_del)
            res = make_delete_call(url_del, api_token)
            print(res)

        print('finished delete.')

        # change column names to be case sensitive as DSE API requires it
        # df.columns = ['labelTypeUID', 'accountUID', 'productUID', 'label', 'accountProductLabelUID', 'description', 'value']

        # batch insert to DSE table APLV
        print('start batch insert ...')
        # result_df_tmp = result_df.drop(columns=['recommended_channel_ai_std_akt'])
        # result_df_tmp = pd.DataFrame({'labelTypeUID':['EmailChannelPropensityScore'], 'accountUID':['0011Y00002lUXTMQA4'], 'label':['EmailChannelPropensityScore'],
        #                               'accountProductLabelUID':['EmailChannelPropensityScore~0011Y00002lUXTMQA4~~EmailChannelPropensityScore'],
        #                               'description':['EmailChannelPropensityScore'], 'value':[0]})

        write_to_APLV(APLV_URL, api_token, CHUNK_SIZE, result_df)
        print('return from write_to_APLV.')

        write_dco_influence(ml_utils, customer, env, result_df)
    except Exception as e:
        rc = 1
        print(e)

    finally:
        end_time = time.time()
        exec_time = int(end_time - start_time)

        # write observability metrics
        statsd_server = region + "statsd.aktana.com"
        if "eks" not in region:
            statsd_server = region[:2] + "statsd.aktana.com"
        statsd_port = '9125'

        # metric prefix
        metric_prefix = 'type.{TYPE}.cmpny.' + customer + '.regn.' + region + '.cntry.none' + '.env.' + env + \
                        '.apptype.CP' + '.cmpnt.POST_PROC' + '.metric'

        # gauge metrics
        statsd_client = statsd.StatsClient(statsd_server, statsd_port,
                                           prefix=metric_prefix.format(TYPE="g"))
        statsd_client.gauge("job_status", rc)
        print(f"Job status: {rc}. 0: SUCCESS, 1: FAIL")
        statsd_client.gauge("exec_time", exec_time)
        print(f"Execution time: {exec_time}")

        # counter metrics
        statsd_client = statsd.StatsClient(statsd_server, statsd_port,
                                           prefix=metric_prefix.format(TYPE="c"))
        if rc:
            statsd_client.incr("fail_cnt", 1)
            print("Failure count increased by 1")
        else:
            statsd_client.incr("success_cnt", 1)
            print("Success count increased by 1")

if __name__ == "__main__":
    main()
