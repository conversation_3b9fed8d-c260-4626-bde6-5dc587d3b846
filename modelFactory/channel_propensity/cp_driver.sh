#!/bin/bash

# Wrapper/driver script for Channel-propensity jobs
# Can be used to invoke any of the Channel-propensity related jobs

# Usage: cp_driver.sh <job_name> --customer <customer-name> --env <env-name>"
#   Supported job names are: RUN_CP_ENGINE and RUN_CP_POSTPROC and RUN_CP_ENGINE_POSTPROC
#   Sample usage: ./cp_driver.sh RUN_CP_PREPROC --customer genentechca --env preprod"


# RUN_CP_ENGINE - to execute the main Job for CP Engine
# RUN_CP_POSTPROC - to execute the post-processor Job for CP
# RUN_CP_ENGINE_POSTPROC - to execute the main Job for CP Engine and then the post-processor Job for CP


# All jobs take the following command line parameters:
#   --customer custName (name of the customer configured in aktana metadata)
#   --env envName (name of the environment configured in aktana metadata)


echo "Python version : $(python3 --version)"

CP_INSTALL_DIR=`dirname $0`

JOB=$1
shift 1

APP_PARAM="--app CHANNEL_PROPENSITY"

#echo "Install python dependencies from $CP_INSTALL_DIR/requirements.txt"
#pip3 install -r $CP_INSTALL_DIR/requirements.txt

# Print CP Install dir
echo "CP Install directory = $CP_INSTALL_DIR"


# Add parent of current working directory to PYTHONPATH for imports to work
CP_DIR=`readlink -f "$CP_INSTALL_DIR"`
export PYTHONPATH="${PYTHONPATH}:$(dirname "$CP_DIR")"

# Add common python utils to PYTHONPATH used to get parameters from Aktana metadata
export PYTHONPATH="${PYTHONPATH}:$(pwd)/learning/common/pyUtils"
echo "Value in PYTHONPATH=$PYTHONPATH"


case $JOB in

    RUN_CP_ENGINE)
        echo "Running CP Engine job...python3 $CP_INSTALL_DIR/main.py $@ $APP_PARAM"
        python3 $CP_INSTALL_DIR/main.py $@ $APP_PARAM
	    rc=$?
        ;;

    RUN_CP_POSTPROC)
        echo "Running CP Post-processor job...python3 $CP_INSTALL_DIR/cp_post_processor_dse.py $@ $APP_PARAM"
        python3 $CP_INSTALL_DIR/cp_post_processor_dse.py $@ $APP_PARAM
	    rc=$?
        ;;

    RUN_CP_ENGINE_POSTPROC)
        echo "Running CP Engine job...python3 $CP_INSTALL_DIR/main.py $@ $APP_PARAM"
        python3 $CP_INSTALL_DIR/main.py $@ $APP_PARAM
	    rc=$?

        if [ $rc -eq 0 ]; then
        echo "Running CP Post-processor job...python3 $CP_INSTALL_DIR/cp_post_processor_dse.py $@ $APP_PARAM"
        python3 $CP_INSTALL_DIR/cp_post_processor_dse.py $@ $APP_PARAM
        rc=$?
        fi

        ;;

    *)
        echo "Usage: cp_driver.sh <job_name> --customer <customer-name> --env <env-name>"
        echo "  Supported job names are: RUN_CP_ENGINE, RUN_CP_POSTPROC or RUN_CP_ENGINE_POSTPROC"
        echo "  Sample usage: ./cp_driver.sh RUN_CP_ENGINE --customer pfizerbdpus --env dev"
	rc=1
        ;;

esac
echo "Returning from cp driver with rc=$rc"
exit $rc

