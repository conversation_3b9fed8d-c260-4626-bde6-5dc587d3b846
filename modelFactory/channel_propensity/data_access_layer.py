import os
import pickle

import pandas as pd
import s3fs
import sklearn2pmml

from channel_propensity import data
from channel_propensity.data import Data
from channel_propensity.constants import Constants
from snowflake.connector.pandas_tools import write_pandas
from datetime import date, datetime
import boto3
import snowflake
from sklearn_pmml_model.ensemble import PMMLGradientBoostingClassifier

class DataAccessLayer:

    @staticmethod
    def get_snowflake_cursor():

        ctx = snowflake.connector.connect(
            user=Constants.sw_user,
            password=Constants.sw_password,
            account=Constants.sw_account,
            warehouse=Constants.sw_warehouse,
            database=Constants.sw_database,
            schema=Constants.sw_schema,
            role=Constants.sw_role)

        # Create a cursor object.
        cur = ctx.cursor()
        return cur, ctx

    @staticmethod
    def read_snow_flake_pandas(query):
        """
        @param spark:
        @param query:
        @return:  Spark dataframe
        @rtype:
        """

        cur, ctx = DataAccessLayer.get_snowflake_cursor()
        cur.execute(query)
        pdf = cur.fetch_pandas_all()
        return pdf

    @staticmethod
    def write_snow_flake_pandas(pdf, table, mode_u="append"):
        try:
            cur, ctx = DataAccessLayer.get_snowflake_cursor()
            write_pandas(ctx, pdf, table)
            print(f"Done writing snowflake table {table}")
        except Exception as e:
            print('Failed with error: ' + str(e))
            raise e

    @staticmethod
    def execute_snowflake_update_query(query):
        try:
            cur, ctx = DataAccessLayer.get_snowflake_cursor()
            cur.execute(query)
            print(f"Done executing snowflake query {query}")
        except Exception as e:
            print('Failed with error: ' + str(e))
            raise e

    @staticmethod
    def write_model_pmml_to_s3(model, s3_path, target_name):
        """
        Method to write a python object to S3
        @param object: Python object
        @param s3_path: S3 path
        @return: None
        """

        ACCESS_KEY = Constants.AWS_ACCESS_KEY_ID
        SECRET_KEY = Constants.AWS_SECRET_ACCESS_KEY
        SESSION_TOKEN = Constants.AWS_SESSION_TOKEN

        s3_bucket = s3_path.split('/')[2]
        s3_path = '/'.join(s3_path.split('/')[3:])

        if ACCESS_KEY != "":
            s3 = boto3.client('s3',
            aws_access_key_id=ACCESS_KEY,
            aws_secret_access_key=SECRET_KEY,
            aws_session_token=SESSION_TOKEN)
        else:
            s3 = boto3.client('s3')

        pipeline = sklearn2pmml.make_pmml_pipeline(model,
                                                   active_fields=model.get_booster().feature_names,
                                                   target_fields=[target_name])

        sklearn2pmml.sklearn2pmml(pipeline=pipeline,
                                  pmml='channel_model.pmml',
                                  with_repr=True)

        s3.upload_file('channel_model.pmml', s3_bucket, s3_path)

        os.remove('channel_model.pmml')

    @staticmethod
    def write_object_pkl_to_s3(object, s3_path):
        """
        Method to write a python object to S3
        @param object: Python object
        @param s3_path: S3 path
        @return: None
        """

        ACCESS_KEY = Constants.AWS_ACCESS_KEY_ID
        SECRET_KEY = Constants.AWS_SECRET_ACCESS_KEY
        SESSION_TOKEN = Constants.AWS_SESSION_TOKEN

        s3_bucket = s3_path.split('/')[2]
        s3_path = '/'.join(s3_path.split('/')[3:])

        if ACCESS_KEY != "":
            s3 = boto3.resource('s3',
                                aws_access_key_id=ACCESS_KEY,
                                aws_secret_access_key=SECRET_KEY,
                                aws_session_token=SESSION_TOKEN)
        else:
            s3 = boto3.resource('s3')

        content = pickle.dumps(object)
        s3.Object(s3_bucket, s3_path).put(Body=content)

    @staticmethod
    def add_metadata_to_pdf(pdf: pd.DataFrame):
        """"
        Method to add metadata to the dataframe. Which includes Model_Name, Process_Name, Date, Timestamp, Run_Uid
        parameters from the data object.

        @param pdf: Pandas DataFrame
        @return: Dataframe with metadata
        """
        assert isinstance(pdf, pd.DataFrame), "Invalid type of DataFrame"

        data = Data.get_instance()
        # Set Model_Name, Process_Name, Date, Timestamp, Run_Uid in the dataframe
        pdf['Model_Name'] = data.get_param('Model_Name')
        pdf['Process_Name'] = data.get_param('Process_Name')
        pdf['Date'] = date.today()
        pdf['Timestamp'] = datetime.now()
        pdf['Timestamp'] = pdf['Timestamp'].dt.tz_localize('UTC')
        pdf['Run_Uid'] = Constants.RUN_UID
        return pdf

    @staticmethod
    def read_model_from_s3(s3_path):
        """
        Method to read a python object from S3
        @param s3_path: S3 path
        @return: Python object
        """

        ACCESS_KEY = Constants.AWS_ACCESS_KEY_ID
        SECRET_KEY = Constants.AWS_SECRET_ACCESS_KEY
        SESSION_TOKEN = Constants.AWS_SESSION_TOKEN

        s3_bucket = s3_path.split('/')[2]
        s3_path = '/'.join(s3_path.split('/')[3:])

        if ACCESS_KEY != "":
            s3 = boto3.client('s3',
            aws_access_key_id=ACCESS_KEY,
            aws_secret_access_key=SECRET_KEY,
            aws_session_token=SESSION_TOKEN)
        else:
            s3 = boto3.client('s3')

        s3.download_file(s3_bucket, s3_path, 'channel_model.pmml')
        model = PMMLGradientBoostingClassifier(pmml='channel_model.pmml')

        os.remove('channel_model.pmml')
        return model

    @staticmethod
    def read_model_pkl_from_s3(s3_path):
        """
        Method to read a python object from S3
        @param s3_path: S3 path
        @return: Python object
        """
        try:
            ACCESS_KEY = Constants.AWS_ACCESS_KEY_ID
            SECRET_KEY = Constants.AWS_SECRET_ACCESS_KEY
            SESSION_TOKEN = Constants.AWS_SESSION_TOKEN

            s3_bucket = s3_path.split('/')[2]
            s3_path = '/'.join(s3_path.split('/')[3:])

            if ACCESS_KEY != "":
                s3 = boto3.resource('s3',
                aws_access_key_id=ACCESS_KEY,
                aws_secret_access_key=SECRET_KEY,
                aws_session_token=SESSION_TOKEN)
            else:
                s3 = boto3.resource('s3')

            content = s3.Object(s3_bucket, s3_path).get()['Body'].read()
            return pickle.loads(content)
        except Exception as e:
            print('Failed with error: ' + str(e))
            print(f"Unable to read model from S3 {s3_path}")
            return None
