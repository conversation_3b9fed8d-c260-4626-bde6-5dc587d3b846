import random
from datetime import datetime

import findspark
import pandas as pd
pd.options.mode.chained_assignment = None  # default='warn'
import numpy as np
import pyspark
from pyspark.sql import SparkSession

from abstract_model_factory.abstract_loader import AbstractLoader
from customer_journey.data import Data
from customer_journey.data_access_layer import DataAccessLayer
from qa_data_handler import QADataHandler
# import constants
from customer_journey.constants import Constants
from athena_reader import AthenaReader
import os


def scale_influence_value(ser, lower_bound, upper_bound):
    return lower_bound + (upper_bound - lower_bound) * (ser - ser.min()) / (ser.max() - ser.min())


class Loader(AbstractLoader):

    def printSummary(self, name, df, rowCount=10):

        print(f"Data frame: {name}:" + str(df.shape))
        print("*******************************")
        if Constants.DEBUG_MODE:
            df.info()
            print(df.head(rowCount))

    def qa_module(self):
        pass

    def read_segment_weights(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")

        sql = f'''select productId, configValue as weight 
                  from {params['rds-learningdbname']}.CJ_Product_Config 
                  where configName = 'COMBINED_VALUE_POTENTIAL_WEIGHT' '''
        df = DataAccessLayer.read_rds_table_as_pandas(sql)
        df['weight'] = df['weight'].astype(float)

        data.set_dataframe("segment_weights_df", df)
        self.printSummary("segment_weights_df", df)

    def read_cri_score(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        runmonth = data.get_param("runmonth")

        reader = AthenaReader()
        reader.connect(aws_access_key=params.get("athena-username"), aws_secret_key=params.get("athena-password"),
                       session_token=params.get('session_token'), aws_region=params.get("athena-region"),
                       athena_staging_bucket=params.get("athena-s3bucket"),
                       athena_staging_dir=params.get("athena-stagedir"), schema=params.get("athena-schema"))

        athena_query = f"""SELECT accountid accountId, s.productid productId, 
                                 yearMonth, index_score as cri
                          FROM cri_score s
                          where yearMonth < '{runmonth}'"""
        try:
            df = reader.query(query=athena_query)
        except Exception as e:
            print(e)
            print("Error in reading cri data, set SKIP_CRI = True")
            Constants.SKIP_CRI = True
            Constants.SKIP_POTENTIAL = True
            return
        df['yearMonth'] = df['yearMonth'].astype(str)

        data.set_dataframe("cri_df", df)
        self.printSummary("cri_df", df)

    def get_segment_rank_mapping(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        sql = f'''select segmentType, segment, segmentRank from {params['rds-learningdbname']}.CJ_Segment_Rank_Mapping'''
        df = DataAccessLayer.read_rds_table_as_pandas(sql)
        df = df.rename(columns={'segmentRank': 'rank'})
        df['rank'] = df['rank'].astype(int)
        segment_rank_mapping = {}
        for i, row in df.iterrows():
            if row['segmentType'] not in segment_rank_mapping:
                segment_rank_mapping[row['segmentType']] = {}
            segment_rank_mapping[row['segmentType']][row['segment']] = row['rank']

        data.set_param("segment_rank_mapping", segment_rank_mapping)

    def get_combined_segment_mapping(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        sql = f'''select salesSegment sales_segment, potentialSegment potential_segment, 
                combinedSegment combined_segment, combinedSegmentRank combined_rank 
                from {params['rds-learningdbname']}.CJ_Combined_Segment_Mapping'''
        df = DataAccessLayer.read_rds_table_as_pandas(sql)

        data.set_dataframe("combined_segment_mapping_df", df)

    def get_test_dfs(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        sql = f'''select distinct accountId from {params['rds-enginedbname']}.SparkDSERunRepDateSuggestion s join {params['rds-enginedbname']}.SparkDSERunRepDate d
        on s.runRepDateId = d.runRepDateId where d.suggestedDate >= "2023-07-01"'''
        df = DataAccessLayer.read_rds_table_as_pandas(sql)
        data.set_dataframe("dse_accts", df)

        sql = f'''select repId, accountId from {params['rds-enginedbname']}.RepAccountAssignment'''
        df = DataAccessLayer.read_rds_table_as_pandas(sql)
        data.set_dataframe("rep_acct_assignment", df)

    def get_acct_specialty(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        reader = AthenaReader()
        reader.connect(aws_access_key=params.get("athena-username"), aws_secret_key=params.get("athena-password"),
                       session_token=params.get('session_token'), aws_region=params.get("athena-region"),
                       athena_staging_bucket=params.get("athena-s3bucket"),
                       athena_staging_dir=params.get("athena-stagedir"), schema=params.get("athena-schema"))
        athena_query = f'''select accountid accountId, specialties_std_akt specialty from account_dse_v'''
        df = reader.query(query=athena_query)

        data.set_dataframe("acct_spec_df", df)

    def get_custom_influence_views(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")

        sql = f'''select influenceType, minInfluenceValue, maxInfluenceValue, viewName from {params['rds-learningdbname']}.CJ_Influences where isActive = 1'''
        df = DataAccessLayer.read_rds_table_as_pandas(sql)

        view_list = []
        custom_influence_views = {}
        influence_params = {}
        for i, row in df.iterrows():
            view_list.append(row['viewName'])
            if row['influenceType'] not in custom_influence_views:
                custom_influence_views[row['influenceType']] = []
            custom_influence_views[row['influenceType']].append(row['viewName'])
            influence_params[row['viewName']] = (row['minInfluenceValue'], row['maxInfluenceValue'])

        data.set_param("custom_influences", custom_influence_views)

        for view in view_list:
            print("Reading view: " + view)
            sql = f'''select a.accountId, p.productId, influenceValue, 
            date_format(startDate, "%Y-%m") startMonth,  date_format(endDate, "%Y-%m") endMonth from {params['rds-stagedbname']}.{view} v
            join {params['rds-enginedbname']}.Account a on a.externalId = v.accountUID
            join {params['rds-enginedbname']}.Product p on p.externalId = v.productUID'''
            df = DataAccessLayer.read_rds_table_as_pandas(sql)
            if influence_params[view] != (0, 0):
                df['influenceValue'] = scale_influence_value(df['influenceValue'], influence_params[view][0],
                                                             influence_params[view][1])

            data.set_dataframe(view, df)

    def get_sales_unit_by_product(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        sql = f'''select productId, configValue as datapointunit from {params['rds-learningdbname']}.CJ_Product_Config where configName = 'SALES_UNIT' '''
        df = DataAccessLayer.read_rds_table_as_pandas(sql)
        data.set_dataframe("sales_unit_by_product", df)

    def read_sales_from_aggregated_view(self, n_months=3):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        runmonth = data.get_param("runmonth")
        sales_unit_by_product = data.get_dataframe("sales_unit_by_product")

        reader = AthenaReader()
        reader.connect(aws_access_key=params.get("athena-username"), aws_secret_key=params.get("athena-password"),
                       session_token=params.get('session_token'), aws_region=params.get("athena-region"),
                       athena_staging_bucket=params.get("athena-s3bucket"),
                       athena_staging_dir=params.get("athena-stagedir"), schema=params.get("athena-schema"))
        athena_query = f'''select a.accountid accountId, v.accountuid accountUID, p.productid productId, v.productuid productUID, 
                            date_format(salesdate, '%Y-%m') yearMonth, 
                            datapointunit, datapointvalue actual_sales, basketvolume basket_value,
                            iscompetitor, market_short_name 
                            from kpi_aggregated_salesdata_v v 
                            left join account_dse_v a on a.externalId = v.accountuid 
                            join product_v p on p.externalid = v.productuid
                            where salesdate between date_add('month', -12, date_parse('{runmonth}', '%Y-%m')) 
                            and date_add('month', -1, date_parse('{runmonth}', '%Y-%m')) 
                        '''
        df_child_prod = reader.query(query=athena_query)

        competitor_query = '''
                            with t as (
                                select c.*, p.productId as productId, p.iscompetitor as iscompetitor from kpi_marketbasket_salesdata_v c
                                join product_v p on c.product_identifier = p.externalId
                                )
                                
                                select c.datapointunit, c.productID as productId, nc.product_identifier as own_productUID, nc.productId as own_productId
                                from t c
                                join t nc on c.datapointunit = nc.datapointunit
                                and c.market_identifier = nc.market_identifier
                                and c.iscompetitor = True and nc.iscompetitor = False
                            '''

        competitor_mapping = reader.query(query=competitor_query)
        competitor_mapping = competitor_mapping.drop_duplicates(subset=['datapointunit', 'productId'])

        # process competitor data
        df_competitor = df_child_prod[df_child_prod['iscompetitor'] == True]
        df_own = df_child_prod[df_child_prod['iscompetitor'] == False]

        acct_basket_own = df_own[['accountId', 'market_short_name']].drop_duplicates()
        df_competitor = pd.merge(df_competitor, acct_basket_own, on=['accountId', 'market_short_name'], how='left', indicator=True)
        df_competitor = df_competitor[df_competitor['_merge'] == 'left_only'].drop('_merge', axis=1)
        df_competitor = pd.merge(df_competitor, competitor_mapping, on=['datapointunit', 'productId'], how='inner')

        df_competitor['productId'] = df_competitor['own_productId']
        df_competitor['productUID'] = df_competitor['own_productUID']
        df_competitor['actual_sales'] = 0
        df_competitor.drop(['own_productId', 'own_productUID'], axis=1, inplace=True)
        df_competitor = df_competitor.drop_duplicates()

        df_child_prod = pd.concat([df_own, df_competitor], ignore_index=True)
        df_child_prod.drop(['iscompetitor'], axis=1, inplace=True)
        df = df_child_prod

        # find product group mapping
        product_group_query = f'''select productId, parentProductId, parentProductUID
                                from {params['rds-learningdbname']}.Product_Group_Mapping_V
                               '''
        
        product_group_mapping = DataAccessLayer.read_rds_table_as_pandas(product_group_query)
        if product_group_mapping.parentProductId.nunique() < product_group_mapping.shape[0]:
            print('Enable product group mapping, compute parent product level sales')
            df_parent_prod = pd.merge(df_child_prod, product_group_mapping, on='productId', how='left')
            df_parent_prod['productId'] = df_parent_prod['parentProductId']
            df_parent_prod['productUID'] = df_parent_prod['parentProductUID']
            df_parent_prod.drop(['parentProductId', 'parentProductUID'], axis=1, inplace=True)
            df_parent_prod = df_parent_prod.groupby(['accountId', 'accountUID', 'productId', 'productUID', 'yearMonth', 'datapointunit']).agg(
                {'actual_sales': 'sum', 'basket_value': 'max'}).reset_index()

            df = pd.concat([df_child_prod, df_parent_prod], ignore_index=True)

        # dedup by ['accountId', 'productId', 'yearMonth', 'datapointunit', 'datapointtype']
        df = df.drop_duplicates(subset=['accountId', 'accountUID', 'productId', 'productUID', 'yearMonth', 'datapointunit'])

        # filter by data point unit
        merged = pd.merge(df, sales_unit_by_product, on='productId', how='left', suffixes=('', '_y'))
        df_filtered = merged[
            ((merged['datapointunit_y'].notnull()) & (merged['datapointunit'] == merged['datapointunit_y'])) |
            ((merged['datapointunit_y'].isnull()) & (merged['datapointunit'] == 'Sales_Amount'))]

        if len(df_filtered[df_filtered['actual_sales'].notnull()]) == 0:
            Constants.SKIP_SALES = True
            Constants.SKIP_POTENTIAL = True
            print("No sales data, set SKIP_SALES = True, SKIP_POTENTIAL = True")
            return

        # interpolate/extrapolate sales
        df_filtered['yearMonth_dt'] = pd.to_datetime(df['yearMonth'], format='%Y-%m')
        min_max_month_by_prod = df_filtered.groupby('productId').agg({'yearMonth_dt': ['min', 'max']}).reset_index()
        min_max_month_by_prod.columns = ['productId', 'min_month', 'max_month']
        all_months = pd.concat([pd.DataFrame(
            {'productId': [row['productId']] * len(pd.date_range(start=row['min_month'], end=row['max_month'], freq='MS')),
             'yearMonth': pd.date_range(start=row['min_month'], end=row['max_month'], freq='MS')})
                                for _, row in min_max_month_by_prod.iterrows()
                                ])
        acct_prod = df_filtered[['accountUID', 'productId', 'datapointunit']].drop_duplicates()
        all_months = pd.merge(all_months, acct_prod, on='productId', how='left')
        all_months['yearMonth'] = all_months['yearMonth'].dt.strftime('%Y-%m')
        acct_id_uid = df_filtered[['accountId', 'accountUID']].drop_duplicates()
        df_filtered = pd.merge(df_filtered, all_months, on=['accountUID', 'productId', 'yearMonth', 'datapointunit'], how='right')
        df_filtered = pd.merge(df_filtered, acct_id_uid, on='accountUID', how='left', suffixes=('_y', ''))
        df_filtered['actual_sales'] = df_filtered['actual_sales'].fillna(0)
        df_filtered['basket_value'] = df_filtered['basket_value'].fillna(0)
        # df_filtered['datapointvalue'] = df_filtered.loc[df_filtered['datapointtype'] == 'Vol', 'datapointvalue'].fillna(0)

        # process sales
        sales_df = df_filtered[['accountId', 'accountUID', 'productId', 'productUID', 'yearMonth', 'actual_sales', 'basket_value']]
        sales_df['sales'] = sales_df['actual_sales']
        sales_df.sort_values(['accountUID', 'productId', 'yearMonth'], inplace=True)
        sales_df['sales'] = np.around(sales_df.groupby(['accountUID', 'productId'])['sales']
                                      .rolling(n_months).mean().reset_index([0, 1], drop=True), decimals=5)

        sales_df['sales'] = sales_df['sales'].combine_first(sales_df['actual_sales'])
        data.set_dataframe("sales_df", sales_df)
        self.printSummary("sales_basket", sales_df)

        # process basket
        if len(sales_df[sales_df['basket_value'].notnull()]) == 0:
            Constants.SKIP_POTENTIAL = True
            print("No basket data, set SKIP_POTENTIAL = True")
            return

    def read_sales_attribution_mappings(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")

        # process account group mapping
        sql = f''' select aga.* from {params['rds-learningdbname']}.CJ_Account_Group_Attributes_v aga'''
        acct_group_df = DataAccessLayer.read_rds_table_as_pandas(sql)
        acct_group_df = acct_group_df.drop(columns=['accountUID', 'productUID'])
        group_counts = acct_group_df.groupby(['accountGroup'])['accountId'].nunique()
        # if there are groups with more than 1 account, enable sales attribution, otherwise skip
        if group_counts[group_counts > 1].shape[0] > 0:
            Constants.ENABLE_SALES_ATTRIBUTION = True
            print("There are groups with more than 1 account, set ENABLE_SALES_ATTRIBUTION = True")
        else:
            return
        acct_group_melted_df = acct_group_df.melt(id_vars=['accountId', 'accountGroup', 'productId'], var_name='dimension', value_name='dimensionValue')
        data.set_dataframe("acct_group_df", acct_group_melted_df)

        # process attribution share mapping
        sql = f''' select p.productId, s.* from {params['rds-learningdbname']}.CJ_Attribution_Share_Mapping s
         join {params['rds-enginedbname']}.Product p on p.externalId = s.productUID '''
        attr_share_df = DataAccessLayer.read_rds_table_as_pandas(sql)
        data.set_dataframe("attr_share_df", attr_share_df)
        if 'CRI' in attr_share_df['dimension'].values:
            Constants.SA_CRI_ADJUSTMENT = True

        # read SA threshold
        sql = f'''select configName, configValue from {params['rds-learningdbname']}.DRL_Config_Params 
        where versionId = '__DEFAULT__' and configName IN ('SA_PERCENTILE_THRES', 'SA_VALUE_THRES') '''
        sa_thres_df = DataAccessLayer.read_rds_table_as_pandas(sql)
        percentile_thres = float(sa_thres_df[sa_thres_df['configName'] == 'SA_PERCENTILE_THRES']['configValue'].values[0])
        value_thres = float(sa_thres_df[sa_thres_df['configName'] == 'SA_VALUE_THRES']['configValue'].values[0])
        data.set_param("percentile_thres", percentile_thres)
        data.set_param("value_thres", value_thres)


    def execute(self):
        self.get_sales_unit_by_product()
        self.read_sales_from_aggregated_view()
        if not Constants.SKIP_POTENTIAL:
            self.read_segment_weights()
            self.get_combined_segment_mapping()
        self.read_cri_score()
        self.get_segment_rank_mapping()
        self.get_acct_specialty()
        self.get_custom_influence_views()
        self.read_sales_attribution_mappings()

        # self.get_test_dfs()
