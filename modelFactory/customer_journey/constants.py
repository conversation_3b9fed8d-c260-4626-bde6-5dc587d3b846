from abstract_model_factory.abstract_constants import AbstractConstants


class Constants(AbstractConstants):
    FIVE_SEG_DICT = {'low_low': 'E',
                     'low_medium': 'D',
                     'low_high': 'C',
                     'medium_low': 'D',
                     'medium_medium': 'C',
                     'medium_high': 'B',
                     'high_low': 'C',
                     'high_medium': 'B',
                     'high_high': 'A'}

    FIVE_TO_INT = {'A': 5,
                   'B': 4,
                   'C': 3,
                   'D': 2,
                   'E': 1}

    SPARK_APP_NAME = "customer_journey"

    SLOPE_ROLLING_WINDOW_SIZE = 6

    SKIP_SALES = False
    SKIP_POTENTIAL = False
    SKIP_CRI = False

    # sales attribution related
    ENABLE_SALES_ATTRIBUTION = False
    SA_CRI_ADJUSTMENT = False
