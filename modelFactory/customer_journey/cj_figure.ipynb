{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:27.940937Z", "start_time": "2024-06-06T20:05:25.296687Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from scipy import stats\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "d6a1fcb47dcbe118", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:28.466853Z", "start_time": "2024-06-06T20:05:27.940076Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape:  (2191356, 11)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>productId</th>\n", "      <th>yearMonth</th>\n", "      <th>segmentType</th>\n", "      <th>value</th>\n", "      <th>segment</th>\n", "      <th>segmentRank</th>\n", "      <th>rankChange</th>\n", "      <th>slope</th>\n", "      <th>trend</th>\n", "      <th>attribute</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1008</td>\n", "      <td>1012</td>\n", "      <td>2023-01</td>\n", "      <td>CRI</td>\n", "      <td>0.031858</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1008</td>\n", "      <td>1012</td>\n", "      <td>2023-02</td>\n", "      <td>CRI</td>\n", "      <td>0.027117</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1008</td>\n", "      <td>1012</td>\n", "      <td>2023-03</td>\n", "      <td>CRI</td>\n", "      <td>0.023106</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>-0.038479</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1008</td>\n", "      <td>1012</td>\n", "      <td>2023-04</td>\n", "      <td>CRI</td>\n", "      <td>0.019667</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>-0.032753</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1008</td>\n", "      <td>1012</td>\n", "      <td>2023-05</td>\n", "      <td>CRI</td>\n", "      <td>0.016687</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>-0.028218</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   accountId  productId yearMonth segmentType     value segment  segmentRank  \\\n", "0       1008       1012   2023-01         CRI  0.031858     Low            1   \n", "1       1008       1012   2023-02         CRI  0.027117     Low            1   \n", "2       1008       1012   2023-03         CRI  0.023106     Low            1   \n", "3       1008       1012   2023-04         CRI  0.019667     Low            1   \n", "4       1008       1012   2023-05         CRI  0.016687     Low            1   \n", "\n", "   rankChange     slope trend  attribute  \n", "0           0  0.000000  Hold        NaN  \n", "1           0  0.000000  Hold        NaN  \n", "2           0 -0.038479  Hold        NaN  \n", "3           0 -0.032753  Hold        NaN  \n", "4           0 -0.028218  Hold        NaN  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# please fill in the parquet file path\n", "cj_parquet_path = ''\n", "df = pd.read_parquet(cj_parquet_path)\n", "print(\"Shape: \", df.shape)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "bd1f845a54a8897c", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:28.791712Z", "start_time": "2024-06-06T20:05:28.463724Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape:  (62040, 11)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>productId</th>\n", "      <th>yearMonth</th>\n", "      <th>segmentType</th>\n", "      <th>value</th>\n", "      <th>segment</th>\n", "      <th>segmentRank</th>\n", "      <th>rankChange</th>\n", "      <th>slope</th>\n", "      <th>trend</th>\n", "      <th>attribute</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2067276</th>\n", "      <td>3960</td>\n", "      <td>1016</td>\n", "      <td>2023-03</td>\n", "      <td>Potential</td>\n", "      <td>0.746957</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>2.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2067277</th>\n", "      <td>3960</td>\n", "      <td>1016</td>\n", "      <td>2023-04</td>\n", "      <td>Potential</td>\n", "      <td>1.102485</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>3.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2067278</th>\n", "      <td>3960</td>\n", "      <td>1016</td>\n", "      <td>2023-05</td>\n", "      <td>Potential</td>\n", "      <td>0.614652</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>-0.071431</td>\n", "      <td>Hold</td>\n", "      <td>1.66666</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2067279</th>\n", "      <td>3960</td>\n", "      <td>1016</td>\n", "      <td>2023-06</td>\n", "      <td>Potential</td>\n", "      <td>0.732592</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>-0.199704</td>\n", "      <td>Hold</td>\n", "      <td>2.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2067280</th>\n", "      <td>3960</td>\n", "      <td>1016</td>\n", "      <td>2023-07</td>\n", "      <td>Potential</td>\n", "      <td>0.849795</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.126953</td>\n", "      <td>Hold</td>\n", "      <td>2.33333</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         accountId  productId yearMonth segmentType     value segment  \\\n", "2067276       3960       1016   2023-03   Potential  0.746957     Low   \n", "2067277       3960       1016   2023-04   Potential  1.102485     Low   \n", "2067278       3960       1016   2023-05   Potential  0.614652     Low   \n", "2067279       3960       1016   2023-06   Potential  0.732592     Low   \n", "2067280       3960       1016   2023-07   Potential  0.849795     Low   \n", "\n", "         segmentRank  rankChange     slope trend  attribute  \n", "2067276            1           0  0.000000  Hold    2.00000  \n", "2067277            1           0  0.000000  Hold    3.00000  \n", "2067278            1           0 -0.071431  Hold    1.66666  \n", "2067279            1           0 -0.199704  Hold    2.00000  \n", "2067280            1           0  0.126953  Hold    2.33333  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# filter CJ output by Brand and SegmentType\n", "productId = 1016 # Ocrevus\n", "segmentType = \"Potential\" # Options: {'CRI', 'Sales', 'Potential', 'Combined'}\n", "df = df[(df.productId == productId) & (df.segmentType == segmentType)]\n", "print(\"Shape: \", df.shape)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "1ca71d03417a0015", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:28.793021Z", "start_time": "2024-06-06T20:05:28.651709Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Range:  2023-03  -  2024-02\n"]}], "source": ["min_month, max_month = df['yearMonth'].min(), df['yearMonth'].max()\n", "print(\"Range: \", min_month, \" - \", max_month)"]}, {"cell_type": "code", "execution_count": 5, "id": "81029bcc5f7082c2", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:28.861144Z", "start_time": "2024-06-06T20:05:28.658900Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# of rows with non-zero upper bound: 44252\n", "# of accounts with non-zero upper bound: 4406\n"]}], "source": ["if segmentType == 'Potential':\n", "    print(f\"# of rows with non-zero upper bound: {df[df.attribute > 0].shape[0]}\")\n", "    print(f\"# of accounts with non-zero upper bound: {df[df.attribute > 0].accountId.nunique()}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "c6afe28e849bc707", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:28.958255Z", "start_time": "2024-06-06T20:05:28.675960Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["Low       55921\n", "Medium     3100\n", "High       3019\n", "Name: segment, dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["segment_row_counts = df['segment'].value_counts()\n", "segment_row_counts"]}, {"cell_type": "code", "execution_count": 7, "id": "cf59bc2b8438a574", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:29.099461Z", "start_time": "2024-06-06T20:05:28.686451Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.pie(segment_row_counts, labels=segment_row_counts.index, autopct='%1.1f%%')\n", "plt.title(f'CJ {segmentType} Segmentation Distribution for Product {productId}')\n", "# plt.legend(['L', 'M', 'H'], title='Segmentation')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "id": "9492d4da-d123-4ff7-844e-1ec5075e25f4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>yearMonth</th>\n", "      <th>Variable</th>\n", "      <th>Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-03</td>\n", "      <td>value</td>\n", "      <td>0.746957</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-04</td>\n", "      <td>value</td>\n", "      <td>1.102485</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-05</td>\n", "      <td>value</td>\n", "      <td>0.614652</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-06</td>\n", "      <td>value</td>\n", "      <td>0.732592</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-07</td>\n", "      <td>value</td>\n", "      <td>0.849795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124075</th>\n", "      <td>2023-10</td>\n", "      <td>attribute</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124076</th>\n", "      <td>2023-11</td>\n", "      <td>attribute</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124077</th>\n", "      <td>2023-12</td>\n", "      <td>attribute</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124078</th>\n", "      <td>2024-01</td>\n", "      <td>attribute</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124079</th>\n", "      <td>2024-02</td>\n", "      <td>attribute</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>124080 rows × 3 columns</p>\n", "</div>"], "text/plain": ["       yearMonth   Variable     Value\n", "0        2023-03      value  0.746957\n", "1        2023-04      value  1.102485\n", "2        2023-05      value  0.614652\n", "3        2023-06      value  0.732592\n", "4        2023-07      value  0.849795\n", "...          ...        ...       ...\n", "124075   2023-10  attribute  0.000000\n", "124076   2023-11  attribute  0.000000\n", "124077   2023-12  attribute  0.000000\n", "124078   2024-01  attribute  0.000000\n", "124079   2024-02  attribute  0.000000\n", "\n", "[124080 rows x 3 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df_melt = pd.melt(df, id_vars=['yearMonth'], value_vars=['value', 'attribute'], var_name='Variable', value_name='Value')\n", "df_melt"]}, {"cell_type": "code", "execution_count": 9, "id": "50a83aa6-887d-4392-8d11-6bc8d38bc985", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.boxplot(x=df_melt['yearMonth'], y=df_melt['Value'], hue=df_melt['Variable'])\n", "plt.yscale('log')\n", "plt.title(f'Box Plot of {segmentType} Values')\n", "plt.xlabel('Value')\n", "plt.xticks(rotation=90)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 10, "id": "63ceb248a60df591", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:29.319656Z", "start_time": "2024-06-06T20:05:28.949698Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>segment</th>\n", "      <th>Low</th>\n", "      <th>Medium</th>\n", "      <th>High</th>\n", "    </tr>\n", "    <tr>\n", "      <th>yearMonth</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-03</th>\n", "      <td>4654</td>\n", "      <td>230</td>\n", "      <td>286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-04</th>\n", "      <td>4635</td>\n", "      <td>257</td>\n", "      <td>278</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05</th>\n", "      <td>4629</td>\n", "      <td>281</td>\n", "      <td>260</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06</th>\n", "      <td>4638</td>\n", "      <td>259</td>\n", "      <td>273</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-07</th>\n", "      <td>4631</td>\n", "      <td>254</td>\n", "      <td>285</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08</th>\n", "      <td>4626</td>\n", "      <td>258</td>\n", "      <td>286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09</th>\n", "      <td>4638</td>\n", "      <td>253</td>\n", "      <td>279</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10</th>\n", "      <td>4638</td>\n", "      <td>270</td>\n", "      <td>262</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-11</th>\n", "      <td>4655</td>\n", "      <td>260</td>\n", "      <td>255</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12</th>\n", "      <td>4663</td>\n", "      <td>261</td>\n", "      <td>246</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01</th>\n", "      <td>4757</td>\n", "      <td>259</td>\n", "      <td>154</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02</th>\n", "      <td>4757</td>\n", "      <td>258</td>\n", "      <td>155</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["segment     Low  Medium  High\n", "yearMonth                    \n", "2023-03    4654     230   286\n", "2023-04    4635     257   278\n", "2023-05    4629     281   260\n", "2023-06    4638     259   273\n", "2023-07    4631     254   285\n", "2023-08    4626     258   286\n", "2023-09    4638     253   279\n", "2023-10    4638     270   262\n", "2023-11    4655     260   255\n", "2023-12    4663     261   246\n", "2024-01    4757     259   154\n", "2024-02    4757     258   155"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["segment_count_by_month = df.groupby('yearMonth')['segment'].value_counts().reset_index(name='count')\n", "segment_count_by_month = segment_count_by_month.pivot(index='yearMonth', columns='segment', values='count')\n", "segment_count_by_month = segment_count_by_month[['Low', 'Medium', 'High']]\n", "segment_count_by_month.head(20)"]}, {"cell_type": "code", "execution_count": 11, "id": "e768b4002952df83", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:29.680661Z", "start_time": "2024-06-06T20:05:28.997035Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# segment_count_by_month.set_index('yearMonth', inplace=True)\n", "ax = segment_count_by_month.drop(columns=['Low']).plot(kind='bar', stacked=True)\n", "for c in ax.containers:\n", "    ax.bar_label(c, label_type='center')\n", "plt.title(f\"CJ {segmentType} segment count by month\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "id": "a2b2f9b0041c432a", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:30.530906Z", "start_time": "2024-06-06T20:05:29.630414Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["segment_count_by_month.plot(kind='line')\n", "plt.yscale('log')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "id": "dcfa2129215efb03", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:30.705739Z", "start_time": "2024-06-06T20:05:30.290600Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>rankChange</th>\n", "      <th>-2</th>\n", "      <th>-1</th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "    <tr>\n", "      <th>yearMonth</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-03</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5170.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-04</th>\n", "      <td>87.0</td>\n", "      <td>137.0</td>\n", "      <td>4705.0</td>\n", "      <td>160.0</td>\n", "      <td>81.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05</th>\n", "      <td>8.0</td>\n", "      <td>179.0</td>\n", "      <td>4845.0</td>\n", "      <td>93.0</td>\n", "      <td>45.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06</th>\n", "      <td>4.0</td>\n", "      <td>107.0</td>\n", "      <td>4943.0</td>\n", "      <td>113.0</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-07</th>\n", "      <td>2.0</td>\n", "      <td>87.0</td>\n", "      <td>4977.0</td>\n", "      <td>98.0</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08</th>\n", "      <td>1.0</td>\n", "      <td>93.0</td>\n", "      <td>4979.0</td>\n", "      <td>93.0</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09</th>\n", "      <td>2.0</td>\n", "      <td>109.0</td>\n", "      <td>4968.0</td>\n", "      <td>88.0</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10</th>\n", "      <td>3.0</td>\n", "      <td>104.0</td>\n", "      <td>4971.0</td>\n", "      <td>91.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-11</th>\n", "      <td>4.0</td>\n", "      <td>109.0</td>\n", "      <td>4965.0</td>\n", "      <td>91.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12</th>\n", "      <td>8.0</td>\n", "      <td>115.0</td>\n", "      <td>4936.0</td>\n", "      <td>108.0</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01</th>\n", "      <td>17.0</td>\n", "      <td>253.0</td>\n", "      <td>4809.0</td>\n", "      <td>81.0</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02</th>\n", "      <td>0.0</td>\n", "      <td>91.0</td>\n", "      <td>4988.0</td>\n", "      <td>90.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["rankChange    -2     -1       0      1     2\n", "yearMonth                                   \n", "2023-03      0.0    0.0  5170.0    0.0   0.0\n", "2023-04     87.0  137.0  4705.0  160.0  81.0\n", "2023-05      8.0  179.0  4845.0   93.0  45.0\n", "2023-06      4.0  107.0  4943.0  113.0   3.0\n", "2023-07      2.0   87.0  4977.0   98.0   6.0\n", "2023-08      1.0   93.0  4979.0   93.0   4.0\n", "2023-09      2.0  109.0  4968.0   88.0   3.0\n", "2023-10      3.0  104.0  4971.0   91.0   1.0\n", "2023-11      4.0  109.0  4965.0   91.0   1.0\n", "2023-12      8.0  115.0  4936.0  108.0   3.0\n", "2024-01     17.0  253.0  4809.0   81.0  10.0\n", "2024-02      0.0   91.0  4988.0   90.0   1.0"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["change_count = df.groupby('yearMonth')['rankChange'].value_counts().reset_index(name='count')\n", "change_count = change_count.pivot(index='yearMonth', columns='rankChange', values='count').fillna(0)\n", "change_count"]}, {"cell_type": "code", "execution_count": 14, "id": "5ca0f89ad103eff6", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:30.956673Z", "start_time": "2024-06-06T20:05:30.381249Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["change_count.plot(kind='line')\n", "plt.yscale('log')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 15, "id": "adea362a-306a-4844-b783-cd4e18a1c6a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["485 accounts need to check\n"]}], "source": ["df_sort = df.sort_values(by=['accountId', 'yearMonth'])\n", "df_sort['prevRankChange'] = df_sort.groupby('accountId')['rankChange'].shift(1)\n", "df_sort = df_sort.dropna(subset=['prevRankChange'])\n", "accts_to_check = df_sort[df_sort.rankChange * df_sort.prevRankChange < 0].accountId.unique()\n", "print(f\"{len(accts_to_check)} accounts need to check\")"]}, {"cell_type": "code", "execution_count": 16, "id": "2fc6322a-6b1a-4680-bd01-fc399eb34a00", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["yearMonth 2023-03 2023-04 2023-05 2023-06 2023-07 2023-08 2023-09 2023-10  \\\n", "accountId                                                                   \n", "26328        High     Low  Medium  Medium  Medium  Medium     Low     Low   \n", "63458         Low  Medium     Low  Medium     Low     Low     Low     Low   \n", "65653      Medium     Low  Medium     Low  Medium  Medium  Medium  Medium   \n", "\n", "yearMonth 2023-11 2023-12 2024-01 2024-02  \n", "accountId                                  \n", "26328         Low     Low     Low     Low  \n", "63458         Low     Low     Low     Low  \n", "65653         Low  Medium     Low     Low  \n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sample_accts = accts_to_check[np.random.randint(len(accts_to_check), size=3)]\n", "samples = df[df.accountId.isin(sample_accts)]\n", "samples_pivot = samples.pivot(index='accountId', columns='yearMonth', values='segment')\n", "print(samples_pivot)\n", "for account in sample_accts:\n", "    account_df = samples[samples['accountId'] == account]\n", "    plt.plot(account_df['yearMonth'], account_df['segmentRank'], marker='o', label=account)\n", "plt.title('Segment Changes Over 12 Months for Sample Accounts')\n", "plt.xlabel('Year-Month')\n", "plt.ylabel('Segment')\n", "plt.yticks([1, 2, 3], ['L', 'M', 'H'])\n", "plt.legend(title='Account')\n", "plt.xticks(rotation=45)\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 17, "id": "3ae5a5281c671739", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:30.959922Z", "start_time": "2024-06-06T20:05:30.825592Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>segmentRank_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3960</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4061</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4066</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4088</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4130</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   accountId  segmentRank_count\n", "0       3960                  1\n", "1       4061                  1\n", "2       4066                  1\n", "3       4088                  1\n", "4       4130                  2"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["acct_rank_count = df.groupby(['accountId'])['segmentRank'].nunique().reset_index(name='segmentRank_count')\n", "acct_rank_count.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "36e7384ba2a13145", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:31.150397Z", "start_time": "2024-06-06T20:05:30.844528Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>segmentRank_count</th>\n", "      <th>num_accts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>4246</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>595</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>329</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   segmentRank_count  num_accts\n", "0                  1       4246\n", "1                  2        595\n", "2                  3        329"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["acct_change_grouped = acct_rank_count.groupby('segmentRank_count')['accountId'].size().reset_index(name='num_accts')\n", "acct_change_grouped"]}, {"cell_type": "code", "execution_count": 19, "id": "67525276a5515c7d", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:31.211409Z", "start_time": "2024-06-06T20:05:30.856419Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.pie(acct_change_grouped['num_accts'], labels=acct_change_grouped['segmentRank_count'], autopct='%1.1f%%')\n", "plt.legend(title='# of segments \\n in 12 months', loc='upper right')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 20, "id": "7a1bb64d9f2f0afc", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:31.212769Z", "start_time": "2024-06-06T20:05:30.979682Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["False    59256\n", "True      2784\n", "Name: changed, dtype: int64"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# find number of changes\n", "df['changed'] = df.rankChange != 0\n", "df['changed'].value_counts()"]}, {"cell_type": "code", "execution_count": 21, "id": "6741dd7e596844a", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:31.214182Z", "start_time": "2024-06-06T20:05:30.985986Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_changes</th>\n", "      <th>num_accts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>4246</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>199</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>247</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>151</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>5</td>\n", "      <td>87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>6</td>\n", "      <td>64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>7</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>8</td>\n", "      <td>8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   num_changes  num_accts\n", "0            0       4246\n", "1            1        199\n", "2            2        247\n", "3            3        143\n", "4            4        151\n", "5            5         87\n", "6            6         64\n", "7            7         25\n", "8            8          8"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["segment_change_count = df.groupby(['accountId'])['changed'].sum().reset_index(name='count')\n", "segment_change_count = segment_change_count.rename(columns={'count': 'num_changes'})\n", "segment_change_count_grouped = segment_change_count.groupby('num_changes')['accountId'].size().reset_index(name='num_accts')\n", "segment_change_count_grouped"]}, {"cell_type": "code", "execution_count": 22, "id": "d74be1f73407a413", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:31.369862Z", "start_time": "2024-06-06T20:05:31.004089Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.pie(segment_change_count_grouped['num_accts'], labels=segment_change_count_grouped['num_changes'], autopct='%1.1f%%')\n", "# plt.legend(title='# of segments \\n in 12 months', loc='upper right')\n", "plt.title(\"# of segment changes in 12 months\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 23, "id": "cdde687b62b6bf51", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:31.371060Z", "start_time": "2024-06-06T20:05:31.122620Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["Hold        46249\n", "Decrease     8269\n", "Increase     7522\n", "Name: trend, dtype: int64"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["trend_count = df['trend'].value_counts()\n", "trend_count"]}, {"cell_type": "code", "execution_count": 24, "id": "1b1843a2819c916", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:05:31.442410Z", "start_time": "2024-06-06T20:05:31.130354Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.pie(trend_count, labels=trend_count.index, autopct='%1.1f%%')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 25, "id": "bdee0f907ad9c46f", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:07:59.773582Z", "start_time": "2024-06-06T20:07:59.644433Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>trend</th>\n", "      <th>Decrease</th>\n", "      <th>Hold</th>\n", "      <th>Increase</th>\n", "    </tr>\n", "    <tr>\n", "      <th>yearMonth</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-03</th>\n", "      <td>NaN</td>\n", "      <td>5170.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-04</th>\n", "      <td>224.0</td>\n", "      <td>4705.0</td>\n", "      <td>241.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05</th>\n", "      <td>739.0</td>\n", "      <td>3625.0</td>\n", "      <td>806.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06</th>\n", "      <td>763.0</td>\n", "      <td>3589.0</td>\n", "      <td>818.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-07</th>\n", "      <td>731.0</td>\n", "      <td>3743.0</td>\n", "      <td>696.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08</th>\n", "      <td>694.0</td>\n", "      <td>3745.0</td>\n", "      <td>731.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09</th>\n", "      <td>659.0</td>\n", "      <td>3826.0</td>\n", "      <td>685.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10</th>\n", "      <td>754.0</td>\n", "      <td>3747.0</td>\n", "      <td>669.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-11</th>\n", "      <td>721.0</td>\n", "      <td>3750.0</td>\n", "      <td>699.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12</th>\n", "      <td>773.0</td>\n", "      <td>3719.0</td>\n", "      <td>678.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01</th>\n", "      <td>1112.0</td>\n", "      <td>3295.0</td>\n", "      <td>763.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02</th>\n", "      <td>1099.0</td>\n", "      <td>3335.0</td>\n", "      <td>736.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["trend      Decrease    Hold  Increase\n", "yearMonth                            \n", "2023-03         NaN  5170.0       NaN\n", "2023-04       224.0  4705.0     241.0\n", "2023-05       739.0  3625.0     806.0\n", "2023-06       763.0  3589.0     818.0\n", "2023-07       731.0  3743.0     696.0\n", "2023-08       694.0  3745.0     731.0\n", "2023-09       659.0  3826.0     685.0\n", "2023-10       754.0  3747.0     669.0\n", "2023-11       721.0  3750.0     699.0\n", "2023-12       773.0  3719.0     678.0\n", "2024-01      1112.0  3295.0     763.0\n", "2024-02      1099.0  3335.0     736.0"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["trend_count_by_month = df.groupby('yearMonth')['trend'].value_counts().reset_index(name='count')\n", "trend_count_by_month = trend_count_by_month.pivot(index='yearMonth', columns='trend', values='count')\n", "trend_count_by_month = trend_count_by_month[['Decrease', 'Hold', 'Increase']]\n", "trend_count_by_month.head(20)"]}, {"cell_type": "code", "execution_count": 26, "id": "506435e6991f2fdf", "metadata": {"ExecuteTime": {"end_time": "2024-06-06T20:08:55.766931Z", "start_time": "2024-06-06T20:08:49.287440Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ax = trend_count_by_month.drop(columns=['Hold']).plot(kind='bar', stacked=True)\n", "for c in ax.containers:\n", "    ax.bar_label(c, label_type='center')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "51352f8f9874221b", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}