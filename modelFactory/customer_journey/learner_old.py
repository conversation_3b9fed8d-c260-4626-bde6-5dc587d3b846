import numpy as np
import pandas as pd
from abstract_model_factory.abstract_learner import AbstractLearner
from customer_journey.constants import Constants
from customer_journey.data import Data
from scipy import stats

BINS_DICT = {'sales_run': [0., 1.39583, 9.06366, float('inf')],
             'p_UB_run': [0., 7.39915, 46.77524, float('inf')],
             'p_Spec_run': [0., 1.23860103, 7.8803316, float('inf')],
             'p_CRI_run': [0., 2.94017214, 21.59782511, float('inf')],
             'p_Spec_CRI_run': [0., 0.49086102, 3.72128405, float('inf')],
             'sales_MktBkt_run': [0., 8.95012, 55.05744, float('inf')],
             'sales_share_run': [0., 0.2, 0.8, 1.]}


def get_bins(ser, bin_edges):
    ser = pd.cut(ser, bins=bin_edges, labels=['low', 'medium', 'high'])
    ser = ser.fillna('low')
    return ser.astype(str)


def get_slope(ser):
    try:
        ser.reset_index(drop=True)
        ser = ser.values
        if len(ser) < 6:
            return np.nan
        if np.nan in ser:
            return np.nan
        slope, intercept, r_value, p_value, std_err = stats.linregress(np.array([1, 2, 3, 4, 5, 6]), ser)
        return slope
    except:
        return np.nan


def compute_slope(series):
    # Extract x (month) and y (val) values from the series
    series = series[series > 0]
    x = series.index.values
    y = series.values

    # Perform linear regression and return the slope
    slope, _, _, _, _ = stats.linregress(x, y)
    return slope


def get_slope_test(df):
    # fill df with all account-yearmonth comb.
    df['date'] = pd.to_datetime(df['date'], format="%Y%m")
    idx = pd.MultiIndex.from_product(
        [df['accountId'].unique(), pd.date_range(df['date'].min(), df['date'].max(), freq='MS')],
        names=['accountId', 'date'])
    df.set_index(['accountId', 'date'], inplace=True)
    df = df.reindex(idx)
    df = df.reset_index()
    df = df.fillna(-1)

    def compute_slope(series):
        # Extract x (month) and y (val) values from the series
        series = series[series > 0]
        x = series.index.values
        y = series.values

        # Perform linear regression and return the slope
        slope, _, _, _, _ = stats.linregress(x, y)
        return slope

    df['AP_slope'] = None
    for w in range(Constants.SLOPE_ROLLING_WINDOW_SIZE, 1, -1):
        df[f'slope_{w}'] = df.groupby('accountId')['AP_int'].rolling(window=w).apply(compute_slope,
                                                                                     raw=False).reset_index(
            level=0, drop=True)
        df['AP_slope'] = df['AP_slope'].combine_first(df[f'slope_{w}'])

    print(df)


class Learner(AbstractLearner):

    def calc_potential_run(self, productId, n_months=3):
        data = Data.get_instance()
        sales_cri = data.get_dataframe("sales_cri_df")

        # filter by productId
        sales_cri = sales_cri[sales_cri['productId'] == productId]

        # sort values by account and date
        sales_cri.sort_values(by=['accountId', 'yearMonth'], inplace=True)
        # calc the rolling statistics for each accountID
        sales_cri['sales_run'] = np.around(
            sales_cri.groupby('accountId')['actual_sales'].rolling(n_months).mean().reset_index(0, drop=True),
            decimals=5)
        sales_cri['sales_MktBkt_run'] = np.around(
            sales_cri.groupby('accountId')['actual_mktbasket'].rolling(n_months).mean().reset_index(0, drop=True),
            decimals=5)
        sales_cri['sales_share_run'] = np.around(sales_cri['sales_run'] / sales_cri['sales_MktBkt_run'], decimals=5)
        sales_cri['index_run'] = np.around(
            sales_cri.groupby('accountId')['index'].rolling(n_months).mean().reset_index(0, drop=True), decimals=5)
        # Calculate potential Upper Bound
        sales_cri['p_UB_run'] = np.around(sales_cri['sales_MktBkt_run'] - sales_cri['sales_run'], decimals=5)
        # Calculate specialty adjusted potential
        demo_mods = sales_cri.groupby(['specialty'], as_index=False).agg(
            {"actual_share": 'mean', 'actual_sales': 'size'})
        demo_mods.rename(columns={'actual_share': 'spec_modify'}, inplace=True)
        demo_mods.sort_values(by='spec_modify', inplace=True)
        sales_cri = sales_cri.merge(demo_mods.loc[:, ['specialty', 'spec_modify']], how='left', on='specialty')
        # Apply adjustments
        sales_cri['p_Spec_run'] = sales_cri['p_UB_run'] * sales_cri['spec_modify'].fillna(
            sales_cri['spec_modify'].mean())
        sales_cri['p_CRI_run'] = sales_cri['p_UB_run'] * sales_cri['index_run'].fillna(sales_cri['index_run'].mean())
        # Both CRI and Specialty Adjustment
        sales_cri['p_Spec_CRI_run'] = sales_cri['p_Spec_run'] * sales_cri['index_run'].fillna(
            sales_cri['index_run'].mean())
        sales_cri = sales_cri.dropna(subset=['sales_run'])

        data.set_dataframe(f"sales_cri_run_{productId}", sales_cri)

    def get_cj_bins(self, productId):
        data = Data.get_instance()
        sales_cri = data.get_dataframe(f"sales_cri_run_{productId}")

        # get monthly segment membership
        sales_cri['date'] = sales_cri['yearMonth']
        sales_cri['sales_bin'] = sales_cri.groupby(['date'])['sales_run'].transform(get_bins, BINS_DICT['sales_run'])
        sales_cri['sales_MktBkt_bin'] = sales_cri.groupby(['date'])['sales_MktBkt_run'].transform(get_bins, BINS_DICT[
            'sales_MktBkt_run'])
        sales_cri['sales_share_bin'] = sales_cri.groupby(['date'])['sales_share_run'].transform(get_bins,
                                                                                                BINS_DICT[
                                                                                                    'sales_share_run'])
        sales_cri['p_UB_bin'] = sales_cri.groupby(['date'])['p_UB_run'].transform(get_bins, BINS_DICT['p_UB_run'])
        sales_cri['p_Spec_bin'] = sales_cri.groupby(['date'])['p_Spec_run'].transform(get_bins, BINS_DICT['p_Spec_run'])
        sales_cri['p_CRI_bin'] = sales_cri.groupby(['date'])['p_CRI_run'].transform(get_bins, BINS_DICT['p_CRI_run'])
        sales_cri['p_Spec_CRI_bin'] = sales_cri.groupby(['date'])['p_Spec_CRI_run'].transform(get_bins, BINS_DICT[
            'p_Spec_CRI_run'])

        sales_cri.rename(columns={'sales_bin': 'actual_bin', 'p_Spec_CRI_bin': 'potential_bin',
                                  'sales_run': 'actual_val', 'p_Spec_CRI_run': 'potential_val',
                                  'sales_MktBkt_run': 'market_basket_val', 'sales_share_run': 'market_share_val',
                                  'sales_MktBkt_bin': 'market_basket_bin', 'sales_share_bin': 'market_share_bin'},
                         inplace=True)
        data.set_dataframe(f"sales_cri_bin_{productId}", sales_cri)

    def compute_cj_outputs(self, productId):
        data = Data.get_instance()
        cj_output = data.get_dataframe(f"sales_cri_bin_{productId}")

        # compute 9-segment label
        cj_output['AP_9_segment_label'] = cj_output['actual_bin'].astype(str) + "_" + cj_output['potential_bin'].astype(
            str)

        # compute 5-segment label and numeric value
        cj_output['AP_priority_segment'] = cj_output['AP_9_segment_label'].map(Constants.FIVE_SEG_DICT)
        cj_output['AP_int'] = cj_output['AP_priority_segment'].map(Constants.FIVE_TO_INT)

        # compute slope and trend
        # cj_output['AP_slope'] = cj_output.groupby('accountId')['AP_int'].rolling(6).apply(get_slope).reset_index(0,
        #                                                                                                          drop=True)

        # cj_output['AP_slope'] = cj_output.groupby('accountId').apply(lambda x: x.sort_values('date').rolling(window=6).apply(get_slope_test))
        cj_output['date'] = pd.to_datetime(cj_output['date'], format="%Y%m")
        idx = pd.MultiIndex.from_product(
            [cj_output['accountId'].unique(),
             pd.date_range(cj_output['date'].min(), cj_output['date'].max(), freq='MS')],
            names=['accountId', 'date'])
        cj_output.set_index(['accountId', 'date'], inplace=True)
        cj_output = cj_output.reindex(idx)
        cj_output = cj_output.reset_index()
        cj_output = cj_output.fillna({"AP_int": -1})

        cj_output['AP_slope'] = cj_output.groupby('accountId')['AP_int'].rolling(window=4).apply(compute_slope,
                                                                                                 raw=False).reset_index(
            level=0, drop=True)

        cj_output['priority_trend'] = pd.cut(cj_output['AP_slope'], bins=[-np.inf, -0.2, 0.2, np.inf],
                                             labels=['decline', 'hold', 'increase']).astype(str)
        cj_output = cj_output[cj_output['AP_int'] >= 0]
        data.set_dataframe(f"cj_output_{productId}", cj_output)
        return cj_output

    def get_non_competitor_products(self):
        data = Data.get_instance()
        sales_df = data.get_dataframe("sales_df")
        non_competitor_products = sales_df[sales_df['iscompetitor'] == False]['productId'].unique().tolist()
        data.set_param("non_competitor_products", non_competitor_products)
        return non_competitor_products

    def execute(self):
        data = Data.get_instance()
        non_competitor_products = self.get_non_competitor_products()
        final_cj_output = pd.DataFrame()
        for productId in non_competitor_products:
            print("Start processing product: ", productId)
            self.calc_potential_run(productId)
            self.get_cj_bins(productId)
            df = self.compute_cj_outputs(productId)
            final_cj_output = final_cj_output.append(df, ignore_index=True)
            print("Finished processing product: ", productId)

        final_cj_output = final_cj_output[['accountId', 'productId', 'AP_9_segment_label', 'AP_int', 'AP_priority_segment', 'AP_slope',
                                           'actual_bin', 'actual_val', 'yearMonth', 'market_basket_bin', 'market_basket_val',
                                           'market_share_bin', 'market_share_val', 'potential_bin', 'potential_val', 'priority_trend']]
        data.set_dataframe("final_cj_output", final_cj_output)

    def qa_module(self):
        pass
