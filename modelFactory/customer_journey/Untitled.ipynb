{"cells": [{"cell_type": "code", "execution_count": 1, "id": "98cfe4b8-81b1-4660-a341-0fa52601eb0c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "2698b732-0078-48ca-b05a-b31bd4217a2a", "metadata": {}, "outputs": [], "source": ["# df_without_cri = pd.read_parquet(\"~/Downloads/customer_journey-sa-spec-persona-cri-adjust-filter.parquet\")\n", "df_with_cri = pd.read_parquet(\"~/Documents/aktana_repos/learning/modelFactory/customer_journey/CJ_parquets/customer_journey-sa-cri-new.parquet\")\n", "df_without_sa = pd.read_parquet(\"~/Downloads/customer_journey-no-sa.parquet\")\n", "product = 1016"]}, {"cell_type": "markdown", "id": "232170d8-9471-4f6a-b0ae-2bd1f6e8c5cd", "metadata": {}, "source": ["## Check one sample account"]}, {"cell_type": "code", "execution_count": 3, "id": "1b9d3657-8d54-4ff5-b072-0596d6c16255", "metadata": {}, "outputs": [], "source": ["df_without_sa_sales = df_without_sa[(df_without_sa.segmentType == 'Sales') & (df_without_sa.productId == product)]\n", "# df_without_cri_sales = df_without_cri[(df_without_cri.segmentType == 'Sales') & (df_without_cri.productId == product)]\n", "df_with_cri_sales = df_with_cri[(df_with_cri.segmentType == 'Sales') & (df_with_cri.productId == product)]"]}, {"cell_type": "code", "execution_count": 4, "id": "d507c7be-76b9-41ce-b7f5-2d0fadc91878", "metadata": {}, "outputs": [{"data": {"text/plain": ["9784.0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["sample_acct = df_without_sa_sales[df_without_sa_sales.value > 0].accountId.sample().values[0]\n", "sample_acct"]}, {"cell_type": "code", "execution_count": 5, "id": "a64fd828-816d-415f-8f0b-8d5e4a0a9a56", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Actual account level sales: \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>productId</th>\n", "      <th>yearMonth</th>\n", "      <th>segmentType</th>\n", "      <th>value</th>\n", "      <th>segment</th>\n", "      <th>segmentRank</th>\n", "      <th>rankChange</th>\n", "      <th>slope</th>\n", "      <th>trend</th>\n", "      <th>attribute</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1891680</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2023-05</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891681</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2023-06</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891682</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2023-07</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891683</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2023-08</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891684</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2023-09</td>\n", "      <td>Sales</td>\n", "      <td>13142.92</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.501275</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891685</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2023-10</td>\n", "      <td>Sales</td>\n", "      <td>13142.92</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.501275</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891686</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2023-11</td>\n", "      <td>Sales</td>\n", "      <td>13142.92</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891687</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2023-12</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>-0.501275</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891688</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2024-01</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>-0.501275</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891689</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2024-02</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891690</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2024-03</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1891691</th>\n", "      <td>9784.0</td>\n", "      <td>1016</td>\n", "      <td>2024-04</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         accountId  productId yearMonth segmentType     value segment  \\\n", "1891680     9784.0       1016   2023-05       Sales      0.00     Low   \n", "1891681     9784.0       1016   2023-06       Sales      0.00     Low   \n", "1891682     9784.0       1016   2023-07       Sales      0.00     Low   \n", "1891683     9784.0       1016   2023-08       Sales      0.00     Low   \n", "1891684     9784.0       1016   2023-09       Sales  13142.92     Low   \n", "1891685     9784.0       1016   2023-10       Sales  13142.92     Low   \n", "1891686     9784.0       1016   2023-11       Sales  13142.92     Low   \n", "1891687     9784.0       1016   2023-12       Sales      0.00     Low   \n", "1891688     9784.0       1016   2024-01       Sales      0.00     Low   \n", "1891689     9784.0       1016   2024-02       Sales      0.00     Low   \n", "1891690     9784.0       1016   2024-03       Sales      0.00     Low   \n", "1891691     9784.0       1016   2024-04       Sales      0.00     Low   \n", "\n", "         segmentRank  rankChange     slope     trend  attribute  \n", "1891680            1         0.0  0.000000      Hold        NaN  \n", "1891681            1         0.0  0.000000      Hold        NaN  \n", "1891682            1         0.0  0.000000      Hold        NaN  \n", "1891683            1         0.0  0.000000      Hold        NaN  \n", "1891684            1         0.0  0.501275  Increase        NaN  \n", "1891685            1         0.0  0.501275  Increase        NaN  \n", "1891686            1         0.0  0.000000      Hold        NaN  \n", "1891687            1         0.0 -0.501275  Decrease        NaN  \n", "1891688            1         0.0 -0.501275  Decrease        NaN  \n", "1891689            1         0.0  0.000000      Hold        NaN  \n", "1891690            1         0.0  0.000000      Hold        NaN  \n", "1891691            1         0.0  0.000000      Hold        NaN  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"Actual account level sales: \")\n", "s1 = df_without_sa_sales[df_without_sa_sales.accountId == sample_acct]\n", "s1"]}, {"cell_type": "code", "execution_count": 6, "id": "8dff6afe-3fcc-46ae-a83f-31e0825687a8", "metadata": {}, "outputs": [], "source": ["# print(\"Sales attribution without CRI adjustment: \")\n", "# s2 = df_without_cri_sales[df_without_cri_sales.accountId == sample_acct]\n", "# s2"]}, {"cell_type": "code", "execution_count": 7, "id": "bbcb7866-a689-4a3a-a1d8-1f701dee41ad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sales attribution with CRI adjustment: \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>productId</th>\n", "      <th>yearMonth</th>\n", "      <th>segmentType</th>\n", "      <th>value</th>\n", "      <th>segment</th>\n", "      <th>segmentRank</th>\n", "      <th>rankChange</th>\n", "      <th>slope</th>\n", "      <th>trend</th>\n", "      <th>attribute</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1896091</th>\n", "      <td>9784</td>\n", "      <td>1016</td>\n", "      <td>2023-10</td>\n", "      <td>Sales</td>\n", "      <td>0.0</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1896092</th>\n", "      <td>9784</td>\n", "      <td>1016</td>\n", "      <td>2023-11</td>\n", "      <td>Sales</td>\n", "      <td>0.0</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1896093</th>\n", "      <td>9784</td>\n", "      <td>1016</td>\n", "      <td>2023-12</td>\n", "      <td>Sales</td>\n", "      <td>0.0</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1896094</th>\n", "      <td>9784</td>\n", "      <td>1016</td>\n", "      <td>2024-01</td>\n", "      <td>Sales</td>\n", "      <td>0.0</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1896095</th>\n", "      <td>9784</td>\n", "      <td>1016</td>\n", "      <td>2024-02</td>\n", "      <td>Sales</td>\n", "      <td>0.0</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1896096</th>\n", "      <td>9784</td>\n", "      <td>1016</td>\n", "      <td>2024-03</td>\n", "      <td>Sales</td>\n", "      <td>0.0</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1896097</th>\n", "      <td>9784</td>\n", "      <td>1016</td>\n", "      <td>2024-04</td>\n", "      <td>Sales</td>\n", "      <td>0.0</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         accountId  productId yearMonth segmentType  value segment  \\\n", "1896091       9784       1016   2023-10       Sales    0.0     Low   \n", "1896092       9784       1016   2023-11       Sales    0.0     Low   \n", "1896093       9784       1016   2023-12       Sales    0.0     Low   \n", "1896094       9784       1016   2024-01       Sales    0.0     Low   \n", "1896095       9784       1016   2024-02       Sales    0.0     Low   \n", "1896096       9784       1016   2024-03       Sales    0.0     Low   \n", "1896097       9784       1016   2024-04       Sales    0.0     Low   \n", "\n", "         segmentRank  rankChange  slope trend  attribute  \n", "1896091            1           0    0.0  Hold        NaN  \n", "1896092            1           0    0.0  Hold        NaN  \n", "1896093            1           0    0.0  Hold        NaN  \n", "1896094            1           0    0.0  Hold        NaN  \n", "1896095            1           0    0.0  Hold        NaN  \n", "1896096            1           0    0.0  Hold        NaN  \n", "1896097            1           0    0.0  Hold        NaN  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"Sales attribution with CRI adjustment: \")\n", "s3 = df_with_cri_sales[df_with_cri_sales.accountId == sample_acct]\n", "s3"]}, {"cell_type": "code", "execution_count": 8, "id": "3b349a06-47fe-4f8b-858d-ec3fff742942", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>value</th>\n", "      <th>value_with_cri</th>\n", "    </tr>\n", "    <tr>\n", "      <th>yearMonth</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-10</th>\n", "      <td>13142.92</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-11</th>\n", "      <td>13142.92</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12</th>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01</th>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02</th>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03</th>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-04</th>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              value  value_with_cri\n", "yearMonth                          \n", "2023-10    13142.92             0.0\n", "2023-11    13142.92             0.0\n", "2023-12        0.00             0.0\n", "2024-01        0.00             0.0\n", "2024-02        0.00             0.0\n", "2024-03        0.00             0.0\n", "2024-04        0.00             0.0"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df_value = s1[['yearMonth', 'value']].merge(s3[['yearMonth', 'value']], on='yearMonth', suffixes=['', '_with_cri'])\n", "combined_df_value = combined_df_value.set_index('yearMonth')\n", "combined_df_value"]}, {"cell_type": "code", "execution_count": 9, "id": "90fcd9c4-b464-49be-a476-5a0f43ed6659", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["combined_df_value.plot(kind='bar')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 10, "id": "0815def0-472e-4232-9285-df2b4da47f77", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>yearMonth</th>\n", "      <th>segmentRank</th>\n", "      <th>segmentRank_with_cri</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-10</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-11</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-12</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-02</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-03</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-04</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  yearMonth  segmentRank  segmentRank_with_cri\n", "0   2023-10            1                     1\n", "1   2023-11            1                     1\n", "2   2023-12            1                     1\n", "3   2024-01            1                     1\n", "4   2024-02            1                     1\n", "5   2024-03            1                     1\n", "6   2024-04            1                     1"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df_seg = s1[['yearMonth', 'segmentRank']].merge(s3[['yearMonth', 'segmentRank']], on='yearMonth', suffixes=['', '_with_cri'])\n", "combined_df_seg.set_index('yearMonth')\n", "combined_df_seg"]}, {"cell_type": "code", "execution_count": 11, "id": "acfe8f99-746c-4c83-8693-ba600f3efb44", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["combined_df_seg.plot(kind='bar')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "9586e98c-83f5-4e60-b37f-484d537f3303", "metadata": {}, "source": ["## Number of accounts in sales"]}, {"cell_type": "code", "execution_count": 12, "id": "c59a877f-1e60-4354-aee0-2bbda7e1c545", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Without SA, there are 2413 accounts in Sales\n"]}], "source": ["print(f\"Without SA, there are {len(df_without_sa_sales.accountId.unique())} accounts in Sales\")"]}, {"cell_type": "code", "execution_count": 13, "id": "757b3c26-6eb6-40b1-972e-28f6f3b59adb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["With SA, there are 6110 accounts in Sales when applying CRI adjustment\n"]}], "source": ["print(f\"With SA, there are {len(df_with_cri_sales.accountId.unique())} accounts in Sales when applying CRI adjustment\")"]}, {"cell_type": "code", "execution_count": 14, "id": "2d1f9f88-f65d-4196-8908-00e74feb9a18", "metadata": {}, "outputs": [], "source": ["# print(f\"With SA, there are {len(df_without_cri_sales.accountId.unique())} accounts in Sales when not applying CRI adjustment\")"]}, {"cell_type": "markdown", "id": "5dd5a121-7f58-4c2a-9e80-f9ece64ce7e7", "metadata": {}, "source": ["## Number of rows with High segment"]}, {"cell_type": "code", "execution_count": 15, "id": "3cc2f024-09b4-414d-abfe-58c53753a99e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Without SA, there are 2149 rows with High Sales\n"]}], "source": ["print(f\"Without SA, there are {len(df_without_sa_sales[df_without_sa_sales.segment == 'High'])} rows with High Sales\")"]}, {"cell_type": "code", "execution_count": 16, "id": "93d23519-e6ee-4d50-9dd7-d2aa83e15cf2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["With SA, there are 1809 rows with High Sales when applying CRI adjustment\n"]}], "source": ["print(f\"With SA, there are {len(df_with_cri_sales[df_with_cri_sales.segment == 'High'])} rows with High Sales when applying CRI adjustment\")"]}, {"cell_type": "code", "execution_count": 17, "id": "045082f4-5e78-4e63-ae89-858a4b852aeb", "metadata": {}, "outputs": [], "source": ["# print(f\"With SA, there are {len(df_without_cri_sales[df_without_cri_sales.segment == 'High'])} rows with High Sales when not applying CRI adjustment\")"]}, {"cell_type": "code", "execution_count": null, "id": "67f452e2-bcc4-4344-a758-9288d3c7d889", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "606b7146-4a3b-49a8-b164-d562b6077d85", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "fd0a2b86-9db7-4b9b-88f8-20cb5a119c50", "metadata": {}, "source": ["## 6 month sum of sales"]}, {"cell_type": "code", "execution_count": 18, "id": "ec3df291-c36f-4978-b882-9258ef71b12b", "metadata": {}, "outputs": [], "source": ["# df_without_sa_sales = df_without_sa[(df_without_sa.segmentType == 'Sales') & (df_without_sa.productId == product)]\n", "# df_without_cri_sales = df_without_cri[(df_without_cri.segmentType == 'Sales') & (df_without_cri.productId == product)]\n", "# df_with_cri_sales"]}, {"cell_type": "code", "execution_count": 19, "id": "80f08172-67b4-494a-8e90-22205f410a9e", "metadata": {}, "outputs": [], "source": ["df_no_sa_6month = df_without_sa_sales[(df_without_sa_sales.yearMonth >= '2023-07') & (df_without_sa_sales.yearMonth <= '2023-12')][['accountId', 'yearMonth', 'value']]\n", "# df_no_cri_6month = df_without_cri_sales[(df_without_cri_sales.yearMonth >= '2023-07') & (df_without_cri_sales.yearMonth <= '2023-12')][['accountId', 'yearMonth', 'value']]\n", "df_cri_6month = df_with_cri_sales[(df_with_cri_sales.yearMonth >= '2023-07') & (df_with_cri_sales.yearMonth <= '2023-12')][['accountId', 'yearMonth', 'value']]"]}, {"cell_type": "code", "execution_count": 20, "id": "bb8dac68-1457-43c2-a0e6-ca0794f8326b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/opt/anaconda3/envs/ml-python3/lib/python3.9/site-packages/pandas/core/reshape/merge.py:1203: RuntimeWarning: invalid value encountered in cast\n", "  if not (lk == lk.astype(rk.dtype))[~np.isnan(lk)].all():\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>yearMonth</th>\n", "      <th>value</th>\n", "      <th>value_cri</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4088.0</td>\n", "      <td>2023-07</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4088.0</td>\n", "      <td>2023-08</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4088.0</td>\n", "      <td>2023-09</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4088.0</td>\n", "      <td>2023-10</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4088.0</td>\n", "      <td>2023-11</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18989</th>\n", "      <td>307808.0</td>\n", "      <td>2023-11</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18990</th>\n", "      <td>307808.0</td>\n", "      <td>2023-12</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18991</th>\n", "      <td>308020.0</td>\n", "      <td>2023-12</td>\n", "      <td>NaN</td>\n", "      <td>128508.551111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18992</th>\n", "      <td>308095.0</td>\n", "      <td>2023-11</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18993</th>\n", "      <td>308095.0</td>\n", "      <td>2023-12</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18994 rows × 4 columns</p>\n", "</div>"], "text/plain": ["       accountId yearMonth  value      value_cri\n", "0         4088.0   2023-07    0.0       0.000000\n", "1         4088.0   2023-08    0.0       0.000000\n", "2         4088.0   2023-09    0.0       0.000000\n", "3         4088.0   2023-10    0.0       0.000000\n", "4         4088.0   2023-11    0.0       0.000000\n", "...          ...       ...    ...            ...\n", "18989   307808.0   2023-11    NaN       0.000000\n", "18990   307808.0   2023-12    NaN       0.000000\n", "18991   308020.0   2023-12    NaN  128508.551111\n", "18992   308095.0   2023-11    NaN       0.000000\n", "18993   308095.0   2023-12    NaN       0.000000\n", "\n", "[18994 rows x 4 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined_6month = df_no_sa_6month.merge(df_cri_6month, on=['accountId', 'yearMonth'], suffixes=['', '_cri'], how='outer')\n", "df_combined_6month"]}, {"cell_type": "code", "execution_count": 21, "id": "e8c78a5e-e54e-48e7-8ad0-dc8f1ef54303", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Real: There are 1146 accounts with sales.\n", "SA: Sales are distributed to 1601 accounts.\n", "Real > 0 and SA > 0: 817\n", "Real > 0 and SA = 0: 329\n", "Real = 0 and SA > 0: 784\n"]}], "source": ["df_combined_6month_group = df_combined_6month.groupby('accountId').sum().reset_index()\n", "# df_combined_6month_group = df_combined_6month_group[df_combined_6month_group.value > 0]\n", "print(f\"Real: There are {df_combined_6month_group[df_combined_6month_group.value > 0].shape[0]} accounts with sales.\")\n", "print(f\"SA: Sales are distributed to {df_combined_6month_group[df_combined_6month_group.value_cri > 0].shape[0]} accounts.\")\n", "print(f\"Real > 0 and SA > 0: {df_combined_6month_group[(df_combined_6month_group.value > 0) & (df_combined_6month_group.value_cri > 0)].shape[0]}\")\n", "print(f\"Real > 0 and SA = 0: {df_combined_6month_group[(df_combined_6month_group.value > 0) & (df_combined_6month_group.value_cri == 0)].shape[0]}\")\n", "print(f\"Real = 0 and SA > 0: {df_combined_6month_group[(df_combined_6month_group.value == 0) & (df_combined_6month_group.value_cri > 0)].shape[0]}\")"]}, {"cell_type": "code", "execution_count": 27, "id": "32ed3f17-2367-4175-868f-1e4df9d6af0e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>value</th>\n", "      <th>value_cri</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>5862.0</td>\n", "      <td>78857.52</td>\n", "      <td>48190.706650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>5918.0</td>\n", "      <td>78857.52</td>\n", "      <td>78857.520000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>5970.0</td>\n", "      <td>26285.84</td>\n", "      <td>26285.840000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>6042.0</td>\n", "      <td>26285.84</td>\n", "      <td>26285.840000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>6113.0</td>\n", "      <td>709717.68</td>\n", "      <td>587052.617110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3251</th>\n", "      <td>291706.0</td>\n", "      <td>13142.92</td>\n", "      <td>18490.246410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3258</th>\n", "      <td>294260.0</td>\n", "      <td>26285.84</td>\n", "      <td>26285.840000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3261</th>\n", "      <td>294672.0</td>\n", "      <td>39428.76</td>\n", "      <td>39428.760000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3276</th>\n", "      <td>297749.0</td>\n", "      <td>52571.68</td>\n", "      <td>37127.997117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3294</th>\n", "      <td>302071.0</td>\n", "      <td>223429.64</td>\n", "      <td>223429.640000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>513 rows × 3 columns</p>\n", "</div>"], "text/plain": ["      accountId      value      value_cri\n", "11       5862.0   78857.52   48190.706650\n", "16       5918.0   78857.52   78857.520000\n", "19       5970.0   26285.84   26285.840000\n", "23       6042.0   26285.84   26285.840000\n", "28       6113.0  709717.68  587052.617110\n", "...         ...        ...            ...\n", "3251   291706.0   13142.92   18490.246410\n", "3258   294260.0   26285.84   26285.840000\n", "3261   294672.0   39428.76   39428.760000\n", "3276   297749.0   52571.68   37127.997117\n", "3294   302071.0  223429.64  223429.640000\n", "\n", "[513 rows x 3 columns]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined_6month_group_non_zero = df_combined_6month_group[df_combined_6month_group.value > 0]\n", "df_combined_6month_group_non_zero[(abs(df_combined_6month_group_non_zero.value - df_combined_6month_group_non_zero.value_cri) / df_combined_6month_group_non_zero.value < 0.5)]"]}, {"cell_type": "code", "execution_count": 23, "id": "859b47bd-ae50-40ee-9184-3b6243460825", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1654, 3)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>value</th>\n", "      <th>value_cri</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>5862.0</td>\n", "      <td>78857.52000</td>\n", "      <td>48190.706650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>6113.0</td>\n", "      <td>709717.68000</td>\n", "      <td>587052.617110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>6354.0</td>\n", "      <td>39435.33133</td>\n", "      <td>24566.206130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>6839.0</td>\n", "      <td>144572.12000</td>\n", "      <td>124864.311340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>7347.0</td>\n", "      <td>184000.88000</td>\n", "      <td>164189.373160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3054</th>\n", "      <td>250892.0</td>\n", "      <td>39428.76000</td>\n", "      <td>42853.206478</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3161</th>\n", "      <td>274214.0</td>\n", "      <td>13142.92000</td>\n", "      <td>19004.662320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3193</th>\n", "      <td>279046.0</td>\n", "      <td>13142.92000</td>\n", "      <td>9098.944615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3251</th>\n", "      <td>291706.0</td>\n", "      <td>13142.92000</td>\n", "      <td>18490.246410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3276</th>\n", "      <td>297749.0</td>\n", "      <td>52571.68000</td>\n", "      <td>37127.997117</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>237 rows × 3 columns</p>\n", "</div>"], "text/plain": ["      accountId         value      value_cri\n", "11       5862.0   78857.52000   48190.706650\n", "28       6113.0  709717.68000  587052.617110\n", "36       6354.0   39435.33133   24566.206130\n", "56       6839.0  144572.12000  124864.311340\n", "84       7347.0  184000.88000  164189.373160\n", "...         ...           ...            ...\n", "3054   250892.0   39428.76000   42853.206478\n", "3161   274214.0   13142.92000   19004.662320\n", "3193   279046.0   13142.92000    9098.944615\n", "3251   291706.0   13142.92000   18490.246410\n", "3276   297749.0   52571.68000   37127.997117\n", "\n", "[237 rows x 3 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined_6month_group_not_equal = df_combined_6month_group[df_combined_6month_group.value != df_combined_6month_group.value_cri]\n", "print(df_combined_6month_group_not_equal.shape)\n", "df_combined_6month_group_not_equal[(abs(df_combined_6month_group_not_equal.value - df_combined_6month_group_not_equal.value_cri) / df_combined_6month_group_not_equal.value < 0.5)]"]}, {"cell_type": "code", "execution_count": null, "id": "ad606562-2241-41a2-9238-b77b73bb39b9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "389e4df8-b8c0-4ed9-bb10-cd4e871ece3a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a8f6ac2e-da04-4b61-9433-62c36388e582", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c6d05fe0-839d-4c87-8586-f9c652af7c8d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3ecb0b16-f296-4e00-b2cc-67976dc73426", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "id": "62f734da-8916-4c80-8406-a0009014d57f", "metadata": {}, "outputs": [], "source": ["df_sales = df_without_sa[df_without_sa.segmentType == 'Sales']\n", "df_cri = df_without_sa[df_without_sa.segmentType == 'CRI']"]}, {"cell_type": "code", "execution_count": 25, "id": "ed77d8ef-4205-4a85-9f12-3763316f3319", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>productId</th>\n", "      <th>yearMonth</th>\n", "      <th>segmentType_cri</th>\n", "      <th>value_cri</th>\n", "      <th>segment_cri</th>\n", "      <th>segmentRank_cri</th>\n", "      <th>rankChange_cri</th>\n", "      <th>slope_cri</th>\n", "      <th>trend_cri</th>\n", "      <th>attribute_cri</th>\n", "      <th>segmentType_sales</th>\n", "      <th>value_sales</th>\n", "      <th>segment_sales</th>\n", "      <th>segmentRank_sales</th>\n", "      <th>rankChange_sales</th>\n", "      <th>slope_sales</th>\n", "      <th>trend_sales</th>\n", "      <th>attribute_sales</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1008.0</td>\n", "      <td>1012</td>\n", "      <td>2023-01</td>\n", "      <td>CRI</td>\n", "      <td>0.032347</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1008.0</td>\n", "      <td>1012</td>\n", "      <td>2023-02</td>\n", "      <td>CRI</td>\n", "      <td>0.027533</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1008.0</td>\n", "      <td>1012</td>\n", "      <td>2023-03</td>\n", "      <td>CRI</td>\n", "      <td>0.023460</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>-0.036803</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1008.0</td>\n", "      <td>1012</td>\n", "      <td>2023-04</td>\n", "      <td>CRI</td>\n", "      <td>0.019969</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>-0.031326</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1008.0</td>\n", "      <td>1012</td>\n", "      <td>2023-05</td>\n", "      <td>CRI</td>\n", "      <td>0.016944</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>-0.026989</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1876231</th>\n", "      <td>308171.0</td>\n", "      <td>1003</td>\n", "      <td>2023-08</td>\n", "      <td>CRI</td>\n", "      <td>0.000000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1876232</th>\n", "      <td>308171.0</td>\n", "      <td>1003</td>\n", "      <td>2023-09</td>\n", "      <td>CRI</td>\n", "      <td>0.000000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1876233</th>\n", "      <td>308171.0</td>\n", "      <td>1003</td>\n", "      <td>2023-10</td>\n", "      <td>CRI</td>\n", "      <td>0.000000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1876234</th>\n", "      <td>308171.0</td>\n", "      <td>1003</td>\n", "      <td>2023-11</td>\n", "      <td>CRI</td>\n", "      <td>0.000000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1876235</th>\n", "      <td>308171.0</td>\n", "      <td>1003</td>\n", "      <td>2023-12</td>\n", "      <td>CRI</td>\n", "      <td>0.609968</td>\n", "      <td>High</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>0.455050</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1876236 rows × 19 columns</p>\n", "</div>"], "text/plain": ["         accountId  productId yearMonth segmentType_cri  value_cri  \\\n", "0           1008.0       1012   2023-01             CRI   0.032347   \n", "1           1008.0       1012   2023-02             CRI   0.027533   \n", "2           1008.0       1012   2023-03             CRI   0.023460   \n", "3           1008.0       1012   2023-04             CRI   0.019969   \n", "4           1008.0       1012   2023-05             CRI   0.016944   \n", "...            ...        ...       ...             ...        ...   \n", "1876231   308171.0       1003   2023-08             CRI   0.000000   \n", "1876232   308171.0       1003   2023-09             CRI   0.000000   \n", "1876233   308171.0       1003   2023-10             CRI   0.000000   \n", "1876234   308171.0       1003   2023-11             CRI   0.000000   \n", "1876235   308171.0       1003   2023-12             CRI   0.609968   \n", "\n", "        segment_cri  segmentRank_cri  rankChange_cri  slope_cri trend_cri  \\\n", "0               Low                1             0.0   0.000000      Hold   \n", "1               Low                1             0.0   0.000000      Hold   \n", "2               Low                1             0.0  -0.036803      Hold   \n", "3               Low                1             0.0  -0.031326      Hold   \n", "4               Low                1             0.0  -0.026989      Hold   \n", "...             ...              ...             ...        ...       ...   \n", "1876231         Low                1             0.0   0.000000      Hold   \n", "1876232         Low                1             0.0   0.000000      Hold   \n", "1876233         Low                1             0.0   0.000000      Hold   \n", "1876234         Low                1             0.0   0.000000      Hold   \n", "1876235        High                3             2.0   0.455050  Increase   \n", "\n", "         attribute_cri segmentType_sales  value_sales segment_sales  \\\n", "0                  NaN               NaN          NaN           NaN   \n", "1                  NaN               NaN          NaN           NaN   \n", "2                  NaN               NaN          NaN           NaN   \n", "3                  NaN               NaN          NaN           NaN   \n", "4                  NaN               NaN          NaN           NaN   \n", "...                ...               ...          ...           ...   \n", "1876231            NaN               NaN          NaN           NaN   \n", "1876232            NaN               NaN          NaN           NaN   \n", "1876233            NaN               NaN          NaN           NaN   \n", "1876234            NaN               NaN          NaN           NaN   \n", "1876235            NaN               NaN          NaN           NaN   \n", "\n", "         segmentRank_sales  rankChange_sales  slope_sales trend_sales  \\\n", "0                      NaN               NaN          NaN         NaN   \n", "1                      NaN               NaN          NaN         NaN   \n", "2                      NaN               NaN          NaN         NaN   \n", "3                      NaN               NaN          NaN         NaN   \n", "4                      NaN               NaN          NaN         NaN   \n", "...                    ...               ...          ...         ...   \n", "1876231                NaN               NaN          NaN         NaN   \n", "1876232                NaN               NaN          NaN         NaN   \n", "1876233                NaN               NaN          NaN         NaN   \n", "1876234                NaN               NaN          NaN         NaN   \n", "1876235                NaN               NaN          NaN         NaN   \n", "\n", "         attribute_sales  \n", "0                    NaN  \n", "1                    NaN  \n", "2                    NaN  \n", "3                    NaN  \n", "4                    NaN  \n", "...                  ...  \n", "1876231              NaN  \n", "1876232              NaN  \n", "1876233              NaN  \n", "1876234              NaN  \n", "1876235              NaN  \n", "\n", "[1876236 rows x 19 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined = df_cri.merge(df_sales, on=['accountId', 'productId', 'yearMonth'], suffixes=['_cri', '_sales'], how='left')\n", "df_combined"]}, {"cell_type": "code", "execution_count": 26, "id": "90263a63-d2a1-4a7e-b2a6-6a7b080ffc74", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>productId</th>\n", "      <th>yearMonth</th>\n", "      <th>segmentType_cri</th>\n", "      <th>value_cri</th>\n", "      <th>segment_cri</th>\n", "      <th>segmentRank_cri</th>\n", "      <th>rankChange_cri</th>\n", "      <th>slope_cri</th>\n", "      <th>trend_cri</th>\n", "      <th>attribute_cri</th>\n", "      <th>segmentType_sales</th>\n", "      <th>value_sales</th>\n", "      <th>segment_sales</th>\n", "      <th>segmentRank_sales</th>\n", "      <th>rankChange_sales</th>\n", "      <th>slope_sales</th>\n", "      <th>trend_sales</th>\n", "      <th>attribute_sales</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5608</th>\n", "      <td>4006.0</td>\n", "      <td>1011</td>\n", "      <td>2023-05</td>\n", "      <td>CRI</td>\n", "      <td>0.027473</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>Sales</td>\n", "      <td>18538.891</td>\n", "      <td>Medium</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5609</th>\n", "      <td>4006.0</td>\n", "      <td>1011</td>\n", "      <td>2023-06</td>\n", "      <td>CRI</td>\n", "      <td>0.027473</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>Sales</td>\n", "      <td>18538.891</td>\n", "      <td>Medium</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5610</th>\n", "      <td>4006.0</td>\n", "      <td>1011</td>\n", "      <td>2023-07</td>\n", "      <td>CRI</td>\n", "      <td>0.027473</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>Sales</td>\n", "      <td>18538.891</td>\n", "      <td>Medium</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5611</th>\n", "      <td>4006.0</td>\n", "      <td>1011</td>\n", "      <td>2023-08</td>\n", "      <td>CRI</td>\n", "      <td>0.027473</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>Sales</td>\n", "      <td>18538.891</td>\n", "      <td>Medium</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5612</th>\n", "      <td>4006.0</td>\n", "      <td>1011</td>\n", "      <td>2023-09</td>\n", "      <td>CRI</td>\n", "      <td>0.027473</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>Sales</td>\n", "      <td>18538.891</td>\n", "      <td>Medium</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1842112</th>\n", "      <td>302782.0</td>\n", "      <td>1016</td>\n", "      <td>2023-05</td>\n", "      <td>CRI</td>\n", "      <td>0.000000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>Sales</td>\n", "      <td>39428.760</td>\n", "      <td>High</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1860892</th>\n", "      <td>305700.0</td>\n", "      <td>1016</td>\n", "      <td>2023-05</td>\n", "      <td>CRI</td>\n", "      <td>0.000000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>Sales</td>\n", "      <td>39428.760</td>\n", "      <td>High</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1860895</th>\n", "      <td>305700.0</td>\n", "      <td>1016</td>\n", "      <td>2023-08</td>\n", "      <td>CRI</td>\n", "      <td>0.000000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>Sales</td>\n", "      <td>26285.840</td>\n", "      <td>Medium</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>0.500812</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1860896</th>\n", "      <td>305700.0</td>\n", "      <td>1016</td>\n", "      <td>2023-09</td>\n", "      <td>CRI</td>\n", "      <td>0.000000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>Sales</td>\n", "      <td>26285.840</td>\n", "      <td>Medium</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.250406</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1860897</th>\n", "      <td>305700.0</td>\n", "      <td>1016</td>\n", "      <td>2023-10</td>\n", "      <td>CRI</td>\n", "      <td>0.000000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "      <td>Sales</td>\n", "      <td>26285.840</td>\n", "      <td>Medium</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5881 rows × 19 columns</p>\n", "</div>"], "text/plain": ["         accountId  productId yearMonth segmentType_cri  value_cri  \\\n", "5608        4006.0       1011   2023-05             CRI   0.027473   \n", "5609        4006.0       1011   2023-06             CRI   0.027473   \n", "5610        4006.0       1011   2023-07             CRI   0.027473   \n", "5611        4006.0       1011   2023-08             CRI   0.027473   \n", "5612        4006.0       1011   2023-09             CRI   0.027473   \n", "...            ...        ...       ...             ...        ...   \n", "1842112   302782.0       1016   2023-05             CRI   0.000000   \n", "1860892   305700.0       1016   2023-05             CRI   0.000000   \n", "1860895   305700.0       1016   2023-08             CRI   0.000000   \n", "1860896   305700.0       1016   2023-09             CRI   0.000000   \n", "1860897   305700.0       1016   2023-10             CRI   0.000000   \n", "\n", "        segment_cri  segmentRank_cri  rankChange_cri  slope_cri trend_cri  \\\n", "5608            Low                1             0.0        0.0      Hold   \n", "5609            Low                1             0.0        0.0      Hold   \n", "5610            Low                1             0.0        0.0      Hold   \n", "5611            Low                1             0.0        0.0      Hold   \n", "5612            Low                1             0.0        0.0      Hold   \n", "...             ...              ...             ...        ...       ...   \n", "1842112         Low                1             0.0        0.0      Hold   \n", "1860892         Low                1             0.0        0.0      Hold   \n", "1860895         Low                1             0.0        0.0      Hold   \n", "1860896         Low                1             0.0        0.0      Hold   \n", "1860897         Low                1             0.0        0.0      Hold   \n", "\n", "         attribute_cri segmentType_sales  value_sales segment_sales  \\\n", "5608               NaN             Sales    18538.891        Medium   \n", "5609               NaN             Sales    18538.891        Medium   \n", "5610               NaN             Sales    18538.891        Medium   \n", "5611               NaN             Sales    18538.891        Medium   \n", "5612               NaN             Sales    18538.891        Medium   \n", "...                ...               ...          ...           ...   \n", "1842112            NaN             Sales    39428.760          High   \n", "1860892            NaN             Sales    39428.760          High   \n", "1860895            NaN             Sales    26285.840        Medium   \n", "1860896            NaN             Sales    26285.840        Medium   \n", "1860897            NaN             Sales    26285.840        Medium   \n", "\n", "         segmentRank_sales  rankChange_sales  slope_sales trend_sales  \\\n", "5608                   2.0               0.0     0.000000        Hold   \n", "5609                   2.0               0.0     0.000000        Hold   \n", "5610                   2.0               0.0     0.000000        Hold   \n", "5611                   2.0               0.0     0.000000        Hold   \n", "5612                   2.0               0.0     0.000000        Hold   \n", "...                    ...               ...          ...         ...   \n", "1842112                3.0               0.0     0.000000        Hold   \n", "1860892                3.0               0.0     0.000000        Hold   \n", "1860895                2.0               1.0     0.500812    Increase   \n", "1860896                2.0               0.0     0.250406    Increase   \n", "1860897                2.0               0.0     0.000000        Hold   \n", "\n", "         attribute_sales  \n", "5608                 NaN  \n", "5609                 NaN  \n", "5610                 NaN  \n", "5611                 NaN  \n", "5612                 NaN  \n", "...                  ...  \n", "1842112              NaN  \n", "1860892              NaN  \n", "1860895              NaN  \n", "1860896              NaN  \n", "1860897              NaN  \n", "\n", "[5881 rows x 19 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined[(df_combined.segment_cri == 'Low') & (df_combined.segment_sales.notnull()) & (df_combined.segment_sales != 'Low')]"]}, {"cell_type": "code", "execution_count": null, "id": "b88aa757-758a-44e7-b543-b94143688b10", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}