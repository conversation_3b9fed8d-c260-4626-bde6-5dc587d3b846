import logging
from datetime import datetime

import pandas as pd

from abstract_model_factory.abstract_deployer import AbstractDeployer
from customer_journey.constants import Constants
from customer_journey.data import Data
from customer_journey.data_access_layer import DataAccessLayer

from sqlalchemy import create_engine

import requests
import os
import time

class Deployer(AbstractDeployer):

    def execute(self):
        self.write_predictions()
        #self.read_cj_output()
        self.write_results_to_aplv()

    def qa_module(self):
        pass

    # Save results to S3
    def write_predictions(self):
        data = Data.get_instance()
        final_cj_output = data.get_dataframe("final_cj_output")

        runmonth = data.get_param("runmonth")
        data_access_layer = DataAccessLayer()
        predictions_s3_dir = Constants.TARGET_S3_PATH + f"runmonth={runmonth}/"
        target_name = "customer_journey.parquet"
        data_access_layer.write_predictions_to_s3(final_cj_output, predictions_s3_dir, target_name)

    # Following is a convenience method to read the CJ output.
    # But this is not used now since we are going to write to RDS using the same df used to write to S3
    def read_cj_output(self):
        data = Data.get_instance()
        runmonth = data.get_param("runmonth")

        print("Reading CJ output")
        data_access_layer = DataAccessLayer()
        final_cj_output = data_access_layer.read_s3_parquet( Constants.TARGET_S3_PATH + f"runmonth={runmonth}/" + "customer_journey.parquet" )
        data.set_dataframe("final_cj_output", final_cj_output)

        return final_cj_output

    # Method to read possible segment-types.  Not used anymore
    def read_label_types(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")

        query = f"select lt.labelTypeId, lt.externalId, lt.labelTypeName, st.segmentType from {params['rds-enginedbname']}.LabelType lt, {params['rds-enginedbname']}_learning.CJ_SegmentType st where lt.externalId = st.labelTypeExternalId "
        df = DataAccessLayer.read_rds_table_as_pandas(query, params['rds-enginedbname'])
        return df

    # To create sqlalchemy engine to write pandas df
    def create_mysql_engine(self):
        if Constants.LOCAL_MODE:
            engine = create_engine(f"mysql+mysqlconnector://{Constants.rds_username}:{Constants.rds_password}@localhost:3337/{Constants.engine_db}")
        else:
            engine = create_engine(f"mysql+mysqlconnector://{Constants.rds_username}:{Constants.rds_password}@{Constants.rds_hostname}:{Constants.rds_port}/{Constants.engine_db}")

        return engine

    # To cleanup existing CJ output in RDS
    def delete_existing_cj_output(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        sql_query = f"DELETE cj_output FROM {params['rds-enginedbname']}_learning.CJ_Output_Segment AS cj_output "
        DataAccessLayer.executeMySql(sql_query, params['rds-enginedbname'])

    # To cleanup existing CJ related APLV records
    def delete_existing_aplv(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        sql_query = f"DELETE aplv FROM {params['rds-enginedbname']}.AccountProductLabelValue AS aplv " \
            f" INNER JOIN {params['rds-enginedbname']}.LabelType lt on lt.labelTypeId = aplv.labelTypeId "\
            f" INNER JOIN {params['rds-enginedbname']}_learning.CJ_SegmentType st on lt.externalId = st.labelTypeExternalId "
        DataAccessLayer.executeMySql(sql_query, params['rds-enginedbname'])

    # To insert new CJ related APLV records from CJ output table
    def insert_new_APLV(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")

        sql_query = "insert into AccountProductLabelValue (accountId, productId, labelTypeId, label, value, description, externalCandidateSignificance_std_akt, externalCandidateValue_std_akt, externalId) "\
            "select cj.accountId, cj.productId, lt.labelTypeId , date_format(segmentMonth,'%Y-%m-%d'), segmentRank, segment, value, 'Flat', concat(lt.labelTypeName, '~', a.externalId , '~', p.externalId, '~', segmentMonth) "\
            f"from {params['rds-enginedbname']}_learning.CJ_Output_Segment cj "\
            f"inner join {params['rds-enginedbname']}_learning.CJ_SegmentType st on cj.segmentType = st.segmentType "\
            "inner join LabelType lt on st.labelTypeExternalId = lt.externalId "\
            "inner join Account a on cj.accountId = a.accountId "\
            "inner join Product p on cj.productId = p.productId "
        DataAccessLayer.executeMySql(sql_query, params['rds-enginedbname'])

    def authenticate(self, ecosystem):

        aimurl = "aim"
        if ecosystem == 'aimqa':
            aimurl = "aimqa"
        if ecosystem == 'saasdev':
            aimurl = "aimdev"

        # Read DRL credentials from environment
        appId = os.environ.get("drl_app_id", "")
        appToken = os.environ.get("drl_app_token", "")

        url = f"https://{aimurl}-api.aktana.com/rbac-api/v1/authentication"
        data = { "client_id": appId, "client_secret": appToken, "grant_type": "client_credentials", "request_jwt_client_token": "true" }

        print (f"Calling AIM authentication url at: {url}")
        r = requests.post(url, json=data)
        #print (r.json())
        if r.status_code == 200:
            access_token = r.json().get('access_token')
            token_type = r.json().get('token_type')
        else:
            print(f"aim authentication response:{r.status_code}")
            print(r.text)
            raise Exception(f"aim authentication failed with response:{r.status_code}")

        #print(f"AIM authentication completed with access-token: Bearer {access_token}")
        return access_token, token_type

    def refreshDSERefData(self):
        data = Data.get_instance()

        # Read ecosystem/region/customer/env from commandline
        ecosystem = data.get_param("ecosystem", "")
        region = data.get_param("region", "")
        env = data.get_param("env", "")
        customer = data.get_param("customer", "")

        #https://<usdeveks>dse<prod>.aktana.com/<devbiogenna>/api/info?rcd=true
        #https://<region>dse<env>.aktana.com/<customer-instance>/api/info?rcd=true
        url = f"https://{region}dse{env}.aktana.com/{customer}/api/secure-info?rcd=true"

        access_token, token_type = self.authenticate(ecosystem)
        print (f"Calling DSE API to refresh custom datapoints cache at: {url}")
        r = requests.get(url, headers={'Authorization': 'Bearer {}'.format(access_token)})

        resp = None
        if r.status_code == 200:
            resp = r.text
        else:
            print(f"refreshDSERefData response:{r.status_code} from {url}")
            print(r.text)
            raise Exception(f"refreshDSERefData failed with response:{r.status_code}")

        return resp

    # Update label definitions based on most recent month/label
    def update_label_definitions(self, cj_output):
        data = Data.get_instance()
        params = data.get_param("connect_params")

        cj_last_month = cj_output.groupby(by=['segmentType']).aggregate(maxSegmentMonth=('segmentMonth','max')).reset_index()

        def update_label_definition(segmentType, maxSegmentMonth):
            maxSegmentMonthStr = maxSegmentMonth.strftime('%Y-%m-%d')
            print(f"Updating label definition for {segmentType} with label:{maxSegmentMonthStr}")
            sql_query = f"UPDATE {params['rds-enginedbname']}.LabelDefinition ld " \
                f" INNER JOIN {params['rds-enginedbname']}.LabelType lt on ld.labelTypeId =  lt.labelTypeId " \
                f" INNER JOIN {params['rds-enginedbname']}_learning.CJ_SegmentType st on lt.externalId = st.labelTypeExternalId " \
                f" SET ld.labelName = '{maxSegmentMonthStr}' " \
                f" WHERE st.segmentType = '{segmentType}' "
            DataAccessLayer.executeMySql(sql_query, params['rds-enginedbname'])

        cj_last_month.apply(lambda x: update_label_definition(x['segmentType'], x['maxSegmentMonth']), axis=1)

        # Refresh DSE for updated label-definitions
        self.refreshDSERefData()

    def build_scaled_output(self, final_cj_output):

        print("Computing scaledValues to persist")

        timestamp1 = time.time()
        final_cj_output['segmentMonth'] = pd.to_datetime(final_cj_output['yearMonth'] + "-01")
        final_cj_output = final_cj_output.drop(columns=['yearMonth'])

        product_min_max = final_cj_output.groupby(by=['productId', 'segmentType']).aggregate(minRawValue=('value','min'),maxRawValue=('value','max'), maxSegmentMonth=('segmentMonth','max')).reset_index()
        timestamp2 = time.time()
        print("Time taken(secs) for:Computing min/max per product:" + str(timestamp2 - timestamp1))

        final_cj_output = pd.merge(final_cj_output, product_min_max, on=['productId', 'segmentType'], how='inner')
        timestamp3 = time.time()
        print("Time taken(secs) for:Merging min/max with CJ output:" + str(timestamp3 - timestamp2))

        final_cj_output['scaledValue'] = final_cj_output.apply(lambda row: 1 if row['maxRawValue'] == row['minRawValue'] else (row['value'] - row['minRawValue'] )  / ( row['maxRawValue']  - row['minRawValue'] ), axis=1 )
        timestamp4 = time.time()
        print("Time taken(secs) for:Compute scaled value for CJ output:" + str(timestamp4 - timestamp3))
        final_cj_output['isCurrent'] = final_cj_output.apply(lambda row: 1 if row['maxSegmentMonth'] == row['segmentMonth'] else 0, axis=1 )
        final_cj_output = final_cj_output.drop(columns=['maxSegmentMonth'])

        timestamp5 = time.time()
        print("Time taken(secs) for:Compute isCurrent for CJ output:" + str(timestamp5 - timestamp4))

        return final_cj_output

    def write_bulk_data_to_db(self, df, table_name):

        csv_file_path = '/tmp/cjoutput.csv'
        df.to_csv(csv_file_path, index=False, header=False)
        data = Data.get_instance()
        params = data.get_param("connect_params")

        # Construct the LOAD DATA INFILE SQL statement
        load_sql = f"""
        LOAD DATA LOCAL INFILE '{csv_file_path}'
        REPLACE INTO TABLE {table_name}
        FIELDS TERMINATED BY ','
        OPTIONALLY ENCLOSED BY '"'
        LINES TERMINATED BY '\\n';
        """

        DataAccessLayer.executeMySql(load_sql, params['rds-enginedbname'] + '_learning')
        print("Data successfully loaded into MySQL using LOAD DATA LOCAL INFILE")


    # To write final CJ output to RDS (both to CJ output table in learning schema and to APLV)
    def write_results_to_aplv(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        starttime = time.time()

        print("Writing CJ results to RDS")
        final_cj_output = data.get_dataframe("final_cj_output")
        final_cj_output = final_cj_output.drop(columns=['slope'])

        final_cj_output = self.build_scaled_output(final_cj_output)
        timestamp0 = time.time()

        print("CJ output shape:" + str(final_cj_output.shape))
        final_cj_output.info()
        print (final_cj_output.head(15))

        self.update_label_definitions(final_cj_output)
        timestamp1 = time.time()
        print("Time taken(secs) for:Updating label definitions:" + str(timestamp1 - timestamp0))

        # Write CJ output to CJ output table in learning schema
        print("Deleting existing CJ output from RDS")
        self.delete_existing_cj_output()
        timestamp2 = time.time()
        print("Time taken(secs) for:Deleting exxisting CJ output:" + str(timestamp2 - timestamp1))
        engine = self.create_mysql_engine()
        print("Inserting new CJ output to RDS")
        #final_cj_output.to_sql('CJ_Output_Segment', schema=params['rds-enginedbname'] + '_learning', con=engine, if_exists='append', index=False, chunksize=50000)
        final_cj_output = final_cj_output[['accountId','productId','segmentMonth','segmentType','value','segment','segmentRank','rankChange','attribute','minRawValue','maxRawValue','scaledValue','isCurrent','trend']]
        self.write_bulk_data_to_db(final_cj_output, 'CJ_Output_Segment')
        timestamp3 = time.time()
        print("Time taken(secs) for:Inserting new CJ output:" + str(timestamp3 - timestamp2))

        # Replace data in APLV using new CJ output
        print("Deleting existing CJ-related APLV from RDS")
        self.delete_existing_aplv()
        timestamp4 = time.time()
        print("Time taken(secs) for:Deleting exxisting APLV:" + str(timestamp4 - timestamp3))
        print("Inserting new CJ output to APLV")
        self.insert_new_APLV()
        timestamp5 = time.time()
        print("Time taken(secs) for:Inserting new APLV:" + str(timestamp5 - timestamp4))

        print("Total Time taken(secs) for:Writing CJ results to RDS:" + str(timestamp5 - starttime))
