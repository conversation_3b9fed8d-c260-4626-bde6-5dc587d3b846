import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

df = pd.read_parquet("~/Downloads/customer_journey-3.parquet")
# df = df[df.potential_rank != 0]
# # segment_df = df.groupby(["yearMonth","potential_rank_change"])['accountId'].size().reset_index()
# # segment_df['accountId'] = np.log(segment_df['accountId'])
# # segment_df = segment_df.pivot(index='yearMonth', columns='potential_rank_change', values='accountId')
# segment_df = df.groupby(["yearMonth","potential_segmentation"])['accountId'].size().reset_index()
# segment_df = segment_df.pivot(index='yearMonth', columns='potential_segmentation', values='accountId')
# # segment_df = segment_df.drop(columns=[''])
# segment_df.plot(kind='line')
# plt.ylabel("# of acct-prod")
# plt.xlabel("year-month")
# plt.savefig("./cj_segment.png")
# # plt.savefig("./cj_segment.png")
# plt.show()
# print(segment_df)

"""
289149 1003
245420 1003
"""
test = df[df.potential_rank != 0].groupby(['accountId', 'productId'])['potential_rank'].nunique().reset_index([0,1])
# print(test[test.potential_rank > 2])

print(df[(df.accountId == 289149) & (df.productId == 1003)][['cri', 'sales', 'potential', 'potential_rank']])
t1 = df[(df.accountId == 289149) & (df.productId == 1003)][['yearMonth', 'potential_rank']]
t1.set_index('yearMonth', inplace=True)
t1.plot(kind='line')
plt.xlabel("year-month")
plt.ylabel("potential rank")
plt.title("Account 289149 Product 1003 potential rank over time")
plt.savefig('./cj_289149_1003.png')
plt.show()