{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2024-03-13T22:11:04.301704Z", "start_time": "2024-03-13T22:10:52.633341Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from scipy import stats"]}, {"cell_type": "code", "execution_count": 3, "outputs": [{"data": {"text/plain": "      accountId  productId yearMonth       cri cri_segmentation  cri_rank  \\\n5016       3911       1003   2023-01  0.227820              low       1.0   \n5017       3911       1003   2023-02  0.223808              low       1.0   \n5018       3911       1003   2023-03  0.220370              low       1.0   \n5019       3911       1003   2023-04  0.217390              low       1.0   \n5020       3911       1003   2023-05  0.214782              low       1.0   \n\n      cri_rank_change  sales sales_segmentation  sales_rank  \\\n5016              0.0  2.006                low         1.0   \n5017              0.0  2.010                low         1.0   \n5018              0.0  1.000                low         1.0   \n5019              0.0  1.000                low         1.0   \n5020              0.0  1.000                low         1.0   \n\n      sales_rank_change  potential  upper_bound potential_segmentation  \\\n5016                0.0   0.156620      2.00000                    low   \n5017                0.0   0.307724      4.00000                    low   \n5018                0.0   0.205325      2.66667                    low   \n5019                0.0   0.176868      2.33333                    low   \n5020                0.0   0.099688      1.33333                    low   \n\n      potential_rank  potential_rank_change  \n5016             1.0                    0.0  \n5017             1.0                    0.0  \n5018             1.0                    0.0  \n5019             1.0                    0.0  \n5020             1.0                    0.0  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>accountId</th>\n      <th>productId</th>\n      <th>yearMonth</th>\n      <th>cri</th>\n      <th>cri_segmentation</th>\n      <th>cri_rank</th>\n      <th>cri_rank_change</th>\n      <th>sales</th>\n      <th>sales_segmentation</th>\n      <th>sales_rank</th>\n      <th>sales_rank_change</th>\n      <th>potential</th>\n      <th>upper_bound</th>\n      <th>potential_segmentation</th>\n      <th>potential_rank</th>\n      <th>potential_rank_change</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>5016</th>\n      <td>3911</td>\n      <td>1003</td>\n      <td>2023-01</td>\n      <td>0.227820</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>2.006</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.156620</td>\n      <td>2.00000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>5017</th>\n      <td>3911</td>\n      <td>1003</td>\n      <td>2023-02</td>\n      <td>0.223808</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>2.010</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.307724</td>\n      <td>4.00000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>5018</th>\n      <td>3911</td>\n      <td>1003</td>\n      <td>2023-03</td>\n      <td>0.220370</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>1.000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.205325</td>\n      <td>2.66667</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>5019</th>\n      <td>3911</td>\n      <td>1003</td>\n      <td>2023-04</td>\n      <td>0.217390</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>1.000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.176868</td>\n      <td>2.33333</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>5020</th>\n      <td>3911</td>\n      <td>1003</td>\n      <td>2023-05</td>\n      <td>0.214782</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>1.000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.099688</td>\n      <td>1.33333</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["cj_parquet_path = '~/Downloads/customer_journey-5.parquet'\n", "df = pd.read_parquet(cj_parquet_path)\n", "df = df[df.potential_rank > 0]\n", "df.head()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-13T22:13:24.733653Z", "start_time": "2024-03-13T22:13:24.104017Z"}}, "id": "b718f65554610082"}, {"cell_type": "code", "execution_count": 5, "outputs": [{"data": {"text/plain": "         accountId  productId yearMonth       cri cri_segmentation  cri_rank  \\\n5026          3911       1003   2023-11  0.204116              low       1.0   \n5027          3911       1003   2023-12  0.202879              low       1.0   \n5441          3983       1012   2023-06  0.011779              low       1.0   \n5442          3983       1012   2023-07  0.009734              low       1.0   \n5443          3983       1012   2023-08  0.007905              low       1.0   \n...            ...        ...       ...       ...              ...       ...   \n2214619     333090       1013   2023-08  0.000000                        0.0   \n2214620     333090       1013   2023-09  0.000000                        0.0   \n2214621     333090       1013   2023-10  0.000000                        0.0   \n2214622     333090       1013   2023-11  0.000000                        0.0   \n2214623     333090       1013   2023-12  0.000000                        0.0   \n\n         cri_rank_change  sales sales_segmentation  sales_rank  \\\n5026                 0.0    0.0                low         1.0   \n5027                 0.0    0.0                low         1.0   \n5441                 0.0    0.0                low         1.0   \n5442                 0.0    0.0                low         1.0   \n5443                 0.0    1.0                low         1.0   \n...                  ...    ...                ...         ...   \n2214619              0.0    0.0                low         1.0   \n2214620              0.0    0.0                low         1.0   \n2214621              0.0    0.0                low         1.0   \n2214622              0.0    0.0                low         1.0   \n2214623              0.0    1.0                low         1.0   \n\n         sales_rank_change  potential  upper_bound potential_segmentation  \\\n5026                   0.0   0.023548      0.33333                    low   \n5027                   0.0   0.000000      0.00000                    low   \n5441                   0.0   0.004108      0.66667                    low   \n5442                   0.0   0.001718      0.33333                    low   \n5443                   0.0   0.000000      0.00000                    low   \n...                    ...        ...          ...                    ...   \n2214619                0.0   0.000000      0.00000                    low   \n2214620                0.0   0.000000      0.00000                    low   \n2214621                0.0   0.000000      0.00000                    low   \n2214622                0.0   0.000000      0.00000                    low   \n2214623                0.0   0.000000      0.00000                    low   \n\n         potential_rank  potential_rank_change  \n5026                1.0                    0.0  \n5027                1.0                    0.0  \n5441                1.0                    0.0  \n5442                1.0                    0.0  \n5443                1.0                    0.0  \n...                 ...                    ...  \n2214619             1.0                    0.0  \n2214620             1.0                    0.0  \n2214621             1.0                    0.0  \n2214622             1.0                    0.0  \n2214623             1.0                    0.0  \n\n[143854 rows x 16 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>accountId</th>\n      <th>productId</th>\n      <th>yearMonth</th>\n      <th>cri</th>\n      <th>cri_segmentation</th>\n      <th>cri_rank</th>\n      <th>cri_rank_change</th>\n      <th>sales</th>\n      <th>sales_segmentation</th>\n      <th>sales_rank</th>\n      <th>sales_rank_change</th>\n      <th>potential</th>\n      <th>upper_bound</th>\n      <th>potential_segmentation</th>\n      <th>potential_rank</th>\n      <th>potential_rank_change</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>5026</th>\n      <td>3911</td>\n      <td>1003</td>\n      <td>2023-11</td>\n      <td>0.204116</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.023548</td>\n      <td>0.33333</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>5027</th>\n      <td>3911</td>\n      <td>1003</td>\n      <td>2023-12</td>\n      <td>0.202879</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.000000</td>\n      <td>0.00000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>5441</th>\n      <td>3983</td>\n      <td>1012</td>\n      <td>2023-06</td>\n      <td>0.011779</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.004108</td>\n      <td>0.66667</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>5442</th>\n      <td>3983</td>\n      <td>1012</td>\n      <td>2023-07</td>\n      <td>0.009734</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.001718</td>\n      <td>0.33333</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>5443</th>\n      <td>3983</td>\n      <td>1012</td>\n      <td>2023-08</td>\n      <td>0.007905</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>1.0</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.000000</td>\n      <td>0.00000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2214619</th>\n      <td>333090</td>\n      <td>1013</td>\n      <td>2023-08</td>\n      <td>0.000000</td>\n      <td></td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.000000</td>\n      <td>0.00000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2214620</th>\n      <td>333090</td>\n      <td>1013</td>\n      <td>2023-09</td>\n      <td>0.000000</td>\n      <td></td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.000000</td>\n      <td>0.00000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2214621</th>\n      <td>333090</td>\n      <td>1013</td>\n      <td>2023-10</td>\n      <td>0.000000</td>\n      <td></td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.000000</td>\n      <td>0.00000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2214622</th>\n      <td>333090</td>\n      <td>1013</td>\n      <td>2023-11</td>\n      <td>0.000000</td>\n      <td></td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.000000</td>\n      <td>0.00000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2214623</th>\n      <td>333090</td>\n      <td>1013</td>\n      <td>2023-12</td>\n      <td>0.000000</td>\n      <td></td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>1.0</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>0.000000</td>\n      <td>0.00000</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n    </tr>\n  </tbody>\n</table>\n<p>143854 rows × 16 columns</p>\n</div>"}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df.upper_bound < 1]"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-13T22:13:44.386421Z", "start_time": "2024-03-13T22:13:44.347107Z"}}, "id": "6b311ed07108c4b3"}, {"cell_type": "code", "execution_count": 12, "outputs": [{"data": {"text/plain": "change_count\n1    1497\n2    1154\n3     501\n4     287\n5     103\n6      32\n7      12\n8       2\nName: accountId, dtype: int64"}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["changed = df[df.potential_rank_change != 0]\n", "segment_change_count = changed.groupby(['accountId', 'productId'])['yearMonth'].size().reset_index()\n", "segment_change_count = segment_change_count.rename(columns={'yearMonth': 'change_count'})\n", "segment_change_count_grouped = segment_change_count.groupby('change_count')['accountId'].size()\n", "segment_change_count_grouped"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-13T22:28:26.575484Z", "start_time": "2024-03-13T22:28:26.551859Z"}}, "id": "b1f2fb8b570e5082"}, {"cell_type": "code", "execution_count": 13, "outputs": [{"data": {"text/plain": "      accountId  productId  change_count\n63         6721       1025             6\n185        8719       1003             6\n315       16651       1003             7\n383       17804       1003             8\n424       19273       1003             6\n508       25515       1003             6\n538       26142       1003             7\n663       33762       1025             6\n823       36529       1025             7\n871       37794       1003             6\n890       43096       1003             6\n1001      45312       1003             6\n1115      52732       1003             6\n1134      53009       1003             6\n1160      53352       1003             6\n1182      53759       1003             6\n1199      54053       1003             6\n1244      54576       1003             6\n1371      62232       1012             7\n1427      63267       1003             6\n1437      63572       1010             8\n1487      64411       1008             7\n1553      71101       1003             6\n1721      73762       1003             7\n1848      81556       1003             6\n1918      82566       1003             7\n2044      90518       1008             6\n2101      91369       1003             6\n2137      91893       1003             6\n2272     100125       1003             6\n2306     100663       1003             6\n2335     101184       1003             7\n2505     109432       1003             6\n2516     109598       1003             6\n2523     109767       1003             6\n2556     110209       1003             7\n2661     112544       1003             6\n2722     118635       1003             6\n2780     119621       1003             6\n2904     126104       1025             7\n3057     167436       1025             6\n3218     177946       1025             6\n3232     178325       1025             7\n3259     179678       1025             6\n3547     288185       1003             6\n3555     288656       1024             7", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>accountId</th>\n      <th>productId</th>\n      <th>change_count</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>63</th>\n      <td>6721</td>\n      <td>1025</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>185</th>\n      <td>8719</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>315</th>\n      <td>16651</td>\n      <td>1003</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>383</th>\n      <td>17804</td>\n      <td>1003</td>\n      <td>8</td>\n    </tr>\n    <tr>\n      <th>424</th>\n      <td>19273</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>508</th>\n      <td>25515</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>538</th>\n      <td>26142</td>\n      <td>1003</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>663</th>\n      <td>33762</td>\n      <td>1025</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>823</th>\n      <td>36529</td>\n      <td>1025</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>871</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>890</th>\n      <td>43096</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1001</th>\n      <td>45312</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1115</th>\n      <td>52732</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1134</th>\n      <td>53009</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1160</th>\n      <td>53352</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1182</th>\n      <td>53759</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1199</th>\n      <td>54053</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1244</th>\n      <td>54576</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1371</th>\n      <td>62232</td>\n      <td>1012</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>1427</th>\n      <td>63267</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1437</th>\n      <td>63572</td>\n      <td>1010</td>\n      <td>8</td>\n    </tr>\n    <tr>\n      <th>1487</th>\n      <td>64411</td>\n      <td>1008</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>1553</th>\n      <td>71101</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1721</th>\n      <td>73762</td>\n      <td>1003</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>1848</th>\n      <td>81556</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>1918</th>\n      <td>82566</td>\n      <td>1003</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>2044</th>\n      <td>90518</td>\n      <td>1008</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2101</th>\n      <td>91369</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2137</th>\n      <td>91893</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2272</th>\n      <td>100125</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2306</th>\n      <td>100663</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2335</th>\n      <td>101184</td>\n      <td>1003</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>2505</th>\n      <td>109432</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2516</th>\n      <td>109598</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2523</th>\n      <td>109767</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2556</th>\n      <td>110209</td>\n      <td>1003</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>2661</th>\n      <td>112544</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2722</th>\n      <td>118635</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2780</th>\n      <td>119621</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>2904</th>\n      <td>126104</td>\n      <td>1025</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>3057</th>\n      <td>167436</td>\n      <td>1025</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>3218</th>\n      <td>177946</td>\n      <td>1025</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>3232</th>\n      <td>178325</td>\n      <td>1025</td>\n      <td>7</td>\n    </tr>\n    <tr>\n      <th>3259</th>\n      <td>179678</td>\n      <td>1025</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>3547</th>\n      <td>288185</td>\n      <td>1003</td>\n      <td>6</td>\n    </tr>\n    <tr>\n      <th>3555</th>\n      <td>288656</td>\n      <td>1024</td>\n      <td>7</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["segment_change_count[segment_change_count.change_count > 5]"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-13T22:28:39.636029Z", "start_time": "2024-03-13T22:28:39.625246Z"}}, "id": "f0abddcba1c0d2eb"}, {"cell_type": "code", "execution_count": 17, "outputs": [{"data": {"text/plain": "        accountId  productId yearMonth       cri cri_segmentation  cri_rank  \\\n310992      37794       1003   2023-01  0.662075             high       3.0   \n310993      37794       1003   2023-02  0.653135             high       3.0   \n310994      37794       1003   2023-03  0.667727             high       3.0   \n310995      37794       1003   2023-04  0.660058             high       3.0   \n310996      37794       1003   2023-05  0.653241             high       3.0   \n310997      37794       1003   2023-06  0.647142             high       3.0   \n310998      37794       1003   2023-07  0.641653             high       3.0   \n310999      37794       1003   2023-08  0.636686             high       3.0   \n311000      37794       1003   2023-09  0.632171             high       3.0   \n311001      37794       1003   2023-10  0.628049             high       3.0   \n311002      37794       1003   2023-11  0.624270             high       3.0   \n311003      37794       1003   2023-12  0.620793             high       3.0   \n\n        cri_rank_change   sales sales_segmentation  sales_rank  \\\n310992              0.0   3.294                low         1.0   \n310993              0.0   7.660             medium         2.0   \n310994              0.0  24.021               high         3.0   \n310995              0.0  22.218               high         3.0   \n310996              0.0  17.717               high         3.0   \n310997              0.0  24.278               high         3.0   \n310998              0.0  11.968               high         3.0   \n310999              0.0  21.940               high         3.0   \n311000              0.0  20.946               high         3.0   \n311001              0.0  16.648               high         3.0   \n311002              0.0  12.716               high         3.0   \n311003              0.0  26.812               high         3.0   \n\n        sales_rank_change  potential  upper_bound potential_segmentation  \\\n310992                0.0   2.264847      9.87100                 medium   \n310993                1.0   1.733814      7.66000                 medium   \n310994                1.0   2.567899     11.21034                 medium   \n310995                0.0   3.428987     14.98467                   high   \n310996                0.0   3.558895     15.55166                   high   \n310997                0.0   3.355851     14.81834                   high   \n310998                0.0   2.315803     10.32266                 medium   \n310999                0.0   3.089239     13.88867                   high   \n311000                0.0   2.856953     12.94500                 medium   \n311001                0.0   3.407191     15.54900                   high   \n311002                0.0   2.338947     10.74433                 medium   \n311003                0.0   2.892755     13.36900                 medium   \n\n        potential_rank  potential_rank_change  \n310992             2.0                    0.0  \n310993             2.0                    0.0  \n310994             2.0                    0.0  \n310995             3.0                    1.0  \n310996             3.0                    0.0  \n310997             3.0                    0.0  \n310998             2.0                   -1.0  \n310999             3.0                    1.0  \n311000             2.0                   -1.0  \n311001             3.0                    1.0  \n311002             2.0                   -1.0  \n311003             2.0                    0.0  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>accountId</th>\n      <th>productId</th>\n      <th>yearMonth</th>\n      <th>cri</th>\n      <th>cri_segmentation</th>\n      <th>cri_rank</th>\n      <th>cri_rank_change</th>\n      <th>sales</th>\n      <th>sales_segmentation</th>\n      <th>sales_rank</th>\n      <th>sales_rank_change</th>\n      <th>potential</th>\n      <th>upper_bound</th>\n      <th>potential_segmentation</th>\n      <th>potential_rank</th>\n      <th>potential_rank_change</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>310992</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-01</td>\n      <td>0.662075</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>3.294</td>\n      <td>low</td>\n      <td>1.0</td>\n      <td>0.0</td>\n      <td>2.264847</td>\n      <td>9.87100</td>\n      <td>medium</td>\n      <td>2.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>310993</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-02</td>\n      <td>0.653135</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>7.660</td>\n      <td>medium</td>\n      <td>2.0</td>\n      <td>1.0</td>\n      <td>1.733814</td>\n      <td>7.66000</td>\n      <td>medium</td>\n      <td>2.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>310994</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-03</td>\n      <td>0.667727</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>24.021</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>1.0</td>\n      <td>2.567899</td>\n      <td>11.21034</td>\n      <td>medium</td>\n      <td>2.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>310995</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-04</td>\n      <td>0.660058</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>22.218</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>3.428987</td>\n      <td>14.98467</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>1.0</td>\n    </tr>\n    <tr>\n      <th>310996</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-05</td>\n      <td>0.653241</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>17.717</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>3.558895</td>\n      <td>15.55166</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>310997</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-06</td>\n      <td>0.647142</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>24.278</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>3.355851</td>\n      <td>14.81834</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>310998</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-07</td>\n      <td>0.641653</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>11.968</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>2.315803</td>\n      <td>10.32266</td>\n      <td>medium</td>\n      <td>2.0</td>\n      <td>-1.0</td>\n    </tr>\n    <tr>\n      <th>310999</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-08</td>\n      <td>0.636686</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>21.940</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>3.089239</td>\n      <td>13.88867</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>1.0</td>\n    </tr>\n    <tr>\n      <th>311000</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-09</td>\n      <td>0.632171</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>20.946</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>2.856953</td>\n      <td>12.94500</td>\n      <td>medium</td>\n      <td>2.0</td>\n      <td>-1.0</td>\n    </tr>\n    <tr>\n      <th>311001</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-10</td>\n      <td>0.628049</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>16.648</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>3.407191</td>\n      <td>15.54900</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>1.0</td>\n    </tr>\n    <tr>\n      <th>311002</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-11</td>\n      <td>0.624270</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>12.716</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>2.338947</td>\n      <td>10.74433</td>\n      <td>medium</td>\n      <td>2.0</td>\n      <td>-1.0</td>\n    </tr>\n    <tr>\n      <th>311003</th>\n      <td>37794</td>\n      <td>1003</td>\n      <td>2023-12</td>\n      <td>0.620793</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>26.812</td>\n      <td>high</td>\n      <td>3.0</td>\n      <td>0.0</td>\n      <td>2.892755</td>\n      <td>13.36900</td>\n      <td>medium</td>\n      <td>2.0</td>\n      <td>0.0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df[(df.accountId == 37794) & (df.productId == 1003)]"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-13T22:34:22.980735Z", "start_time": "2024-03-13T22:34:22.954550Z"}}, "id": "5bfcb8f6a63e9e67"}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false}, "id": "302ad0fbb800bb21"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}