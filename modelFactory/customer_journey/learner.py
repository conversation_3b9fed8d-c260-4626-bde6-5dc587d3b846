import numpy as np
import pandas as pd
from abstract_model_factory.abstract_learner import AbstractLearner
from customer_journey.constants import Constants
from customer_journey.data import Data
from scipy import stats

BINS_DICT = {'sales_run': [0., 1.39583, 9.06366, float('inf')],
             'p_UB_run': [0., 7.39915, 46.77524, float('inf')],
             'p_Spec_run': [0., 1.23860103, 7.8803316, float('inf')],
             'p_CRI_run': [0., 2.94017214, 21.59782511, float('inf')],
             'p_Spec_CRI_run': [0., 0.49086102, 3.72128405, float('inf')],
             'sales_MktBkt_run': [0., 8.95012, 55.05744, float('inf')],
             'sales_share_run': [0., 0.2, 0.8, 1.]}


def get_bins(ser, bin_edges):
    ser = pd.cut(ser, bins=bin_edges, labels=['low', 'medium', 'high'])
    ser = ser.fillna('low')
    return ser.astype(str)


def get_slope(ser):
    try:
        ser.reset_index(drop=True)
        ser = ser.values
        if len(ser) < 6:
            return np.nan
        if np.nan in ser:
            return np.nan
        slope, intercept, r_value, p_value, std_err = stats.linregress(np.array([1, 2, 3, 4, 5, 6]), ser)
        return slope
    except:
        return np.nan


def compute_slope(series):
    # Extract x (month) and y (val) values from the series
    # series = series[series > 0]
    # x = series.index.values
    x = range(len(series))
    y = series.values

    # Perform linear regression and return the slope
    slope, _, _, _, _ = stats.linregress(x, y)
    return slope


def get_trend(row):
    if row['slope'] > 0.2:
        return 'Increase'
    elif row['slope'] < -0.2:
        return 'Decrease'
    elif row['rankChange'] > 0:
        return 'Increase'
    elif row['rankChange'] < 0:
        return 'Decrease'
    else:
        return 'Hold'


def get_slope_test(df):
    # fill df with all account-yearmonth comb.
    df['date'] = pd.to_datetime(df['date'], format="%Y%m")
    idx = pd.MultiIndex.from_product(
        [df['accountId'].unique(), pd.date_range(df['date'].min(), df['date'].max(), freq='MS')],
        names=['accountId', 'date'])
    df.set_index(['accountId', 'date'], inplace=True)
    df = df.reindex(idx)
    df = df.reset_index()
    df = df.fillna(-1)

    def compute_slope(series):
        # Extract x (month) and y (val) values from the series
        series = series[series > 0]
        x = series.index.values
        y = series.values

        # Perform linear regression and return the slope
        slope, _, _, _, _ = stats.linregress(x, y)
        return slope

    df['AP_slope'] = None
    for w in range(Constants.SLOPE_ROLLING_WINDOW_SIZE, 1, -1):
        df[f'slope_{w}'] = df.groupby('accountId')['AP_int'].rolling(window=w).apply(compute_slope,
                                                                                     raw=False).reset_index(
            level=0, drop=True)
        df['AP_slope'] = df['AP_slope'].combine_first(df[f'slope_{w}'])

    print(df)


class Learner(AbstractLearner):

    def get_bins(self, df, col, mapping):
        try:
            df[f'{col}_segmentation'] = pd.qcut(df[col], q=3, labels=['low', 'medium', 'high'])
        except ValueError:
            mask = df[col] != 0
            df.loc[mask, f'{col}_segmentation'] = pd.qcut(df[col].rank(method='first'), q=2, labels=['medium', 'high'])
            df[f'{col}_segmentation'] = df[f'{col}_segmentation'].astype(str)
            df.loc[~mask, f'{col}_segmentation'] = 'low'
        df[f'{col}_rank'] = df[f'{col}_segmentation'].map(mapping)
        df[f'{col}_segmentation'] = df[f'{col}_segmentation'].astype(str)
        df[f'{col}_rank'] = df[f'{col}_rank'].astype(int)
        df.sort_values(['accountId', 'productId', 'yearMonth'], inplace=True)
        df[f'{col}_rank_change'] = df.groupby(['accountId', 'productId'])[f'{col}_rank'].diff().fillna(0).astype(int)
        return df

    @staticmethod
    def get_bins_by_value(df, col, mapping):
        for prod in df['productId'].unique():
            min_value, max_value = df.loc[df.productId == prod, col].quantile(0.03), df.loc[df.productId == prod, col].quantile(0.97)
            bin_boundary = np.linspace(min_value, max_value, num=4)
            if bin_boundary[1] == bin_boundary[2]:
                bin_boundary[2] = bin_boundary[1] + 0.001
            print(f"{col} boundary for product {prod} : {bin_boundary[1:-1]}")
            bin_boundary[0], bin_boundary[-1] = -np.inf, np.inf
            df.loc[df.productId == prod, 'segment'] = pd.cut(df.loc[df.productId == prod, col], bins=bin_boundary, labels=['Low', 'Medium', 'High'])

        df['segmentRank'] = df['segment'].map(mapping)
        df['segment'] = df['segment'].astype(str)
        df['segmentRank'] = df['segmentRank'].astype(int)
        df.sort_values(['accountId', 'productId', 'yearMonth'], inplace=True)
        df['rankChange'] = df.groupby(['accountId', 'productId'])['segmentRank'].diff().fillna(0).astype(int)
        df['segmentType'] = col
        df = df.rename(columns={col: 'value'})
        return df

    def get_slope_and_trend(self, df):
        df['slope'] = df.groupby(['accountId', 'productId'])['value'].apply(
            lambda x: x.rolling(window=3).apply(compute_slope, raw=False))
        df['prod_seg_mean'] = df.groupby(['productId', 'segmentRank'])['value'].transform(lambda x: x[x != 0].mean())
        df['slope'] = df['slope'] / df['prod_seg_mean']
        df['slope'] = df['slope'].fillna(0)
        df['trend'] = df.apply(get_trend, axis=1)
        return df

    def apply_custom_influences(self, df, col):
        data = Data.get_instance()
        custom_influences = data.get_param("custom_influences")

        for view in custom_influences.get(col, []):
            infl_df = data.get_dataframe(view)
            matched_infl = df[['accountId', 'productId', 'yearMonth']].merge(infl_df, on=['accountId', 'productId'], how='left').query('yearMonth >= startMonth & yearMonth <= endMonth')
            # matched_infl = matched_infl.drop(['influenceValue', 'startMonth', 'endMonth'], axis=1)
            df = df.merge(matched_infl, on=['accountId', 'productId', 'yearMonth'], how='left')
            df['influenceValue'] = df['influenceValue'].fillna(1.0)
            df[col] = df[col] * df['influenceValue']
            df = df.drop(['influenceValue', 'startMonth', 'endMonth'], axis=1)

        return df

    def get_non_competitor_products(self):
        data = Data.get_instance()
        sales_df = data.get_dataframe("sales_df")
        non_competitor_products = sales_df[sales_df['iscompetitor'] == False]['productId'].unique().tolist()
        data.set_param("non_competitor_products", non_competitor_products)
        return non_competitor_products

    def execute(self):
        data = Data.get_instance()
        segment_rank_mapping = data.get_param("segment_rank_mapping")
        combined_segment_mapping_df = data.get_dataframe("combined_segment_mapping_df")
        final_df = pd.DataFrame(columns=['accountId', 'productId', 'yearMonth', 'segmentType', 'value', 'segment', 'segmentRank', 'rankChange', 'slope', 'trend', 'attribute'])
        # process CRI
        if not Constants.SKIP_CRI:
            print("Processing CRI...")
            cri_df = data.get_dataframe("cri_df")
            cri_df = cri_df.rename(columns={'cri': 'CRI'})
            cri_df = self.apply_custom_influences(cri_df, 'CRI')
            cri_bin_df = self.get_bins_by_value(cri_df, 'CRI', segment_rank_mapping['CRI'])
            cri_bin_df = self.get_slope_and_trend(cri_bin_df)
            cri_bin_df = cri_bin_df[['accountId', 'productId', 'yearMonth', 'segmentType', 'value', 'segment', 'segmentRank', 'rankChange', 'slope', 'trend']]
            final_df = pd.concat([final_df, cri_bin_df])
        # process Sales
        if not Constants.SKIP_SALES:
            print("Processing Sales...")
            sales_df = data.get_dataframe("sales_df")
            sales_df = sales_df.rename(columns={'sales': 'Sales'})
            sales_df = sales_df[['accountId', 'productId', 'yearMonth', 'Sales']]
            sales_bin_df = self.get_bins_by_value(sales_df, 'Sales', segment_rank_mapping['Sales'])
            sales_bin_df = self.get_slope_and_trend(sales_bin_df)
            sales_bin_df = sales_bin_df[['accountId', 'productId', 'yearMonth', 'segmentType', 'value', 'segment', 'segmentRank', 'rankChange', 'slope', 'trend']]
            final_df = pd.concat([final_df, sales_bin_df])
        # process Potential
        if not Constants.SKIP_POTENTIAL:
            print("Processing Potential...")
            potential_df = data.get_dataframe("potential_df")
            potential_df = potential_df.rename(columns={'potential': 'Potential'})
            potential_df = self.apply_custom_influences(potential_df, 'Potential')
            potential_bin_df = self.get_bins_by_value(potential_df, 'Potential', segment_rank_mapping['Potential'])
            potential_bin_df = self.get_slope_and_trend(potential_bin_df)
            potential_bin_df = potential_bin_df.rename(columns={'upper_bound': 'attribute'})
            potential_bin_df = potential_bin_df[['accountId', 'productId', 'yearMonth', 'segmentType', 'value', 'segment', 'segmentRank', 'rankChange', 'slope', 'trend', 'attribute']]
            final_df = pd.concat([final_df, potential_bin_df])

            # process combined segment
            print("Processing combined segment...")
            potential_combined_df = potential_bin_df[['accountId', 'productId', 'yearMonth', 'value', 'segment']]
            potential_combined_df = potential_combined_df.rename(columns={'value': 'potential', 'segment': 'potential_segment'})
            sales_combined_df = sales_bin_df[['accountId', 'productId', 'yearMonth', 'value', 'segment']]
            sales_combined_df = sales_combined_df.rename(columns={'value': 'sales', 'segment': 'sales_segment'})
            combined_df = potential_combined_df.merge(sales_combined_df, on=['accountId', 'productId', 'yearMonth'], how='inner')

            segment_weights_df = data.get_dataframe("segment_weights_df")
            combined_df = combined_df.merge(segment_weights_df, on=['productId'], how='left')
            combined_df['weight'] = combined_df['weight'].fillna(0.5)

            combined_df['value'] = combined_df['potential'] * combined_df['weight'] + combined_df['sales'] * (1 - combined_df['weight'])
            combined_df = pd.merge(combined_df, combined_segment_mapping_df, on=['sales_segment', 'potential_segment'], how='left')
            combined_df = combined_df.rename(columns={'combined_segment': 'segment', 'combined_rank': 'segmentRank'})
            combined_df['segmentRank'] = combined_df['segmentRank'].astype(int)
            combined_df.sort_values(['accountId', 'productId', 'yearMonth'], inplace=True)
            combined_df['rankChange'] = combined_df.groupby(['accountId', 'productId'])['segmentRank'].diff().fillna(0).astype(int)
            combined_df['segmentType'] = 'Combined'
            combined_df = self.get_slope_and_trend(combined_df)
            combined_df = combined_df[['accountId', 'productId', 'yearMonth', 'segmentType', 'value', 'segment', 'segmentRank', 'rankChange', 'slope', 'trend']]

            final_df = pd.concat([final_df, combined_df])

        final_df = final_df[['accountId', 'productId', 'yearMonth', 'segmentType', 'value', 'segment', 'segmentRank', 'rankChange', 'slope', 'trend', 'attribute']]
        data.set_dataframe("final_cj_output", final_df)


    def qa_module(self):
        pass
