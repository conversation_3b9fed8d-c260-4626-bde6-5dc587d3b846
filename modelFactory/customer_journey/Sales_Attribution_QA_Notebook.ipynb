{"cells": [{"cell_type": "code", "execution_count": 1, "id": "98cfe4b8-81b1-4660-a341-0fa52601eb0c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "116453fe-7b1a-4ed8-b283-0f2d094649b4", "metadata": {}, "outputs": [], "source": ["# enter the paths for account & brick level CJ parquet\n", "brick_parquet_path = ''\n", "account_parquet_path = ''\n", "# pick a product\n", "product = 1016"]}, {"cell_type": "code", "execution_count": 3, "id": "2698b732-0078-48ca-b05a-b31bd4217a2a", "metadata": {}, "outputs": [], "source": ["df_with_sa = pd.read_parquet(brick_parquet_path)\n", "df_without_sa = pd.read_parquet(account_parquet_path)"]}, {"cell_type": "markdown", "id": "232170d8-9471-4f6a-b0ae-2bd1f6e8c5cd", "metadata": {}, "source": ["## Check one sample account"]}, {"cell_type": "code", "execution_count": 4, "id": "1b9d3657-8d54-4ff5-b072-0596d6c16255", "metadata": {}, "outputs": [], "source": ["# pick only Actual Sales related rows\n", "df_without_sa_sales = df_without_sa[(df_without_sa.segmentType == 'Sales') & (df_without_sa.productId == product)]\n", "df_with_sa_sales = df_with_sa[(df_with_sa.segmentType == 'Sales') & (df_with_sa.productId == product)]"]}, {"cell_type": "code", "execution_count": 5, "id": "d507c7be-76b9-41ce-b7f5-2d0fadc91878", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Picked accountId: 84032.0\n"]}], "source": ["# randomly select an account from account level results\n", "sample_acct = df_without_sa_sales[df_without_sa_sales.value > 0].accountId.sample().values[0]\n", "print(\"Picked accountId: \" + str(sample_acct))"]}, {"cell_type": "code", "execution_count": 6, "id": "a64fd828-816d-415f-8f0b-8d5e4a0a9a56", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Actual account level sales: \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>productId</th>\n", "      <th>yearMonth</th>\n", "      <th>segmentType</th>\n", "      <th>value</th>\n", "      <th>segment</th>\n", "      <th>segmentRank</th>\n", "      <th>rankChange</th>\n", "      <th>slope</th>\n", "      <th>trend</th>\n", "      <th>attribute</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021868</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2023-05</td>\n", "      <td>Sales</td>\n", "      <td>39428.76</td>\n", "      <td>High</td>\n", "      <td>3</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021869</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2023-06</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>-2.0</td>\n", "      <td>0.000000</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021870</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2023-07</td>\n", "      <td>Sales</td>\n", "      <td>26285.84</td>\n", "      <td>Medium</td>\n", "      <td>2</td>\n", "      <td>1.0</td>\n", "      <td>-0.250406</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021871</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2023-08</td>\n", "      <td>Sales</td>\n", "      <td>13142.92</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>-1.0</td>\n", "      <td>0.501275</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021872</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2023-09</td>\n", "      <td>Sales</td>\n", "      <td>13142.92</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>-0.501275</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021873</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2023-10</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>-0.501275</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021874</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2023-11</td>\n", "      <td>Sales</td>\n", "      <td>13142.92</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021875</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2023-12</td>\n", "      <td>Sales</td>\n", "      <td>13142.92</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.501275</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021876</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2024-01</td>\n", "      <td>Sales</td>\n", "      <td>26285.84</td>\n", "      <td>Medium</td>\n", "      <td>2</td>\n", "      <td>1.0</td>\n", "      <td>0.250406</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021877</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2024-02</td>\n", "      <td>Sales</td>\n", "      <td>13142.92</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>-1.0</td>\n", "      <td>0.000000</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021878</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2024-03</td>\n", "      <td>Sales</td>\n", "      <td>13142.92</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>-0.501275</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021879</th>\n", "      <td>84032.0</td>\n", "      <td>1016</td>\n", "      <td>2024-04</td>\n", "      <td>Sales</td>\n", "      <td>0.00</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>-0.501275</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         accountId  productId yearMonth segmentType     value segment  \\\n", "2021868    84032.0       1016   2023-05       Sales  39428.76    High   \n", "2021869    84032.0       1016   2023-06       Sales      0.00     Low   \n", "2021870    84032.0       1016   2023-07       Sales  26285.84  Medium   \n", "2021871    84032.0       1016   2023-08       Sales  13142.92     Low   \n", "2021872    84032.0       1016   2023-09       Sales  13142.92     Low   \n", "2021873    84032.0       1016   2023-10       Sales      0.00     Low   \n", "2021874    84032.0       1016   2023-11       Sales  13142.92     Low   \n", "2021875    84032.0       1016   2023-12       Sales  13142.92     Low   \n", "2021876    84032.0       1016   2024-01       Sales  26285.84  Medium   \n", "2021877    84032.0       1016   2024-02       Sales  13142.92     Low   \n", "2021878    84032.0       1016   2024-03       Sales  13142.92     Low   \n", "2021879    84032.0       1016   2024-04       Sales      0.00     Low   \n", "\n", "         segmentRank  rankChange     slope     trend  attribute  \n", "2021868            3         0.0  0.000000      Hold        NaN  \n", "2021869            1        -2.0  0.000000  Decrease        NaN  \n", "2021870            2         1.0 -0.250406  Decrease        NaN  \n", "2021871            1        -1.0  0.501275  Increase        NaN  \n", "2021872            1         0.0 -0.501275  Decrease        NaN  \n", "2021873            1         0.0 -0.501275  Decrease        NaN  \n", "2021874            1         0.0  0.000000      Hold        NaN  \n", "2021875            1         0.0  0.501275  Increase        NaN  \n", "2021876            2         1.0  0.250406  Increase        NaN  \n", "2021877            1        -1.0  0.000000  Decrease        NaN  \n", "2021878            1         0.0 -0.501275  Decrease        NaN  \n", "2021879            1         0.0 -0.501275  Decrease        NaN  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"Actual account level sales: \")\n", "s1 = df_without_sa_sales[df_without_sa_sales.accountId == sample_acct]\n", "s1"]}, {"cell_type": "code", "execution_count": 7, "id": "bbcb7866-a689-4a3a-a1d8-1f701dee41ad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sales attribution: \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>productId</th>\n", "      <th>yearMonth</th>\n", "      <th>segmentType</th>\n", "      <th>value</th>\n", "      <th>segment</th>\n", "      <th>segmentRank</th>\n", "      <th>rankChange</th>\n", "      <th>slope</th>\n", "      <th>trend</th>\n", "      <th>attribute</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2051255</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2023-05</td>\n", "      <td>Sales</td>\n", "      <td>13142.92000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051256</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2023-06</td>\n", "      <td>Sales</td>\n", "      <td>13142.92000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051257</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2023-07</td>\n", "      <td>Sales</td>\n", "      <td>21904.86667</td>\n", "      <td>Medium</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0.219337</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051258</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2023-08</td>\n", "      <td>Sales</td>\n", "      <td>17523.89333</td>\n", "      <td>Medium</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.109668</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051259</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2023-09</td>\n", "      <td>Sales</td>\n", "      <td>13142.92000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-0.898760</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051260</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2023-10</td>\n", "      <td>Sales</td>\n", "      <td>0.00000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>-1.797521</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051261</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2023-11</td>\n", "      <td>Sales</td>\n", "      <td>4380.97333</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>-0.898760</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051262</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2023-12</td>\n", "      <td>Sales</td>\n", "      <td>13142.92000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1.348140</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051263</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2024-01</td>\n", "      <td>Sales</td>\n", "      <td>16428.65000</td>\n", "      <td>Medium</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0.301588</td>\n", "      <td>Increase</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051264</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2024-02</td>\n", "      <td>Sales</td>\n", "      <td>18619.13667</td>\n", "      <td>Medium</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.137086</td>\n", "      <td>Hold</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051265</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2024-03</td>\n", "      <td>Sales</td>\n", "      <td>9857.19000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-0.674070</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2051266</th>\n", "      <td>84032</td>\n", "      <td>1016</td>\n", "      <td>2024-04</td>\n", "      <td>Sales</td>\n", "      <td>9857.19000</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>-0.898760</td>\n", "      <td>Decrease</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         accountId  productId yearMonth segmentType        value segment  \\\n", "2051255      84032       1016   2023-05       Sales  13142.92000     Low   \n", "2051256      84032       1016   2023-06       Sales  13142.92000     Low   \n", "2051257      84032       1016   2023-07       Sales  21904.86667  Medium   \n", "2051258      84032       1016   2023-08       Sales  17523.89333  Medium   \n", "2051259      84032       1016   2023-09       Sales  13142.92000     Low   \n", "2051260      84032       1016   2023-10       Sales      0.00000     Low   \n", "2051261      84032       1016   2023-11       Sales   4380.97333     Low   \n", "2051262      84032       1016   2023-12       Sales  13142.92000     Low   \n", "2051263      84032       1016   2024-01       Sales  16428.65000  Medium   \n", "2051264      84032       1016   2024-02       Sales  18619.13667  Medium   \n", "2051265      84032       1016   2024-03       Sales   9857.19000     Low   \n", "2051266      84032       1016   2024-04       Sales   9857.19000     Low   \n", "\n", "         segmentRank  rankChange     slope     trend  attribute  \n", "2051255            1           0  0.000000      Hold        NaN  \n", "2051256            1           0  0.000000      Hold        NaN  \n", "2051257            2           1  0.219337  Increase        NaN  \n", "2051258            2           0  0.109668      Hold        NaN  \n", "2051259            1          -1 -0.898760  Decrease        NaN  \n", "2051260            1           0 -1.797521  Decrease        NaN  \n", "2051261            1           0 -0.898760  Decrease        NaN  \n", "2051262            1           0  1.348140  Increase        NaN  \n", "2051263            2           1  0.301588  Increase        NaN  \n", "2051264            2           0  0.137086      Hold        NaN  \n", "2051265            1          -1 -0.674070  Decrease        NaN  \n", "2051266            1           0 -0.898760  Decrease        NaN  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"Sales attribution: \")\n", "s3 = df_with_sa_sales[df_with_sa_sales.accountId == sample_acct]\n", "s3"]}, {"cell_type": "code", "execution_count": 8, "id": "3b349a06-47fe-4f8b-858d-ec3fff742942", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>value</th>\n", "      <th>value_with_sa</th>\n", "    </tr>\n", "    <tr>\n", "      <th>yearMonth</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-05</th>\n", "      <td>39428.76</td>\n", "      <td>13142.92000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06</th>\n", "      <td>0.00</td>\n", "      <td>13142.92000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-07</th>\n", "      <td>26285.84</td>\n", "      <td>21904.86667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08</th>\n", "      <td>13142.92</td>\n", "      <td>17523.89333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09</th>\n", "      <td>13142.92</td>\n", "      <td>13142.92000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10</th>\n", "      <td>0.00</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-11</th>\n", "      <td>13142.92</td>\n", "      <td>4380.97333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12</th>\n", "      <td>13142.92</td>\n", "      <td>13142.92000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01</th>\n", "      <td>26285.84</td>\n", "      <td>16428.65000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02</th>\n", "      <td>13142.92</td>\n", "      <td>18619.13667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03</th>\n", "      <td>13142.92</td>\n", "      <td>9857.19000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-04</th>\n", "      <td>0.00</td>\n", "      <td>9857.19000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              value  value_with_sa\n", "yearMonth                         \n", "2023-05    39428.76    13142.92000\n", "2023-06        0.00    13142.92000\n", "2023-07    26285.84    21904.86667\n", "2023-08    13142.92    17523.89333\n", "2023-09    13142.92    13142.92000\n", "2023-10        0.00        0.00000\n", "2023-11    13142.92     4380.97333\n", "2023-12    13142.92    13142.92000\n", "2024-01    26285.84    16428.65000\n", "2024-02    13142.92    18619.13667\n", "2024-03    13142.92     9857.19000\n", "2024-04        0.00     9857.19000"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df_value = s1[['yearMonth', 'value']].merge(s3[['yearMonth', 'value']], on='yearMonth', suffixes=['', '_with_sa'])\n", "combined_df_value = combined_df_value.set_index('yearMonth')\n", "combined_df_value"]}, {"cell_type": "code", "execution_count": 9, "id": "90fcd9c4-b464-49be-a476-5a0f43ed6659", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["combined_df_value.plot(kind='bar')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 25, "id": "0815def0-472e-4232-9285-df2b4da47f77", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>segmentRank</th>\n", "      <th>segmentRank_with_cri</th>\n", "    </tr>\n", "    <tr>\n", "      <th>yearMonth</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-05</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-07</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-11</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-04</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           segmentRank  segmentRank_with_cri\n", "yearMonth                                   \n", "2023-05              3                     1\n", "2023-06              1                     1\n", "2023-07              2                     2\n", "2023-08              1                     2\n", "2023-09              1                     1\n", "2023-10              1                     1\n", "2023-11              1                     1\n", "2023-12              1                     1\n", "2024-01              2                     2\n", "2024-02              1                     2\n", "2024-03              1                     1\n", "2024-04              1                     1"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df_seg = s1[['yearMonth', 'segmentRank']].merge(s3[['yearMonth', 'segmentRank']], on='yearMonth', suffixes=['', '_with_cri'])\n", "combined_df_seg = combined_df_seg.set_index('yearMonth')\n", "combined_df_seg"]}, {"cell_type": "code", "execution_count": 26, "id": "acfe8f99-746c-4c83-8693-ba600f3efb44", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAiMAAAHcCAYAAAAEBqrgAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAABErElEQVR4nO3dd3gVZeL28fsESCMFI4QECAEMndCLSVhBpGNBAXlBlyosKCuKFFmUokJ0kQUVpYjAorK6KGLXpRiQYqEEKcKqlCAkYIGEIgGS5/2DH0fPkk<PERSON>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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["combined_df_seg.plot(kind='bar')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "9586e98c-83f5-4e60-b37f-484d537f3303", "metadata": {}, "source": ["## Number of accounts in sales"]}, {"cell_type": "code", "execution_count": 12, "id": "c59a877f-1e60-4354-aee0-2bbda7e1c545", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Without SA, there are 2413 accounts in Sales\n"]}], "source": ["print(f\"Without SA, there are {len(df_without_sa_sales.accountId.unique())} accounts in Sales\")"]}, {"cell_type": "code", "execution_count": 13, "id": "757b3c26-6eb6-40b1-972e-28f6f3b59adb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["With SA, there are 6110 accounts in Sales\n"]}], "source": ["print(f\"With SA, there are {len(df_with_sa_sales.accountId.unique())} accounts in Sales\")"]}, {"cell_type": "markdown", "id": "5dd5a121-7f58-4c2a-9e80-f9ece64ce7e7", "metadata": {}, "source": ["## Number of rows with High segment"]}, {"cell_type": "code", "execution_count": 14, "id": "3cc2f024-09b4-414d-abfe-58c53753a99e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Without SA, there are 2149 rows with High Sales\n"]}], "source": ["print(f\"Without SA, there are {len(df_without_sa_sales[df_without_sa_sales.segment == 'High'])} rows with High Sales\")"]}, {"cell_type": "code", "execution_count": 15, "id": "93d23519-e6ee-4d50-9dd7-d2aa83e15cf2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["With SA, there are 1809 rows with High Sales\n"]}], "source": ["print(f\"With SA, there are {len(df_with_sa_sales[df_with_sa_sales.segment == 'High'])} rows with High Sales\")"]}, {"cell_type": "code", "execution_count": null, "id": "67f452e2-bcc4-4344-a758-9288d3c7d889", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "606b7146-4a3b-49a8-b164-d562b6077d85", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "fd0a2b86-9db7-4b9b-88f8-20cb5a119c50", "metadata": {}, "source": ["## 6 month sum of sales"]}, {"cell_type": "code", "execution_count": 16, "id": "af6be5e9-af68-4b5e-bc4e-b56a21351dda", "metadata": {}, "outputs": [], "source": ["start_month = '2023-07'\n", "end_month = '2023-12'"]}, {"cell_type": "code", "execution_count": 17, "id": "80f08172-67b4-494a-8e90-22205f410a9e", "metadata": {}, "outputs": [], "source": ["df_no_sa_6month = df_without_sa_sales[(df_without_sa_sales.yearMonth >= start_month) & (df_without_sa_sales.yearMonth <= end_month)][['accountId', 'yearMonth', 'value']]\n", "df_sa_6month = df_with_sa_sales[(df_with_sa_sales.yearMonth >= start_month) & (df_with_sa_sales.yearMonth <= end_month)][['accountId', 'yearMonth', 'value']]"]}, {"cell_type": "code", "execution_count": 18, "id": "bb8dac68-1457-43c2-a0e6-ca0794f8326b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/opt/anaconda3/envs/ml-python3/lib/python3.9/site-packages/pandas/core/reshape/merge.py:1203: RuntimeWarning: invalid value encountered in cast\n", "  if not (lk == lk.astype(rk.dtype))[~np.isnan(lk)].all():\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>yearMonth</th>\n", "      <th>value</th>\n", "      <th>value_sa</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4088.0</td>\n", "      <td>2023-07</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4088.0</td>\n", "      <td>2023-08</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4088.0</td>\n", "      <td>2023-09</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4088.0</td>\n", "      <td>2023-10</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4088.0</td>\n", "      <td>2023-11</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18989</th>\n", "      <td>307808.0</td>\n", "      <td>2023-11</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18990</th>\n", "      <td>307808.0</td>\n", "      <td>2023-12</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18991</th>\n", "      <td>308020.0</td>\n", "      <td>2023-12</td>\n", "      <td>NaN</td>\n", "      <td>128508.551111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18992</th>\n", "      <td>308095.0</td>\n", "      <td>2023-11</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18993</th>\n", "      <td>308095.0</td>\n", "      <td>2023-12</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18994 rows × 4 columns</p>\n", "</div>"], "text/plain": ["       accountId yearMonth  value       value_sa\n", "0         4088.0   2023-07    0.0       0.000000\n", "1         4088.0   2023-08    0.0       0.000000\n", "2         4088.0   2023-09    0.0       0.000000\n", "3         4088.0   2023-10    0.0       0.000000\n", "4         4088.0   2023-11    0.0       0.000000\n", "...          ...       ...    ...            ...\n", "18989   307808.0   2023-11    NaN       0.000000\n", "18990   307808.0   2023-12    NaN       0.000000\n", "18991   308020.0   2023-12    NaN  128508.551111\n", "18992   308095.0   2023-11    NaN       0.000000\n", "18993   308095.0   2023-12    NaN       0.000000\n", "\n", "[18994 rows x 4 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined_6month = df_no_sa_6month.merge(df_sa_6month, on=['accountId', 'yearMonth'], suffixes=['', '_sa'], how='outer')\n", "df_combined_6month"]}, {"cell_type": "code", "execution_count": 19, "id": "e8c78a5e-e54e-48e7-8ad0-dc8f1ef54303", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Real: There are 1146 accounts with sales.\n", "SA: Sales are distributed to 1601 accounts.\n", "Real > 0 and SA > 0: 817\n", "Real > 0 and SA = 0: 329\n", "Real = 0 and SA > 0: 784\n"]}], "source": ["df_combined_6month_group = df_combined_6month.groupby('accountId').sum().reset_index()\n", "print(f\"Real: There are {df_combined_6month_group[df_combined_6month_group.value > 0].shape[0]} accounts with sales.\")\n", "print(f\"SA: Sales are distributed to {df_combined_6month_group[df_combined_6month_group.value_sa > 0].shape[0]} accounts.\")\n", "print(f\"Real > 0 and SA > 0: {df_combined_6month_group[(df_combined_6month_group.value > 0) & (df_combined_6month_group.value_sa > 0)].shape[0]}\")\n", "print(f\"Real > 0 and SA = 0: {df_combined_6month_group[(df_combined_6month_group.value > 0) & (df_combined_6month_group.value_sa == 0)].shape[0]}\")\n", "print(f\"Real = 0 and SA > 0: {df_combined_6month_group[(df_combined_6month_group.value == 0) & (df_combined_6month_group.value_sa > 0)].shape[0]}\")"]}, {"cell_type": "code", "execution_count": 20, "id": "32ed3f17-2367-4175-868f-1e4df9d6af0e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>value</th>\n", "      <th>value_sa</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>5862.0</td>\n", "      <td>78857.52</td>\n", "      <td>48190.706650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>5918.0</td>\n", "      <td>78857.52</td>\n", "      <td>78857.520000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>5970.0</td>\n", "      <td>26285.84</td>\n", "      <td>26285.840000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>6042.0</td>\n", "      <td>26285.84</td>\n", "      <td>26285.840000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>6113.0</td>\n", "      <td>709717.68</td>\n", "      <td>587052.617110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3251</th>\n", "      <td>291706.0</td>\n", "      <td>13142.92</td>\n", "      <td>18490.246410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3258</th>\n", "      <td>294260.0</td>\n", "      <td>26285.84</td>\n", "      <td>26285.840000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3261</th>\n", "      <td>294672.0</td>\n", "      <td>39428.76</td>\n", "      <td>39428.760000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3276</th>\n", "      <td>297749.0</td>\n", "      <td>52571.68</td>\n", "      <td>37127.997117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3294</th>\n", "      <td>302071.0</td>\n", "      <td>223429.64</td>\n", "      <td>223429.640000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>513 rows × 3 columns</p>\n", "</div>"], "text/plain": ["      accountId      value       value_sa\n", "11       5862.0   78857.52   48190.706650\n", "16       5918.0   78857.52   78857.520000\n", "19       5970.0   26285.84   26285.840000\n", "23       6042.0   26285.84   26285.840000\n", "28       6113.0  709717.68  587052.617110\n", "...         ...        ...            ...\n", "3251   291706.0   13142.92   18490.246410\n", "3258   294260.0   26285.84   26285.840000\n", "3261   294672.0   39428.76   39428.760000\n", "3276   297749.0   52571.68   37127.997117\n", "3294   302071.0  223429.64  223429.640000\n", "\n", "[513 rows x 3 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined_6month_group_non_zero = df_combined_6month_group[df_combined_6month_group.value > 0]\n", "df_combined_6month_group_non_zero[(abs(df_combined_6month_group_non_zero.value - df_combined_6month_group_non_zero.value_sa) / df_combined_6month_group_non_zero.value < 0.5)]"]}, {"cell_type": "code", "execution_count": 21, "id": "859b47bd-ae50-40ee-9184-3b6243460825", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1654, 3)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>value</th>\n", "      <th>value_sa</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>5862.0</td>\n", "      <td>78857.52000</td>\n", "      <td>48190.706650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>6113.0</td>\n", "      <td>709717.68000</td>\n", "      <td>587052.617110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>6354.0</td>\n", "      <td>39435.33133</td>\n", "      <td>24566.206130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>6839.0</td>\n", "      <td>144572.12000</td>\n", "      <td>124864.311340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>7347.0</td>\n", "      <td>184000.88000</td>\n", "      <td>164189.373160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3054</th>\n", "      <td>250892.0</td>\n", "      <td>39428.76000</td>\n", "      <td>42853.206478</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3161</th>\n", "      <td>274214.0</td>\n", "      <td>13142.92000</td>\n", "      <td>19004.662320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3193</th>\n", "      <td>279046.0</td>\n", "      <td>13142.92000</td>\n", "      <td>9098.944615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3251</th>\n", "      <td>291706.0</td>\n", "      <td>13142.92000</td>\n", "      <td>18490.246410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3276</th>\n", "      <td>297749.0</td>\n", "      <td>52571.68000</td>\n", "      <td>37127.997117</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>237 rows × 3 columns</p>\n", "</div>"], "text/plain": ["      accountId         value       value_sa\n", "11       5862.0   78857.52000   48190.706650\n", "28       6113.0  709717.68000  587052.617110\n", "36       6354.0   39435.33133   24566.206130\n", "56       6839.0  144572.12000  124864.311340\n", "84       7347.0  184000.88000  164189.373160\n", "...         ...           ...            ...\n", "3054   250892.0   39428.76000   42853.206478\n", "3161   274214.0   13142.92000   19004.662320\n", "3193   279046.0   13142.92000    9098.944615\n", "3251   291706.0   13142.92000   18490.246410\n", "3276   297749.0   52571.68000   37127.997117\n", "\n", "[237 rows x 3 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined_6month_group_not_equal = df_combined_6month_group[df_combined_6month_group.value != df_combined_6month_group.value_sa]\n", "print(df_combined_6month_group_not_equal.shape)\n", "df_combined_6month_group_not_equal[(abs(df_combined_6month_group_not_equal.value - df_combined_6month_group_not_equal.value_sa) / df_combined_6month_group_not_equal.value < 0.5)]"]}, {"cell_type": "code", "execution_count": null, "id": "ad606562-2241-41a2-9238-b77b73bb39b9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}