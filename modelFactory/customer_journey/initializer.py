
import logging

import ast
import uuid
import datetime
import pandas as pd

from abstract_model_factory.abstract_initializer import AbstractInitializer
import sys
import getopt

from customer_journey.data import Data
from customer_journey.constants import Constants
from os import path
import json
import os
# from customer_journey.data_access_layer import DataAccessLayer
from aktana_ml_utils import aktana_ml_utils


class Initializer(AbstractInitializer):

    def read_param_file(self):
        """

        @return:
        """
        data = Data.get_instance()

        # print command line arguments
        #print("****** Command Line Parameters:",  sys.argv)

        short_opt = "hc:e:a:r:ec:o:l:d"
        long_opt = ["customer=", "env=", "app=", "region=", "ecosystem=", "json-output=", "local=", "use-debug=",
                    "scenario=", "loglevel=", "autocreate=", "runMode=", "postProcMode=", "journey=",
                    "useSCC=", "rundate=", "startdate=", "enddate=", "user=", "incrementalRun="]
        cmd_argv = sys.argv

        ak_ml_utils = aktana_ml_utils()
        cmdline_params, metadata_params = ak_ml_utils.initialize(cmd_argv, short_opt, long_opt, Constants.TEST_MODE)
        params = ak_ml_utils.get_params_json(Constants.TEST_MODE)
        params = json.loads(params)
        #print("Params:" + str(params))

        for key, value in cmdline_params.items():
            data.set_param(key, value)

        # If local is true set local constants
        if data.get_param("local", "") != "":
            Constants.LOCAL_MODE = True

        # If use-debug is true set debug constants
        if data.get_param("use-debug", "") != "":
            Constants.TEST_MODE = True

        if data.get_param("executionMode", "false") == "true":
            Constants.EXECUTION_MODE = True

        if data.get_param("rundate", "") != "":
            data.set_param("runmonth", data.get_param("rundate")[:-3])

        print("****** Command Line Parameters:" + str(data.params))

        data.set_param("connect_params", params)

        Constants.ACCESS_ID = params.get('adl-awsAccessKey')
        Constants.ACCESS_KEY = params.get('adl-awsAccessKey')
        Constants.SECRET_KEY = params.get('adl-awsSecretKey')

        Constants.DCO_S3_PATH = params.get('adl-dcoS3Location')
        Constants.ADL_S3_PATH = params.get('adl-adlS3Location')
        Constants.ADL_S3_PATH = Constants.ADL_S3_PATH.replace('s3:', 's3a:')
        Constants.DCO_S3_PATH = params.get('adl-dcoS3Location')
        # Constants.ADL_S3_PATH = params.get('adl-adlS3Location')
        Constants.S3_BASE_PATH = Constants.ADL_S3_PATH.split('adl/')[0]
        Constants.TARGET_S3_PATH = Constants.S3_BASE_PATH + 'customer-journey/'

        Constants.sw_account = params.get('snowflake-account')
        Constants.sw_user = params.get('snowflake-user')
        Constants.sw_password = params.get('snowflake-password')
        Constants.sw_database = params.get('snowflake-database')
        Constants.sw_schema = params.get('snowflake-schema')
        Constants.sw_role = params.get('snowflake-role')
        Constants.sw_warehouse = params.get('snowflake-warehouse')

        Constants.rds_hostname = params.get('rds-server')
        Constants.rds_port = params.get('rds-port') if params.get('rds-port') else 3306
        Constants.rds_username = params.get('rds-user')
        Constants.rds_password = params.get('rds-password')
        Constants.engine_db = params.get('rds-enginedbname')
        Constants.stage_db = params.get('rds-stagedbname')
        Constants.learning_db = params.get('rds-learningdbname')


    def execute(self):
        self.read_param_file()

        Constants.RUN_UID = str(uuid.uuid4())

        print("Initialized candidate generator with Run Uid- ", Constants.RUN_UID)

        #model = self.initialize_model_struct()

    def qa_module(self):
        pass
