import logging

from sklearn import preprocessing

from abstract_model_factory.abstract_transformer import AbstractTransformer
from customer_journey.constants import Constants
from customer_journey.data import Data
import pandas as pd
import numpy as np
from scipy import stats
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta

from customer_journey.qa_data_handler import QADataHandler
from customer_journey.learner import Learner

def custom_mean(group):
    if (group['adjustedRatio'] == 0).any():
        return 0
    else:
        return group['adjustedRatio'].mean()


class Transformer(AbstractTransformer):

    def printSummary(self, name, df, rowCount=10):

        print(f"Data frame: {name}:" + str(df.shape))
        print("*******************************")
        if Constants.DEBUG_MODE:
            df.info()
            print(df.head(rowCount))

    def qa_module(self):
        pass

    def build_sales_cri_df(self):
        data = Data.get_instance()
        cri_df = data.get_dataframe("cri_df")
        sales_basket_df = data.get_dataframe("sales_df")
        acct_spec_df = data.get_dataframe("acct_spec_df")

        # sales_basket_df = pd.merge(sales_df, basket_df, on=['accountId', 'productId', 'yearMonth'], how='outer')
        sales_basket_df['actual_sales'] = sales_basket_df['actual_sales'].fillna(0)
        sales_basket_df['basket_value'] = sales_basket_df['basket_value'].combine_first(sales_basket_df['actual_sales'])
        sales_basket_df = sales_basket_df.rename(columns={'basket_value': 'actual_mktbasket'})
        sales_basket_df['actual_share'] = sales_basket_df['actual_sales'] / sales_basket_df['actual_mktbasket']

        sales_cri_df = pd.merge(sales_basket_df, cri_df, on=['accountId', 'yearMonth', 'productId'], how='left')

        sales_cri_df = pd.merge(sales_cri_df, acct_spec_df, on=['accountId'], how='left')

        data.set_dataframe("sales_cri_df", sales_cri_df)

    def calc_potential_run(self, n_months=3):
        data = Data.get_instance()
        sales_cri = data.get_dataframe("sales_cri_df")

        # sort values by account and date
        sales_cri.sort_values(by=['accountId', 'productId', 'yearMonth'], inplace=True)
        # calc the rolling statistics for each accountID
        sales_cri['sales_run'] = np.around(
            sales_cri.groupby(['accountId', 'productId'])['actual_sales'].rolling(n_months).mean().reset_index([0, 1], drop=True),
            decimals=5)
        sales_cri['sales_run'] = sales_cri['sales_run'].combine_first(sales_cri['actual_sales'])
        sales_cri['sales_MktBkt_run'] = np.around(
            sales_cri.groupby(['accountId', 'productId'])['actual_mktbasket'].rolling(n_months).mean().reset_index([0, 1], drop=True),
            decimals=5)
        sales_cri['sales_MktBkt_run'] = sales_cri['sales_MktBkt_run'].combine_first(sales_cri['actual_mktbasket'])
        sales_cri['sales_share_run'] = np.around(sales_cri['sales_run'] / sales_cri['sales_MktBkt_run'], decimals=5)
        sales_cri['index_run'] = np.around(
            sales_cri.groupby(['accountId', 'productId'])['cri'].rolling(n_months).mean().reset_index([0,1], drop=True), decimals=5)
        sales_cri['index_run'] = sales_cri['index_run'].combine_first(sales_cri['cri'])
        # Calculate potential Upper Bound
        sales_cri['p_UB_run'] = np.around(sales_cri['sales_MktBkt_run'] - sales_cri['sales_run'], decimals=5)
        sales_cri[sales_cri.p_UB_run < 0]['p_UB_run'] = 0.0
        # Calculate specialty adjusted potential
        demo_mods = sales_cri.groupby(['specialty'], as_index=False).agg(
            {"actual_share": 'mean', 'actual_sales': 'size'})
        demo_mods.rename(columns={'actual_share': 'spec_modify'}, inplace=True)
        demo_mods.sort_values(by='spec_modify', inplace=True)
        sales_cri = sales_cri.merge(demo_mods.loc[:, ['specialty', 'spec_modify']], how='left', on='specialty')
        # Apply adjustments
        sales_cri['p_Spec_run'] = sales_cri['p_UB_run'] * sales_cri['spec_modify'].fillna(
            sales_cri['spec_modify'].mean())
        sales_cri['p_CRI_run'] = sales_cri['p_UB_run'] * sales_cri['index_run'].fillna(sales_cri['index_run'].mean())
        # Both CRI and Specialty Adjustment
        sales_cri['p_Spec_CRI_run'] = sales_cri['p_Spec_run'] * sales_cri['index_run'].fillna(
            sales_cri['index_run'].mean())
        # sales_cri = sales_cri.dropna(subset=['sales_run'])
        sales_cri = sales_cri.rename(columns={'p_Spec_CRI_run': 'potential', 'p_UB_run': 'upper_bound'})
        sales_cri = sales_cri.fillna({'potential': 0.0, 'upper_bound': 0.0})
        potential_df = sales_cri[['accountId', 'productId', 'yearMonth', 'potential', 'upper_bound']]

        data.set_dataframe("potential_df", potential_df)
        self.printSummary("potential_df", potential_df)

    def perform_sales_attribution(self):
        data = Data.get_instance()
        sales_df = data.get_dataframe("sales_df")
        acct_group_df = data.get_dataframe("acct_group_df")
        attr_share_df = data.get_dataframe("attr_share_df")
        cri_df = data.get_dataframe("cri_df")
        segment_rank_mapping = data.get_param("segment_rank_mapping")
        percentile_thres = data.get_param("percentile_thres")
        value_thres = data.get_param("value_thres")

        # add yearMonth as extra dimension
        sales_months = sales_df['yearMonth'].unique()
        sales_months_df = pd.DataFrame({'yearMonth': sales_months})
        acct_group_df = pd.merge(acct_group_df, sales_months_df, how='cross')

        # append cri to acct_group_df
        if Constants.SA_CRI_ADJUSTMENT:
            cri_df = cri_df.rename(columns={'cri': 'CRI'})
            cri_df = Learner(Constants.LEARNER_MODULE).get_bins_by_value(cri_df, 'CRI', segment_rank_mapping['CRI'])
            cri_df = cri_df.rename(columns={'segment': 'dimensionValue', 'segmentType': 'dimension'})
            cri_df = cri_df[['accountId', 'productId', 'yearMonth', 'dimension', 'dimensionValue']]
            acct_group_mapping = acct_group_df[['accountId', 'accountGroup']].drop_duplicates()
            cri_df = pd.merge(cri_df, acct_group_mapping, on=['accountId'])
            acct_group_df = pd.concat([acct_group_df, cri_df], axis=0)

        # join attribution share with exact match
        acct_group_share_exact = pd.merge(acct_group_df, attr_share_df, on=['dimension', 'dimensionValue', 'productId'], how='left', suffixes=('', '_y'))
        # join attribution share with 'Other'
        attr_share_df_other = attr_share_df[attr_share_df.dimensionValue == 'Other']
        acct_group_share_other = pd.merge(acct_group_df, attr_share_df_other, on=['productId', 'dimension'], how='left', suffixes=('', '_y'))
        acct_group_share_other['dimensionValue'] = 'Other'
        # combine exact and 'Other'
        acct_group_share_df = acct_group_share_exact.copy()
        acct_group_share_df['ratio'] = acct_group_share_df['ratio'].combine_first(acct_group_share_other['ratio'])
        acct_group_share_df['dimensionGroup'] = acct_group_share_df['dimensionGroup'].combine_first(acct_group_share_other['dimensionGroup'])
        acct_group_share_df = acct_group_share_df[acct_group_share_df.ratio.notnull()]

        acct_group_share_df = acct_group_share_df[['accountId', 'accountGroup', 'productId', 'yearMonth','dimension',
                                                   'dimensionValue', 'dimensionGroup',  'ratio']]

        # drop row if missing dimension
        acct_group_share_df['actualDimCnt'] = acct_group_share_df.groupby(['productId', 'yearMonth', 'accountId'])['dimension'].transform('nunique')
        acct_group_share_df['maxDimCnt'] = acct_group_share_df.groupby(['productId', 'yearMonth'])['dimension'].transform('nunique')
        acct_group_share_df = acct_group_share_df[acct_group_share_df['actualDimCnt'] == acct_group_share_df['maxDimCnt']]

        acct_group_share_df['groupCnt'] = acct_group_share_df.groupby(['productId', 'yearMonth', 'dimension', 'dimensionGroup', 'accountGroup'])['accountId'].transform('count')
        acct_group_share_df['ratio'] = acct_group_share_df['ratio'] / acct_group_share_df['groupCnt']
        acct_group_share_df['acctGroupSum'] = acct_group_share_df.groupby(['productId', 'accountGroup', 'dimension', 'yearMonth'])['ratio'].transform('sum')
        acct_group_share_df['adjustedRatio'] = acct_group_share_df['ratio'] / acct_group_share_df['acctGroupSum']
        acct_group_share_agg_df = acct_group_share_df.groupby(['accountId', 'productId', 'accountGroup', 'yearMonth']).apply(custom_mean).reset_index(name='adjustedRatio')
        acct_group_share_agg_df = acct_group_share_agg_df[acct_group_share_agg_df['adjustedRatio'] > 0]

        percentile_thres = acct_group_share_agg_df.groupby(['productId', 'accountGroup', 'yearMonth'])['adjustedRatio'].transform(lambda x: x.quantile(percentile_thres))
        acct_group_share_agg_df = acct_group_share_agg_df[~((acct_group_share_agg_df['adjustedRatio'] < percentile_thres) & (acct_group_share_agg_df['adjustedRatio'] < value_thres))]
        acct_group_share_agg_df['finalRatioSum'] = acct_group_share_agg_df.groupby(['productId', 'accountGroup', 'yearMonth'])['adjustedRatio'].transform('sum')
        acct_group_share_agg_df['adjustedRatio'] = acct_group_share_agg_df['adjustedRatio'] / acct_group_share_agg_df['finalRatioSum']


        sales_df = sales_df.rename(columns={'accountUID': 'accountGroup'})
        sales_df['accountGroup'] = sales_df['accountGroup'].astype(str)
        sales_df = sales_df.drop(columns=['accountId'])
        sales_df = pd.merge(sales_df, acct_group_share_agg_df, on=['productId', 'accountGroup', 'yearMonth'])
        sales_df['actual_sales'] = sales_df['actual_sales'] * sales_df['adjustedRatio']
        sales_df['basket_value'] = sales_df['basket_value'] * sales_df['adjustedRatio']

        sales_df.sort_values(['accountId', 'productId', 'yearMonth'], inplace=True)
        sales_df['sales'] = np.around(sales_df.groupby(['accountId', 'productId'])['actual_sales']
                                      .rolling(3).mean().reset_index([0, 1], drop=True), decimals=5)

        sales_df['sales'] = sales_df['sales'].combine_first(sales_df['actual_sales'])
        data.set_dataframe("sales_df", sales_df)


    def execute(self):
        if Constants.ENABLE_SALES_ATTRIBUTION:
            self.perform_sales_attribution()
        if not Constants.SKIP_POTENTIAL:
            self.build_sales_cri_df()
            self.calc_potential_run()



