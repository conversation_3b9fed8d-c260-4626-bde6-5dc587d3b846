import logging

from customer_journey.constants import Constants
from customer_journey.customer_journey_driver import CustomerJourneyDriver
from customer_journey.data import Data
from customer_journey.initializer import Initializer
from customer_journey.loader import Loader
from customer_journey.transformer import Transformer
from customer_journey.learner import Learner
# from customer_journey.predictor import Predictor
# from customer_journey.model_drift_detector import ModelDriftDetector
from customer_journey.deployer import Deployer
# from customer_journey.evaluator import Evaluator
import traceback
import statsd
import time
import argparse

expected_args = [
    {"name": "--customer", "default": "DEFAULT"},
    {"name": "--env", "default": "DEFAULT"},
    {"name": "--region", "default": "DEFAULT"},
    {"name": "--loglevel", "default": "30"}
]


def initialize_logger(loglevel_input):
    """
        CRITICAL = 50
        FATAL = CRITICAL
        ERROR = 40
        WARNING = 30
        WARN = WARNING
        INFO = 20
        DEBUG = 10
        NOTSET = 0
        """

    log_map = {"CRITICAL": logging.CRITICAL,
               "ERROR": logging.ERROR,
               "WARNING": logging.WARNING,
               "INFO": logging.INFO,
               "DEBUG": logging.DEBUG
               }

    data = Data.get_instance()
    logging.basicConfig(level=int(loglevel_input), format='%(asctime)s - %(levelname)s - %(message)s',
                        datefmt='%Y-%m-%d %H:%M:%S')

    logging.info("Starting the DRL Suggestion Candidate Generator")


def main():
    start_time = time.time()
    # get cmdline args
    parser = argparse.ArgumentParser(description='CP arguments')
    for arg in expected_args:
        thetype = arg.get('type')
        parser.add_argument(arg.get('name'), help=arg.get('help'), required=arg.get('required'),
                            default=arg.get('default'), type=thetype if thetype is None else locate(thetype))
    args, _ = parser.parse_known_args()

    initialize_logger(args.loglevel)

    rc = 0
    try:
        candidate_generator_driver = CustomerJourneyDriver(data=Data.get_instance(),
                                                           initializer=Initializer(Constants.INITIALIZER_MODULE),
                                                           loader=Loader(Constants.LOADER_MODULE),
                                                           transformer=Transformer(Constants.TRANSFORMER_MODULE),
                                                           # model_drift_detector=ModelDriftDetector(),
                                                           learner=Learner(Constants.LEARNER_MODULE),
                                                           # predictor=Predictor(Constants.PREDICTOR_MODULE),
                                                           # evaluator=Evaluator(Constants.EVALUATOR_MODULE),
                                                           deployer=Deployer(Constants.DEPLOYER_MODULE)
                                                           )

        # Execute the driver
        rc = candidate_generator_driver.start()

        exit(rc)

    except Exception as e:
        print(e)
        traceback.print_exc()
        rc = 1
    finally:
        end_time = time.time()
        exec_time = int(end_time - start_time)
        # write observability metrics
        statsd_server = args.region + "statsd.aktana.com"
        if "eks" not in args.region:
            statsd_server = args.region[:2] + "statsd.aktana.com"
        statsd_port = '9125'

        # metric prefix
        metric_prefix = 'type.{TYPE}.cmpny.' + args.customer + '.regn.' + args.region + '.cntry.none' + '.env.' + args.env + \
                        '.apptype.CP' + '.cmpnt.ENGINE' + '.metric'

        # gauge metrics
        statsd_client = statsd.StatsClient(statsd_server, statsd_port,
                                           prefix=metric_prefix.format(TYPE="g"))
        statsd_client.gauge("job_status", rc)
        print(f"Job status: {rc}. 0: SUCCESS, 1: FAIL")
        statsd_client.gauge("exec_time", exec_time)
        print(f"Execution time: {exec_time}")

        # counter metrics
        statsd_client = statsd.StatsClient(statsd_server, statsd_port,
                                           prefix=metric_prefix.format(TYPE="c"))
        if rc:
            statsd_client.incr("fail_cnt", 1)
            print("Failure count increased by 1")
        else:
            statsd_client.incr("success_cnt", 1)
            print("Success count increased by 1")

        exit(rc)


if __name__ == '__main__':
    main()
