
from abstract_model_factory.abstract_data_access_layer import AbstractDataAccessLayer

from customer_journey.constants import Constants
from customer_journey.data import Data
import mysql.connector
import boto3
import pandas as pd

import paramiko
from paramiko import SSHClient
from sshtunnel import SSHTunnelForwarder
import pymysql
import tempfile


def get_s3_client():
    ACCESS_KEY = Constants.AWS_ACCESS_KEY_ID
    SECRET_KEY = Constants.AWS_SECRET_ACCESS_KEY
    SESSION_TOKEN = Constants.AWS_SESSION_TOKEN

    if ACCESS_KEY != "":
        s3 = boto3.client('s3',
                          aws_access_key_id=ACCESS_KEY,
                          aws_secret_access_key=SECRET_KEY,
                          aws_session_token=SESSION_TOKEN)

    else:
        s3 = boto3.client('s3')

    return s3


class DataAccessLayer(AbstractDataAccessLayer):

    @staticmethod
    def write_predictions_to_s3(predictions, s3_path, target_name):
        s3_bucket = s3_path.split('/')[2]
        predictions_path = '/'.join(s3_path.split('/')[3:]) + target_name

        s3 = get_s3_client()

        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/{target_name}"
            predictions.to_parquet(temp_path, index=False)
            s3.upload_file(temp_path, s3_bucket, predictions_path)

        print("Uploaded predictions to : " + s3_path)

    @staticmethod
    def read_s3_parquet(s3_path):

        s3_bucket = s3_path.split('/')[2]
        parquet_path = '/'.join(s3_path.split('/')[3:])

        s3 = get_s3_client()
        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/output.parquet"
            try:
                s3.download_file(s3_bucket,parquet_path,temp_path)
            except botocore.exceptions.ClientError as e:
                if e.response['Error']['Code'] == "404":
                    logging.warning(f"The file object does not exist in S3 at path: {s3_path}")
                    return None
                elif e.response['Error']['Code'] == 403:
                    logging.warning(f"The file object not authorized to access in S3 at path: {s3_path}")
                    return None
                else:
                    # Something else has gone wrong.
                    raise

            parquet_df = pd.read_parquet(temp_path, engine='pyarrow')
            return parquet_df

    @staticmethod
    def getMySqlConnection(db):
        conn = None
        if Constants.USE_PARAMIKO:
            #print ("Using mysql ssh tunnel forwarder using" + Constants.ssh_host + " and " + str(Constants.ssh_port) + "for user:" + Constants.ssh_user)
            mypkey = paramiko.RSAKey.from_private_key_file(Constants.ssh_private_key)
            with SSHTunnelForwarder((Constants.ssh_host, Constants.ssh_port),
                                    ssh_username=Constants.ssh_user, ssh_pkey=mypkey,
                                    remote_bind_address=(Constants.rds_hostname,
                                                        Constants.rds_port)) as tunnel:
                conn = pymysql.connect(host='127.0.0.1', user=Constants.rds_username,
                                    passwd=Constants.rds_password,
                                    port=tunnel.local_bind_port, database=db, local_infile=True)

        if Constants.LOCAL_MODE:
            #print ("Using mysql ssh tunnel forwarder using localhost")
            conn = pymysql.connect(host='127.0.0.1', user=Constants.rds_username,
                                passwd=Constants.rds_password,
                                port=3337, database=db, local_infile=True)

        else:
            #print ("Using mysql direct connection")
            conn = pymysql.connect(host=Constants.rds_hostname, user=Constants.rds_username,
                                passwd=Constants.rds_password,
                                port=Constants.rds_port, database=db, local_infile=True)
        return conn

    @staticmethod
    def read_rds_table_as_pandas(sql, db=None):
        data = Data.get_instance()

        print("Reading df using sql: " + sql)
        conn = DataAccessLayer.getMySqlConnection(db)
        df = pd.read_sql(sql, conn)
        print(df)
        return df

    @staticmethod
    def executeMySql(
            sql, db=None
    ):

        print("Executing sql: " + sql)
        conn = DataAccessLayer.getMySqlConnection(db)

        try:
            cur = conn.cursor()
            cur.execute(sql)
            conn.commit()
            conn.close()
        except Exception as e:
            print('Error while executing mysql sql:' + str(e))
            raise
