{"cells": [{"cell_type": "code", "execution_count": 1, "id": "878e95c9a7d61cf4", "metadata": {"ExecuteTime": {"end_time": "2024-05-14T16:35:23.460026Z", "start_time": "2024-05-14T16:35:23.350655Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["import pyspark\n", "import os\n", "from pyspark.sql import SparkSession\n", "import findspark"]}, {"cell_type": "code", "execution_count": 2, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-05-14T16:35:25.240276Z", "start_time": "2024-05-14T16:35:25.176090Z"}}, "outputs": [], "source": ["def get_spark():\n", "\n", "    findspark.init('/Users/<USER>/Downloads/spark-3.3.0-bin-hadoop3')\n", "    os.environ['PYSPARK_SUBMIT_ARGS'] = \"--driver-memory 12g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.199,\" \\\n", "                                        \"org.apache.hadoop:hadoop-aws:3.3.1,io.delta:delta-core_2.12:1.0.0 \" \\\n", "                                        \"pyspark-shell \"\n", "\n", "    sc = pyspark.SparkContext()\n", "    sc.setSystemProperty(\"com.amazonaws.services.s3.enableV4\", \"true\")\n", "    sc.setLogLevel(\"WARN\")\n", "    hadoop_conf = sc._jsc.hadoopConfiguration()\n", "    hadoop_conf.set(\"fs.s3a.impl\", \"org.apache.hadoop.fs.s3a.S3AFileSystem\")\n", "    hadoop_conf.set(\"com.amazonaws.services.s3.enableV4\", \"true\")\n", "    # prod\n", "    # hadoop_conf.set(\"fs.s3a.access.key\", \"********************\")\n", "    # hadoop_conf.set(\"fs.s3a.secret.key\", \"4Goa3uN4lt00LBhs4YwQB5Q6a7zuiWTp/5CCRIl5\")\n", "    # saas-dev\n", "    hadoop_conf.set(\"fs.s3a.access.key\", \"********************\")\n", "    hadoop_conf.set(\"fs.s3a.secret.key\", \"iwRMBapQoFL/7WumtDY9q39BzEr+ubmlELGuI0x6\")\n", "    # hadoop_conf.set(\"fs.s3a.session.token\", Constants.AWS_SESSION_TOKEN)\n", "    hadoop_conf.set(\"fs.s3a.connection.maximum\", \"100000\")\n", "    # hadoop_conf.set(\"fs.s3a.endpoint\", \"s3.\" + constants.aws_region + \".amazonaws.com\")\n", "    hadoop_conf.set(\"fs.s3a.endpoint\", \"s3.amazonaws.com\")\n", "    hadoop_conf.set(\"delta.logRetentionDuration\", \"36500\")\n", "    hadoop_conf.set(\"delta.deletedFileRetentionDuration\", \"365\")\n", "\n", "    spark = SparkSession(sc) \\\n", "        .builder \\\n", "        .appName(\"CJ_Test\") \\\n", "        .config(\"spark.sql.extensions\", \"io.delta.sql.DeltaSparkSessionExtension\") \\\n", "        .config(\"spark.sql.catalog.spark_catalog\", \"org.apache.spark.sql.delta.catalog.DeltaCatalog\") \\\n", "        .config(\"spark.sql.debug.maxToStringFields\", 1000) \\\n", "        .getOrCreate()\n", "\n", "    print(\"Spark Initialized.\")\n", "    return spark"]}, {"cell_type": "code", "execution_count": 3, "id": "2d9306d9a0aa7fd3", "metadata": {"ExecuteTime": {"end_time": "2024-05-14T16:35:35.198862Z", "start_time": "2024-05-14T16:35:28.078064Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [":: loading settings :: url = jar:file:/Users/<USER>/Downloads/spark-3.3.0-bin-hadoop3/jars/ivy-2.5.0.jar!/org/apache/ivy/core/settings/ivysettings.xml\n"]}, {"name": "stderr", "output_type": "stream", "text": ["<PERSON> Default Cache set to: /Users/<USER>/.ivy2/cache\n", "The jars for the packages stored in: /Users/<USER>/.ivy2/jars\n", "com.amazonaws#aws-java-sdk-bundle added as a dependency\n", "org.apache.hadoop#hadoop-aws added as a dependency\n", "io.delta#delta-core_2.12 added as a dependency\n", ":: resolving dependencies :: org.apache.spark#spark-submit-parent-015e4119-847c-479e-b29e-c1ff816cf5b3;1.0\n", "\tconfs: [default]\n", "\tfound com.amazonaws#aws-java-sdk-bundle;1.11.199 in central\n", "\tfound org.apache.hadoop#hadoop-aws;3.3.1 in central\n", "\tfound com.amazonaws#aws-java-sdk-bundle;1.11.901 in central\n", "\tfound org.wildfly.openssl#wildfly-openssl;1.0.7.Final in central\n", "\tfound io.delta#delta-core_2.12;1.0.0 in central\n", "\tfound org.antlr#antlr4;4.7 in central\n", "\tfound org.antlr#antlr4-runtime;4.7 in central\n", "\tfound org.antlr#antlr-runtime;3.5.2 in central\n", "\tfound org.antlr#ST4;4.0.8 in central\n", "\tfound org.abego.treelayout#org.abego.treelayout.core;1.0.3 in central\n", "\tfound org.glassfish#javax.json;1.0.4 in central\n", "\tfound com.ibm.icu#icu4j;58.2 in central\n", ":: resolution report :: resolve 291ms :: artifacts dl 10ms\n", "\t:: modules in use:\n", "\tcom.amazonaws#aws-java-sdk-bundle;1.11.901 from central in [default]\n", "\tcom.ibm.icu#icu4j;58.2 from central in [default]\n", "\tio.delta#delta-core_2.12;1.0.0 from central in [default]\n", "\torg.abego.treelayout#org.abego.treelayout.core;1.0.3 from central in [default]\n", "\torg.antlr#ST4;4.0.8 from central in [default]\n", "\torg.antlr#antlr-runtime;3.5.2 from central in [default]\n", "\torg.antlr#antlr4;4.7 from central in [default]\n", "\torg.antlr#antlr4-runtime;4.7 from central in [default]\n", "\torg.apache.hadoop#hadoop-aws;3.3.1 from central in [default]\n", "\torg.glassfish#javax.json;1.0.4 from central in [default]\n", "\torg.wildfly.openssl#wildfly-openssl;1.0.7.Final from central in [default]\n", "\t:: evicted modules:\n", "\tcom.amazonaws#aws-java-sdk-bundle;1.11.199 by [com.amazonaws#aws-java-sdk-bundle;1.11.901] in [default]\n", "\t---------------------------------------------------------------------\n", "\t|                  |            modules            ||   artifacts   |\n", "\t|       conf       | number| search|dwnlded|evicted|| number|dwnlded|\n", "\t---------------------------------------------------------------------\n", "\t|      default     |   12  |   0   |   0   |   1   ||   11  |   0   |\n", "\t---------------------------------------------------------------------\n", ":: retrieving :: org.apache.spark#spark-submit-parent-015e4119-847c-479e-b29e-c1ff816cf5b3\n", "\tconfs: [default]\n", "\t0 artifacts copied, 11 already retrieved (0kB/6ms)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["24/05/14 09:37:39 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}, {"name": "stdout", "output_type": "stream", "text": ["24/05/14 09:37:40 WARN Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.\n", "24/05/14 09:37:43 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n", "Spark Initialized.\n"]}], "source": ["spark = get_spark()"]}, {"cell_type": "code", "execution_count": 8, "id": "7d7d2c427a30b176", "metadata": {"ExecuteTime": {"end_time": "2024-05-06T02:04:42.121041Z", "start_time": "2024-05-06T02:04:22.740949Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["24/05/05 19:04:23 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "24/05/05 19:04:23 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/05 19:04:23 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/05 19:04:23 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/05 19:04:24 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/05 19:04:24 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/05 19:04:24 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/05 19:04:24 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/05 19:04:24 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/05 19:04:24 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["adlx = spark.read.format(\"delta\").load(\"s3a://aktana-bdp-genentechca/prod/adl/data/silver/final_dataset/\")"]}, {"cell_type": "code", "execution_count": 9, "id": "84027da5e119c63b", "metadata": {"ExecuteTime": {"end_time": "2024-05-06T02:04:57.396483Z", "start_time": "2024-05-06T02:04:52.886037Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["adlh = spark.read.format(\"delta\").load(\"s3a://aktana-bdp-genentechca/prod/adl/data/silver/hcpFeatureStore/\")"]}, {"cell_type": "code", "execution_count": 18, "id": "91932b2f7025c81e", "metadata": {"ExecuteTime": {"end_time": "2024-05-06T02:38:30.620969Z", "start_time": "2024-05-06T02:36:06.093927Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/plain": ["31"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["email_open_adlx = adlx.filter(\"productIdList = 1003 and channel = 'SEND_CHANNEL' and interactionYearMonth = 202312 and emailOpened = 1 and interactionIsCompleted = '1' and interactionIsDeleted = '0'\").count()\n", "email_open_adlx"]}, {"cell_type": "code", "execution_count": 21, "id": "3614c7c03cb1f82b", "metadata": {"ExecuteTime": {"end_time": "2024-05-06T02:44:05.779850Z", "start_time": "2024-05-06T02:44:03.561432Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/plain": ["31"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["email_open_adlh = adlh.filter(\"productId = 1003 and yearMonth = 202312\").agg({\"emailOpen1MonthCount\": \"sum\"}).collect()[0][0]\n", "email_open_adlh"]}, {"cell_type": "code", "execution_count": 22, "id": "85b89ca553980f65", "metadata": {"ExecuteTime": {"end_time": "2024-05-06T02:45:25.433909Z", "start_time": "2024-05-06T02:45:25.401962Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["email_open_adlh == email_open_adlx"]}, {"cell_type": "markdown", "id": "1a78987e254a0081", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": []}, {"cell_type": "code", "execution_count": 22, "id": "b88c4c05b4c84ad7", "metadata": {"ExecuteTime": {"end_time": "2024-05-06T02:45:29.151625Z", "start_time": "2024-05-06T02:45:29.115384Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 25, "id": "c656db619759660d", "metadata": {"ExecuteTime": {"end_time": "2024-05-06T13:56:39.573959Z", "start_time": "2024-05-06T13:56:39.432042Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["acct_230255 = adlx.filter(\"accountId = 230255 and productIdList = 1003 and interactionYearMonth = 202303\")"]}, {"cell_type": "code", "execution_count": 24, "id": "f76464cb39301a1", "metadata": {"ExecuteTime": {"end_time": "2024-05-06T13:55:29.433560Z", "start_time": "2024-05-06T13:55:29.372366Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- recordclassId: string (nullable = true)\n", " |-- recordclass: string (nullable = true)\n", " |-- interactionId: string (nullable = true)\n", " |-- externalId: string (nullable = true)\n", " |-- interactionTimeZoneId: string (nullable = true)\n", " |-- interactionYear: string (nullable = true)\n", " |-- interactionMonth: string (nullable = true)\n", " |-- interactionDay: string (nullable = true)\n", " |-- interactionStartDateTime: string (nullable = true)\n", " |-- interactionStartDateLocal: string (nullable = true)\n", " |-- interactionWeekDayName: string (nullable = true)\n", " |-- interactionIsWeekend: string (nullable = true)\n", " |-- interactionDowInMonth: string (nullable = true)\n", " |-- interactionDayOfYear: string (nullable = true)\n", " |-- interactionWeekOfMonth: string (nullable = true)\n", " |-- interactionWeekOfYear: string (nullable = true)\n", " |-- interactionYearMonth: string (nullable = true)\n", " |-- interactionQuarter: string (nullable = true)\n", " |-- interactionTypeId: string (nullable = true)\n", " |-- interactionTypeName: string (nullable = true)\n", " |-- repActionTypeId: string (nullable = true)\n", " |-- repActionTypeName: string (nullable = true)\n", " |-- interactionDuration: string (nullable = true)\n", " |-- interactionWasCreatedFromSuggestion: string (nullable = true)\n", " |-- interactionIsCompleted: string (nullable = true)\n", " |-- interactionIsDeleted: string (nullable = true)\n", " |-- interactionCreatedAt: string (nullable = true)\n", " |-- interactionUpdatedAt: string (nullable = true)\n", " |-- interactionRepId: string (nullable = true)\n", " |-- interactionAccountId: string (nullable = true)\n", " |-- interactionRepAccount: string (nullable = true)\n", " |-- interactionFacilityId: string (nullable = true)\n", " |-- interactionAccountIsDeleted: string (nullable = true)\n", " |-- rawCopyStormId: string (nullable = true)\n", " |-- emailSentDate: string (nullable = true)\n", " |-- emailCaptureDatetime: string (nullable = true)\n", " |-- emailAccountUID: string (nullable = true)\n", " |-- accountEmail: string (nullable = true)\n", " |-- emailConfigValues: string (nullable = true)\n", " |-- emailSubject: string (nullable = true)\n", " |-- emailContent: string (nullable = true)\n", " |-- emailOpened: string (nullable = true)\n", " |-- emailOpenCount: string (nullable = true)\n", " |-- emailClickCount: string (nullable = true)\n", " |-- emailProductDisplay: string (nullable = true)\n", " |-- emailProduct: string (nullable = true)\n", " |-- emailSender: string (nullable = true)\n", " |-- emailStatus: string (nullable = true)\n", " |-- emailMessageId: string (nullable = true)\n", " |-- emailMessageChannelId: string (nullable = true)\n", " |-- emailMessageTopidId: string (nullable = true)\n", " |-- emailMessageTopicName: string (nullable = true)\n", " |-- emailMessageName: string (nullable = true)\n", " |-- emailMessageDescription: string (nullable = true)\n", " |-- emailCall2: string (nullable = true)\n", " |-- callDatetime: string (nullable = true)\n", " |-- callDate: string (nullable = true)\n", " |-- callAccountuid: string (nullable = true)\n", " |-- callAddressLine1: string (nullable = true)\n", " |-- callState: string (nullable = true)\n", " |-- callCity: string (nullable = true)\n", " |-- callZip4: string (nullable = true)\n", " |-- callZip: string (nullable = true)\n", " |-- callAddressLine2: string (nullable = true)\n", " |-- callNextCallNotes: string (nullable = true)\n", " |-- callType: string (nullable = true)\n", " |-- callAttendeeType: string (nullable = true)\n", " |-- callDetailedProducts: string (nullable = true)\n", " |-- callSubmittedByMobile: string (nullable = true)\n", " |-- productInteractionTypeId: string (nullable = true)\n", " |-- productInteractionTypeName: string (nullable = true)\n", " |-- messageId: string (nullable = true)\n", " |-- messageTopicId: string (nullable = true)\n", " |-- channel: string (nullable = true)\n", " |-- productIdList: string (nullable = true)\n", " |-- accountIdList: string (nullable = true)\n", " |-- reportedChannelId: string (nullable = true)\n", " |-- messageReaction: string (nullable = true)\n", " |-- physicalMessageDesc: string (nullable = true)\n", " |-- physicalMessageUID: string (nullable = true)\n", " |-- quantity: string (nullable = true)\n", " |-- suggestionReferenceId: string (nullable = true)\n", " |-- suggestionUID: string (nullable = true)\n", " |-- suggestedDate: string (nullable = true)\n", " |-- SuggestionWeekDayName: string (nullable = true)\n", " |-- SuggestionIsWeekend: string (nullable = true)\n", " |-- SuggestionDowInMonth: string (nullable = true)\n", " |-- SuggestionDayOfYear: string (nullable = true)\n", " |-- SuggestionWeekOfMonth: string (nullable = true)\n", " |-- SuggestionWeekOfYear: string (nullable = true)\n", " |-- suggestionYearMonth: string (nullable = true)\n", " |-- SuggestionQuarter: string (nullable = true)\n", " |-- suggestionChannel: string (nullable = true)\n", " |-- suggestionActionTaken: string (nullable = true)\n", " |-- suggestionDismissReason: string (nullable = true)\n", " |-- suggestionInteractionRaw: string (nullable = true)\n", " |-- suggestionInteractionId: string (nullable = true)\n", " |-- suggestionRepID: string (nullable = true)\n", " |-- suggestionAccountId: string (nullable = true)\n", " |-- suggestionRepAccount: string (nullable = true)\n", " |-- suggestionDetailRepActiontypeId: string (nullable = true)\n", " |-- suggestionDetailRepActionName: string (nullable = true)\n", " |-- suggestionMessageId: string (nullable = true)\n", " |-- suggestionMessageName: string (nullable = true)\n", " |-- suggestionRepTeamId: string (nullable = true)\n", " |-- suggestionProductId: string (nullable = true)\n", " |-- suggestedYear: string (nullable = true)\n", " |-- suggested<PERSON>onth: string (nullable = true)\n", " |-- suggestedDay: string (nullable = true)\n", " |-- dseRunId: long (nullable = true)\n", " |-- dseIsAccountCritical: boolean (nullable = true)\n", " |-- dseNumWorkDaysOverdue: string (nullable = true)\n", " |-- dseCurrentCompletionsPeriodTargetsProgressStatusId: string (nullable = true)\n", " |-- dseEvaluatedCompletionsPeriodTargetsProgressStatusId: string (nullable = true)\n", " |-- dseAllTargetsProgressText: string (nullable = true)\n", " |-- dseTargetDrivenEvaluationInfo: string (nullable = true)\n", " |-- dseRunStartDateTime: timestamp (nullable = true)\n", " |-- dseRunStartDateLocal: date (nullable = true)\n", " |-- dseNumSuggestibleReps: integer (nullable = true)\n", " |-- dseNumSuggestibleAccounts: integer (nullable = true)\n", " |-- dseNumEvaluatedAccounts: integer (nullable = true)\n", " |-- dseRunRepDateSuggestionId: string (nullable = true)\n", " |-- dseSuggestionPriorityScore: double (nullable = true)\n", " |-- dseIsSuggestionCritical: boolean (nullable = true)\n", " |-- dseSupersededByTrigger: boolean (nullable = true)\n", " |-- repId: integer (nullable = true)\n", " |-- accountId1: integer (nullable = true)\n", " |-- effectiveDate: date (nullable = true)\n", " |-- effectiveYearMonth: string (nullable = true)\n", " |-- repAccount: string (nullable = true)\n", " |-- effectiveYear: integer (nullable = true)\n", " |-- effectiveMonth: integer (nullable = true)\n", " |-- effectiveDay: integer (nullable = true)\n", " |-- repTeamId: integer (nullable = true)\n", " |-- cluster: string (nullable = true)\n", " |-- accountId: integer (nullable = true)\n", " |-- accountUid: string (nullable = true)\n", " |-- accountTypeId: integer (nullable = true)\n", " |-- facilityId: integer (nullable = true)\n", " |-- accountName: string (nullable = true)\n", " |-- configCountryCode: string (nullable = true)\n", " |-- isDeleted: boolean (nullable = true)\n", " |-- createdAt: timestamp (nullable = true)\n", " |-- updatedAt: timestamp (nullable = true)\n", " |-- claimsDataMonth: string (nullable = true)\n", " |-- cur1wSalesWeeklyDate: string (nullable = true)\n", " |-- cur6mCallCompleted: integer (nullable = true)\n", " |-- cur6mCallCompletedBem: integer (nullable = true)\n", " |-- cur6mCallCompletedCs: integer (nullable = true)\n", " |-- cur6mCallCompletedFrm: integer (nullable = true)\n", " |-- cur6mCallCompletedHd: integer (nullable = true)\n", " |-- cur6mCallCompletedIfs: integer (nullable = true)\n", " |-- cur6mCallCompletedMed: integer (nullable = true)\n", " |-- cur6mCallCompletedMsl: integer (nullable = true)\n", " |-- cur6mCallCompletedNel: integer (nullable = true)\n", " |-- departmentStd: string (nullable = true)\n", " |-- doNotCall: string (nullable = true)\n", " |-- emailcapturedStd: string (nullable = true)\n", " |-- facilityBedsStd: integer (nullable = true)\n", " |-- ispersonaccountStd: string (nullable = true)\n", " |-- kaiserFlag: string (nullable = true)\n", " |-- levelHCOStd: string (nullable = true)\n", " |-- pdrpoptoutStd: string (nullable = true)\n", " |-- personalEmailStd: string (nullable = true)\n", " |-- professionalEmailStd: string (nullable = true)\n", " |-- profileConsentStd: string (nullable = true)\n", " |-- salesDataMonth: string (nullable = true)\n", " |-- specialtiesStd: string (nullable = true)\n", " |-- specialties2Std: string (nullable = true)\n", " |-- squadPriority: string (nullable = true)\n", " |-- titleStd: string (nullable = true)\n", " |-- yearSinceGraduationStd: integer (nullable = true)\n", " |-- squadTarget: string (nullable = true)\n", " |-- accountNameLangStd: string (nullable = true)\n", " |-- accountNameAlphabetStd: string (nullable = true)\n", " |-- gnecustomEmail: string (nullable = true)\n", " |-- claimsDataYear: string (nullable = true)\n", " |-- gnespecialty1: string (nullable = true)\n", " |-- gnespecialty2: string (nullable = true)\n", " |-- gnespecialty3: string (nullable = true)\n", " |-- salesDataYear: string (nullable = true)\n", " |-- cur12mVabNoSampleSale: string (nullable = true)\n", " |-- updatedatyear: integer (nullable = true)\n", " |-- updatedatmonth: integer (nullable = true)\n", " |-- updatedatday: integer (nullable = true)\n", " |-- hcpGender: string (nullable = true)\n", " |-- hcpCred: string (nullable = true)\n", " |-- hcpSpec: string (nullable = true)\n", " |-- hcpSpec2: string (nullable = true)\n", " |-- tenure: string (nullable = true)\n", " |-- latitude: double (nullable = true)\n", " |-- longitude: double (nullable = true)\n", " |-- geoLocationString: string (nullable = true)\n", " |-- timeZoneId: string (nullable = true)\n", " |-- accountTypeName: string (nullable = true)\n", " |-- newColumnQA: string (nullable = true)\n", " |-- repName: string (nullable = true)\n", " |-- repTimeZoneId: string (nullable = true)\n", " |-- repIsActivated: boolean (nullable = true)\n", " |-- repSeConfigId: integer (nullable = true)\n", " |-- repAvgLatCurrentAccounts: double (nullable = true)\n", " |-- repAvgLongCurrentAccounts: double (nullable = true)\n", " |-- avgMilesCurrentAccounts: double (nullable = true)\n", " |-- repMaxMilesCurrentAccounts: double (nullable = true)\n", " |-- repNumCurrAccounts: integer (nullable = true)\n", " |-- repNumCurrAccountsWithLatLong: integer (nullable = true)\n", " |-- repNumCurrAccountsWithValidLatLong: integer (nullable = true)\n", " |-- repTypeName: string (nullable = true)\n", " |-- channelTypeName: string (nullable = true)\n", " |-- channelCategoryName: string (nullable = true)\n", " |-- activityDeliveryModeName: string (nullable = true)\n", " |-- suggestionDeliveryModeName: string (nullable = true)\n", " |-- reportedChannelName: string (nullable = true)\n", " |-- actionGroupName: string (nullable = true)\n", " |-- emailOpenScore: double (nullable = true)\n", " |-- tenureScoreStd: double (nullable = true)\n", " |-- visitScoreStd: double (nullable = true)\n", " |-- cadenceScoreStd: double (nullable = true)\n", " |-- suggestionVisitScore: double (nullable = true)\n", " |-- suggestionEmailScore: double (nullable = true)\n", " |-- targetAchievementScoreStd: double (nullable = true)\n", " |-- channelScore: double (nullable = true)\n", " |-- sumIndex: double (nullable = true)\n", " |-- index: double (nullable = true)\n", " |-- recordclassesId: string (nullable = true)\n", " |-- crossixExpiration: integer (nullable = true)\n", " |-- enspSampleNoSale: integer (nullable = true)\n", " |-- gneAddress: string (nullable = true)\n"]}], "source": ["acct_230255.printSchema()"]}, {"cell_type": "code", "execution_count": 29, "id": "50cb21df9f337673", "metadata": {"ExecuteTime": {"end_time": "2024-05-06T14:18:36.560689Z", "start_time": "2024-05-06T14:16:16.971670Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 75:====================================================>   (29 + 2) / 31]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["+-------------+-------------+-------------------------+---------------------+---------------------+--------------------+-------+\n", "|recordclassId|interactionId|interactionStartDateLocal|interactionRepAccount|interactionFacilityId|       gnespecialty1|hcpSpec|\n", "+-------------+-------------+-------------------------+---------------------+---------------------+--------------------+-------+\n", "|            6|       367950|               2023-03-03|          1793_230255|              1282933|    GENERAL PRACTICE|     GP|\n", "|            6|       286278|               2023-03-09|          1244_230255|               137958|ALLERGY AND IMMUN...|     GP|\n", "+-------------+-------------+-------------------------+---------------------+---------------------+--------------------+-------+\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["acct_230255.select(\"recordclassId\", \"interactionId\", \"interactionStartDateLocal\", \"interactionRepAccount\", \"interactionFacilityId\", \"gnespecialty1\", \"hcpSpec\").show()"]}, {"cell_type": "code", "execution_count": 29, "id": "7435e6f770edb207", "metadata": {"ExecuteTime": {"end_time": "2024-05-07T19:32:43.724500Z", "start_time": "2024-05-07T19:32:43.553273Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 30, "id": "7c989a861ea0d8f4", "metadata": {"ExecuteTime": {"end_time": "2024-05-07T19:33:06.977707Z", "start_time": "2024-05-07T19:32:58.182872Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 31, "id": "d85a28ccf31117f1", "metadata": {"ExecuteTime": {"end_time": "2024-05-07T20:52:56.978708Z", "start_time": "2024-05-07T20:52:53.961670Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["cj_df = pd.read_parquet(\"~/Downloads/customer_journey-7.parquet\")"]}, {"cell_type": "code", "execution_count": 32, "id": "6dddc86d4baa88b3", "metadata": {"ExecuteTime": {"end_time": "2024-05-07T20:53:02.917264Z", "start_time": "2024-05-07T20:53:01.470933Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>productId</th>\n", "      <th>yearMonth</th>\n", "      <th>segmentType</th>\n", "      <th>value</th>\n", "      <th>segment</th>\n", "      <th>segmentRank</th>\n", "      <th>rankChange</th>\n", "      <th>attribute</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1037</td>\n", "      <td>1003</td>\n", "      <td>2023-01</td>\n", "      <td>CRI</td>\n", "      <td>0.075692</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1037</td>\n", "      <td>1003</td>\n", "      <td>2023-02</td>\n", "      <td>CRI</td>\n", "      <td>0.068855</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1037</td>\n", "      <td>1003</td>\n", "      <td>2023-03</td>\n", "      <td>CRI</td>\n", "      <td>0.063262</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1037</td>\n", "      <td>1003</td>\n", "      <td>2023-04</td>\n", "      <td>CRI</td>\n", "      <td>0.058601</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1037</td>\n", "      <td>1003</td>\n", "      <td>2023-05</td>\n", "      <td>CRI</td>\n", "      <td>0.054657</td>\n", "      <td>Low</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3414247</th>\n", "      <td>321279</td>\n", "      <td>1009</td>\n", "      <td>2023-08</td>\n", "      <td>Combined</td>\n", "      <td>5725.400000</td>\n", "      <td>T5</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3414248</th>\n", "      <td>321279</td>\n", "      <td>1009</td>\n", "      <td>2023-09</td>\n", "      <td>Combined</td>\n", "      <td>5725.400000</td>\n", "      <td>T5</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3414249</th>\n", "      <td>321279</td>\n", "      <td>1009</td>\n", "      <td>2023-10</td>\n", "      <td>Combined</td>\n", "      <td>0.000000</td>\n", "      <td>T5</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3414250</th>\n", "      <td>321279</td>\n", "      <td>1009</td>\n", "      <td>2023-11</td>\n", "      <td>Combined</td>\n", "      <td>0.000000</td>\n", "      <td>T5</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3414251</th>\n", "      <td>321279</td>\n", "      <td>1009</td>\n", "      <td>2023-12</td>\n", "      <td>Combined</td>\n", "      <td>0.000000</td>\n", "      <td>T5</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3414252 rows × 9 columns</p>\n", "</div>"], "text/plain": ["         accountId  productId yearMonth segmentType        value segment  \\\n", "0             1037       1003   2023-01         CRI     0.075692     Low   \n", "1             1037       1003   2023-02         CRI     0.068855     Low   \n", "2             1037       1003   2023-03         CRI     0.063262     Low   \n", "3             1037       1003   2023-04         CRI     0.058601     Low   \n", "4             1037       1003   2023-05         CRI     0.054657     Low   \n", "...            ...        ...       ...         ...          ...     ...   \n", "3414247     321279       1009   2023-08    Combined  5725.400000      T5   \n", "3414248     321279       1009   2023-09    Combined  5725.400000      T5   \n", "3414249     321279       1009   2023-10    Combined     0.000000      T5   \n", "3414250     321279       1009   2023-11    Combined     0.000000      T5   \n", "3414251     321279       1009   2023-12    Combined     0.000000      T5   \n", "\n", "         segmentRank  rankChange  attribute  \n", "0                  1           0        NaN  \n", "1                  1           0        NaN  \n", "2                  1           0        NaN  \n", "3                  1           0        NaN  \n", "4                  1           0        NaN  \n", "...              ...         ...        ...  \n", "3414247            1           0        NaN  \n", "3414248            1           0        NaN  \n", "3414249            1           0        NaN  \n", "3414250            1           0        NaN  \n", "3414251            1           0        NaN  \n", "\n", "[3414252 rows x 9 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["cj_df"]}, {"cell_type": "code", "execution_count": 34, "id": "58312478dfbd9678", "metadata": {"ExecuteTime": {"end_time": "2024-05-07T21:05:19.145237Z", "start_time": "2024-05-07T21:05:17.686846Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["cj_ocr = cj_df[(cj_df.productId == 1016) & (cj_df.segmentType == 'CRI')]"]}, {"cell_type": "code", "execution_count": 35, "id": "ce75d52cbb5c566e", "metadata": {"ExecuteTime": {"end_time": "2024-05-07T21:07:25.333013Z", "start_time": "2024-05-07T21:07:21.432454Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["array([[<Axes: title={'center': 'value'}>]], dtype=object)"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cj_ocr[['value']].hist(bins=10)"]}, {"cell_type": "code", "execution_count": 37, "id": "85918b92300bcea", "metadata": {"ExecuteTime": {"end_time": "2024-05-07T21:10:48.289018Z", "start_time": "2024-05-07T21:10:48.215420Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["segment\n", "Low        103178\n", "Medium      38139\n", "High        35539\n", "Name: count, dtype: int64"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["cj_ocr[['segment']].value_counts()"]}, {"cell_type": "code", "execution_count": 40, "id": "c8a614506321cff8", "metadata": {"ExecuteTime": {"end_time": "2024-05-08T15:58:12.353928Z", "start_time": "2024-05-08T15:58:12.029223Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["array([[<Axes: title={'center': 'value'}>]], dtype=object)"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cj_ocr[cj_ocr.value > 0.0].groupby(\"accountId\").agg({\"value\": \"mean\"}).hist(bins=10)"]}, {"cell_type": "code", "execution_count": 3, "id": "f4f0274ecd641551", "metadata": {"ExecuteTime": {"end_time": "2024-05-14T16:33:38.380454Z", "start_time": "2024-05-14T16:33:38.375806Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "id": "41b94323204157a", "metadata": {"ExecuteTime": {"end_time": "2024-05-14T16:35:51.089857Z", "start_time": "2024-05-14T16:35:47.151797Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["24/05/14 09:38:05 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "24/05/14 09:38:05 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/14 09:38:05 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/14 09:38:05 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/14 09:38:05 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/14 09:38:05 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/14 09:38:05 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/14 09:38:05 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/14 09:38:05 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n", "24/05/14 09:38:05 WARN BasicProfileConfigLoader: Your profile name includes a 'profile ' prefix. This is considered part of the profile name in the Java SDK, so you will need to include this prefix in your profile name when you reference this profile from your Java code.\n"]}, {"ename": "Py4JJavaError", "evalue": "An error occurred while calling o32.load.\n: java.lang.NoSuchMethodError: org.apache.spark.sql.AnalysisException.<init>(Ljava/lang/String;Lscala/Option;Lscala/Option;Lscala/Option;Lscala/Option;)V\n\tat org.apache.spark.sql.delta.DeltaErrors$.notADeltaTableException(DeltaErrors.scala:260)\n\tat org.apache.spark.sql.delta.catalog.DeltaTableV2.toBaseRelation(DeltaTableV2.scala:142)\n\tat org.apache.spark.sql.delta.sources.DeltaDataSource.createRelation(DeltaDataSource.scala:177)\n\tat org.apache.spark.sql.execution.datasources.DataSource.resolveRelation(DataSource.scala:350)\n\tat org.apache.spark.sql.DataFrameReader.loadV1Source(DataFrameReader.scala:228)\n\tat org.apache.spark.sql.DataFrameReader.$anonfun$load$2(DataFrameReader.scala:210)\n\tat scala.Option.getOrElse(Option.scala:189)\n\tat org.apache.spark.sql.DataFrameReader.load(DataFrameReader.scala:210)\n\tat org.apache.spark.sql.DataFrameReader.load(DataFrameReader.scala:185)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tat java.lang.reflect.Method.invoke(Method.java:498)\n\tat py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:244)\n\tat py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:357)\n\tat py4j.Gateway.invoke(Gateway.java:282)\n\tat py4j.commands.AbstractCommand.invokeMethod(AbstractCommand.java:132)\n\tat py4j.commands.CallCommand.execute(CallCommand.java:79)\n\tat py4j.ClientServerConnection.waitForCommands(ClientServerConnection.java:182)\n\tat py4j.ClientServerConnection.run(ClientServerConnection.java:106)\n\tat java.lang.Thread.run(Thread.java:748)\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mPy4JJavaError\u001b[0m                             Traceback (most recent call last)", "Cell \u001b[0;32mIn[4], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m adl \u001b[38;5;241m=\u001b[39m \u001b[43mspark\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mformat\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m<PERSON><PERSON>\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43ms3a://aktana-bdp-devgenentechca/dev/adl/data/final_dataset/\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/opt/anaconda3/envs/ct-env-new/lib/python3.9/site-packages/pyspark/sql/readwriter.py:177\u001b[0m, in \u001b[0;36mDataFrameReader.load\u001b[0;34m(self, path, format, schema, **options)\u001b[0m\n\u001b[1;32m    175\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions)\n\u001b[1;32m    176\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(path, \u001b[38;5;28mstr\u001b[39m):\n\u001b[0;32m--> 177\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_df(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jreader\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    178\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m path \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    179\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mtype\u001b[39m(path) \u001b[38;5;241m!=\u001b[39m \u001b[38;5;28mlist\u001b[39m:\n", "File \u001b[0;32m~/opt/anaconda3/envs/ct-env-new/lib/python3.9/site-packages/py4j/java_gateway.py:1321\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1315\u001b[0m command \u001b[38;5;241m=\u001b[39m proto\u001b[38;5;241m.\u001b[39mCALL_COMMAND_NAME \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1316\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_header \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1317\u001b[0m     args_command \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1318\u001b[0m     proto\u001b[38;5;241m.\u001b[39mEND_COMMAND_PART\n\u001b[1;32m   1320\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgateway_client\u001b[38;5;241m.\u001b[39msend_command(command)\n\u001b[0;32m-> 1321\u001b[0m return_value \u001b[38;5;241m=\u001b[39m \u001b[43mget_return_value\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1322\u001b[0m \u001b[43m    \u001b[49m\u001b[43manswer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgateway_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtarget_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1324\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m temp_arg \u001b[38;5;129;01min\u001b[39;00m temp_args:\n\u001b[1;32m   1325\u001b[0m     temp_arg\u001b[38;5;241m.\u001b[39m_detach()\n", "File \u001b[0;32m~/opt/anaconda3/envs/ct-env-new/lib/python3.9/site-packages/pyspark/sql/utils.py:190\u001b[0m, in \u001b[0;36mcapture_sql_exception.<locals>.deco\u001b[0;34m(*a, **kw)\u001b[0m\n\u001b[1;32m    188\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdeco\u001b[39m(\u001b[38;5;241m*\u001b[39ma: Any, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkw: Any) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Any:\n\u001b[1;32m    189\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 190\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkw\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    191\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m Py4JJavaError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    192\u001b[0m         converted \u001b[38;5;241m=\u001b[39m convert_exception(e\u001b[38;5;241m.\u001b[39mjava_exception)\n", "File \u001b[0;32m~/opt/anaconda3/envs/ct-env-new/lib/python3.9/site-packages/py4j/protocol.py:326\u001b[0m, in \u001b[0;36mget_return_value\u001b[0;34m(answer, gateway_client, target_id, name)\u001b[0m\n\u001b[1;32m    324\u001b[0m value \u001b[38;5;241m=\u001b[39m OUTPUT_CONVERTER[\u001b[38;5;28mtype\u001b[39m](answer[\u001b[38;5;241m2\u001b[39m:], gateway_client)\n\u001b[1;32m    325\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m answer[\u001b[38;5;241m1\u001b[39m] \u001b[38;5;241m==\u001b[39m REFERENCE_TYPE:\n\u001b[0;32m--> 326\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m Py4JJavaError(\n\u001b[1;32m    327\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAn error occurred while calling \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39m\n\u001b[1;32m    328\u001b[0m         \u001b[38;5;28mformat\u001b[39m(target_id, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m, name), value)\n\u001b[1;32m    329\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    330\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m Py4JError(\n\u001b[1;32m    331\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAn error occurred while calling \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m. Trace:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{3}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39m\n\u001b[1;32m    332\u001b[0m         \u001b[38;5;28mformat\u001b[39m(target_id, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m, name, value))\n", "\u001b[0;31mPy4JJavaError\u001b[0m: An error occurred while calling o32.load.\n: java.lang.NoSuchMethodError: org.apache.spark.sql.AnalysisException.<init>(Ljava/lang/String;Lscala/Option;Lscala/Option;Lscala/Option;Lscala/Option;)V\n\tat org.apache.spark.sql.delta.DeltaErrors$.notADeltaTableException(DeltaErrors.scala:260)\n\tat org.apache.spark.sql.delta.catalog.DeltaTableV2.toBaseRelation(DeltaTableV2.scala:142)\n\tat org.apache.spark.sql.delta.sources.DeltaDataSource.createRelation(DeltaDataSource.scala:177)\n\tat org.apache.spark.sql.execution.datasources.DataSource.resolveRelation(DataSource.scala:350)\n\tat org.apache.spark.sql.DataFrameReader.loadV1Source(DataFrameReader.scala:228)\n\tat org.apache.spark.sql.DataFrameReader.$anonfun$load$2(DataFrameReader.scala:210)\n\tat scala.Option.getOrElse(Option.scala:189)\n\tat org.apache.spark.sql.DataFrameReader.load(DataFrameReader.scala:210)\n\tat org.apache.spark.sql.DataFrameReader.load(DataFrameReader.scala:185)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\n\tat sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\n\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tat java.lang.reflect.Method.invoke(Method.java:498)\n\tat py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:244)\n\tat py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:357)\n\tat py4j.Gateway.invoke(Gateway.java:282)\n\tat py4j.commands.AbstractCommand.invokeMethod(AbstractCommand.java:132)\n\tat py4j.commands.CallCommand.execute(CallCommand.java:79)\n\tat py4j.ClientServerConnection.waitForCommands(ClientServerConnection.java:182)\n\tat py4j.ClientServerConnection.run(ClientServerConnection.java:106)\n\tat java.lang.Thread.run(Thread.java:748)\n"]}], "source": ["adl = spark.read.format(\"delta\").load(\"s3a://aktana-bdp-devgenentechca/dev/adl/data/final_dataset/\")"]}, {"cell_type": "code", "execution_count": null, "id": "f6bb02a8a672af96", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}