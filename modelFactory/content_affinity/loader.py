import random
from datetime import datetime

import findspark
import pandas as pd
pd.options.mode.chained_assignment = None  # default='warn'
import numpy as np
import pyspark
from pyspark.sql import SparkSession

from abstract_model_factory.abstract_loader import AbstractLoader
from content_affinity.data import Data
from content_affinity.data_access_layer import DataAccessLayer
from qa_data_handler import QADataHandler
# import constants
from content_affinity.constants import Constants
from athena_reader import AthenaReader
import os


class Loader(AbstractLoader):

    def printSummary(self, name, df, rowCount=10):

        print(f"Data frame: {name}:" + str(df.shape))
        print("*******************************")
        if Constants.DEBUG_MODE:
            df.info()
            print(df.head(rowCount))

    def qa_module(self):
        pass


    def read_interaction_content(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")

        reader = AthenaReader()
        reader.connect(aws_access_key=params.get("athena-username"), aws_secret_key=params.get("athena-password"),
                       session_token=params.get('session_token'), aws_region=params.get("athena-region"),
                       athena_staging_bucket=params.get("athena-s3bucket"),
                       athena_staging_dir=params.get("athena-stagedir"), schema=params.get("athena-schema"))
        athena_query = f'''select accountId, productId, tag, count(1) count from interaction_account_product_content_v
                            where tag is not null
                            and activityDate >= date_add('month', -24, current_date)
                            group by accountId, productId, tag'''
        df = reader.query(query=athena_query)

        data.set_dataframe("interaction_content_df", df)
        self.printSummary("interaction_content", df)


    def read_demographic_features(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")

        reader = AthenaReader()
        reader.connect(aws_access_key=params.get("athena-username"), aws_secret_key=params.get("athena-password"),
                       session_token=params.get('session_token'), aws_region=params.get("athena-region"),
                       athena_staging_bucket=params.get("athena-s3bucket"),
                       athena_staging_dir=params.get("athena-stagedir"), schema=params.get("athena-schema"))
        athena_query = f'''select a.accountId, a.ispersonaccount_std_akt isPersonAccount, 
                            a.specialties_std_akt specialty, f.geoLocationString
                            from account_dse_v a
                            join facility_v f on a.facilityid = f.facilityid'''
        df = reader.query(query=athena_query)

        data.set_dataframe("demographic_features_df", df)
        self.printSummary("demographic_features", df)


    def get_account_product_df(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")

        reader = AthenaReader()
        reader.connect(aws_access_key=params.get("athena-username"), aws_secret_key=params.get("athena-password"),
                       session_token=params.get('session_token'), aws_region=params.get("athena-region"),
                       athena_staging_bucket=params.get("athena-s3bucket"),
                       athena_staging_dir=params.get("athena-stagedir"), schema=params.get("athena-schema"))
        athena_query = f'''select distinct ia.accountId, ip.productId
                            from interaction_v i
                            join interactionaccount_v ia on i.interactionid = ia.interactionid
                            join interactionproduct_v ip on i.interactionid = ip.interactionid
                            where i.startdatelocal >= date_add('month', -24, current_date)
                            -- union suggestion_v'''
        df = reader.query(query=athena_query)

        data.set_dataframe("account_product_df", df)
        self.printSummary("account_product_df", df)


    def execute(self):
        self.read_interaction_content()
        self.read_demographic_features()
        self.get_account_product_df()
