import logging
from datetime import datetime

import pandas as pd

from abstract_model_factory.abstract_deployer import AbstractDeployer
from content_affinity.constants import Constants
from content_affinity.data import Data
from content_affinity.data_access_layer import DataAccessLayer

from sqlalchemy import create_engine

import requests
import os
import time

class Deployer(AbstractDeployer):

    def execute(self):
        self.write_predictions()

    def qa_module(self):
        pass

    # Save results to S3
    def write_predictions(self):
        data = Data.get_instance()
        final_recommend_df = data.get_dataframe("final_recommend_df")

        data_access_layer = DataAccessLayer()
        predictions_s3_dir = Constants.TARGET_S3_PATH
        target_name = "hcp_level_content_affinity.parquet"
        data_access_layer.write_predictions_to_s3(final_recommend_df, predictions_s3_dir, target_name)

