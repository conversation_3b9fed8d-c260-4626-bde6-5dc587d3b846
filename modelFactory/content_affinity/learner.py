import numpy as np
import pandas as pd
from abstract_model_factory.abstract_learner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from content_affinity.constants import Constants
from content_affinity.data import Data
from scipy import stats
from implicit.als import AlternatingLeastSquares
from scipy.sparse import csr_matrix
from sklearn.preprocessing import <PERSON><PERSON>ot<PERSON>ncoder
from sklearn.neighbors import KNeighborsClassifier
from collections import Counter


class Learner(AbstractLearner):

    def get_content_from_matrix_factorization(self):
        data = Data.get_instance()
        interaction_content_df = data.get_dataframe("interaction_content_df")

        interaction_content_pivot_df = pd.pivot_table(interaction_content_df, index=['accountId', 'productId'], columns='tag',
                                                      values='count', fill_value=0)
        print("interaction_content_pivot_df: \n" + str(interaction_content_pivot_df))

        interaction_content_pivot_matrix = interaction_content_pivot_df.values
        sparse_matrix = csr_matrix(interaction_content_pivot_matrix)

        model = AlternatingLeastSquares(factors=10, regularization=0.1, iterations=10, random_state=42)
        model.fit(sparse_matrix)

        user_factors = model.user_factors
        content_factors = model.item_factors

        predicted_matrix = np.dot(user_factors, content_factors.T)
        print("predicted_matrix: \n" + str(predicted_matrix))

        # find top 3 content and filter if value < 0
        top_n = 3
        top_indices = np.argsort(predicted_matrix, axis=1)[:, -top_n:][:, ::-1]
        top_values = np.take_along_axis(predicted_matrix, top_indices, axis=1)
        non_negative_mask = top_values >= 0
        masked_indices = np.where(non_negative_mask, top_indices, -1)
        filtered_indices = [row[row != -1] for row in masked_indices]
        recommended_contents = [','.join(interaction_content_pivot_df.columns[row]) for row in filtered_indices]
        recommended_scores = [','.join([str(round(i, 5)) for i in np.take(predicted_matrix[i], row)]) for i, row in enumerate(filtered_indices)]

        print('Content count:')
        print(Counter([len(row) for row in filtered_indices]))

        recommended_df = pd.DataFrame({'recommended_content': recommended_contents, 'score': recommended_scores})
        recommended_df = recommended_df.set_index(interaction_content_pivot_df.index).reset_index()

        print("recommended_df: \n" + str(recommended_df))
        data.set_dataframe("recommended_df", recommended_df)

    def fill_content_for_account_product(self):
        data = Data.get_instance()
        recommended_df = data.get_dataframe("recommended_df")
        account_product_demo_with_content_df = data.get_dataframe("account_product_demo_with_content_df")
        recommended_with_demo_df = pd.merge(recommended_df, account_product_demo_with_content_df, on=['accountId', 'productId'], how='inner')
        account_product_demo_without_content_df = data.get_dataframe("account_product_demo_without_content_df")

        predictions = []

        for prod in account_product_demo_without_content_df['productId'].unique():
            train_subset = recommended_with_demo_df[recommended_with_demo_df['productId'] == prod]
            predict_subset = account_product_demo_without_content_df[account_product_demo_without_content_df['productId'] == prod]

            if train_subset.empty or predict_subset.empty:
                continue

            X_train = train_subset.drop(columns=['accountId', 'productId', 'recommended_content'])
            y_train = train_subset['recommended_content']
            X_predict = predict_subset.drop(columns=['accountId', 'productId'])

            # One-hot encoding for categorical features
            combined = pd.concat([X_train, X_predict])
            encoder = OneHotEncoder()
            combined_encoded = encoder.fit_transform(combined)
            X_train_encoded = combined_encoded[:X_train.shape[0]]
            X_predict_encoded = combined_encoded[X_train.shape[0]:]

            knn = KNeighborsClassifier(n_neighbors=3)
            knn.fit(X_train_encoded, y_train)
            predict_subset['recommended_content'] = knn.predict(X_predict_encoded)
            predictions.append(predict_subset[['accountId', 'productId', 'recommended_content']])

        predictions_df = pd.concat(predictions)

        final_recommend_df = pd.concat([recommended_df, predictions_df]).drop_duplicates()
        print("final_recommend_df: \n" + str(final_recommend_df))
        data.set_dataframe("final_recommend_df", final_recommend_df)

    def execute(self):
        self.get_content_from_matrix_factorization()
        self.fill_content_for_account_product()

    def qa_module(self):
        data = Data.get_instance()
        final_recommend_df = data.get_dataframe("final_recommend_df")

        print("Total account-product: " + str(final_recommend_df.shape[0]))
        print("Account-product with real content: " + str(final_recommend_df[final_recommend_df['score'].notnull()].shape[0]))
        print("Account-product with content from neighbors: " + str(final_recommend_df[final_recommend_df['score'].isnull()].shape[0]))
        print("Total account: " + str(final_recommend_df.accountId.nunique()))
        print("Account with real content: " + str(final_recommend_df[final_recommend_df['score'].notnull()].accountId.nunique()))
        print("Account with content from neighbors: " + str(final_recommend_df[final_recommend_df['score'].isnull()].accountId.nunique()))
