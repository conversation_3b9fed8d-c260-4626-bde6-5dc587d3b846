import logging

from sklearn import preprocessing

from abstract_model_factory.abstract_transformer import AbstractTransformer
from content_affinity.constants import Constants
from content_affinity.data import Data
import pandas as pd
import numpy as np
from scipy import stats
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta

from customer_journey.qa_data_handler import Q<PERSON>ata<PERSON>andler
from content_affinity.learner import Learner


class Transformer(AbstractTransformer):

    def printSummary(self, name, df, rowCount=10):

        print(f"Data frame: {name}:" + str(df.shape))
        print("*******************************")
        if Constants.DEBUG_MODE:
            df.info()
            print(df.head(rowCount))

    def qa_module(self):
        pass

    def combine_account_product_demographic(self):
        data = Data.get_instance()
        account_product_df = data.get_dataframe("account_product_df")
        demographic_features_df = data.get_dataframe("demographic_features_df")
        interaction_content_df = data.get_dataframe("interaction_content_df")

        # find account_product with/without content
        account_product_with_content = interaction_content_df[['accountId', 'productId']].drop_duplicates()
        account_product_without_content = account_product_df.merge(account_product_with_content, on=['accountId', 'productId'], how='left', indicator=True).query('_merge=="left_only"').drop(columns='_merge')

        account_product_demo_with_content_df = pd.merge(account_product_with_content, demographic_features_df, on='accountId', how='inner')
        account_product_demo_without_content_df = pd.merge(account_product_without_content, demographic_features_df, on='accountId', how='inner')

        data.set_dataframe("account_product_demo_with_content_df", account_product_demo_with_content_df)
        data.set_dataframe("account_product_demo_without_content_df", account_product_demo_without_content_df)
        self.printSummary("account_product_demo_with_content_df", account_product_demo_with_content_df)
        self.printSummary("account_product_demo_without_content_df", account_product_demo_without_content_df)

        print("Distinct account-product that have content: " + str(account_product_demo_with_content_df.shape[0]))
        print("Distinct account-product that do not have content: " + str(account_product_demo_without_content_df.shape[0]))
        print("Distinct account that have content: " + str(account_product_demo_with_content_df.accountId.nunique()))
        print("Distinct account that do not have content: " + str(account_product_demo_without_content_df.accountId.nunique()))

    def execute(self):
        self.combine_account_product_demographic()



