#!/bin/bash

# Wrapper/driver script for Channel-propensity jobs
# Can be used to invoke any of the Channel-propensity related jobs

# Usage: cp_driver.sh <job_name> --customer <customer-name> --env <env-name>"
#   Supported job names are: RUN_CP_ENGINE and RUN_CP_POSTPROC and RUN_CP_ENGINE_POSTPROC
#   Sample usage: ./cp_driver.sh RUN_CP_PREPROC --customer genentechca --env preprod"


# RUN_CP_ENGINE - to execute the main Job for CP Engine
# RUN_CP_POSTPROC - to execute the post-processor Job for CP
# RUN_CP_ENGINE_POSTPROC - to execute the main Job for CP Engine and then the post-processor Job for CP


# All jobs take the following command line parameters:
#   --customer custName (name of the customer configured in aktana metadata)
#   --env envName (name of the environment configured in aktana metadata)


echo "Python version : $(python3 --version)"

# Print CP Install dir
INSTALL_DIR=`dirname $0`
INSTALL_DIR=`readlink -f "$INSTALL_DIR"`
echo "Install directory = $INSTALL_DIR"

APP_PARAM="--app STATE_MODEL"

#echo "Install python dependencies from $CP_INSTALL_DIR/requirements.txt"
#pip3 install -r $CP_INSTALL_DIR/requirements.txt
#pip3 install imbalanced-learn==0.11.0


# Add modelFactory folder to PYTHONPATH for imports to work
export PYTHONPATH="${PYTHONPATH}:$(dirname "$INSTALL_DIR")"

# Add common python utils to PYTHONPATH used to get parameters from Aktana metadata
export PYTHONPATH="$INSTALL_DIR/../../common/pyUtils:${PYTHONPATH}"
echo "Value in PYTHONPATH=$PYTHONPATH"


echo "Running DRL state model job...python3 $INSTALL_DIR/main.py $@ $APP_PARAM"
eval "python3 $INSTALL_DIR/main.py $@ $APP_PARAM"
rc=$?


echo "Returning from drl state model driver with rc=$rc"
exit $rc

