import keras.models
import numpy as np
import pandas as pd
import shap as shap

from abstract_model_factory.abstract_predictor import AbstractPredictor
from drl_state_model.constants import Constants
from drl_state_model.data import Data
from scipy.optimize import minimize

# from channel_propensity.qa_data_handler import QADataHandler


class Predictor(AbstractPredictor):

    def qa_module(self):
        pass


    def execute(self):

        data = Data.get_instance()

        cp_model = data.get_model("cp_model")
        rem_model = data.get_model("rem_model")
        st_model = data.get_model("st_model")

        cp_predict_features = data.get_numpy("cp_predict_features")
        rem_predict_features = data.get_numpy("rem_predict_features")
        st_predict_features = data.get_numpy("st_predict_features")


        if not Constants.CP_NO_TARGET:
            cp_predictions = cp_model.predict(cp_predict_features, verbose=2)
        else:
            cp_predictions = np.zeros(shape=(3, cp_predict_features.shape[0], 2))
        if not Constants.REM_NO_TARGET:
            rem_predictions = rem_model.predict(rem_predict_features, verbose=2)
        else:
            rem_predictions = np.zeros(shape=(rem_predict_features.shape[0], 2))
        if not Constants.ST_NO_TARGET:
            st_predictions = st_model.predict(st_predict_features, verbose=2)
        else:
            st_predictions = np.zeros(shape=(st_predict_features.shape[0], 2))
        # rem_predictions = np.random.random((1000, 2))

        cp_predictions = np.array(cp_predictions)[:, :, 1].squeeze()
        rem_predictions = np.array(rem_predictions)[:, 1].squeeze()
        st_predictions = np.array(st_predictions)[:, 1].squeeze()


        cp_rem_predictions = np.vstack((cp_predictions, rem_predictions[np.newaxis, :]))

        predict_accts = data.get_numpy("predict_accts")
        cp_rem_predictions_with_acct = np.hstack((predict_accts[:, np.newaxis], cp_rem_predictions.T))
        cp_rem_predictions_pdf = pd.DataFrame(cp_rem_predictions_with_acct)
        cp_rem_predictions_pdf.columns = ["accountId", "email_prob", "f2f_prob", "virtual_prob", "rem_prob"]
        cp_rem_predictions_pdf['accountId'] = cp_rem_predictions_pdf['accountId'].astype(int)
        print(cp_rem_predictions_pdf)
        data.set_dataframe("cp_rem_predictions_pdf", cp_rem_predictions_pdf)

        predict_acct_prod = data.get_numpy("predict_acct_prod")
        st_predictions_with_acct_prod = np.hstack((predict_acct_prod, st_predictions[:, np.newaxis]))
        st_predictions_pdf = pd.DataFrame(st_predictions_with_acct_prod)
        st_predictions_pdf.columns = ['accountId', 'productId', 'st_prob']
        st_predictions_pdf['accountId'] = st_predictions_pdf['accountId'].astype(int)
        st_predictions_pdf['productId'] = st_predictions_pdf['productId'].astype(int)

        # all_predictions_pdf = pd.merge(st_predictions_pdf, cp_rem_predictions_pdf, how='outer', on=['accountId'])

        print(st_predictions_pdf)
        data.set_dataframe("st_predictions_pdf", st_predictions_pdf)


if __name__ == '__main__':
    obj = Predictor("Predictor")
    obj.execute()
