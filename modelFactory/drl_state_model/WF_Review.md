# DRL state model review

## Node: input data
read adlh

read adlx and filter cols

output1: adlh

output2: adlx

## Node: RNN input data preprocessing

input1: adlh

input2: adlx

### Node: AP-priority_segment Trend
input: adlh sorted by account and yearmonth

output: account, yearmonth, segment, trend (current_segment - previous_segment)

### Node: Channel Propensity
input1: adlh removed segment

input2: segment with trend

for each channel:
    1. select following cols:
        yearMonth (String)
        <channel>1MonthCount (Number (double))
        <channel>1MonthWeekCount (Number (double))
        <channel>1MonthDayCount (Number (double))
        mean<channel>GapsOver1Month (Number (double))
        mean<channel>GapsOver3Month (Number (double))
        mean<channel>GapsOver6Month (Number (double))
        accountUid (String)
        cri<channel>Score (Number (double))
        criSuggestion<channel>Score (Number (double))
    
    2. add year month as col and pivot

then join by accountid

for segment trend: add year month as col and pivot

join channel info with segment trend by accountid

use latest month value (e.g. trend_2023_09, visit1MonthCount) as binary target

output1: (channel features + trend) by month

output2: accountIds


### Node: RepEng-1

#### Node: RegEng-2

1. for each account, count distinct rep, and keep those accounts who have only 1 rep
2. for each account+rep+yearmonth, find if there is engagement
3. for each account, fill null repId and engagement with previous/next month data 



one-hot encode gender and pivot all features by yearmonth



## Node: REP Engagement

input1: training accountid
input2: testing accountid
input3: feature by yearmonth

for training data, SMOTE to increase minority class
normalize both training & testing data

output1: training set
output2: testing set

both outputs have accountid + repId + repEng as target ([0] or [1]) + other features ([0, 0, 0.23, ...])


## Node: Channel Propensity

input1: training output from REP Engagement
input2: features by month
input3: training accountid
input4: testing accountid

for training data, SMOTE to increase minority class
normalize both training & testing data

output1: training set, including target for each channel + segment + RepEng, plus input for both CP and REM

output2: testing set







## Features fed to NN

Common: 

accountuid
by month:
{'criOpenScore',
 'criSuggestionEmailScore',
 'criSuggestionVisitScore',
 'criVisitScore',
 'emailClick1MonthCount',
 'emailClick1MonthDayCount',
 'emailClick1MonthWeekCount',
 'emailClickRate',
 'emailClickRateOver12Months',
 'emailClickRateOver3Months',
 'emailClickRateOver6Months',
 'emailOpen1MonthCount',
 'emailOpen1MonthDayCount',
 'emailOpen1MonthWeekCount',
 'emailOpenClickRate',
 'emailOpenClickRateOver12Months',
 'emailOpenClickRateOver3Months',
 'emailOpenClickRateOver6Months',
 'emailOpenRate',
 'emailOpenRateOver12Months',
 'emailOpenRateOver3Months',
 'emailOpenRateOver6Months',
 'emailSent1MonthCount',
 'emailSent1MonthDayCount',
 'emailSent1MonthWeekCount',
 'meanEmailClickGapsOver1Month',
 'meanEmailClickGapsOver3Month',
 'meanEmailClickGapsOver6Month',
 'meanEmailOpenGapsOver1Month',
 'meanEmailOpenGapsOver3Month',
 'meanEmailOpenGapsOver6Month',
 'meanEmailSentGapsOver1Month',
 'meanEmailSentGapsOver3Month',
 'meanEmailSentGapsOver6Month',
 'meanVirtualVisitGapsOver1Month',
 'meanVirtualVisitGapsOver3Month',
 'meanVirtualVisitGapsOver6Month',
 'meanVisitGapsOver1Month',
 'meanVisitGapsOver3Month',
 'meanVisitGapsOver6Month',
 'virtualVisit1MonthCount',
 'virtualVisit1MonthDayCount',
 'virtualVisit1MonthWeekCount',
 'visit1MonthCount',
 'visit1MonthDayCount',
 'visit1MonthWeekCount'}



RepEng specific:

 repId
 by month:
 {F,
  M,
  Unknown,
  RepEngTag,
  criCadenceScore,
  criMaxAvgIndex,
  criTenureScore,
  facilityTargetAccountCount,
  numberHcpFacility,
  tenureMonths,
 }


Channel Propensity specific:

 by month:
 {ap_segment_trend}
 
 




