import pandas as pd
import numpy as np

test_pdf = pd.read_csv("~/Downloads/knime_output/test.csv")
test_pdf = test_pdf.drop(columns=['accountUid'])

acct_num = test_pdf.shape[0]
features = np.zeros(shape=(11, acct_num, 47))


for i in range(11):
    features[i] = test_pdf.iloc[:, range(0, 564, 12)]

features = features.reshape([-1, 11, 47])
# print(features)

targets = np.zeros(shape=(4, acct_num, 2), dtype=int)

for i, t in enumerate(['emailOpen1MonthCount_2022_04', 'visit1MonthCount_2022_04', "virtualVisit1MonthCount_2022_04", 'ap_segment_trend_2022_04']):
    for j, c in enumerate(test_pdf[t]):
        targets[i, j, c] = 1


