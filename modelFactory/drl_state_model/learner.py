from abstract_model_factory.abstract_learner import AbstractLearner
from drl_state_model.data import Data
from keras.models import Model
from keras.layers import Input, LSTM, Dense, Dropout
import numpy as np
from keras.optimizers import Adam
from drl_state_model.constants import Constants
import pandas as pd
from sklearn.preprocessing import MinMaxScaler


class Learner(AbstractLearner):
    @staticmethod
    def get_cp_nn_model():
        data = Data.get_instance()
        cp_feature_num = data.get_param("cp_feature_num", 46)

        input_layer = Input(shape=(None, cp_feature_num))
        lstm_layer = LSTM(128)(input_layer)
        dense_layer = Dense(128, activation='tanh')(lstm_layer)
        dropout_layer = Dropout(0.1)(dense_layer)
        email_layer = Dense(2, activation='softmax', name='email_task')(dropout_layer)
        f2f_layer = Dense(2, activation='softmax', name='f2f_task')(dropout_layer)
        virtual_layer = Dense(2, activation='softmax', name='virtual_task')(dropout_layer)
        # segment_layer = Dense(2, activation='softmax', name='segment_task')(dropout_layer)
        model = Model(inputs=input_layer, outputs=[email_layer, f2f_layer, virtual_layer])

        optimizer = Adam()

        model.compile(optimizer=optimizer,
                      loss={'email_task': 'categorical_crossentropy',
                            'f2f_task': 'categorical_crossentropy',
                            'virtual_task': 'categorical_crossentropy'})

        return model

    @staticmethod
    def get_rem_nn_model():
        data = Data.get_instance()
        rem_feature_num = data.get_param("rem_feature_num", 56)

        input_layer = Input(shape=(None, rem_feature_num))
        lstm_layer = LSTM(128)(input_layer)
        dense_layer = Dense(128, activation='tanh')(lstm_layer)
        dropout_layer = Dropout(0.1)(dense_layer)
        output_layer = Dense(2, activation='softmax', name='rem_task')(dropout_layer)
        model = Model(inputs=input_layer, outputs=output_layer)

        optimizer = Adam()

        model.compile(optimizer=optimizer,
                      loss={'rem_task': 'categorical_crossentropy'})

        return model

    @staticmethod
    def get_st_nn_model():
        data = Data.get_instance()
        rem_feature_num = data.get_param("st_feature_num", 47)

        input_layer = Input(shape=(None, rem_feature_num))
        lstm_layer = LSTM(128)(input_layer)
        dense_layer = Dense(128, activation='tanh')(lstm_layer)
        dropout_layer = Dropout(0.1)(dense_layer)
        output_layer = Dense(2, activation='softmax', name='st_task')(dropout_layer)
        model = Model(inputs=input_layer, outputs=output_layer)

        optimizer = Adam()

        model.compile(optimizer=optimizer,
                      loss={'st_task': 'categorical_crossentropy'})

        return model

    def qa_module(self):
        pass

    def execute(self):
        data = Data.get_instance()

        # get features and targets from Data
        cp_training_features = data.get_numpy("cp_training_features")
        cp_training_targets = data.get_numpy("cp_training_targets")
        rem_training_features = data.get_numpy("rem_training_features")
        rem_training_targets = data.get_numpy("rem_training_targets")
        st_training_features = data.get_numpy("st_training_features")
        st_training_targets = data.get_numpy("st_training_targets")

        # get NN model
        cp_model = None
        rem_model = None
        st_model = None

        if Constants.TEST_MODE:
            cp_training_features = np.random.random([1000, 11, 47])
            cp_training_targets = np.zeros((4, 1000, 2), dtype=int)
            for i in range(4):
                for j in range(1000):
                    idx = np.random.randint(0, 2)
                    cp_training_targets[i, j, idx] = 1

            rem_training_features = np.random.random([1000, 11, 47])
            rem_training_targets = np.zeros((1000, 2), dtype=int)
            for i in range(1000):
                idx = np.random.randint(0, 2)
                rem_training_targets[i, idx] = 1

        if Constants.TEST_MODE:
            test_pdf = pd.read_csv("")
            test_pdf = test_pdf.drop(columns=['accountUid'])

            acct_num = test_pdf.shape[0]
            features = np.zeros(shape=(11, acct_num, 47))

            for i in range(11):
                features[i] = test_pdf.iloc[:, range(0, 564, 12)]

            cp_training_features = features.reshape([-1, 11, 47])

            targets = np.zeros(shape=(4, acct_num, 2), dtype=int)

            for i, t in enumerate(
                    ['emailOpen1MonthCount_2022_04', 'visit1MonthCount_2022_04', "virtualVisit1MonthCount_2022_04",
                     'ap_segment_trend_2022_04']):
                for j, c in enumerate(test_pdf[t]):
                    targets[i, j, c] = 1

            cp_training_targets = targets

        if not Constants.CP_NO_TARGET:
            cp_model = self.get_cp_nn_model()
            print("--------------Start training CP model--------------")
            cp_model.fit(x=cp_training_features,
                         y={'email_task': cp_training_targets[:, 0],
                            'f2f_task': cp_training_targets[:, 1],
                            'virtual_task': cp_training_targets[:, 2]},
                         epochs=Constants.CP_TRAINING_EPOCHS,
                         batch_size=Constants.CP_TRAINING_BATCH_SIZE,
                         validation_split=Constants.VALIDATION_SPLIT,
                         verbose=2)
            print("--------------CP training finished--------------")

        if not Constants.REM_NO_TARGET:
            rem_model = self.get_rem_nn_model()
            print("--------------Start training REM model--------------")
            rem_model.fit(x=rem_training_features,
                          y={'rem_task': rem_training_targets},
                          epochs=Constants.REM_TRAINING_EPOCHS,
                          batch_size=Constants.REM_TRAINING_BATCH_SIZE,
                          validation_split=Constants.VALIDATION_SPLIT,
                          verbose=2)
            print("--------------REM training finished--------------")

        if not Constants.ST_NO_TARGET:
            st_model = self.get_st_nn_model()
            print("--------------Start training ST model--------------")
            st_model.fit(x=st_training_features,
                         y={'st_task': st_training_targets},
                         epochs=Constants.ST_TRAINING_EPOCHS,
                         batch_size=Constants.ST_TRAINING_BATCH_SIZE,
                         validation_split=Constants.VALIDATION_SPLIT,
                         verbose=2)
            print("--------------ST training finished-------------- ")

        data.set_model("cp_model", cp_model)
        data.set_model("rem_model", rem_model)
        data.set_model('st_model', st_model)
        print("Learner complete...")


if __name__ == '__main__':
    obj = Learner("Learn")
    obj.execute()
