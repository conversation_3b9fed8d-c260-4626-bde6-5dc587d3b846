from drl_state_model.constants import Constants
from drl_state_model.drl_state_model_driver import DRLStateModelDriver
from drl_state_model.data import Data
from drl_state_model.deployer import Deployer
# from drl_state_model.evaluator import Evaluator
from drl_state_model.initializer import Initializer
from drl_state_model.learner import Learner
from drl_state_model.loader import Loader
from drl_state_model.model_drift_detector import ModelDriftDetector
from drl_state_model.predictor import Predictor
from drl_state_model.transformer import Transformer
import traceback
import statsd
import time
import argparse

expected_args = [
    {"name": "--customer", "default": "DEFAULT"},
    {"name": "--env", "default": "DEFAULT"},
    {"name": "--region", "default": "DEFAULT"},
]

if __name__ == "__main__" :
    start_time = time.time()
    # get cmdline args
    parser = argparse.ArgumentParser(description='DRL state model arguments')
    for arg in expected_args:
        thetype = arg.get('type')
        parser.add_argument(arg.get('name'), help=arg.get('help'), required=arg.get('required'),
                            default=arg.get('default'), type=thetype if thetype is None else locate(thetype))
    args, _ = parser.parse_known_args()
    rc = 0
    try:
        drl_state_model_driver = DRLStateModelDriver(data=Data.get_instance(),
                                                     initializer=Initializer(Constants.INITIALIZER_MODULE),
                                                     loader=Loader(Constants.LOADER_MODULE),
                                                     transformer=Transformer(Constants.TRANSFORMER_MODULE),
                                                     model_drift_detector=ModelDriftDetector(),
                                                     learner=Learner(Constants.LEARNER_MODULE),
                                                     predictor=Predictor(Constants.PREDICTOR_MODULE),
                                                     deployer=Deployer(Constants.DEPLOYER_MODULE))
        # Execute the driver
        rc = drl_state_model_driver.start()


        # Add execution time in QA logs
        # if rc == 0:
        #     rc = drl_state_model_driver.add_execution_time_qa_logs()
        #
        # # Print MF QA Logs
        # data = Data.get_instance()
        # mf_qa_logs_pdf = data.get_dataframe("mf_qa_logs_pdf")
        # print(mf_qa_logs_pdf)
        #
        # # Write QA logs in snowflake
        # if rc == 0:
        #     rc = drl_state_model_driver.write_mf_qa_logs()
        if rc == 0:
            print("Job succeeded...")
        else:
            print("Job failed...")

        exit(rc)

    except Exception as e:
        print(e)
        traceback.print_exc()
        rc = 1
    finally:
        end_time = time.time()
        exec_time = int(end_time - start_time)
        # write observability metrics
        statsd_server = args.region + "statsd.aktana.com"
        if "eks" not in args.region:
            statsd_server = args.region[:2] + "statsd.aktana.com"
        statsd_port = '9125'

        # metric prefix
        metric_prefix = 'type.{TYPE}.cmpny.' + args.customer + '.regn.' + args.region + '.cntry.none' + '.env.' + args.env + \
                        '.apptype.DRL_STATE_MODEL' + '.cmpnt.ENGINE' + '.metric'

        # gauge metrics
        statsd_client = statsd.StatsClient(statsd_server, statsd_port,
                                           prefix=metric_prefix.format(TYPE="g"))
        statsd_client.gauge("job_status", rc)
        print(f"Job status: {rc}. 0: SUCCESS, 1: FAIL")
        statsd_client.gauge("exec_time", exec_time)
        print(f"Execution time: {exec_time}")

        # counter metrics
        statsd_client = statsd.StatsClient(statsd_server, statsd_port,
                                           prefix=metric_prefix.format(TYPE="c"))
        if rc:
            statsd_client.incr("fail_cnt", 1)
            print("Failure count increased by 1")
        else:
            statsd_client.incr("success_cnt", 1)
            print("Success count increased by 1")



        exit(rc)