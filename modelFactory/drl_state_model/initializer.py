import ast
import uuid

import datetime
import pandas as pd

from abstract_model_factory.abstract_initializer import AbstractInitializer
import sys
import getopt

from drl_state_model.data import Data
from drl_state_model.constants import Constants
from os import path
import json
import os
from drl_state_model.data_access_layer import DataAccessLayer
from aktana_ml_utils import aktana_ml_utils


class Initializer(AbstractInitializer):

    def read_param_file(self):
        """

        @return:
        """
        data = Data.get_instance()

        # print command line arguments
        print("****** Command Line Parameters:",  sys.argv)

        short_opt = "hc:e:a:r:ec:o:l:d"
        long_opt = ["customer=", "env=", "app=", "region=", "ecosystem=", "json-output=", "local=", "use-debug=",
                    "scenario=", "rundate=", "loglevel=", "autocreate=", "runMode=", "postProcMode=", "journey=",
                    "useSCC=", "rundate=", "startdate=", "enddate=", "user=", "incrementalRun="]
        cmd_argv = sys.argv

        ak_ml_utils = aktana_ml_utils()
        cmdline_params, metadata_params = ak_ml_utils.initialize(cmd_argv, short_opt, long_opt, Constants.TEST_MODE)

        for key, value in cmdline_params.items():
            data.set_param(key, value)

        print("****** Command Line Parameters:" + str(data.params))

        params = ak_ml_utils.get_params_json()
        params = json.loads(params)
        #print("Params:" + str(params))

        data.set_param("connect_params", params)

        # If local is true set local constants
        if data.get_param("local", "") != "":
            Constants.LOCAL_MODE = True

        # If use-debug is true set debug constants
        if data.get_param("use-debug", "") != "":
            Constants.TEST_MODE = True

        if data.get_param("rundate", "") != "":
            rundate = data.get_param("rundate")
            runmonth = rundate[:4] + rundate[5:7]
            cj_runmonth = rundate[:-3]
            data.set_param("runmonth", runmonth)
            data.set_param("cj_runmonth", cj_runmonth)

        Constants.ACCESS_ID = params.get("athena-username")
        Constants.AWS_ACCESS_KEY_ID = params.get("athena-username")
        Constants.AWS_SECRET_ACCESS_KEY = params.get("athena-password")
        if Constants.LOCAL_MODE:
            Constants.AWS_SESSION_TOKEN = os.environ.get("AWS_SESSION_TOKEN", "")

        Constants.DCO_S3_PATH = params.get('adl-dcoS3Location')
        Constants.ADL_S3_PATH = params.get('adl-adlS3Location')
        Constants.S3_BASE_PATH = Constants.ADL_S3_PATH.split('adl/')[0]
        Constants.TARGET_S3_PATH = Constants.S3_BASE_PATH + 'candidate-generator/state-model/'

        Constants.sw_account = params.get('snowflake-account')
        Constants.sw_user = params.get('snowflake-user')
        Constants.sw_password = params.get('snowflake-password')
        Constants.sw_database = params.get('snowflake-database')
        Constants.sw_schema = params.get('snowflake-schema')
        Constants.sw_role = params.get('snowflake-role')
        Constants.sw_warehouse = params.get('snowflake-warehouse')

        Constants.ADL_S3_PATH = Constants.ADL_S3_PATH.replace('s3:', 's3a:')
        Constants.TARGET_S3_PATH = Constants.TARGET_S3_PATH.replace('s3:', 's3a:')

        Constants.CJ_PATH = Constants.S3_BASE_PATH + 'customer-journey/{run_month_str}/customer_journey.parquet'
        Constants.CJ_PATH = Constants.CJ_PATH.replace('s3:', 's3a:')


    def initialize_qa_log(self):
        data = Data.get_instance()
        mf_qa_config_pdf = data.get_dataframe('mf_qa_config_pdf')
        mf_qa_logs_pdf = pd.DataFrame(columns=mf_qa_config_pdf.columns)
        mf_qa_logs_pdf["Test_Value"] = None
        mf_qa_logs_pdf["Test_Result"] = None
        mf_qa_logs_pdf["Test_Action"] = None
        mf_qa_logs_pdf["Client_Name"] = None
        mf_qa_logs_pdf["Process_Name"] = None
        mf_qa_logs_pdf["Model_Name"] = None
        mf_qa_logs_pdf["Update_Date"] = None
        mf_qa_logs_pdf["Run_Uid"] = None

        data.set_dataframe("mf_qa_logs_pdf", mf_qa_logs_pdf)

    def qa_module(self):
        pass
        # data = Data.get_instance()
        # qa_config_pdf = data.get_dataframe("mf_qa_config_pdf")
        # test_qa_config = qa_config_pdf.loc[qa_config_pdf['"Test_Phase"'] == 'Init', :]
        # All the Tests are not substantial hence passing them now

    def execute(self):
        self.read_param_file()

        # self.initialize_qa_log()

        print("Initialized params for DRL state model")
