from datetime import datetime

import findspark
import pandas as pd
import pyspark
from pyspark.sql import SparkSession
import pyspark.sql.functions as F
from abstract_model_factory.abstract_loader import AbstractLoader
from drl_state_model.data import Data
from drl_state_model.data_access_layer import DataAccessLayer
# from qa_data_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from athena_reader import <PERSON><PERSON><PERSON>er
from drl_state_model.constants import Constants
from qa_data_handler import QADataHandler
import os
import numpy as np
from datetime import datetime, timedelta


class Loader(AbstractLoader):

    def __get_previous_runs_qa_log(self):

        query = "SELECT TOP 1 * FROM CHANNEL_PROPENSITY.\"MF_QA_logs\"\
                 WHERE \"Test_Phase\" = 'Load' AND \"Test_ID\" = 1\
                 ORDER BY \"Update_Date\" desc"
        last_mf_qa_log_pdf = DataAccessLayer.read_snow_flake_pandas(query)
        return last_mf_qa_log_pdf

    def qa_module(self):
        data = Data.get_instance()
        adlh_pdf = data.get_dataframe("adlh_pdf")
        adlx_pdf = data.get_dataframe('adlx_pdf')
        segments_pdf = data.get_dataframe('segment_trend_df')
        QADataHandler.create_logs_dataframe()
        logs_pdf = data.get_dataframe('logs_pdf')
        unique_hcp_count = adlh_pdf.accountId.nunique()
        unique_hcp_count_adl = adlx_pdf.accountId.nunique()

        # TEST-1: Check adlh dataframe is not empty
        test_dict_1 = QADataHandler.get_dict_with_test_info('Load', 1, adlh_pdf.empty)
        test_dict_1['Test_Name'] = "Check adlh dataframe is not empty"
        if adlh_pdf.empty:
            test_dict_1['Test_Result'] = "Fail"
        QADataHandler.add_row_qa_logs(test_dict_1)

        # TEST-2: Check adlx dataframe is not empty
        test_dict_2 = QADataHandler.get_dict_with_test_info('Load', 2, adlx_pdf.empty)
        test_dict_2['Test_Name'] = "Check adlx dataframe is not empty"
        if adlx_pdf.empty:
            test_dict_2['Test_Result'] = "Fail"
        QADataHandler.add_row_qa_logs(test_dict_2)

        # TEST-3: Check Segments are not empty
        test_dict_3 = QADataHandler.get_dict_with_test_info('Load', 3, segments_pdf.empty)
        test_dict_3['Test_Name'] = "Check Segments are not empty"
        if segments_pdf.empty:
            test_dict_3['Test_Result'] = "Fail"
        QADataHandler.add_row_qa_logs(test_dict_3)

        # TEST-4:  Check if distinct number of accounts in adlh is greater than x threshold
        test_dict_4 = QADataHandler.get_dict_with_test_info('Load', 4, unique_hcp_count_adl)
        test_dict_4['Test_Name'] = "Check if distinct number of accounts in adlh is greater than x threshold"
        if Constants.DISTINCT_ACCOUNT_THRESHOLD < unique_hcp_count:
            test_dict_4['Test_Result'] = "Fail"
        QADataHandler.add_row_qa_logs(test_dict_4)

        # TEST-5:  Check if there is data for account product combination with valid products
        account_product_pdf = adlh_pdf[adlh_pdf['productId'] > str(0)]
        account_product_pdf = account_product_pdf[['accountId', 'productId']].nunique()
        test_dict_5 = QADataHandler.get_dict_with_test_info('Load', 5, account_product_pdf.empty)
        test_dict_5['Test_Name'] = "Check if there is data for account product combination with valid products"

        if account_product_pdf.empty:
            test_dict_5['Test_Result'] = "Fail"
        QADataHandler.add_row_qa_logs(test_dict_5)

        print(logs_pdf)

    def __get_spark(self):

        findspark.init()
        os.environ['PYSPARK_SUBMIT_ARGS'] = "--driver-memory 12g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.199," \
                                            "org.apache.hadoop:hadoop-aws:3.3.1,io.delta:delta-core_2.12:1.0.0 " \
                                            "pyspark-shell "

        sc = pyspark.SparkContext()
        sc.setSystemProperty("com.amazonaws.services.s3.enableV4", "true")
        sc.setLogLevel("WARN")
        hadoop_conf = sc._jsc.hadoopConfiguration()
        hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
        hadoop_conf.set("com.amazonaws.services.s3.enableV4", "true")

        if Constants.LOCAL_MODE:
            hadoop_conf.set("fs.s3a.access.key", Constants.AWS_ACCESS_KEY_ID)
            hadoop_conf.set("fs.s3a.secret.key", Constants.AWS_SECRET_ACCESS_KEY)
            hadoop_conf.set("fs.s3a.session.token", Constants.AWS_SESSION_TOKEN)
        hadoop_conf.set("fs.s3a.connection.maximum", "100000")
        # hadoop_conf.set("fs.s3a.endpoint", "s3." + constants.aws_region + ".amazonaws.com")
        hadoop_conf.set("fs.s3a.endpoint", "s3.amazonaws.com")
        hadoop_conf.set("delta.logRetentionDuration", "36500")
        hadoop_conf.set("delta.deletedFileRetentionDuration", "365")

        spark = SparkSession(sc) \
            .builder \
            .appName(Constants.SPARK_APP_NAME) \
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
            .config("spark.sql.debug.maxToStringFields", 1000) \
            .getOrCreate()

        print("Spark Initialized.")
        return spark

    def read_adlh_adlx(self):

        data = Data.get_instance()

        if False:
            hcp_fs_pdf = pd.read_csv("/Users/<USER>/code/learning_develop/learning/modelFactory/channel_propensity/tests/data/hcp_fs_pdf.csv")
            # Select random 200000 rows from dataframe
            hcp_fs_pdf = hcp_fs_pdf.sample(n=100000, random_state=7)
            hcp_fs_pdf['yearMonth'] = hcp_fs_pdf['yearMonth']
            data.set_dataframe("hcp_fs_pdf", hcp_fs_pdf)
            return

        spark = self.__get_spark()
        hcp_feature_store_path = Constants.ADL_S3_PATH + "data/silver/hcpFeatureStore"
        print("Reading HCP Feature Store from Path: " + hcp_feature_store_path)
        hcp_fs_df = spark.read.format("delta").load(hcp_feature_store_path)
        fs_max_month = hcp_fs_df.agg({"yearMonth": "max"}).collect()[0][0]
        runmonth = data.get_param("runmonth")
        if runmonth != fs_max_month:
            print("Warning: runmonth: " + runmonth + " does not match latest month in HCP Feature Store: " + fs_max_month)
        latest_month = min(runmonth, fs_max_month)
        print("Reading FS until: " + latest_month)
        start_month_1 = hcp_fs_df.agg({"yearMonth": "min"}).collect()[0][0]
        start_month = datetime.strptime(latest_month, "%Y%m")
        # start_month = start_month - timedelta(days=start_month.day)
        target_month = start_month
        for _ in range(Constants.LOOK_BACK_MONTH - 1):
            start_month = start_month - timedelta(days=start_month.day)
        start_month = start_month.strftime("%Y%m")
        target_month = target_month.strftime("%Y%m")
        start_month = max(start_month_1, start_month)

        hcp_fs_pdf = hcp_fs_df.filter(hcp_fs_df.yearMonth >= start_month).filter(
            hcp_fs_df.yearMonth <= target_month).toPandas()

        print(f"HCP Feature Store from {start_month} to {latest_month}")
        print(hcp_fs_pdf)

        data.set_dataframe("adlh_pdf", hcp_fs_pdf)
        data.set_param("start_month", start_month)
        data.set_param("target_month", target_month)
        data.set_param("latest_month", latest_month)

        adlx_path = Constants.ADL_S3_PATH + "data/silver/final_dataset"
        print("Reading ADLx from Path: " + adlx_path)
        adlx_df = spark.read.format("delta").load(adlx_path).select(*Constants.ADLX_COLS)
        # start_month = data.get_param("start_month")
        adlx_pdf = adlx_df.filter(adlx_df.effectiveYearMonth >= start_month).\
            filter(adlx_df.effectiveYearMonth <= target_month).toPandas()

        print("ADLx-")
        print(adlx_pdf)

        data.set_dataframe("adlx_pdf", adlx_pdf)

    def read_adlh_sfmc(self):
        data = Data.get_instance()

        params = data.get_param('connect_params')
        start_month = data.get_param("start_month")
        target_month = data.get_param("target_month")
        sql = f"select * from {params['rds-learningdbname']}.ADLH_HQ_Email_v where "
        adlh_sfmc_df = DataAccessLayer.read_rds_table_as_pandas(sql)
        

        data.set_dataframe()


    def combine_adlh(self):
        adlh_sfmc_df = 


    def read_segment_trend_from_cj(self):
        data = Data.get_instance()

        params = data.get_param('connect_params')
        segment_type_df = DataAccessLayer.read_rds_table_as_pandas(f"select configValue from {params['rds-learningdbname']}.DRL_Config_Params where versionId = '__DEFAULT__' and configName = 'STATE_MODEL_SEGMENT_TYPE'")
        segment_type = segment_type_df['configValue'][0]
        # segment_col = segment_col + '_rank_change'

        cj_month = DataAccessLayer.get_max_month_from_cj()
        if not cj_month:
            print("Error: No CJ data found, please make sure you have run the customer journey model first")
            exit(1)
        print("Reading segment trend from CJ for month: " + str(cj_month))
        cj_df = DataAccessLayer.read_s3_parquet(Constants.CJ_PATH.format(run_month_str=cj_month))
        segment_trend_df = cj_df[cj_df.segmentType == segment_type][['accountId', 'productId', 'yearMonth', 'rankChange']]

        segment_trend_df['yearMonth'] = pd.to_datetime(segment_trend_df['yearMonth'], format='%Y-%m')
        segment_trend_df['yearMonth'] = segment_trend_df['yearMonth'].dt.strftime('%Y%m')
        segment_trend_df['productId'] = segment_trend_df['productId'].astype('str')
        segment_trend_df = segment_trend_df.rename(columns={'rankChange': 'segment_trend'})

        # segment_trend_df = segment_trend_df.drop_duplicates(['accountId', 'productId', 'yearMonth'])
        data.set_dataframe("segment_trend_df", segment_trend_df)

    def read_product_group_mapping(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        sql = f"select productId, parentProductId from {params['rds-learningdbname']}.Product_Group_Mapping_V"
        product_group_mapping_pdf = DataAccessLayer.read_rds_table_as_pandas(sql)

        product_group_mapping_pdf['productId'] = product_group_mapping_pdf['productId'].astype('str')
        product_group_mapping_pdf['parentProductId'] = product_group_mapping_pdf['parentProductId'].astype('str')

        data.set_dataframe("product_group_mapping", product_group_mapping_pdf)

    def execute(self):
        self.read_adlh_adlx()
        self.read_segment_trend_from_cj()
        self.read_product_group_mapping()


if __name__ == '__main__':
    obj = Loader("Loader")
    obj.execute()