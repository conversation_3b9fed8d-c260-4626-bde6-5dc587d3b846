from datetime import datetime, date

import pandas as pd

from abstract_model_factory.abstract_deployer import AbstractDeployer
from drl_state_model.constants import Constants
from drl_state_model.data import Data
from drl_state_model.data_access_layer import DataAccessLayer
from keras.models import load_model
# from drl_state_model.qa_data_handler import QAData<PERSON><PERSON>ler


class Deployer(AbstractDeployer):

    def qa_module(self):
        pass

    @staticmethod
    def write_predictions_to_s3():
        data = Data.get_instance()
        cp_rem_predictions = data.get_dataframe("cp_rem_predictions_pdf")
        st_predictions = data.get_dataframe("st_predictions_pdf")
        run_month = data.get_param("runmonth")

        predictions_dir = Constants.TARGET_S3_PATH + f'predictions/yearMonth={run_month}/'
        predictions_csv_path = Constants.TARGET_S3_PATH + f'predictions/latest.csv'
        latest_dict = {}
        if not Constants.CP_NO_TARGET or not Constants.REM_NO_TARGET:
            DataAccessLayer.write_predictions_to_s3(cp_rem_predictions, predictions_dir, 'cp_rem_predictions.parquet')
            cp_rem_predictions_path = predictions_dir + 'cp_rem_predictions.parquet'
            latest_dict['cp_rem_predictions_path'] = [cp_rem_predictions_path]
        else:
            print("Warning: CP_REM predictions not written to s3 due to no targets")
        if not Constants.ST_NO_TARGET:
            DataAccessLayer.write_predictions_to_s3(st_predictions, predictions_dir, 'st_predictions.parquet')
            st_predictions_path = predictions_dir + 'st_predictions.parquet'
            latest_dict['st_predictions_path'] = [st_predictions_path]
        else:
            print("Warning: ST predictions not written to s3 due to no targets")

        if len(latest_dict) > 0:
            latest_pdf = pd.DataFrame(latest_dict)
            DataAccessLayer.write_latest_csv(predictions_csv_path, latest_pdf)


    @staticmethod
    def write_model_to_s3():
        data = Data.get_instance()
        cp_model = data.get_model("cp_model")
        rem_model = data.get_model("rem_model")
        st_model = data.get_model("st_model")
        run_month = data.get_param("runmonth")

        model_dir = Constants.TARGET_S3_PATH + f'models/yearMonth={run_month}/'
        # rem_model_path = Constants.TARGET_S3_PATH + f'models/yearMonth={latest_month}/rem_model.keras'
        # st_model_path = Constants.TARGET_S3_PATH + f'models/yearMonth={latest_month}/st_model.keras'
        model_csv_path = Constants.TARGET_S3_PATH + f'models/latest.csv'
        latest_dict = {}
        if not Constants.CP_NO_TARGET:
            DataAccessLayer.write_model_to_s3(cp_model, model_dir, 'cp_model.keras')
            cp_model_path = model_dir + 'cp_model.keras'
            latest_dict['cp_model_path'] = [cp_model_path]
        else:
            print("Warning: CP model not written to s3 due to no targets")
        if not Constants.REM_NO_TARGET:
            DataAccessLayer.write_model_to_s3(rem_model, model_dir, 'rem_model.keras')
            rem_model_path = model_dir + 'rem_model.keras'
            latest_dict['rem_model_path'] = [rem_model_path]
        else:
            print("Warning: REM model not written to s3 due to no targets")
        if not Constants.ST_NO_TARGET:
            DataAccessLayer.write_model_to_s3(st_model, model_dir, 'st_model.keras')
            st_model_path = model_dir + 'st_model.keras'
            latest_dict['st_model_path'] = [st_model_path]
        else:
            print("Warning: ST model not written to s3 due to no targets")

        if len(latest_dict) > 0:
            latest_pdf = pd.DataFrame(latest_dict)
            DataAccessLayer.write_latest_csv(model_csv_path, latest_pdf)

    def execute(self):
        self.write_model_to_s3()
        self.write_predictions_to_s3()





if __name__ == "__main__":
    data = Data.get_instance()
    data.set_param("latest_month", "202305")

    cp_model = load_model("./cp_model_br.keras")
    rem_model = load_model("./rem_model_br.keras")
    predictions = pd.read_csv("./predictions_br.csv")
    data.set_model("cp_model", cp_model)
    data.set_model("rem_model", rem_model)
    data.set_dataframe("predictions_pdf", predictions)
    Deployer('Deploy').execute()
