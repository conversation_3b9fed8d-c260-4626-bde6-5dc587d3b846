from abstract_model_factory.abstract_driver import AbstractDriver
from drl_state_model.data import Data
from drl_state_model.data_access_layer import DataAccessLayer
# from drl_state_model.qa_data_handler import QADataHandler


class DRLStateModelDriver(AbstractDriver):

    def add_execution_time_qa_logs(self):
        data = Data.get_instance()
        mf_qa_config_pdf = data.get_dataframe("mf_qa_config_pdf")
        phases = mf_qa_config_pdf['Test_Phase'].unique()
        for phase in phases:
            execution_time = data.get_param(f"execution_time_{phase}", 0)
            # QADataHandler.append_info_in_qa_logs(test_phase=phase, test_id=0, test_value=execution_time)

        return 0

    def write_mf_qa_logs(self):
        data = Data.get_instance()
        mf_qa_logs_pdf = data.get_dataframe("mf_qa_logs_pdf")
        mf_qa_logs_pdf['Test_Value'] = mf_qa_logs_pdf['Test_Value'].astype(str)
        DataAccessLayer.write_snow_flake_pandas(mf_qa_logs_pdf, "MF_QA_logs")

        return 0
