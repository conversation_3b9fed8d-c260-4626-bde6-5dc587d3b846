
Change segment trend to account-product level

Background:
segment trend as one of the targets in CP model
all features are account level
ADLh does have productId


Target: segment trend for latest month


Features:

Option 1 ****:
move segment trend out of CP model
    - CP remains account level (productId = -1)
    - drop segment trend from features and targets in CP

prepare another set of features for segment trend (from ADLh, similar to CP but account product level)


Option 2:
Change entire CP model to be account product level
    - prepare account product features for both CP and ST
    - output of CP grouped to account level (?)



Segment Index mapping:
1. RDS reference table
2. static s3 csv file
3. sth else...