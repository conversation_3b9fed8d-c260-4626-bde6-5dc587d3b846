class Constants:
    TEST_MODE = False
    LOCAL_MODE = True

    AWS_ACCESS_KEY_ID = ""
    AWS_SECRET_ACCESS_KEY = ""
    AWS_SESSION_TOKEN = ""

    SPARK_APP_NAME = "DRL_STATE_MODEL"

    ADL_S3_PATH = ""

    CP_TRAINING_EPOCHS = 25
    CP_TRAINING_BATCH_SIZE = 100
    REM_TRAINING_EPOCHS = 25
    REM_TRAINING_BATCH_SIZE = 100
    ST_TRAINING_EPOCHS = 25
    ST_TRAINING_BATCH_SIZE = 100
    VALIDATION_SPLIT = 0.2

    CP_NO_TARGET = False
    REM_NO_TARGET = False
    ST_NO_TARGET = False

    ADLX_COLS = ['effectiveYearMonth', 'repId', 'accountId', 'suggestionActionTaken']
    # ADLH_COLS = ['criOpenScore',
    #              'criSuggestionEmailScore',
    #              'criSuggestionVisitScore',
    #              'criVisitScore',
    #              'emailClick1MonthCount',
    #              'emailClick1MonthDayCount',
    #              'emailClick1MonthWeekCount',
    #              'emailClickRate',
    #              'emailClickRateOver12Months',
    #              'emailClickRateOver3Months',
    #              'emailClickRateOver6Months',
    #              'emailOpen1MonthCount',
    #              'emailOpen1MonthDayCount',
    #              'emailOpen1MonthWeekCount',
    #              'emailOpenClickRate',
    #              'emailOpenClickRateOver12Months',
    #              'emailOpenClickRateOver3Months',
    #              'emailOpenClickRateOver6Months',
    #              'emailOpenRate',
    #              'emailOpenRateOver12Months',
    #              'emailOpenRateOver3Months',
    #              'emailOpenRateOver6Months',
    #              'emailSent1MonthCount',
    #              'emailSent1MonthDayCount',
    #              'emailSent1MonthWeekCount',
    #              'meanEmailClickGapsOver1Month',
    #              'meanEmailClickGapsOver3Month',
    #              'meanEmailClickGapsOver6Month',
    #              'meanEmailOpenGapsOver1Month',
    #              'meanEmailOpenGapsOver3Month',
    #              'meanEmailOpenGapsOver6Month',
    #              'meanEmailSentGapsOver1Month',
    #              'meanEmailSentGapsOver3Month',
    #              'meanEmailSentGapsOver6Month',
    #              'meanVirtualVisitGapsOver1Month',
    #              'meanVirtualVisitGapsOver3Month',
    #              'meanVirtualVisitGapsOver6Month',
    #              'meanVisitGapsOver1Month',
    #              'meanVisitGapsOver3Month',
    #              'meanVisitGapsOver6Month',
    #              'virtualVisit1MonthCount',
    #              'virtualVisit1MonthDayCount',
    #              'virtualVisit1MonthWeekCount',
    #              'visit1MonthCount',
    #              'visit1MonthDayCount',
    #              'visit1MonthWeekCount',
    #              'hcpGender',
    #              'criCadenceScore',
    #              'criMaxAvgIndex',
    #              'criTenureScore',
    #              'facilityTargetAccountCount',
    #              'numberHcpFacility',
    #              'tenureMonths']

    DISTINCT_ACCOUNT_THRESHOLD = 5000

    CP_COLS = ['accountId',
               'yearMonth',
               'criOpenScore',
               'criSuggestionEmailScore',
               'criSuggestionVisitScore',
               'criVisitScore',
               'emailClick1MonthCount',
               'emailClick1MonthDayCount',
               'emailClick1MonthWeekCount',
               'emailClickRate',
               'emailClickRateOver12Months',
               'emailClickRateOver3Months',
               'emailClickRateOver6Months',
               'emailOpen1MonthCount',
               'emailOpen1MonthDayCount',
               'emailOpen1MonthWeekCount',
               'emailOpenClickRate',
               'emailOpenClickRateOver12Months',
               'emailOpenClickRateOver3Months',
               'emailOpenClickRateOver6Months',
               'emailOpenRate',
               'emailOpenRateOver12Months',
               'emailOpenRateOver3Months',
               'emailOpenRateOver6Months',
               'emailSent1MonthCount',
               'emailSent1MonthDayCount',
               'emailSent1MonthWeekCount',
               'meanEmailClickGapsOver1Month',
               'meanEmailClickGapsOver3Month',
               'meanEmailClickGapsOver6Month',
               'meanEmailOpenGapsOver1Month',
               'meanEmailOpenGapsOver3Month',
               'meanEmailOpenGapsOver6Month',
               'meanEmailSentGapsOver1Month',
               'meanEmailSentGapsOver3Month',
               'meanEmailSentGapsOver6Month',
               'meanVirtualVisitGapsOver1Month',
               'meanVirtualVisitGapsOver3Month',
               'meanVirtualVisitGapsOver6Month',
               'meanVisitGapsOver1Month',
               'meanVisitGapsOver3Month',
               'meanVisitGapsOver6Month',
               'virtualVisit1MonthCount',
               'virtualVisit1MonthDayCount',
               'virtualVisit1MonthWeekCount',
               'visit1MonthCount',
               'visit1MonthDayCount',
               'visit1MonthWeekCount']

    REM_COLS = ['accountId',
                'yearMonth',
                'criOpenScore',
                'criSuggestionEmailScore',
                'criSuggestionVisitScore',
                'criVisitScore',
                'emailClick1MonthCount',
                'emailClick1MonthDayCount',
                'emailClick1MonthWeekCount',
                'emailClickRate',
                'emailClickRateOver12Months',
                'emailClickRateOver3Months',
                'emailClickRateOver6Months',
                'emailOpen1MonthCount',
                'emailOpen1MonthDayCount',
                'emailOpen1MonthWeekCount',
                'emailOpenClickRate',
                'emailOpenClickRateOver12Months',
                'emailOpenClickRateOver3Months',
                'emailOpenClickRateOver6Months',
                'emailOpenRate',
                'emailOpenRateOver12Months',
                'emailOpenRateOver3Months',
                'emailOpenRateOver6Months',
                'emailSent1MonthCount',
                'emailSent1MonthDayCount',
                'emailSent1MonthWeekCount',
                'meanEmailClickGapsOver1Month',
                'meanEmailClickGapsOver3Month',
                'meanEmailClickGapsOver6Month',
                'meanEmailOpenGapsOver1Month',
                'meanEmailOpenGapsOver3Month',
                'meanEmailOpenGapsOver6Month',
                'meanEmailSentGapsOver1Month',
                'meanEmailSentGapsOver3Month',
                'meanEmailSentGapsOver6Month',
                'meanVirtualVisitGapsOver1Month',
                'meanVirtualVisitGapsOver3Month',
                'meanVirtualVisitGapsOver6Month',
                'meanVisitGapsOver1Month',
                'meanVisitGapsOver3Month',
                'meanVisitGapsOver6Month',
                'virtualVisit1MonthCount',
                'virtualVisit1MonthDayCount',
                'virtualVisit1MonthWeekCount',
                'visit1MonthCount',
                'visit1MonthDayCount',
                'visit1MonthWeekCount',
                'hcpGender',
                'criCadenceScore',
                'criMaxAvgIndex',
                'criTenureScore',
                'facilityTargetAccountCount',
                'numberHcpFacility',
                'tenureMonths']

    ADLH_COLS = list(set(CP_COLS).union(set(REM_COLS)))

    INITIALIZER_MODULE = "Init"
    LOADER_MODULE = "Load"
    TRANSFORMER_MODULE = "Transform"
    LEARNER_MODULE = "Learn"
    PREDICTOR_MODULE = "Score"
    EVALUATOR_MODULE = "Evaluate"
    DEPLOYER_MODULE = "Deploy"

    LOOK_BACK_MONTH = 12

    SEGMENT_INDEX_MAPPING = {
        "ST": 5,
        "T1": 4,
        "T2": 3,
        "T3": 2,
        "notier": 1
    }