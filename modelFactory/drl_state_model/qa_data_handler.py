import pandas as pd
from drl_state_model.data import Data


class QADataHandler:

    @staticmethod
    def create_logs_dataframe():
        data = Data.get_instance()
        log_pdf = pd.DataFrame(columns=['Phase', 'Test_ID', 'Test_Name', 'Test_Value', 'Test_Result'])

        data.set_dataframe("logs_pdf", log_pdf)

    @staticmethod
    def get_dict_with_test_info(phase, test_id, test_value=None):
        test_result_dict = {'Phase': phase, 'Test_ID': test_id, 'Test_Name': None,
                            'Test_Value': test_value, 'Test_Result': 'Pass'}

        return test_result_dict

    @staticmethod
    def append_info_in_qa_logs(test_phase:str, test_id:int, test_value):
        test_dict = QADataHandler.get_dict_with_test_info(test_phase, test_id, test_value)
        test_dict['Test_Result'] = 'Pass'
        QADataHandler.add_row_qa_logs(test_dict)

    @staticmethod
    def add_row_qa_logs(test_result_dict):
        data = Data.get_instance()
        logs_pdf = data.get_dataframe("logs_pdf")

        logs_pdf.loc[len(logs_pdf)] = test_result_dict
