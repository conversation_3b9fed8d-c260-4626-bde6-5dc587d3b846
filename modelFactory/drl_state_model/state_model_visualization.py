import pandas as pd
from matplotlib import pyplot as plt
import numpy as np
import shap

# df = pd.read_parquet("~/Downloads/st_predictions.parquet")
# plt.hist(df['st_prob'], bins=10, log=True)
# plt.xlabel("st_prob")
# plt.ylabel("# of acct-prod (log scale)")
# plt.title("st_prob histogram")
# plt.savefig('./st_prob.png')
# plt.show()

df = pd.read_parquet("~/Downloads/cp_rem_predictions.parquet")
plt.hist(df['f2f_prob'], bins=10, log=True)
plt.xlabel("f2f_prob")
plt.ylabel("# of acct-prod (log scale)")
plt.title("f2f_prob histogram")
plt.savefig('./f2f_prob.png')
plt.show()