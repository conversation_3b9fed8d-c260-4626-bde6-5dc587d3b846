import numpy as np
import pandas as pd








cp_predictions = cp_model.predict(cp_predict_features)
rem_predictions = rem_model.predict(rem_predict_features)

cp_predictions = np.array(cp_predictions)[:, :, 1].squeeze()
rem_predictions = np.array(rem_predictions)[:, 1].squeeze()

all_predictions = np.vstack((cp_predictions, rem_predictions[np.newaxis, :]))

predict_accts = data.get_dataframe("predict_accts")
predictions_with_acct = np.hstack((all_predictions.T, predict_accts[:, np.newaxis]))
predictions_pdf = pd.DataFrame(predictions_with_acct)