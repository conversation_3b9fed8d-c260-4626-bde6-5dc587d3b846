import io

import pandas as pd
import mysql.connector
from drl_state_model.data import Data
from drl_state_model.constants import Constants
import boto3
import tempfile
import botocore
import logging
import bisect


def get_s3_client():
    ACCESS_KEY = Constants.AWS_ACCESS_KEY_ID
    SECRET_KEY = Constants.AWS_SECRET_ACCESS_KEY
    SESSION_TOKEN = Constants.AWS_SESSION_TOKEN

    if ACCESS_KEY != "":
        s3 = boto3.client('s3',
                          aws_access_key_id=ACCESS_KEY,
                          aws_secret_access_key=SECRET_KEY,
                          aws_session_token=SESSION_TOKEN)

    else:
        s3 = boto3.client('s3')

    return s3


class DataAccessLayer:

    @staticmethod
    def write_model_to_s3(model, model_dir, target_name):
        """
        Writes a model to an S3 bucket.

        Parameters:
            model (object): The model object to be written.
            model_dir (str): The directory where the model will be saved.
            target_name (str): The name of the model

        Returns:
            None
        """
        s3 = get_s3_client()

        s3_bucket = model_dir.split('/')[2]
        model_path = '/'.join(model_dir.split('/')[3:]) + target_name

        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/{target_name}"
            model.save(temp_path)
            s3.upload_file(temp_path, s3_bucket, model_path)

        print("Uploaded model to : " + model_path)

    @staticmethod
    def write_latest_csv(csv_s3_path, latest_pdf):

        s3 = get_s3_client()

        s3_bucket = csv_s3_path.split('/')[2]
        csv_path = '/'.join(csv_s3_path.split('/')[3:])

        csv_string = latest_pdf.to_csv(index=False)
        print("Writing csv: " + csv_string)
        s3.put_object(Bucket=s3_bucket, Key=csv_path, Body=csv_string)

        print("Uploaded latest.csv to : " + csv_s3_path)


    @staticmethod
    def write_predictions_to_s3(predictions, s3_path, target_name):

        s3_bucket = s3_path.split('/')[2]
        predictions_path = '/'.join(s3_path.split('/')[3:]) + target_name

        s3 = get_s3_client()

        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/{target_name}"
            predictions.to_parquet(temp_path, index=False)
            s3.upload_file(temp_path, s3_bucket, predictions_path)

        print("Uploaded predictions to : " + s3_path)

    @staticmethod
    def read_rds_table_as_pandas(sql):
        data = Data.get_instance()

        params = data.get_param("connect_params")
        db_config = {
            # 'host': params['rds-server'],
            'host': '127.0.0.1',
            'user': params['rds-user'],
            'password': params['rds-password'],
            'port': 3337
            # 'port': 33066
        }
        conn = mysql.connector.connect(**db_config)

        print("Executing sql: " + sql)
        df = pd.read_sql(sql, conn)
        print(df)
        return df

    @staticmethod
    def read_s3_parquet(s3_path):

        s3_bucket = s3_path.split('/')[2]
        parquet_path = '/'.join(s3_path.split('/')[3:])

        s3 = get_s3_client()
        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/output.parquet"
            try:
                s3.download_file(s3_bucket, parquet_path, temp_path)
            except botocore.exceptions.ClientError as e:
                if e.response['Error']['Code'] == "404":
                    logging.warning(f"The file object does not exist in S3 at path: {s3_path}")
                    return None
                elif e.response['Error']['Code'] == 403:
                    logging.warning(f"The file object not authorized to access in S3 at path: {s3_path}")
                    return None
                else:
                    # Something else has gone wrong.
                    raise

            parquet_df = pd.read_parquet(temp_path, engine='pyarrow')
            return parquet_df

    @staticmethod
    def get_max_month_from_cj():
        data = Data.get_instance()
        cj_runmonth = data.get_param("cj_runmonth")

        base_path = Constants.CJ_PATH[6:]
        path_list = base_path.split("/")
        bucket = path_list[0]
        prefix = '/'.join(path_list[1:3]) + '/'

        s3 = get_s3_client()
        month_list = []
        for s in s3.list_objects_v2(Bucket=bucket, Prefix=prefix, Delimiter='/')['CommonPrefixes']:
            p = s['Prefix']
            month_list.append(p.split('/')[-2])
        idx = bisect.bisect(month_list, f'runmonth={cj_runmonth}')
        if idx > 0:
            if month_list[idx-1] != f'runmonth={cj_runmonth}':
                print("Warning: could not find CJ data for runmonth: " + cj_runmonth)
            return month_list[idx - 1]
        else:
            return None



