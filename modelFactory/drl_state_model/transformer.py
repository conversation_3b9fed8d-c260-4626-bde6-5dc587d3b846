from abstract_model_factory.abstract_transformer import AbstractTransformer
from drl_state_model.constants import Constants
from drl_state_model.data import Data
import pandas as pd
import numpy as np
from scipy import stats
from imblearn.over_sampling import SMOTENC, SMOTE
from sklearn.preprocessing import MinMaxScaler

# from drl_state_model.qa_data_handler import QADataHandler


class Transformer(AbstractTransformer):

    def __reshape_pivot_features(self, df, time_step):
        m, n = df.shape

        res = np.zeros(shape=(m, time_step, n//time_step))

        for i in range(time_step):
            col_idx = [j for j in range(i, n, time_step)]
            res[:, i, :] = df[:, col_idx]

        return res

    def __smote(self):
        data = Data.get_instance()
        cp_training_features_pivot = data.get_dataframe("cp_training_features_pivot")
        cp_targets = data.get_dataframe("cp_targets")
        rem_training_features_pivot = data.get_dataframe("rem_training_features_pivot")
        rem_targets = data.get_dataframe("rem_targets")
        st_training_features_pivot = data.get_dataframe("st_training_features_pivot")
        st_targets = data.get_dataframe("st_targets")
        time_step = data.get_param("time_step")
        st_time_step = data.get_param("st_time_step")


        cp_training_features_targets = pd.merge(cp_training_features_pivot, cp_targets, on=['accountId']).drop(columns=['accountId'])
        rem_training_features_targets = pd.merge(rem_training_features_pivot, rem_targets, on=['accountId']).drop(columns=['accountId'])
        st_training_features_targets = pd.merge(st_training_features_pivot, st_targets, on=['accountId', 'productId']).drop(
            columns=['accountId', 'productId'])

        smote_cp = SMOTENC(categorical_features=['f2f_target', 'virtual_target'])
        # cp_training_features_targets = cp_training_features_targets.fillna(0)
        if Constants.CP_NO_TARGET:
            print("No CP targets. Skip CP SMOTE processing...")
        else:
            cp_training_features_resampled, cp_targets_resampled = smote_cp.fit_resample(cp_training_features_targets.drop(columns=['email_target']), cp_training_features_targets[['email_target']])
            print(f"Before SMOTE, Channel Propensity training matrix has {cp_training_features_targets.shape[0]} rows...")
            print(f"After SMOTE, Channel Propensity training matrix has {cp_training_features_resampled.shape[0]} rows...")
            cp_training_features = cp_training_features_resampled.drop(columns=['f2f_target', 'virtual_target'])
            cp_training_features = self.__reshape_pivot_features(cp_training_features.values, time_step)
            cp_training_targets = pd.concat(
                [cp_targets_resampled, cp_training_features_resampled[['f2f_target', 'virtual_target']]], axis=1)
            cp_training_targets['f2f_target'] = cp_training_targets['f2f_target'].astype(int)
            cp_training_targets['virtual_target'] = cp_training_targets['virtual_target'].astype(int)
            # cp_training_targets['segment_target'] = cp_training_targets['segment_target'].astype(int)
            cp_training_targets = cp_training_targets.values
            cp_training_targets = np.concatenate(
                [1 - cp_training_targets[:, :, np.newaxis], cp_training_targets[:, :, np.newaxis]], axis=-1)
            data.set_numpy("cp_training_features", cp_training_features)
            data.set_numpy("cp_training_targets", cp_training_targets)
            print("CP SMOTE complete...")

        hcpGender_cols = [c for c in rem_training_features_targets.columns if 'hcpGender' in c]
        smote_rem = SMOTENC(categorical_features=hcpGender_cols)
        # rem_training_features_targets[hcpGender_cols] = rem_training_features_targets[hcpGender_cols].fillna('Unknown')
        # rem_training_features_targets = rem_training_features_targets.fillna(0)
        if Constants.REM_NO_TARGET:
            print("No REM targets. Skip REM SMOTE processing...")
        else:
            rem_training_features_resampled, rem_targets_resampled = smote_rem.fit_resample(
                rem_training_features_targets.drop(columns=['rem_target']), rem_training_features_targets[['rem_target']])
            print(f"Before SMOTE, Engagement training matrix has {rem_training_features_targets.shape[0]} rows...")
            print(f"After SMOTE, Engagement training matrix has {rem_training_features_resampled.shape[0]} rows...")
            rem_training_features = self.__reshape_pivot_features(rem_training_features_resampled.values, time_step)
            rem_training_targets = rem_targets_resampled.values
            rem_training_targets = np.concatenate([1 - rem_training_targets, rem_training_targets], axis=-1)
            data.set_numpy("rem_training_features", rem_training_features)
            data.set_numpy("rem_training_targets", rem_training_targets)
            print("REM SMOTE complete...")

        smote_st = SMOTE()
        if Constants.ST_NO_TARGET:
            print("No ST targets. Skip ST SMOTE processing...")
        else:
            st_training_features_resampled, st_targets_resampled = smote_st.fit_resample(st_training_features_targets.drop(columns=['st_target']), st_training_features_targets[['st_target']])
            print(f"Before SMOTE, Segment Trend training matrix has {st_training_features_targets.shape[0]} rows...")
            print(f"After SMOTE, Segment Trend training matrix has {st_training_features_resampled.shape[0]} rows...")
            st_training_features = self.__reshape_pivot_features(st_training_features_resampled.values, st_time_step)
            st_training_targets = st_targets_resampled.values
            st_training_targets = np.concatenate([1 - st_training_targets, st_training_targets], axis=-1)
            data.set_numpy("st_training_features", st_training_features)
            data.set_numpy("st_training_targets", st_training_targets)
            print("ST SMOTE complete...")

    def __pivot_features(self, df, index=None):
        if index is None:
            index = ['accountId']
        df_pivot = df.pivot(index=index, columns=['yearMonth'])
        df_pivot.columns = [col + '_' + yearMonth for col, yearMonth in df_pivot.columns]
        df_pivot = df_pivot.reset_index()
        return df_pivot

    def __merge_engagement_tag(self):
        data = Data.get_instance()

        adlx_pdf = data.get_dataframe("adlx_pdf")
        adlh_pdf = data.get_dataframe("adlh_pdf")
        condition = adlx_pdf['suggestionActionTaken'].isin(["Accepted but Incomplete", "Accepted"])
        adlx_pdf['engagement'] = np.where(condition, 1, 0)

        engagement = adlx_pdf.groupby(["accountId", "effectiveYearMonth"])['engagement'].agg(rep_eng_tag="max").reset_index()
        engagement = engagement.rename(columns={"effectiveYearMonth": "yearMonth"})

        adlh_engagement_df = pd.merge(adlh_pdf, engagement, on=['accountId', 'yearMonth'], how='left')
        adlh_engagement_df['rep_eng_tag'] = adlh_engagement_df['rep_eng_tag'].fillna(0)
        data.set_dataframe("adlh_engagement_df", adlh_engagement_df)

    def __merge_segment_trend(self):
        data = Data.get_instance()

        adlh_pdf = data.get_dataframe("adlh_pdf")
        segment_trend_df = data.get_dataframe("segment_trend_df")

        st_target_month = min(segment_trend_df['yearMonth'].max(), adlh_pdf['yearMonth'].max())
        st_start_month = max(segment_trend_df['yearMonth'].min(), adlh_pdf['yearMonth'].min())
        data.set_param("st_target_month", st_target_month)
        data.set_param("st_start_month", st_start_month)
        print("ST target month: " + str(st_target_month))
        print("ST start month: " + str(st_start_month))

        adlh_pdf_by_prod = adlh_pdf[adlh_pdf['productId'] != '-1']

        # get parent productId for adlh before joining
        product_group_mapping = data.get_dataframe("product_group_mapping")
        adlh_pdf_by_prod = pd.merge(adlh_pdf_by_prod, product_group_mapping, on=['productId'], how='left')
        adlh_pdf_by_prod['parentProductId'] = adlh_pdf_by_prod['parentProductId'].combine_first(adlh_pdf_by_prod['productId'])
        adlh_pdf_by_prod = adlh_pdf_by_prod.rename(columns={"productId": "childProductId", "parentProductId": "productId"})

        adlh_segment_df = pd.merge(adlh_pdf_by_prod, segment_trend_df, on=['accountId', 'productId', 'yearMonth'], how='left')
        adlh_segment_df = adlh_segment_df.drop(columns=['productId'])
        adlh_segment_df = adlh_segment_df.rename(columns={"childProductId": "productId"})
        adlh_segment_df = adlh_segment_df[adlh_segment_df.yearMonth <= st_target_month]
        adlh_segment_df = adlh_segment_df[adlh_segment_df.yearMonth >= st_start_month]
        adlh_segment_df['segment_trend'] = adlh_segment_df['segment_trend'].fillna(0).astype(int)
        data.set_dataframe("adlh_segment_df", adlh_segment_df)

        st_time_step = adlh_segment_df['yearMonth'].nunique() - 1
        print("st time step: " + str(st_time_step))
        data.set_param("st_time_step", st_time_step)



    def __get_cp_targets(self):
        data = Data.get_instance()

        adlh_df = data.get_dataframe("adlh_pdf")
        adlh_df = adlh_df[adlh_df['productId'] == '-1']
        target_month = data.get_param("target_month")

        cp_targets = adlh_df[['accountId', 'yearMonth', 'emailOpen1MonthCount', 'visit1MonthCount', 'virtualVisit1MonthCount']]
        # cp_targets = pd.merge(cp_targets, segment_trend_df, on=['accountId', 'yearMonth'])
        cp_targets = cp_targets[cp_targets.yearMonth == target_month]
        cp_targets = cp_targets.drop(columns=["yearMonth"])
        cp_targets.loc[cp_targets.emailOpen1MonthCount >= 1, 'emailOpen1MonthCount'] = 1
        cp_targets.loc[cp_targets.visit1MonthCount >= 1, 'visit1MonthCount'] = 1
        cp_targets.loc[cp_targets.virtualVisit1MonthCount >= 1, 'virtualVisit1MonthCount'] = 1
        # cp_targets.loc[cp_targets.segment_trend >= 1, 'segment_trend'] = 1
        # cp_targets.loc[cp_targets.segment_trend < 1, 'segment_trend'] = 0
        cp_targets = cp_targets.rename(columns={"emailOpen1MonthCount": "email_target",
                                        "visit1MonthCount": "f2f_target",
                                        "virtualVisit1MonthCount": "virtual_target"})

        # # TODO for now only keep the row with highest email_target across products per account
        # idx = cp_targets.groupby("accountId")['email_target'].idxmax()
        # cp_targets = cp_targets.loc[idx]

        if cp_targets[cp_targets.email_target == 1].shape[0] == 0:
            print("Warning: There are no email targets. Will skip CP training and prediction...")
            Constants.CP_NO_TARGET = True

        data.set_dataframe("cp_targets", cp_targets)

    def __get_cp_features(self):
        data = Data.get_instance()

        adlh_df = data.get_dataframe("adlh_pdf")
        adlh_df = adlh_df[adlh_df['productId'] == '-1']
        target_month = data.get_param("target_month")
        start_month = data.get_param("start_month")

        cp_cols = Constants.CP_COLS
        cp_features_df = adlh_df[cp_cols]

        cp_features_df = cp_features_df.fillna(0)

        # scaling
        cols_to_scale = cp_features_df.drop(columns=['accountId', 'yearMonth']).columns
        scaler = MinMaxScaler()
        cp_features_df[cols_to_scale] = scaler.fit_transform(cp_features_df[cols_to_scale])

        cp_training_features = cp_features_df[cp_features_df.yearMonth < target_month]
        cp_predict_features = cp_features_df[cp_features_df.yearMonth > start_month]

        # cp_training_features = cp_training_features.drop_duplicates(subset=['accountId', 'yearMonth'])
        # cp_predict_features = cp_predict_features.drop_duplicates(subset=['accountId', 'yearMonth'])

        cp_training_features_pivot = self.__pivot_features(cp_training_features)
        data.set_dataframe("cp_training_features_pivot", cp_training_features_pivot)

        cp_predict_features_sort = cp_predict_features.sort_values(by=['accountId', 'yearMonth'])
        predict_accts = cp_predict_features_sort['accountId'].unique()
        data.set_numpy("predict_accts", predict_accts)
        cp_predict_features_np = cp_predict_features_sort.drop(columns=['accountId', 'yearMonth']).values

        time_step = data.get_param("time_step")
        cp_feature_num = cp_predict_features_np.shape[1]
        data.set_param("cp_feature_num", cp_feature_num)
        print(f"Channel Propensity has {cp_feature_num} features...")

        cp_predict_features_reshaped = cp_predict_features_np.reshape(-1, time_step, cp_feature_num)
        data.set_numpy("cp_predict_features", cp_predict_features_reshaped)

    def __get_rem_targets(self):
        data = Data.get_instance()

        adlh_engagement_df = data.get_dataframe("adlh_engagement_df")
        adlh_engagement_df = adlh_engagement_df[adlh_engagement_df['productId'] == '-1']
        target_month = data.get_param("target_month")

        rem_targets = adlh_engagement_df[['accountId', 'yearMonth', 'rep_eng_tag']]
        # cp_targets = pd.merge(cp_targets, segment_trend_df, on=['accountId', 'yearMonth'])
        rem_targets = rem_targets[rem_targets.yearMonth == target_month]
        rem_targets = rem_targets.drop(columns=["yearMonth"])
        rem_targets = rem_targets.rename(columns={'rep_eng_tag': 'rem_target'})

        # TODO for now only keep the row with highest rem_target across products per account
        idx = rem_targets.groupby("accountId")['rem_target'].idxmax()
        rem_targets = rem_targets.loc[idx]

        if rem_targets[rem_targets.rem_target == 1].shape[0] == 0:
            print("Warning: There are no engagement targets. Will skip REM training and prediction...")
            Constants.REM_NO_TARGET = True

        data.set_dataframe("rem_targets", rem_targets)

    def __get_rem_features(self):
        data = Data.get_instance()

        adlh_engagement_df = data.get_dataframe("adlh_engagement_df")
        adlh_engagement_df = adlh_engagement_df[adlh_engagement_df['productId'] == '-1']
        target_month = data.get_param("target_month")
        start_month = data.get_param("start_month")

        rem_cols = Constants.REM_COLS + ['rep_eng_tag']
        rem_features_df = adlh_engagement_df[rem_cols]

        rem_features_df = rem_features_df.fillna({'hcpGender': 'Unknown'})
        rem_features_df = rem_features_df.fillna(0)

        # scaling
        cols_to_scale = rem_features_df.drop(columns=['accountId', 'yearMonth', 'hcpGender']).columns
        scaler = MinMaxScaler()
        rem_features_df[cols_to_scale] = scaler.fit_transform(rem_features_df[cols_to_scale])

        # one-hot encoding hcpGender
        rem_features_df = pd.get_dummies(rem_features_df, columns=['hcpGender'])

        rem_training_features = rem_features_df[rem_features_df.yearMonth < target_month]
        rem_predict_features = rem_features_df[rem_features_df.yearMonth > start_month]

        rem_training_features = rem_training_features.drop_duplicates(subset=['accountId', 'yearMonth'])
        rem_predict_features = rem_predict_features.drop_duplicates(subset=['accountId', 'yearMonth'])

        rem_training_features_pivot = self.__pivot_features(rem_training_features)
        data.set_dataframe("rem_training_features_pivot", rem_training_features_pivot)

        # rem_predict_features = self.__one_hot_encoding(rem_predict_features, 'hcpGender')
        rem_predict_features_np = rem_predict_features.sort_values(by=['accountId', 'yearMonth']) \
            .drop(columns=['accountId', 'yearMonth']).values

        time_step = data.get_param("time_step")
        rem_feature_num = rem_predict_features_np.shape[1]
        data.set_param("rem_feature_num", rem_feature_num)
        print(f"Engagement has {rem_feature_num} features...")

        rem_predict_features_reshaped = rem_predict_features_np.reshape(-1, time_step, rem_feature_num)
        data.set_numpy("rem_predict_features", rem_predict_features_reshaped)

    def __get_st_targets(self):
        data = Data.get_instance()

        adlh_segment_df = data.get_dataframe("adlh_segment_df")
        target_month = data.get_param("st_target_month")

        st_targets = adlh_segment_df[['accountId', 'productId', 'yearMonth', 'segment_trend']]
        st_targets = st_targets[st_targets.yearMonth == target_month]
        st_targets = st_targets.drop(columns=["yearMonth"])
        st_targets = st_targets.rename(columns={'segment_trend': 'st_target'})
        st_targets.loc[st_targets.st_target >= 1, 'st_target'] = 1
        st_targets.loc[st_targets.st_target < 1, 'st_target'] = 0

        # # TODO for now only keep the row with highest rem_target across products per account
        # idx = rem_targets.groupby("accountId")['rem_target'].idxmax()
        # rem_targets = rem_targets.loc[idx]

        if st_targets[st_targets.st_target == 1].shape[0] == 0:
            print("Warning: There are no segment trend targets. Will skip ST training and prediction...")
            Constants.ST_NO_TARGET = True

        data.set_dataframe("st_targets", st_targets)

    def __get_st_features(self):
        data = Data.get_instance()

        adlh_segment_df = data.get_dataframe("adlh_segment_df")
        target_month = data.get_param("st_target_month")
        start_month = data.get_param("st_start_month")

        st_cols = Constants.CP_COLS + ['segment_trend', 'productId']
        st_features_df = adlh_segment_df[st_cols]

        st_features_df = st_features_df.fillna(0)

        # scaling
        cols_to_scale = st_features_df.drop(columns=['accountId', 'yearMonth', 'productId']).columns
        scaler = MinMaxScaler()
        st_features_df[cols_to_scale] = scaler.fit_transform(st_features_df[cols_to_scale])

        st_training_features = st_features_df[st_features_df.yearMonth < target_month]
        st_predict_features = st_features_df[st_features_df.yearMonth > start_month]

        # cp_training_features = cp_training_features.drop_duplicates(subset=['accountId', 'yearMonth'])
        # cp_predict_features = cp_predict_features.drop_duplicates(subset=['accountId', 'yearMonth'])

        st_training_features = self.__pivot_features(st_training_features, ['accountId', 'productId'])
        data.set_dataframe("st_training_features_pivot", st_training_features)

        st_predict_features_sort = st_predict_features.sort_values(by=['accountId', 'productId', 'yearMonth'])
        predict_acct_prod = st_predict_features_sort[['accountId', 'productId']].drop_duplicates().values
        data.set_numpy("predict_acct_prod", predict_acct_prod)
        st_predict_features_np = st_predict_features_sort.drop(columns=['accountId', 'productId', 'yearMonth']).values

        time_step = data.get_param("st_time_step")
        st_feature_num = st_predict_features_np.shape[1]
        data.set_param("st_feature_num", st_feature_num)
        print(f"Segment Trend has {st_feature_num} features...")

        st_predict_features_reshaped = st_predict_features_np.reshape(-1, time_step, st_feature_num)
        data.set_numpy("st_predict_features", st_predict_features_reshaped)

    def __get_distinct_months(self):
        data = Data.get_instance()

        df = data.get_dataframe("adlh_pdf")
        distinct_months = df['yearMonth'].unique()
        data.set_param("look_back", len(distinct_months))
        data.set_param("time_step", len(distinct_months) - 1)

    def __process_cp_data(self):

        # self.__merge_segment_trend()
        self.__get_cp_targets()
        self.__get_cp_features()

    def __process_rem_data(self):
        self.__merge_engagement_tag()
        self.__get_rem_targets()
        self.__get_rem_features()

    def __process_st_data(self):
        self.__merge_segment_trend()
        self.__get_st_targets()
        self.__get_st_features()

    def execute(self):
        self.__get_distinct_months()
        self.__process_cp_data()
        self.__process_rem_data()
        self.__process_st_data()
        self.__smote()

    def qa_module(self):
        pass


if __name__ == '__main__':
    data = Data.get_instance()
    adlh_pdf = pd.read_csv("./adlh_br.csv", index_col=0)
    adlh_pdf['yearMonth'] = adlh_pdf['yearMonth'].astype(str)
    data.set_dataframe("adlh_pdf", adlh_pdf)
    adlx_pdf = pd.read_csv("./adlx_br.csv", index_col=0)
    adlx_pdf['effectiveYearMonth'] = adlx_pdf['effectiveYearMonth'].astype(str)
    data.set_dataframe("adlx_pdf", adlx_pdf)
    segment_trend_df = pd.read_csv("./segment_br.csv", index_col=0)
    segment_trend_df['yearMonth'] = segment_trend_df['yearMonth'].astype(str)
    data.set_dataframe("segment_trend_df", segment_trend_df)

    data.set_param("latest_month", "202305")
    data.set_param("start_month", "202206")

    obj = Transformer("Transformer")
    obj.execute()