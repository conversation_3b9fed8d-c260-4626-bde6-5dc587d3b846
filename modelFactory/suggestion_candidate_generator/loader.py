from datetime import datetime

import findspark
import pandas as pd
import pyspark
from pyspark.sql import SparkSession
from scipy.stats import stats
from sklearn.preprocessing import MinMaxScaler

from abstract_model_factory.abstract_loader import AbstractLoader
from suggestion_candidate_generator.data import Data
from suggestion_candidate_generator.data_access_layer import DataAccessLayer
from qa_data_handler import QADataHandler
import constants
from suggestion_candidate_generator.constants import Constants
from athena_reader import <PERSON>Reader
import os

from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta

from suggestion_candidate_generator.initializer import Initializer
from suggestion_candidate_generator.scc_integrator import SCCIntegrator

import logging


class Loader(AbstractLoader):

    def printSummary(self, name, df, rowCount=10):

        print(f"Data frame: {name}:" + str(df.shape))
        print("*******************************")
        if Constants.DEBUG_MODE:
            df.info()
            print(df.head(rowCount))

    def qa_module(self):
        data = Data.get_instance()

        channel_cooloff_df = data.get_dataframe("channel_cooloff")
        content_tags_df = data.get_dataframe("content_tags")
        content_cooloff_df = data.get_dataframe("content_cooloff")
        simulation_rate_df = data.get_dataframe("simulation_rate")
        channels_df = data.get_dataframe("channels")
        reward_params_df = data.get_dataframe("reward_params")

        drl_scenario_df = data.get_dataframe(f"drl_scenario")
        drl_config_df = data.get_dataframe("drl_config")

        if Constants.EXECUTION_MODE:
            interactions_df = data.get_dataframe("ap_cross_channel")
        else:
            interactions_df = data.get_dataframe("interactions")

        initial_dsl_channel_df = data.get_dataframe("initial_dsl_channel")
        initial_dsl_content_df = data.get_dataframe("initial_dsl_content")

        self.printSummary("channel_cooloff",
                          channel_cooloff_df[['currentChannel', 'nextChannel', 'hcpSegment', 'days']], 100)
        self.printSummary("content_tags_df", content_tags_df)
        self.printSummary("content_cooloff_df", content_cooloff_df)
        self.printSummary("simulation_rate_df",
                          simulation_rate_df[['versionId', 'month_offset_from_last', 'simulation_percent']])
        self.printSummary("channels", channels_df)
        self.printSummary("reward_params_df", reward_params_df[['versionId', 'dimValue', 'impact', 'cost']])

        self.printSummary("drl_scenario_df", drl_scenario_df)
        self.printSummary("drl_config_df", drl_config_df)
        self.printSummary("initial_dsl_channel_df", initial_dsl_channel_df)
        self.printSummary("initial_dsl_content_df", initial_dsl_content_df)

        self.printSummary("eligible_accounts_summary_df", data.get_dataframe("eligible_accounts_summary"))
        self.printSummary("interactions_df", interactions_df)

        if Constants.LEARNING_MODE or Constants.EXECUTION_MODE:
            assert interactions_df.empty == False, "Interactions dataframe is empty."
            assert initial_dsl_channel_df.empty == False, "Initial DSL Channel dataframe is empty."
            assert drl_config_df.empty == False, "DRL Config dataframe is empty."
            assert channels_df.empty == False, "Channels dataframe is empty."
            assert channel_cooloff_df.empty == False, "Channel cooloff dataframe is empty."
            assert reward_params_df.empty == False, "Reward params dataframe is empty."

    def __get_spark(self):

        findspark.init()
        os.environ['PYSPARK_SUBMIT_ARGS'] = "--driver-memory 12g --packages=com.amazonaws:aws-java-sdk-bundle:1.11.199," \
                                            "org.apache.hadoop:hadoop-aws:3.3.1,io.delta:delta-core_2.12:1.0.0 " \
                                            "pyspark-shell "

        sc = pyspark.SparkContext()
        sc.setSystemProperty("com.amazonaws.services.s3.enableV4", "true")
        sc.setLogLevel("WARN")
        hadoop_conf = sc._jsc.hadoopConfiguration()
        hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
        hadoop_conf.set("com.amazonaws.services.s3.enableV4", "true")
        if Constants.LOCAL_MODE:
            hadoop_conf.set("fs.s3a.access.key", Constants.AWS_ACCESS_KEY_ID)
            hadoop_conf.set("fs.s3a.secret.key", Constants.AWS_SECRET_ACCESS_KEY)
            hadoop_conf.set("fs.s3a.session.token", Constants.AWS_SESSION_TOKEN)
        hadoop_conf.set("fs.s3a.connection.maximum", "100000")
        # hadoop_conf.set("fs.s3a.endpoint", "s3." + constants.aws_region + ".amazonaws.com")
        hadoop_conf.set("fs.s3a.endpoint", "s3.amazonaws.com")
        hadoop_conf.set("delta.logRetentionDuration", "36500")
        hadoop_conf.set("delta.deletedFileRetentionDuration", "365")

        spark = SparkSession(sc) \
            .builder \
            .appName(Constants.SPARK_APP_NAME) \
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
            .config("spark.sql.debug.maxToStringFields", 1000) \
            .getOrCreate()

        print("Spark Initialized.")
        return spark

    def read_feature_store_adl(self):

        data = Data.get_instance()

        if Constants.TEST_MODE:
            hcp_fs_pdf = pd.read_csv(
                "/Users/<USER>/code/learning_develop/learning/modelFactory/channel_propensity/tests/data/hcp_fs_pdf.csv")
            # Select random 200000 rows from dataframe
            hcp_fs_pdf = hcp_fs_pdf.sample(n=100000, random_state=7)
            hcp_fs_pdf['yearMonth'] = hcp_fs_pdf['yearMonth']
            data.set_dataframe("hcp_fs_pdf", hcp_fs_pdf)
            return

        spark = self.__get_spark()
        hcp_feature_store_path = Constants.ADL_S3_PATH + "data/silver/hcpFeatureStore"
        print("Reading HCP Feature Store from Path: " + hcp_feature_store_path)
        hcp_fs_pdf = spark.read.format("delta").load(hcp_feature_store_path).toPandas()

        print("HCP Feature Store-")
        print(hcp_fs_pdf)

        data.set_dataframe("hcp_fs_pdf", hcp_fs_pdf)

    def get_simulaton_months(self):
        #Returns the number of months of data to use for learning simulation
        # if not previously computed, compute and store as maximum # from simulation_rate
        data = Data.get_instance()
        simulaton_months = data.get_param("simulaton_months", None)
        if not simulaton_months:
            print("Computing simulaton_months...")
            simulation_rate_df = data.get_dataframe("simulation_rate")
            if simulation_rate_df is not None:
                simulaton_months = int(simulation_rate_df['month_offset_from_last'].max()) + 1
            else:
                simulaton_months = 6
            data.set_param("simulaton_months", simulaton_months)
            print(f"Saving simulaton_months as {simulaton_months}")

        return simulaton_months

    def get_history_start_date(self):
        #Returns the start date for historic data to use for learning simulation
        # if not previously computed, compute and store as first_day_of_current_month - simulation-offset-months - #_of_simulation_months
        data = Data.get_instance()
        history_start_date = data.get_param("history_start_date", None)
        if history_start_date is None:
            print("Computing history_start_date...")
            history_months = int(data.get_param('SIMULATION_HISTORY_MONTH_OFFSET', 2)) + self.get_simulaton_months()

            # Convert rundate to datetime
            run_date = data.get_param("rundate")
            if isinstance(run_date, str):
                run_date = datetime.strptime(run_date, "%Y-%m-%d").date()

            today = run_date  #TODO: Replace this with start date or rundate
            first_day_of_month = today.replace(day=1)
            history_start_date = first_day_of_month - relativedelta(months=history_months-1)
            history_start_date = pd.Timestamp(history_start_date)
            print("Type of history_start_date:" + str(type(history_start_date)))
            data.set_param("history_start_date", history_start_date)
            print(f"Saving history_start_date as {str(history_start_date)}")

        return history_start_date

    def get_history_end_date(self):
        #Returns the end date for historic data to use for learning simulation
        # if not previously computed, compute and store as history_start_date + simulation_months
        data = Data.get_instance()
        history_end_date = data.get_param("history_end_date", None)
        if history_end_date is None:
            print("Computing history_end_date...")
            history_start_date = self.get_history_start_date()
            simulaton_months = self.get_simulaton_months()
            history_end_date = history_start_date + relativedelta(months=simulaton_months)
            history_end_date = pd.to_datetime(history_end_date)
            print("Type of history_start_date:" + str(type(history_start_date)))
            data.set_param("history_end_date", history_end_date)
            print(f"Saving history_end_date as {str(history_end_date)}")

        return history_end_date
    
    def get_exec_history_start_end_date(self):
        data = Data.get_instance()
        history_end_date = data.get_param("history_end_date", None)
        history_start_date = data.get_param("history_start_date", None)
        if not history_end_date or not history_start_date:
            exec_start_date = data.get_param("startdate")
            # history_end_date = exec_start_date - relativedelta(days=1)
            history_end_date = exec_start_date
            exec_history_days = data.get_param("EXEC_HISTORY_DAYS", 60)
            history_start_date = history_end_date - relativedelta(days=exec_history_days)
            history_end_date = pd.to_datetime(history_end_date)
            history_start_date = pd.to_datetime(history_start_date)
            data.set_param("history_end_date", history_end_date)
            data.set_param("history_start_date", history_start_date)
            print(f"Saving history_start_date as {str(history_start_date)}")
            print(f"Saving history_end_date as {str(history_end_date)}")

        return history_start_date, history_end_date

    def read_referenceData(self):
        #Channel-cooloff, Content list, Content cooloff, Message priority, Simulation-rate
        print("Reading reference data...")
        data = Data.get_instance()
        #data.set_dataframe("channel_cooloff", DataAccessLayer.runMySqlQuery("select * from DRL_Channel_CoolOffDays", Constants.learning_db))
        data.set_dataframe("content_tags",
                           DataAccessLayer.runMySqlQuery("select * from DRL_ContentTag", Constants.learning_db))
        data.set_dataframe("content_cooloff", DataAccessLayer.runMySqlQuery("select * from DRL_Content_CoolOffDays",
                                                                            Constants.learning_db))
        data.set_dataframe("simulation_rate", DataAccessLayer.runMySqlQuery("select * from DRL_Monthly_Simulation_Rate",
                                                                            Constants.learning_db))

    def create_scenario(self, scenarioUid, strategy):

        # insert into DRL_Scenario (scenarioUid, scenarioName, scenarioDescription, isPublished) VALUES ('NEW_SCENARIO', 'NEW_SCENARIO', 'NEW_SCENARIO', 0);

        scenarioName = scenarioUid
        isPublished = 0
        startDate = 'null'
        endDate = 'null'
        if strategy:
            scenarioName = strategy.get("name")
            startDate = ' STR_TO_DATE("' + strategy.get("startDate") + '", "%Y-%m-%d") '
            endDate = ' STR_TO_DATE("' + strategy.get("endDate") + '", "%Y-%m-%d") '
            isPublished = 1

        sql = f"insert into DRL_Scenario (scenarioUid, scenarioName, scenarioDescription, isPublished, startDate, endDate, useSCC) " \
              f" VALUES ('{scenarioUid}', '{scenarioName}', '{scenarioName}', {isPublished}, {startDate}, {endDate}, {isPublished})"
        DataAccessLayer.executeMySql(sql, Constants.learning_db)

        scenario_df = DataAccessLayer.runMySqlQuery(f"select * from DRL_Scenario where scenarioUid = '{scenarioUid}'",
                                                    Constants.learning_db)
        return scenario_df

    def create_scenario_params(self, scenarioUid):

        # insert into DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup)
        # select 'NEW_SCENARIO', configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup from DRL_Config_Params where versionId = '__DEFAULT__';

        # INSERT into DRL_Reward_Params (versionId, dimension, dimValue, impact, cost)
        # select 'NEW_SCENARIO', dimension, dimValue, impact, cost from DRL_Reward_Params where versionId = '__DEFAULT__';

        sql = f"insert into DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) " + \
              f"select '{scenarioUid}', configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup from DRL_Config_Params c1 where versionId = '__DEFAULT__'" + \
              f" and not exists (select 1 from DRL_Config_Params c2 where c2.versionId = '{scenarioUid}' and c2.configName = c1.configName ) "
        DataAccessLayer.executeMySql(sql, Constants.learning_db)

        sql = f"INSERT into DRL_Reward_Params (versionId, dimension, dimValue, impact, cost) " + \
              f"select '{scenarioUid}', dimension, dimValue, impact, cost from DRL_Reward_Params c1 where versionId = '__DEFAULT__'" + \
              f" and not exists (select 1 from DRL_Reward_Params c2 where c2.versionId = '{scenarioUid}' and c2.dimension = c1.dimension and c2.dimValue = c1.dimValue ) "
        DataAccessLayer.executeMySql(sql, Constants.learning_db)

        sql = f"INSERT into DRL_Simulator_Input (hcpSegment, channel, acceptanceThreshold, adjustmentFactor, versionId) " + \
              f"select hcpSegment, channel, acceptanceThreshold, adjustmentFactor, '{scenarioUid}' from DRL_Simulator_Input c1 where versionId = '__DEFAULT__'" + \
              f" and not exists (select 1 from DRL_Simulator_Input c2 where c2.versionId = '{scenarioUid}' and coalesce(c2.hcpSegment,'') = coalesce(c1.hcpSegment,'') and coalesce(c2.channel,'') = coalesce(c1.channel,'') ) "
        DataAccessLayer.executeMySql(sql, Constants.learning_db)

    def str2bool(self, v):
        return v.lower() in ("yes", "true", "t", "1")

    def read_attributeTypeDefinitions(self):
        # Scenario, Config, (Reward-params will list be a section/group within DRL_Config)
        print("Reading attribute type definition data...")
        data = Data.get_instance()
        attributeTypeDefinition_df = DataAccessLayer.runMySqlQuery(f"select * from AttributeTypeDefinition",
                                                                   Constants.engine_db + "_scc")
        data.set_dataframe("attributeTypeDefinition", attributeTypeDefinition_df)
        #self.printSummary("attribute-defs", attributeTypeDefinition_df)
        return attributeTypeDefinition_df

    def get_attributeTypeDefinition(self, attributeTypeDef_df, typeName):
        attributeTypeDef_df = attributeTypeDef_df[attributeTypeDef_df['attributeTypeDefinitionName'] == typeName]
        if not attributeTypeDef_df.empty:
            return attributeTypeDef_df['s3ObjectName'].iloc[0], attributeTypeDef_df['s3FieldName'].iloc[0], \
            attributeTypeDef_df['rdsObjectName'].iloc[0], attributeTypeDef_df['rdsFieldName'].iloc[0]
        return None, None

    def update_audience_source(self, sccapi, strategy):
        attributeTypeDefinition_df = self.read_attributeTypeDefinitions()
        audiences = strategy.get('audiences')

        # Build list of attributes to include in SQL and with a name
        updated_audiences = []
        attributeTypeDefinitionNames = []
        for attributeType in audiences:
            #print(f"  Type:{attributeType.get('typeDefinitionName')}, Values:{attributeType.get('typeNames')}")
            # Find the row in the df and create s3objectname and s3columnname in the attributeType object
            s3object, s3column, rdsobject, rdscolumn = self.get_attributeTypeDefinition(attributeTypeDefinition_df,
                                                                                        attributeType.get(
                                                                                            'typeDefinitionName'))
            attributeType["s3object"] = s3object
            attributeType["s3column"] = s3column
            attributeType["rdsobject"] = rdsobject
            attributeType["rdscolumn"] = rdscolumn
            attributeTypeDefinitionNames.append(attributeType.get('typeDefinitionName'))
            updated_audiences.append(attributeType)
        attributeValues = sccapi.getAllAttributeTypes(attributeTypeDefinitionNames)
        data = Data.get_instance()
        strategy['audiences'] = updated_audiences
        data.set_param('audiences', updated_audiences)
        data.set_param('attributeValues', attributeValues)
        print("Updated audiences:" + str(updated_audiences))
        print("Attribute values:" + str(attributeValues))
        return strategy

    def read_strategy(self, scenarioUid):
        data = Data.get_instance()

        sccapi, strategy = SCCIntegrator.readStrategy(data.get_param("region"), data.get_param("customer"),
                                                      data.get_param("env"), scenarioUid)
        strategy = self.update_audience_source(sccapi, strategy)
        data.set_param("strategy", strategy)
        data.set_param("sccapi", sccapi)

        # Convert 'audiences' data into sql
        # Read attribute-type-definition and find table-name/column-name
        # Build SQL for join condition e.g. inner join accountproduct_cdc_v or account_dse_cdc_v and rep-account-assignment
        #select attributetypedefinitionname, s3objectname, s3fieldname from attributetypedefinition_v
        # those tables have updatedAt.  Find the previous value to know range when it was valid and then join with interaction ???
        # Bring those attribute values as possible dimensions to DRL ?
        # Build SQL for where condition

        return strategy

    def get_scenario_start_and_end_dates(self, scenarioUid):
        data = Data.get_instance()
        scenario_df = data.get_dataframe("drl_scenario")

        # Get scenario/strategy's start and end dates
        scenario_start = scenario_df['startDate'].iloc[0]
        scenario_end = scenario_df['endDate'].iloc[0]

        if scenario_start is None or scenario_end is None or scenario_start == "" or scenario_end == "":
            raise Exception(f"Scenario '{scenarioUid}' has no start or end date")

        if isinstance(scenario_start, str):
            scenario_start = datetime.datetime.strptime(scenario_start, "%Y-%m-%d").date()

        if isinstance(scenario_end, str):
            scenario_end = datetime.datetime.strptime(scenario_end, "%Y-%m-%d").date()

        return scenario_start, scenario_end

    def compute_next_execution_period(self, scenarioUid):

        data = Data.get_instance()
        # Given the scenario start/end dates, find the next execution month after the last execution month (in DRL_Run table)
        scenario_start, scenario_end = self.get_scenario_start_and_end_dates(scenarioUid)
        # Convert to datetime if it's string
        # Find end-date of last execution
        exec_start_date = scenario_start
        last_exec_end_date = self.get_last_execution_end_date(scenarioUid)
        if last_exec_end_date is not None:
            logging.info(f"Scenario's last execution end date: {last_exec_end_date}")
            exec_start_date = last_exec_end_date + relativedelta(days=1)

        # If last execution is done, stop
        if exec_start_date > scenario_end:
            raise Exception(f"Scenario '{scenarioUid}' has already been executed till it's end date: '{scenario_end}'")

        # Compute month end as the end-date
        first_day_of_month = exec_start_date.replace(day=1)
        exec_end_date = first_day_of_month + relativedelta(months=1) - relativedelta(days=1)
        exec_end_date = min(exec_end_date, scenario_end)

        data.set_param("startdate", exec_start_date)
        data.set_param("enddate", exec_end_date)

        # Set rundate as string format of start date
        data.set_param("rundate", exec_start_date.strftime("%Y-%m-%d"))

        logging.warning(f"Calculated and using next execution period: {exec_start_date} to {exec_end_date}")

    def get_last_execution_end_date(self, scenarioUid):
        last_exec_end_date = None
        last_execution_df = DataAccessLayer.runMySqlQuery(
            f"select max(endDate) as max_end_date from DRL_Run where scenarioUid = '{scenarioUid}' and action like '%execution%' ",
            Constants.learning_db)
        # self.printSummary("last_execution_df", last_execution_df)
        if not last_execution_df.empty and last_execution_df['max_end_date'].iloc[0] and \
                last_execution_df['max_end_date'].iloc[0] != "0000-00-00":
            last_exec_end_date = last_execution_df['max_end_date'].iloc[0]
            if isinstance(last_exec_end_date, str):
                last_exec_end_date = datetime.strptime(last_exec_end_date, "%Y-%m-%d").date()

        return last_exec_end_date

    def read_scenarioData(self, scenarioUid):
        # Scenario, Config, (Reward-params will list be a section/group within DRL_Config)
        print("Reading scenario data...")
        data = Data.get_instance()

        # Read scenario
        scenario_df = DataAccessLayer.runMySqlQuery(f"select * from DRL_Scenario where scenarioUid = '{scenarioUid}'",
                                                    Constants.learning_db)

        # Read strategy if job command-line (or scenario) says to use strategy
        strategy = None
        useSCC = self.str2bool(data.get_param("useSCC", "false"))
        if not scenario_df.empty:
            useSCC = useSCC or self.str2bool(str(scenario_df['useSCC'].iloc[0]))
        if useSCC:
            print(f"Reading strategy from SCC for id={scenarioUid}")
            strategy = self.read_strategy(scenarioUid)

        # Create scenario if it doesn't already exist
        autocreate = data.get_param("autocreate", "false")
        if (scenario_df.empty):
            if (autocreate.lower() == "true"):
                # Scenario doesn't exist.  Create it
                if useSCC and (strategy is None or not strategy.get("name")):
                    #strategy could not be retrieved, fail
                    print(
                        f"Could not retrieve Strategy details for id={scenarioUid}... Check SCC connection for: {data.get_param('region', '')}, {data.get_param('customer', '')}, {data.get_param('env', '')} ")
                    exit(-1)
                scenario_df = self.create_scenario(scenarioUid, strategy)
            else:
                print(f"Scenario:'{scenarioUid}' not found")
                exit(-1)

        # Create any missing scenario params
        if (autocreate.lower() == "true"):
            # Scenario params may not exist.  Create it if it doesn't exist
            self.create_scenario_params(scenarioUid)

        data.set_dataframe("drl_scenario", scenario_df)

        config_params_df = DataAccessLayer.runMySqlQuery(
            f"select * from DRL_Config_Params where versionId = '{scenarioUid}'", Constants.learning_db)
        data.set_dataframe("drl_config", config_params_df)
        print("Scenario params:")
        for index, row in config_params_df.iterrows():
            print(f"{row['configName']} = {row['configValue']}")
            val = row['configValue']
            if (row['configDataType'] == "integer"):
                val = int(val)
            if (row['configDataType'] == "float"):
                val = float(val)
            if (row['configDataType'] == "boolean"):
                val = self.str2bool(val)
            data.set_param(row['configName'], val)

        reward_params_df = DataAccessLayer.runMySqlQuery(
            f"select * from DRL_Reward_Params where versionId = '{scenarioUid}'", Constants.learning_db)
        data.set_dataframe("reward_params", reward_params_df)

        reward_components_pdf = DataAccessLayer.runMySqlQuery(
            f"select * from DRL_Reward_Components where versionId = '{scenarioUid}' and enabled=1",
            Constants.learning_db)
        data.set_dataframe("reward_components", reward_components_pdf)

        simulator_input_pdf = DataAccessLayer.runMySqlQuery(
            f"select * from DRL_Simulator_Input where versionId = '{scenarioUid}'", Constants.learning_db)
        data.set_dataframe("simulator_input", simulator_input_pdf.drop(columns=['versionId', 'createdAt', 'updatedAt']))

        cooloff_df = DataAccessLayer.runMySqlQuery(
            f"select * from DRL_Channel_CoolOffDays where versionId = '{scenarioUid}'", Constants.learning_db)
        if cooloff_df.empty:
            cooloff_df = DataAccessLayer.runMySqlQuery(
                f"select * from DRL_Channel_CoolOffDays where versionId = '__DEFAULT__'", Constants.learning_db)
        data.set_dataframe("channel_cooloff", cooloff_df.drop(columns=['versionId', 'createdAt', 'updatedAt']))

    def __create_audience_filter(self, audiences):
        '''
        Format of the audience_json is:
              [{'typeDefinitionName': 'Tiers',
              'typeNames': ['T3', 'T2'],
              's3object': 'account_dse_cdc_v',
              's3column': 'enumHCPTier_akt',
              'rdsobject': 'Account',
              'rdscolumn': 'enumHCPTier_akt'},
             {'typeDefinitionName': 'Marketing Opt Out',
              'typeNames': ['N/A'],
              's3object': 'account_dse_cdc_v',
              's3column': 'marketingOptOut_akt',
              'rdsobject': 'Account',
              'rdscolumn': 'marketingOptOut_akt'},
             {'typeDefinitionName': 'Sample Consent',
              'typeNames': ['Sim'],
              's3object': 'account_dse_cdc_v',
              's3column': 'sampleConsent_akt',
              'rdsobject': 'Account',
              'rdscolumn': 'sampleConsent_akt'}]
        '''
        where_filter = ""
        s3_obj_map = {'account_dse_cdc_v': 'a',
                      'accountproduct_v': 'ap',
                      'repaccountassignment_cdc_v': 'ra'}
        for aud in audiences:
            if where_filter != "":
                where_filter += " and "
            print(aud['s3column'])
            print(aud['typeNames'])
            audience_values = aud['typeNames']
            # Convert items in audience_values to right type
            for i in range(len(audience_values)):
                try:
                    if audience_values[i].isnumeric():
                        audience_values[i] = eval(audience_values[i])
                except:
                    pass
            where_filter += f"{s3_obj_map[aud['s3object']]}.{aud['s3column']} in {str(audience_values).replace('[', '(').replace(']', ')')}"
        return where_filter

    def __create_product_filter(self, strategy):
        data = Data.get_instance()
        if strategy is None or strategy.get("productUID") is None:
            return ""

        productuid = strategy.get("productUID")
        if data.get_param("ENABLE_PRODUCT_GROUP_MAPPING"):
            product_group_mapping = data.get_dataframe("product_group_mapping")
            parent_productuid = product_group_mapping.loc[product_group_mapping.productUID == productuid, 'parentProductUID'].iloc[0]

            filter = f" and pgmv.parentProductUID = '{parent_productuid}'"
        else:
            filter = f" and v.productUid = '{productuid}' "
        return filter

    def __get_account_ids_for_strategy_rep_team(self, strategy):
        if strategy is None or strategy.get("repTeamUID") is None:
            return None

        rep_team_uid = strategy.get("repTeamUID")
        logging.info(f"Getting account ids for rep team '{rep_team_uid}'")
        # Get list of reps from RDS
        query = (f"select distinct(raa.accountId) from RepAccountAssignment raa"
                 f" where raa.repId IN (select rtr.repId from RepTeamRep rtr"
                 f" inner join RepTeam rt on rt.repTeamId = rtr.repTeamId"
                 f" where rt.externalId ='{rep_team_uid}')")

        rep_account_pdf = DataAccessLayer.runMySqlQuery(query, Constants.engine_db)

        # if list is empty log warning
        if rep_account_pdf is None or rep_account_pdf.empty:
            logging.warning(f"No accounts found for rep team '{rep_team_uid}'")
            return None

        return rep_account_pdf

    def __get_interactions_cols_to_read(self):
        data = Data.get_instance()

        cols = "accountId, accountUid, productId, productName, productUid, repId, repUid, currentChannel, activityDate, interactionId"

        addition_feature_cols = data.get_param("additional_feature_cols", [])
        for feature_col in addition_feature_cols:
            cols += f", {feature_col}"

        audiences = data.get_param("audiences")
        audience_cols = []
        for aud in audiences:
            cols += f", {aud['s3column']}"
            audience_cols.append(aud['s3column'])

        # Remove duplicates in cols
        cols = ", ".join(list(set(cols.split(", "))))
        data.set_param("audience_cols", audience_cols)

        return cols

    def __get_account_ids_audience_filter(self, prediction_date):

        query = """SELECT distinct a.accountId, a.externalId accountUid FROM
                    (((account_dse_v a)
                    LEFT JOIN accountproduct_v ap ON ((a.accountId = ap.accountId)))
                    LEFT JOIN repaccountassignment_v ra ON ((a.accountId = ra.accountId)))"""

        data = Data.get_instance()
        audience = data.get_param("audiences")
        audience_filter = self.__create_audience_filter(audience)
        if audience_filter != "":
            query += f" WHERE {audience_filter}"

        params = data.get_param("connect_params")
        athena_reader = AthenaReader()
        athena_reader.connect(aws_access_key=params.get("athena-username"),
                              aws_secret_key=params.get("athena-password"),
                              session_token="", aws_region=params.get("athena-region"),
                              athena_staging_bucket=params.get("athena-s3bucket"),
                              athena_staging_dir=params.get("athena-stagedir"), schema=params.get("athena-schema"))
        account_id_pdf = athena_reader.query(query=query)

        print(
            f"Read {len(account_id_pdf)} accounts after rep team filter from Athena based on audience filter = {audience_filter}")

        return account_id_pdf

    def __get_account_ids_audience_filter_rds(self):
        query = """SELECT distinct a.accountId, a.externalId accountUid FROM
                            (((Account a)
                            LEFT JOIN AccountProduct ap ON ((a.accountId = ap.accountId)))
                            LEFT JOIN RepAccountAssignment ra ON ((a.accountId = ra.accountId)))"""

        data = Data.get_instance()
        audience = data.get_param("audiences")
        audience_filter = self.__create_audience_filter(audience)
        if audience_filter != "":
            query += f" WHERE {audience_filter}"

        account_id_pdf = DataAccessLayer.runMySqlQuery(query, Constants.engine_db)

        print(
            f"Read {len(account_id_pdf)} accounts after rep team filter from RDS based on audience filter = {audience_filter}")

        return account_id_pdf

    def read_interactions_athena(self, history_start_date, history_end_date):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        athena_reader = AthenaReader()
        athena_reader.connect(aws_access_key=params.get("athena-username"),
                              aws_secret_key=params.get("athena-password"),
                              session_token="", aws_region=params.get("athena-region"),
                              athena_staging_bucket=params.get("athena-s3bucket"),
                              athena_staging_dir=params.get("athena-stagedir"), schema=params.get("athena-schema"))
        cols = self.__get_interactions_cols_to_read()

        # Read empty table for structure
        empty_query = "select * from DRL_Interaction_History_v limit 0"
        empty_pdf = athena_reader.query(query=empty_query)

        # Remove cols not in the empty table check case-insensitive

        removed_cols = [col for col in cols.split(", ") if col.lower() not in map(str.lower, empty_pdf.columns)]
        cols = ", ".join([col for col in cols.split(", ") if col.lower() in map(str.lower, empty_pdf.columns)])
        print(f"Removed cols not exists in the DRL_Interaction_History_V on Athena: {removed_cols}")

        # Read list cols of the Athena view

        activity_date_filter = f"activityDate >= date_parse('{history_start_date.strftime('%Y-%m-%d')}', '%Y-%m-%d') and activityDate < date_parse('{history_end_date.strftime('%Y-%m-%d')}', '%Y-%m-%d')"
        query = f"select {cols} from DRL_Interaction_History_v" \
                f" where {activity_date_filter}"

        product_filter = self.__create_product_filter(data.get_param("strategy"))
        query += f"{product_filter}"

        interactions_pdf = athena_reader.query(query=query)
        print(
            f"Read {len(interactions_pdf)} unfiltered interactions from Athena for {history_start_date} to {history_end_date}")

        if interactions_pdf is None or interactions_pdf.empty:
            logging.warning(f"No interactions found in Athena for {history_start_date} to {history_end_date}")
            raise Exception(f"No interactions found in Athena for {history_start_date} to {history_end_date}")

        if Constants.EXECUTION_MODE or data.get_param('ENABLE_AUDIENCE_FILTER_IN_LEARNING', False):

            # Get the accounts that based on the audience filter
            aud_filter_account_id_pdf = self.__get_account_ids_audience_filter(history_end_date)
            audience_filtered_interactions_df = pd.merge(interactions_pdf, aud_filter_account_id_pdf['accountId'],
                                                         on='accountId', how='inner')
            print(f"After audience filter found {len(audience_filtered_interactions_df)} interactions from Athena")

            if audience_filtered_interactions_df is None or audience_filtered_interactions_df.empty:
                logging.warning("No interactions passed audience filter")
                raise Exception("No interactions passed audience filter")

            # Get the latest date in the interactions
            latest_date = audience_filtered_interactions_df['activityDate'].max()
            latest_date = datetime.strptime(latest_date, "%Y-%m-%d").date()

            # Check if latest_date is less than history_end_date
            if pd.Timestamp(latest_date) < history_end_date:
                # Read latest interactions from RDS
                additional_interactions_pdf = self.read_latest_interactions_rds(latest_date, history_end_date)

                # Remove accountIds that are not in the Athena interactions
                additional_interactions_pdf = pd.merge(additional_interactions_pdf,
                                                       audience_filtered_interactions_df['accountId'], on='accountId',
                                                       how='inner')
                print(f"After account filter found {len(additional_interactions_pdf)} additional interactions from RDS")

                # Concatenate the additional interactions to the Athena interactions avoid duplicates in accountId, repId, productId, activityDate
                audience_filtered_interactions_df = pd.concat(
                    [audience_filtered_interactions_df, additional_interactions_pdf]).drop_duplicates(
                    subset=['accountId', 'repId', 'productId', 'activityDate', 'currentChannel'])

            # Generate output only for AccountIds for the strategy's rep team
            rep_account_id_pdf = self.__get_account_ids_for_strategy_rep_team(data.get_param("strategy"))
            if rep_account_id_pdf is not None and len(rep_account_id_pdf) > 0:
                #print(f"Filtering interactions for rep team > accountID list: {account_id_list}")
                audience_filtered_interactions_df = pd.merge(audience_filtered_interactions_df, rep_account_id_pdf,
                                                             on='accountId', how='inner')
                print(f"After rep team filter found {len(audience_filtered_interactions_df)} interactions from Athena")
            else:
                logging.warning("Unable to apply filtering based on RepTeam in strategy")

            interactions_pdf = audience_filtered_interactions_df

        interactions_pdf['activityDate'] = pd.to_datetime(interactions_pdf['activityDate'])

        data.set_dataframe("interactions", interactions_pdf)
        data.set_dataframe('interactions_raw', interactions_pdf)
        # Update the eligible accounts to intersection (i.e. all the accounts passed the audience and rep filter)
        data.set_dataframe("eligible_accounts_summary", interactions_pdf[['accountId', 'accountUid',
                                                                          'productId', 'productName',
                                                                          'productUid']].drop_duplicates())

    def read_interactions_rds(self, history_start_date, history_end_date):
        data = Data.get_instance()

        column_query = (f"SELECT TABLE_NAME, COLUMN_NAME FROM information_schema.COLUMNS "
                        f"WHERE TABLE_SCHEMA IN ('{Constants.engine_db}', '{Constants.learning_db}') "
                        f"AND TABLE_NAME IN ('Account', 'AccountProduct', "
                        f"'RepAccountAssignment', 'DRL_Interaction_History_v')")
        columns_df = DataAccessLayer.runMySqlQuery(column_query, Constants.engine_db)

        table_column_df = columns_df.groupby('TABLE_NAME')['COLUMN_NAME'].apply(list).reset_index()
        table_column_dict = table_column_df.set_index('TABLE_NAME')['COLUMN_NAME'].to_dict()

        select_cols = ''
        seen_cols = set()
        table_dict = {'DRL_Interaction_History_v': 'v.', 'Account': 'a.', 'AccountProduct': 'ap.',
                      'RepAccountAssignment': 'raa.'}
        for table in ['DRL_Interaction_History_v', 'Account', 'AccountProduct', 'RepAccountAssignment']:
            for col in table_column_dict[table]:
                if col not in seen_cols:
                    select_cols += table_dict[table] + col + ', '
                    seen_cols.add(col)

        select_cols = select_cols[:-2]

        query = (f"SELECT {select_cols} "
                 f"FROM {Constants.learning_db}.DRL_Interaction_History_v v "
                 f"LEFT JOIN {Constants.engine_db}.Account a ON v.accountId = a.accountId "
                 f"LEFT JOIN {Constants.engine_db}.AccountProduct ap ON v.accountId = ap.accountId AND v.productId = ap.productId "
                 f"LEFT JOIN {Constants.engine_db}.RepAccountAssignment raa ON v.accountId = raa.accountId "
                 f"LEFT JOIN {Constants.learning_db}.Product_Group_Mapping_V pgmv ON v.productId = pgmv.productId ")

        activity_date_filter = f"activityDate >= '{history_start_date.strftime('%Y-%m-%d')}' and activityDate < '{history_end_date.strftime('%Y-%m-%d')}' "
        query += f"WHERE {activity_date_filter}"

        product_filter = self.__create_product_filter(data.get_param("strategy"))
        query += f"{product_filter}"

        interactions_pdf = DataAccessLayer.runMySqlQuery(query, Constants.engine_db)

        print(
            f"Read {len(interactions_pdf)} unfiltered interactions from RDS for {history_start_date} to {history_end_date}")

        if interactions_pdf is None or interactions_pdf.empty:
            logging.warning(f"No interactions found in RDS for {history_start_date} to {history_end_date}")
            raise Exception(f"No interactions found in RDS for {history_start_date} to {history_end_date}")

        # read event data
        query_event = (f"SELECT {select_cols} "
                 f"FROM {Constants.learning_db}.DRL_Event_Interaction_History_v v "
                 f"LEFT JOIN {Constants.engine_db}.Account a ON v.accountId = a.accountId "
                 f"LEFT JOIN {Constants.engine_db}.AccountProduct ap ON v.accountId = ap.accountId AND v.productId = ap.productId "
                 f"LEFT JOIN {Constants.engine_db}.RepAccountAssignment raa ON v.accountId = raa.accountId "
                 f"LEFT JOIN {Constants.learning_db}.Product_Group_Mapping_V pgmv ON v.productId = pgmv.productId ")
        
        query_event += f"WHERE {activity_date_filter}"
        query_event += f"{product_filter}"
        query_event += f" and v.eventTypeName = 'SFMC_Email_Sent' "

        events_pdf = DataAccessLayer.runMySqlQuery(query_event, Constants.engine_db)
        print(
            f"Read {len(events_pdf)} unfiltered events from RDS for {history_start_date} to {history_end_date}")
    
        interactions_pdf = pd.concat([interactions_pdf, events_pdf], ignore_index=True)
        
        
        if Constants.EXECUTION_MODE or data.get_param('ENABLE_AUDIENCE_FILTER_IN_LEARNING', False):

            # Get the accounts that based on the audience filter
            aud_filter_account_id_pdf = self.__get_account_ids_audience_filter_rds()
            audience_filtered_interactions_df = pd.merge(interactions_pdf, aud_filter_account_id_pdf['accountId'],
                                                         on='accountId', how='inner')
            print(f"After audience filter found {len(audience_filtered_interactions_df)} interactions from Athena")

            if audience_filtered_interactions_df is None or audience_filtered_interactions_df.empty:
                logging.warning("No interactions passed audience filter")
                raise Exception("No interactions passed audience filter")

            # Get the latest date in the interactions
            latest_date = audience_filtered_interactions_df['activityDate'].max()
            # latest_date = datetime.strptime(latest_date, "%Y-%m-%d").date()

            # Check if latest_date is less than history_end_date
            # if pd.Timestamp(latest_date) < history_end_date:
            #     # Read latest interactions from RDS
            #     additional_interactions_pdf = self.read_latest_interactions_rds(latest_date, history_end_date)

            #     # Remove accountIds that are not in the Athena interactions
            #     additional_interactions_pdf = pd.merge(additional_interactions_pdf,
            #                                            audience_filtered_interactions_df['accountId'], on='accountId',
            #                                            how='inner')
            #     print(f"After account filter found {len(additional_interactions_pdf)} additional interactions from RDS")

            #     # Concatenate the additional interactions to the Athena interactions avoid duplicates in accountId, repId, productId, activityDate
            #     audience_filtered_interactions_df = pd.concat(
            #         [audience_filtered_interactions_df, additional_interactions_pdf]).drop_duplicates(
            #         subset=['accountId', 'repId', 'productId', 'activityDate', 'currentChannel'])

            # Generate output only for AccountIds for the strategy's rep team
            rep_account_id_pdf = self.__get_account_ids_for_strategy_rep_team(data.get_param("strategy"))
            if rep_account_id_pdf is not None and len(rep_account_id_pdf) > 0:
                #print(f"Filtering interactions for rep team > accountID list: {account_id_list}")
                audience_filtered_interactions_df = pd.merge(audience_filtered_interactions_df, rep_account_id_pdf,
                                                             on='accountId', how='inner')
                print(f"After rep team filter found {len(audience_filtered_interactions_df)} interactions from Athena")
            else:
                logging.warning("Unable to apply filtering based on RepTeam in strategy")

            interactions_pdf = audience_filtered_interactions_df

        interactions_pdf['activityDate'] = pd.to_datetime(interactions_pdf['activityDate'])

        data.set_dataframe("interactions", interactions_pdf)
        data.set_dataframe('interactions_raw', interactions_pdf)
        # Update the eligible accounts to intersection (i.e. all the accounts passed the audience and rep filter)
        data.set_dataframe("eligible_accounts_summary", interactions_pdf[['accountId', 'accountUid',
                                                                          'productId', 'productName',
                                                                          'productUid']].drop_duplicates())

    def read_product_group_mapping(self):
        data = Data.get_instance()

        sql = f"select productId, parentProductId, productUID, parentProductUID from Product_Group_Mapping_V"
        product_group_mapping_pdf = DataAccessLayer.runMySqlQuery(sql, Constants.learning_db)

        if product_group_mapping_pdf.parentProductId.nunique() < product_group_mapping_pdf.shape[0]:
            print('Enable product group mapping')
            data.set_param('ENABLE_PRODUCT_GROUP_MAPPING', True)

        data.set_dataframe("product_group_mapping", product_group_mapping_pdf)

    def build_account_list_for_execution(self, history_start_date, history_end_date):
        data = Data.get_instance()

        strategy = data.get_param("strategy")
        productuid = strategy.get("productUID")

        # Read product details from interaction history
        product_query = f"select productId, productName, productUid from DRL_Interaction_History_v where productUid = '{productuid}' limit 1"
        product_pdf = DataAccessLayer.runMySqlQuery(product_query, Constants.learning_db)

        if product_pdf is None or product_pdf.empty:
            logging.warning(f"No interactions found in RDS for product {productuid} ")
            raise Exception(f"No interactions found in RDS for product {productuid} ")

        # Get the accounts that based on the audience filter
        aud_filter_account_id_pdf = self.__get_account_ids_audience_filter_rds()
        print(f"After audience filter found {len(aud_filter_account_id_pdf)} accounts from RDS")

        if aud_filter_account_id_pdf is None or aud_filter_account_id_pdf.empty:
            logging.warning("No accounts passed audience filter")
            raise Exception("No accounts passed audience filter")

        # Generate output only for AccountIds for the strategy's rep team
        rep_account_id_pdf = self.__get_account_ids_for_strategy_rep_team(data.get_param("strategy"))
        if rep_account_id_pdf is not None and len(rep_account_id_pdf) > 0:
            #print(f"Filtering interactions for rep team > accountID list: {account_id_list}")
            aud_filter_account_id_pdf = pd.merge(aud_filter_account_id_pdf, rep_account_id_pdf, on='accountId',
                                                 how='inner')
            print(f"After rep team filter found {len(aud_filter_account_id_pdf)} accounts from Athena")
        else:
            logging.warning("Unable to apply filtering based on RepTeam in strategy")

        eligible_accounts_summary = pd.merge(aud_filter_account_id_pdf, product_pdf, how='cross')
        # Update the eligible accounts to intersection (i.e. all the accounts passed the audience and rep filter)
        data.set_dataframe("eligible_accounts_summary", eligible_accounts_summary[['accountId', 'accountUid',
                                                                                   'productId', 'productName',
                                                                                   'productUid']].drop_duplicates())

    def read_latest_interactions_rds(self, start_date, end_date):
        print(f"Reading RDS interaction history from:'{start_date}' to '{end_date}'")
        data = Data.get_instance()
        query = f"select * from DRL_Interaction_History_v where activityDate > '{start_date}' and activityDate <'{end_date}'"
        product_filter = self.__create_product_filter(data.get_param("strategy"))
        if product_filter != "":
            query += f"{product_filter}"
        interactions_pdf = DataAccessLayer.runMySqlQuery(query, Constants.learning_db)
        print(f"Read {len(interactions_pdf)} interactions from RDS")
        return interactions_pdf

    def read_dsl_channel(self, start_date, end_date):
        data = Data.get_instance()
        # one_month_earlier = history_start_date - relativedelta(months=1)
        print(f"Reading prev interactions using {start_date} to {end_date}")
        initial_dsl_channel_sql = f"select accountId, currentChannel, max(interactionId) lastInteractionId , max(activityDate) lastActivityDate from DRL_Interaction_History_v where activityDate < '{end_date}' and activityDate > '{start_date}' group by accountId , currentChannel"
        initial_dsl_channel_df = DataAccessLayer.runMySqlQuery(initial_dsl_channel_sql, Constants.learning_db)
        # compute dsl for event
        initial_dsl_event_channel_sql = f"select accountId, currentChannel, max(interactionId) lastInteractionId , max(activityDate) lastActivityDate from DRL_Event_Interaction_History_v where activityDate < '{end_date}' and activityDate > '{start_date}' and eventTypeName = 'SFMC_Email_Sent' group by accountId, currentChannel"
        initial_dsl_event_channel_df = DataAccessLayer.runMySqlQuery(initial_dsl_event_channel_sql, Constants.learning_db)
        # combine interaction with event
        initial_dsl_channel_df = pd.concat([initial_dsl_channel_df, initial_dsl_event_channel_df])
        data.set_dataframe("initial_dsl_channel", initial_dsl_channel_df)
        # self.read_segment_history(history_start_date)
        # Note: Removing following line since we are not reading content tags into DRL model anymore since we plan to handle content assignment outside DRL
        #self.read_content_for_interaction(history_start_date)

    def read_segment_history(self, history_start_date, history_end_date):
        """
        TODO: Replace with new CJ segments with as of data for last 12 months year month
        """
        data = Data.get_instance()
        params = data.get_param("connect_params")

        # history_end_date = self.get_history_end_date().strftime('%Y-%m-%d')
        print(f"Reading segment history from:'{history_start_date} to {history_end_date}'")
        hcp_segment_type = data.get_param('hcp_segment_type', 'Combined')
        sql_query = f"select accountId, productId, segment as hcpSegment, segmentMonth from CJ_Output_Segment where segmentType='{hcp_segment_type}' and segmentMonth >= STR_TO_DATE('{history_start_date.strftime('%Y-%m-%d')}', '%Y-%m-%d') and segmentMonth < STR_TO_DATE('{history_end_date.strftime('%Y-%m-%d')}', '%Y-%m-%d')"
        segment_pdf = DataAccessLayer.runMySqlQuery(sql_query, Constants.learning_db)
        # Convert segmentMonth to datetime
        segment_pdf['segmentMonth'] = pd.to_datetime(segment_pdf['segmentMonth'])
        self.printSummary("segment_history", segment_pdf)
        data.set_dataframe("segment_history", segment_pdf)

    # Following method is no longer used since content is assigned later (after DRL)
    def read_content_for_interaction(self, history_start_date):
        data = Data.get_instance()
        params = data.get_param("connect_params")
        history_end_date = self.get_history_end_date()
        print(f"Reading content history from:'{history_start_date}' to '{history_end_date}' ")
        reader = AthenaReader()
        reader.connect(aws_access_key=params.get("athena-username"), aws_secret_key=params.get("athena-password"),
                       session_token="", aws_region=params.get("athena-region"),
                       athena_staging_bucket=params.get("athena-s3bucket"),
                       athena_staging_dir=params.get("athena-stagedir"), schema=params.get("athena-schema"))
        df = reader.query(
            query=f"select * from interaction_to_cms_doc_mapping_v where startDate >= date_parse('{history_start_date.strftime('%Y-%m-%d')}', '%Y-%m-%d') and startDate < date_parse('{history_end_date.strftime('%Y-%m-%d')}', '%Y-%m-%d')")
        #where as_of_date > '{history_start_date}'
        self.printSummary("content_history", df)
        data.set_dataframe("content_history", df)

    def build_intermediate_data(self):

        data = Data.get_instance()

        # Pre-build list of channels from channel-cooloff to use for simulation
        channel_cooloff_df = data.get_dataframe("channel_cooloff")
        channel_list = channel_cooloff_df.currentChannel.unique().tolist()
        channels_df = pd.DataFrame(channel_cooloff_df.drop_duplicates(subset="currentChannel")['currentChannel'])
        print("channels_df type:" + str(type(channels_df)))
        data.set_dataframe("channels", channels_df)

        # Pre-compute history end date for next steps
        self.get_history_end_date()

        # Append a row into content-tags list for channel = NO_ACTION and content = NA to use for simulation
        content_tags_df = data.get_dataframe("content_tags")
        content_tags_df = content_tags_df.rename(
            columns={'contentChannel': 'currentChannel', 'contentTagName': 'currentContent'})
        no_action_dict = {'contentTagId': 0, 'currentChannel': 'NO_ACTION', 'currentContent': 'NA',
                          'contentTagDescription': 'NA', 'contentWeight': 1.0, 'createdAt': date.today(),
                          'updatedAt': date.today()}
        content_tags_df = content_tags_df.append(no_action_dict, ignore_index=True)
        data.set_dataframe("content_tags", content_tags_df)

        #Keep the date as a pd dateime object to make date filters consistent in the dataframe, regardless of whether it is read from file, db or adl
        interactions_df = data.get_dataframe("interactions")
        if Constants.EXECUTION_MODE:
            static_cols = data.get_param("additional_feature_cols") + ['accountId', 'productId']
            static_features = interactions_df.drop_duplicates(subset=['accountId', 'productId'])[static_cols]
            distinct_account_product = data.get_dataframe("eligible_accounts_summary")
            interactions_df = pd.merge(distinct_account_product, channels_df, how='cross')
            interactions_df = pd.merge(interactions_df, static_features, on=['accountId', 'productId'], how='left')
            interactions_df['activityDate'] = data.get_param("rundate")

        interactions_df = interactions_df[interactions_df.currentChannel.isin(channel_list)]
        interactions_df['activityDate'] = pd.to_datetime(interactions_df['activityDate'], format='%Y-%m-%d')
        interactions_df['activityYearMonth'] = pd.to_datetime(interactions_df['activityDate']) - pd.offsets.MonthBegin(
            1)
        #interactions_df['activityYearMonth'] = interactions_df['activityDate'].replace(day=1)
        #interactions_df['currentContent'] = 'NA' #TODO: Replace with content from athena content-tagging

        # Merge content for each interaction
        # Assign a constant value of NA for all interactions since content assignment is not part of DRL
        interactions_df['currentContent'] = 'NA'
        #content_history_df = data.get_dataframe("content_history")
        #interactions_df = pd.merge(interactions_df, content_history_df, on='interactionId', how="left")
        #interactions_df = interactions_df.rename(columns={'topicName':'currentContent'})

        # get parent product for interactions
        # self.read_product_group_mapping()
        product_group_mapping_df = data.get_dataframe("product_group_mapping")
        product_group_mapping_df = product_group_mapping_df[['productId', 'parentProductId']]
        interactions_df = pd.merge(interactions_df, product_group_mapping_df, on='productId', how="left")
        interactions_df['parentProductId'] = interactions_df['parentProductId'].fillna(interactions_df['productId'])

        # Merge segment at time of interaction
        segment_history_df = data.get_dataframe("segment_history")
        # join interactions with segment
        if not Constants.EXECUTION_MODE:
            # if training mode, merge by each month
            interactions_df = pd.merge(interactions_df, segment_history_df,
                                       left_on=['accountId', 'parentProductId', 'activityYearMonth'],
                                       right_on=['accountId', 'productId', 'segmentMonth'], how="left",
                                       suffixes=('', '_y'))
            interactions_df = interactions_df.drop(columns=['segmentMonth', 'productId_y'])
        else:
            # if execution mode, use latest month segment
            latest_segment_df = segment_history_df[
                segment_history_df['segmentMonth'] == segment_history_df['segmentMonth'].max()]
            interactions_df = pd.merge(interactions_df, latest_segment_df, left_on=['accountId', 'parentProductId'],
                                       right_on=['accountId', 'productId'], how="left", suffixes=('', '_y'))
            interactions_df = interactions_df.drop(columns=['segmentMonth', 'productId_y'])
        interactions_df.info()
        interactions_df['hcpSegment'] = interactions_df['hcpSegment'].fillna('notier')
        # Update interactions with content, segment and date/month columns
        if Constants.EXECUTION_MODE:
            data.set_dataframe("ap_cross_channel", interactions_df)
        else:
            data.set_dataframe("interactions", interactions_df)

        # For now, make a copy of channel dsl for content as well with empty data
        #TODO: Need to change logic below to read content-tags
        initial_dsl_content_df = data.get_dataframe("initial_dsl_channel")
        initial_dsl_content_df = initial_dsl_content_df[(initial_dsl_content_df['accountId'] == -1)]
        initial_dsl_content_df = initial_dsl_content_df.rename(columns={"currentChannel": "currentContent"})
        data.set_dataframe("initial_dsl_content", initial_dsl_content_df)

        # Build unique list of channels and content using reference lists
        # channel_cooloff_df = data.get_dataframe("channel_cooloff")
        content_tags_df = data.get_dataframe("content_tags")
        # channel_list = channel_cooloff_df.currentChannel.unique().tolist()
        #channel_list.append('NO_ACTION')
        content_list = content_tags_df[(content_tags_df['currentContent'] != 'NA')].currentContent.unique().tolist()
        #content_list = content_tags_df.currentContent.unique().tolist()
        print("Channel list:" + str(channel_list))
        print("Content list:" + str(content_list))
        data.set_param("channel_list", channel_list)
        data.set_param("content_list", content_list)

        # Build segment list
        segment_list = interactions_df.hcpSegment.unique().tolist()
        productId_list = interactions_df.productId.unique().tolist()
        data.set_param("segment_list", segment_list)
        data.set_param("productId_list", productId_list)

    def load_models_for_execution(self):
        data = Data.get_instance()

        scenario_uid = data.get_param("scenario", "__BASE_SCENARIO__")
        model_csv_path = Constants.TARGET_S3_PATH + f"models/scenario={scenario_uid}/latest.csv"

        latest_pdf = DataAccessLayer.read_latest_csv(model_csv_path)

        model_s3_path = latest_pdf[latest_pdf.key == 'model_s3_path']['value'].iloc[0]
        network_input_cols = eval(latest_pdf[latest_pdf.key == 'network_input_cols']['value'].iloc[0])
        dsl_normalizer_s3_path = latest_pdf[latest_pdf.key == 'dsl_normalizer_s3_path']['value'].iloc[0]
        model_channel_list = eval(latest_pdf[latest_pdf.key == 'channel_list']['value'].iloc[0])

        model = DataAccessLayer.load_keras_model_from_s3(model_s3_path)
        dsl_normalizer = DataAccessLayer.read_model_pkl_from_s3(dsl_normalizer_s3_path)

        data.set_model("reward_predictor", model)
        data.set_param("dsl_normalizer", dsl_normalizer)
        data.set_param("network_input_cols", network_input_cols)
        data.set_param("model_channel_list", model_channel_list)

    def validate_and_set_start_end_dates(self):
        data = Data.get_instance()
        scenario_uid = data.get_param("scenario", "__BASE_SCENARIO__")
        if Constants.LEARNING_MODE:
            if Constants.INCREMENTAL_RUN:
                raise Exception("Incremental run is not supported in learning mode")
            else:
                # If run date or start date is provided use that
                if data.get_param("rundate", "") == "" and data.get_param('startdate', "") == "":
                    logging.warning(
                        "Run and start both dates are not provided. Using current date as run and start date")
                    data.set_param("startdate", date.today())
        elif Constants.EXECUTION_MODE:
            if Constants.INCREMENTAL_RUN:
                if data.get_param("startdate", "") == "" or data.get_param("enddate", "") == "":
                    # If start date and end dates are NOT provided
                    # Use start date as current date and end date as last execution end date
                    last_execution_end_date = self.get_last_execution_end_date(scenario_uid)
                    if last_execution_end_date is None:
                        raise Exception(
                            f"Last execution end date is not found for scenario '{scenario_uid}' for incremental run")
                    data.set_param("startdate", date.today())
                    data.set_param("enddate", last_execution_end_date)

                    if last_execution_end_date <= date.today():
                        print(
                            "No on-going run found last execution date is before today. Can not perform incremental run, try full execution run")
                        exit(0)
            else:  # Not the incremental run
                if data.get_param("startdate", "") == "" or data.get_param("enddate", "") == "":
                    # Start date and end dates are not provided
                    # Calculate the dates for next month's run
                    self.compute_next_execution_period(scenario_uid)
                else:
                    cmd_start_date = data.get_param("startdate")
                    cmd_end_date = data.get_param("enddate")

                    scenario_start, scenario_end = self.get_scenario_start_and_end_dates(scenario_uid)

                    # We checked cmd_start_date <= cmd_end_date in initializer reading cmd line args
                    if ((scenario_start <= cmd_start_date) and (cmd_end_date <= scenario_end)):
                        logging.info(
                            f"Using execution start/end dates as provided in cmd line: {cmd_start_date} to {cmd_end_date}")
                    else:
                        raise Exception(f"Cmd line start/end dates are not within scenario start/end dates")

                    # Check if the provided dates overlap with earlier runs
                    last_execution_end_date = self.get_last_execution_end_date(scenario_uid)
                    if last_execution_end_date is not None:
                        if data.get_param("startdate") < last_execution_end_date:
                            raise Exception(
                                f"Start date '{data.get_param('startdate')}' is before last execution end date '{last_execution_end_date}'")
                    else:
                        logging.warning(
                            f"Last execution end date is not found for scenario '{scenario_uid}' to validate")

        # Convert start date to string to set as run date
        data.set_param("rundate", data.get_param("startdate").strftime("%Y-%m-%d"))

        # Validation if start_date is greater than end_date
        if data.get_param("startdate") > data.get_param("enddate"):
            raise Exception(
                f"Start date '{data.get_param('startdate')}' is greater than end date '{data.get_param('enddate')}'")

    def execute(self):
        data = Data.get_instance()
        scenarioUid = data.get_param("scenario", "__BASE_SCENARIO__")
        self.read_referenceData()
        self.read_scenarioData(scenarioUid)
        self.validate_and_set_start_end_dates()
        self.read_product_group_mapping()

        if Constants.LEARNING_MODE or Constants.EXECUTION_MODE:
            if Constants.LEARNING_MODE:
                history_start_date = self.get_history_start_date()
                history_end_date = self.get_history_end_date()
            else:
                history_start_date, history_end_date = self.get_exec_history_start_end_date()
            print("Type of history_start_date in execute:" + str(type(history_start_date)))
            print("Type of history_end_date in execute:" + str(type(history_end_date)))
            #history_end_date = datetime.strptime(data.get_param("rundate"), "%Y-%m-%d").date()
            self.read_segment_history(history_start_date, history_end_date)
            # self.read_interactions_athena(history_start_date, history_end_date)
            self.read_interactions_rds(history_start_date, history_end_date)
            if Constants.EXECUTION_MODE:
                self.build_account_list_for_execution(history_start_date, history_end_date)
            '''
              For execution mode, we need to create days since last interaction from looking at 30 days in past from
              start date (i.e. run date in case of the one day run). Where as for learning mode, we need to look at
              30 days in past from the history start date which could be 6 months in past from the run date.

            '''
            if Constants.EXECUTION_MODE:
                one_month_earlier = history_end_date - relativedelta(months=1)
                self.read_dsl_channel(one_month_earlier, history_end_date)
            else:
                one_month_earlier = history_start_date - relativedelta(months=1)
                self.read_dsl_channel(one_month_earlier, history_start_date)

            self.read_state_model_output()
            self.read_reward_component_views()

            self.build_intermediate_data()

        # Need to read model info (incl list of channels) for touchpoints generation as well
        if Constants.EXECUTION_MODE or "touchpoints" in data.get_param("postProcMode", "none"):

            self.load_models_for_execution()

        if Constants.LEARNING_MODE:
            Initializer.initialize_model_struct()

    def read_state_model_output(self):
        data = Data.get_instance()
        params = data.get_param("connect_params")

        customer = params.get("env-customer")
        envName = params.get("env-envName")

        #Sample path: s3://aktana-bdp-devnovartisbr/prod/candidate-generator/state-model/predictions/latest.csv
        state_model_csv_path = f"s3://aktana-bdp-{customer}/{envName}/candidate-generator/state-model/predictions/latest.csv"

        latest_pdf = DataAccessLayer.read_latest_csv(state_model_csv_path)
        st_predictions_path = None
        cp_rem_predictions_path = None
        if 'st_predictions_path' in latest_pdf.columns:
            st_predictions_path = latest_pdf['st_predictions_path'].iloc[0]
            st_predictions_df = DataAccessLayer.read_s3_parquet(st_predictions_path)
            st_predictions_df.rename(columns={'st_prob': 'segmentClimbProb'}, inplace=True)
            DEFAULT_SEG_CLIMB_PROB = data.get_param("DEFAULT_SEG_PROP_VAL", 0.0001)
            # Clip the segment climb probability to a minimum value
            st_predictions_df['segmentClimbProb'] = st_predictions_df['segmentClimbProb'].clip(
                lower=DEFAULT_SEG_CLIMB_PROB)
            self.printSummary('st_predictions', st_predictions_df)
            data.set_dataframe("st_predictions", st_predictions_df)
            data.set_param("st_component", True)
            data.set_param("st_predictions_path", st_predictions_path)
        if 'cp_rem_predictions_path' in latest_pdf.columns:
            cp_rem_predictions_path = latest_pdf['cp_rem_predictions_path'].iloc[0]
            cp_rem_predictions_df = DataAccessLayer.read_s3_parquet(cp_rem_predictions_path)
            cp_rem_predictions_df.rename(columns={'rem_prob': 'remProb',
                                                  'email_prob': 'SEND_CHANNEL',
                                                  'f2f_prob': 'VISIT_CHANNEL',
                                                  'virtual_prob': 'WEB_INTERACTIVE_CHANNEL'}, inplace=True)
            cp_rem_predictions_df = pd.melt(cp_rem_predictions_df, id_vars=['accountId', 'remProb'],
                                            value_vars=['SEND_CHANNEL', 'VISIT_CHANNEL', 'WEB_INTERACTIVE_CHANNEL'],
                                            var_name='currentChannel', value_name='channelPropensity')
            prob_cols = ['channelPropensity', 'remProb']
            DEFAULT_CP_PROB = data.get_param("DEFAULT_CHANNEL_PROP_VAL", 0.0001)
            # Clip the channel propensity probability to a minimum value
            cp_rem_predictions_df[prob_cols] = cp_rem_predictions_df[prob_cols].clip(lower=DEFAULT_CP_PROB)
            self.printSummary('cp_predictions', cp_rem_predictions_df)
            data.set_dataframe("cp_predictions", cp_rem_predictions_df)
            data.set_param("cp_rem_component", True)
            data.set_param("cp_rem_predictions_path", cp_rem_predictions_path)

    def read_reward_component_views(self):
        data = Data.get_instance()
        reward_components_pdf = data.get_dataframe("reward_components")
        if reward_components_pdf is None or reward_components_pdf.empty:
            logging.warning("No reward components found.")
            return

        # iterate through rows of reward_components_pdf
        for index, row in reward_components_pdf.iterrows():
            reward_component_id = row['rewardComponentId']
            view_name = row['viewName']
            min_value = row['minValue']
            max_value = row['maxValue']
            value_col = row['valueColumn']
            weight = row['weight']
            reward_component_name = row['rewardComponentName']
            reward_adjustment_days = row['adjustmentDays']
            reward_adjustment_months = row['adjustmentMonths']
            # convert dimensions to list
            dimensions = row['dimensions']  # ['accountId', 'productId', 'yearMonth']
            dimensions = eval(dimensions)
            view_pdf = DataAccessLayer.runMySqlQuery(
                f"select * from {view_name} where rewardComponentName='{reward_component_name}'", Constants.learning_db)

            print(f"Read Reward Component {reward_component_name} using View: {view_name}, with shape {view_pdf.shape}")

            # Rename the value column to make it unique across reward components
            updated_value_col_name = f"{reward_component_name}_{reward_component_id}_{value_col}"
            updated_view_name = f"{view_name}_{reward_component_name}_{reward_component_id}"
            view_pdf = view_pdf.rename(columns={value_col: updated_value_col_name})
            value_col = updated_value_col_name

            # Get eligible accounts for the reward component
            eligible_accounts_summary = data.get_dataframe("eligible_accounts_summary")
            view_pdf = pd.merge(view_pdf, eligible_accounts_summary[['accountId']].drop_duplicates(), on='accountId', how='inner')

            # Update value column name in reward_components_pdf
            reward_components_pdf.loc[index, 'valueColumn'] = updated_value_col_name

            # Replace values >97th percentile with 97th percentile value
            max_raw_value = view_pdf[value_col].quantile(0.97)
            view_pdf[value_col] = view_pdf[value_col].clip(upper=max_raw_value)

            # Scale the value column in minvalue and maxvalue range
            if min_value != 0 or max_value != 0:  # If both 0 then no scaling required
                min_max_scalar = MinMaxScaler(feature_range=(min_value, max_value))
                view_pdf[value_col] = min_max_scalar.fit_transform(view_pdf[[value_col]])

            # if dimensions contains 'yearMonth' then use it to adjust the date
            if 'yearMonth' in dimensions:
                # Convert 'yearMonth' column from string to date
                view_pdf['yearMonth'] = view_pdf['yearMonth'].apply(lambda x: datetime.strptime(str(x), '%Y-%m-%d'))
                view_pdf['yearMonth'] = view_pdf['yearMonth'].apply(
                    lambda x: x - relativedelta(months=reward_adjustment_months, days=reward_adjustment_days))
                view_pdf['yearMonth'] = view_pdf['yearMonth'].apply(lambda x: x.strftime('%Y-%m'))
            elif 'activityDate' in dimensions:
                # Convert 'activityDate' column from string to date
                view_pdf['activityDate'] = view_pdf['activityDate'].apply(
                    lambda x: datetime.strptime(str(x), '%Y-%m-%d'))
                view_pdf['activityDate'] = view_pdf['activityDate'].apply(
                    lambda x: x - relativedelta(months=reward_adjustment_months, days=reward_adjustment_days))

            # product_group_mapping_df = data.get_dataframe("product_group_mapping")
            # view_pdf = pd.merge(view_pdf, product_group_mapping_df, on='productId', how='left')
            if 'productId' in view_pdf.columns:
                view_pdf['parentProductId'] = view_pdf['productId']

            # Compute reward components for entire period of learning/execution
            # If data is missing, use the most recent data till end-date
            start_date = data.get_param("history_start_date")
            end_date = data.get_param("history_end_date")

            if Constants.EXECUTION_MODE:
                start_date = data.get_param("startdate")
                end_date = data.get_param("enddate")

            timeDimName = ""
            if 'yearMonth' in dimensions:
                delta = relativedelta(months=1)
                current_date = pd.Timestamp(start_date.replace(day=1))
                timeDimName = 'yearMonth'
                daySuffix = "-01"
            elif 'activityDate' in dimensions:
                delta = relativedelta(days=1)
                current_date = start_date
                timeDimName = 'activityDate'
                daySuffix = ""

            print("    Reward data summary:")
            if timeDimName:
                print(view_pdf.groupby([timeDimName])[updated_value_col_name].describe())
            else:
                print(view_pdf[updated_value_col_name].describe())

            if timeDimName:
                maxRewardDate = view_pdf[timeDimName].max()
                latest_view_pdf = view_pdf.loc[view_pdf[timeDimName] == maxRewardDate]
                maxRewardDate = pd.Timestamp(datetime.strptime(maxRewardDate + daySuffix, '%Y-%m-%d').date())

                while (current_date <= pd.Timestamp(end_date)):
                    print(f"    Checking reward data for {current_date}...")
                    if current_date > maxRewardDate:
                        print("    Start/End period is after maxRewardDate:" + str(current_date) + " vs. " + str(
                            maxRewardDate) + "... extending reward data till end-date")
                        if 'yearMonth' in dimensions:
                            latest_view_pdf.loc[:][timeDimName] = current_date.strftime("%Y-%m")
                        else:
                            latest_view_pdf.loc[:][timeDimName] = datetime.strptime(str(current_date), '%Y-%m-%d')
                        view_pdf = pd.concat([view_pdf, latest_view_pdf], ignore_index=True)
                    current_date += delta

            # Multiply the value column with weight
            view_pdf[value_col] = view_pdf[value_col] * weight

            if timeDimName:
                maxRewardDate = view_pdf[timeDimName].max()
                # latest_view_pdf = view_pdf.loc[view_pdf[timeDimName] == maxRewardDate]
                print("    Max Reward date in extended data:" + str(maxRewardDate))

            data.set_dataframe(updated_view_name, view_pdf)
