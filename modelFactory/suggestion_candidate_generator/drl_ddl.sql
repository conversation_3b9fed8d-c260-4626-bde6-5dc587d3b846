use novartisbrprod_learning;

DROP TABLE `DRL_Scenario`;

CREATE TABLE `DRL_Scenario` (
  `scenarioId` int(11) NOT NULL AUTO_INCREMENT,
  `scenarioUid` VARCHAR(50) NOT NULL,
  `scenarioName` VARCHAR(100) NOT NULL,
  `scenarioDescription` VARCHAR(100) NULL,
  `isDeleted` tinyint(4) NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`scenarioId`),
  UNIQUE KEY `scenario_uniqueAK` (`scenarioUid`)
) ;

insert into DRL_Scenario (scenarioUid, scenarioName, scenarioDescription) VALUES ('__DEFAULT__', 'Default Scenario', 'Default <PERSON>enario to copy others from' );
insert into DRL_Scenario (scenarioUid, scenarioName, scenarioDescription) VALUES ('__BASE_SCENARIO__', 'Base Scenario', 'Base Scenario');

commit;

DROP TABLE `DRL_Config_Params`;

CREATE TABLE `DRL_Config_Params` (
  `configId` int(11) NOT NULL AUTO_INCREMENT,
  `versionId` VARCHAR(50) NOT NULL,
  `configName` VARCHAR(50) NOT NULL,
  `configDataType` VARCHAR(20) NOT NULL,
  `configValue` VARCHAR(200) NULL,
  `configDisplayName` VARCHAR(100) NOT NULL,
  `isVisibleInUi` boolean DEFAULT True,
  `configGroup` VARCHAR(50) NOT NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`configId`),
  UNIQUE KEY `simulationRate_uniqueAK` (`configName`, `versionId`)
) ;

insert into DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'SIMULATION_HISTORY_MONTH_OFFSET', 'integer', '1', 'Months to skip in history to find data for simulation from current date', True, 'Learning');
insert into DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__BASE_SCENARIO__', 'SIMULATION_HISTORY_MONTH_OFFSET', 'integer', '1', 'Months to skip in history to find data for simulation from current date', True, 'Learning');
insert into DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__BASE_SCENARIO__', 'ELIGIBLE_TRANSACTION_MIN_LIMIT', 'integer', '2', 'Min. number of transactions to be eligible for simulation', True, 'Learning');
insert into DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__BASE_SCENARIO__', 'ELIGIBLE_TRANSACTION_MONTHS', 'integer', '6', 'Number of months to check for eligible transactions', True, 'Learning');
insert into DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__BASE_SCENARIO__', 'ELIGIBLE_TRANSACTION_IS_BY_PRODUCT', 'integer', '1', 'Check eligible transaction volume by account or account/product', True, 'Learning');

-- Model Configurations
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'optimizer', 'string', 'adagrad', 'Learning Optimizer', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'loss', 'string', 'mse', 'Loss Function For Training', False, 'Learning');
--INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'metrics', 'string', 'value', 'Metrics Configuration', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'drop_out_rate', 'float', '0.1', 'Dropout Rate', False, 'Learning');

-- Learning Configurations
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'LEARNING_EPOCH', 'integer', '25', 'Learning Epochs', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'LEARNING_BATCH_SIZE', 'integer', '1000', 'Learning Batch Size', False, 'Learning');

-- Early Stopping Configurations
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'EARLY_STOPPING_PATIENCE', 'integer', '5', 'Early Stopping Patience', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'EARLY_STOPPING_START_EPOCH', 'integer', '5', 'Early Stopping Start Epoch', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'ITERATION_IMPROVEMENT_FACTOR', 'float', '5', 'Min Iteration Improvement Factor', False, 'Learning');

-- Simulation Configurations
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'SIM_ENABLE_RANDOMNESS', 'boolean', 'False', 'Enable Simulation Randomness', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'sim_lower_random_bound', 'float', '-0.1', 'Simulation Lower Random Bound', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'sim_upper_random_bound', 'float', '0.1', 'Simulation Upper Random Bound', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'SIM_ENABLE_SUPPRESSION', 'boolean', 'False', 'Enable Simulation Suppression', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'SIM_ENABLE_BOOSTING', 'boolean', 'False', 'Enable Simulation Boosting', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'SIM_ENABLE_ACCEPT_RATE_CONTROL', 'boolean', 'True', 'Enable Accept Rate Control', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'SIM_ACCEPT_RATE_TARGET', 'float', '0.7', 'Accept Rate Target', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'sim_acceptance_rate_random_min', 'float', '-0.05', 'Acceptance Rate Random Min', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'sim_acceptance_rate_random_max', 'float', '0.02', 'Acceptance Rate Random Max', False, 'Learning');

-- Reward Configurations
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES ('__DEFAULT__', 'NO_ACTION_REWARD', 'float', '-1', 'No Action Reward', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES('__DEFAULT__', 'DEFAULT_CHANNEL_PROP_VAL', 'float', '0.0001', 'Default Channel Propensity Value', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES('__DEFAULT__', 'DEFAULT_SEG_PROP_VAL', 'float', '0.0001', 'Default Segment Propensity Value', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES('__DEFAULT__', 'ENABLE_CJ_REWARD', 'boolean', 'True', 'Enable CJ Reward', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES('__DEFAULT__', 'CHANNEL_COOL_OFF_VIOL_PENALTY', 'float', '7', 'Channel Cool Off Violation Penalty', False, 'Learning');
INSERT INTO DRL_Config_Params (versionId, configName, configDataType, configValue, configDisplayName, isVisibleInUi, configGroup) VALUES('__DEFAULT__', 'CONTENT_COOL_OFF_VIOL_PENALTY', 'float', '7', 'Content Cool Off Violation Penalty', False, 'Learning');


commit;

DROP TABLE `DRL_Monthly_Simulation_Rate`;

CREATE TABLE `DRL_Monthly_Simulation_Rate` (
  `simulationRateId` int(11) NOT NULL AUTO_INCREMENT,
  `month_offset_from_last` int(11) NOT NULL,
  `simulation_percent` double NOT NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`simulationRateId`),
  UNIQUE KEY `simulationRate_uniqueAK` (`month_offset_from_last`)
) ;

insert into DRL_Monthly_Simulation_Rate (month_offset_from_last, simulation_percent) VALUES (0, 0);
insert into DRL_Monthly_Simulation_Rate (month_offset_from_last, simulation_percent) VALUES (1, 0.2);
insert into DRL_Monthly_Simulation_Rate (month_offset_from_last, simulation_percent) VALUES (2, 0.4);
insert into DRL_Monthly_Simulation_Rate (month_offset_from_last, simulation_percent) VALUES (3, 0.6);
insert into DRL_Monthly_Simulation_Rate (month_offset_from_last, simulation_percent) VALUES (4, 0.8);
insert into DRL_Monthly_Simulation_Rate (month_offset_from_last, simulation_percent) VALUES (5, 1.0);

commit;

DROP TABLE `DRL_Channel_CoolOffDays`;

CREATE TABLE `DRL_Channel_CoolOffDays` (
  `channelCoolOffDaysId` int(11) NOT NULL AUTO_INCREMENT,
  `currentChannel` VARCHAR(50) NOT NULL,
  `nextChannel` VARCHAR(50) NOT NULL,
  `segment` VARCHAR(50) NULL,
  `brand` VARCHAR(50) NULL,
  `days` int(11) NOT NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`channelCoolOffDaysId`),
  UNIQUE KEY `channelCoolOffDays_uniqueAK` (`currentChannel`,`nextChannel`, `segment`, `brand`),
  KEY `channelCoolOffDays_segment` (`segment`),
  KEY `channelCoolOffDays_brand` (`brand`),
  KEY `channelCoolOffDays_nextChannel` (`nextChannel`)
) ;

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'EMAIL_CHANNEL', 'ST', null, 10 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'VISIT_CHANNEL', 'ST', null, 10 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'ST', null, 10 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'EMAIL_CHANNEL', 'ST', null, 10 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'VISIT_CHANNEL', 'ST', null, 10 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'ST', null, 10 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'EMAIL_CHANNEL', 'ST', null, 10 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'VISIT_CHANNEL', 'ST', null, 10 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'ST', null, 10 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'EMAIL_CHANNEL', 'T1', null, 15 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'VISIT_CHANNEL', 'T1', null, 15 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'T1', null, 15 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'EMAIL_CHANNEL', 'T1', null, 15 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'VISIT_CHANNEL', 'T1', null, 15 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'T1', null, 15 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'EMAIL_CHANNEL', 'T1', null, 15 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'VISIT_CHANNEL', 'T1', null, 15 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'T1', null, 15 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'EMAIL_CHANNEL', 'T2', null, 20 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'VISIT_CHANNEL', 'T2', null, 20 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'T2', null, 20 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'EMAIL_CHANNEL', 'T2', null, 20 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'VISIT_CHANNEL', 'T2', null, 20 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'T2', null, 20 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'EMAIL_CHANNEL', 'T2', null, 20 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'VISIT_CHANNEL', 'T2', null, 20 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'T2', null, 20 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'EMAIL_CHANNEL', 'T3', null, 30 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'VISIT_CHANNEL', 'T3', null, 30 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'T3', null, 30 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'EMAIL_CHANNEL', 'T3', null, 30 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'VISIT_CHANNEL', 'T3', null, 30 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'T3', null, 30 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'EMAIL_CHANNEL', 'T3', null, 30 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'VISIT_CHANNEL', 'T3', null, 30 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'T3', null, 30 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'EMAIL_CHANNEL', 'notier', null, 60 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'VISIT_CHANNEL', 'notier', null, 60 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('EMAIL_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'notier', null, 60 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'EMAIL_CHANNEL', 'notier', null, 60 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'VISIT_CHANNEL', 'notier', null, 60 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('VISIT_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'notier', null, 60 );

insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'EMAIL_CHANNEL', 'notier', null, 60 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'VISIT_CHANNEL', 'notier', null, 60 );
insert into DRL_Channel_CoolOffDays (currentChannel, nextChannel, segment, brand, days) VALUES ('WEB_INTERACTIVE_CHANNEL', 'WEB_INTERACTIVE_CHANNEL', 'notier', null, 60 );

update DRL_Channel_CoolOffDays set currentChannel = 'SEND_CHANNEL' where currentChannel = 'EMAIL_CHANNEL';
update DRL_Channel_CoolOffDays set nextChannel = 'SEND_CHANNEL' where nextChannel = 'EMAIL_CHANNEL';

commit;

DROP TABLE `DRL_ContentTag`;


CREATE TABLE `DRL_ContentTag` (
  `contentTagId` int(11) NOT NULL AUTO_INCREMENT,
  `contentChannel` VARCHAR(50) NOT NULL,
  `contentTagName` VARCHAR(50) NOT NULL,
  `contentTagDescription` VARCHAR(200) NOT NULL,
  `contentWeight` double DEFAULT 1.0,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`contentTagId`),
  UNIQUE KEY `contentTags_uniqueAK` (`contentTagName`,`contentChannel`),
  KEY `contentTags_channel` (`contentChannel`)
) ;

insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('SEND_CHANNEL', 'NA', 'NA', 1.0);
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('VISIT_CHANNEL', 'NA', 'NA', 1.0);
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('WEB_INTERACTIVE_CHANNEL', 'NA', 'NA', 1.0);
/*
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('SEND_CHANNEL', 'A', 'A', 1.0);
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('SEND_CHANNEL', 'B', 'B', 1.0);
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('SEND_CHANNEL', 'C', 'C', 1.0);
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('VISIT_CHANNEL', 'A', 'A', 1.0);
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('VISIT_CHANNEL', 'B', 'B', 1.0);
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('VISIT_CHANNEL', 'C', 'C', 1.0);
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('WEB_INTERACTIVE_CHANNEL', 'A', 'A', 1.0);
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('WEB_INTERACTIVE_CHANNEL', 'B', 'B', 1.0);
insert into DRL_ContentTag (contentChannel, contentTagName, contentTagDescription, contentWeight) VALUES ('WEB_INTERACTIVE_CHANNEL', 'C', 'C', 1.0);
*/

commit;

DROP TABLE `DRL_Content_CoolOffDays`;

CREATE TABLE `DRL_Content_CoolOffDays` (
  `contentCoolOffDaysId` int(11) NOT NULL AUTO_INCREMENT,
  `currentContentName` VARCHAR(50) NOT NULL,
  `nextContentName` VARCHAR(50) NOT NULL,
  `segment` VARCHAR(50) NULL,
  `brand` VARCHAR(50) NULL,
  `days` int(11) NOT NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`contentCoolOffDaysId`),
  UNIQUE KEY `contentCoolOffDays_uniqueAK` (`currentContentName`, `nextContentName`, `segment`, `brand`),
  KEY `contentCoolOffDays_segment` (`segment`),
  KEY `contentCoolOffDays_brand` (`brand`),
  KEY `contentCoolOffDays_nextContentName` (`nextContentName`)
);

insert into DRL_Content_CoolOffDays (currentContentName, nextContentName, segment, brand, days) VALUES ('NA', 'NA', 'notier', null, 1 );

commit;

DROP TABLE `DRL_Content_SequencePriority`;

CREATE TABLE `DRL_Content_SequencePriority` (
  `contentSequenceId` int(11) NOT NULL AUTO_INCREMENT,
  `currentChannel` VARCHAR(50) NOT NULL,
  `currentContentName` VARCHAR(50) NOT NULL,
  `nextChannel` VARCHAR(50) NOT NULL,
  `nextContentName` VARCHAR(50) NOT NULL,
  `priority` double DEFAULT 1.0,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`contentSequenceId`),
  UNIQUE KEY `contentSequence_uniqueAK` (`currentContentName`, `currentChannel`, `nextContentName`, `nextChannel`),
  KEY `contentSequence_currentChannel` (`currentChannel`),
  KEY `contentSequence_nextChannel` (`nextChannel`),
  KEY `contentSequence_nextContentName` (`nextContentName`)
);

insert into DRL_Content_SequencePriority (currentChannel, currentContentName, nextChannel, nextContentName, priority) VALUES ('VISIT_CHANNEL', 'NA', 'EMAIL_CHANNEL', 'NA', 1.0 );

commit;

DROP VIEW `Account_Segment_Order_v`;

CREATE or REPLACE VIEW novartisbrprod_stage.`Account_Segment_Order_v` as (
select 'ST' tier, 5 tier_rank
UNION
select 'T1' tier, 4 tier_rank
UNION
select 'T2' tier, 3 tier_rank
UNION
select 'T3' tier, 2 tier_rank
UNION
select 'notier' tier, 1 tier_rank
);



create or replace view DRL_Interaction_History_v as (
select ia.accountId , a.externalId accountUid , ip.productId, p.productName, p.externalId productUid , r.repId, r.externalId repUid, acm.channelName currentChannel, date(startDateTime) activityDate, i.interactionId, ip.messageId 
from novartisbrprod.Interaction i
inner join novartisbrprod.InteractionAccount ia on i.interactionId = ia.interactionId 
inner join novartisbrprod.InteractionProduct ip on i.interactionId = ip.interactionId
inner join novartisbrprod.Rep r on i.repId = r.repId
-- inner join novartisbrprod.RepAccountAssignment raa on raa.accountId = ia.accountId 
inner join novartisbrprod.ActionChannelMap acm on acm.actionTypeId = i.repActionTypeId and acm.actorTypeId = r.repTypeId 
inner join novartisbrprod.Account a on ia.accountId = a.accountId 
inner join novartisbrprod.Product p on ip.productId = p.productId 
where i.isCompleted = 1
);

select accountId , productId, lastChannel, max(interactionId) lastInteractionId , max(activityDate) lastActivityDate 
from DRL_Interaction_History_v
where activityDate < ''
group by accountId , productId, lastChannel

create or replace view Last_Activity_by_Channel_v as (
select ia.accountId , ip.productId, acm.channelName lastChannel, max(i.interactionId) lastInteractionId , max(startDateTime) lastActivityDate 
from Interaction i
inner join InteractionAccount ia on i.interactionId = ia.interactionId 
inner join InteractionProduct ip on i.interactionId = ip.interactionId
inner join Rep r on i.repId = r.repId
inner join RepAccountAssignment raa on raa.accountId = ia.accountId 
inner join ActionChannelMap acm on acm.actionTypeId = i.repActionTypeId and acm.actorTypeId = r.repTypeId 
where i.isCompleted = 1
group by ia.accountId, ip.productId, acm.channelName
)
;


create or replace view Last_Activity_v as (
select ia.accountId , ip.productId, max(i.interactionId) lastInteractionId , max(startDateTime) lastActivityDate from Interaction i
inner join InteractionAccount ia on i.interactionId = ia.interactionId 
inner join InteractionProduct ip on i.interactionId = ip.interactionId
inner join Rep r on i.repId = r.repId
inner join RepAccountAssignment raa on raa.accountId = ia.accountId 
inner join ActionChannelMap acm on acm.actionTypeId = i.repActionTypeId and acm.actorTypeId = r.repTypeId 
where i.isCompleted = 1
group by ia.accountId, ip.productId
)
;

create or replace view  Last_Activity_Channel_v as (
select ia.accountId , ip.productId, max(acm.channelName) lastChannel, max(la.lastActivityDate) lastActivityDate, 
from Interaction i
inner join InteractionAccount ia on i.interactionId = ia.interactionId 
inner join InteractionProduct ip on i.interactionId = ip.interactionId
inner join Last_Activity_v la on ia.accountId = la.accountId and ip.productId = la.productId and i.lastInteractionId = la.lastInteractionId and i.startDateTime = la.lastActivityDate
inner join Rep r on i.repId = r.repId
inner join RepAccountAssignment raa on raa.accountId = ia.accountId 
inner join ActionChannelMap acm on acm.actionTypeId = i.repActionTypeId and acm.actorTypeId = r.repTypeId 
where i.isCompleted = 1
group by ia.accountId , ip.productId
);

create or replace view Possible_moves_v as (
select lastActivity.accountId, lastActivity.productId, c.channelName nextChannel, lastActivity.lastChannel, lastActivity.lastActivityDate, COALESCE (aasmv.hcpSegmentName, 'notier') hcpSegmentName
from Last_Activity_Channel_v lastActivity
join Channel c
left join novartisbrprod_stage.AKT_Account_Segment_Mapping_V aasmv on aasmv.accountId = lastActivity.accountId
)
;

CREATE TABLE `Content_Target_Plan` (
  `contentTargetPlanId` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(11) NULL,
  `hcpSegment` VARCHAR(50) NULL,
  `productId` int(11) NOT NULL,
  `contentTopicName` VARCHAR(50) NOT NULL,
  `contentTopicOrder` int(11) NULL,
  `channel` VARCHAR(50) NOT NULL,
  `cmsDocumentId` VARCHAR(50) NOT NULL,
  `cmsDocumentOrder` int(11) NULL,
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`contentTargetPlanId`),
  KEY `contentTargetPlan_accountContentId` (`accountId`, `productId`, `contentTopicName`, `channel`),
  KEY `contentTargetPlan_segmentContentId` (`hcpSegment`, `productId`, `contentTopicName`, `channel`)
) ;

INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T1",1,"A","VISIT_CHANNEL",1,1)
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T1",1,"A","VISIT_CHANNEL",2,2);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T1",1,"A","VISIT_CHANNEL",3,3);

INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T1",1,"A","SEND_CHANNEL",4,1);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T1",1,"A","SEND_CHANNEL",5,2);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T1",1,"A","SEND_CHANNEL",6,3);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T1",1,"A","SEND_CHANNEL",7,4);

INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T1",1,"B","VISIT_CHANNEL",2,1);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T1",1,"B","VISIT_CHANNEL",8,2);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T1",1,"B","VISIT_CHANNEL",9,3);

INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T2",1,"A","VISIT_CHANNEL",1,1);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T2",1,"A","VISIT_CHANNEL",2,2);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T2",1,"A","VISIT_CHANNEL",10,3);

INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T2",1,"A","SEND_CHANNEL",4,1);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T2",1,"A","SEND_CHANNEL",6,2);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T2",1,"A","SEND_CHANNEL",7,3);

INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T2",1,"B","VISIT_CHANNEL",2,1);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T2",1,"B","VISIT_CHANNEL",8,2);
INSERT INTO Content_Target_Plan (hcpSegment, productId, contentTopicName, channel, cmsDocumentId, cmsDocumentOrder) VALUES ("T2",1,"B","VISIT_CHANNEL",9,3);


/*

select * from CoolOffDays;

set @RUN_DATE = '2022-09-02'

set @ACTIVITY_CUTOFF_DAYS = 365

select * from novartisbrprod_stage.DRL_accounts 
where lastActivityDate > date_add(@RUN_DATE, INTERVAL -@ACTIVITY_CUTOFF_DAYS day) ;

select * from novartisbrprod_stage.AKT_Account_Segment_Mapping_V ;

select * from novartisbrprod.AccountProduct; 

select max(lastActivityDate) from novartisbrprod_stage.DRL_accounts ;


select * from novartisbrprod_stage.DRL_accounts 
where lastActivityDate > RUN_DATE - ;

drop table novartisbrprod_stage.Last_Activity_v ;
drop table  novartisbrprod_stage.Last_Activity_Channel_v ;

select * from Interaction_History_v 
where activityDate > '2022-06-01' limit 10;


select accountId, productId, a.nextChannel, lastChannel, lastActivityDate, datediff(@RUN_DATE, a.lastActivityDate) days_since_last_activity, hcpSegmentName, coalesce(max(c1.days), max(c2.days), max(c3.days)) coolOffDays
from Possible_moves_v a
left join CoolOffDays c1 on lastChannel = c1.currentChannel and c1.nextChannel = a.nextChannel and a.hcpSegmentName = c1.segment and a.productId = c1.brand
left join CoolOffDays c2 on lastChannel = c2.currentChannel and c2.nextChannel = a.nextChannel and a.hcpSegmentName = c2.segment
left join CoolOffDays c3 on lastChannel = c3.currentChannel and c3.nextChannel = a.nextChannel
where lastActivityDate > date_add(@RUN_DATE, INTERVAL -@ACTIVITY_CUTOFF_DAYS day) 
group by accountId, productId, a.nextChannel, lastChannel, lastActivityDate, hcpSegmentName
having datediff(@RUN_DATE, a.lastActivityDate) > coalesce(max(c1.days), max(c2.days), max(c3.days)) 
;

select * from Channel;

select * from CoolOffDays;

select nextActivity.accountId, nextActivity.productId, nextActivity.nextChannel, previousActivity.lastChannel, previousActivity.lastActivityDate from 
(select accountId, productId, c.channelName nextChannel
from novartisbrprod_stage.Last_Activity_Channel_v lastActivity
join Channel c
where lastActivityDate > date_add(@RUN_DATE, INTERVAL -@ACTIVITY_CUTOFF_DAYS day) 
group by accountId, productId, c.channelName) nextActivity
left join 
(select accountId, productId, lastActivity.lastChannel, lastActivityDate, date_add(@RUN_DATE, INTERVAL -@ACTIVITY_CUTOFF_DAYS day) activity_cutoff_date
from novartisbrprod_stage.Last_Activity_Channel_v lastActivity
where lastActivityDate > date_add(@RUN_DATE, INTERVAL -@ACTIVITY_CUTOFF_DAYS day) previousActivity
on nextActivity.accountId = previousActivity.accountId and nextActivity.productId = previousActivity.productId

*/
