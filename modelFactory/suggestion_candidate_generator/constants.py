from abstract_model_factory.abstract_constants import AbstractConstants


class Constants(AbstractConstants):
    ADL_S3_PATH = None
    DCO_S3_PATH = None
    LOCAL_MODE = True

    SPARK_APP_NAME = "candidate_generator"

    SNOWFLAKE_SOURCE_NAME = "net.snowflake.spark.snowflake"
    sfOptions_spark_snowFlake = None

    sw_account = 'aktana.ap-northeast-1.aws'
    sw_user = 'novartisau_ml_admin'
    sw_password = ''
    sw_database = 'NOVARTISAU_DW_DEV'
    sw_schema = 'DCO'
    sw_warehouse = 'DEMO_WH'
    sw_role = 'NOVARTISAU_NONPROD_APPADMIN_ROLE'

    TARGET_S3_PATH = ""

    ## Credentials for Database [MySql RDS]
    # ssh -i <filename>.pem -N -L 33068:dconovartisaurds.aktana.com:3306 <username>@bdpeu002.aktana.com
    rds_hostname = 'localhost' #'novartisaurds.aktana.com'  # sql hostname for the RDS sever
    rds_port = 3306  # 33068  # default sql port number.
    # username to access the database on the RDS server.
    rds_username = 'appadmin'
    # database password to access the database on the RDS server
    rds_password = ''
    ssh_host = 'bastion-us.aktana.com'  # SSH host to the RDS Server
    ssh_port = 22  # default ssh port
    ssh_user = 'sankar.dhanushkodi'  # username to the ssh
    ssh_private_key = ''  # path to your pem file
    rds_db = ''  # dbname is optional.
    
    engine_db = ''
    stage_db = ''
    learning_db = ''

    ADL_S3_PATH = ""
    DCO_S3_PATH = ""

    EXECUTION_MODE = False
    LEARNING_MODE = False
    INCREMENTAL_RUN = False
    MULTI_DAY_EXECUTION = False


    @classmethod
    def get_snowflake_conn_params(cls):
        global sfOptions_spark_snowFlake

        if sfOptions_spark_snowFlake is None:
            sfOptions_spark_snowFlake = {
                "sfURL": cls.sw_account + ".snowflakecomputing.com",
                "sfUser": cls.sw_user,
                "sfPassword": cls.sw_password,
                "sfDatabase": cls.sw_database,
                "sfSchema": cls.sw_schema,
                "sfWarehouse": cls.sw_warehouse,
                "sfRole": cls.sw_role,
                "parallelism": "64",
            }
            print("Global sfOptions_spark_snowFlake has been initialized")
        return sfOptions_spark_snowFlake