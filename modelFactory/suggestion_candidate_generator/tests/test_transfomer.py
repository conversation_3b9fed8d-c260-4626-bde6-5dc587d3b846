import unittest

import pandas as pd

from suggestion_candidate_generator.transformer import Transformer


class MyTestCase(unittest.TestCase):
    def test_state_variable_creation(self):
        input_pdf = pd.read_csv("/tmp/input_pdf_drl.csv")
        channel_cool_off_pdf = pd.read_csv("/tmp/channel_cooloff.csv")
        content_cool_off_pdf = pd.read_csv("/tmp/content_cooloff.csv")

        # Using channel cool-off matrix figure out which channels are allowed
        transformer = Transformer('Transformer')
        state_pdf = transformer.create_network_input_variables(input_pdf, channel_cool_off_pdf, content_cool_off_pdf)

        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', 200)

        print(state_pdf)
        print(state_pdf.columns)

if __name__ == '__main__':
    unittest.main()
