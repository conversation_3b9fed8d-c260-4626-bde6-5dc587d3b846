import unittest
from suggestion_candidate_generator.learner import <PERSON><PERSON>
from suggestion_candidate_generator.initializer import Initializer
from suggestion_candidate_generator.data import Data
import pandas as pd
import numpy as np


class ModelLearningTest(unittest.TestCase):

    @classmethod
    def setUpClass(cls) -> None:
        initializer = Initializer("Initializer")
        model = initializer.initialize_model_struct()
        data = Data.get_instance()
        data.set_model('reward_predictor', model)

    def test_model_training(self):
        data = Data.get_instance()

        # Create a 100 rows and 50 col input dataframe with random values
        data = Data.get_instance()
        input_dim = data.get_param('input_dim', 50)
        output_dim = data.get_param('output_dim', 1)
        X = pd.DataFrame(np.random.rand(100, input_dim))
        y = pd.DataFrame(np.random.rand(100, output_dim))
        learner = Learner("Learner")

        model = learner.train_model(X, y)



if __name__ == '__main__':
    unittest.main()
