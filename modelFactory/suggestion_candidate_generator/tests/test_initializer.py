import unittest

from suggestion_candidate_generator.data import Data
from suggestion_candidate_generator.initializer import Initializer
from suggestion_candidate_generator.main import *

class ModelStrutureTest(unittest.TestCase):

    @classmethod
    def setUpClass(cls) -> None:
        initializer = Initializer("Initializer")
        model = initializer.initialize_model_struct()
        data = Data.get_instance()
        data.set_model('reward_predictor', model)

    def test_model_structure(self):
        initializer = Initializer("Initializer")
        model = initializer.initialize_model_struct()
        print(model.summary())

        # Check if models are equal
        data = Data.get_instance()
        self.assertEqual(model, data.get_model('reward_predictor'))


    def test_model_input_output_layers(self):

        data = Data.get_instance()
        model = data.get_model('reward_predictor')

        assert data.get_param('input_dim', 50) == model.layers[0].input_shape[1]
        assert data.get_param('output_dim', 1) == model.layers[-1].output_shape[1]

if __name__ == '__main__':
    unittest.main()
