import unittest

import pandas as pd

from suggestion_candidate_generator.data import Data
from suggestion_candidate_generator.reward_calculator import RewardCalculator


class RewardCalculatorBase(unittest.TestCase):

    def test_calculate_reward(self):
        # Create Data object
        data = Data.get_instance()
        input_pdf = pd.read_csv("/tmp/drl/reward_calc_input.csv")
        data.set_dataframe('input_pdf', input_pdf)

        channel_impact_pdf = pd.read_csv("/tmp/drl/channel_impact.csv")
        data.set_dataframe('channel_impact', channel_impact_pdf)

        #Channel cost
        action_cost_pdf = pd.read_csv("/tmp/drl/action_cost.csv")
        data.set_dataframe('action_cost', action_cost_pdf)

        input_pdf = data.get_dataframe('input_pdf')
        reward_calculator = RewardCalculator()
        reward_pdf = reward_calculator.calculate_reward(input_pdf)
        print(reward_pdf['reward'].describe())


if __name__ == '__main__':
    unittest.main()
