import unittest

import pandas as pd

from suggestion_candidate_generator.constants import Constants
from suggestion_candidate_generator.data import Data
from suggestion_candidate_generator.deployer import Deployer
from suggestion_candidate_generator.initializer import Initializer


class TestWriteModel(unittest.TestCase):
    def setUp(self) -> None:
        data = Data.get_instance()

        initializer = Initializer('Initialize')
        model = initializer.initialize_model_struct()
        data.set_model("reward_predictor", model)


        dummy_prediction_pdf = pd.DataFrame(
            {'accountId': [1, 2, 3], 'drl_suggestion_candidate_generator_prob': [0.1, 0.2, 0.3]})
        data.set_dataframe("predictions_pdf", dummy_prediction_pdf)

        Constants.TARGET_S3_PATH = "s3a://aktana-bdp-devnovartisbr/prod/drl/reward_predictor/"

        Constants.AWS_ACCESS_KEY_ID = ""
        Constants.AWS_SECRET_ACCESS_KEY = ""
        Constants.AWS_SESSION_TOKEN = ""

    def test_write_model(self):
        deployer = Deployer('Deploy')
        deployer.save_model()

    def test_write_predictions(self):
        deployer = Deployer('Deploy')
        deployer.write_predictions()



if __name__ == '__main__':
    unittest.main()
