import unittest

import numpy as np

from suggestion_candidate_generator.data import Data
from suggestion_candidate_generator.initializer import Initializer
from suggestion_candidate_generator.predictor import Predictor
import pandas as pd


class ModelPredictionTest(unittest.TestCase):
    @classmethod
    def setUpClass(cls) -> None:
        initializer = Initializer("Initializer")
        model = initializer.initialize_model_struct()
        data = Data.get_instance()
        data.set_model('reward_predictor', model)

    def test_predict(self):
        predictor = Predictor("Predictor")

        # Create pandas dataframe with 25 rows and 50 columns with random values
        data = Data.get_instance()
        input_dim = data.get_param('input_dim', 50)
        X = pd.DataFrame(np.random.rand(25, input_dim))

        prediction_result = predictor.predict(input_data=X)
        self.assertEqual(prediction_result.shape, (25, 1))
        print("Predicted Results: ", prediction_result)


if __name__ == '__main__':
    unittest.main()
