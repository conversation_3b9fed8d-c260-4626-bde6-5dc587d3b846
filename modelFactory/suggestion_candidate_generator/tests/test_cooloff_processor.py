import unittest

import pandas as pd

from suggestion_candidate_generator.cooloff_processor import CoolOff_Processor


class MyTestCase(unittest.TestCase):
    def test_channel_cool_off(self):
        cool_off_processor = CoolOff_Processor()
        input_pdf = pd.read_csv("/tmp/drl_inputs/input_pdf_drl.csv")
        channel_cool_off_pdf = pd.read_csv("/tmp/drl_inputs/channel_cooloff.csv")
        # Using channel cool-off matrix figure out which channels are allowed
        input_pdf = cool_off_processor.generate_allow_channel_columns(input_pdf, channel_cool_off_pdf)

        channel_list = list(channel_cool_off_pdf['nextChannel'].unique())
        #allow_col_list = [f'allow_channel_{channel}' for channel in channel_list]

        #for allow_col in allow_col_list:
        #    assert (allow_col in input_pdf.columns) == True, f"{allow_col} not added as a column failed"

        assert ('allow_channel' in input_pdf.columns) == True, "allow_channel not added as a column failed"


    def test_content_cool_off(self):
        cool_off_processor = CoolOff_Processor()
        input_pdf = pd.read_csv("/tmp/drl_inputs/input_pdf_drl.csv")
        content_cool_off_pdf = pd.read_csv("/tmp/drl_inputs/content_cooloff.csv")
        # # Using channel cool-off matrix figure out which channels are allowed
        input_pdf = cool_off_processor.generate_allow_content_columns(input_pdf, content_cool_off_pdf)

        content_list = list(content_cool_off_pdf['nextContent'].unique())
        allow_col_list = [f'allow_content_{content}' for content in content_list]

        for allow_col in allow_col_list:
            assert (allow_col in input_pdf.columns) == True, f"{allow_col} not added as a column failed"


if __name__ == '__main__':
    unittest.main()
