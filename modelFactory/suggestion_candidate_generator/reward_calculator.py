import logging
from datetime import timedelta, datetime

from dateutil.relativedelta import relativedelta

from suggestion_candidate_generator.data import Data


class RewardCalculator:

    def __init__(self):
        pass

    @staticmethod
    def printSummary(name, df, rowCount=10):

        print(f"Data frame: {name}:" + str(df.shape))
        print("*******************************")
        if Constants.DEBUG_MODE:
            df.info()
            print (df.head(rowCount))

    @staticmethod
    def reward_calculation(row, DEFAULT_CHANNEL_PROP_VAL=0.0001, DEFAULT_SEG_PROP_VAL=0.0001, ENABLE_CJ_REWARD=1, value_cols=[]):
        current_channel = row['currentChannel']
        reward = ((row['modelProb'] *
                  row['impact'] *
                  max(row[f'channelPropensity'], DEFAULT_CHANNEL_PROP_VAL) *
                  row['contentWeight'] *
                  row['actionSeqWeight']) \
                 + \
                 (row['wtCJSegmentReward'] *
                  ENABLE_CJ_REWARD *
                  max(row['segmentClimbProb'], DEFAULT_SEG_PROP_VAL))) \
                 - \
                 (row['cost'] *
                  row['modelProb'] *
                  max(row[f'channelPropensity'], DEFAULT_CHANNEL_PROP_VAL))

        # Iterate over each value columns and add the reward
        for value_col in value_cols:
            reward += row[value_col]
        #TODO: Calcuate dynamic cost
        #TODO: Calculate accurate channel impact

        return reward

    @staticmethod
    def channel_cool_off_penalty_calculation(row, CHANNEL_COOL_OFF_VIOL_PENALTY=7):
        """
        """
        # current_channel = row['currentChannel']
        if row[f'allow_channel'] < 1:
            penalty = CHANNEL_COOL_OFF_VIOL_PENALTY * (1 - row[f'allow_channel'])
            penalty = penalty / row['scaled_hcp_value']
            return penalty

        return 0

    @staticmethod
    def content_cool_off_penalty_calculation(row, CONTENT_COOL_OFF_VIOL_PENALTY):
        current_content = row['currentContent']
        if row[f'allow_content_{current_content}'] == 0:
            return CONTENT_COOL_OFF_VIOL_PENALTY

        return 0

    def get_reward_params(self, dimension):
        data = Data.get_instance()

        dim_reward_params_df = data.get_dataframe(f"reward_params_{dimension}")
        if dim_reward_params_df is None or dim_reward_params_df.empty:
            reward_params_df = data.get_dataframe("reward_params")
            dim_reward_params_df = reward_params_df[(reward_params_df['dimension'] == dimension) ]
            data.set_dataframe(f"reward_params_{dimension}", dim_reward_params_df)
        return dim_reward_params_df

    @staticmethod
    def add_reward_component_cols(input_pdf):

        data = Data.get_instance()
        reward_components_pdf = data.get_dataframe('reward_components')
        # Get values of 'viewName' column as list
        value_cols = reward_components_pdf['valueColumn'].unique().tolist()

        # Add yearMonth column to input_pdf using activityDate in string format
        input_pdf['yearMonth'] = input_pdf['activityDate'].apply(lambda x: x.strftime('%Y-%m'))

        # Iterate over each row and calculate the reward
        for index, row in reward_components_pdf.iterrows():
            # Calculate the reward for all other actions
            view_name = row['viewName']
            value_col = row['valueColumn']
            reward_component_name = row['rewardComponentName']
            reward_component_id = row['rewardComponentId']
            updated_view_name = f"{view_name}_{reward_component_name}_{reward_component_id}"

            # convert dimensions to list
            dimensions = row['dimensions']  # ['accountId', 'productId', 'yearMonth']
            dimensions = eval(dimensions)
            subset_cols = dimensions + [value_col]

            # Continue if any of dimensions is not present in input_pdf
            if not all(dim in input_pdf.columns for dim in dimensions):
                logging.warning(
                    f"Skipping view {updated_view_name} as one of the dimensions {dimensions} is not present in input_pdf")
                continue

            view_pdf = data.get_dataframe(updated_view_name).copy()

            if view_pdf is None or view_pdf.empty:
                logging.warning(f"Skipping view {updated_view_name} as it is not present in data")
                continue

            # Make each dimension col as same type as in input_pdf
            for dim in dimensions:
                view_pdf[dim] = view_pdf[dim].astype(input_pdf[dim].dtype)

            # Drop value_col if it is present in input_pdf
            if value_col in input_pdf.columns:
                input_pdf.drop(columns=[value_col], inplace=True)

            left_dim = dimensions
            right_dim = dimensions
            if data.get_param("ENABLE_PRODUCT_GROUP_MAPPING", False) and 'productId' in dimensions:
                # print("Product group mapping is enabled, use parentProductId to join with influence view")
                left_dim = ['parentProductId' if x == 'productId' else x for x in dimensions]
                # subset_cols = dimensions + [value_col]
                # view_pdf['parentProductId'] = view_pdf['parentProductId'].astype(input_pdf['parentProductId'].dtype)

            view_pdf = view_pdf.drop_duplicates(subset=subset_cols)
            input_pdf = input_pdf.merge(view_pdf[subset_cols], how='left', left_on=left_dim, right_on=right_dim, suffixes=('', '_view_dup'))
            input_pdf.drop(columns=[x for x in input_pdf.columns if x.endswith('_view_dup')], inplace=True)
            #print(input_pdf[value_col].describe())

            # Fill na values in value_col with 0
            input_pdf[value_col].fillna(0, inplace=True)


        return input_pdf, value_cols

    def calculate_reward(self, input_pdf):
        """
        """
        data = Data.get_instance()
        NO_ACTION_REWARD = data.get_param('NO_ACTION_REWARD', -1)
        DEFAULT_CHANNEL_PROP_VAL = data.get_param('DEFAULT_CHANNEL_PROP_VAL', 0.001)
        DEFAULT_SEG_PROP_VAL = data.get_param('DEFAULT_SEG_PROP_VAL', 0.001)
        ENABLE_CJ_REWARD = data.get_param('ENABLE_CJ_REWARD', 1)
        ENABLE_SALES_REWARD = int(data.get_param('ENABLE_SALES_REWARD', 0))

        # Merge channel impact on currentChannel
        channel_impact_pdf = self.get_reward_params('CHANNEL')
        input_pdf = input_pdf.merge(channel_impact_pdf[['dimValue', 'impact', 'cost']], how='left', left_on='currentChannel', right_on='dimValue')
        input_pdf.drop(columns=['dimValue'], inplace=True)

        # Merge content weight on currentContent
        # content_weight_pdf = data.get_dataframe('content_weight')
        # input_pdf = input_pdf.merge(content_weight_pdf[['content', 'contentWeight']], how='left', left_on='currentContent', right_on='content')
        input_pdf['contentWeight'] = 1

        # Merge action sequence weight on pastContent and currentContent
        # action_seq_weight_pdf = data.get_dataframe('action_seq_weight')
        # input_pdf = input_pdf.merge(action_seq_weight_pdf[['pastContent', 'currentContent', 'pastChannel', 'currentChannel', 'actionSeqWeight']], how='left', left_on=['pastContent', 'currentContent', 'pastChannel', 'currentChannel'])
        input_pdf['actionSeqWeight'] = 1

        # TBD: Merge segment probability and weight on currentSegment
        #segment_prob_pdf = data.get_dataframe('segment_prob')
        input_pdf['wtCJSegmentReward'] = 1

        data = Data.get_instance()
        reward_components_pdf = data.get_dataframe('reward_components')
        # Get values of 'viewName' column as list
        value_cols = reward_components_pdf['valueColumn'].unique().tolist()

        # Raise error if input_pdf does not have column from value_cols
        if not all(col in input_pdf.columns for col in value_cols):
            raise ValueError(f"Columns {value_cols} are not present in input_pdf columns {input_pdf.columns}")

        no_action_mask = (input_pdf['currentChannel'] == 'NO_ACTION')
        input_pdf.loc[no_action_mask,'reward'] = NO_ACTION_REWARD

        # Calculate the reward for all other actions
        input_pdf.loc[~no_action_mask, 'reward'] = (
            input_pdf.loc[~no_action_mask, :].apply(lambda row: self.reward_calculation(row, DEFAULT_CHANNEL_PROP_VAL,
                                                                                        DEFAULT_SEG_PROP_VAL,
                                                                                        ENABLE_CJ_REWARD,
                                                                                        value_cols), axis=1))

        # Subtract the channel-cool-off violation penalty
        input_pdf = self.penalize_channel_cool_off_violation(input_pdf, value_cols)

        # Subtract the content-cool-off violation penalty
        #input_pdf = self.penalize_content_cool_off_violation(input_pdf)

        return input_pdf

    def penalize_channel_cool_off_violation(self, input_pdf, value_cols):

        data = Data.get_instance()
        CHANNEL_COOL_OFF_VIOL_PENALTY = data.get_param('CHANNEL_COOL_OFF_VIOL_PENALTY', 7)

        na_violation_mask = (input_pdf['currentChannel'] == 'NO_ACTION')

        input_pdf.loc[na_violation_mask, 'channel_cool_off_penalty'] = 0

        CHANNEL_COOL_OFF_VIOL_PENALTY_SCALER = data.get_param('CHANNEL_COOL_OFF_VIOL_PENALTY_SCALER', 1.3)
        raw_sum = input_pdf[value_cols].sum(axis=1)
        if raw_sum.max() > raw_sum.min():
            normalized = (raw_sum - raw_sum.min()) / (raw_sum.max() - raw_sum.min())
        else:
            normalized = 0
        input_pdf['scaled_hcp_value'] = 1 + normalized * (CHANNEL_COOL_OFF_VIOL_PENALTY_SCALER - 1)

        input_pdf.loc[~na_violation_mask, 'channel_cool_off_penalty'] = (
            input_pdf.loc[~na_violation_mask, :].apply(lambda row: self.channel_cool_off_penalty_calculation(row, CHANNEL_COOL_OFF_VIOL_PENALTY), axis=1))

        print("Channel cool off penalty df:")
        print(input_pdf[(input_pdf.allow_channel > 0) & (input_pdf.allow_channel < 1)][['channel_cool_off_penalty', 'scaled_hcp_value', 'reward']])
        print(input_pdf['reward'].describe())
        print(input_pdf['channel_cool_off_penalty'].describe())
        input_pdf['reward'] = input_pdf['reward'] - input_pdf['channel_cool_off_penalty']
        # input_pdf.drop(columns=['channel_cool_off_penalty', 'scaled_hcp_value'], inplace=True)
        return input_pdf

    def penalize_content_cool_off_violation(self, input_pdf):
        data = Data.get_instance()
        CONTENT_COOL_OFF_VIOL_PENALTY = data.get_param('CONTENT_COOL_OFF_VIOL_PENALTY', 7)

        na_violation_mask = ((input_pdf['currentChannel'] == 'NO_ACTION') |
                             (input_pdf['currentContent'] == "NA") |
                             (input_pdf['currentContent'].isna()))

        input_pdf[na_violation_mask]['content_cool_off_penalty'] = 0

        input_pdf[~na_violation_mask]['content_cool_off_penalty'] = (
            input_pdf[~na_violation_mask].apply(lambda row: self.content_cool_off_penalty_calculation(row, CONTENT_COOL_OFF_VIOL_PENALTY), axis=1))

        input_pdf['reward'] = input_pdf['reward'] - input_pdf['content_cool_off_penalty']
        input_pdf.drop(columns=['content_cool_off_penalty'], inplace=True)
        return input_pdf

