import logging
from suggestion_candidate_generator.constants import Constants

from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta

import pandas as pd

import requests
from requests.auth import HTTPBasicAuth 
import urllib.parse

import json
import sys
import os

from sqlalchemy import create_engine

class SCCIntegrator():

    def __init__(self, region, customer, env):
        self.region = region
        self.customer = customer
        self.env = env
        self.access_token = ""
        self.scope = 'devnovartis-sa-br-prod'

        ecosystem="prod"
        if 'deveks' in region:
            ecosystem="saasdev"
        if 'qaeks' in region:
            ecosystem="aimqa"

        self.ecosystem = ecosystem

        self.strategy = {}

    def authenticate(self):

        aimurl = "aim"
        if self.ecosystem == 'aimqa':
            aimurl = "aimqa"
        if self.ecosystem == 'saasdev':
            aimurl = "aimdev"

        # Read DRL credentials from environment
        appId = os.environ.get("drl_app_id", "97f821d8-829b-4f0a-a183-aa4cdd74f73b")
        appToken = os.environ.get("drl_app_token", "7m9hXAbSYhQnqeTc_Kk-9rQGx9W2-sE0R4XtbVFtkChSx-6Xe1Ws8e9xCFtGav7DfmfJTlFbqgyPig1UlDjoaDaaD0Up_WdCwnjySInbjveeDWhAFbvryferLfXlvIvI6XFFiOo4GrI1CBjDs4EWD2yN0IhXrH3AACX0PP7-zJo")

        url = f"https://{aimurl}-api.aktana.com/rbac-api/v1/authentication"
        data = { "client_id": appId, "client_secret": appToken, "grant_type": "client_credentials", "request_jwt_client_token": "true" }

        print (f"Calling AIM authentication url at: {url}")
        r = requests.post(url, json=data)
        #print (r.json())
        if r.status_code == 200:
            self.access_token = r.json().get('access_token')
            self.token_type = r.json().get('token_type')
        else:
            print(f"aim authentication response:{r.status_code}")
            print(r.text)

        #print(f"AIM authentication completed with access-token: Bearer {self.access_token}")
        return self.access_token
    
    def getStrategy(self, id):
        
        # Sample output: {'strategyId': 1147, 'name': 'Improve Customer Journey Progression', 'repTeamUID': 'COSENTYX_PUBLIC', 'repTeamName': 'COSENTYX_PUBLIC', 'productUID': 'a005f000007ldKPAAY', 'productName': 'Cosentyx EA', 'countryCode': 'BR', 'goalId': 1001, 'goalName': 'Grow Market Share', 'messageTopics': [], 'audiences': [{'typeDefinitionName': 'Tiers', 'typeNames': ['T3', 'T2']}, {'typeDefinitionName': 'Marketing Opt Out', 'typeNames': ['N/A']}, {'typeDefinitionName': 'Sample Consent', 'typeNames': ['Sim']}], 'kpis': [{'kpiId': 1143, 'kpiTypeId': 1001, 'kpiTypeName': 'Customer Journey Progression', 'initialValue': 0, 'targetValue': 0, 'currentValue': 0, 'additionalSetting': 'T1', 'kpiRank': 1, 'kpiTypeDescription': 'The total number of Accounts moved to the stage indicated in the KPI name in the given period. This KPI is by Account and Product.', 'kpiTypeAggregationLevel': 'Brand', 'kpiTypeTemplate': '%(kpiTypeDescription)s Current Value: %(currentValue)s'}, {'kpiId': 1144, 'kpiTypeId': 1003, 'kpiTypeName': '# of Calls completed', 'initialValue': 0, 'targetValue': 0, 'currentValue': 0, 'additionalSetting': '', 'kpiRank': 2, 'kpiTypeDescription': 'The total number of Visit to Physicians completed by reps in the given period. This KPI is by by Account and Rep.', 'kpiTypeAggregationLevel': 'Brand', 'kpiTypeTemplate': '%(kpiTypeDescription)s Current Value: %(currentValue)s'}, {'kpiId': 1145, 'kpiTypeId': 1002, 'kpiTypeName': '# of RTE completed', 'initialValue': 0, 'targetValue': 0, 'currentValue': 0, 'additionalSetting': '', 'kpiRank': 3, 'kpiTypeDescription': 'The total number of Rep-Triggered-Email sent to Physicians by reps in the given period. This KPI is by Account, Rep, and Product.', 'kpiTypeAggregationLevel': 'Brand', 'kpiTypeTemplate': '%(kpiTypeDescription)s Current Value: %(currentValue)s'}], 'startPeriod': 'Q4-2023', 'startPeriodId': 48799, 'endPeriod': 'Q1-2024', 'endPeriodId': 48800, 'strategyRank': 1, 'status': 'INPROGRESS'}
        self.authenticate()
        url = f"https://scc-{self.region}-{self.env}.aktana.com/scc-api/v1.0/internal/strategies/{id}?customer={self.customer}"
        print (f"Calling SCC API interface url at: {url}")
        r = requests.get(url, headers={'Content-Type':'application/json',
               'Authorization': 'Bearer {}'.format(self.access_token)})
        
        strategy = None
        if r.status_code == 200:
            strategy = r.json()
        else:
            print(f"getStrategy response:{r.status_code} from {url}")
            print(r.text)

        if strategy:
            strategy["startDate"] = self._convertPeriodToDate(strategy.get('startPeriod'))
            strategy["endDate"] = self._convertPeriodToDate(strategy.get('endPeriod'), False)
            # Model-trigger-name cannot contain spaces and cannot be more than 100 chars. But Factor-name (model_trigger + channel) cannot be more than 100
            # Truncate strategy-name and take just first 74 chars.  Leave the rest for channel-name to be appended in factor.  
            # So, model-trigger has to be limited to 74 since WEB_INTERACTIVE_CHANNEL with space-pound before it makes it 26 chars.
            strategy["modelTriggerName"] = "TacticGenie_Strategy_" + str(strategy.get('strategyId'))
            strategy["journeyName"] = strategy.get('name').replace(' ', '_')[0:74] 

        self.strategy = strategy

        return self.strategy

    def getAttributeTypes(self, audienceTypeDefinitionNames):
        
        self.authenticate()
        #https://scc-usdeveks-prod.aktana.com/scc-api/v1.0/internal/audienceTypes?customer=devnovartisbr&audienceTypeDefinitionNames=Specialities&page=0&size=20
        url = f"https://scc-{self.region}-{self.env}.aktana.com/scc-api/v1.0/internal/audienceTypes?customer={self.customer}&audienceTypeDefinitionNames={audienceTypeDefinitionNames}&page=0&size=200"
        print (f"Calling SCC API for audiencetypes at: {url}")
        r = requests.get(url, headers={'Content-Type':'application/json',
               'Authorization': 'Bearer {}'.format(self.access_token)})
        
        audienceTypes = None
        if r.status_code == 200:
            audienceTypes = r.json()
            assert(audienceTypes["totalElements"] == audienceTypes["numberOfElements"])
        else:
            print(f"getStrategy response:{r.status_code} from {url}")
            print(r.text)

        return audienceTypes

    def getAllAttributeTypes(self, audienceTypeDefinitionNames):

        params = {'customer': self.customer, 'audienceTypeDefinitionNames': audienceTypeDefinitionNames, 'page': '0', 'size': '200' }
        query_params = urllib.parse.urlencode(params, quote_via=urllib.parse.quote, doseq=True)        
        self.authenticate()
        url = f"https://scc-{self.region}-{self.env}.aktana.com/scc-api/v1.0/internal/audienceTypes/group?{query_params}"
        print (f"Calling SCC API for audiencetypes at: {url}")
        r = requests.get(url, headers={'Content-Type':'application/json',
               'Authorization': 'Bearer {}'.format(self.access_token)})
        
        audienceTypes = None
        if r.status_code == 200:
            audienceTypes = r.json()
        else:
            print(f"getStrategy response:{r.status_code} from {url}")
            print(r.text)

        return audienceTypes

    def refreshDSERefData(self):
        
        #https://<usdeveks>dse<prod>.aktana.com/<devbiogenna>/api/info?rcd=true
        #https://<region>dse<env>.aktana.com/<customer-instance>/api/info?rcd=true
        url = f"https://{self.region}dse{self.env}.aktana.com/{self.customer}/api/secure-info?rcd=true"
        print (f"Calling DSE API to refresh custom datapoints cache at: {url}")

        # Use internal auth
        #r = requests.get(url, auth=HTTPBasicAuth('admin','@ktana20!8'))
        # Use AIM client-credentials
        self.authenticate()
        r = requests.get(url, headers={'Authorization': 'Bearer {}'.format(self.access_token)})
        
        resp = None
        if r.status_code == 200:
            resp = r.text
        else:
            print(f"refreshDSERefData response:{r.status_code} from {url}")
            print(r.text)

        return resp

    def postTouchPointFile(self, channels):

        self.authenticate()
        customerOnly = self.customer[:-2]
        if self.customer[-1].isnumeric(): # If last-char is numeric, it could be biogeneu2.  Strip out last 3 chars
            customerOnly = self.customer[:-3]

        customerOnly = customerOnly.upper()
        touchPointFile = self._generateTouchPointFile(customerOnly, self.strategy, channels)
        print ("")
        print ("")
        print (touchPointFile)

        auth_header = {"Authorization", f"{self.token_type} {self.access_token}" }
        #url = f"https://scc-{self.region}-{self.env}.aktana.com/scc-api/v1.0/internal/strategy/{id}?scope={self.scope}"
        url = f"https://campaign-{self.region}-{self.env}.aktana.com/campaign-api/internal/touchpoint/file?customer={customerOnly}&countryCode={self.strategy.get('countryCode')}"
        print (f"Calling touchpoint interface url at: {url}")
        r = requests.post(url, headers={'Authorization': 'Bearer {}'.format(self.access_token)}, files = {'file': ('touchpoint.json', touchPointFile, 'application/json')} )

        print(r.text)
        if r.status_code != 200:
            print(f"refreshDSERefData failed with response:{r.status_code} from {url}")
            raise Exception(f"refreshDSERefData failed with response:{r.status_code}")

    def _printStrategy(self, strategy):
        print (strategy)
        #print (strategy.keys())
        print(f"Name:{strategy.get('name')}")
        print(f"Product:{strategy.get('productUID')},{strategy.get('productName')}")
        print(f"RepTeam:{strategy.get('repTeamUID')},{strategy.get('repTeamName')}")
        audiences = strategy.get('audiences')
        print("Audiences:")
        for attributeType in audiences:
            print(f"  Type:{attributeType.get('typeDefinitionName')}, Values:{attributeType.get('typeNames')}")
        
        print(f"StartPeriod={strategy.get('startPeriod')}, StartDate={strategy.get('startDate')}")
        print(f"EndPeriod={strategy.get('endPeriod')}, EndDate={strategy.get('endDate')}")

    def _convertPeriodToDate(self, startPeriod, isStart=True):
        dateParts = startPeriod.split('-')
        year = dateParts[1]
        periodPrefix = dateParts[0][0]
        periodNumber = int(dateParts[0][1:])
        monthMultiplier = 1
        if periodPrefix == "Q":
            monthMultiplier = 3
        if periodPrefix == "T":
            monthMultiplier = 4
        if periodPrefix == "S":
            monthMultiplier = 6
        if periodPrefix == "Y":
            monthMultiplier = 0

        if isStart:
            monthOffset = (periodNumber-1) * monthMultiplier
        else:
            monthOffset = periodNumber * monthMultiplier
        if isStart:
            month = 1+monthOffset
        else:
            month = monthOffset
        finalDate = year + '-' + str(month).zfill(2) + '-01'
        computed_date = datetime.strptime(finalDate, '%Y-%m-%d') # Computes the first day of the month
        if not isStart:
            # Add 1 month and substract 1 day to get last day of the month
            computed_date = computed_date + relativedelta(months=1) - relativedelta(days=1)
        computed_date_str = computed_date.strftime("%Y-%m-%d")

        return computed_date_str

    def _generateTouchPointFile(self, customerOnly, strategy, channels):
        touchPoint = {}
        touchPoint["generationLocalDate"] = date.today().strftime("%Y-%m-%d")
        touchPoint["modelTriggerName"] = strategy.get('modelTriggerName') 
        touchPoint["specVersion"] = 1
        touchPoint["journeyName"] = strategy.get('journeyName') 
        touchPoint["repTeamUID"] = strategy.get('repTeamUID')
        # Use instanceName to support multi-country DSE-API lookup correctly using DCS - DEVOPS-6859
        touchPoint["instanceName"] = self.customer
        touchPoint["customerCode"] = customerOnly
        touchPoint["countryCode"] = strategy.get('countryCode')
        touchPoint["productUID"] = strategy.get('productUID')
        touchPoint["treatAsJourney"] = False
        touchPoint["startDateTime"] = str(strategy.get('startDate')) + "T00:00:00"
        touchPoint["endDateTime"] = str(strategy.get('endDate')) + "T00:00:00"

        # tag id for DRL_CANDIDATE in prod db: 2925
        # tag id for DRL_JOURNEY in prod db: 2926
        # tag id for DRL_CANDIDATE in saasdev db: 683
        # tag id for DRL_JOURNEY in saasdev db: 684
        # tag id for DRL_CANDIDATE in aimqa db: 1751
        # tag id for DRL_JOURNEY in aimqa db: 1752

        tagId = 2925
        tagName = "DRL_CANDIDATE"
        if self.ecosystem == 'aimqa':
            tagId = 1751
        if self.ecosystem == 'saasdev':
            tagId = 683
        if Constants.MULTI_DAY_EXECUTION:
            tagId = tagId + 1
            tagName = "DRL_JOURNEY"

        nodes = []
        for channel in channels:
            node = {}
            node["id"] = channel
            node["activityType"] = "TACTIC"
            if channel == "VISIT_CHANNEL":
                action = "VISIT_DETAIL"
            elif channel == "WEB_INTERACTIVE_CHANNEL":
                action = "WEB_EDETAIL"
            else:
                action = "SEND"
            node["actions"] = [action]
            node["actorTypes"] = ["FieldRep"]
            node["factorType"] = ["Suggestion"]
            #node["messages"] = []
            #node["messageSet"] = null
            #node["reasonText"] = "", 
            #node["modelTriggerPriority"] = "",
            #node["modelTriggerAccountPriority"] = "",
            #node["isCritical"] = false,
            node["strategyId"] = strategy.get('strategyId')
            node["strategyName"] = strategy.get('name')
            node["tags"] = [
            {
                "tagId": tagId,
                "tagName": tagName
            }
            ]

            rule = {}
            rule["id"] = 1
            rule["datapointType"] = "METRIC"
            rule["datapointName"] = "LVAttribute:" + touchPoint["modelTriggerName"] + ":" + touchPoint["modelTriggerName"] + "~SuggestedChannel:externalCandidateValue_std_akt"
            rule["operator1"] = "CONTAINS"
            rule["comparisonValue1"] = channel
            ruleset = {}
            ruleset["rules"] = [rule]
            ruleset["groupingCondition"] = "AND"
            node["ruleSet"] = ruleset 
            nodes.append(node)
        touchPoint["nodes"] = nodes
        touchpointFile = {}
        touchpointFile["touchpoints"]= [touchPoint]
        touchpointString = json.dumps(touchpointFile)

        # TODO: Use AF-509 to invoke the touchpoint interface
        return touchpointString

    
    @staticmethod
    def readStrategy(region, customer, env, id):
        sccapi = SCCIntegrator(region, customer, env)
        strategy = sccapi.getStrategy(id)
        print (strategy)
        #sccapi._printStrategy(strategy)        
        return sccapi, strategy

    @staticmethod
    def uploadTouchPoints(region, customer, env, id, channels):
        sccapi, strategy = SCCIntegrator.readStrategy(region, customer, env, id)    
        print(sccapi.postTouchPointFile(channels))    
        return sccapi, strategy
    
if __name__ == '__main__':
    sccapi, strategy = SCCIntegrator.uploadTouchPoints('usqaeks', 'qagenentechca', 'dev', sys.argv[1], ["SEND_CHANNEL", "VISIT_CHANNEL", "WEB_INTERACTION_CHANNEL"])
    #sccapi, strategy = SCCIntegrator.uploadTouchPoints('usdeveks', 'devnovartisbr', 'prod', sys.argv[1], ["SEND_CHANNEL", "VISIT_CHANNEL", "WEB_INTERACTION_CHANNEL"])
    print("Token:"+sccapi.access_token)
    print(sccapi.getAllAttributeTypes(["Specialties", "Marketing Opt Out"]))
    sccapi.refreshDSERefData()
