import logging

from sklearn import preprocessing

from abstract_model_factory.abstract_transformer import AbstractTransformer
from suggestion_candidate_generator.behavior_simulator import BehaviorSimulator
from suggestion_candidate_generator.constants import Constants
from suggestion_candidate_generator.cooloff_processor import CoolOff_Processor
from suggestion_candidate_generator.data import Data
import pandas as pd
import numpy as np
from scipy import stats
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta

from suggestion_candidate_generator.learner import <PERSON><PERSON>
from suggestion_candidate_generator.predictor import Predictor
from suggestion_candidate_generator.qa_data_handler import Q<PERSON><PERSON><PERSON>andler
from suggestion_candidate_generator.reward_calculator import RewardCalculator


class Transformer(AbstractTransformer):

    def printSummary(self, name, df, rowCount=10):

        print(f"Data frame: {name}:" + str(df.shape))
        print("*******************************")
        if Constants.DEBUG_MODE:
            df.info()
            print (df.head(rowCount))

    def printDebugDetails(self):
        data = Data.get_instance()

        interactions_df = data.get_dataframe("interactions")
        self.printSummary("interactions", interactions_df)

        account_products_with_most_actions_df = accounts_per_month_df[(accounts_per_month_df['countRank'] < 20) ]
        print("Top account/products in each month:")
        print("*******************************")
        print(account_products_with_most_actions_df.head(account_products_with_most_actions_df.shape[0]))
        actions_for_top_accounts_df = pd.merge(interactions_df, account_products_with_most_actions_df[['accountId', 'productId', 'activityYearMonth', 'countRank']], on=['accountId', 'productId', 'activityYearMonth'])
        print("All actions from top account/products in each month:")
        print("*******************************")
        print(actions_for_top_accounts_df.sort_values(by=['activityYearMonth', 'countRank', 'accountId', 'productId', 'activityDate']).head(actions_for_top_accounts_df.shape[0]))

        self.printSummary("accounts_per_month", accounts_per_month_df)
        self.printSummary("prev_dsl", data.get_dataframe("prev_dsl"))

    def qa_module(self):
        data = Data.get_instance()

        if Constants.EXECUTION_MODE:
            predictions_df = data.get_dataframe("predictions_pdf")
            self.printSummary("Predictions", predictions_df, 50)
        else:
            self.printSummary("simulation_rate", data.get_dataframe("simulation_rate"))

            accounts_per_month_df = data.get_dataframe("accounts_per_month")
            summary_df = accounts_per_month_df.groupby(by=['activityYearMonth', 'isActual']).aggregate(accountProductCount=('accountProductCount','count'))
            self.printSummary("Use actuals summary", summary_df, 50)

            iteration_training_df = data.get_dataframe("training_df")
            self.printSummary("Training data", iteration_training_df)

        #self.printDebugDetails()

    # Based on month-offsets in simulation-rate table and history-offset, compute actual-months to be used for learning
    def build_simulation_months(self):
        data = Data.get_instance()
        #TODO: Change param below to 1 once table is available.  In devnovartisbr data is available one for 2 months ago
        simulation_month_offset = int(data.get_param('SIMULATION_HISTORY_MONTH_OFFSET', 2))
        run_date = data.get_param("rundate")
        if isinstance(run_date, str):
            run_date = datetime.strptime(run_date, "%Y-%m-%d").date()
        today = run_date
        first_day_of_month = today.replace(day=1)
        firstday_of_last_simulation_month = first_day_of_month - relativedelta(months=simulation_month_offset)
        print('firstday_of_last_simulation_month:', firstday_of_last_simulation_month)

        # Compute actual-month for each simulation-rate row (month) based on offset and last-simulation-month
        simulation_rate_df = data.get_dataframe("simulation_rate")
        simulation_rate_df['simulation_month'] = simulation_rate_df.apply(lambda x: firstday_of_last_simulation_month - pd.offsets.MonthBegin(x['month_offset_from_last']), axis=1)
        data.set_dataframe("simulation_rate", simulation_rate_df)

        self.printSummary("simulation_rate", data.get_dataframe("simulation_rate"))

    # Elibigle account/products is the list of account/products that have enough historic transactions and will be used to simulate actions for learning
    def build_actual_vs_sim_accounts(self):
        data = Data.get_instance()
        interactions_df =  data.get_dataframe("interactions")

        interactions_df['activityYearMonth'] = pd.to_datetime(interactions_df['activityDate']).dt.normalize() - pd.offsets.MonthBegin(1)

        #Build summary of activity per account/product/month
        accounts_per_month_df = interactions_df.groupby(by=['accountId', 'productId', 'activityYearMonth']).aggregate(activityCount=('activityDate','count'),hcpSegment=('hcpSegment','min')).reset_index()

        # Remove account/products that have too few transactions
        interactions_df, accounts_per_month_df = self.remove_ineligible_accounts(interactions_df, accounts_per_month_df)

        # Find total # of accountProducts per month to then apply simulation_percent
        accounts_per_month_df['countRank'] = accounts_per_month_df.groupby(by=['activityYearMonth'])['activityCount'].rank(ascending=False,method='first')
        accounts_per_month_df = accounts_per_month_df.reset_index()
        actions_monthly_summary_df = accounts_per_month_df.groupby(by=['activityYearMonth']).aggregate(accountProductCount=('countRank','max'),activityCountMax=('activityCount','max')).reset_index()
        
        #Compute how many actual account/products should be used per month based on simulation-rate percentage
        simulation_rate_df = data.get_dataframe("simulation_rate")
        simulation_rate_df = pd.merge(actions_monthly_summary_df[['activityYearMonth', 'accountProductCount', 'activityCountMax']], simulation_rate_df[['simulation_month', 'simulation_percent', 'month_offset_from_last']], left_on='activityYearMonth', right_on='simulation_month')
        simulation_rate_df['actualsLimit'] = (1 - simulation_rate_df['simulation_percent'])*simulation_rate_df['accountProductCount']

        # Mark the account/products (by month) that should be actuals vs. simulated
        # Top sim_percent account/products will use actuals.  Rest will use simulated actions
        accounts_per_month_df = pd.merge(accounts_per_month_df, simulation_rate_df[['simulation_month', 'accountProductCount', 'actualsLimit']], left_on='activityYearMonth', right_on='simulation_month')
        accounts_per_month_df['isActual'] = accounts_per_month_df['countRank'] <=  accounts_per_month_df['actualsLimit']

        # Add isActual flag to interactions
        interactions_df = pd.merge(interactions_df, accounts_per_month_df[['accountId', 'productId', 'activityYearMonth', 'isActual']], on=['accountId', 'productId', 'activityYearMonth'])

        print("simulation_rate_df:")
        print(simulation_rate_df)
        accounts_per_month_summary = accounts_per_month_df.groupby(['activityYearMonth', 'isActual']).size().reset_index(name='accountProductCount')
        print("accounts_per_month_summary:")
        print(accounts_per_month_summary)
        # Add the resulting dataframes to Data for later use
        data.set_dataframe("interactions", interactions_df)
        data.set_dataframe("simulation_rate", simulation_rate_df)
        data.set_dataframe("accounts_per_month", accounts_per_month_df)
    
    # Remove accounts/products that don't have at least ELIGIBLE_TRANSACTION_MIN_LIMIT transactions in ELIGIBLE_TRANSACTION_MONTHS months
    def remove_ineligible_accounts(self, interactions_df, accounts_per_month_df):
        data = Data.get_instance()
        eligible_months = int(data.get_param('ELIGIBLE_TRANSACTION_MONTHS', 6))
        transaction_limit = int(data.get_param('ELIGIBLE_TRANSACTION_MIN_LIMIT', 1))
        is_product_level = int(data.get_param('ELIGIBLE_TRANSACTION_IS_BY_PRODUCT', 1))

        simulation_rate_df = data.get_dataframe("simulation_rate")

        eligible_months_df = simulation_rate_df[(simulation_rate_df['month_offset_from_last'] < eligible_months)]
        self.printSummary("Eligible months", eligible_months_df)
        self.printSummary("All interactions", interactions_df)

        eligible_accounts_df = pd.merge(accounts_per_month_df, eligible_months_df[['simulation_month']], left_on='activityYearMonth', right_on='simulation_month')

        self.printSummary("eligible_accounts_df", eligible_accounts_df)
        if is_product_level == 1:
            eligible_accounts_summary_df = eligible_accounts_df.groupby(by=['accountId', 'productId']).aggregate(activityCount=('activityCount','sum')).reset_index()
            eligible_accounts_summary_df = eligible_accounts_summary_df[(eligible_accounts_summary_df['activityCount'] >= transaction_limit)]
            interactions_df = pd.merge(interactions_df, eligible_accounts_summary_df[['accountId', 'productId']], on=['accountId', 'productId'])
            accounts_per_month_df = pd.merge(accounts_per_month_df, eligible_accounts_summary_df[['accountId', 'productId']], on=['accountId', 'productId'])
        else:
            eligible_accounts_summary_df = eligible_accounts_df.groupby(by=['accountId']).aggregate(activityCount=('activityCount','sum'))
            eligible_accounts_summary_df = eligible_accounts_summary_df[(eligible_accounts_summary_df['activityCount'] > transaction_limit)]
            interactions_df = pd.merge(interactions_df, eligible_accounts_summary_df[['accountId']], on=['accountId'])
            accounts_per_month_df = pd.merge(accounts_per_month_df, eligible_accounts_summary_df[['accountId']], on=['accountId'])
        
        data.set_dataframe("eligible_accounts_summary", eligible_accounts_summary_df)

        ineligible_accounts_summary_df = eligible_accounts_summary_df[(eligible_accounts_summary_df['activityCount'] < transaction_limit)]
        self.printSummary("Eligible account summary", eligible_accounts_summary_df)
        self.printSummary("Ineligible account summary", ineligible_accounts_summary_df)
        self.printSummary("Filtered interactions", interactions_df)
        self.printSummary("Filtered accounts_per_month", accounts_per_month_df)

        return interactions_df, accounts_per_month_df

    # Build previous DSL (days-since-last) dataframe for all account/products and all channels/content
    def initialize_prev_dsl(self):
        data = Data.get_instance()
        eligible_accounts_summary_df = data.get_dataframe("eligible_accounts_summary")
        eligible_accounts_summary_df = eligible_accounts_summary_df.drop_duplicates(subset=['accountId'], keep='first')

        channel_list = data.get_param("channel_list")
        content_list = data.get_param("content_list")

        # Compute initial prev-dsl-channel using all account/products and all channels
        unique_channel_df = pd.DataFrame(channel_list, columns=['currentChannel'])
        all_dsl_channel_df = pd.merge(eligible_accounts_summary_df, unique_channel_df, how="cross")

        prev_dsl_df = data.get_dataframe("initial_dsl_channel")
        prev_dsl_df = pd.merge(all_dsl_channel_df, prev_dsl_df, on=['accountId', 'currentChannel'], how='left')
        prev_dsl_df = prev_dsl_df.pivot(index=['accountId'], columns=['currentChannel'], values=['lastActivityDate'])
        prev_dsl_df = prev_dsl_df.droplevel(level=0,axis=1)
        prev_dsl_df = prev_dsl_df.reset_index()

        # Compute the initial dsl values for channels
        history_start_date = data.get_param("history_start_date", None)
        if Constants.EXECUTION_MODE:
            # In case of execution first day of the prediction as start date
            history_start_date = data.get_param("startdate", None)
            history_start_date = datetime.fromordinal(history_start_date.toordinal())
        for channel in channel_list:
            prev_dsl_df[f"{channel}"] = pd.to_datetime(prev_dsl_df[f"{channel}"],format="%Y-%m-%d")
            prev_dsl_df[f"dsl_channel_{channel}"] = (history_start_date - prev_dsl_df[f"{channel}"]) / pd.Timedelta(days=1)
            prev_dsl_df[f"dsl_channel_{channel}"] = prev_dsl_df[f"dsl_channel_{channel}"].fillna(31)
            print(f"Initial DSL summary for {channel}")
            print(prev_dsl_df.groupby(f'dsl_channel_{channel}').size())
        prev_dsl_df = prev_dsl_df.drop(columns=channel_list)
        data.set_dataframe("prev_dsl", prev_dsl_df)

        #TODO: For now, just add dsl columns for content.  Later, read the actual content history and build dsl column values
        unique_content_df = pd.DataFrame(content_list, columns=['lastContent'])
        for content in content_list:
            prev_dsl_df[f"dsl_content_{content}"] = 31

        data.set_dataframe("prev_dsl", prev_dsl_df)

    def build_actions_for_day(self, current_date, current_month):
        # Find all account/products from monthly summary of actions
        data = Data.get_instance()
        interactions_df = data.get_dataframe("interactions")
        accounts_per_month_df = data.get_dataframe("accounts_per_month")

        # Get actual accounts based on isActual for the month and merge with actuals interactions for the day
        actual_interactions_for_day_df = interactions_df[(interactions_df['activityDate'] == current_date) & (interactions_df['isActual'] == True) ] 
        # print("     # of actual actions for day:" + str(actual_interactions_for_day_df.shape))
        actual_noactions_for_day_df = accounts_per_month_df[(accounts_per_month_df['activityYearMonth'] == current_month) & (accounts_per_month_df['isActual'] == True) ] 
        # print("     # of actual account/products for day:" + str(actual_noactions_for_day_df.shape))
        actuals_for_day_df = pd.merge(actual_noactions_for_day_df[['accountId', 'productId', 'hcpSegment', 'isActual']], actual_interactions_for_day_df[['accountId', 'productId', 'currentChannel', 'currentContent']], how="left", on=['accountId', 'productId'], suffixes=('', '_y'))
        actuals_for_day_df.currentChannel = actuals_for_day_df.currentChannel.fillna('NO_ACTION')
        actuals_for_day_df.currentContent = actuals_for_day_df.currentContent.fillna('NA')
        actuals_for_day_df['contentWeight'] = 1.0
        actuals_for_day_df['activityDate'] = current_date
        actuals_for_day_df['activityYearMonth'] = current_month

        # print("     # of actual actions:" + str(actuals_for_day_df.shape))
        # print("     # of actual account/products:" + str(actuals_for_day_df.groupby(['accountId', 'productId']).ngroups))

        # Get simulated accounts/products
        # Approach: Get all account/products that were not in top x% for the month and cross-join with all possible actions/content including NO_ACTION/NA
        simulated_accounts_for_day_df = accounts_per_month_df[(accounts_per_month_df['activityYearMonth'] == current_month) & (accounts_per_month_df['isActual'] == False) ] 
        logging.debug("     # of simulated account/products in month:" + str(simulated_accounts_for_day_df.shape))
        logging.debug("     # of simulated account/products in month:" + str(simulated_accounts_for_day_df.groupby(['accountId', 'productId']).ngroups))
        logging.debug("     # of simulated accounts in month:" + str(simulated_accounts_for_day_df.groupby(['accountId']).ngroups))
        # Cartesian join account/product list with list of all content - this will cover channels and content (including the NO_ACTION/NA row)
        # content_tags_df = data.get_dataframe("content_tags")
        channels_df = data.get_dataframe("channels")
        simulated_for_day_df = simulated_accounts_for_day_df[['accountId', 'productId', 'hcpSegment', 'isActual']].merge(channels_df, how="cross")
        simulated_for_day_df['activityDate'] = current_date
        simulated_for_day_df['activityYearMonth'] = current_month
        # print("     # of simulated actions for simulated account/products:" + str(simulated_for_day_df.shape))

        return (actuals_for_day_df, simulated_for_day_df)
    
    def apply_cooloff_and_compute_reward(self, current_date, actuals_for_day_df, simulated_for_day_df):
        pass

    def sample_actions_for_day(self, actuals_for_day_df, simulated_for_day_df):
        data = Data.get_instance()
        sampling_rate = float(data.get_param('SAMPLING_RATE', 1.0))

        if sampling_rate < 1.0:
            actuals_for_day_df = actuals_for_day_df.sample(frac=sampling_rate, replace=False, random_state=1)
            simulated_for_day_df = simulated_for_day_df.sample(frac=sampling_rate, replace=False, random_state=1)
            logging.info("     # of sampled actual actions:" + str(actuals_for_day_df.shape))
            logging.info("     # of sampled simulated actions:" + str(simulated_for_day_df.shape))

        return actuals_for_day_df, simulated_for_day_df

    def prepare_actions_for_day(self, current_date, actuals_for_day_df, simulated_for_day_df):
        
        actuals_for_day_df, simulated_for_day_df = self.sample_actions_for_day(actuals_for_day_df, simulated_for_day_df) # Do any sampling to reduce data volume

        data = Data.get_instance()
        prev_dsl_df = data.get_dataframe("prev_dsl")
        #self.printSummary("prev_dsl", prev_dsl_df)

        # Build single list with actual and simulated actions
        actions_for_day_df = pd.concat([actuals_for_day_df, simulated_for_day_df])
        #self.printSummary("actions_for_day_df", actions_for_day_df)

        # Merge the dsl columns to the final data-frame
        actions_for_day_df = actions_for_day_df.merge(prev_dsl_df, on=['accountId'], how="inner")
        #self.printSummary("final actions for day1", actions_for_day_df)

        return actions_for_day_df

    def pick_best_move(self, actions_for_day_df):
        act_actions_for_day_df = actions_for_day_df[(actions_for_day_df['isActual'])]

        if not act_actions_for_day_df.empty:
            act_actions_for_day_df.loc[:,'moveSelected'] = 1
            act_actions_for_day_df.loc[:,'modelProb'] = 1

        sim_actions_for_day_df = actions_for_day_df[(actions_for_day_df['isActual']==False)]
        if sim_actions_for_day_df.empty:
            return act_actions_for_day_df, act_actions_for_day_df

        data = Data.get_instance()
        network_input_cols = data.get_param('network_input_cols')
        sim_actions_for_day_df.loc[:,'predicted_reward'] = Predictor.predict(sim_actions_for_day_df[network_input_cols])
        idx = sim_actions_for_day_df.groupby(by=['accountId','productId'])['predicted_reward'].idxmax()

        sim_actions_for_day_df.loc[idx, 'moveSelected'] = 1

        #self.printSummary("Sim Selected move summary:" , sim_actions_for_day_df.groupby(by=['accountId','productId', 'moveSelected']).aggregate(rowcount=('moveSelected','count')))
        #self.printSummary("Act Selected move summary:" , act_actions_for_day_df.groupby(by=['accountId','productId', 'moveSelected']).aggregate(rowcount=('moveSelected','count')))

        # Invoke behavior simulator to compute modelProb and repAccepted
        use_simulator = data.get_param('USE_BEHAVIOR_SIMULATOR', False)
        if use_simulator:
            print("Applying behavior simulator to predict action completion probability")
            behavior_simulator = BehaviorSimulator()
            sim_actions_for_day_df = behavior_simulator.simulate(sim_actions_for_day_df)
        else:
            print("Skipping the use of behavior simulator")
            sim_actions_for_day_df.loc[:,'modelProb'] = 1

        if not act_actions_for_day_df.empty:
            actions_for_day_df = pd.concat([act_actions_for_day_df, sim_actions_for_day_df])
        else:
            actions_for_day_df = sim_actions_for_day_df

        selected_actions_for_day_df = actions_for_day_df[(actions_for_day_df['moveSelected'] == 1)]
        #self.printSummary("selectedActions", selected_actions_for_day_df)
        return selected_actions_for_day_df, actions_for_day_df

    def update_dsl_for_day(self, current_date, selected_actions_for_day_df):
        data = Data.get_instance()
        prev_dsl_df = data.get_dataframe("prev_dsl")
        #self.printSummary("prev_dsl before dsl update for day", prev_dsl_df)
        
        channel_list = data.get_param("channel_list")
        content_list = data.get_param("content_list")

        if selected_actions_for_day_df is None or selected_actions_for_day_df.empty:
            dsl_channel_cols = [f'dsl_channel_{channel}' for channel in channel_list]
            prev_dsl_df[dsl_channel_cols] = prev_dsl_df[dsl_channel_cols] + 1
            data.set_dataframe("prev_dsl", prev_dsl_df)
            return

        # Merge prev DSL with actions for the day
        # Update dsl to dsl + 1 for all channels that are not current channel. Reset dsl for current channel/content to 0


        # Build unique list of account/products because actuals sometimes have the same action more than one within a day
        selected_channels_for_day_df = selected_actions_for_day_df.groupby(by=['accountId', 'currentChannel']).aggregate(moveSelected=('moveSelected','count')).reset_index()
        selected_content_for_day_df = selected_actions_for_day_df.groupby(by=['accountId', 'currentContent']).aggregate(moveSelected=('moveSelected','count')).reset_index()

        # Update DSL for channels
        selected_channels_for_day_df = selected_channels_for_day_df.pivot(index=['accountId'],columns=['currentChannel'], values=['moveSelected'])
        selected_channels_for_day_df = selected_channels_for_day_df.droplevel(level=0,axis=1)
        selected_channels_for_day_df = selected_channels_for_day_df.reset_index()

        cols_to_select = ['accountId', 'NO_ACTION'] + channel_list
        cols_to_select = [col for col in cols_to_select if col in selected_channels_for_day_df.columns]
        selected_channels_for_day_df = selected_channels_for_day_df[cols_to_select]
        prev_dsl_df = pd.merge(prev_dsl_df, selected_channels_for_day_df, on=['accountId'], how='left')
        # prev_dsl_df = prev_dsl_df.drop(columns=['productId'], errors='ignore')
        #Note: Logic below should work even if selected_actions_for_day_df is not the full list but a sampled subset because, for all other account/products that are not in selected-moves, we want to increment by 1 anyway
        for channel in channel_list:
            prev_dsl_df[f"dsl_channel_{channel}"] = prev_dsl_df.apply(lambda row: 1 if row.get(f"{channel}", 0) > 0 else (row[f"dsl_channel_{channel}"] + 1), axis=1 )
        prev_dsl_df = prev_dsl_df.drop(columns=channel_list, errors='ignore')
        if 'NO_ACTION' in prev_dsl_df.columns:
            prev_dsl_df = prev_dsl_df.drop(columns=['NO_ACTION']) # Remove NO_ACTION as well from prev_dsl_df

        # Update DSL for content
        selected_content_for_day_df = selected_content_for_day_df.pivot(index=['accountId'],columns=['currentContent'], values=['moveSelected'])
        selected_content_for_day_df = selected_content_for_day_df.droplevel(level=0,axis=1)
        selected_content_for_day_df = selected_content_for_day_df.reset_index()

        prev_dsl_df = pd.merge(prev_dsl_df, selected_content_for_day_df, on=['accountId'], how='left')
        # prev_dsl_df = prev_dsl_df.drop(columns=['productId'], errors='ignore')
        #Note: Logic below should work even if selected_actions_for_day_df is not the full list but a sampled subset because, for all other account/products that are not in selected-moves, we want to increment by 1 anyway
        for content in content_list:
            prev_dsl_df[f"dsl_content_{content}"] = prev_dsl_df.apply(lambda row: 1 if row.get(f"{content}", 0) >= 1 else (row[f"dsl_content_{content}"] + 1), axis=1 )
        prev_dsl_df = prev_dsl_df.drop(columns=content_list, errors='ignore')
        if 'NA' in prev_dsl_df.columns:
            prev_dsl_df = prev_dsl_df.drop(columns=['NA']) # Remove NA as well from prev_dsl_df

        #self.printSummary("prev_dsl after dsl update for day", prev_dsl_df)

        data.set_dataframe("prev_dsl", prev_dsl_df)

    def build_training_data(self):
        # For each date:     
        #     a. Find the actual moves for the day
        #     b. Build the simulated moves (Account + Product * 4 Channels * x Content) + NO_ACTION
        #     c. Sample the data for training (stratified sampling by channel/content etc.)
        #     d. Compute days-since-last (DSL) channel/content for all the moves
        #     e. Check Channel-cool-off and Content-cool-off
        #     f. Build state variables for deep-learning-model 
        #     g. Compute actual reward (target variable for training)
        #     h. Invoke the DRL Executor to find top recommended action for each HCP and 
        #     mark those actions with moveSelected = 1
        #     i. Update DSL for next day based on moveSelected
        #     j. Collect this data for all dates in the simulation period
        # 

        data = Data.get_instance()
        #TODO: Remove hard-coding below
        current_date = data.get_param("history_start_date", None)
        history_end_date = data.get_param("history_end_date", None)
        #current_date = pd.Timestamp(datetime.strptime("2023-03-02", '%Y-%m-%d').date())
        #history_end_date = pd.Timestamp(datetime.strptime("2023-03-03", '%Y-%m-%d').date())
        delta = timedelta(days=1)

            #str_current_date = current_date.strftime('%Y-%m-%d')
            #str_current_month = current_month.strftime('%Y-%m-%d')
        
        iteration_training_df = None
        
        # iterate over range of dates
        while (current_date < history_end_date):
            current_month = pd.Timestamp(current_date.replace(day=1))
            print(f"Building data for date {current_date}")
            actuals_for_day_df, simulated_for_day_df = self.build_actions_for_day(current_date, current_month) # Build actions from actuals and from simulated data
            #self.printSummary("actuals_for_day_df", actuals_for_day_df)
            #self.printSummary("simulated_for_day_df", simulated_for_day_df)
            actions_for_day_df = self.prepare_actions_for_day(current_date, actuals_for_day_df, simulated_for_day_df) # Sample data, concat actuals/simulated, attach dsl columns
            if not actions_for_day_df.empty: #TODO: How to update DSL in this case?
                actions_for_day_df = self.create_network_input_variables(actions_for_day_df) # Build state
                selected_actions_for_day_df, actions_for_day_df = self.pick_best_move(actions_for_day_df) # Invoke model execution to predict best-move for the simulated actions and mark all actuals with move_selected=1
                self.update_dsl_for_day(current_date, selected_actions_for_day_df) # Compute new DSL based on previous's days DSL and selected moves
                if iteration_training_df is None:
                    iteration_training_df = actions_for_day_df
                else:
                    iteration_training_df = iteration_training_df.append(actions_for_day_df)
                # Append to final learning-input
            else:
                print(f"Skipping the date as no actions found: {current_date}")
                self.update_dsl_for_day(None, None)
            current_date += delta
        # Now, ready to call learner will data for one-iteration
        data.set_dataframe("training_df", iteration_training_df)
        #iteration_training_df.to_csv("/tmp/training_data.csv")

    def create_network_input_variables(self, input_pdf):
        """
        This method creates 50ish variables from the input provided.
        """
        logging.info("Started creating network input variables...")
        data = Data.get_instance()
        channel_cool_off_pdf = data.get_dataframe("channel_cooloff")
        # content_cool_off_pdf = data.get_dataframe("content_cool_off")

        cool_off_processor = CoolOff_Processor()
        # Using channel cool-off matrix figure out which channels are allowed
        input_pdf = cool_off_processor.generate_varying_allow_channel_columns(input_pdf, channel_cool_off_pdf)

        # Using content cool-off matrix determine which contents are allowed
        # input_pdf = cool_off_processor.generate_allow_content_columns(input_pdf, content_cool_off_pdf)

        # Explode the variables with 1-hot encoding
        cols_to_encode = ['hcpSegment', 'currentChannel', 'productId']

        additional_feature_cols = data.get_param('additional_feature_cols', [])
        cols_to_encode.extend(additional_feature_cols)

        # Add audiences columns
        audience_cols = data.get_param('audience_cols', [])
        cols_to_encode.extend(audience_cols)

        # Remove duplicates from cols_to_encode
        cols_to_encode = list(set(cols_to_encode))

        interactions_pdf = data.get_dataframe("interactions_raw")


        if Constants.EXECUTION_MODE:
            ap_attributes_pdf = (interactions_pdf.sort_values(by='activityDate', ascending=False).
                                 drop_duplicates(subset=['accountId', 'productId'], keep='first'))

        # All columns that are in cols_to_encode and are not in input_pdf will be added to input_pdf
            merge_cols = ['accountId', 'productId']
        else:
            ap_attributes_pdf = interactions_pdf
            ap_attributes_pdf['activityYearMonth'] = pd.to_datetime(ap_attributes_pdf['activityDate']).dt.normalize() - pd.offsets.MonthBegin(1)
            ap_attributes_pdf = (interactions_pdf.sort_values(by='activityYearMonth', ascending=False).
                                 drop_duplicates(subset=['accountId', 'productId', 'activityYearMonth'], keep='first'))
            merge_cols = ['accountId', 'productId', 'activityYearMonth']

        interaction_cols = [col for col in cols_to_encode if
                            (col in ap_attributes_pdf.columns) and (col not in input_pdf.columns)]
        interaction_cols.extend(merge_cols)

        input_pdf = pd.merge(input_pdf, ap_attributes_pdf[interaction_cols], on=merge_cols, how='left')

        # Remove any columns that are not in input_pdf
        cols_to_encode = [col for col in cols_to_encode if col in input_pdf.columns]

        input_encoded_pdf = pd.get_dummies(input_pdf[cols_to_encode], columns=cols_to_encode,
                                           prefix=cols_to_encode, prefix_sep='_', dummy_na=True)
        # Remove .0 from the column names
        '''
        Required because of the bug in pandas which is converting int columns to float dummy values
        Link- https://github.com/pandas-dev/pandas/issues/20693
        '''
        input_encoded_pdf.rename(lambda col: col.replace('.0', ''), axis='columns', inplace=True)

        input_pdf = pd.concat([input_pdf, input_encoded_pdf], axis=1)

        if not Constants.EXECUTION_MODE:
            product_group_mapping_pdf = data.get_dataframe("product_group_mapping")
            product_group_mapping_pdf = product_group_mapping_pdf[['productId', 'parentProductId']]
            input_pdf = pd.merge(input_pdf, product_group_mapping_pdf, on=['productId'], how='left')
            input_pdf['parentProductId'] = input_pdf['parentProductId'].fillna(input_pdf['productId'])

        # Merge with state model output
        if Constants.TEST_MODE:
            state_model_output_pdf = data.get_dataframe("state_model_output")
            input_pdf = pd.merge(input_pdf, state_model_output_pdf, on=['accountId', 'productId'], how='left')
        else:
            cp_predictions_pdf = data.get_dataframe("cp_predictions")
            st_predictions_pdf = data.get_dataframe("st_predictions")
            #input_pdf = pd.merge(input_pdf, cp_predictions_pdf, on=['accountId'], how='left')
            input_pdf = pd.merge(input_pdf, cp_predictions_pdf, on=['accountId', 'currentChannel'], how='left')
            input_pdf = pd.merge(input_pdf, st_predictions_pdf, left_on=['accountId', 'parentProductId'],
                                 right_on=['accountId', 'productId'], how='left', suffixes=('', '_y'))
            input_pdf = input_pdf.drop(['productId_y'], axis=1)

        # Add reward component cols used as features
        input_pdf, _ = RewardCalculator.add_reward_component_cols(input_pdf)

        # Add 0 for the columns in not in input_pdf
        network_input_cols = data.get_param('network_input_cols')
        absent_cols = [col for col in network_input_cols if col not in input_pdf.columns]

        if absent_cols:
            logging.info(f"Creating zero valued cols for absent in input_pdf: {absent_cols}" )
            # Create a DataFrame with zeros for absent columns
            absent_cols_df = pd.DataFrame(0, index=input_pdf.index, columns=absent_cols)

            # Concatenate the original DataFrame with the absent columns DataFrame
            input_pdf = pd.concat([input_pdf, absent_cols_df], axis=1)
        else:
            logging.info("No absent columns to add.")


        # Fill na to 0 for network input columns
        input_pdf[network_input_cols] = input_pdf[network_input_cols].fillna(0)

        logging.info(f"Shape of Input data with network input columns: {input_pdf.shape}")


        logging.info("Completed creating network input variables.")

        return input_pdf

    def get_predictions_for_multi_day_execution(self):
        data = Data.get_instance()
        start_date = data.get_param("startdate")
        end_date = data.get_param("enddate")

        # Global predictions
        global_intermediate_pdf = pd.DataFrame()
        global_predictions = pd.DataFrame()
        current_date = start_date
        while current_date <= end_date:

            print(f"Generating predictions for date: {current_date}")

            prev_dsl_df = data.get_dataframe("prev_dsl")
            ap_cross_channel_df = data.get_dataframe("ap_cross_channel")

            ap_cross_channel_df['activityDate'] = current_date
            ap_cross_channel_df['activityYearMonth'] = pd.to_datetime(ap_cross_channel_df['activityDate']).dt.normalize() - pd.offsets.MonthBegin(1)

            print(f"ap_cross_channel shape:" + str(ap_cross_channel_df.shape))

            # Merge the dsl columns to the final data-frame
            execution_input_df = pd.merge(ap_cross_channel_df, prev_dsl_df, on=['accountId'], how="inner")
            input_df = self.create_network_input_variables(execution_input_df)

            print(f"Execution input shape:" + str(input_df.shape))

            # call predictor to get predictions
            network_input_cols = data.get_param('network_input_cols')
            print("Input to predictor:")
            print(input_df[network_input_cols])
            predictions = Predictor.predict(input_df[network_input_cols])
            # print("Raw predictions:")
            # print(predictions)
            predictions_pdf = pd.concat([input_df[['accountId', 'accountUid', 'productId', 'productUid', 'currentChannel']],
                                         pd.DataFrame(predictions, columns=["reward"])], axis=1)

            intermediate_pdf = pd.concat([input_df, pd.DataFrame(predictions, columns=["reward"])], axis=1)

            # apply global constraints to predictions
            top_candidates = int(data.get_param("GLOBAL_CONSTRAINTS_TOP_CANDIDATES", 1000))
            reward_threshold = float(data.get_param("GLOBAL_CONSTRAINTS_REWARD_THRESHOLD", 1.0))
            print(f"Applying global constraints: Top candidates: {top_candidates}, reward threshold: {reward_threshold}...")

            filtered_predictions = predictions_pdf[predictions_pdf.reward >= reward_threshold]

            if filtered_predictions.empty == False:

                # Select 1 candidate with highest reward per accountId and productId
                filtered_predictions = filtered_predictions.groupby(['accountId', 'productId']).apply(lambda x: x.nlargest(1, 'reward')).reset_index(drop=True)
                filtered_predictions = filtered_predictions.nlargest(top_candidates, "reward")

                component_used = []
                if data.get_param("st_component", False):
                    component_used.append("segment-trend-prob")
                if data.get_param("cp_rem_component", False):
                    component_used.append("cp-rem-prob")
                filtered_predictions['component_used'] = ','.join(component_used)

                data.set_dataframe("predictions_pdf", filtered_predictions)

                # Add current date to predictions
                filtered_predictions['action_date'] = current_date.strftime('%Y-%m-%d')

                # Append prediction rows  to global predictions
                global_predictions = global_predictions.append(filtered_predictions)

                idx = filtered_predictions.groupby(by=['accountId'])['reward'].idxmax()
                # Select random x% of idx
                fraction_of_accounts_engaged_per_day = float(data.get_param("REP_ENGAGEMENT_RATE", 0.20))
                idx = idx.sample(frac=fraction_of_accounts_engaged_per_day, replace=False, random_state=1)
                filtered_predictions.loc[idx, 'moveSelected'] = 1
                filtered_predictions['currentContent'] = 'NA'

                merge_cols = ['accountId', 'accountUid', 'productId', 'productUid', 'currentChannel']
                use_cols = merge_cols + ['component_used', 'action_date', 'moveSelected', 'currentContent']
                intermediate_pdf = pd.merge(intermediate_pdf, filtered_predictions[use_cols], on=merge_cols, how='left')

                # Log number rof items selected out of total
                print(f"Number of action marked acted on out of total: {len(idx)}/{len(filtered_predictions)}")

                # Update prev_dsl for next day
                self.update_dsl_for_day(current_date, filtered_predictions[filtered_predictions['moveSelected'] == 1])


            else:
                print(f"No actions found for date: {current_date} after applying threshold: {reward_threshold}. Rewards are:")
                print(predictions_pdf["reward"].describe())
                self.update_dsl_for_day(None, None)

            global_intermediate_pdf = global_intermediate_pdf.append(intermediate_pdf)


            # increment current_date
            current_date += timedelta(days=1)

        if global_predictions.empty == False:
            print(f"Number of unique accounts in predictions: {len(pd.unique(global_predictions['accountId']))}")
            print(f"Number of actions: {len(global_predictions)}")
            print(f"Number of unique accounts in intermediate: {len(pd.unique(global_intermediate_pdf['accountId']))}")
            print(f"Number of actions in intermediate: {len(global_intermediate_pdf)}")
        else:
            print(f"********* No actions found that passed the threshold check")

        data.set_dataframe("predictions_pdf", global_predictions)
        data.set_dataframe('intermediate_pdf', global_intermediate_pdf)

    def print_actions_summary(self, df):
        print("Number of actions: " + str(len(df)))
        print("Number of unique accounts: " + str(len(pd.unique(df['accountId']))))
        print("Number of actions by month and isActual:")
        print(df.groupby(['activityYearMonth', 'isActual']).size().reset_index(name='count'))
        print("Number of unique accounts by month and isActual:")
        print(df.groupby(['activityYearMonth', 'isActual'])['accountId'].nunique().reset_index(name='count'))
    
    def execute(self):

        # 1. Find the simulation months 
        # 2. Find eligible Accounts/Products:
        # (at least x transactions in the last y period)
        # 3. Find the actual vs. simulated accounts split for each month
        # (Pick top x% of Accounts from actuals and 1-x% from simulated events based on simulation-rate reference data)
        # 4. Initialize Days-since-last-channel and Days-since-last-content
        # (Find last action per channel for all Account/Products prior to the simulation period
        # (Days since last VISIT, Days since last SEND, etc.)
        # 5. Build training data:
        # 6. Invoke Neural-network learner and repeat for n iterations

        data = Data.get_instance()

        if Constants.EXECUTION_MODE:
            self.initialize_prev_dsl()
            self.get_predictions_for_multi_day_execution()
        else:
            pd.options.mode.chained_assignment = None  # default='warn'
            self.build_simulation_months()
            self.build_actual_vs_sim_accounts()
            self.initialize_prev_dsl()

            global_intermediate_pdf = pd.DataFrame()
            iteration_count = data.get_param('learning_iterations', 5)
            reward_calculator = RewardCalculator()
            for i in range(iteration_count):
                logging.info(f"Starting iteration {i+1}/{iteration_count}")
                self.build_training_data()
                training_pdf = data.get_dataframe("training_df")
                print("Before downsampling:")
                self.print_actions_summary(training_pdf)

                if training_pdf is None or training_pdf.empty:
                    logging.warning("No training data found. Skipping iteration.")
                    continue

                CHANNEL_SAMPLING_MULTIPLIER = data.get_param('CHANNEL_SAMPLING_MULTIPLIER', 0)
                if CHANNEL_SAMPLING_MULTIPLIER > 0:
                    # Make sure that in the training data no hcpSegment has more than 5 times the number of samples as the smallest hcpSegment
                    channel_count_df = training_pdf.groupby(['currentChannel']).size().reset_index(name='count')
                    min_channel_count = channel_count_df['count'].min()
                    max_channel_count = channel_count_df['count'].max()
                    CHANNEL_SAMPLING_MULTIPLIER = data.get_param('CHANNEL_SAMPLING_MULTIPLIER', 10)
                    allowed_max_channel_count = CHANNEL_SAMPLING_MULTIPLIER * min_channel_count
                    if max_channel_count > allowed_max_channel_count:
                        print(f"Down sampling training data to make sure no channel has more than {CHANNEL_SAMPLING_MULTIPLIER} times the number of samples as the smallest channel")
                        # Down sample to min(length of the segment or 5 * min_segment_count)
                        training_pdf = training_pdf.groupby(['currentChannel']).apply(
                            lambda x: x.sample(min(len(x), allowed_max_channel_count), replace=False,
                                            random_state=1)).reset_index(drop=True)

                SEGMENT_SAMPLING_MULTIPLIER = data.get_param('SEGMENT_SAMPLING_MULTIPLIER', 0)
                if SEGMENT_SAMPLING_MULTIPLIER > 0:
                    # Make sure that in the training data no hcpSegment has more than 5 times the number of samples as the smallest hcpSegment
                    segment_count_df = training_pdf.groupby(['hcpSegment']).size().reset_index(name='count')
                    min_segment_count = segment_count_df['count'].min()
                    max_segment_count = segment_count_df['count'].max()
                    SEGMENT_SAMPLING_MULTIPLIER = data.get_param('SEGMENT_SAMPLING_MULTIPLIER', 10)
                    allowed_max_segment_count = SEGMENT_SAMPLING_MULTIPLIER * min_segment_count
                    if max_segment_count > allowed_max_segment_count:
                        print(f"Down sampling training data to make sure no hcpSegment has more than {allowed_max_segment_count} samples")
                        # Down sample to min(length of the segment or 5 * min_segment_count)
                        training_pdf = training_pdf.groupby(['hcpSegment']).apply(
                            lambda x: x.sample(min(len(x), allowed_max_segment_count), replace=False,
                                            random_state=1)).reset_index(drop=True)
                                            
                if data.get_param('SEGMENT_SAMPLING_LIMIT_LARGEST', 0) > 0:
                    # Make sure that in the training data no hcpSegment has more than 5 times the number of samples as the smallest hcpSegment
                    segment_count_df = training_pdf.groupby(['hcpSegment']).size().reset_index(name='count').sort_values(by='count', ascending=False)
                    min_segment_count = segment_count_df['count'].min()
                    max_segment_count = segment_count_df['count'].max()
                    if segment_count_df.shape[0] > 1:
                        second_largest_count = segment_count_df.iloc[1, :]['count']
                        print(f"Down sampling training data to make sure largest hcpSegment has no more than {second_largest_count} samples")
                        training_pdf = training_pdf.groupby(['hcpSegment']).apply(
                            lambda x: x.sample(min(len(x), second_largest_count), replace=False,
                                            random_state=1)).reset_index(drop=True)

                training_pdf = reward_calculator.calculate_reward(training_pdf)

                # Down sample training data where reward is negative to make it equal in numbers
                down_sampled_training_pdf = training_pdf
                if data.get_param('DOWNSAMPLE_NEGATIVE_REWARD', True):
                    positive_reward_count = training_pdf[training_pdf['reward'] >= 0].shape[0]
                    negative_reward_count = training_pdf[training_pdf['reward'] < 0].shape[0]
                    NEGATIVE_TRAIN_SAMPLES_MULTIPLIER = data.get_param('NEGATIVE_TRAIN_SAMPLES_MULTIPLIER', 2)
                    MAX_NEGATIVE_REWARD_COUNT = max(NEGATIVE_TRAIN_SAMPLES_MULTIPLIER * positive_reward_count, min(negative_reward_count, 1000))
                    if negative_reward_count > MAX_NEGATIVE_REWARD_COUNT:
                        print(f"Down sampling negative reward from {negative_reward_count} to {MAX_NEGATIVE_REWARD_COUNT}")
                        negative_reward_df = training_pdf[training_pdf['reward'] < 0].sample(n=MAX_NEGATIVE_REWARD_COUNT, replace=False, random_state=1)
                        positive_reward_df = training_pdf[training_pdf['reward'] >= 0]
                        down_sampled_training_pdf = pd.concat([positive_reward_df, negative_reward_df])

                        # Training Pdf mark which rows exists in down_sampled_training_pdf
                        training_pdf['used_in_training'] = training_pdf.index.isin(down_sampled_training_pdf.index)

                if i == 0 or i == iteration_count - 1:
                    training_pdf['iteration'] = i+1
                    global_intermediate_pdf = global_intermediate_pdf.append(training_pdf)

                training_pdf = down_sampled_training_pdf
                print("After downsampling:")
                self.print_actions_summary(training_pdf)

                network_input_cols = data.get_param('network_input_cols')
                model = Learner.train_model(X=training_pdf[network_input_cols], y=training_pdf['reward'])
                data.set_model("reward_predictor", model)

            final_predictions = training_pdf.copy()
            final_predictions['predicted_reward'] = Predictor.predict(final_predictions[network_input_cols])
            data.set_dataframe("intermediate_pdf", global_intermediate_pdf)
            # final_predictions.to_csv("/tmp/final_predictions.csv")
            # Corr between reward and predicted reward
            logging.debug("Correlation between reward and predicted reward:" + str(final_predictions['reward'].corr(final_predictions['predicted_reward'])))

    

