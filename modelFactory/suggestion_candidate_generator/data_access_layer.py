import os
import pickle
import tempfile

import botocore
import pandas as pd
import s3fs
import sklearn2pmml

from abstract_model_factory.abstract_data_access_layer import AbstractDataAccessLayer
from suggestion_candidate_generator import data
from suggestion_candidate_generator.data import Data
from suggestion_candidate_generator.constants import Constants
from snowflake.connector.pandas_tools import write_pandas
from datetime import date, datetime
import boto3
import snowflake
from sklearn_pmml_model.ensemble import PMMLGradientBoostingClassifier
import paramiko
from paramiko import SSHClient
from sshtunnel import SSHTunnelForwarder
import pymysql
import tempfile
import logging
import keras

def get_s3_client():
    ACCESS_KEY = Constants.AWS_ACCESS_KEY_ID
    SECRET_KEY = Constants.AWS_SECRET_ACCESS_KEY
    SESSION_TOKEN = Constants.AWS_SESSION_TOKEN

    if ACCESS_KEY != "":
        s3 = boto3.client('s3',
                          aws_access_key_id=ACCESS_KEY,
                          aws_secret_access_key=SECRET_KEY,
                          aws_session_token=SESSION_TOKEN)

    else:
        s3 = boto3.client('s3')

    return s3

class DataAccessLayer(AbstractDataAccessLayer):

    def write_normalizer_to_s3(self, normalizer, s3_path):
        s3_bucket = s3_path.split('/')[2]
        normalizer_path = '/'.join(s3_path.split('/')[3:])
        target_name = s3_path.split('/')[-1]

        s3 = self.get_s3_client()

        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/{target_name}"
            pickle.dump(normalizer, open(temp_path, 'wb'))
            s3.upload_file(temp_path, s3_bucket, normalizer_path)

        print("Uploaded normalizer to : " + s3_path)

    @staticmethod
    def get_snowflake_cursor():

        ctx = snowflake.connector.connect(
            user=Constants.sw_user,
            password=Constants.sw_password,
            account=Constants.sw_account,
            warehouse=Constants.sw_warehouse,
            database=Constants.sw_database,
            schema=Constants.sw_schema,
            role=Constants.sw_role)

        # Create a cursor object.
        cur = ctx.cursor()
        return cur, ctx

    @staticmethod
    def read_snow_flake_pandas(query):
        """
        @param spark:
        @param query:
        @return:  Spark dataframe
        @rtype:
        """

        cur, ctx = DataAccessLayer.get_snowflake_cursor()
        cur.execute(query)
        pdf = cur.fetch_pandas_all()
        return pdf

    @staticmethod
    def write_snow_flake_pandas(pdf, table, mode_u="append"):
        try:
            cur, ctx = DataAccessLayer.get_snowflake_cursor()
            write_pandas(ctx, pdf, table)
            print(f"Done writing snowflake table {table}")
        except Exception as e:
            print('Failed with error: ' + str(e))
            raise e

    @staticmethod
    def execute_snowflake_update_query(query):
        try:
            cur, ctx = DataAccessLayer.get_snowflake_cursor()
            cur.execute(query)
            print(f"Done executing snowflake query {query}")
        except Exception as e:
            print('Failed with error: ' + str(e))
            raise e

    @staticmethod
    def getMySqlConnection(db):
        conn = None
        if Constants.USE_PARAMIKO:
            logging.info ("Using mysql ssh tunnel forwarder using" + Constants.ssh_host + " and " + str(Constants.ssh_port) + "for user:" + Constants.ssh_user)
            mypkey = paramiko.RSAKey.from_private_key_file(Constants.ssh_private_key)
            with SSHTunnelForwarder((Constants.ssh_host, Constants.ssh_port),
                                    ssh_username=Constants.ssh_user, ssh_pkey=mypkey,
                                    remote_bind_address=(Constants.rds_hostname,
                                                        Constants.rds_port)) as tunnel:
                conn = pymysql.connect(host='127.0.0.1', user=Constants.rds_username,
                                    passwd=Constants.rds_password,
                                    port=tunnel.local_bind_port, database=db)

        if Constants.LOCAL_MODE:
            logging.info ("Using mysql ssh tunnel forwarder using localhost")
            conn = pymysql.connect(host='127.0.0.1', user=Constants.rds_username,
                                passwd=Constants.rds_password,
                                port=3337, database=db)
            
        else:
            print (f"Using mysql direct connection to {Constants.rds_hostname} on port:{Constants.rds_port}")
            conn = pymysql.connect(host=Constants.rds_hostname, user=Constants.rds_username,
                                passwd=Constants.rds_password,
                                port=Constants.rds_port, database=db)
        return conn


    @staticmethod
    def runMySqlQuery(
            query,
            db=None,
    ):
        conn = DataAccessLayer.getMySqlConnection(db)
        try:
            df = pd.read_sql_query(query, conn)
            print('Read data for sql:' + query)
            conn.close()
            return df
        except Exception as e:
            logging.error('Error while executing mysql query:' + str(e))
            raise

    @staticmethod
    def executeMySql(
            sql, db=None
    ):
        conn = DataAccessLayer.getMySqlConnection(db)
        try:
            cur = conn.cursor()
            cur.execute(sql)
            conn.commit()
            conn.close()
        except Exception as e:
            logging.error('Error while executing mysql sql:' + str(e))
            raise

    @staticmethod
    def read_model_from_s3(s3_path):
        """
        Method to read a python object from S3
        @param s3_path: S3 path
        @return: Python object
        """

        ACCESS_KEY = Constants.AWS_ACCESS_KEY_ID
        SECRET_KEY = Constants.AWS_SECRET_ACCESS_KEY
        SESSION_TOKEN = Constants.AWS_SESSION_TOKEN

        s3_bucket = s3_path.split('/')[2]
        s3_path = '/'.join(s3_path.split('/')[3:])

        if ACCESS_KEY != "":
            s3 = boto3.client('s3',
            aws_access_key_id=ACCESS_KEY,
            aws_secret_access_key=SECRET_KEY,
            aws_session_token=SESSION_TOKEN)
        else:
            s3 = boto3.client('s3')

        s3.download_file(s3_bucket, s3_path, 'channel_model.pmml')
        model = PMMLGradientBoostingClassifier(pmml='channel_model.pmml')

        os.remove('channel_model.pmml')
        return model

    @staticmethod
    def read_model_pkl_from_s3(s3_path):
        """
        Method to read a python object from S3
        @param s3_path: S3 path
        @return: Python object
        """
        try:
            ACCESS_KEY = Constants.AWS_ACCESS_KEY_ID
            SECRET_KEY = Constants.AWS_SECRET_ACCESS_KEY
            SESSION_TOKEN = Constants.AWS_SESSION_TOKEN

            s3_bucket = s3_path.split('/')[2]
            s3_path = '/'.join(s3_path.split('/')[3:])

            if ACCESS_KEY != "":
                s3 = boto3.resource('s3',
                aws_access_key_id=ACCESS_KEY,
                aws_secret_access_key=SECRET_KEY,
                aws_session_token=SESSION_TOKEN)
            else:
                s3 = boto3.resource('s3')

            content = s3.Object(s3_bucket, s3_path).get()['Body'].read()
            return pickle.loads(content)
        except Exception as e:
            print('Failed with error: ' + str(e))
            print(f"Unable to read model from S3 {s3_path}")
            return None


    # @staticmethod
    # def write_model_to_s3(model, model_dir, target_name):
    #     """
    #     Writes a model to an S3 bucket.
    #
    #     Parameters:
    #         model (object): The model object to be written.
    #         model_dir (str): The directory where the model will be saved.
    #         target_name (str): The name of the model
    #
    #     Returns:
    #         None
    #     """
    #     s3 = get_s3_client()
    #
    #     s3_bucket = model_dir.split('/')[2]
    #     model_path = '/'.join(model_dir.split('/')[3:]) + target_name
    #
    #     with tempfile.TemporaryDirectory() as tempdir:
    #         temp_path = f"{tempdir}/{target_name}"
    #         model.save(temp_path)
    #         s3.upload_file(temp_path, s3_bucket, model_path)
    #
    #     print("Uploaded model to : " + model_path)

    @staticmethod
    def write_latest_csv(csv_s3_path, latest_pdf):

        s3 = get_s3_client()

        s3_bucket = csv_s3_path.split('/')[2]
        csv_path = '/'.join(csv_s3_path.split('/')[3:])

        csv_string = latest_pdf.to_csv(index=False)
        print("Writing csv: " + csv_string)
        s3.put_object(Bucket=s3_bucket, Key=csv_path, Body=csv_string)

        print("Uploaded latest.csv to : " + csv_s3_path)

    @staticmethod
    def write_predictions_to_s3(predictions, s3_path, target_name):

        s3_bucket = s3_path.split('/')[2]
        predictions_path = '/'.join(s3_path.split('/')[3:]) + target_name

        s3 = get_s3_client()

        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/{target_name}"
            predictions.to_parquet(temp_path, index=False)
            s3.upload_file(temp_path, s3_bucket, predictions_path)

        print("Uploaded predictions to : " + s3_path)

    @staticmethod
    def read_latest_csv(csv_s3_path):

        s3 = get_s3_client()

        s3_bucket = csv_s3_path.split('/')[2]
        csv_path = '/'.join(csv_s3_path.split('/')[3:])

        print("Reading latest.csv from : " + csv_s3_path)

        response = s3.get_object(Bucket=s3_bucket, Key=csv_path)
        status = response.get("ResponseMetadata", {}).get("HTTPStatusCode")
        latest_pdf = None
        if status == 200:
            #print(f"Successful S3 get_object response. Status - {status}")
            latest_pdf = pd.read_csv(response.get("Body"))
            print ("Latest source files:")
            print(latest_pdf)
        else:
            print(f"Unsuccessful S3 get_object for latest csv. Status - {status}")

        # latest_pdf = pd.read_csv(
        #     f"s3://{s3_bucket}/{csv_path}",
        #     storage_options={
        #         "key": AWS_ACCESS_KEY_ID,
        #         "secret": AWS_SECRET_ACCESS_KEY,
        #         "token": AWS_SESSION_TOKEN,
        #     }
        # )

        return latest_pdf

    @staticmethod
    def read_s3_parquet(s3_path):

        s3_bucket = s3_path.split('/')[2]
        parquet_path = '/'.join(s3_path.split('/')[3:])

        s3 = get_s3_client()
        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/output.parquet"
            try:
                s3.download_file(s3_bucket,parquet_path,temp_path)
            except botocore.exceptions.ClientError as e:
                if e.response['Error']['Code'] == "404":
                    logging.warning(f"The file object does not exist in S3 at path: {s3_path}")
                    return None
                elif e.response['Error']['Code'] == 403:
                    logging.warning(f"The file object not authorized to access in S3 at path: {s3_path}")
                    return None
                else:
                    # Something else has gone wrong.
                    raise

            parquet_df = pd.read_parquet(temp_path, engine='pyarrow')
            return parquet_df


    @staticmethod
    def load_keras_model_from_s3(model_s3_path):

        s3 = get_s3_client()

        s3_bucket = model_s3_path.split('/')[2]
        model_path = '/'.join(model_s3_path.split('/')[3:])

        print("Reading keras model from : " + model_s3_path)
        model = None
        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/tmp.keras"
            s3.download_file(s3_bucket, model_path, temp_path)
            model = keras.models.load_model(temp_path)

        return model


