import logging

from suggestion_candidate_generator.data import Data
import numpy as np
import pandas as pd

CHANNEL_COOLOFF_BY_SEGMENT = False
CHANNEL_COOLOFF_BY_BRAND = False

CONTENT_COOLOFF_BY_SEGMENT = False
CONTENT_COOLOFF_BY_BRAND = False

class CoolOff_Processor:

    def __init__(self) -> None:
        self.channel_cooloff = None
        self.content_cooloff = None

        self.prev_DSL_interaction = None
        self.prev_DSL_interaction_date = None

        self.prev_DSL_content = None
        self.prev_DSL_content_date = None

    def readCoofOff(self):
        pass

    # Data frame should have following columns:
    # account_id, product_id, action, content

    # Internally maintained previous values:
    # PreviousChannel: account_id, product_id, prev_action, action_date
    # PreviousContent: account_id, product_id, prev_action, prev_content, action_date

    # Expects following columns in the data-frame
    # account_id, product_id, action, content, action_date, move_selected
    # move_selected to know the subset of rows to use to compute days-since-last-interaction
    def applyCoolOff(self, df, enforce=False):

        # Maintain a previousAction DF with  lastAction and lastContent for every account/product
        # Join current data with that previousAction to compute new DSL action and content
        
        return df

    def savePrevious(self, df):
        self.savePreviousChannel(df)
        self.savePreviousContent(df)

    def savePreviousChannel(self, df):
        # Concatenate prev_DSL_interaction with new df
        # Group by account_id, product_id, action and get max_action_date
        pass

    def savePreviousContent(self, df):
        # Similar logic as for prev_DSL_interaction. But group-by action and content
        pass

    def generate_allow_channel_columns(self, input_pdf, channel_cool_off_pdf):
        """

        """
        logging.info("Generating allow channel columns..")
        data = Data.get_instance()
        channel_list = data.get_param('channel_list', ['SEND', 'VISIT', 'WEB_INTERACTIVE'])
        allow_list = ['allow_channel_' + chnl for chnl in channel_list]

        for channel in channel_list:
            
            # Subset channel_cool_off on nextChannel = {channel
            channel_cool_off_subset = channel_cool_off_pdf.loc[
                channel_cool_off_pdf['nextChannel'] == channel, ['currentChannel', 'days', 'hcpSegment']]
            channel_cool_off_subset = channel_cool_off_subset.pivot(values='days', columns=['currentChannel'],
                                                                    index='hcpSegment').reset_index()

            # Join input_pdf and channel_cool_off_subset on segment
            input_pdf = input_pdf.merge(channel_cool_off_subset, on='hcpSegment', how='left')

            # Subtract cols dsli_{channel} from {channel} in the dataframe
            for chnl in channel_list:
                input_pdf.loc[input_pdf.currentChannel == channel, f'{chnl}'] = input_pdf.loc[
                                                                                    input_pdf.currentChannel == channel, f'dsl_channel_{chnl}'] - \
                                                                                input_pdf.loc[
                                                                                    input_pdf.currentChannel == channel, chnl]

            input_pdf.loc[input_pdf.currentChannel == channel, f'allow_channel'] = (
                        input_pdf.loc[input_pdf.currentChannel == channel, channel_list] >= 0).all(axis=1)

            # drop columns
            input_pdf.drop(columns=channel_list, inplace=True)
            # input_pdf.loc[input_pdf.currentChannel==channel, 'allow_channel'] = input_pdf.loc[input_pdf.currentChannel==channel,'allow_channel_' + val]

        input_pdf.loc[input_pdf.currentChannel == 'NO_ACTION', 'allow_channel'] = True
        input_pdf['allow_channel'] = input_pdf['allow_channel'].fillna(False)
        input_pdf['allow_channel'] = input_pdf['allow_channel'].astype(int)
        # channels_used = input_pdf.currentChannel.unique()
        # channels_used = np.setdiff1d(channels_used, ['NO_ACTION'])
        # for val in channels_used:
        #    input_pdf.loc[input_pdf.currentChannel==val, 'allow_channel'] = input_pdf.loc[input_pdf.currentChannel==val,'allow_channel_' + val]

        # input_pdf.drop(columns=allow_list, inplace=True)

        logging.info("Generated allow channel columns.")
        return input_pdf

    def generate_allow_content_columns(self, input_pdf, content_cool_off_pdf):
        data = Data.get_instance()
        content_list = data.get_param('content_list', ['A', 'B', 'C'])

        for content in content_list:
            original_cols = input_pdf.columns
            # Subset channel_cool_off on nextChannel = {channel
            content_cool_off_subset = content_cool_off_pdf.loc[
                content_cool_off_pdf['nextContent'] == content, ['currentContent', 'days', 'segment']]

            # Join input_pdf and channel_cool_off_subset on segment
            input_pdf = input_pdf.merge(content_cool_off_subset, on='segment', how='left')
            input_pdf = input_pdf.pivot(values='days', columns=['currentContent'], index=original_cols).reset_index()

            # Subtract cols dsli_{channel} from {channel} in the dataframe
            for cont in content_list:
                input_pdf[f'{cont}'] = input_pdf[cont] - input_pdf[f'dsl_content_{cont}']

            input_pdf[f'allow_content_{content}'] = input_pdf[content_list].apply(lambda x: all(x >= 0), axis=1)

            # drop columns
            input_pdf.drop(columns=content_list, inplace=True)

        return input_pdf
    
    def generate_varying_allow_channel_columns(self, input_df, channel_cool_off_pdf):
        data = Data.get_instance()
        
        adjRangePct = data.get_param('ADJUSTMENT_RANGE_PCT', 0.25)
        penaltyStartPct = data.get_param('PENALTY_START_PCT', 0.5)
        channel_cool_off_pdf['adjRangePct'] = adjRangePct
        channel_cool_off_pdf['penaltyStartPct'] = penaltyStartPct

        # original_cols = input_df.columns.tolist()
        input_df_with_id = input_df.reset_index().rename(columns={'index': '_original_row_id'})

        dsl_cols = [col for col in input_df_with_id.columns if col.startswith('dsl_channel_')]
        id_vars = [col for col in input_df_with_id.columns if col not in dsl_cols]

        df_long = input_df_with_id.melt(
            id_vars=id_vars,
            value_vars=dsl_cols,
            var_name='prevChannelRaw',
            value_name='DSL'
        )
        df_long['prevChannel'] = df_long['prevChannelRaw'].str.replace('dsl_channel_', '')

        df_merged = pd.merge(
            df_long,
            channel_cool_off_pdf,
            left_on=['prevChannel', 'currentChannel'],
            right_on=['currentChannel', 'nextChannel'],
            how='left'
        )

        df_merged['windowHalfWidth'] = df_merged['days'] * df_merged['adjRangePct']
        df_merged['transitionStartDay'] = df_merged['days'] - df_merged['windowHalfWidth']
        df_merged['transitionEndDay'] = df_merged['days'] + df_merged['windowHalfWidth']

        df_merged['transitionWidth'] = df_merged['transitionEndDay'] - df_merged['transitionStartDay']

        df_merged['allowanceStart'] = 1 - df_merged['penaltyStartPct']

        cond_before_transition = df_merged['DSL'] < df_merged['transitionStartDay']
        cond_during_transition = (
            (df_merged['DSL'] >= df_merged['transitionStartDay']) &
            (df_merged['DSL'] <= df_merged['transitionEndDay']) &
            (df_merged['transitionWidth'] > 0) # Avoid division by zero
        )
        cond_after_transition = df_merged['DSL'] > df_merged['transitionEndDay']

        cond_zero_width_allowed = (df_merged['transitionWidth'] == 0) & (df_merged['DSL'] >= df_merged['transitionStartDay'])
        cond_zero_width_disallowed = (df_merged['transitionWidth'] == 0) & (df_merged['DSL'] < df_merged['transitionStartDay'])

        conditions = [
            cond_before_transition,
            cond_during_transition,
            cond_after_transition,
            cond_zero_width_allowed,    # Handle zero width case - allowed
            cond_zero_width_disallowed  # Handle zero width case - disallowed
        ]

        choice_allowance_0 = 0.0
        progress = (df_merged['DSL'] - df_merged['transitionStartDay']) / df_merged['transitionWidth']
        progress = progress.clip(0, 1) # Ensure progress is between 0 and 1
        choice_allowance_interpolated = df_merged['allowanceStart'] + (df_merged['penaltyStartPct'] * progress)

        choice_allowance_1 = 1.0

        choices = [
            choice_allowance_0,
            choice_allowance_interpolated,
            choice_allowance_1,
            choice_allowance_1,  # Zero width, allowed
            choice_allowance_0   # Zero width, disallowed
        ]

        df_merged['Allowance'] = np.select(conditions, choices, default=1.0)
        df_merged['Allowance'] = df_merged['Allowance'].clip(0, 1)

        idx_min_allowance = df_merged.groupby('_original_row_id')['Allowance'].idxmin()
    
        final_scores_per_row = df_merged.loc[idx_min_allowance].reset_index(drop=True) # drop current index from df_merged

        final_scores_to_merge = final_scores_per_row[[
            '_original_row_id', 'Allowance'
        ]].rename(columns={'Allowance': 'allow_channel'})

        output_df = pd.merge(input_df_with_id, final_scores_to_merge, on='_original_row_id', how='left')

        output_df = output_df.drop(columns=['_original_row_id'])
        output_df['allow_channel'] = output_df['allow_channel'].astype(float)

        return output_df
