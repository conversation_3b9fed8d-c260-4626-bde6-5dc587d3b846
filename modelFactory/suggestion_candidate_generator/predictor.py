import logging

from sklearn import preprocessing

from abstract_model_factory.abstract_predictor import AbstractPredictor
from suggestion_candidate_generator.data import Data
import pandas as pd

class Predictor(AbstractPredictor):
    def execute(self):
        pass

    @staticmethod
    def predict(input_data):
        assert isinstance(input_data, pd.DataFrame), "Input data is not a pandas dataframe"

        logging.info(f"Started predicting on input data of shape: {input_data.shape}")
        data = Data.get_instance()
        model = data.get_model('reward_predictor')

        prediction = model.predict(input_data, verbose=2)

        logging.info("Completed predicting.")

        return prediction

    def qa_module(self):
        pass
