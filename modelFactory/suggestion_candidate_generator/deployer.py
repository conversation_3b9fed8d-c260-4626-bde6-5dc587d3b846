import logging
from datetime import datetime
from datetime import date

import pandas as pd

from abstract_model_factory.abstract_deployer import AbstractDeployer
from suggestion_candidate_generator.constants import Constants
from suggestion_candidate_generator.data import Data
from suggestion_candidate_generator.data_access_layer import DataAccessLayer
from suggestion_candidate_generator.scc_integrator import SCCIntegrator

from sqlalchemy import create_engine

class Deployer(AbstractDeployer):

    def printSummary(self, name, df, rowCount=10):

        print(f"Data frame: {name}:" + str(df.shape))
        print("*******************************")
        if Constants.DEBUG_MODE:
            df.info()
            print (df.head(rowCount))

    def execute(self):
        # Learning mode
        if Constants.LEARNING_MODE:
            self.save_model()

        # Execution mode
        if Constants.EXECUTION_MODE:
            self.write_predictions()

        self.write_intermediate_data()

        # Post-proc modes
        data = Data.get_instance()
        # Create action candidates first. They are required when touchpoints are created ???
        if "predictions" in data.get_param("postProcMode", "none") or "candidates" in data.get_param("postProcMode", "none"):
            self.upload_action_candidates()
        if "touchpoints" in data.get_param("postProcMode", "none"):
            self.upload_touchpoints(data.get_param("model_channel_list", []))

        self.write_drl_run()

    def qa_module(self):
        pass

    def _create_latest_pdf(self):
        data = Data.get_instance()
        model_s3_path = data.get_param("model_s3_path")

        # Add the latest model path
        latest_pdf = pd.DataFrame(columns=['key', 'value'])
        latest_pdf = latest_pdf.append({'key': 'model_s3_path', 'value': model_s3_path}, ignore_index=True)

        normalizer_s3_path = data.get_param("normalizer_s3_path")
        latest_pdf = latest_pdf.append({'key': 'dsl_normalizer_s3_path', 'value': normalizer_s3_path}, ignore_index=True)

        # Add network_input cols
        network_input_cols = data.get_param('network_input_cols')
        latest_pdf = latest_pdf.append({'key': 'network_input_cols', 'value': network_input_cols}, ignore_index=True)

        # Add channel list to latest dataframe
        channel_list = data.get_param('channel_list')
        latest_pdf = latest_pdf.append({'key': 'channel_list', 'value': channel_list}, ignore_index=True)

        # Add st_prediction_path to latest dataframe
        st_predictions_path = data.get_param('st_predictions_path', "")
        latest_pdf = latest_pdf.append({'key': 'state_model_predictions_path', 'value': st_predictions_path}, ignore_index=True)

        # Add cp_rem_predictions_path to latest dataframe
        cp_rem_predictions_path = data.get_param('cp_rem_predictions_path', "")
        latest_pdf = latest_pdf.append({'key': 'cp_rem_predictions_path', 'value': cp_rem_predictions_path}, ignore_index=True)

        return latest_pdf

    def save_model(self):
        data = Data.get_instance()
        model = data.get_model("reward_predictor")

        data_access_layer = DataAccessLayer()
        scenario_uid = data.get_param("scenario", "__BASE_SCENARIO__")
        model_dir_path = Constants.TARGET_S3_PATH + f"models/scenario={scenario_uid}/"

        model_s3_path = model_dir_path + f"drl_reward_predictor_{Constants.RUN_UID}_model.keras"
        data_access_layer.write_model_to_s3(model, model_s3_path)
        data.set_param("model_s3_path", model_s3_path)

        # Write the normalizer
        normalizer = data.get_param('dsl_normalizer')
        normalizer_s3_path = model_dir_path + f"drl_normalizer_{Constants.RUN_UID}.pkl"
        data_access_layer.write_normalizer_to_s3(normalizer, normalizer_s3_path)
        data.set_param("normalizer_s3_path", normalizer_s3_path)

        latest_pdf = self._create_latest_pdf()

        model_config_csv_s3_path = model_dir_path + f"model_configs_{Constants.RUN_UID}.csv"
        data_access_layer.write_csv_to_s3(latest_pdf, model_config_csv_s3_path)

        model_latest_csv_s3_path = model_dir_path + f"latest.csv"
        data_access_layer.write_csv_to_s3(latest_pdf, model_latest_csv_s3_path)

    def write_intermediate_data(self):
        data = Data.get_instance()

        intermediate_pdf = data.get_dataframe('intermediate_pdf')
        run_mode = data.get_param("runMode", "none")
        if not intermediate_pdf.empty:
            data_access_layer = DataAccessLayer()
            rundate = data.get_param("rundate")
            scenario = data.get_param("scenario", "__BASE_SCENARIO__")
            predictions_s3_dir = Constants.TARGET_S3_PATH + f"predictions/rundate={rundate}/scenario={scenario}/"
            target_name = f"drl_intermediate_data_{run_mode}_{Constants.RUN_UID}.parquet"
            data_access_layer.write_predictions_to_s3(intermediate_pdf, predictions_s3_dir, target_name)
            print(f"Intermediate data written to: {predictions_s3_dir}{target_name}")

            if Constants.LEARNING_MODE:
                # Print reward from last iteration for actual transactions
                print("Reward for actual interactions:")
                print(intermediate_pdf[(intermediate_pdf['iteration']!=0)&(intermediate_pdf['isActual']==True) & (intermediate_pdf['currentChannel']!='NO_ACTION')].groupby(['currentChannel', 'activityYearMonth'])[['reward', 'predicted_reward']].describe())

            # Execution mode
            if Constants.EXECUTION_MODE:
                print("Reward for DRL selected interactions:")
                # Print reward for selected transactions
                print(intermediate_pdf[(intermediate_pdf['moveSelected']==1)].groupby(['currentChannel'])[['reward']].describe())

        else:
            logging.warning("No intermediate dataframe to write")
    def write_predictions(self):
        data = Data.get_instance()
        predictions = data.get_dataframe("predictions_pdf")

        if not predictions.empty:
            data_access_layer = DataAccessLayer()
            rundate = data.get_param("rundate")
            scenario = data.get_param("scenario", "__BASE_SCENARIO__")
            predictions_s3_dir = Constants.TARGET_S3_PATH + f"predictions/rundate={rundate}/scenario={scenario}/"
            target_name = "drl_suggestion_candidates.parquet"
            data_access_layer.write_predictions_to_s3(predictions, predictions_s3_dir, target_name)
        else:
            logging.warning("No predictions to write")

    def upload_touchpoints(self, channels):
        data = Data.get_instance()
        scenario = data.get_param("scenario", "__BASE_SCENARIO__")
        (data.get_param("region"), data.get_param("customer"), data.get_param("env"), scenario, channels)
        # sccapi, strategy = SCCIntegrator.readStrategy(data.get_param("region"), data.get_param("customer"), data.get_param("env"), scenario)
        sccapi = data.get_param("sccapi")
        strategy = data.get_param("strategy")
        self.upload_label_definition(strategy.get("modelTriggerName"), "SuggestedChannel", "Significance")
        sccapi.refreshDSERefData()
        sccapi.postTouchPointFile(channels)

    def create_mysql_engine(self):
        if Constants.LOCAL_MODE:
            engine = create_engine(f"mysql+mysqlconnector://{Constants.rds_username}:{Constants.rds_password}@localhost:3337/{Constants.stage_db}")
        else:
            engine = create_engine(f"mysql+mysqlconnector://{Constants.rds_username}:{Constants.rds_password}@{Constants.rds_hostname}:{Constants.rds_port}/{Constants.stage_db}")

        return engine

    def upload_label_definition(self, model, dataPointName1, dataPointName2):

        data_access_layer = DataAccessLayer()
        sql = f"CALL createDRLLabelDefinition('{model}', '{dataPointName1}', '{dataPointName2}', 1);"
        print(f"Creating LabelType and LabelDefinition using {sql}")
        data_access_layer.executeMySql(sql, Constants.learning_db)

    def upload_action_candidates(self):
        predictions_df = self.read_predictions()
        # if prediction is none or empty log warning
        if predictions_df is None or predictions_df.empty:
            logging.warning("No predictions to upload")
            return

        self.printSummary("predictions", predictions_df)

        #with engine.connect() as connection:
        #    connection.execute("CREATE TABLE example (id INTEGER, name VARCHAR(20))")

        # create table IF NOT EXISTS AKT_SIF_ActionCandidate
        # ( GenerationDate Date  not null
        # ,TimeZone VARCHAR(20)  not null
        # ,AccountId VARCHAR(40)  not null
        # ,ProductId VARCHAR(40)  null
        # ,ModelTriggerName VARCHAR(80)  not null
        # ,DataPointName VARCHAR(80)  not null
        # ,DataPointValue VARCHAR(500)  not null
        # ,Sequence Int(11)  null
        # ,StartDate Date  null
        # ,EndDate Date  null
        # ,IdealDate Date  null
        # ,CreatedDate Datetime  not null DEFAULT CURRENT_TIMESTAMP
        # );

        data = Data.get_instance()
        rundate = data.get_param("rundate")
        start_date = data.get_param("startdate")
        end_date = data.get_param("enddate")
        if not start_date:
            start_date, end_date = self.get_last_execution_period()

        if not start_date and not Constants.MULTI_DAY_EXECUTION:
            start_date = rundate
            end_date = rundate

        # Connect to RDS and write df
        engine = self.create_mysql_engine()
        data_access_layer = DataAccessLayer()

        strategy = data.get_param("strategy")
        modelTriggerName = strategy.get("modelTriggerName")

        if Constants.MULTI_DAY_EXECUTION:

            if Constants.INCREMENTAL_RUN:
                # If incremental=true, read existing accounts that exist
                # Write actions only for new accounts that don't already exist in AKT_SIF_ActionCandidateLanding on start-date
                existing_accounts_df = data_access_layer.runMySqlQuery(f"select distinct accountId from {Constants.stage_db}.AKT_SIF_ActionCandidateLanding where ModelTriggerName = '{modelTriggerName}' and endDate >= str_to_date('{start_date}', '%Y-%m-%d') ")
                print(f"Total number of actions generated:{predictions_df.shape[0]}")
                print(f"Number of existing accounts with actions that end after start-date:{existing_accounts_df.shape[0]}")
                predictions_df = predictions_df[~predictions_df.accountUid.isin(existing_accounts_df.accountId)]
                print(f"Total number of incremental actions (based on new accounts) to persist:{predictions_df.shape[0]}")
            else:
                # This is not incremental mode... So, check to make sure there is no existing data beyond start-date.  If there is, it should be cleaned up first.
                existing_action_count_df = data_access_layer.runMySqlQuery(f"select count(1) action_count from {Constants.stage_db}.AKT_SIF_ActionCandidateLanding where ModelTriggerName = '{modelTriggerName}' and endDate >= str_to_date('{start_date}', '%Y-%m-%d') ")
                existing_action_count = existing_action_count_df['action_count'].iloc[0]
                if existing_action_count > 0:
                    print(f"********** Actions already exist beyond specified startdate:{existing_action_count}. Please cleanup the data and re-run.")
                    print(f"Possible queries to use to cleanup (Note: only Pending actions will be deleted and only Current actions will be end-dated using SQL below):")
                    print(f"    SQL to delete existing 'Pending' ActionCandidates for model: {modelTriggerName} that end after {start_date}")
                    delete_sql = f"delete from {Constants.stage_db}.AKT_SIF_ActionCandidateLanding where ModelTriggerName = '{modelTriggerName}' and endDate >= str_to_date('{start_date}', '%Y-%m-%d') and status = 'Pending' "
                    print(f"    {delete_sql}")
                    ## Delete any Pending actions that are in the future
                    # Comment out the actual call.  Let this be a manual user-action for now
                    #data_access_layer.executeMySql(delete_sql)

                    # Expire actions with status = Current that overlaps with new data by setting endDate = new_start_date - 1
                    print(f"    SQL to end-date 'Current' ActionCandidates for model: {modelTriggerName} that end after {start_date}")
                    update_sql = f" UPDATE {Constants.stage_db}.AKT_SIF_ActionCandidateLanding set endDate = date_add('{start_date}', INTERVAL -1 DAY) where ModelTriggerName = '{modelTriggerName}' and endDate >= str_to_date('{start_date}', '%Y-%m-%d') and status = 'Current' "
                    print(f"    {update_sql}")
                    # Comment out the actual call.  Let this be a manual user-action for now
                    #data_access_layer.executeMySql(update_sql)
                    raise Exception(f"Please retry after cleaning up overlapping action candidates using SQL provided at the end of this run...")

        # Prepare action candidate data
        predictions_df = predictions_df.drop(columns=['component_used'])
        predictions_df['GenerationDate'] = date.today()
        predictions_df['TimeZone'] = 'America/Los_Angeles' # TODO: How should this be computed
        predictions_df['ModelTriggerName'] = modelTriggerName

        predictions_df['StartDate'] = start_date
        predictions_df['EndDate'] = end_date

        #predictions_df['Sequence'] = 1
        predictions_df = predictions_df.drop(columns=['accountId', 'productId'])
        if Constants.MULTI_DAY_EXECUTION:
            predictions_df = predictions_df.rename(columns={"accountUid": "accountId", "productUid": "productId", "action_date": "IdealDate"})
        else:
            predictions_df = predictions_df.rename(columns={"accountUid": "accountId", "productUid": "productId"})
            predictions_df['IdealDate'] = rundate

        predictions_df['Sequence'] = predictions_df.groupby(['accountId', 'productId'])['IdealDate'].rank(ascending=True)

        # Customize predictions_df for actions
        actions_df = predictions_df.drop(columns=['reward'])
        actions_df['DataPointName'] = 'SuggestedChannel'
        actions_df = actions_df.rename(columns={"currentChannel": "DataPointValue"})
        self.printSummary("actions", actions_df)
        actions_df.to_sql('AKT_SIF_ActionCandidateLanding', con=engine, if_exists='append', index=False)

        # Customize predictions_df for significance
        significance_df = predictions_df.drop(columns=['currentChannel'])
        significance_df['DataPointName'] = 'Significance'
        significance_df = significance_df.rename(columns={"reward": "DataPointValue"})
        self.printSummary("significance", significance_df)
        significance_df.to_sql('AKT_SIF_ActionCandidateLanding', con=engine, if_exists='append', index=False)


    def read_predictions(self):

        data = Data.get_instance()
        rundate = data.get_param("rundate")
        scenario = data.get_param("scenario", "__BASE_SCENARIO__")
        predictions_s3_path = Constants.TARGET_S3_PATH + f"predictions/rundate={rundate}/scenario={scenario}/drl_suggestion_candidates.parquet"

        print(f"Reading preductions from: {predictions_s3_path}")

        data_access_layer = DataAccessLayer()

        predictions_df = data_access_layer.read_s3_parquet(predictions_s3_path)
        return predictions_df

    def write_drl_run(self):
        data = Data.get_instance()
        run_uid = Constants.RUN_UID

        scenario_pdf = data.get_dataframe("drl_scenario")
        scenario_id = scenario_pdf['scenarioId'].values[0]
        scenario_uid = scenario_pdf['scenarioUid'].values[0]
        scenario_name = scenario_pdf['scenarioName'].values[0]

        start_date = data.get_param("startdate")
        end_date = data.get_param("enddate")

        # Prepare action mode for the run -- learning, execution, touchpoints, candidates
        if Constants.LEARNING_MODE:
            action = "learning"
        elif Constants.EXECUTION_MODE:
            action = "execution"
        else:
            action = "post-proc"
        # Append touchpoint postproc mode
        post_proc_mode = data.get_param("postProcMode", "none")
        if "touchpoints" in post_proc_mode:
            action = action + ", touchpoints"
        if "predictions" in post_proc_mode or "candidates" in post_proc_mode:
            action = action + ", candidates"

        user = data.get_param("user", "unknown")

        sql_query = (f"insert into DRL_Run (runUID, scenarioId, scenarioUid, scenarioName, action, startDate, endDate, executedBy) "
               f"values ('{run_uid}', {scenario_id}, '{scenario_uid}', '{scenario_name}', '{action}', '{start_date}','{end_date}', '{user}')")

        data_access_layer = DataAccessLayer()
        data_access_layer.executeMySql(sql_query, Constants.learning_db)
        logging.info(f"Inserted into DRL_Run table for run: {run_uid}")

    def get_last_execution_period(self):
        data = Data.get_instance()

        last_exec_start_date = ""
        last_exec_end_date = ""

        scenario_pdf = data.get_dataframe("drl_scenario")
        scenario_uid = scenario_pdf['scenarioUid'].values[0]
        last_execution_df = DataAccessLayer.runMySqlQuery(f"select startDate, endDate from DRL_Run where scenarioUid = '{scenario_uid}' and action like '%execution%' order by executionDate desc limit 1", Constants.learning_db)
        if not last_execution_df.empty:
            last_exec_start_date = last_execution_df['startDate'].iloc[0]
            last_exec_end_date = last_execution_df['endDate'].iloc[0]

        logging.warning(f"Last execution period: {last_exec_start_date} to {last_exec_end_date}")
        return last_exec_start_date, last_exec_end_date
