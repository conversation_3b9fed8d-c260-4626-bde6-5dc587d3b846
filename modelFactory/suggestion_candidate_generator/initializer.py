import logging

import ast
import uuid
import datetime
import pandas as pd
from keras.optimizers.schedules import ExponentialDecay
from keras.layers import Dense, BatchNormalization, LeakyReLU, Dropout
from keras.regularizers import l2

from abstract_model_factory.abstract_initializer import AbstractInitializer
import sys
import getopt

from suggestion_candidate_generator.data import Data
from suggestion_candidate_generator.constants import Constants
from os import path
import json
import os
from suggestion_candidate_generator.data_access_layer import DataAccessLayer
from aktana_ml_utils import aktana_ml_utils

import keras
from keras import layers, Sequential


class Initializer(AbstractInitializer):

    @staticmethod
    def calculate_network_input_cols()->list:
        data = Data.get_instance()
        input_cols = []
        # Segment Count
        for segment in data.get_param('segment_list'):
            input_cols.append(f"hcpSegment_{segment}")

        # Get product list
        for productId in data.get_param('productId_list'):
            input_cols.append(f"productId_{productId}")

        # Get Channel list
        for channel in data.get_param('channel_list'):
            input_cols.append(f"currentChannel_{channel}")
            # input_cols.append(f"dsl_channel_{channel}")
        input_cols.append(f"channelPropensity")
        input_cols.append(f"allow_channel")

        # # Get Content list
        # for content in data.get_param('content_list'):
        #     input_cols.append(f"currentContent_{content}")
        #     input_cols.append(f"dsl_content_{content}")
        #     input_cols.append(f"allow_content_{content}")
        #     input_cols.append(f"lastContent_{content}")

        input_cols.append('remProb')
        input_cols.append('segmentClimbProb')

        # Add audience related columns
        '''
        [{'typeDefinitionName': 'Marketing Opt Out', 'typeNames': ['N/A', 'Não', 'Sim']}, 
        {'typeDefinitionName': 'Sample Consent', 'typeNames': ['N/A', 'Sim']}, 
        {'typeDefinitionName': 'Tiers', 'typeNames': ['N/A', 'ST', 'T1', 'T2', 'T3']}]
        '''
        attribute_values = data.get_param('attributeValues', None)
        audiences = data.get_param('audiences', None)

        if attribute_values is not None:

            for attribute in attribute_values:
                for audience in audiences:
                    if audience['typeDefinitionName'] == attribute['typeDefinitionName']:
                        for value in attribute['typeNames']:
                            input_cols.append(f"{audience['s3column']}_{value}")


        # Add additional cols
        additional_cols = data.get_param('additional_feature_cols', [])
        interaction_pdf = data.get_dataframe('interactions')
        for col in additional_cols:
            if col in interaction_pdf.columns:
                # Add all distinct values of the column
                unique_values = interaction_pdf[col].unique()
                for value in unique_values:
                    input_cols.append(f"{col}_{value}")

        # Add feature cols from reward_component views
        reward_components_pdf = data.get_dataframe('reward_components')
        for index, row in reward_components_pdf.iterrows():
            if row['useAsLearningFeature'] == 1:
                input_cols.append(row['valueColumn'])

        # Remove duplicates from input_cols
        input_cols = list(set(input_cols))

        print("Complete list of input cols-", input_cols)

        data.set_param('network_input_cols', input_cols)
        return input_cols


    @classmethod
    def initialize_model_struct(cls):
        data = Data.get_instance()

        if data.get_model('reward_predictor') is not None:
            logging.info("Model structure already initialized")
            return data.get_model('reward_predictor')

        input_dim = len(cls.calculate_network_input_cols())
        hidden_layers_dim = data.get_param('hidden_layers', {'layer1': 200, 'layer2': 100, 'layer3': 70, 'layer4': 40})
        output_dim = data.get_param('output_dim', 1)
        drop_out_rate = data.get_param('drop_out_rate', 0.1)

        # model = keras.Sequential(
        #     [
        #         layers.Input(shape=(input_dim,), name="input"),
        #         layers.Dense(hidden_layers_dim['layer1'], activation="tanh", name="layer1"),
        #         layers.Dropout(drop_out_rate),
        #         layers.Dense(hidden_layers_dim['layer2'], activation="tanh", name="layer2"),
        #         layers.Dropout(drop_out_rate),
        #         layers.Dense(hidden_layers_dim['layer3'], activation="tanh", name="layer3"),
        #         layers.Dropout(drop_out_rate),
        #         layers.Dense(hidden_layers_dim['layer4'], activation="tanh", name="layer4"),
        #         layers.Dropout(drop_out_rate),
        #         layers.Dense(output_dim, activation="linear", name="output")
        #     ]
        # )

        model = Sequential([
            keras.layers.Input(shape=(input_dim,), name="input"),

            Dense(hidden_layers_dim['layer1'], name="layer1"),
            BatchNormalization(),
            LeakyReLU(alpha=0.1),

            Dense(hidden_layers_dim['layer2'], name="layer2"),
            BatchNormalization(),
            LeakyReLU(alpha=0.1),

            Dense(hidden_layers_dim['layer3'], kernel_regularizer=l2(0.01), name="layer3"),
            BatchNormalization(),
            LeakyReLU(alpha=0.1),
            Dropout(drop_out_rate),

            Dense(hidden_layers_dim['layer4'], activation="relu", name="layer4"),

            Dense(output_dim, activation="linear", name="output")
        ])

        optimizer_type = data.get_param('optimizer', 'adagrad')
        if optimizer_type == 'adagrad':
            lr_schedule = ExponentialDecay(initial_learning_rate=0.01, decay_steps=1000, decay_rate=0.9)
            optimizer = keras.optimizers.Adagrad(learning_rate=lr_schedule)
        elif optimizer_type == 'adam':
            lr_schedule = ExponentialDecay(initial_learning_rate=0.001, decay_steps=1000, decay_rate=0.9)
            optimizer = keras.optimizers.Adam(learning_rate=lr_schedule)
        else:
            lr_schedule = ExponentialDecay(initial_learning_rate=0.01, decay_steps=1000, decay_rate=0.9)
            optimizer = keras.optimizers.SGD(learning_rate=lr_schedule)
        loss = data.get_param('loss', 'mse')
        metrics = data.get_param('metrics', ['mse'])

        model.compile(optimizer=optimizer, loss=loss, metrics=metrics)

        data.set_model('reward_predictor', model)

        logging.info("Initialized model structure")

        return model

    def read_param_file(self):
        """

        @return:
        """
        data = Data.get_instance()

        # print command line arguments
        #print("****** Command Line Parameters:",  sys.argv)

        short_opt = "hc:e:a:r:ec:o:l:d"
        long_opt = ["customer=", "env=", "app=", "region=", "ecosystem=", "json-output=", "local=", "use-debug=",
                    "scenario=", "loglevel=", "autocreate=", "runMode=", "postProcMode=", "journey=",
                    "useSCC=", "rundate=", "startdate=", "enddate=", "user=", "incrementalRun="]
        cmd_argv = sys.argv

        ak_ml_utils = aktana_ml_utils()
        cmdline_params, metadata_params = ak_ml_utils.initialize(cmd_argv, short_opt, long_opt, Constants.TEST_MODE)
        params = ak_ml_utils.get_params_json(Constants.TEST_MODE)
        params = json.loads(params)
        #print("Params:" + str(params))

        for key, value in cmdline_params.items():
            data.set_param(key, value)

        # If local is true set local constants
        if data.get_param("local", "") != "":
            Constants.LOCAL_MODE = True

        # If use-debug is true set debug constants
        if data.get_param("use-debug", "") != "":
            Constants.TEST_MODE = True

        if Constants.TEST_MODE:
            print("Params from metadata:" + str(params))

        if data.get_param("runMode", "none").lower() == "execution":
            Constants.EXECUTION_MODE = True

        if data.get_param("runMode", "none").lower() == "learning":
            Constants.LEARNING_MODE = True

        if data.get_param("incrementalRun", "none").lower() == "true":
            Constants.INCREMENTAL_RUN = True

        if Constants.LEARNING_MODE and Constants.INCREMENTAL_RUN:
            raise Exception("Incremental run is not supported in learning mode")

        if data.get_param("startdate", "") != "":
            # Copy start date to runDate
            data.set_param("rundate", data.get_param("startdate"))

            # Convert start and end date to datetime
            start_date = datetime.datetime.strptime(data.get_param("startdate"), "%Y-%m-%d").date()
            end_date = datetime.datetime.strptime(data.get_param("enddate"), "%Y-%m-%d").date()
            if start_date > end_date:
                raise Exception("Execution start date cannot be greater than end date")

            data.set_param("startdate", start_date)
            data.set_param("enddate", end_date)
            Constants.MULTI_DAY_EXECUTION = True
        elif data.get_param("rundate", "") != "":
            # Convert run date to date and set to startdate
            start_date = datetime.datetime.strptime(data.get_param("rundate"), "%Y-%m-%d").date()
            data.set_param("startdate", start_date)
            data.set_param("enddate", start_date)
            Constants.MULTI_DAY_EXECUTION = True


        print("****** Command Line Parameters:" + str(data.params))

        data.set_param("connect_params", params)

        Constants.ACCESS_ID = params.get('adl-awsAccessKey')
        Constants.ACCESS_KEY = params.get('adl-awsAccessKey')
        Constants.SECRET_KEY = params.get('adl-awsSecretKey')

        Constants.DCO_S3_PATH = params.get('adl-dcoS3Location')
        Constants.ADL_S3_PATH = params.get('adl-adlS3Location')
        Constants.ADL_S3_PATH = Constants.ADL_S3_PATH.replace('s3:', 's3a:')
        Constants.DCO_S3_PATH = params.get('adl-dcoS3Location')
        Constants.ADL_S3_PATH = params.get('adl-adlS3Location')
        Constants.S3_BASE_PATH = Constants.ADL_S3_PATH.split('adl/')[0]
        Constants.TARGET_S3_PATH = Constants.S3_BASE_PATH + 'candidate-generator/reward-predictor/'

        Constants.sw_account = params.get('snowflake-account')
        Constants.sw_user = params.get('snowflake-user')
        Constants.sw_password = params.get('snowflake-password')
        Constants.sw_database = params.get('snowflake-database')
        Constants.sw_schema = params.get('snowflake-schema')
        Constants.sw_role = params.get('snowflake-role')
        Constants.sw_warehouse = params.get('snowflake-warehouse')

        Constants.rds_hostname = params.get('rds-server')
        Constants.rds_port = params.get('rds-port') if params.get('rds-port') else 3306
        Constants.rds_username = params.get('rds-user')
        Constants.rds_password = params.get('rds-password')
        Constants.engine_db = params.get('rds-enginedbname')
        Constants.stage_db = params.get('rds-stagedbname')
        Constants.learning_db = params.get('rds-learningdbname')

    def read_qa_config(self):
        query = 'select * from "MF_QA_config"'
        qa_config_pdf = DataAccessLayer.read_snow_flake_pandas(query)
        print("MF QA Config-", qa_config_pdf)
        Data.get_instance().set_dataframe('mf_qa_config_pdf', qa_config_pdf)

    def read_model_threshold(self):
        query = 'select * from "Model_Threshold"'
        model_threshold_pdf = DataAccessLayer.read_snow_flake_pandas(query)
        print("Model Thresholds", model_threshold_pdf)
        data = Data.get_instance()

        # Iterate through the model thresholds and set the parameters
        for index, row in model_threshold_pdf.iterrows():
            data.set_param(f"{row['Threshold_ID']}", row['Threshold_Value_Number'])

    def read_system_config(self):
        query = 'select * from "System_Config" where "Process_Name" = \'suggestion_candidate_generator\';'
        system_config_pdf = DataAccessLayer.read_snow_flake_pandas(query)
        print("System Config", system_config_pdf)
        data = Data.get_instance()

        # Iterate through the dataframe columns and set values in data params
        for col in system_config_pdf.columns:
            data.set_param(col, system_config_pdf[col].values[0])


        # If Client_Name is default set customer value from command line params
        if data.get_param("Client", "Default") == "Default":
            data.set_param("Client_Name", data.get_param("customer"))

        channel_active_dict = ast.literal_eval(data.get_param('Channel_Active'))
        for channel, value in channel_active_dict.items():
            data.set_param(channel, value)
            data.set_param(f'transform_{channel}', value)

        # Set all channel to 1
        data.set_param('transform_all', 1)

        data.set_param('deployment_option', 1) # 0 - Data, 1- Data+Model

        data.set_param('s3_model_store_path', Constants.DCO_S3_PATH + "data/dco_read_write/raw_files/suggestion_candidate_generator/model/")
        model_name = data.get_param("Client_Name") + "_" + datetime.date.today().strftime("%Y-%m-%d")
        data.set_param('Model_Name', model_name)

    def initialize_qa_log(self):
        data = Data.get_instance()
        mf_qa_config_pdf = data.get_dataframe('mf_qa_config_pdf')
        mf_qa_logs_pdf = pd.DataFrame(columns=mf_qa_config_pdf.columns)
        mf_qa_logs_pdf["Test_Value"] = None
        mf_qa_logs_pdf["Test_Result"] = None
        mf_qa_logs_pdf["Test_Action"] = None
        mf_qa_logs_pdf["Client_Name"] = None
        mf_qa_logs_pdf["Process_Name"] = None
        mf_qa_logs_pdf["Model_Name"] = None
        mf_qa_logs_pdf["Update_Date"] = None
        mf_qa_logs_pdf["Run_Uid"] = None

        data.set_dataframe("mf_qa_logs_pdf", mf_qa_logs_pdf)

    def set_additional_feature_cols(self):
        data = Data.get_instance()
        additional_feature_cols = data.get_param('additional_feature_cols', None)
        if additional_feature_cols is None:
            additional_feature_cols = ["department_std_akt", "facilityBeds_std_akt", "ispersonaccount_std_akt",
                                       "levelHCO_std_akt",
                                       "pdrpoptout_std_akt", "profileConsent_std_akt", "specialties_std_akt",
                                       "specialties2_std_akt", "title_std_akt", "yearSinceGraduation_std_akt",
                                       "adoptionLevel_std_akt", "adoptionObjective_std_akt", "advocacy_std_akt",
                                       "awareness_std_akt", "behavior_std_akt", "decile_std_akt", "hcpPriority_std_akt",
                                       "hcpSegment_std_akt", "hcpTier_std_akt", "hospitalBudget_std_akt",
                                       "isProductTargeted_std_akt", "journeyStage_std_akt", "potential_std_akt",
                                       "priority_std_akt", "proposedSegment_std_akt", "protocolPosition_std_akt",
                                       "quintile_std_akt", "rating_std_akt", "readiness_std_akt", "segment_std_akt",
                                       "sellingStage_std_akt", "sendsCompleted_dse_akt", "sentiment_std_akt",
                                       "speakerSkills_std_akt", "tier_std_akt"]
        else:
            additional_feature_cols = additional_feature_cols.split(',')
        data.set_param('additional_feature_cols', additional_feature_cols)

    def execute(self):
        self.read_param_file()

        Constants.RUN_UID = str(uuid.uuid4())

        self.set_additional_feature_cols()

        print("Initialized candidate generator with Run Uid- ", Constants.RUN_UID)

    def qa_module(self):
        pass
