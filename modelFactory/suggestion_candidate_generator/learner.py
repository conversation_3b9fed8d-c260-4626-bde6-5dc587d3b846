import logging

import keras
from keras.callbacks import ReduceLROnPlate<PERSON>
from sklearn import preprocessing

from abstract_model_factory.abstract_learner import AbstractLearner

from suggestion_candidate_generator.data import Data


class Learner(AbstractLearner):

    @staticmethod
    def train_model(X, y):
        print(f"Started Training for data of shape: {X.shape}")
        data = Data.get_instance()
        model = data.get_model('reward_predictor')
        epoch = data.get_param('LEARNING_EPOCH', 25)
        batch_size = data.get_param('LEARNING_BATCH_SIZE', 1000)

        enable_early_stopping = bool(data.get_param('ENABLE_EARLY_STOPPING', False))

        # reduce_lr = ReduceLROnPlateau(monitor='loss', factor=0.5, patience=3, min_lr=1e-6, verbose=1)
        # callbacks = [reduce_lr]
        callbacks = []

        if enable_early_stopping:
            baseline = None
            last_training_loss = data.get_param('last_training_loss')
            patience = int(data.get_param('EARLY_STOPPING_PATIENCE', 5))
            start_from_epoch = int(data.get_param('EARLY_STOPPING_START_EPOCH', 5))

            if last_training_loss:
                iteration_improvement_factor = float(data.get_param('ITERATION_IMPROVEMENT_FACTOR', 5))
                baseline = last_training_loss * iteration_improvement_factor

            early_stopping = keras.callbacks.EarlyStopping(monitor='loss', patience=patience, verbose=1, restore_best_weights=True,
                                                           start_from_epoch=start_from_epoch, baseline=baseline)
            callbacks.append(early_stopping)

        history = model.fit(X, y, epochs=epoch, batch_size=batch_size, verbose=2, callbacks=callbacks)
        last_training_loss = history.history['loss'][-1]
        data.set_param('last_training_loss', last_training_loss)

        print("Completed Training.")

        return model

    def execute(self):
        pass

    def qa_module(self):
        pass