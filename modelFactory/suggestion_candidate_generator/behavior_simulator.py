import json
from math import tanh
import numpy as np
import pandas as pd
import random
import logging
from suggestion_candidate_generator.data import Data


class BehaviorSimulator:

    def get_accepted_suggestion(self, input_pdf: pd.DataFrame, probability_column_name: object,
                                base_quantile: float,
                                percentile_random_factor_min: float,
                                percentile_random_factor_max: float) -> pd.DataFrame:
        """
        This method implements a simple solution to the problem of insuring the acceptance rate is in a specified range
        the base_quantile is the the initial number, between 0.0 and 1.0, that determines  cutoff value of
        probability for acceptance. This value will be modified adding a random number between
        percentile_random_factor_min and percentile_random_factor_max to determine the quantile that will be
        used for a particular run

        the action_taken and are is_completed columns added to a copy of the input_data_frame.
        Both columns contain the same information, are needed downstream

        @param input_pdf: input data
        @type input_pdf: pd.DataFrame
        @param probability_column_name: column name in the input_data_frame used for acceptance
        @type probability_column_name: object (string)
        @param base_quantile: initial quantile provided to determine lower bound for accepted probability
        @type base_quantile:float
        @param percentile_random_factor_min: lower bound for uniform random number added to base_quantile
        @type percentile_random_factor_min: float
        @param percentile_random_factor_max: upper bound for uniform random number added to base_quantile
        @type percentile_random_factor_max: float
        @return: returns a copy of the input_data_frame with action_taken and is_completed columns added
        @rtype:
        """
        random_quantile_factor = np.random.uniform(low=percentile_random_factor_min, high=percentile_random_factor_max)
        quantile_to_use = base_quantile + random_quantile_factor
        probability_at_quantile = input_pdf[probability_column_name].quantile(q=quantile_to_use)

        # Set repAccepted and interactionCompleted to 1 if modelProb is greater than probability_at_quantile
        input_pdf['repAccepted'] = input_pdf[probability_column_name] > probability_at_quantile
        input_pdf['interactionCompleted'] = input_pdf['repAccepted']

        return input_pdf


    def adjust_model_prob(self, input_pdf):

        data = Data.get_instance()
        simulator_randomness = data.get_param('SIM_ENABLE_RANDOMNESS', False)

        if simulator_randomness:
            lower_random_bound = float(data.get_param('sim_lower_random_bound', -0.1))
            upper_random_bound = float(data.get_param('sim_upper_random_bound', 0.1))
            input_pdf['modelProb'] = input_pdf['modelProb'].apply(lambda x: x + random.uniform(lower_random_bound, upper_random_bound))

        def get_supressed_prob(row):
            channel = row['currentChannel']
            # Return original modelProb if allow_channel_{channel} is 1
            #if channel == 'NO_ACTION' or row[f'allow_channel_{channel}'] == 1:
            if channel == 'NO_ACTION' or row['allow_channel'] == 1:
                return row['modelProb']

            supression_factor = row['modelProb'] * tanh(-row[f'dsl_channel_{channel}'] / row['adjustmentFactor'])
            new_prob = row['modelProb'] + supression_factor
            return new_prob

        def get_boosted_prob(row):
            channel = row['currentChannel']
            # Return original modelProb if allow_channel_{channel} is 0
            #if channel == 'NO_ACTION' or row[f'allow_channel_{channel}'] == 0:
            if channel == 'NO_ACTION' or row['allow_channel'] == 0:
                return row['modelProb']

            boost_factor = (1 - row['modelProb']) * tanh(row[f'dsl_channel_{channel}'] / row['adjustmentFactor'])
            new_prob = row['modelProb'] + boost_factor
            return new_prob

        enable_simulator_supression = data.get_param('SIM_ENABLE_SUPPRESSION', False)
        if enable_simulator_supression:
            input_pdf.loc[:, 'modelProb'] = input_pdf.apply(lambda row: get_supressed_prob(row), axis=1)

        enable_simulator_boosting = data.get_param('SIM_ENABLE_BOOSTING', False)
        if enable_simulator_boosting:
            input_pdf.loc[:, 'modelProb'] = input_pdf.apply(lambda row: get_boosted_prob(row), axis=1)

        return input_pdf

    def rule_based_acceptance_model(self, input_pdf):

        # set Accepted to 1 if modelProb is greater than threshold
        input_pdf['repAccepted'] = input_pdf['modelProb'] > input_pdf['acceptanceThreshold']
        input_pdf['interactionCompleted'] = input_pdf['repAccepted']

        data = Data.get_instance()
        enable_acceptance_rate_control = data.get_param('SIM_ENABLE_ACCEPT_RATE_CONTROL', False)
        if enable_acceptance_rate_control:
            acceptance_rate_target = data.get_param('SIM_ACCEPT_RATE_TARGET', 0.70)
            acceptance_rate_random_min = data.get_param('sim_acceptance_rate_random_min', -0.05)
            acceptance_rate_random_max = data.get_param('sim_acceptance_rate_random_max', 0.02)
            input_pdf = self.get_accepted_suggestion(input_pdf, 'modelProb',
                                                    1-acceptance_rate_target, acceptance_rate_random_min,
                                                    acceptance_rate_random_max)

        return input_pdf

    def simulate(self, input_pdf):
        data = Data.get_instance()
        simulator_input = data.get_dataframe('simulator_input')

        # Merge based on hcpSegment and currentChannel
        input_pdf = input_pdf.merge(simulator_input,how='left', left_on=['hcpSegment', 'currentChannel'],
                                    right_on=['hcpSegment', 'channel'])

        # Create column modelProb copying remProb
        input_pdf['modelProb'] = input_pdf['remProb']

        # Fill missing values in modelProb with mean
        input_pdf['modelProb'].fillna(input_pdf['modelProb'].mean(), inplace=True)

        input_pdf = self.adjust_model_prob(input_pdf)

        input_pdf = self.rule_based_acceptance_model(input_pdf)

        # Drop columns added during the process
        input_pdf.drop(columns=['acceptanceThreshold', 'adjustmentFactor'], inplace=True)

        return input_pdf

