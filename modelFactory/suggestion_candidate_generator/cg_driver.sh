#!/bin/bash

# Wrapper/driver script for Candidate-generator jobs
# Can be used to invoke any of the Candidate-generator related jobs

# Usage: cg_driver.sh <job_name> --customer <customer-name> --env <env-name>"
#   Supported job names are: RUN_CG_LEARN, RUN_CG, RUN_JG, RUN_STATE_MODEL, (if needed RUN_STATE_MODEL_LEARN)  and RUN_CG_POSTPROC
#   Sample usage: ./cg_driver.sh RUN_CG --customer genentechca --env preprod"


# RUN_CG_LEARN - to execute the learning Job for DRL-based Candidate Generator
# RUN_CG - to execute the DRL-based Candidate Generator for daily candidates
# RUN_JG - to execute the DRL-based Candidate Generator for a sequence of candidates
# RUN_STATE_MODEL - to learn and predict the DRL-based state model for Candidate Generation
# RUN_STATE_MODEL_LEARN - to learn the state model for DRL
# RUN_CG_POSTPROC - to execute the post-processor job for Candidate generator

# All jobs take the following command line parameters:
#   --customer custName (name of the customer configured in aktana metadata)
#   --env envName (name of the environment configured in aktana metadata)

JOB=$1
shift 1

echo "Python version : $(python3 --version)"

# Directory of this script is the CG Install dir
CG_INSTALL_DIR=`dirname $0`
CG_INSTALL_DIR=`readlink -f $CG_INSTALL_DIR`
echo "CG Install directory = $CG_INSTALL_DIR"

#echo "Install python dependencies from $CG_INSTALL_DIR/requirements_incremental.txt"
#pip3 install -r $CG_INSTALL_DIR/requirements_incremental.txt

# For state-model, drl_state_model.sh will build PYTHONPATH.  Do it here for other jobs
if [[ $JOB != RUN_STATE_MODEL*  && $JOB != RUN_CJ* ]] ;
then
    APP_PARAM="--app CANDIDATE_GENERATOR"
    
    # Add modelFactory directory (one level up from the cg directory) to PYTHONPATH
    export PYTHONPATH="$(dirname "$CG_INSTALL_DIR"):${PYTHONPATH}"

    # Add common python utils to PYTHONPATH used to get parameters from Aktana metadata
    export PYTHONPATH="$CG_INSTALL_DIR/../../common/pyUtils:${PYTHONPATH}"
    echo "PYTHONPATH=$PYTHONPATH"
fi

case $JOB in

    RUN_DRL)
        echo "Learning DRL model for Candidate generator ...python3 $CG_INSTALL_DIR/main.py "$@" $APP_PARAM"
        eval "python3 $CG_INSTALL_DIR/main.py $@ $APP_PARAM"
	    rc=$?
        ;;

    RUN_CSM)
        echo "Run Content Selection model ... $CG_INSTALL_DIR/../content_selection/csm_driver.sh $@"
        $CG_INSTALL_DIR/../content_selection/csm_driver.sh "$@"
	    rc=$?
        ;;

    RUN_CG_LEARN)
        echo "Learning DRL model for Candidate generator ...python3 $CG_INSTALL_DIR/main.py --runMode learning $@ $APP_PARAM"
        eval "python3 $CG_INSTALL_DIR/main.py --executionMode learning $@ $APP_PARAM"
	    rc=$?
        ;;

    RUN_CG)
        echo "Executing DRL model for Candidate generation ...python3 $CG_INSTALL_DIR/main.py --runMode execution $@ $APP_PARAM"
        eval "python3 $CG_INSTALL_DIR/main.py --executionMode execution $@ $APP_PARAM"
	    rc=$?
        ;;

    RUN_JG)
        echo "Executing DRL model for Journey generation ...python3 $CG_INSTALL_DIR/main.py $@ --runMode execution --journey true $APP_PARAM"
        python3 $CG_INSTALL_DIR/main.py $@ --executionMode execution --journey true $APP_PARAM
	    rc=$?
        ;;

    RUN_STATE_MODEL*)
        echo "Building state model and output ...$CG_INSTALL_DIR/../drl_state_model/drl_state_model_driver.sh $@ $APP_PARAM"
        $CG_INSTALL_DIR/../drl_state_model/drl_state_model_driver.sh "$@" $APP_PARAM
	    rc=$?
        ;;

    RUN_CG_POSTPROC)

        echo "Executing DRL model for post-processing ...python3 $CG_INSTALL_DIR/main.py --runMode none --postProcMode predictions $@ $APP_PARAM"
        eval "python3 $CG_INSTALL_DIR/main.py  --executionMode none $@ $APP_PARAM"
	    rc=$?
        ;;

    RUN_CJ)
        echo "Running Customer Journey job... $CG_INSTALL_DIR/../customer_journey/cj_driver.sh $@"
        $CG_INSTALL_DIR/../customer_journey/cj_driver.sh "$@"
        rc=$?
        ;;

    RUN_CONTENT)
        echo "Running Customer Journey job... $CG_INSTALL_DIR/../customer_journey/cj_driver.sh $@"
        $CG_INSTALL_DIR/../content_affinity/content_affinity_driver.sh "$@"
        rc=$?
        ;;

    SETUP_DRL_WORKFLOW)
        echo "Setup workflows for DRL ...python3 $CG_INSTALL_DIR/setup_drl_worfklow.py "$@" $APP_PARAM"
        eval "python3 $CG_INSTALL_DIR/setup_drl_workflow.py $@ $APP_PARAM"
	    rc=$?
        ;;

    *)
        echo "Usage: cg_driver.sh <job_name> --customer <customer-name> --env <env-name>"
        echo "  Supported job names are: RUN_CG_LEARN, RUN_CG, RUN_JG, RUN_STATE_MODEL, (if needed RUN_STATE_MODEL_LEARN)  and RUN_CG_POSTPROC"
        echo "  Sample usage: ./cg_driver.sh RUN_CG --customer genentechca --env prod"
    	rc=1
        ;;

esac
echo "Returning from cg driver with rc=$rc"
exit $rc

