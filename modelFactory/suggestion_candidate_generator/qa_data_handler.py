from datetime import datetime
from suggestion_candidate_generator.data import Data
from suggestion_candidate_generator.constants import Constants

class QADataHandler():

    @staticmethod
    def get_dict_with_test_info(phase, test_id, test_value=None):

        data = Data.get_instance()
        mf_qa_config_pdf = data.get_dataframe("mf_qa_config_pdf")
        mf_phase_qa_pdf = mf_qa_config_pdf.loc[mf_qa_config_pdf['Test_Phase'] == phase, :]

        test_result_dict = {'Update_Date': None, 'Test_ID': None, 'Test_Value': test_value, 'Test_Result': 'Pass'}

        test_info_dict = mf_phase_qa_pdf.loc[mf_phase_qa_pdf['Test_ID'] == test_id,
        ['Test_Phase', 'Test_Description', 'Test_Priority', 'Test_Action']].to_dict('records')[0]

        test_result_dict.update(test_info_dict)

        test_result_dict['Test_ID'] = test_id
        test_result_dict['Update_Date'] = datetime.timestamp(datetime.now())
        test_result_dict['Client_Name'] = data.get_param('Client')
        test_result_dict['Process_Name'] = data.get_param('Process_Name')
        test_result_dict['Model_Name'] = data.get_param('Model_Name')
        test_result_dict['Run_Uid'] = Constants.RUN_UID

        return test_result_dict

    @staticmethod
    def append_info_in_qa_logs(test_phase:str, test_id:int, test_value):
        test_dict = QADataHandler.get_dict_with_test_info(test_phase, test_id, test_value)
        test_dict['Test_Result'] = 'Pass'
        QADataHandler.add_row_qa_logs(test_dict)

    @staticmethod
    def add_row_qa_logs(test_result_dict):
        data = Data.get_instance()
        if test_result_dict['Test_Result'] == 'Pass':
            test_result_dict['Test_Action'] = 'NA'

        mf_qa_logs_pdf = data.get_dataframe("mf_qa_logs_pdf")
        mf_qa_logs_pdf.loc[len(mf_qa_logs_pdf)] = test_result_dict

        if test_result_dict['Test_Result'] == 'Fail':
            # If test is high priority and test fails (i.e. not a default values), raise an Exception
            if test_result_dict['Test_Priority'] == 'High':
                # In case of failure check if values are default i.e. 0 or -9999
                if test_result_dict['Test_Value'] != -999999.0:
                    # Raise an invalid value error Exception
                    raise ValueError('Invalid value:' + test_result_dict['Test_Value'] +
                                     ' for test_id: ' + str(test_result_dict['Test_ID']) +
                                     ' in test_phase: ' + str(test_result_dict['Test_Phase']) +
                                     ' for test_description: ' + str(test_result_dict['Test_Description']))


