import os
import sys
import traceback

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
model_factory_dir = os.path.dirname(script_dir)
learning_dir = os.path.dirname(model_factory_dir)
#sys.path.append(learning_dir)
#print ("Adding folder to python path:"+learning_dir)
#import common.pyUtils.aktana_ml_utils as ml_utils

from aktana_ml_utils import aktana_ml_utils
import pymysql

import pandas as pd
import numpy as np
import time
import json

def getMySqlConnection(db, params):
    conn = None
    port = params.get('rds-port') if params.get('rds-port') else 3306

    if isLocal:
        #print ("Using mysql ssh tunnel forwarder using localhost")
        conn = pymysql.connect(host='127.0.0.1', user=params.get('rds-user'),
                            passwd=params.get('rds-password'),
                            port=3337, database=db)
    else:
        #print ("Using mysql direct connection")
        conn = pymysql.connect(host=params.get('rds-server'), user=params.get('rds-user'),
                            passwd=params.get('rds-password'),
                            port=port, database=db)
    return conn

def runMySqlQuery(
        query,
        params,
        db=None
):
    conn = getMySqlConnection(db, params)
    try:
        df = pd.read_sql_query(query, conn)
        print('Data Read Successfully')
        conn.close()
        return df
    except Exception as e:
        print('Error while executing mysql query:' + str(e))
        raise

def executeMySql(
        sql, params, db=None
):
    conn = getMySqlConnection(db, params)
    try:
        cur = conn.cursor()
        print("Executing sql:" + sql)
        cur.execute(sql)
        conn.commit()
        conn.close()
    except Exception as e:
        print('Error while executing mysql sql:' + str(e))
        raise

def delete_prior_workflow_setup(params, incremental):

    print ("Deleting workflow steps...")

    sql_query = f"delete from {params['rds-stagedbname']}.PAPI_Param_WorkflowStep where wfUID in ('DRL_Learning', 'DRL_Execution', 'DRL_Inc_Execution') "
    executeMySql(sql_query, params, params['rds-enginedbname'])

    if not incremental:
        print ("Deleting workflow one-time setup...")
        sql_query = f"delete from {params['rds-stagedbname']}.AMD_WorkflowActionType where wfActionTypeUID in ('DRL_Learning', 'DRL_Execution', 'DRL_Inc_Execution') "
        executeMySql(sql_query, params, params['rds-enginedbname'])

        sql_query = f"delete from {params['rds-stagedbname']}.PAPI_Param_Workflow where wfUID in ('DRL_Learning', 'DRL_Execution', 'DRL_Inc_Execution') "
        executeMySql(sql_query, params, params['rds-enginedbname'])

        sql_query = f"delete from {params['rds-stagedbname']}.AMD_WorkflowActionCategory where wfActionCategoryUID = 'DRL' "
        executeMySql(sql_query, params, params['rds-enginedbname'])


def prepare_one_time_drl_type_setup(params, workflow_prefix, job_params):

    print (f"Inserting AMD_WorkflowActionType for {workflow_prefix}...")

    sql_query = f"INSERT INTO {params['rds-stagedbname']}.AMD_WorkflowActionType "\
    f"(wfActionTypeUID, wfActionTypeName, wfActionTypeDescription, wfJobRefSuffix, wfJobRefArgs, wfJobDynamicArgs, wfActionCategoryUID, isActive, createdAt, updatedAt) "\
    f" VALUES('{workflow_prefix}', '{workflow_prefix}', '{workflow_prefix}', '$customerName-$envname-Run_DRL', '-DRL_ACTION RUN_DRL {job_params}', '', 'DRL', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP) "

    executeMySql(sql_query, params, params['rds-enginedbname'])

    print (f"Inserting PAPI_Param_Workflow for {workflow_prefix}...")

    sql_query = f"INSERT INTO {params['rds-stagedbname']}.PAPI_Param_Workflow "\
    f"(wfUID, wfName, wfTemplateName, wfTemplateDescription, wfTemplateTypeUID, isActive, isSingleInstance, failureSeverityLevel, wfTimeout, wfDurationExceedThreshold, wfDynamicArgs, limitToEnvironments, wfSequenceStrategy, createdAt, updatedAt, createdBy, updatedBy, continueOnFailure) "\
    f" VALUES('{workflow_prefix}', '{workflow_prefix}', '{workflow_prefix}', '{workflow_prefix}', 'Workflow', 1, 0, 'critical', '1h', '', '', '', 'node-first', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'appadmin', 'appadmin', 1) "

    executeMySql(sql_query, params, params['rds-enginedbname'])

def prepare_one_time_workflow_setup(params):

    print (f"Inserting AMD_WorkflowActionCategory...")

    sql_query = f"INSERT INTO {params['rds-stagedbname']}.AMD_WorkflowActionCategory "\
    f"(wfActionCategoryUID, wfActionCategoryName, wfActionCategoryDescription, isActive, createdAt, updatedAt, logKeyWords, errorMap) "\
    f" VALUES('DRL', 'DRL', 'DRL', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '', '') "

    executeMySql(sql_query, params, params['rds-enginedbname'])

    prepare_one_time_drl_type_setup(params, 'DRL_Learning', '-runMode learning -postProcMode touchpoints')
    prepare_one_time_drl_type_setup(params, 'DRL_Execution', '-runMode execution -postProcMode candidates')
    prepare_one_time_drl_type_setup(params, 'DRL_Inc_Execution', '-runMode execution -postProcMode candidates -Incremental true')

def prepare_workflow_steps(workflow_prefix, params):

    print (f"Inserting PAPI_Param_WorkflowStep for {workflow_prefix}...")

    sql_query = f"INSERT INTO {params['rds-stagedbname']}.PAPI_Param_WorkflowStep "\
    f"(wfStepUID, wfStepName, wfUID, wfActionTypeUID, wfCustomActionTypeUID, wfJobRefArgsOverride, stepOrder, failureSeverityLevel, createdAt, updatedAt, createdBy, updatedBy) "\
    f" with strategies as (select ROW_NUMBER() OVER (order by strategyId) as rowid, strategyId, name, status from {params['rds-enginedbname']}_scc.Strategy s where status not in ('DRAFT', 'EXPIRED') ) "\
    f" select concat('{workflow_prefix}_Step-',rowid), concat('{workflow_prefix}_Step-',rowid), '{workflow_prefix}', '{workflow_prefix}', null, concat('-ScenarioIdentifier ', strategyId), rowid, 'critical', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'appadmin', 'appadmin' from strategies "

    executeMySql(sql_query, params, params['rds-enginedbname'])

def main():
    start_time = time.time()
    rc = 0
    try:

        short_opt = "hc:e:a:r:ec:o:l:d"
        long_opt = ["customer=","env=","app=","region=","ecosystem=","local=","incremental="]
        cmd_argv = sys.argv

        ml_utils = aktana_ml_utils()
        cmdline_params, metadata_params = ml_utils.initialize(cmd_argv, short_opt, long_opt)
        params = ml_utils.get_params_json()
        params = json.loads(params)
        for key, value in cmdline_params.items():
            params[key] = value

        global isLocal
        isLocal = False
        if params.get("local", "false") == "true":
            isLocal = True
            print ("Using mysql tunnel connection")

        incremental = params.get("incremental", "true") == "true"
        delete_prior_workflow_setup(params, incremental)
        if not incremental:
            prepare_one_time_workflow_setup(params)

        prepare_workflow_steps("DRL_Learning", params)
        prepare_workflow_steps("DRL_Execution", params)
        prepare_workflow_steps("DRL_Inc_Execution", params)

    except Exception as e:
        rc = 1
        print(e)

    finally:
        end_time = time.time()
        exec_time = int(end_time - start_time)

if __name__ == "__main__":
    main()
