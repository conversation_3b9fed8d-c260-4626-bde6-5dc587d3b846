
import time
from abstract_model_factory.abstract_mf_module import AbstractModule
import traceback
from abc import ABC, abstractmethod


class AbstractDriver(ABC):
    """
    This is the driver for the model factory which starts the execution of the
    """
    def __init__(self,
                data,
                initializer = None,
                loader = None,
                transformer = None,
                model_drift_detector = None,
                learner = None,
                predictor = None,
                evaluator = None,
                deployer = None):
        """
        This is the constructor for the driver class. It takes in all the modules used in the model factory.
        Except Data object all the other modules are optional, if not provided then the execution of that module is skipped.

        :param data: Data object which is used to store the data throughout the process
        :param initializer: Initializer module
        :param loader: Data loader module
        :param transformer: Data transformer module
        :param model_drift_detector: Model drift detector module to check if previous model needs to be retrained
        :param learner: Model learner module
        :param predictor: Model predictor or scoring module
        :param evaluator: Result evaluator module
        :param deployer: Deployer module which writes results and models at appropriate locations
        """

        self.data = data
        self.initializer = initializer
        self.loader = loader
        self.transformer = transformer
        self.model_drift_detector = model_drift_detector
        self.learner = learner
        self.predictor = predictor
        self.evaluator = evaluator
        self.deployer = deployer

    def __run_and_time_method(self, module: AbstractModule, name):

        if module is None:
            print(f"Skipping {name} as it is not defined")
            return

        print(f"\n== Starting {name} == ")

        start = time.time()
        module.execute()
        end = time.time()

        print(f"\n--- Completed {name} in time {end-start: .3f} (secs) ---")

        self.data.set_param(f"execution_time_{module.name}", end - start)

        # Run QA module method on the module
        print(f"Starting QA module method on the {name} module")
        module.qa_module()
        print(f"Completed QA module method on the {name} module")


    def __is_model_retraining_required(self):
        is_model_training_required = True
        if self.model_drift_detector:
            is_model_training_required = self.model_drift_detector.is_model_retraining_required()
        else:
            print("Model drift detector not defined. Retraining model")
        return is_model_training_required

    def start(self):
        """
        This method starts the execution of the model factory. It calls the execute method of all the modules
        in the order of Initializer, Loader, Transformer, Learner, Predictor, Evaluator and Deployer. If any of the
        module is not defined then it skips the execution of that module. It stores the execution time of each module
        in the data object with the key as execution_time_<module_name>.It also calls the qa_module method of each
        module to run the QA module method.

        :return:
        """
        # Call all the execute methods
        rc = 0

        try:
            self.__run_and_time_method(self.initializer, "Initialization")
            self.__run_and_time_method(self.loader, "Data Loading")
            self.__run_and_time_method(self.transformer, "Data Transformation")
            if self.__is_model_retraining_required():
                self.__run_and_time_method(self.learner, "Model Learning")
            else:
                print("Skipping model learning as model retraining is not required")
            self.__run_and_time_method(self.predictor, "Model Prediction")
            self.__run_and_time_method(self.evaluator, "Model Evaluation")
            self.__run_and_time_method(self.deployer, "Model Deployment")
        except Exception as e:
            print(e)
            traceback.print_exc()
            rc = 1
        finally:
            return rc
