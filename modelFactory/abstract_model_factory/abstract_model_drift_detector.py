from abc import ABC, abstractmethod


class AbstractModelDriftDetector(ABC):
    """
    Module to detect if the existing model has drifted and won't be able to provide accurate predictions.
    """

    @abstractmethod
    def is_model_retraining_required(self) -> bool:
        """
        Check if the model needs to be retrained for the current data.
        :return: True if model needs to be retrained, False otherwise
        """
        pass