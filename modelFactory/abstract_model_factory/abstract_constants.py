from abc import ABC


class AbstractConstants(ABC):

    # Module Names for the different stages of the pipeline
    INITIALIZER_MODULE = "Init"
    LOADER_MODULE = "Load"
    TRANSFORMER_MODULE = "Transform"
    LEARNER_MODULE = "Learn"
    PREDICTOR_MODULE = "Score"
    EVALUATOR_MODULE = "Evaluate"
    DEPLOYER_MODULE = "Deploy"

    CHANNELS = ['send', 'visit', 'virtual_visit', 'phone']
    CHANNELS_ALL = CHANNELS + ['all']

    TEST_MODE = False
    LOCAL_MODE = False
    USE_PARAMIKO = False
    DEBUG_MODE = True
    RUN_UID = "test-run-local"

    AWS_ACCESS_KEY_ID = ""
    AWS_SECRET_ACCESS_KEY = ""
    AWS_SESSION_TOKEN = ""
