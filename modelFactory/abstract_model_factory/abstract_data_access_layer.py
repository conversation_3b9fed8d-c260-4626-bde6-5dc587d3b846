import tempfile
from abc import ABC

import boto3
import pandas as pd

from abstract_model_factory.abstract_constants import AbstractConstants


class AbstractDataAccessLayer(ABC):

    @staticmethod
    def get_s3_client():
        ACCESS_KEY = AbstractConstants.AWS_ACCESS_KEY_ID
        SECRET_KEY = AbstractConstants.AWS_SECRET_ACCESS_KEY
        SESSION_TOKEN = AbstractConstants.AWS_SESSION_TOKEN

        if ACCESS_KEY != "":
            s3 = boto3.client('s3',
                              aws_access_key_id=ACCESS_KEY,
                              aws_secret_access_key=SECRET_KEY,
                              aws_session_token=SESSION_TOKEN)

        else:
            s3 = boto3.client('s3')

        return s3

    def write_model_to_s3(self, model, s3_path):
        s3_bucket = s3_path.split('/')[2]
        model_path = '/'.join(s3_path.split('/')[3:])
        target_name = s3_path.split('/')[-1]

        s3 = self.get_s3_client()

        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/{target_name}"
            model.save(temp_path)
            s3.upload_file(temp_path, s3_bucket, model_path)

        print("Uploaded model to : " + s3_path)


    def write_csv_to_s3(self, pdf, s3_path):
        s3 = self.get_s3_client()

        s3_bucket = s3_path.split('/')[2]
        csv_path = '/'.join(s3_path.split('/')[3:])

        csv_string = pdf.to_csv(index=False)
        s3.put_object(Bucket=s3_bucket, Key=csv_path, Body=csv_string)

        print("Uploaded latest.csv to : " + s3_path)

    def write_predictions_to_s3(self, predictions, s3_path):

        s3_bucket = s3_path.split('/')[2]
        predictions_path = '/'.join(s3_path.split('/')[3:])
        latest_csv_path = '/'.join(s3_path.split('/')[:-1]) + '/latest.csv'
        target_name = s3_path.split('/')[-1]

        s3 = self.get_s3_client()

        with tempfile.TemporaryDirectory() as tempdir:
            temp_path = f"{tempdir}/{target_name}"
            predictions.to_parquet(temp_path)
            s3.upload_file(temp_path, s3_bucket, predictions_path)

        latest_pdf = pd.DataFrame({'predictions_path': [s3_path]})
        self.write_csv_to_s3(latest_pdf, latest_csv_path)
        print("Uploaded predictions to : " + s3_path)
