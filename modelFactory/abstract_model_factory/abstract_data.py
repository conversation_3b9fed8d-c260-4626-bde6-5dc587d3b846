import pandas as pd
import numpy as np

class AbstractData:
    """
    This class holds data flowing through workflow across different modules in Model Factory.
    """
    """
    1. Parameters
    2. Metrics
    3. DataFrames (real Data)
    """
    __instance = None

    @classmethod
    def get_instance(cls):
        if (cls.__instance == None):
            __instance = cls()
        return cls.__instance

    @classmethod
    def set_instance(cls, instance):
        cls.__instance = instance

    def __init__(self):
        if (AbstractData.__instance != None):
            raise Exception("This is a singleton - create using getInstance() instead")
        else:
            AbstractData.__instance = self
            self.params = {}
            self.dataframes = {}
            self.metrics = {}
            self.models = {}
            self.numpy_array = {}

    def set_param(self, name, value):
        assert type(value) != pd.DataFrame
        self.params[name] = value

    def get_param(self, name, default_value=None):
        value = default_value
        if name in self.params :
            value = self.params[name]

        return value

    def set_metric(self, name, value):
        assert type(value) != pd.DataFrame
        self.metrics[name] = value

    def get_metric(self, name, default_value=None):
        value = default_value
        if name in self.metrics :
            value = self.metrics[name]

        return value

    def set_dataframe(self, name, dataframe):
        assert type(dataframe) == pd.DataFrame
        self.dataframes[name] = dataframe

    def get_dataframe(self, name):
        #assert name in self.dataframes, f"DataFrame {name} does not exists"
        dataframe = pd.DataFrame()
        if name in self.dataframes:
            dataframe = self.dataframes[name]
        else:
            print(f"DataFrame {name} does not exists")

        return dataframe

    def set_model(self, name, model):
        assert type(model) != pd.DataFrame
        assert type(model) != str

        self.models[name] = model

    def get_model(self, name):
        #assert name in self.models, f"Model {name} does not exists"
        model = None
        if name in self.models:
            model = self.models[name]

        return model

    def set_numpy(self, name, arr):
        assert type(arr) == np.ndarray

        self.numpy_array[name] = arr

    def get_numpy(self, name):
        arr = None
        if name in self.numpy_array:
            arr = self.numpy_array[name]
        else:
            print(f"Numpy array {name} does not exists")

        return arr
