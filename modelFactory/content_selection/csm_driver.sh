#!/bin/bash


echo "Python version : $(python3 --version)"

# Print CP Install dir
INSTALL_DIR=`dirname $0`
INSTALL_DIR=`readlink -f "$INSTALL_DIR"`
echo "Install directory = $INSTALL_DIR"

APP_PARAM="--app CONTENT_SELECTION"

#echo "Install python dependencies from $CP_INSTALL_DIR/requirements.txt"
#pip3 install -r $CP_INSTALL_DIR/requirements.txt
#pip3 install imbalanced-learn==0.11.0
pip3 install shap==0.48.0

# Add modelFactory folder to PYTHONPATH for imports to work
export PYTHONPATH="${PYTHONPATH}:$(dirname "$INSTALL_DIR")"

# Add common python utils to PYTHONPATH used to get parameters from Aktana metadata
export PYTHONPATH="$INSTALL_DIR/../../common/pyUtils:${PYTHONPATH}"
echo "Value in PYTHONPATH=$PYTHONPATH"


echo "Running Content Selection model job...python3 $INSTALL_DIR/main.py $@ $APP_PARAM"
eval "python3 $INSTALL_DIR/main.py $@ $APP_PARAM"
rc=$?


echo "Returning from content selection model driver with rc=$rc"
exit $rc

