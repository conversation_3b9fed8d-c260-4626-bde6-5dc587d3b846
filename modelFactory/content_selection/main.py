from content_selection.constants import Constants
from content_selection.content_selection_driver import ContentSelectionDriver
from content_selection.data import Data
from content_selection.deployer import Deployer
from content_selection.initializer import Initializer
from content_selection.learner import Learner
from content_selection.loader import Loader
from content_selection.model_drift_detector import ModelDriftDetector
from content_selection.predictor import Predictor
from content_selection.transformer import Transformer
from content_selection.evaluator import Evaluator
import traceback
import statsd
import time
import argparse

expected_args = [
    {"name": "--customer", "default": "DEFAULT"},
    {"name": "--env", "default": "DEFAULT"},
    {"name": "--region", "default": "DEFAULT"},
]

if __name__ == "__main__" :
    start_time = time.time()
    # get cmdline args
    parser = argparse.ArgumentParser(description='CP arguments')
    for arg in expected_args:
        thetype = arg.get('type')
        parser.add_argument(arg.get('name'), help=arg.get('help'), required=arg.get('required'),
                            default=arg.get('default'), type=thetype if thetype is None else locate(thetype))
    args, _ = parser.parse_known_args()
    rc = 0
    try:
        content_selection_driver = ContentSelectionDriver(data=Data.get_instance(),
                                                            initializer=Initializer(Constants.INITIALIZER_MODULE),
                                                            loader=Loader(Constants.LOADER_MODULE),
                                                            transformer=Transformer(Constants.TRANSFORMER_MODULE),
                                                            model_drift_detector=ModelDriftDetector(),
                                                            learner=Learner(Constants.LEARNER_MODULE),
                                                            evaluator=Evaluator(Constants.EVALUATOR_MODULE),
                                                            predictor=Predictor(Constants.PREDICTOR_MODULE),
                                                            deployer=Deployer(Constants.DEPLOYER_MODULE))
        # Execute the driver
        rc = content_selection_driver.start()

        exit(rc)

    except Exception as e:
        print(e)
        traceback.print_exc()
        rc = 1
    finally:
        end_time = time.time()
        exec_time = int(end_time - start_time)
        # write observability metrics
        statsd_server = args.region + "statsd.aktana.com"
        if "eks" not in args.region:
            statsd_server = args.region[:2] + "statsd.aktana.com"
        statsd_port = '9125'

        # metric prefix
        metric_prefix = 'type.{TYPE}.cmpny.' + args.customer + '.regn.' + args.region + '.cntry.none' + '.env.' + args.env + \
                        '.apptype.CP' + '.cmpnt.ENGINE' + '.metric'

        # gauge metrics
        statsd_client = statsd.StatsClient(statsd_server, statsd_port,
                                            prefix=metric_prefix.format(TYPE="g"))
        statsd_client.gauge("job_status", rc)
        print(f"Job status: {rc}. 0: SUCCESS, 1: FAIL")
        statsd_client.gauge("exec_time", exec_time)
        print(f"Execution time: {exec_time}")

        # counter metrics
        statsd_client = statsd.StatsClient(statsd_server, statsd_port,
                                            prefix=metric_prefix.format(TYPE="c"))
        if rc:
            statsd_client.incr("fail_cnt", 1)
            print("Failure count increased by 1")
        else:
            statsd_client.incr("success_cnt", 1)
            print("Success count increased by 1")



        exit(rc)
        