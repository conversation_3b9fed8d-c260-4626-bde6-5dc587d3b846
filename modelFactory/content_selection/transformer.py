from datetime import datetime

from abstract_model_factory.abstract_transformer import AbstractTransformer
from content_selection.constants import Constants
from content_selection.data import Data
import pandas as pd
import numpy as np
from scipy import stats
from sklearn.metrics import mutual_info_score

from content_selection.qa_data_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from imblearn.over_sampling import SMOTENC

class Transformer(AbstractTransformer):

    def qa_module(self):
        pass

    def assess_data_quality(self):
        """
        Assess training data quality and learnability for content selection model.

        Performs 4 core assessments with qualitative evaluation:
        1. Target Distribution: Engagement rate and class balance analysis
        2. Feature Cardinality: Analysis of high-cardinality risks and overfitting potential
        3. Feature Predictive Power: Mutual information, information gain, and engagement variance
        4. Feature Correlation: Identification of redundant categorical features

        Returns:
            dict: {
                'overall_assessment': str (Excellent/Good/Fair/Poor),
                'suitable_for_training': bool,
                'assessments': dict of component assessments with reasoning,
                'summary': str description of overall data quality,
                'recommendations': list of actionable improvement suggestions
            }
        """
        data = Data.get_instance()
        df = data.get_dataframe("training_df")

        if df is None or df.empty:
            return {
                'quality_score': 0,
                'suitable_for_training': False,
                'issues': ['No training data available'],
                'recommendations': ['Load training data before assessment']
            }

        # Create target variable for analysis
        df_analysis = df.copy()
        # df_analysis['engagement'] = df_analysis['openCount'] + df_analysis['clickCount']
        # df_analysis['target'] = df_analysis['engagement'].apply(lambda x: 1 if x > 0 else 0)

        assessments = {}

        # 1. Target Distribution Analysis
        target_assessment = self._analyze_target_distribution(df_analysis)
        assessments['target_distribution'] = target_assessment

        # 2. Feature Cardinality Analysis
        cardinality_assessment = self._analyze_feature_cardinality(df_analysis)
        assessments['cardinality'] = cardinality_assessment

        # 3. Feature Predictive Power Analysis
        feature_assessment = self._analyze_feature_relationships(df_analysis)
        assessments['predictive_power'] = feature_assessment

        # 4. Feature Correlation Analysis
        correlation_assessment = self._analyze_feature_correlations(df_analysis)
        assessments['correlation'] = correlation_assessment

        # Determine overall suitability
        overall_assessment = self._determine_overall_suitability(assessments)

        return {
            'overall_assessment': overall_assessment['quality'],
            'suitable_for_training': overall_assessment['suitable'],
            'assessments': assessments,
            'summary': overall_assessment['summary'],
            'recommendations': overall_assessment['recommendations']
        }

    def _analyze_target_distribution(self, df):
        """Analyze target variable distribution and balance."""
        engagement_rate = df['target'].mean()
        class_balance = df['target'].value_counts(normalize=True)
        minority_class_pct = min(class_balance)

        # Determine quality level
        if engagement_rate < 0.005:
            quality = "Poor"
            reasoning = f"Extremely low engagement rate ({engagement_rate:.1%}) indicates insufficient positive examples"
            recommendations = ["Expand data collection time window", "Verify data quality and target definition"]
        elif engagement_rate < 0.01:
            quality = "Poor"
            reasoning = f"Very low engagement rate ({engagement_rate:.1%}) may lead to poor model performance"
            recommendations = ["Consider expanding data collection period", "Apply oversampling techniques"]
        elif engagement_rate < 0.02:
            quality = "Fair"
            reasoning = f"Low engagement rate ({engagement_rate:.1%}) but sufficient for training"
            recommendations = ["Monitor model performance closely", "Consider SMOTE for class balance"]
        elif engagement_rate > 0.5:
            quality = "Fair"
            reasoning = f"Unusually high engagement rate ({engagement_rate:.1%}) - verify target calculation"
            recommendations = ["Verify target variable definition", "Check for data leakage"]
        elif minority_class_pct < 0.005:
            quality = "Poor"
            reasoning = f"Severe class imbalance ({minority_class_pct:.1%} minority class)"
            recommendations = ["Apply SMOTE or other oversampling", "Collect more positive examples"]
        elif minority_class_pct < 0.01:
            quality = "Fair"
            reasoning = f"High class imbalance ({minority_class_pct:.1%} minority class) but manageable"
            recommendations = ["Consider oversampling techniques", "Use appropriate evaluation metrics"]
        else:
            quality = "Good"
            reasoning = f"Healthy engagement rate ({engagement_rate:.1%}) and class balance"
            recommendations = ["Proceed with standard training approach"]

        return {
            'quality': quality,
            'engagement_rate': engagement_rate,
            'minority_class_pct': minority_class_pct,
            'reasoning': reasoning,
            'recommendations': recommendations
        }

    def _analyze_feature_relationships(self, df):
        """
        Analyze predictive power of categorical features for binary target.

        Thresholds for predictive utility:
        - Composite Score >0.05: Strong predictive power
        - Composite Score >0.02: Moderate predictive power
        - Composite Score >0.01: Weak but usable predictive power
        - Composite Score ≤0.01: No meaningful predictive power

        Composite score combines:
        - Mutual Information (30%): >0.1 = strong, >0.05 = moderate
        - Information Gain (30%): >0.1 = strong, >0.05 = moderate
        - Engagement Variance (40%): >0.3 CV = strong, >0.15 CV = moderate
        """
        STRONG_THRESHOLD = 0.05
        MODERATE_THRESHOLD = 0.02
        WEAK_THRESHOLD = 0.01

        total_features = 0
        strong_features = 0
        moderate_features = 0
        weak_features = 0
        useless_features = 0
        feature_scores = {}

        for feature in Constants.CATEGORICAL_FEATURES:
            if feature in df.columns and df[feature].notna().sum() > 0:
                total_features += 1
                try:
                    predictive_score = self._calculate_predictive_power(df, feature)
                    cardinality = df[feature].nunique()

                    # Apply cardinality penalty
                    cardinality_penalty = self._calculate_cardinality_penalty(cardinality, len(df))
                    adjusted_score = predictive_score * (1 - cardinality_penalty)

                    feature_scores[feature] = {
                        'raw_score': predictive_score,
                        'adjusted_score': adjusted_score,
                        'cardinality': cardinality
                    }

                    # Categorize by adjusted score
                    if adjusted_score > STRONG_THRESHOLD:
                        strong_features += 1
                    elif adjusted_score > MODERATE_THRESHOLD:
                        moderate_features += 1
                    elif adjusted_score > WEAK_THRESHOLD:
                        weak_features += 1
                    else:
                        useless_features += 1

                except Exception as e:
                    print(f"Error calculating predictive power for feature {feature}: {e}")
                    continue

        print(f"Feature scores: {feature_scores}")

        if total_features == 0:
            return self._create_predictive_assessment("Poor", "No categorical features available",
                                                    ["Load categorical features"], {})

        # Calculate ratios
        useful_features = strong_features + moderate_features + weak_features
        useful_ratio = useful_features / total_features
        strong_ratio = strong_features / total_features

        # Determine quality based on thresholds
        quality_thresholds = [
            (0.6, 0.3, "Excellent", "Most features show strong predictive power"),
            (0.4, 0.2, "Good", "Good mix of predictive features available"),
            (0.2, 0.1, "Fair", "Limited but usable predictive features"),
            (0.0, 0.0, "Poor", "Very few features show meaningful predictive power")
        ]

        for useful_min, strong_min, quality, base_reasoning in quality_thresholds:
            if useful_ratio >= useful_min and strong_ratio >= strong_min:
                reasoning = f"{base_reasoning} ({strong_features} strong, {moderate_features} moderate, {weak_features} weak, {useless_features} useless out of {total_features})"
                break

        recommendations = self._get_predictive_recommendations(quality, strong_features, total_features)

        # Show top features
        if feature_scores:
            top_features = sorted(feature_scores.items(), key=lambda x: x[1]['adjusted_score'], reverse=True)[:3]
            print(f"Top predictive features: {[(f, round(s['adjusted_score'], 4)) for f, s in top_features]}")

        return self._create_predictive_assessment(quality, reasoning, recommendations, feature_scores)

    def _create_predictive_assessment(self, quality, reasoning, recommendations, feature_scores):
        """Create standardized predictive assessment result."""
        return {
            'quality': quality,
            'reasoning': reasoning,
            'recommendations': recommendations,
            'feature_scores': feature_scores
        }

    def _get_predictive_recommendations(self, quality, strong_features, total_features):
        """Get recommendations based on predictive power quality."""
        recommendations_map = {
            'Excellent': ["Proceed with training", "Consider feature selection to reduce complexity"],
            'Good': ["Proceed with training", "Monitor model performance"],
            'Fair': ["Consider feature engineering", "Explore behavioral features", "Review data collection"],
            'Poor': ["Review feature engineering strategy", "Investigate data quality", "Consider domain expertise"]
        }
        # Parameters strong_features and total_features available for future enhancements
        return recommendations_map.get(quality, [])

    def _analyze_feature_correlations(self, df):
        """
        Analyze correlations between categorical features to identify redundant candidates.

        Uses Cramér's V correlation coefficient for categorical variables:
        - >0.8: Very high correlation (strong redundancy candidates)
        - >0.6: High correlation (moderate redundancy candidates)
        - >0.4: Moderate correlation (monitor for potential redundancy)
        - ≤0.4: Low correlation (acceptable)
        """
        HIGH_CORRELATION_THRESHOLD = 0.8
        MODERATE_CORRELATION_THRESHOLD = 0.6
        MONITOR_CORRELATION_THRESHOLD = 0.4

        available_features = [f for f in Constants.CATEGORICAL_FEATURES
                            if f in df.columns and df[f].notna().sum() > 0]

        if len(available_features) < 2:
            return {
                'quality': 'Excellent',
                'reasoning': 'Insufficient features for correlation analysis',
                'recommendations': ['No correlation analysis needed'],
                'high_correlations': [],
                'moderate_correlations': [],
                'monitor_correlations': []
            }

        high_correlations = []
        moderate_correlations = []
        monitor_correlations = []

        # Calculate pairwise Cramér's V correlations
        for i, feature1 in enumerate(available_features):
            for feature2 in available_features[i+1:]:
                try:
                    cramers_v = self._calculate_cramers_v(df[feature1], df[feature2])

                    if cramers_v > HIGH_CORRELATION_THRESHOLD:
                        high_correlations.append((feature1, feature2, cramers_v))
                    elif cramers_v > MODERATE_CORRELATION_THRESHOLD:
                        moderate_correlations.append((feature1, feature2, cramers_v))
                    elif cramers_v > MONITOR_CORRELATION_THRESHOLD:
                        monitor_correlations.append((feature1, feature2, cramers_v))

                except Exception:
                    continue

        # Determine quality and recommendations
        if high_correlations:
            quality = "Fair"
            reasoning = f"Found {len(high_correlations)} highly correlated feature pairs (>0.8), here are the pairs: {high_correlations}"
            recommendations = [
                "Consider removing one feature from highly correlated pairs",
                "Evaluate which features provide more predictive value",
                "Test model performance with and without redundant features"
            ]
        elif moderate_correlations:
            quality = "Good"
            reasoning = f"Found {len(moderate_correlations)} moderately correlated feature pairs (>0.6)"
            recommendations = [
                "Monitor moderately correlated features for redundancy",
                "Consider feature selection if model complexity is high"
            ]
        elif monitor_correlations:
            quality = "Good"
            reasoning = f"Found {len(monitor_correlations)} feature pairs with moderate correlation (>0.4)"
            recommendations = ["Feature correlations are at acceptable levels"]
        else:
            quality = "Excellent"
            reasoning = "No significant feature correlations detected"
            recommendations = ["Feature independence is excellent for training"]

        return {
            'quality': quality,
            'reasoning': reasoning,
            'recommendations': recommendations,
            'high_correlations': high_correlations,
            'moderate_correlations': moderate_correlations,
            'monitor_correlations': monitor_correlations
        }

    def _calculate_cramers_v(self, x, y):
        """Calculate Cramér's V correlation coefficient for categorical variables."""
        # Create contingency table
        contingency_table = pd.crosstab(x, y)

        # Calculate chi-square statistic
        chi2 = stats.chi2_contingency(contingency_table)[0]

        # Calculate Cramér's V
        n = contingency_table.sum().sum()
        min_dim = min(contingency_table.shape) - 1

        if min_dim == 0 or n == 0:
            return 0.0

        cramers_v = np.sqrt(chi2 / (n * min_dim))
        return min(cramers_v, 1.0)  # Cap at 1.0

    def _calculate_predictive_power(self, df, feature):
        """
        Calculate predictive power of a categorical feature for binary target.

        Combines three metrics:
        1. Mutual Information: How much information the feature provides about target
        2. Information Gain: Entropy reduction when splitting by feature
        3. Engagement Rate Variance: Practical difference in engagement across categories

        Returns:
            float: Composite predictive power score (0-1 scale)
        """
        # Clean data for analysis
        feature_data = df[feature] # .fillna('other')
        target_data = df['target']

        # Remove rows where both feature and target are missing
        valid_mask = feature_data.notna() & target_data.notna()
        feature_clean = feature_data[valid_mask]
        target_clean = target_data[valid_mask]

        if len(feature_clean) < 10:  # Need minimum data
            return 0.0

        # 1. Mutual Information (normalized)
        try:
            mi_score = mutual_info_score(feature_clean.astype(str), target_clean)
            # Normalize by target entropy (max possible MI)
            target_entropy = -sum(p * np.log2(p) for p in [target_clean.mean(), 1-target_clean.mean()] if p > 0)
            normalized_mi = mi_score / target_entropy if target_entropy > 0 else 0
        except:
            normalized_mi = 0

        # 2. Information Gain (entropy reduction)
        try:
            # Calculate weighted entropy after split
            total_entropy = self._calculate_entropy(target_clean)
            weighted_entropy = 0

            for category in feature_clean.unique():
                category_mask = feature_clean == category
                category_targets = target_clean[category_mask]
                if len(category_targets) > 0:
                    category_entropy = self._calculate_entropy(category_targets)
                    weight = len(category_targets) / len(target_clean)
                    weighted_entropy += weight * category_entropy

            information_gain = total_entropy - weighted_entropy
            # Normalize by total entropy
            normalized_ig = information_gain / total_entropy if total_entropy > 0 else 0
        except:
            normalized_ig = 0

        # 3. Engagement Rate Variance (practical predictive value)
        try:
            engagement_by_category = df.groupby(feature)['target'].agg(['mean', 'count'])
            # Only consider categories with sufficient data
            sufficient_data = engagement_by_category[engagement_by_category['count'] >= 5]

            if len(sufficient_data) > 1:
                engagement_rates = sufficient_data['mean']
                overall_rate = target_clean.mean()

                # Calculate coefficient of variation relative to overall rate
                rate_variance = engagement_rates.var()
                normalized_variance = np.sqrt(rate_variance) / overall_rate if overall_rate > 0 else 0
            else:
                normalized_variance = 0
        except:
            normalized_variance = 0

        # Combine metrics with weights emphasizing practical predictive value
        composite_score = (
            0.3 * normalized_mi +           # Information content
            0.3 * normalized_ig +           # Decision tree utility
            0.4 * normalized_variance       # Practical engagement differences
        )

        return min(composite_score, 1.0)  # Cap at 1.0

    def _calculate_cardinality_penalty(self, cardinality, total_samples):
        """Calculate penalty for high cardinality features."""
        # Penalty based on cardinality relative to sample size
        if cardinality <= 10:
            return 0.0  # No penalty for low cardinality
        elif cardinality <= 50:
            return 0.1  # Small penalty for moderate cardinality
        elif cardinality <= 100:
            return 0.3  # Moderate penalty
        elif cardinality > total_samples * 0.1:
            return 0.7  # Heavy penalty if cardinality > 10% of samples
        else:
            return 0.5  # Standard penalty for high cardinality

    def _analyze_feature_cardinality(self, df):
        """
        Analyze cardinality issues across categorical features.

        Thresholds:
        - Severe Risk: >10% of sample size (overfitting risk)
        - High Risk: >100 categories (memory/performance issues)
        - Moderate Risk: >50 categories (manageable but monitor)
        - Low Risk: ≤50 categories (appropriate for training)
        """
        total_samples = len(df)
        severe_risk_features = []
        high_risk_features = []
        moderate_risk_features = []

        for feature in Constants.CATEGORICAL_FEATURES:
            if feature in df.columns and df[feature].notna().sum() > 0:
                cardinality = df[feature].nunique()

                # Apply specific thresholds
                if cardinality > total_samples * 0.1:  # >10% of samples
                    severe_risk_features.append(f"{feature} ({cardinality} categories)")
                elif cardinality > 100:  # >100 categories
                    high_risk_features.append(f"{feature} ({cardinality} categories)")
                elif cardinality > 50:  # >50 categories
                    moderate_risk_features.append(f"{feature} ({cardinality} categories)")

        # Determine quality based on risk levels
        quality_map = {
            'severe': ("Poor", "Features with >10% sample size cardinality pose severe overfitting risk"),
            'high': ("Fair", "Features with >100 categories may cause memory/performance issues"),
            'moderate': ("Good", "Features with >50 categories are manageable but should be monitored"),
            'none': ("Excellent", "All features have appropriate cardinality (≤50 categories)")
        }

        if severe_risk_features:
            quality, reasoning = quality_map['severe']
            recommendations = ["Group rare categories into 'other'", "Apply strong regularization", "Consider feature selection"]
        elif high_risk_features:
            quality, reasoning = quality_map['high']
            recommendations = ["Group rare categories", "Monitor memory usage", "Use sparse encoding"]
        elif moderate_risk_features:
            quality, reasoning = quality_map['moderate']
            recommendations = ["Monitor rare categories", "Consider grouping if needed"]
        else:
            quality, reasoning = quality_map['none']
            recommendations = ["Cardinality levels are appropriate"]

        return {
            'quality': quality,
            'reasoning': reasoning,
            'recommendations': recommendations,
            'severe_risk_features': severe_risk_features,
            'high_risk_features': high_risk_features,
            'moderate_risk_features': moderate_risk_features
        }

    def _calculate_entropy(self, target_series):
        """Calculate entropy of binary target variable."""
        if len(target_series) == 0:
            return 0

        p1 = target_series.mean()
        p0 = 1 - p1

        entropy = 0
        if p1 > 0:
            entropy -= p1 * np.log2(p1)
        if p0 > 0:
            entropy -= p0 * np.log2(p0)

        return entropy

    def _determine_overall_suitability(self, assessments):
        """Determine overall data suitability based on component assessments."""
        # Extract quality levels
        qualities = {component: assessment['quality'] for component, assessment in assessments.items()}

        # Quality ranking and weights
        quality_ranks = {'Excellent': 4, 'Good': 3, 'Fair': 2, 'Poor': 1}
        weights = {
            'target_distribution': 0.40,
            'cardinality': 0.30,
            'predictive_power': 0.20,
            'correlation': 0.10
        }

        # Calculate weighted score
        weighted_score = sum(quality_ranks[qualities[component]] * weights[component]
                           for component in qualities)

        # Map score to quality levels
        quality_thresholds = [
            (3.5, "Excellent", True, "Data is excellent for model training"),
            (2.8, "Good", True, "Data is suitable for model training"),
            (2.0, "Fair", True, "Data can be used for training but expect limited performance"),
            (0.0, "Poor", False, "Data quality issues make training inadvisable")
        ]

        for threshold, quality, suitable, summary in quality_thresholds:
            if weighted_score >= threshold:
                break

        # Collect recommendations (prioritize critical issues)
        all_recommendations = []

        # Add critical warnings first
        if qualities['target_distribution'] == "Poor":
            all_recommendations.append("CRITICAL: Review data collection strategy - target distribution is problematic")
        elif qualities['cardinality'] == "Poor":
            all_recommendations.append("PRIORITY: Address high-cardinality features to prevent overfitting")

        # Add component-specific recommendations
        for component in ['target_distribution', 'cardinality', 'predictive_power', 'correlation']:
            all_recommendations.extend(assessments[component]['recommendations'])

        return {
            'quality': quality,
            'suitable': suitable,
            'summary': summary,
            'recommendations': list(dict.fromkeys(all_recommendations))[:8]  # Top 8 unique recommendations
        }



    def smote(self):
        data = Data.get_instance()
        df = data.get_dataframe("training_df")
        categorical_features = ['state']
        smote = SMOTENC(categorical_features=categorical_features, sampling_strategy='minority')
        df = smote.fit_resample(df, df['target'])
        data.set_dataframe("training_df", df)

    def prepare_training_data(self):
        data = Data.get_instance()
        df = data.get_dataframe("training_df")
        df = self.process_categorical_features(df)
        df = self.get_targets(df)
        # self.smote()
        data.set_dataframe("training_df", df)
    
    def process_categorical_features(self, df):
        # process categorical features without grouping infrequent values
        for col in Constants.CATEGORICAL_FEATURES:
            if col not in df.columns:
                continue
            df[col] = df[col].fillna('NA').astype(str)
            # Get all unique categories without filtering by frequency
            all_categories = df[col].unique().tolist()
            cat_dtype = pd.CategoricalDtype(categories=all_categories, ordered=False)
            df[col] = df[col].astype(cat_dtype)

        return df
    
    def get_targets(self, df):
        # data = Data.get_instance()
        df['engagement'] = df['openCount'] + df['clickCount']
        df['target'] = df['engagement'].apply(lambda x: 1 if x > 0 else 0)
        df = df.drop(['openCount', 'clickCount', 'engagement'], axis=1)
        return df
    
    def prepare_prediction_data(self):
        data = Data.get_instance()
        prediction_df = data.get_dataframe("prediction_df")
        prediction_df = self.process_categorical_features(prediction_df)
        data.set_dataframe("prediction_df", prediction_df)
        
    
    def execute(self):
        """
        Execute method for the transformer.
        This method is responsible for transforming the data read from the data access layer
        into the format required by the model.
        """
        if Constants.EXECUTION_MODE:
            self.prepare_prediction_data()
        if Constants.LEARNING_MODE:
            self.prepare_training_data()

            # Assess data quality after preparing training data
            quality_assessment = self.assess_data_quality()

            print("=== DATA QUALITY ASSESSMENT ===")
            print(f"Overall Assessment: {quality_assessment['overall_assessment']}")
            print(f"Suitable for Training: {quality_assessment['suitable_for_training']}")
            print(f"Summary: {quality_assessment['summary']}")

            print("\nComponent Assessments:")
            for component, assessment in quality_assessment['assessments'].items():
                print(f"  • {component.replace('_', ' ').title()}: {assessment['quality']}")
                print(f"    Reasoning: {assessment['reasoning']}")

            if quality_assessment['recommendations']:
                print("\nRecommendations:")
                for i, rec in enumerate(quality_assessment['recommendations'][:5], 1):  # Show top 5
                    print(f"  {i}. {rec}")

            print("=" * 50)

            # Store assessment for later use
            data = Data.get_instance()
            data.set_param("data_quality_assessment", quality_assessment)

        

        

