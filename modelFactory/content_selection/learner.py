
import pandas as pd
import numpy as np
import warnings
import time
warnings.filterwarnings('ignore')

from abstract_model_factory.abstract_learner import Abstract<PERSON>earner
from content_selection.data import Data
from sklearn.model_selection import train_test_split, StratifiedKFold, RandomizedSearchCV
from sklearn.metrics import roc_auc_score
from xgboost import XGBClassifier
from content_selection.constants import Constants


class Learner(AbstractLearner):

    def execute(self):
        if Constants.LEARNING_MODE:

            
            """Simplified execute method with working SHAP analysis"""
            print("Executing Content Selection Learner")

            data = Data.get_instance()
            training_df = data.get_dataframe("training_df")
            
            training_df = training_df.sample(n=100000, random_state=111)

            # Sample data
            # training_df = training_df.sample(n=300000, random_state=111)
            # training_df = training_df.reset_index(drop=True)

            target = 'target'
            col_removal_list = ['accountId', 'target', 'fragmentId']
            col_removal_list = [col for col in col_removal_list if col in training_df.columns]

            # Calculate class weights
            pos_n = training_df[training_df[target] == 1][target].count()
            neg_n = training_df[training_df[target] == 0][target].count()
            pos_weight = neg_n / (pos_n + 1e-8)
            print(f"Target distribution - Negative: {neg_n}, Positive: {pos_n}, Pos weight: {pos_weight:.3f}")

            # === PHASE 1: DATA SPLITTING ===
            # Three-way split: 60% train, 20% validation, 20% test
            X_temp, X_test_20, y_temp, y_test_20 = train_test_split(
                training_df.drop(col_removal_list, axis=1),
                training_df[target].astype(int),
                stratify=training_df[target],
                test_size=0.2,
                random_state=111
            )

            X_train_60, X_val_20, y_train_60, y_val_20 = train_test_split(
                X_temp, y_temp,
                stratify=y_temp,
                test_size=0.25,  # 0.25 of 80% = 20% of original
                random_state=111
            )

            print(f"Data splits - Train: {len(X_train_60)} (60%), Validation: {len(X_val_20)} (20%), Test: {len(X_test_20)} (20%)")

            # === PHASE 2: HYPERPARAMETER SEARCH ===
            # Enhanced hyperparameter grid targeting improved generalization and performance
            # Total combinations: ~288 (targeting 200-300 range for efficient search)
            params = {
                # Regularization parameters - key for reducing overfitting
                'reg_alpha': [0.1, 0.5, 1.0],              # L1 regularization (4 values)
                'reg_lambda': [0.5, 1.0, 2.0, 4.0],             # L2 regularization (4 values)
                'min_child_weight': [1, 3, 7, 15],              # Minimum sum of instance weight (4 values)

                # Tree structure parameters - balance complexity and performance
                'max_depth': [4, 6, 8],                         # Tree depth (3 values)
                'subsample': [0.7, 0.8, 0.9],                  # Row sampling (3 values)
                'colsample_bytree': [0.7, 0.8, 0.9],           # Column sampling (3 values)

                # Learning parameters - optimize convergence
                'learning_rate': [0.05, 0.1, 0.15],            # Step size (3 values)
                'n_estimators': [600, 800, 1000],              # Number of trees (3 values)

                # Class balance - critical for imbalanced data
                'scale_pos_weight': [0.7 * pos_weight, pos_weight, 1.3 * pos_weight],  # (3 values)

                # Fixed parameters for consistency
                'objective': ['binary:logistic'],
                'booster': ['gbtree']
            }

            # Hyperparameter search using only training data (60% of original)
            xgb = XGBClassifier(
                enable_categorical=True,
                tree_method='hist',
                random_state=111,
                eval_metric='auc'
            )

            # Use smaller sample for hyperparameter search if needed
            if len(X_train_60) > 100000:
                sample_idx = np.random.choice(len(X_train_60), 100000, replace=False)
                X_train_hp = X_train_60.iloc[sample_idx]
                y_train_hp = y_train_60.iloc[sample_idx]
            else:
                X_train_hp = X_train_60
                y_train_hp = y_train_60

            skf = StratifiedKFold(n_splits=4, shuffle=True, random_state=111)

            # Calculate total possible combinations and set search iterations
            total_combinations = np.prod([len(v) for v in params.values() if isinstance(v, list)])
            search_iterations = min(30, int(total_combinations * 0.8))  # Target 200-300 range

            print(f"Total parameter combinations: {total_combinations}")
            print(f"Search iterations: {search_iterations}")

            random_search = RandomizedSearchCV(
                xgb,
                param_distributions=params,
                n_iter=search_iterations,
                scoring='roc_auc',
                n_jobs=-1,
                cv=skf,
                random_state=111,
                verbose=1
            )

            print(f"Starting hyperparameter search with {search_iterations} iterations...")
            start_time = time.time()
            random_search.fit(X_train_hp, y_train_hp)
            search_time = time.time() - start_time

            print(f"Hyperparameter search completed in {search_time/60:.1f} minutes")
            print(f"Best CV AUC: {random_search.best_score_:.4f}")

            print(f"\n{'='*50}")
            print("OPTIMAL HYPERPARAMETERS FOUND")
            print(f"{'='*50}")
            best_params = random_search.best_params_
            for param, value in sorted(best_params.items()):
                if param not in ['objective', 'booster']:  # Skip fixed parameters
                    print(f"{param:20s}: {value}")
            print(f"{'='*50}")

            # === PHASE 3: MODEL SELECTION ===
            # Evaluate best model on validation set (20% of original data)
            best_model = random_search.best_estimator_
            val_score = roc_auc_score(y_val_20, best_model.predict_proba(X_val_20)[:, 1])
            print(f"Validation AUC: {val_score:.4f}")

            # === PHASE 4: FINAL TRAINING ===
            # Combine training and validation data for final model (80% of original data)
            X_train_combined = pd.concat([X_train_60, X_val_20], axis=0)
            y_train_combined = pd.concat([y_train_60, y_val_20], axis=0)

            # Use the best parameters found during search
            xgb_clf_model = XGBClassifier(
                **best_params,
                enable_categorical=True,
                tree_method='hist',
                random_state=111,
                eval_metric='auc'
            )

            # Train final model on combined data without early stopping splits
            xgb_clf_model.fit(X_train_combined, y_train_combined)

            # === PHASE 5: STORE RESULTS FOR EVALUATOR ===
            # Store training results and data for evaluator
            feature_imp = xgb_clf_model.get_booster().get_score(importance_type="gain")
            hyper_params = best_params
            cv_performance = random_search.best_score_
            feature_names = X_test_20.columns.tolist() if hasattr(X_test_20, 'columns') else [f'feature_{i}' for i in range(X_test_20.shape[1])]

            # Store model and training metadata
            data.set_param("hyper_params", hyper_params)
            data.set_param("feature_importance", feature_imp)
            data.set_param("cv_performance", cv_performance)
            data.set_param("validation_score", val_score)
            data.set_model("xgb_model", xgb_clf_model)

            # Store evaluation data for evaluator
            data.set_dataframe("X_test", X_test_20)
            data.set_param("y_test", y_test_20)
            data.set_dataframe("X_train", X_train_combined)
            data.set_param("y_train", y_train_combined)
            data.set_param("feature_names", feature_names)

            # Print training completion summary
            print(f"\n{'='*50}")
            print("TRAINING COMPLETED")
            print(f"{'='*50}")
            print(f"CV AUC: {cv_performance:.4f}")
            print(f"Validation AUC: {val_score:.4f}")
            print(f"Model and evaluation data stored for evaluator")
            print("✅ Training phase complete - ready for evaluation")
            print(f"{'='*50}")
        else:
            print("LEARNING MODE IS OFF: Skipping training and evaluation")




    def qa_module(self):
        pass