
from abstract_model_factory.abstract_loader import AbstractLoader
from content_selection.data import Data
from content_selection.data_access_layer import DataAccessLayer
from qa_data_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from content_selection.constants import Constants
import os



class Loader(AbstractLoader):

    def qa_module(self):
        pass

    def read_training_data(self):
        data = Data.get_instance()
        col_str = ', '.join(Constants.TRAINING_FEATURES_FROM_RDS)
        rundate = data.get_param("rundate")
        sql = f"""select {col_str} from {Constants.learning_db}.Content_Selection_Input_v
        where eventDate between DATE_SUB('{rundate}', INTERVAL 180 DAY) and DATE_SUB('{rundate}', INTERVAL 5 DAY)"""
        df = DataAccessLayer.read_rds_table_as_pandas(sql)

        data.set_dataframe("training_df", df)

    def read_prediction_data(self):
        data = Data.get_instance()

        col_str = ', '.join(Constants.PREDICTION_FEATURES_FROM_RDS)

        sql = f"select {col_str} from {Constants.learning_db}.Content_Selection_Eligible_v"
        df = DataAccessLayer.read_rds_table_as_pandas(sql)
        
        data.set_dataframe("prediction_df", df)

    def execute(self):
        if Constants.LEARNING_MODE:
            self.read_training_data()
        if Constants.EXECUTION_MODE:
            self.read_prediction_data()
