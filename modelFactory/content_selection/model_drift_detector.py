from datetime import date, <PERSON><PERSON><PERSON>

from abstract_model_factory.abstract_model_drift_detector import AbstractModelDriftDetector
from content_selection.constants import Constants
from content_selection.data import Data
from content_selection.data_access_layer import DataAccessLayer
from content_selection.predictor import Predictor


class ModelDriftDetector(AbstractModelDriftDetector):
    def is_model_retraining_required(self) -> bool:
        return True
