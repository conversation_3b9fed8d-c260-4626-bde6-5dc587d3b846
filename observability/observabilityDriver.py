import os
import sys
import pandas as pd
import statsd

# Update sys path to find the modules from Common and DataAccessLayer
script_path = os.path.realpath(__file__)
script_dir = os.path.dirname(script_path)
learning_dir = os.path.dirname(script_dir)
sys.path.append(learning_dir)


def get_model(rundriver):
    model = 'none'
    component = 'none'
    if rundriver == 'messageTimingDriver':
        model = 'TTE'
        component = 'build'
    elif rundriver == 'messageTimingScoreDriver':
        model = 'TTE'
        component = 'score'
    elif rundriver == 'messageSequenceDriver':
        model = 'MSO'
        component = 'build'
    elif rundriver == 'messageScoreDriver':
        model = 'MSO'
        component = 'score'
    elif rundriver == 'anchorDriver':
        model = 'ANCHOR'
        component = 'score'
    elif rundriver == 'engagementDriver':
        model = 'REM'
        component = 'score'
    elif rundriver == 'AnchorAccuracyReportDriver':
        model = 'ANCHOR'
        component = 'accuracy'

    return model, component


class Observability:
    def __init__(self, customer, envname, region, runmodel, rundriver, file_path):
        self.customer = customer
        self.envname = envname
        self.region = region.lower()
        self.model, self.component = get_model(rundriver)
        self.rundriver = rundriver
        self.file_path = file_path

        # USDEVEKS => usdeveksstatsd.aktana.com
        # useks => useksstatsd.aktana.com
        # us => usstatsd.aktana.com
        # euqa => eustatsd.aktana.com
        self.statsd_server = self.region + "statsd.aktana.com"
        if "eks" not in self.region:
            self.statsd_server = self.region[:2] + "statsd.aktana.com"
        self.statsd_port = '9125'

        # self.metrics[cfg_id][g/c][metric_name]
        self.metrics = {}

    def _get_metric_value_from_csv(self, file_path):
        df = pd.read_csv(file_path)
        for i, row in df.iterrows():
            cfg_id, m_type, m_name, m_value = row['config_id'], row['metric_type'], row['metric_name'], row['metric_value']
            if cfg_id not in self.metrics:
                self.metrics[cfg_id] = {'g': {}, 'c': {}}

            self.metrics[cfg_id][m_type][m_name] = m_value
            print(cfg_id, m_type, m_name, m_value)

    def run(self):
        metric_prefix = 'type.{TYPE}.cmpny.' + self.customer + '.regn.' + self.region + '.cntry.none' + '.env.' + self.envname + \
                        '.apptype.' + self.model + '.configid.{CONFIG}' + '.rungroup.' + self.component + '.metric'

        self._get_metric_value_from_csv(self.file_path)

        for cfg in self.metrics:
            metrics = self.metrics[cfg]

            # setup gauge metrics
            statsd_client = statsd.StatsClient(self.statsd_server, self.statsd_port,
                                               prefix=metric_prefix.format(TYPE="g", CONFIG=cfg))
            for m in metrics['g']:
                statsd_client.gauge(m, metrics['g'][m])
                print(f"Config {cfg} Metric {m}: {metrics['g'][m]}")

            # setup counter metrics
            statsd_client = statsd.StatsClient(self.statsd_server, self.statsd_port,
                                               prefix=metric_prefix.format(TYPE="c", CONFIG=cfg))
            for m in metrics['c']:
                statsd_client.incr(m, metrics['c'][m])
                print(f"Config {cfg} Metric {m} increased by {metrics['c'][m]}")


def main():
    """
    This is main function
    """
    # Validate the input argument to the script

    # Retrieve the arguments
    input_args_dict = dict(i.split("=") for i in sys.argv[1:])
    try:

        customer = input_args_dict['customer']
        envname = input_args_dict['envname']
        region = input_args_dict['region']
        runmodel = input_args_dict['runmodel']
        rundriver = input_args_dict['rundriver']
        file_path = input_args_dict['file_path'] if 'file_path' in input_args_dict else '/tmp/metrics.csv'

    except KeyError as e:
        print("Could not get parameters:{}".format(e))
        return

    observability = Observability(customer, envname, region, runmodel, rundriver, file_path)
    observability.run()


if __name__ == "__main__":
    main()
